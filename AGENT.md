# AGENT.md - Baby Tracker Pro Development Guide

## Build/Test Commands
- `flutter test` - run all tests
- `flutter test test/widget_test.dart` - run specific test file  
- `flutter run --dart-define-from-file=env.json` - run with environment variables
- `flutter build apk --dart-define-from-file=env.json` - build Android APK
- `flutter build ios --dart-define-from-file=env.json` - build iOS app
- `flutter analyze` - static analysis and linting

## Architecture
- **Flutter/Dart** app with Supabase backend and OpenAI integration
- **Core structure**: `/lib/core/app_export.dart` contains all main exports
- **Services**: Supabase (database), OpenAI (AI chat/insights), Auth, Settings
- **Models**: BabyProfile, ActivityLog, AIInsight, UserProfile
- **UI**: Material 3 with custom AppTheme, responsive using Sizer package

## Code Style
- **Imports**: Use `app_export.dart` for core imports, relative imports for local files
- **Widgets**: StatefulWidget pattern, private methods with `_` prefix
- **Services**: Singleton pattern with dependency injection via constructors
- **Naming**: camelCase for variables/methods, PascalCase for classes
- **Responsive**: Use Sizer (e.g., `4.w` for width, `2.h` for height)
- **Error handling**: try-catch with user-friendly SnackBar messages
- **Theme**: Use `AppTheme.lightTheme` colors, avoid hardcoded colors
