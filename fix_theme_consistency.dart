#!/usr/bin/env dart

/// Comprehensive theme consistency fix script
/// This script will systematically replace hardcoded AppTheme.lightTheme references
/// with proper Theme.of(context) calls for dark mode compatibility

import 'dart:io';

void main() async {
  print('🎨 Starting comprehensive theme consistency fixes...\n');
  
  // Define the replacement patterns
  final replacements = <String, String>{
    // Color scheme replacements
    'AppTheme.lightTheme.colorScheme.primary': 'Theme.of(context).colorScheme.primary',
    'AppTheme.lightTheme.colorScheme.secondary': 'Theme.of(context).colorScheme.secondary',
    'AppTheme.lightTheme.colorScheme.tertiary': 'Theme.of(context).colorScheme.tertiary',
    'AppTheme.lightTheme.colorScheme.surface': 'Theme.of(context).colorScheme.surface',
    'AppTheme.lightTheme.colorScheme.onSurface': 'Theme.of(context).colorScheme.onSurface',
    'AppTheme.lightTheme.colorScheme.onSurfaceVariant': 'Theme.of(context).colorScheme.onSurfaceVariant',
    'AppTheme.lightTheme.colorScheme.outline': 'Theme.of(context).colorScheme.outline',
    'AppTheme.lightTheme.colorScheme.error': 'Theme.of(context).colorScheme.error',
    'AppTheme.lightTheme.primaryColor': 'Theme.of(context).colorScheme.primary',
    
    // Text theme replacements
    'AppTheme.lightTheme.textTheme.titleLarge': 'Theme.of(context).textTheme.titleLarge',
    'AppTheme.lightTheme.textTheme.titleMedium': 'Theme.of(context).textTheme.titleMedium',
    'AppTheme.lightTheme.textTheme.titleSmall': 'Theme.of(context).textTheme.titleSmall',
    'AppTheme.lightTheme.textTheme.bodyLarge': 'Theme.of(context).textTheme.bodyLarge',
    'AppTheme.lightTheme.textTheme.bodyMedium': 'Theme.of(context).textTheme.bodyMedium',
    'AppTheme.lightTheme.textTheme.bodySmall': 'Theme.of(context).textTheme.bodySmall',
    'AppTheme.lightTheme.textTheme.labelLarge': 'Theme.of(context).textTheme.labelLarge',
    'AppTheme.lightTheme.textTheme.labelMedium': 'Theme.of(context).textTheme.labelMedium',
    'AppTheme.lightTheme.textTheme.labelSmall': 'Theme.of(context).textTheme.labelSmall',
    'AppTheme.lightTheme.textTheme.headlineLarge': 'Theme.of(context).textTheme.headlineLarge',
    'AppTheme.lightTheme.textTheme.headlineMedium': 'Theme.of(context).textTheme.headlineMedium',
    'AppTheme.lightTheme.textTheme.headlineSmall': 'Theme.of(context).textTheme.headlineSmall',
    
    // Background colors
    'AppTheme.lightTheme.scaffoldBackgroundColor': 'Theme.of(context).scaffoldBackgroundColor',
  };
  
  // Files to process (activity widgets and other UI files)
  final filesToProcess = [
    'lib/presentation/quick_log_bottom_sheet/widgets/sleep_entry_widget.dart',
    'lib/presentation/quick_log_bottom_sheet/widgets/indoor_play_entry_widget.dart',
    'lib/presentation/quick_log_bottom_sheet/widgets/outdoor_play_entry_widget.dart',
    'lib/presentation/quick_log_bottom_sheet/widgets/custom_entry_widget.dart',
    'lib/presentation/quick_log_bottom_sheet/widgets/story_time_entry_widget.dart',
    'lib/presentation/ai_chat_assistant/widgets/chat_search_widget.dart',
    'lib/presentation/ai_chat_assistant/widgets/enhanced_chat_message_widget.dart',
    'lib/presentation/ai_chat_assistant/widgets/typing_indicator_widget.dart',
    'lib/presentation/ai_chat_assistant/widgets/enhanced_chat_input_widget.dart',
    'lib/presentation/auth/sign_up_screen.dart',
    'lib/presentation/auth/sign_in_screen.dart',
    'lib/presentation/babies_management_screen.dart',
    'lib/widgets/shared/growth_chart_preview_widget.dart',
  ];
  
  int totalReplacements = 0;
  
  for (final filePath in filesToProcess) {
    final file = File(filePath);
    if (!file.existsSync()) {
      print('⚠️  File not found: $filePath');
      continue;
    }
    
    print('🔧 Processing: $filePath');
    String content = await file.readAsString();
    int fileReplacements = 0;
    
    for (final entry in replacements.entries) {
      final oldPattern = entry.key;
      final newPattern = entry.value;
      
      if (content.contains(oldPattern)) {
        content = content.replaceAll(oldPattern, newPattern);
        fileReplacements++;
      }
    }
    
    if (fileReplacements > 0) {
      await file.writeAsString(content);
      print('   ✅ Made $fileReplacements replacements');
      totalReplacements += fileReplacements;
    } else {
      print('   ℹ️  No changes needed');
    }
  }
  
  print('\n🎉 Theme consistency fix completed!');
  print('📊 Total replacements made: $totalReplacements');
  print('\n📝 Next steps:');
  print('   1. Test the app in both light and dark modes');
  print('   2. Check for any remaining hardcoded theme references');
  print('   3. Update any custom color usage to use theme-aware helpers');
}