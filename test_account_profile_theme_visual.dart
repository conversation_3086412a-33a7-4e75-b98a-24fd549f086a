import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'lib/theme/app_theme.dart';
import 'lib/widgets/user_profile_account_section.dart';
import 'lib/widgets/profile_header_card.dart';
import 'lib/widgets/family_sharing_card.dart';
import 'lib/widgets/account_management_card.dart';
import 'lib/widgets/shared/animated_profile_widgets.dart';
import 'lib/widgets/account_profile_theme_helper.dart';
import 'lib/services/account_profile_controller.dart';
import 'lib/models/user_profile.dart';
import 'lib/models/family_member.dart';
import 'lib/models/subscription_info.dart';
import 'lib/models/profile_completion_status.dart';
import 'lib/models/enums.dart';

void main() {
  runApp(const AccountProfileThemeTestApp());
}

class AccountProfileThemeTestApp extends StatelessWidget {
  const AccountProfileThemeTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'Account Profile Theme Test',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.system,
          home: const ThemeTestScreen(),
        );
      },
    );
  }
}

class ThemeTestScreen extends StatefulWidget {
  const ThemeTestScreen({super.key});

  @override
  State<ThemeTestScreen> createState() => _ThemeTestScreenState();
}

class _ThemeTestScreenState extends State<ThemeTestScreen> {
  bool _isDarkMode = false;
  bool _isCompact = false;
  int _selectedTab = 0;

  // Mock data
  final UserProfile _mockUser = UserProfile(
    id: '1',
    email: '<EMAIL>',
    fullName: 'John Doe',
    avatarUrl: null,
    role: 'parent',
    createdAt: DateTime.now().subtract(const Duration(days: 30)),
    lastSignInAt: DateTime.now().subtract(const Duration(hours: 2)),
    signInCount: 15,
    isEmailVerified: true,
    isTwoFactorEnabled: false,
  );

  final List<FamilyMember> _mockFamilyMembers = [
    FamilyMember(
      id: '1',
      fullName: 'Jane Doe',
      email: '<EMAIL>',
      role: 'parent',
      avatarUrl: null,
      joinedAt: DateTime.now().subtract(const Duration(days: 25)),
      lastActiveAt: DateTime.now().subtract(const Duration(minutes: 30)),
      status: FamilyMemberStatus.active,
      permissions: {'canEdit': true, 'canInvite': true},
      invitedBy: null,
    ),
    FamilyMember(
      id: '2',
      fullName: 'Grandma Smith',
      email: '<EMAIL>',
      role: 'grandparent',
      avatarUrl: null,
      joinedAt: DateTime.now().subtract(const Duration(days: 20)),
      lastActiveAt: DateTime.now().subtract(const Duration(hours: 1)),
      status: FamilyMemberStatus.active,
      permissions: {'canEdit': false, 'canInvite': false},
      invitedBy: '1',
    ),
    FamilyMember(
      id: '3',
      fullName: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'caregiver',
      avatarUrl: null,
      joinedAt: DateTime.now().subtract(const Duration(days: 5)),
      lastActiveAt: null,
      status: FamilyMemberStatus.pending,
      permissions: {'canEdit': false, 'canInvite': false},
      invitedBy: '1',
    ),
  ];

  final SubscriptionInfo _mockSubscription = SubscriptionInfo(
    planId: 'pro',
    planName: 'BabyTracker Pro',
    status: SubscriptionStatus.active,
    renewalDate: DateTime.now().add(const Duration(days: 25)),
    monthlyPrice: 9.99,
    features: [
      'Unlimited family members',
      'AI-powered insights',
      'Advanced analytics',
      'Priority support',
      'Data export',
    ],
    isTrialActive: false,
    trialEndsAt: null,
    paymentMethod: PaymentMethod(
      type: 'card',
      displayName: '**** 1234',
      isExpired: false,
    ),
  );

  final ProfileCompletionStatus _mockCompletion = ProfileCompletionStatus(
    percentage: 75.0,
    completedSteps: [
      'Basic profile info',
      'Email verification',
      'Baby profile setup',
    ],
    remainingSteps: [
      'Add profile photo',
      'Set up notifications',
    ],
    nextRecommendedAction: 'Add profile photo',
  );

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: _isDarkMode ? AppTheme.darkTheme : AppTheme.lightTheme,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Account Profile Theme Test'),
          actions: [
            IconButton(
              icon: Icon(_isDarkMode ? Icons.light_mode : Icons.dark_mode),
              onPressed: () => setState(() => _isDarkMode = !_isDarkMode),
              tooltip: 'Toggle theme',
            ),
            IconButton(
              icon: Icon(_isCompact ? Icons.fullscreen : Icons.fullscreen_exit),
              onPressed: () => setState(() => _isCompact = !_isCompact),
              tooltip: 'Toggle compact mode',
            ),
          ],
          bottom: TabBar(
            controller: TabController(length: 5, vsync: Scaffold.of(context)),
            onTap: (index) => setState(() => _selectedTab = index),
            tabs: const [
              Tab(text: 'Full Section'),
              Tab(text: 'Profile Card'),
              Tab(text: 'Family Card'),
              Tab(text: 'Account Card'),
              Tab(text: 'Animations'),
            ],
          ),
        ),
        body: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(2.w),
            child: _buildSelectedTab(),
          ),
        ),
        floatingActionButton: FloatingActionButton(
          onPressed: () => _showThemeInfo(context),
          child: const Icon(Icons.info),
          tooltip: 'Theme info',
        ),
      ),
    );
  }

  Widget _buildSelectedTab() {
    switch (_selectedTab) {
      case 0:
        return _buildFullSection();
      case 1:
        return _buildProfileCard();
      case 2:
        return _buildFamilyCard();
      case 3:
        return _buildAccountCard();
      case 4:
        return _buildAnimationsDemo();
      default:
        return _buildFullSection();
    }
  }

  Widget _buildFullSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Complete Account Profile Section'),
        SizedBox(height: 2.h),
        UserProfileAccountSection.full(
          userProfile: _mockUser,
          onEditProfile: () => _showSnackBar('Edit Profile tapped'),
          onNavigateToUserManagement: () => _showSnackBar('User Management tapped'),
          onSubscriptionTap: () => _showSnackBar('Subscription tapped'),
          onFamilySharingTap: () => _showSnackBar('Family Sharing tapped'),
          onAvatarTap: () => _showSnackBar('Avatar tapped'),
        ),
        SizedBox(height: 4.h),
        _buildSectionHeader('Compact Version'),
        SizedBox(height: 2.h),
        UserProfileAccountSection.compact(
          userProfile: _mockUser,
          onEditProfile: () => _showSnackBar('Edit Profile tapped'),
          onNavigateToUserManagement: () => _showSnackBar('User Management tapped'),
          onSubscriptionTap: () => _showSnackBar('Subscription tapped'),
          onFamilySharingTap: () => _showSnackBar('Family Sharing tapped'),
          onAvatarTap: () => _showSnackBar('Avatar tapped'),
        ),
      ],
    );
  }

  Widget _buildProfileCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Profile Header Card Variants'),
        SizedBox(height: 2.h),
        ProfileHeaderCard.full(
          userProfile: _mockUser,
          completionStatus: _mockCompletion,
          onEditProfile: () => _showSnackBar('Edit Profile tapped'),
          onAvatarTap: () => _showSnackBar('Avatar tapped'),
        ),
        SizedBox(height: 3.h),
        ProfileHeaderCard.compact(
          userProfile: _mockUser,
          onEditProfile: () => _showSnackBar('Edit Profile tapped'),
          onAvatarTap: () => _showSnackBar('Avatar tapped'),
        ),
        SizedBox(height: 3.h),
        ProfileHeaderCard.readOnly(
          userProfile: _mockUser,
        ),
      ],
    );
  }

  Widget _buildFamilyCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Family Sharing Card'),
        SizedBox(height: 2.h),
        FamilySharingCard(
          familyMembers: _mockFamilyMembers,
          pendingInvitations: 1,
          onInviteMember: () => _showSnackBar('Invite Member tapped'),
          onSendInvitation: (email, role, permissions, message) {
            _showSnackBar('Invitation sent to $email as $role');
          },
          onResendInvitation: (member) => _showSnackBar('Resent invitation to ${member.fullName}'),
          onCancelInvitation: (member) => _showSnackBar('Cancelled invitation for ${member.fullName}'),
          onMemberTap: (member) => _showSnackBar('Tapped ${member.fullName}'),
          canManageFamily: true,
          onManageFamily: () => _showSnackBar('Manage Family tapped'),
        ),
        SizedBox(height: 3.h),
        _buildSectionHeader('Empty State'),
        SizedBox(height: 2.h),
        FamilySharingCard(
          familyMembers: const [],
          onInviteMember: () => _showSnackBar('Invite Member tapped'),
          canManageFamily: true,
        ),
      ],
    );
  }

  Widget _buildAccountCard() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Account Management Card'),
        SizedBox(height: 2.h),
        AccountManagementCard(
          subscription: _mockSubscription,
          hasAdminPrivileges: true,
          isTwoFactorEnabled: false,
          isEmailVerified: true,
          activeSessions: 2,
          onManageSubscription: () => _showSnackBar('Manage Subscription tapped'),
          onSecuritySettings: () => _showSnackBar('Security Settings tapped'),
          onAccountPreferences: () => _showSnackBar('Account Preferences tapped'),
          onUpgrade: () => _showSnackBar('Upgrade tapped'),
          onDowngrade: () => _showSnackBar('Downgrade tapped'),
        ),
        SizedBox(height: 3.h),
        _buildSectionHeader('Loading State'),
        SizedBox(height: 2.h),
        const AccountManagementCard(
          isLoading: true,
        ),
      ],
    );
  }

  Widget _buildAnimationsDemo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader('Animation Components'),
        SizedBox(height: 2.h),
        
        // Animated Cards
        _buildSubsectionHeader('Animated Cards'),
        SizedBox(height: 1.h),
        AnimatedProfileWidgets.animatedCard(
          context: context,
          onTap: () => _showSnackBar('Animated card tapped'),
          child: const Padding(
            padding: EdgeInsets.all(16),
            child: Text('Tap me! I have press animations'),
          ),
        ),
        SizedBox(height: 2.h),
        
        // Animated Buttons
        _buildSubsectionHeader('Animated Buttons'),
        SizedBox(height: 1.h),
        Row(
          children: [
            Expanded(
              child: AnimatedProfileWidgets.animatedButton(
                context: context,
                onPressed: () => _showSnackBar('Primary button tapped'),
                isPrimary: true,
                child: const Text('Primary Button'),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: AnimatedProfileWidgets.animatedButton(
                context: context,
                onPressed: () => _showSnackBar('Secondary button tapped'),
                isPrimary: false,
                child: const Text('Secondary Button'),
              ),
            ),
          ],
        ),
        SizedBox(height: 2.h),
        
        // Animated Progress
        _buildSubsectionHeader('Animated Progress'),
        SizedBox(height: 1.h),
        AnimatedProfileWidgets.animatedProgress(
          value: 0.75,
          context: context,
        ),
        SizedBox(height: 1.h),
        AnimatedProfileWidgets.animatedProgress(
          value: 0.25,
          context: context,
        ),
        SizedBox(height: 2.h),
        
        // Animated Badges
        _buildSubsectionHeader('Animated Badges'),
        SizedBox(height: 1.h),
        Wrap(
          spacing: 2.w,
          runSpacing: 1.h,
          children: [
            AnimatedProfileWidgets.animatedBadge(
              context: context,
              status: 'active',
              child: const Text('Active'),
            ),
            AnimatedProfileWidgets.animatedBadge(
              context: context,
              status: 'pending',
              child: const Text('Pending'),
            ),
            AnimatedProfileWidgets.animatedBadge(
              context: context,
              status: 'premium',
              child: const Text('Premium'),
            ),
            AnimatedProfileWidgets.animatedBadge(
              context: context,
              status: 'inactive',
              child: const Text('Inactive'),
            ),
          ],
        ),
        SizedBox(height: 2.h),
        
        // Animated List Items
        _buildSubsectionHeader('Animated List Items'),
        SizedBox(height: 1.h),
        ...List.generate(3, (index) => AnimatedProfileWidgets.animatedListItem(
          context: context,
          index: index,
          onTap: () => _showSnackBar('List item ${index + 1} tapped'),
          child: Container(
            margin: EdgeInsets.only(bottom: 1.h),
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
            child: Text('Animated List Item ${index + 1}'),
          ),
        )),
      ],
    );
  }

  Widget _buildSectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
        fontWeight: FontWeight.w600,
        color: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildSubsectionHeader(String title) {
    return Text(
      title,
      style: Theme.of(context).textTheme.titleMedium?.copyWith(
        fontWeight: FontWeight.w500,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showThemeInfo(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context, isCompact: _isCompact);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Theme Information'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('Theme: ${theme.brightness.name}'),
              Text('Compact Mode: $_isCompact'),
              const SizedBox(height: 16),
              Text('Spacing:', style: theme.textTheme.titleSmall),
              Text('Card: ${spacing.cardSpacing}'),
              Text('Section: ${spacing.sectionSpacing}'),
              Text('Element: ${spacing.elementSpacing}'),
              const SizedBox(height: 16),
              Text('Colors:', style: theme.textTheme.titleSmall),
              Text('Primary: ${theme.colorScheme.primary}'),
              Text('Surface: ${theme.colorScheme.surface}'),
              Text('Background: ${theme.scaffoldBackgroundColor}'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}