# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/
pubspec.lock

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
/web/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Environment and Configuration Files
env.json
.env
.env.local
.env.development
.env.production
*.env

# API Keys and Secrets
secrets.dart
api_keys.dart
config/secrets/

# iOS specific
/ios/Pods/
/ios/.symlinks/
/ios/Flutter/Flutter.framework
/ios/Flutter/Flutter.podspec
/ios/Runner.xcworkspace/
/ios/Runner/GeneratedPluginRegistrant.*
/ios/Flutter/app.flx
/ios/Flutter/app.zip
/ios/Flutter/flutter_assets/
/ios/ServiceDefinitions.json
/ios/Runner/GoogleService-Info.plist

# Android specific
/android/app/src/main/java/io/flutter/plugins/
/android/local.properties
/android/app/src/main/res/raw/
/android/app/google-services.json
/android/key.properties
/android/app/upload-keystore.jks
/android/gradle/
!/android/gradle/wrapper/gradle-wrapper.properties

# Supabase
/supabase/.branches
/supabase/.temp
/supabase/config.toml

# Logs
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database
*.db
*.sqlite
*.sqlite3

# Temporary files
*.tmp
*.temp
.temp/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk
