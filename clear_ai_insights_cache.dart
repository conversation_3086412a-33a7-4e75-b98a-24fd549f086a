import 'dart:io';
import 'dart:convert';

Future<void> main() async {
  print('🔄 Clearing AI Insights Cache to Force Fresh Generation...\n');
  
  // Read environment configuration
  final envFile = File('env.json');
  if (!envFile.existsSync()) {
    print('❌ Error: env.json file not found');
    exit(1);
  }
  
  final envContent = await envFile.readAsString();
  final envConfig = jsonDecode(envContent);
  
  final supabaseUrl = envConfig['SUPABASE_URL'];
  final supabaseKey = envConfig['SUPABASE_ANON_KEY'];
  
  if (supabaseUrl == null || supabaseKey == null) {
    print('❌ Error: Supabase credentials not found in env.json');
    exit(1);
  }
  
  // The baby ID from the logs
  const babyId = 'c5959165-09bb-4aa5-8149-42c12b17f3c3';
  
  try {
    // Delete cached insights for this baby
    final deleteResult = await Process.run('curl', [
      '-X', 'DELETE',
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '-H', 'Content-Type: application/json',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (deleteResult.exitCode == 0) {
      print('✅ Successfully cleared cached AI insights for baby: $babyId');
      print('📊 Response: ${deleteResult.stdout}');
      print('\n🔄 The next time the app loads AI insights, it will generate fresh ones with correct timestamps.');
      print('\n📱 You can now:');
      print('   1. Pull to refresh the home screen, or');
      print('   2. Tap the refresh button on the AI insights card, or');
      print('   3. Restart the app');
      print('\n⚠️  This will force regeneration of all AI insights for this baby.');
    } else {
      print('❌ Error clearing cached insights: ${deleteResult.stderr}');
      exit(1);
    }
  } catch (e) {
    print('❌ Error executing cache clear: $e');
    exit(1);
  }
}
