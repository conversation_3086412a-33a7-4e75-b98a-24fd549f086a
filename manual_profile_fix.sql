-- Manual fix for user profile issues
-- Run this directly in Supabase SQL Editor

-- First, let's see what columns actually exist in user_profiles
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'user_profiles' 
AND table_schema = 'public'
ORDER BY ordinal_position;

-- Check if there are any existing user profiles
SELECT id, auth_id, email, display_name, created_at 
FROM public.user_profiles 
LIMIT 5;

-- Check current authenticated user
SELECT id, email, email_confirmed_at, created_at
FROM auth.users
WHERE id = auth.uid();

-- Create user profile for current authenticated user if it doesn't exist
DO $$
DECLARE
    v_auth_user_id UUID;
    v_auth_email TEXT;
    v_profile_exists BOOLEAN := FALSE;
BEGIN
    -- Get current authenticated user (you'll need to replace this with your actual user ID)
    -- Since we can't use auth.uid() in a manual script, we'll get the most recent user
    SELECT id, email INTO v_auth_user_id, v_auth_email
    FROM auth.users 
    ORDER BY created_at DESC 
    LIMIT 1;
    
    IF v_auth_user_id IS NOT NULL THEN
        -- Check if profile already exists
        SELECT EXISTS(
            SELECT 1 FROM public.user_profiles 
            WHERE auth_id = v_auth_user_id
        ) INTO v_profile_exists;
        
        IF NOT v_profile_exists THEN
            -- Create the profile with all required fields
            INSERT INTO public.user_profiles (
                auth_id,
                email,
                display_name,
                full_name,
                role,
                sign_in_count,
                is_email_verified,
                preferences,
                permissions,
                created_at,
                updated_at
            ) VALUES (
                v_auth_user_id,
                v_auth_email,
                v_auth_email,
                v_auth_email,
                'parent',
                0,
                true,
                '{}',
                '{}',
                NOW(),
                NOW()
            );
            
            RAISE NOTICE 'Created user profile for user: %', v_auth_email;
        ELSE
            RAISE NOTICE 'User profile already exists for: %', v_auth_email;
        END IF;
    ELSE
        RAISE NOTICE 'No authenticated user found';
    END IF;
END $$;

-- Verify the profile was created
SELECT id, auth_id, email, display_name, full_name, role, is_email_verified, created_at
FROM public.user_profiles
ORDER BY created_at DESC
LIMIT 5;