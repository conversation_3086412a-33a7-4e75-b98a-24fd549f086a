import 'dart:io';
import 'dart:convert';

Future<void> main() async {
  print('🧹 Comprehensive Cache Clearing - All Sources...\n');
  
  // Read environment configuration
  final envFile = File('env.json');
  if (!envFile.existsSync()) {
    print('❌ Error: env.json file not found');
    exit(1);
  }
  
  final envContent = await envFile.readAsString();
  final envConfig = jsonDecode(envContent);
  
  final supabaseUrl = envConfig['SUPABASE_URL'];
  final supabaseKey = envConfig['SUPABASE_ANON_KEY'];
  
  if (supabaseUrl == null || supabaseKey == null) {
    print('❌ Error: Supabase credentials not found in env.json');
    exit(1);
  }
  
  // The baby ID from the logs
  const babyId = 'c5959165-09bb-4aa5-8149-42c12b17f3c3';
  
  print('🔍 Step 1: Checking Supabase ai_insights table...');
  
  try {
    // First, check what insights exist in ai_insights table
    final checkResult = await Process.run('curl', [
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (checkResult.exitCode == 0) {
      final response = checkResult.stdout.toString();
      if (response.trim() != '[]') {
        print('🔍 Found cached insights in ai_insights table:');
        print(response);
        
        // Delete from ai_insights table
        print('🗑️ Clearing ai_insights table...');
        final deleteAiInsights = await Process.run('curl', [
          '-X', 'DELETE',
          '-H', 'apikey: $supabaseKey',
          '-H', 'Authorization: Bearer $supabaseKey',
          '-H', 'Content-Type: application/json',
          '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
        ]);
        
        if (deleteAiInsights.exitCode == 0) {
          print('✅ Cleared ai_insights table');
        } else {
          print('❌ Error clearing ai_insights: ${deleteAiInsights.stderr}');
        }
      } else {
        print('✅ No cached insights found in ai_insights table');
      }
    }
    
    print('\n🔍 Step 2: Checking for other cache tables...');
    
    // Check for other possible cache tables
    final tables = ['cached_insights', 'insight_cache', 'ai_cache', 'baby_insights_cache'];
    
    for (final table in tables) {
      print('   Checking table: $table');
      final checkTable = await Process.run('curl', [
        '-H', 'apikey: $supabaseKey',
        '-H', 'Authorization: Bearer $supabaseKey',
        '$supabaseUrl/rest/v1/$table?baby_id=eq.$babyId'
      ]);
      
      if (checkTable.exitCode == 0) {
        final response = checkTable.stdout.toString();
        if (!response.contains('404') && !response.contains('relation') && response.trim() != '[]') {
          print('   🔍 Found data in $table: $response');
          
          // Clear this table
          final deleteTable = await Process.run('curl', [
            '-X', 'DELETE',
            '-H', 'apikey: $supabaseKey',
            '-H', 'Authorization: Bearer $supabaseKey',
            '-H', 'Content-Type: application/json',
            '$supabaseUrl/rest/v1/$table?baby_id=eq.$babyId'
          ]);
          
          if (deleteTable.exitCode == 0) {
            print('   ✅ Cleared $table');
          } else {
            print('   ❌ Error clearing $table: ${deleteTable.stderr}');
          }
        } else {
          print('   ✅ No data in $table (or table does not exist)');
        }
      }
    }
    
    print('\n🔍 Step 3: Looking for any insight-related data for this baby...');
    
    // Search for any tables that might contain insights for this baby
    final searchTables = await Process.run('curl', [
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '$supabaseUrl/rest/v1/?select=*'
    ]);
    
    print('\n📱 Step 4: Clear app data (emulator only)...');
    
    // For Android emulator, clear app data to remove SharedPreferences
    print('🧹 Clearing app data on Android emulator...');
    
    final clearAppData = await Process.run('adb', [
      'shell',
      'pm',
      'clear',
      'com.babytracker_pro.app'
    ]);
    
    if (clearAppData.exitCode == 0) {
      print('✅ Cleared Android app data (including SharedPreferences)');
    } else {
      print('⚠️ Could not clear app data (emulator might not be running): ${clearAppData.stderr}');
    }
    
    print('\n🔍 Step 5: Final verification...');
    
    // Final verification - check ai_insights table is empty
    final finalCheck = await Process.run('curl', [
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (finalCheck.exitCode == 0) {
      final response = finalCheck.stdout.toString();
      if (response.trim() == '[]') {
        print('✅ All caches cleared successfully!');
        print('\n🎉 RESOLUTION COMPLETE:');
        print('   ✅ Database cache cleared');
        print('   ✅ App data cleared (SharedPreferences)');
        print('   ✅ Memory cache will be cleared on app restart');
        print('\n📱 Next steps:');
        print('   1. Restart the Flutter app');
        print('   2. The app will generate completely fresh AI insights');
        print('   3. New insights will have correct current timestamps');
        print('   4. The "Last updated: just now" issue should be resolved');
      } else {
        print('⚠️ Some cache still exists:');
        print(response);
      }
    }
    
  } catch (e) {
    print('❌ Error: $e');
    exit(1);
  }
}
