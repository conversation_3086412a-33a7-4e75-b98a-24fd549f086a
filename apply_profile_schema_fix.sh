#!/bin/bash

# <PERSON>ript to apply the user profile schema fix migration
# This fixes the missing columns and tables causing the profile update errors

echo "🔧 Applying user profile schema fix migration..."

# Check if supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "❌ Error: Supabase CLI is not installed. Please install it first:"
    echo "   npm install -g supabase"
    echo "   or"
    echo "   brew install supabase/tap/supabase"
    exit 1
fi

# Check if we're in a Supabase project
if [ ! -f "supabase/config.toml" ]; then
    echo "⚠️  Warning: No supabase/config.toml found. Make sure you're in the project root."
    echo "   If this is your first time, run: supabase init"
fi

# Apply the migration
echo "📦 Applying migration: 20250121000002_fix_user_profile_schema.sql"
supabase db push

if [ $? -eq 0 ]; then
    echo "✅ Migration applied successfully!"
    echo ""
    echo "🎉 The following issues have been fixed:"
    echo "   • Added missing columns to user_profiles table:"
    echo "     - is_email_verified, family_id, phone_number, timezone"
    echo "     - full_name, role, provider, provider_id, etc."
    echo "   • Created missing tables:"
    echo "     - families (for family sharing)"
    echo "     - subscriptions (for subscription management)"
    echo "     - user_sessions (for session tracking)"
    echo "   • Added required functions:"
    echo "     - track_user_session() - for session management"
    echo "     - get_chat_sessions() - for chat functionality"
    echo "     - update_user_profile() - for safe profile updates"
    echo "     - create_user_profile_if_not_exists() - for profile creation"
    echo "   • Added automatic user profile creation trigger"
    echo ""
    echo "🚀 You can now try creating/updating your profile again in the app."
    echo "   The 'Failed to update profile' error should be resolved."
else
    echo "❌ Migration failed. Please check the error messages above."
    echo "   Common issues:"
    echo "   - Make sure you're connected to the internet"
    echo "   - Verify your Supabase project is accessible"
    echo "   - Check if you have the correct permissions"
    exit 1
fi