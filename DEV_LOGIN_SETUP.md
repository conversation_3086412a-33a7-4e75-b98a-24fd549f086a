# Development Auto-Login Feature

## Overview
A development-only auto-login feature has been added to make testing easier. This feature allows you to quickly log in with your test account without manually entering credentials.

## Features Added

### 1. Dev Login Button
- **Location**: Both sign-in screens (`SignInScreen` and `AuthenticationScreen`)
- **Visibility**: Only appears in debug mode (`kDebugMode`)
- **Design**: Orange outlined button with developer icon
- **Text**: "Dev Login"
- **Position**: Bottom of the screen below all other content

### 2. Auto-Login Functionality
- **Email**: <EMAIL>
- **Password**: qqnbbn00 (you need to set this for your test account)
- **Access Level**: Full admin access with all permissions
- **Navigation**: Automatically redirects to main navigation after successful login

### 3. User Permissions
Once logged in via dev login, you'll have full access to:
- ✅ Log baby activities (feeding, sleep, diaper changes, etc.)
- ✅ Add and edit scheduled activities
- ✅ Edit user profile settings
- ✅ Create and edit baby profiles
- ✅ Access AI insights and analytics
- ✅ Export data
- ✅ Manage family members (admin features)
- ✅ All other app features

## Setup Instructions

### 1. Ensure Test Account Exists
Make sure your test account is properly set up in Supabase:
```
Email: <EMAIL>
Password: qqnbbn00
Role: admin (for full access)
```

### 2. Verify Debug Mode
The dev login button only appears when running in debug mode. To verify:
```bash
flutter run tmp_rovodev_test_dev_login.dart
```

### 3. Using Dev Login
1. Launch the app in debug mode
2. Navigate to any sign-in screen
3. Scroll to the bottom and look for the orange "Dev Login" button
4. Click the button to automatically log in
5. You'll be redirected to the main app with full access

## Security Notes

### ✅ Safe for Development
- Only visible in debug mode (`kDebugMode`)
- Automatically hidden in production builds
- No security risk for released apps

### 🔒 Production Safety
- The dev login button will NOT appear in:
  - Release builds
  - Profile builds
  - Production deployments
- Flutter's `kDebugMode` ensures this is development-only

## Files Modified

1. **lib/presentation/auth/sign_in_screen.dart**
   - Added `kDebugMode` import
   - Added `_devAutoLogin()` method
   - Added dev login button UI

2. **lib/presentation/authentication_screen/authentication_screen.dart**
   - Added `kDebugMode` import
   - Added `_devAutoLogin()` method
   - Added dev login button UI

## Testing

Run the test script to verify the setup:
```bash
flutter run tmp_rovodev_test_dev_login.dart
```

This will show you:
- Current debug mode status
- Whether the dev login button will be visible
- Setup instructions

## Troubleshooting

### Dev Login Button Not Visible
- Ensure you're running in debug mode (`flutter run`)
- Check that `kDebugMode` is true
- Verify the app is not built in release mode

### Login Fails
- Verify the test account exists in Supabase
- Check the password is set to "test123"
- Ensure the account has proper permissions
- Check Supabase connection and configuration

### No Admin Access
- Verify the user role is set to "admin" in Supabase
- Check user permissions in the database
- Ensure the user profile is properly created

## Usage Examples

### Quick Testing Workflow
1. `flutter run` (debug mode)
2. Click "Dev Login" button
3. Start testing features immediately
4. Log activities, edit profiles, etc.

### Feature Testing
- **Activity Logging**: Navigate to tracker → quick log → test different activity types
- **Scheduling**: Go to scheduler → add new activities → verify they save
- **Profile Editing**: Settings → user profile → make changes → verify they persist
- **Baby Management**: Baby selector → add/edit babies → test photo uploads

This setup eliminates the need to manually enter credentials during development while maintaining security for production builds.