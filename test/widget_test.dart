// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility that <PERSON>lut<PERSON> provides. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:babytracker_pro/main.dart';
import 'package:babytracker_pro/presentation/splash_screen/splash_screen.dart';

void main() {
  testWidgets('Initial route is SplashScreen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(MyApp());
    
    // Pump once to let the widget tree build
    await tester.pump();

    // Verify that SplashScreen is displayed.
    expect(find.byType(SplashScreen), findsOneWidget);
    
    // Verify that the app name is shown
    expect(find.text('BabyTracker Pro'), findsOneWidget);
    
    // Verify that the tagline is shown
    expect(find.text('AI-Powered Baby Care Assistant'), findsOneWidget);
    
    // We don't wait for the timer to complete to avoid test timeout issues
  });
}
