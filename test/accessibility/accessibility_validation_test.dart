import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../lib/models/user_profile.dart';
import '../../lib/models/family_member.dart';
import '../../lib/models/subscription_info.dart';
import '../../lib/models/profile_completion_status.dart';
import '../../lib/models/enums.dart';
import '../../lib/services/account_profile_controller.dart';
import '../../lib/widgets/user_profile_account_section.dart';
import '../../lib/theme/app_theme.dart';
import '../../lib/utils/accessibility_helper.dart';

/// Comprehensive accessibility validation tests for the account profile redesign
/// 
/// Tests cover:
/// - WCAG 2.1 AA compliance
/// - Touch target sizes (minimum 44dp)
/// - Color contrast ratios (minimum 4.5:1)
/// - Keyboard navigation support
/// - Screen reader compatibility
/// - Semantic markup validation
void main() {
  group('Accessibility Validation Tests', () {
    late UserProfile testUserProfile;
    late List<FamilyMember> testFamilyMembers;
    late SubscriptionInfo testSubscription;
    late ProfileCompletionStatus testCompletion;

    setUp(() {
      testUserProfile = UserProfile(
        id: 'test-id',
        email: '<EMAIL>',
        fullName: 'John Doe',
        role: 'parent',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastSignInAt: DateTime.now().subtract(const Duration(hours: 2)),
        signInCount: 15,
        avatarUrl: null,
        isEmailVerified: true,
        isTwoFactorEnabled: false,
        permissions: {'can_edit': true},
      );

      testFamilyMembers = [
        FamilyMember(
          id: 'member-1',
          fullName: 'Jane Doe',
          email: '<EMAIL>',
          role: 'parent',
          status: FamilyMemberStatus.active,
          joinedAt: DateTime.now().subtract(const Duration(days: 20)),
          lastActiveAt: DateTime.now().subtract(const Duration(hours: 1)),
          permissions: {'can_view': true},
        ),
        FamilyMember(
          id: 'member-2',
          fullName: 'Grandma Smith',
          email: '<EMAIL>',
          role: 'caregiver',
          status: FamilyMemberStatus.pending,
          joinedAt: DateTime.now().subtract(const Duration(days: 1)),
          permissions: {'can_view': true},
        ),
      ];

      testSubscription = SubscriptionInfo(
        planId: 'premium',
        planName: 'Premium Plan',
        status: SubscriptionStatus.active,
        monthlyPrice: 9.99,
        features: ['Unlimited tracking', 'AI insights', 'Family sharing'],
        renewalDate: DateTime.now().add(const Duration(days: 30)),
      );

      testCompletion = ProfileCompletionStatus(
        percentage: 75.0,
        completedSteps: ['basic_info', 'avatar'],
        remainingSteps: ['preferences'],
        nextRecommendedAction: 'Set up notification preferences',
      );
    });

    group('WCAG 2.1 AA Compliance', () {
      testWidgets('validates heading hierarchy', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find all semantic headers
        final headers = find.byWidgetPredicate(
          (widget) => widget is Semantics && widget.properties.header == true,
        );

        expect(headers, findsWidgets);

        // Verify proper heading hierarchy exists
        final headerElements = headers.evaluate().toList();
        expect(headerElements.length, greaterThan(0), 
               reason: 'Should have at least one heading for proper document structure');
      });

      testWidgets('validates semantic landmarks', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find semantic regions/landmarks
        final landmarks = find.byWidgetPredicate(
          (widget) => widget is Semantics && 
                      (widget.properties.label?.contains('section') == true ||
                       widget.properties.label?.contains('region') == true),
        );

        expect(landmarks, findsWidgets);
      });

      testWidgets('validates button semantics', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find all buttons
        final buttons = find.byWidgetPredicate(
          (widget) => widget is Semantics && widget.properties.button == true,
        );

        expect(buttons, findsWidgets);

        // Validate each button has proper semantics
        for (final buttonElement in buttons.evaluate()) {
          final semantics = buttonElement.widget as Semantics;
          
          // Each button should have a label
          expect(semantics.properties.label, isNotNull,
                 reason: 'All buttons should have semantic labels');
          expect(semantics.properties.label!.isNotEmpty, isTrue,
                 reason: 'Button labels should not be empty');
        }
      });

      testWidgets('validates form field semantics', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find text fields
        final textFields = find.byWidgetPredicate(
          (widget) => widget is Semantics && widget.properties.textField == true,
        );

        // Validate text field semantics if any exist
        for (final fieldElement in textFields.evaluate()) {
          final semantics = fieldElement.widget as Semantics;
          
          expect(semantics.properties.label, isNotNull,
                 reason: 'Text fields should have labels');
        }
      });
    });

    group('Touch Target Size Validation', () {
      testWidgets('validates minimum touch target sizes (44dp)', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find all interactive elements
        final interactiveElements = [
          ...find.byType(GestureDetector).evaluate(),
          ...find.byType(InkWell).evaluate(),
          ...find.byType(ElevatedButton).evaluate(),
          ...find.byType(OutlinedButton).evaluate(),
          ...find.byType(TextButton).evaluate(),
        ];

        for (final element in interactiveElements) {
          final renderBox = element.renderObject as RenderBox?;
          
          if (renderBox != null && renderBox.hasSize) {
            final size = renderBox.size;
            
            // Check if either dimension meets minimum requirement
            final meetsMinimum = size.width >= AccessibilityHelper.minTouchTargetSize ||
                                size.height >= AccessibilityHelper.minTouchTargetSize;
            
            expect(meetsMinimum, isTrue,
                   reason: 'Interactive element should meet minimum touch target size of ${AccessibilityHelper.minTouchTargetSize}dp. '
                          'Current size: ${size.width}x${size.height}');
          }
        }
      });

      testWidgets('validates recommended touch target sizes (48dp)', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find primary action buttons
        final primaryButtons = find.byType(ElevatedButton);
        
        for (final buttonElement in primaryButtons.evaluate()) {
          final renderBox = buttonElement.renderObject as RenderBox?;
          
          if (renderBox != null && renderBox.hasSize) {
            final size = renderBox.size;
            
            // Primary buttons should meet recommended size
            expect(size.height, greaterThanOrEqualTo(AccessibilityHelper.recommendedTouchTargetSize),
                   reason: 'Primary buttons should meet recommended touch target size of ${AccessibilityHelper.recommendedTouchTargetSize}dp');
          }
        }
      });
    });

    group('Color Contrast Validation', () {
      testWidgets('validates text contrast in light theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);

        // Test primary text contrast
        final primaryTextContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
          minRatio: 4.5, // WCAG AA standard
        );
        expect(primaryTextContrast, isTrue,
               reason: 'Primary text should meet WCAG AA contrast ratio (4.5:1) in light theme');

        // Test secondary text contrast
        final secondaryTextContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurfaceVariant,
          background: theme.colorScheme.surface,
          minRatio: 4.5,
        );
        expect(secondaryTextContrast, isTrue,
               reason: 'Secondary text should meet WCAG AA contrast ratio (4.5:1) in light theme');

        // Test button text contrast
        final buttonTextContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onPrimary,
          background: theme.colorScheme.primary,
          minRatio: 4.5,
        );
        expect(buttonTextContrast, isTrue,
               reason: 'Button text should meet WCAG AA contrast ratio (4.5:1) in light theme');
      });

      testWidgets('validates text contrast in dark theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.darkTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);

        // Test primary text contrast
        final primaryTextContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
          minRatio: 4.5,
        );
        expect(primaryTextContrast, isTrue,
               reason: 'Primary text should meet WCAG AA contrast ratio (4.5:1) in dark theme');

        // Test secondary text contrast
        final secondaryTextContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurfaceVariant,
          background: theme.colorScheme.surface,
          minRatio: 4.5,
        );
        expect(secondaryTextContrast, isTrue,
               reason: 'Secondary text should meet WCAG AA contrast ratio (4.5:1) in dark theme');
      });

      testWidgets('validates enhanced contrast for AAA compliance', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);

        // Test enhanced contrast for important text (WCAG AAA standard)
        final enhancedContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
          minRatio: 7.0, // WCAG AAA standard
        );

        // Note: This might not always pass, but we should strive for it
        if (!enhancedContrast) {
          debugPrint('Warning: Text does not meet WCAG AAA enhanced contrast ratio (7:1)');
        }
      });
    });

    group('Keyboard Navigation Support', () {
      testWidgets('supports tab navigation between focusable elements', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find focusable elements
        final focusableElements = find.byWidgetPredicate(
          (widget) => widget is Focus || 
                      (widget is Semantics && widget.properties.focusable == true),
        );

        expect(focusableElements, findsWidgets,
               reason: 'Should have focusable elements for keyboard navigation');

        // Test tab navigation
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle();

        // Verify focus management
        final focusedElements = find.byWidgetPredicate(
          (widget) => widget is Focus && widget.focusNode?.hasFocus == true,
        );

        // Should have at least one focused element after tab
        expect(focusedElements.evaluate().isNotEmpty, isTrue,
               reason: 'Tab navigation should move focus to an element');
      });

      testWidgets('supports Enter/Space activation', (WidgetTester tester) async {
        bool buttonPressed = false;

        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                  onEditProfile: () => buttonPressed = true,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find and focus a button
        final editButton = find.text('Edit Profile');
        if (editButton.evaluate().isNotEmpty) {
          await tester.tap(editButton);
          await tester.pumpAndSettle();

          // Test Enter key activation
          await tester.sendKeyEvent(LogicalKeyboardKey.enter);
          await tester.pumpAndSettle();

          expect(buttonPressed, isTrue,
                 reason: 'Enter key should activate focused button');
        }
      });

      testWidgets('supports arrow key navigation', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Test arrow key navigation (implementation depends on specific widgets)
        await tester.sendKeyEvent(LogicalKeyboardKey.arrowDown);
        await tester.pumpAndSettle();

        await tester.sendKeyEvent(LogicalKeyboardKey.arrowUp);
        await tester.pumpAndSettle();

        // Verify navigation doesn't cause errors
        expect(tester.takeException(), isNull,
               reason: 'Arrow key navigation should not cause exceptions');
      });
    });

    group('Screen Reader Compatibility', () {
      testWidgets('provides live region updates', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find live regions for dynamic content updates
        final liveRegions = find.byWidgetPredicate(
          (widget) => widget is Semantics && widget.properties.liveRegion == true,
        );

        expect(liveRegions, findsWidgets,
               reason: 'Should have live regions for dynamic content updates');
      });

      testWidgets('provides proper semantic structure', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify semantic structure
        final semanticElements = find.byType(Semantics);
        expect(semanticElements, findsWidgets,
               reason: 'Should have semantic elements for screen readers');

        // Check for proper labels
        int elementsWithLabels = 0;
        for (final element in semanticElements.evaluate()) {
          final semantics = element.widget as Semantics;
          if (semantics.properties.label != null && 
              semantics.properties.label!.isNotEmpty) {
            elementsWithLabels++;
          }
        }

        expect(elementsWithLabels, greaterThan(0),
               reason: 'Should have semantic elements with meaningful labels');
      });

      testWidgets('excludes decorative elements from semantics', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find elements that exclude semantics (decorative elements)
        final excludedElements = find.byWidgetPredicate(
          (widget) => widget is ExcludeSemantics,
        );

        // Decorative elements should be excluded from semantics tree
        // This is good practice for screen reader optimization
        for (final element in excludedElements.evaluate()) {
          expect(element.widget, isA<ExcludeSemantics>(),
                 reason: 'Decorative elements should be excluded from semantics');
        }
      });
    });

    group('Semantic Markup Validation', () {
      testWidgets('validates proper use of semantic properties', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find all semantic elements
        final semanticElements = find.byType(Semantics);
        
        for (final element in semanticElements.evaluate()) {
          final semantics = element.widget as Semantics;
          final properties = semantics.properties;

          // Validate button semantics
          if (properties.button == true) {
            expect(properties.label, isNotNull,
                   reason: 'Buttons should have semantic labels');
            expect(properties.enabled, isNotNull,
                   reason: 'Buttons should specify enabled state');
          }

          // Validate header semantics
          if (properties.header == true) {
            expect(properties.label, isNotNull,
                   reason: 'Headers should have semantic labels');
          }

          // Validate text field semantics
          if (properties.textField == true) {
            expect(properties.label, isNotNull,
                   reason: 'Text fields should have semantic labels');
          }
        }
      });

      testWidgets('validates tooltip accessibility', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find tooltips
        final tooltips = find.byType(Tooltip);
        
        for (final tooltipElement in tooltips.evaluate()) {
          final tooltip = tooltipElement.widget as Tooltip;
          
          expect(tooltip.message.isNotEmpty, isTrue,
                 reason: 'Tooltips should have meaningful messages');
        }
      });
    });

    group('Accessibility Integration Tests', () {
      testWidgets('validates complete user flow accessibility', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Simulate complete user interaction flow
        
        // 1. Navigate using keyboard
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle();

        // 2. Activate elements using keyboard
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pumpAndSettle();

        // 3. Verify no accessibility violations occurred
        expect(tester.takeException(), isNull,
               reason: 'Keyboard navigation should not cause exceptions');

        // 4. Verify semantic structure is maintained
        final semanticElements = find.byType(Semantics);
        expect(semanticElements, findsWidgets,
               reason: 'Semantic structure should be maintained throughout interaction');
      });

      testWidgets('validates accessibility in different states', (WidgetTester tester) async {
        // Test loading state
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: null, // Triggers loading state
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify loading state has proper semantics
        final loadingSemantics = find.byWidgetPredicate(
          (widget) => widget is Semantics && 
                      widget.properties.label?.contains('loading') == true,
        );

        // Loading states should be announced to screen readers
        expect(loadingSemantics.evaluate().isNotEmpty || 
               find.byType(CircularProgressIndicator).evaluate().isNotEmpty, 
               isTrue,
               reason: 'Loading states should be accessible');

        // Test with data
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Verify data state maintains accessibility
        final dataSemantics = find.byType(Semantics);
        expect(dataSemantics, findsWidgets,
               reason: 'Data state should maintain semantic structure');
      });
    });
  });
}

/// Helper extension for accessibility testing
extension AccessibilityTestExtensions on WidgetTester {
  /// Validates that all interactive elements meet minimum touch target size
  Future<void> validateTouchTargets() async {
    final interactiveElements = [
      ...find.byType(GestureDetector).evaluate(),
      ...find.byType(InkWell).evaluate(),
      ...find.byType(ElevatedButton).evaluate(),
      ...find.byType(OutlinedButton).evaluate(),
      ...find.byType(TextButton).evaluate(),
    ];

    for (final element in interactiveElements) {
      final renderBox = element.renderObject as RenderBox?;
      
      if (renderBox != null && renderBox.hasSize) {
        final size = renderBox.size;
        final meetsMinimum = size.width >= AccessibilityHelper.minTouchTargetSize ||
                            size.height >= AccessibilityHelper.minTouchTargetSize;
        
        expect(meetsMinimum, isTrue,
               reason: 'Interactive element should meet minimum touch target size');
      }
    }
  }

  /// Validates semantic structure
  Future<void> validateSemanticStructure() async {
    final semanticElements = find.byType(Semantics);
    expect(semanticElements, findsWidgets,
           reason: 'Should have semantic elements');

    // Verify headers exist
    final headers = find.byWidgetPredicate(
      (widget) => widget is Semantics && widget.properties.header == true,
    );
    expect(headers, findsWidgets,
           reason: 'Should have semantic headers for proper document structure');
  }

  /// Validates color contrast
  Future<void> validateColorContrast(ThemeData theme) async {
    // Test primary text contrast
    final primaryTextContrast = AccessibilityHelper.hasGoodContrast(
      foreground: theme.colorScheme.onSurface,
      background: theme.colorScheme.surface,
    );
    expect(primaryTextContrast, isTrue,
           reason: 'Primary text should meet WCAG AA contrast requirements');

    // Test button contrast
    final buttonContrast = AccessibilityHelper.hasGoodContrast(
      foreground: theme.colorScheme.onPrimary,
      background: theme.colorScheme.primary,
    );
    expect(buttonContrast, isTrue,
           reason: 'Button text should meet WCAG AA contrast requirements');
  }
}