import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/utils/accessibility_helper.dart';
import '../../lib/widgets/user_avatar_widget.dart';
import '../../lib/theme/app_theme.dart';

/// Core accessibility tests for the account profile redesign components
/// 
/// Tests the essential accessibility features:
/// - Semantic labels and hints
/// - Touch target sizes
/// - Color contrast validation
/// - Keyboard navigation support
/// - Screen reader compatibility
void main() {
  group('Core Accessibility Tests', () {
    group('AccessibilityHelper Utilities', () {
      test('generates correct avatar semantic labels', () {
        final label = AccessibilityHelper.generateAvatarSemanticLabel(
          userName: '<PERSON>',
          role: 'parent',
          hasImage: true,
          isEditable: true,
        );

        expect(label, contains('Profile picture for <PERSON>'));
        expect(label, contains('role: parent'));
        expect(label, contains('double tap to change picture'));
      });

      test('generates correct avatar semantic labels without image', () {
        final label = AccessibilityHelper.generateAvatarSemanticLabel(
          userName: '<PERSON>',
          role: 'caregiver',
          hasImage: false,
          isEditable: false,
        );

        expect(label, contains('Profile initials for Jane Smith'));
        expect(label, contains('role: caregiver'));
        expect(label, isNot(contains('double tap')));
      });

      test('generates correct completion semantic labels', () {
        final label = AccessibilityHelper.generateCompletionSemanticLabel(
          percentage: 75.0,
          nextAction: 'Set up preferences',
          isComplete: false,
        );

        expect(label, contains('75 percent complete'));
        expect(label, contains('Next step: Set up preferences'));
      });

      test('generates correct completion semantic labels when complete', () {
        final label = AccessibilityHelper.generateCompletionSemanticLabel(
          percentage: 100.0,
          nextAction: '',
          isComplete: true,
        );

        expect(label, contains('100% complete'));
        expect(label, isNot(contains('Next step')));
      });

      test('generates correct family member semantic labels', () {
        final label = AccessibilityHelper.generateFamilyMemberSemanticLabel(
          memberName: 'Jane Doe',
          role: 'parent',
          status: 'active',
          lastActivity: 'yesterday',
        );

        expect(label, contains('Family member: Jane Doe'));
        expect(label, contains('role: parent'));
        expect(label, contains('status: active'));
        expect(label, contains('last active: yesterday'));
      });

      test('generates correct family member semantic labels without activity', () {
        final label = AccessibilityHelper.generateFamilyMemberSemanticLabel(
          memberName: 'Bob Smith',
          role: 'caregiver',
          status: 'pending',
        );

        expect(label, contains('Family member: Bob Smith'));
        expect(label, contains('role: caregiver'));
        expect(label, contains('status: pending'));
        expect(label, isNot(contains('last active')));
      });

      test('generates correct subscription semantic labels', () {
        final label = AccessibilityHelper.generateSubscriptionSemanticLabel(
          planName: 'Premium Plan',
          status: 'active',
          renewalDate: 'January 15, 2024',
          price: 9.99,
        );

        expect(label, contains('Subscription: Premium Plan plan'));
        expect(label, contains('status: active'));
        expect(label, contains('price: \$9.99 per month'));
        expect(label, contains('renews: January 15, 2024'));
      });

      test('generates correct interaction hints', () {
        final buttonHint = AccessibilityHelper.generateInteractionHint(
          action: 'edit profile',
          isButton: true,
          requiresDoubleTab: false,
        );
        expect(buttonHint, equals('Button. Tap to edit profile'));

        final doubleTapHint = AccessibilityHelper.generateInteractionHint(
          action: 'change picture',
          isButton: false,
          requiresDoubleTab: true,
        );
        expect(doubleTapHint, equals('Double tap to change picture'));
      });
    });

    group('Color Contrast Validation', () {
      test('validates good color contrast', () {
        final goodContrast = AccessibilityHelper.hasGoodContrast(
          foreground: Colors.black,
          background: Colors.white,
          minRatio: 4.5,
        );
        expect(goodContrast, isTrue);
      });

      test('validates poor color contrast', () {
        final poorContrast = AccessibilityHelper.hasGoodContrast(
          foreground: Colors.grey[400]!,
          background: Colors.grey[300]!,
          minRatio: 4.5,
        );
        expect(poorContrast, isFalse);
      });

      test('validates enhanced contrast for AAA compliance', () {
        final enhancedContrast = AccessibilityHelper.hasGoodContrast(
          foreground: Colors.black,
          background: Colors.white,
          minRatio: 7.0, // WCAG AAA standard
        );
        expect(enhancedContrast, isTrue);
      });

      test('gets accessible color when contrast is poor', () {
        final accessibleColor = AccessibilityHelper.getAccessibleColor(
          baseColor: Colors.grey[400]!,
          backgroundColor: Colors.grey[300]!,
          isDark: false,
        );
        
        // Should return high contrast alternative
        expect(accessibleColor, equals(Colors.black));
      });

      test('returns original color when contrast is good', () {
        final accessibleColor = AccessibilityHelper.getAccessibleColor(
          baseColor: Colors.black,
          backgroundColor: Colors.white,
          isDark: false,
        );
        
        // Should return original color
        expect(accessibleColor, equals(Colors.black));
      });
    });

    group('Text Formatting for Accessibility', () {
      test('formats role text correctly', () {
        expect(AccessibilityHelper.formatRoleForAccessibility('parent'), 
               equals('Parent'));
        expect(AccessibilityHelper.formatRoleForAccessibility('family_admin'), 
               equals('Family Admin'));
        expect(AccessibilityHelper.formatRoleForAccessibility('primary_caregiver'), 
               equals('Primary Caregiver'));
        expect(AccessibilityHelper.formatRoleForAccessibility('ADMIN'), 
               equals('Admin'));
      });

      test('formats dates for accessibility', () {
        final now = DateTime.now();
        
        final today = AccessibilityHelper.formatDateForAccessibility(now);
        expect(today, equals('today'));

        final yesterday = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 1)),
        );
        expect(yesterday, equals('yesterday'));

        final threeDaysAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 3)),
        );
        expect(threeDaysAgo, equals('3 days ago'));

        final oneWeekAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 7)),
        );
        expect(oneWeekAgo, equals('1 week ago'));

        final twoWeeksAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 14)),
        );
        expect(twoWeeksAgo, equals('2 weeks ago'));

        final oneMonthAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 30)),
        );
        expect(oneMonthAgo, equals('1 month ago'));

        final twoMonthsAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 60)),
        );
        expect(twoMonthsAgo, equals('2 months ago'));

        final oneYearAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 365)),
        );
        expect(oneYearAgo, equals('1 year ago'));

        final twoYearsAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 730)),
        );
        expect(twoYearsAgo, equals('2 years ago'));
      });
    });

    group('Touch Target Size Validation', () {
      test('validates minimum touch target size constants', () {
        expect(AccessibilityHelper.minTouchTargetSize, equals(44.0));
        expect(AccessibilityHelper.recommendedTouchTargetSize, equals(48.0));
      });
    });

    group('Keyboard Navigation Support', () {
      test('handles keyboard events correctly', () {
        bool activateCalled = false;
        bool nextCalled = false;
        bool previousCalled = false;

        // Test Enter key
        final enterEvent = KeyDownEvent(
          physicalKey: PhysicalKeyboardKey.enter,
          logicalKey: LogicalKeyboardKey.enter,
          character: '\n',
          timeStamp: Duration.zero,
        );

        final enterResult = AccessibilityHelper.handleKeyboardNavigation(
          event: enterEvent,
          onActivate: () => activateCalled = true,
          onNext: () => nextCalled = true,
          onPrevious: () => previousCalled = true,
        );

        expect(enterResult, equals(KeyEventResult.handled));
        expect(activateCalled, isTrue);
        expect(nextCalled, isFalse);
        expect(previousCalled, isFalse);

        // Reset flags
        activateCalled = false;
        nextCalled = false;
        previousCalled = false;

        // Test Space key
        final spaceEvent = KeyDownEvent(
          physicalKey: PhysicalKeyboardKey.space,
          logicalKey: LogicalKeyboardKey.space,
          character: ' ',
          timeStamp: Duration.zero,
        );

        final spaceResult = AccessibilityHelper.handleKeyboardNavigation(
          event: spaceEvent,
          onActivate: () => activateCalled = true,
          onNext: () => nextCalled = true,
          onPrevious: () => previousCalled = true,
        );

        expect(spaceResult, equals(KeyEventResult.handled));
        expect(activateCalled, isTrue);

        // Reset flags
        activateCalled = false;
        nextCalled = false;
        previousCalled = false;

        // Test Arrow Down key
        final arrowDownEvent = KeyDownEvent(
          physicalKey: PhysicalKeyboardKey.arrowDown,
          logicalKey: LogicalKeyboardKey.arrowDown,
          character: null,
          timeStamp: Duration.zero,
        );

        final arrowDownResult = AccessibilityHelper.handleKeyboardNavigation(
          event: arrowDownEvent,
          onActivate: () => activateCalled = true,
          onNext: () => nextCalled = true,
          onPrevious: () => previousCalled = true,
        );

        expect(arrowDownResult, equals(KeyEventResult.handled));
        expect(nextCalled, isTrue);
        expect(activateCalled, isFalse);
        expect(previousCalled, isFalse);

        // Reset flags
        activateCalled = false;
        nextCalled = false;
        previousCalled = false;

        // Test Arrow Up key
        final arrowUpEvent = KeyDownEvent(
          physicalKey: PhysicalKeyboardKey.arrowUp,
          logicalKey: LogicalKeyboardKey.arrowUp,
          character: null,
          timeStamp: Duration.zero,
        );

        final arrowUpResult = AccessibilityHelper.handleKeyboardNavigation(
          event: arrowUpEvent,
          onActivate: () => activateCalled = true,
          onNext: () => nextCalled = true,
          onPrevious: () => previousCalled = true,
        );

        expect(arrowUpResult, equals(KeyEventResult.handled));
        expect(previousCalled, isTrue);
        expect(activateCalled, isFalse);
        expect(nextCalled, isFalse);
      });

      test('ignores unhandled keyboard events', () {
        bool anyCalled = false;

        final unhandledEvent = KeyDownEvent(
          physicalKey: PhysicalKeyboardKey.keyA,
          logicalKey: LogicalKeyboardKey.keyA,
          character: 'a',
          timeStamp: Duration.zero,
        );

        final result = AccessibilityHelper.handleKeyboardNavigation(
          event: unhandledEvent,
          onActivate: () => anyCalled = true,
          onNext: () => anyCalled = true,
          onPrevious: () => anyCalled = true,
        );

        expect(result, equals(KeyEventResult.ignored));
        expect(anyCalled, isFalse);
      });
    });

    group('Widget Accessibility Tests', () {
      testWidgets('UserAvatarWidget has proper semantic labels', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
                isEditable: true,
              ),
            ),
          ),
        );

        // Find the semantic widget
        final semanticsFinder = find.byType(Semantics);
        expect(semanticsFinder, findsWidgets);

        // Verify semantic properties exist
        final semanticsWidgets = tester.widgetList<Semantics>(semanticsFinder);
        bool foundProperSemantics = false;

        for (final semantics in semanticsWidgets) {
          if (semantics.properties.label != null && 
              semantics.properties.label!.contains('User avatar')) {
            foundProperSemantics = true;
            expect(semantics.properties.label, contains('Parent'));
            expect(semantics.properties.hint, contains('change profile picture'));
            expect(semantics.properties.button, isTrue);
            break;
          }
        }

        expect(foundProperSemantics, isTrue, 
               reason: 'Should find semantic widget with proper avatar label');
      });

      testWidgets('UserAvatarWidget meets minimum touch target size', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 30.0, // Small size to test minimum enforcement
                isEditable: true,
              ),
            ),
          ),
        );

        // Find the gesture detector or focus widget
        final interactiveWidget = find.byType(Focus);
        expect(interactiveWidget, findsOneWidget);

        // Verify the widget meets minimum touch target size
        final renderBox = tester.renderObject(interactiveWidget) as RenderBox;
        expect(renderBox.size.width, greaterThanOrEqualTo(AccessibilityHelper.minTouchTargetSize));
        expect(renderBox.size.height, greaterThanOrEqualTo(AccessibilityHelper.minTouchTargetSize));
      });

      testWidgets('UserAvatarWidget supports keyboard navigation', (WidgetTester tester) async {
        bool tapCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
                isEditable: true,
                onTap: () => tapCalled = true,
              ),
            ),
          ),
        );

        // Find the focus widget
        final focusWidget = find.byType(Focus);
        expect(focusWidget, findsOneWidget);

        // Focus the widget
        await tester.tap(focusWidget);
        await tester.pumpAndSettle();

        // Simulate Enter key press
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pumpAndSettle();

        expect(tapCalled, isTrue, reason: 'Enter key should activate the avatar');
      });

      testWidgets('validates color contrast in light theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Get theme colors
        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);
        
        // Test primary text on background
        final hasGoodContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
        );
        
        expect(hasGoodContrast, isTrue, 
               reason: 'Primary text should have good contrast with background');
      });

      testWidgets('validates color contrast in dark theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.darkTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Get theme colors
        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);
        
        // Test primary text on background
        final hasGoodContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
        );
        
        expect(hasGoodContrast, isTrue, 
               reason: 'Primary text should have good contrast with background in dark theme');
      });
    });

    group('Accessibility Extension Tests', () {
      testWidgets('withSemanticLabel extension works correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Container(
                width: 100,
                height: 100,
                color: Colors.blue,
              ).withSemanticLabel('Test container', hint: 'This is a test'),
            ),
          ),
        );

        final semantics = find.byType(Semantics);
        expect(semantics, findsOneWidget);

        final semanticsWidget = tester.widget<Semantics>(semantics);
        expect(semanticsWidget.properties.label, equals('Test container'));
        expect(semanticsWidget.properties.hint, equals('This is a test'));
      });

      testWidgets('withMinTouchTarget extension works correctly', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: Container(
                width: 20,
                height: 20,
                color: Colors.red,
              ).withMinTouchTarget(),
            ),
          ),
        );

        // Find the constrained box that enforces minimum size
        final constrainedBox = find.byType(ConstrainedBox);
        expect(constrainedBox, findsOneWidget);

        final constraints = tester.widget<ConstrainedBox>(constrainedBox).constraints;
        expect(constraints.minWidth, equals(AccessibilityHelper.minTouchTargetSize));
        expect(constraints.minHeight, equals(AccessibilityHelper.minTouchTargetSize));
      });
    });
  });
}