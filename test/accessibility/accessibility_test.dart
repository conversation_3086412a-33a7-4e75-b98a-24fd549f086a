import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../lib/models/user_profile.dart';
import '../../lib/models/family_member.dart';
import '../../lib/models/subscription_info.dart';
import '../../lib/models/profile_completion_status.dart';
import '../../lib/models/enums.dart';
import '../../lib/services/account_profile_controller.dart';
import '../../lib/widgets/user_avatar_widget.dart';
import '../../lib/widgets/profile_header_card.dart';
import '../../lib/widgets/family_sharing_card.dart';
import '../../lib/widgets/account_management_card.dart';
import '../../lib/widgets/user_profile_account_section.dart';
import '../../lib/utils/accessibility_helper.dart';
import '../../lib/theme/app_theme.dart';

void main() {
  group('Accessibility Tests', () {
    late UserProfile testUserProfile;
    late List<FamilyMember> testFamilyMembers;
    late SubscriptionInfo testSubscription;
    late ProfileCompletionStatus testCompletion;

    setUp(() {
      testUserProfile = UserProfile(
        id: 'test-id',
        email: '<EMAIL>',
        fullName: 'John Doe',
        role: 'parent',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        lastSignInAt: DateTime.now().subtract(const Duration(hours: 2)),
        signInCount: 15,
        avatarUrl: null,
        isEmailVerified: true,
        isTwoFactorEnabled: false,
        permissions: {'can_edit': true},
      );

      testFamilyMembers = [
        FamilyMember(
          id: 'member-1',
          fullName: 'Jane Doe',
          email: '<EMAIL>',
          role: 'parent',
          status: FamilyMemberStatus.active,
          joinedAt: DateTime.now().subtract(const Duration(days: 20)),
          lastActiveAt: DateTime.now().subtract(const Duration(hours: 1)),
          permissions: {'can_view': true},
        ),
        FamilyMember(
          id: 'member-2',
          fullName: 'Grandma Smith',
          email: '<EMAIL>',
          role: 'caregiver',
          status: FamilyMemberStatus.pending,
          joinedAt: DateTime.now().subtract(const Duration(days: 1)),
          permissions: {'can_view': true},
        ),
      ];

      testSubscription = SubscriptionInfo(
        planId: 'premium',
        planName: 'Premium Plan',
        status: SubscriptionStatus.active,
        monthlyPrice: 9.99,
        features: ['Unlimited tracking', 'AI insights', 'Family sharing'],
        renewalDate: DateTime.now().add(const Duration(days: 30)),
      );

      testCompletion = ProfileCompletionStatus(
        percentage: 75.0,
        completedSteps: ['basic_info', 'avatar'],
        remainingSteps: ['preferences'],
        nextRecommendedAction: 'Set up notification preferences',
      );
    });

    group('UserAvatarWidget Accessibility', () {
      testWidgets('has proper semantic labels', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
                isEditable: true,
              ),
            ),
          ),
        );

        // Find the semantic widget
        final semanticsFinder = find.byType(Semantics);
        expect(semanticsFinder, findsWidgets);

        // Verify semantic properties
        final semantics = tester.widget<Semantics>(semanticsFinder.first);
        expect(semantics.properties.label, contains('User avatar'));
        expect(semantics.properties.label, contains('Parent'));
        expect(semantics.properties.hint, contains('change profile picture'));
        expect(semantics.properties.button, isTrue);
      });

      testWidgets('meets minimum touch target size', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 30.0, // Small size
                isEditable: true,
              ),
            ),
          ),
        );

        // Find the gesture detector
        final gestureDetector = find.byType(GestureDetector);
        expect(gestureDetector, findsOneWidget);

        // Verify the widget meets minimum touch target size
        final renderBox = tester.renderObject(gestureDetector) as RenderBox;
        expect(renderBox.size.width, greaterThanOrEqualTo(AccessibilityHelper.minTouchTargetSize));
        expect(renderBox.size.height, greaterThanOrEqualTo(AccessibilityHelper.minTouchTargetSize));
      });

      testWidgets('supports keyboard navigation', (WidgetTester tester) async {
        bool tapCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
                isEditable: true,
                onTap: () => tapCalled = true,
              ),
            ),
          ),
        );

        // Find the focus widget
        final focusWidget = find.byType(Focus);
        expect(focusWidget, findsOneWidget);

        // Focus the widget
        await tester.tap(focusWidget);
        await tester.pumpAndSettle();

        // Simulate Enter key press
        await tester.sendKeyEvent(LogicalKeyboardKey.enter);
        await tester.pumpAndSettle();

        expect(tapCalled, isTrue);
      });
    });

    group('ProfileHeaderCard Accessibility', () {
      testWidgets('has proper heading hierarchy', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: ProfileHeaderCard(
                userProfile: testUserProfile,
                completionStatus: testCompletion,
              ),
            ),
          ),
        );

        // Find semantic headers
        final headerSemantics = find.byWidgetPredicate(
          (widget) => widget is Semantics && 
                      widget.properties.header == true,
        );
        
        expect(headerSemantics, findsWidgets);
      });

      testWidgets('provides accessible progress information', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: ProfileHeaderCard(
                userProfile: testUserProfile,
                completionStatus: testCompletion,
              ),
            ),
          ),
        );

        // Find progress indicator
        final progressIndicator = find.byType(LinearProgressIndicator);
        expect(progressIndicator, findsOneWidget);

        // Verify progress has semantic information
        final progressSemantics = find.ancestor(
          of: progressIndicator,
          matching: find.byWidgetPredicate(
            (widget) => widget is Semantics && 
                        widget.properties.label != null &&
                        widget.properties.label!.contains('75'),
          ),
        );
        expect(progressSemantics, findsOneWidget);
      });

      testWidgets('edit button meets accessibility requirements', (WidgetTester tester) async {
        bool editCalled = false;

        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: ProfileHeaderCard(
                userProfile: testUserProfile,
                onEditProfile: () => editCalled = true,
              ),
            ),
          ),
        );

        // Find edit button
        final editButton = find.text('Edit Profile');
        expect(editButton, findsOneWidget);

        // Verify button semantics
        final buttonSemantics = find.ancestor(
          of: editButton,
          matching: find.byWidgetPredicate(
            (widget) => widget is Semantics && 
                        widget.properties.button == true,
          ),
        );
        expect(buttonSemantics, findsOneWidget);

        // Verify minimum touch target
        final renderBox = tester.renderObject(editButton) as RenderBox;
        expect(renderBox.size.height, greaterThanOrEqualTo(AccessibilityHelper.minTouchTargetSize));
      });
    });

    group('FamilySharingCard Accessibility', () {
      testWidgets('provides accessible family member information', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: FamilySharingCard(
                familyMembers: testFamilyMembers,
                canManageFamily: true,
              ),
            ),
          ),
        );

        // Verify family member list has semantic information
        final memberListSemantics = find.byWidgetPredicate(
          (widget) => widget is Semantics && 
                      widget.properties.label != null &&
                      widget.properties.label!.contains('Family members list'),
        );
        expect(memberListSemantics, findsOneWidget);
      });

      testWidgets('invite button is accessible', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: FamilySharingCard(
                familyMembers: testFamilyMembers,
                canManageFamily: true,
              ),
            ),
          ),
        );

        // Find invite button
        final inviteButton = find.text('Invite Member');
        expect(inviteButton, findsOneWidget);

        // Verify button meets minimum touch target
        final renderBox = tester.renderObject(inviteButton) as RenderBox;
        expect(renderBox.size.height, greaterThanOrEqualTo(AccessibilityHelper.minTouchTargetSize));
      });
    });

    group('AccountManagementCard Accessibility', () {
      testWidgets('subscription information is accessible', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: AccountManagementCard(
                subscription: testSubscription,
                hasAdminPrivileges: true,
                isEmailVerified: true,
                isTwoFactorEnabled: false,
              ),
            ),
          ),
        );

        // Find subscription information
        final subscriptionText = find.text('Premium Plan');
        expect(subscriptionText, findsOneWidget);

        // Verify semantic structure
        final headerSemantics = find.byWidgetPredicate(
          (widget) => widget is Semantics && 
                      widget.properties.header == true,
        );
        expect(headerSemantics, findsWidgets);
      });
    });

    group('Color Contrast Validation', () {
      testWidgets('validates color contrast in light theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Get theme colors
        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);
        
        // Test primary text on background
        final hasGoodContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
        );
        
        expect(hasGoodContrast, isTrue, 
               reason: 'Primary text should have good contrast with background');
      });

      testWidgets('validates color contrast in dark theme', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.darkTheme,
            home: Scaffold(
              body: UserAvatarWidget(
                initials: 'JD',
                role: 'parent',
                size: 80.0,
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Get theme colors
        final context = tester.element(find.byType(Scaffold));
        final theme = Theme.of(context);
        
        // Test primary text on background
        final hasGoodContrast = AccessibilityHelper.hasGoodContrast(
          foreground: theme.colorScheme.onSurface,
          background: theme.colorScheme.surface,
        );
        
        expect(hasGoodContrast, isTrue, 
               reason: 'Primary text should have good contrast with background in dark theme');
      });
    });

    group('Screen Reader Support', () {
      testWidgets('provides live region updates', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: ProfileHeaderCard(
                userProfile: testUserProfile,
                completionStatus: testCompletion,
              ),
            ),
          ),
        );

        // Find live region semantics
        final liveRegionSemantics = find.byWidgetPredicate(
          (widget) => widget is Semantics && 
                      widget.properties.liveRegion == true,
        );
        expect(liveRegionSemantics, findsWidgets);
      });
    });

    group('Keyboard Navigation', () {
      testWidgets('supports tab navigation', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: Column(
                children: [
                  UserAvatarWidget(
                    initials: 'JD',
                    role: 'parent',
                    size: 80.0,
                    isEditable: true,
                  ),
                  ProfileHeaderCard(
                    userProfile: testUserProfile,
                    isEditable: true,
                  ),
                ],
              ),
            ),
          ),
        );

        // Test tab navigation
        await tester.sendKeyEvent(LogicalKeyboardKey.tab);
        await tester.pumpAndSettle();

        // Verify focus moves between elements
        final focusedElements = find.byWidgetPredicate(
          (widget) => widget is Focus && widget.focusNode?.hasFocus == true,
        );
        expect(focusedElements, findsWidgets);
      });
    });

    group('Touch Target Sizes', () {
      testWidgets('all interactive elements meet minimum size', (WidgetTester tester) async {
        await tester.pumpWidget(
          ChangeNotifierProvider(
            create: (_) => AccountProfileController(),
            child: MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: UserProfileAccountSection(
                  userProfile: testUserProfile,
                ),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // Find all gesture detectors (interactive elements)
        final gestureDetectors = find.byType(GestureDetector);
        
        for (int i = 0; i < gestureDetectors.evaluate().length; i++) {
          final element = gestureDetectors.evaluate().elementAt(i);
          final renderBox = element.renderObject as RenderBox?;
          
          if (renderBox != null) {
            expect(
              renderBox.size.width >= AccessibilityHelper.minTouchTargetSize ||
              renderBox.size.height >= AccessibilityHelper.minTouchTargetSize,
              isTrue,
              reason: 'Interactive element $i should meet minimum touch target size',
            );
          }
        }
      });
    });

    group('Semantic Labels', () {
      test('generates correct avatar semantic labels', () {
        final label = AccessibilityHelper.generateAvatarSemanticLabel(
          userName: 'John Doe',
          role: 'parent',
          hasImage: true,
          isEditable: true,
        );

        expect(label, contains('Profile picture for John Doe'));
        expect(label, contains('role: parent'));
        expect(label, contains('double tap to change picture'));
      });

      test('generates correct completion semantic labels', () {
        final label = AccessibilityHelper.generateCompletionSemanticLabel(
          percentage: 75.0,
          nextAction: 'Set up preferences',
          isComplete: false,
        );

        expect(label, contains('75 percent complete'));
        expect(label, contains('Next step: Set up preferences'));
      });

      test('generates correct family member semantic labels', () {
        final label = AccessibilityHelper.generateFamilyMemberSemanticLabel(
          memberName: 'Jane Doe',
          role: 'parent',
          status: 'active',
          lastActivity: 'yesterday',
        );

        expect(label, contains('Family member: Jane Doe'));
        expect(label, contains('role: parent'));
        expect(label, contains('status: active'));
        expect(label, contains('last active: yesterday'));
      });

      test('generates correct subscription semantic labels', () {
        final label = AccessibilityHelper.generateSubscriptionSemanticLabel(
          planName: 'Premium Plan',
          status: 'active',
          renewalDate: 'January 15, 2024',
          price: 9.99,
        );

        expect(label, contains('Subscription: Premium Plan plan'));
        expect(label, contains('status: active'));
        expect(label, contains('price: \$9.99 per month'));
        expect(label, contains('renews: January 15, 2024'));
      });
    });

    group('Accessibility Helper Utilities', () {
      test('validates color contrast correctly', () {
        // Test good contrast
        final goodContrast = AccessibilityHelper.hasGoodContrast(
          foreground: Colors.black,
          background: Colors.white,
        );
        expect(goodContrast, isTrue);

        // Test poor contrast
        final poorContrast = AccessibilityHelper.hasGoodContrast(
          foreground: Colors.grey[400]!,
          background: Colors.grey[300]!,
        );
        expect(poorContrast, isFalse);
      });

      test('formats role text for accessibility', () {
        expect(AccessibilityHelper.formatRoleForAccessibility('parent'), equals('Parent'));
        expect(AccessibilityHelper.formatRoleForAccessibility('family_admin'), equals('Family Admin'));
        expect(AccessibilityHelper.formatRoleForAccessibility('primary_caregiver'), equals('Primary Caregiver'));
      });

      test('formats dates for accessibility', () {
        final now = DateTime.now();
        final today = AccessibilityHelper.formatDateForAccessibility(now);
        expect(today, equals('today'));

        final yesterday = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 1)),
        );
        expect(yesterday, equals('yesterday'));

        final threeDaysAgo = AccessibilityHelper.formatDateForAccessibility(
          now.subtract(const Duration(days: 3)),
        );
        expect(threeDaysAgo, equals('3 days ago'));
      });
    });
  });
}