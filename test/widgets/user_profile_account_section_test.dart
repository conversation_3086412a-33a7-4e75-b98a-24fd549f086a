import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/user_profile_account_section.dart';

// Simple mock controller for testing
class MockAccountProfileController extends ChangeNotifier {
  bool get isLoading => false;
  bool get isLoadingProfile => false;
  bool get isLoadingFamilyMembers => false;
  bool get isLoadingSubscription => false;
  bool get hasErrors => false;
  bool get canManageFamily => true;
  int get pendingInvitationsCount => 1;
  
  dynamic get userProfile => null;
  List<dynamic> get familyMembers => [];
  dynamic get subscriptionInfo => null;
  dynamic get profileCompletionStatus => null;
  String? get profileError => null;
  String? get familyError => null;
  String? get subscriptionError => null;
  
  Future<void> refresh() async {}
  void clearErrors() {}
  
  Future<bool> inviteFamilyMember({
    required String email,
    required String role,
    Map<String, bool>? permissions,
    String? customMessage,
  }) async => true;
  
  Future<bool> resendInvitation(String memberId) async => true;
  Future<bool> cancelInvitation(String memberId) async => true;
}

void main() {
  group('UserProfileAccountSection', () {
    late MockAccountProfileController mockController;
    
    setUp(() {
      mockController = MockAccountProfileController();
    });
    
    Widget createTestWidget({
      dynamic userProfile,
      VoidCallback? onEditProfile,
      VoidCallback? onNavigateToUserManagement,
      VoidCallback? onSubscriptionTap,
      VoidCallback? onFamilySharingTap,
      bool showHeader = true,
      bool isCompact = false,
    }) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            home: Scaffold(
              body: ChangeNotifierProvider<MockAccountProfileController>.value(
                value: mockController,
                child: UserProfileAccountSection(
                  userProfile: userProfile,
                  onEditProfile: onEditProfile,
                  onNavigateToUserManagement: onNavigateToUserManagement,
                  onSubscriptionTap: onSubscriptionTap,
                  onFamilySharingTap: onFamilySharingTap,
                  showHeader: showHeader,
                  isCompact: isCompact,
                ),
              ),
            ),
          );
        },
      );
    }
    
    group('Widget Structure', () {
      testWidgets('displays section header when showHeader is true', (tester) async {
        await tester.pumpWidget(createTestWidget(showHeader: true));
        await tester.pumpAndSettle();
        
        expect(find.text('Account & Profile'), findsOneWidget);
        expect(find.byIcon(Icons.account_circle_outlined), findsOneWidget);
      });
      
      testWidgets('hides section header when showHeader is false', (tester) async {
        await tester.pumpWidget(createTestWidget(showHeader: false));
        await tester.pumpAndSettle();
        
        expect(find.text('Account & Profile'), findsNothing);
      });
      
      testWidgets('displays empty profile state when no user profile', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        expect(find.text('Complete Your Profile'), findsOneWidget);
        expect(find.text('Create Profile'), findsOneWidget);
      });
    });
    
    group('Loading States', () {
      testWidgets('displays loading state when controller is loading', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        // Should show some content (empty state in this case)
        expect(find.byType(Card), findsWidgets);
      });
    });
    
    group('Interaction Callbacks', () {
      testWidgets('calls onEditProfile when create profile is tapped', (tester) async {
        bool editProfileCalled = false;
        
        await tester.pumpWidget(createTestWidget(
          onEditProfile: () => editProfileCalled = true,
        ));
        await tester.pumpAndSettle();
        
        // Find and tap create profile button
        final createButton = find.text('Create Profile');
        await tester.tap(createButton);
        await tester.pumpAndSettle();
        
        expect(editProfileCalled, isTrue);
      });
    });
    
    group('Responsive Layout', () {
      testWidgets('adapts layout for compact mode', (tester) async {
        await tester.pumpWidget(createTestWidget(isCompact: true));
        await tester.pumpAndSettle();
        
        // Should show the empty profile state
        expect(find.text('Complete Your Profile'), findsOneWidget);
      });
      
      testWidgets('uses full layout by default', (tester) async {
        await tester.pumpWidget(createTestWidget(isCompact: false));
        await tester.pumpAndSettle();
        
        // Should show the empty profile state
        expect(find.text('Complete Your Profile'), findsOneWidget);
      });
    });
    
    group('Accessibility', () {
      testWidgets('provides semantic labels for icons', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        // Check for semantic labels
        final accountIcon = find.byIcon(Icons.account_circle_outlined);
        expect(accountIcon, findsOneWidget);
        
        final iconWidget = tester.widget<Icon>(accountIcon);
        expect(iconWidget.semanticLabel, equals('Account section'));
      });
      
      testWidgets('supports screen reader navigation', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        // Verify that text elements are accessible
        expect(find.text('Account & Profile'), findsOneWidget);
        expect(find.text('Complete Your Profile'), findsOneWidget);
      });
    });
    
    group('Error Handling', () {
      testWidgets('handles controller gracefully', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        // Should show empty profile state
        expect(find.text('Complete Your Profile'), findsOneWidget);
      });
    });
    
    group('Basic Functionality', () {
      testWidgets('widget builds without errors', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        // Should build successfully
        expect(find.byType(UserProfileAccountSection), findsOneWidget);
      });
      
      testWidgets('shows appropriate content based on state', (tester) async {
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        // Should show empty profile state
        expect(find.text('Complete Your Profile'), findsOneWidget);
        expect(find.text('Create Profile'), findsOneWidget);
      });
    });
  });
}