import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/family_sharing_card_refactored.dart';
import '../../lib/models/family_member.dart';
import '../../lib/models/enums.dart';
import '../../lib/theme/app_theme.dart';

void main() {
  group('FamilySharingCard', () {
    late List<FamilyMember> mockFamilyMembers;

    setUp(() {
      mockFamilyMembers = [
        FamilyMember(
          id: '1',
          fullName: '<PERSON>',
          email: '<EMAIL>',
          role: 'parent',
          joinedAt: DateTime.now().subtract(const Duration(days: 30)),
          status: FamilyMemberStatus.active,
          permissions: {'view_activities': true},
          lastActiveAt: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        FamilyMember(
          id: '2',
          fullName: '<PERSON>',
          email: '<EMAIL>',
          role: 'caregiver',
          joinedAt: DateTime.now().subtract(const Duration(days: 15)),
          status: FamilyMemberStatus.pending,
          permissions: {'view_activities': true},
        ),
      ];
    });

    Widget createTestWidget(Widget child) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(body: child),
          );
        },
      );
    }

    testWidgets('displays loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const FamilySharingCard(
            familyMembers: [],
            isLoading: true,
          ),
        ),
      );

      // Verify loading skeleton is displayed
      expect(find.byType(Card), findsOneWidget);
      
      // Should not display actual content during loading
      expect(find.text('Family Sharing'), findsNothing);
    });

    testWidgets('displays error state correctly', (WidgetTester tester) async {
      const errorMessage = 'Failed to load family data';
      
      await tester.pumpWidget(
        createTestWidget(
          const FamilySharingCard(
            familyMembers: [],
            errorMessage: errorMessage,
          ),
        ),
      );

      expect(find.text('Unable to load family information'), findsOneWidget);
      expect(find.text(errorMessage), findsOneWidget);
      expect(find.text('Try Again'), findsOneWidget);
    });

    testWidgets('displays family members correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: mockFamilyMembers,
          ),
        ),
      );

      // Verify header is displayed
      expect(find.text('Family Sharing'), findsOneWidget);
      expect(find.text('Manage family members and sharing'), findsOneWidget);

      // Verify stats are displayed
      expect(find.text('Total'), findsOneWidget);
      expect(find.text('Active'), findsOneWidget);
      expect(find.text('Pending'), findsOneWidget);
      expect(find.text('2'), findsOneWidget); // Total count
      expect(find.text('1'), findsAtLeastNWidgets(2)); // Active and pending counts

      // Verify member overview
      expect(find.text('Family Members'), findsOneWidget);
    });

    testWidgets('displays empty state when no members', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const FamilySharingCard(
            familyMembers: [],
          ),
        ),
      );

      expect(find.text('No Family Members Yet'), findsOneWidget);
      expect(find.text('Invite family members to share baby care responsibilities'), findsOneWidget);
    });

    testWidgets('shows invite button when user can manage family', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: mockFamilyMembers,
            canManageFamily: true,
          ),
        ),
      );

      expect(find.text('Invite Member'), findsOneWidget);
    });

    testWidgets('hides invite button when user cannot manage family', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: mockFamilyMembers,
            canManageFamily: false,
          ),
        ),
      );

      expect(find.text('Invite Member'), findsNothing);
    });

    testWidgets('calls onInviteMember when invite button is tapped', (WidgetTester tester) async {
      bool inviteTapped = false;
      
      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: mockFamilyMembers,
            canManageFamily: true,
            onInviteMember: () => inviteTapped = true,
          ),
        ),
      );

      await tester.tap(find.text('Invite Member'));
      expect(inviteTapped, isTrue);
    });

    testWidgets('calls onMemberTap when member avatar is tapped', (WidgetTester tester) async {
      FamilyMember? tappedMember;
      
      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: mockFamilyMembers,
            onMemberTap: (member) => tappedMember = member,
          ),
        ),
      );

      // Find and tap the first member avatar
      await tester.tap(find.byType(GestureDetector).first);
      expect(tappedMember, isNotNull);
      expect(tappedMember?.id, mockFamilyMembers.first.id);
    });

    testWidgets('shows manage family button when callback provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: mockFamilyMembers,
            canManageFamily: true,
            onManageFamily: () {},
          ),
        ),
      );

      expect(find.text('Manage Family'), findsOneWidget);
    });

    testWidgets('respects maxFamilyMembers limit', (WidgetTester tester) async {
      // Create a list that reaches the max limit
      final maxMembers = List.generate(2, (index) => 
        FamilyMember(
          id: '$index',
          fullName: 'Member $index',
          email: 'member$<EMAIL>',
          role: 'caregiver',
          joinedAt: DateTime.now(),
          status: FamilyMemberStatus.active,
          permissions: {},
        ),
      );

      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: maxMembers,
            canManageFamily: true,
            maxFamilyMembers: 2,
            onInviteMember: () {},
          ),
        ),
      );

      // Should not show invite button when at max capacity
      expect(find.text('Invite Member'), findsNothing);
    });

    testWidgets('shows remaining members indicator when more than 5 members', (WidgetTester tester) async {
      // Create 7 members to test the "more" indicator
      final manyMembers = List.generate(7, (index) => 
        FamilyMember(
          id: '$index',
          fullName: 'Member $index',
          email: 'member$<EMAIL>',
          role: 'caregiver',
          joinedAt: DateTime.now(),
          status: FamilyMemberStatus.active,
          permissions: {},
        ),
      );

      await tester.pumpWidget(
        createTestWidget(
          FamilySharingCard(
            familyMembers: manyMembers,
          ),
        ),
      );

      // Should show "+2" indicator for the 2 members beyond the first 5
      expect(find.text('+2'), findsOneWidget);
      expect(find.text('more'), findsOneWidget);
    });

    group('Accessibility', () {
      testWidgets('has proper semantic labels', (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: mockFamilyMembers,
              canManageFamily: true,
            ),
          ),
        );

        // Verify semantic structure
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('supports keyboard navigation', (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: mockFamilyMembers,
              canManageFamily: true,
              onInviteMember: () {},
            ),
          ),
        );

        // Verify focusable elements exist
        expect(find.byType(GestureDetector), findsWidgets);
        expect(find.byType(TextButton), findsWidgets);
      });
    });

    group('Member Status Handling', () {
      testWidgets('displays different member statuses correctly', (WidgetTester tester) async {
        final mixedStatusMembers = [
          FamilyMember(
            id: '1',
            fullName: 'Active Member',
            email: '<EMAIL>',
            role: 'parent',
            joinedAt: DateTime.now().subtract(const Duration(days: 30)),
            status: FamilyMemberStatus.active,
            permissions: {'view_activities': true},
            lastActiveAt: DateTime.now().subtract(const Duration(hours: 1)),
          ),
          FamilyMember(
            id: '2',
            fullName: 'Pending Member',
            email: '<EMAIL>',
            role: 'caregiver',
            joinedAt: DateTime.now().subtract(const Duration(days: 1)),
            status: FamilyMemberStatus.pending,
            permissions: {'view_activities': true},
          ),
          FamilyMember(
            id: '3',
            fullName: 'Inactive Member',
            email: '<EMAIL>',
            role: 'caregiver',
            joinedAt: DateTime.now().subtract(const Duration(days: 60)),
            status: FamilyMemberStatus.inactive,
            permissions: {'view_activities': false},
            lastActiveAt: DateTime.now().subtract(const Duration(days: 30)),
          ),
        ];

        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: mixedStatusMembers,
            ),
          ),
        );

        // Verify stats show correct counts
        expect(find.text('3'), findsOneWidget); // Total
        expect(find.text('1'), findsAtLeastNWidgets(2)); // Active and pending counts
      });

      testWidgets('handles invitation callbacks correctly', (WidgetTester tester) async {
        String? invitedEmail;
        String? invitedRole;
        Map<String, bool>? invitedPermissions;
        String? invitationMessage;

        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: mockFamilyMembers,
              canManageFamily: true,
              onSendInvitation: (email, role, permissions, message) {
                invitedEmail = email;
                invitedRole = role;
                invitedPermissions = permissions;
                invitationMessage = message;
              },
            ),
          ),
        );

        // This would require opening the invitation dialog
        // For now, verify the invite button exists
        expect(find.text('Invite Member'), findsOneWidget);
      });

      testWidgets('displays member roles correctly', (WidgetTester tester) async {
        final membersWithDifferentRoles = [
          FamilyMember(
            id: '1',
            fullName: 'Parent User',
            email: '<EMAIL>',
            role: 'parent',
            joinedAt: DateTime.now(),
            status: FamilyMemberStatus.active,
            permissions: {},
          ),
          FamilyMember(
            id: '2',
            fullName: 'Admin User',
            email: '<EMAIL>',
            role: 'admin',
            joinedAt: DateTime.now(),
            status: FamilyMemberStatus.active,
            permissions: {},
          ),
          FamilyMember(
            id: '3',
            fullName: 'Caregiver User',
            email: '<EMAIL>',
            role: 'caregiver',
            joinedAt: DateTime.now(),
            status: FamilyMemberStatus.active,
            permissions: {},
          ),
        ];

        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: membersWithDifferentRoles,
            ),
          ),
        );

        // Verify different roles are handled
        expect(find.text('Family Sharing'), findsOneWidget);
        expect(find.text('3'), findsOneWidget); // Total count
      });
    });

    group('Error Recovery', () {
      testWidgets('provides retry functionality on error', (WidgetTester tester) async {
        bool retryTapped = false;
        
        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: [],
              errorMessage: 'Network error',
              onRetry: () => retryTapped = true,
            ),
          ),
        );

        await tester.tap(find.text('Try Again'));
        expect(retryTapped, isTrue);
      });

      testWidgets('handles partial data gracefully', (WidgetTester tester) async {
        final memberWithMissingData = [
          FamilyMember(
            id: '1',
            fullName: 'Incomplete Member',
            email: '<EMAIL>',
            role: 'caregiver',
            joinedAt: DateTime.now(),
            status: FamilyMemberStatus.active,
            permissions: {},
            // Missing lastActiveAt and other optional fields
          ),
        ];

        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: memberWithMissingData,
            ),
          ),
        );

        // Should still render without errors
        expect(find.text('Family Sharing'), findsOneWidget);
        expect(find.text('1'), findsOneWidget);
      });
    });

    group('Performance', () {
      testWidgets('handles large member lists efficiently', (WidgetTester tester) async {
        // Create a large list of members
        final largeMemberList = List.generate(100, (index) => 
          FamilyMember(
            id: '$index',
            fullName: 'Member $index',
            email: 'member$<EMAIL>',
            role: 'caregiver',
            joinedAt: DateTime.now(),
            status: FamilyMemberStatus.active,
            permissions: {},
          ),
        );

        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: largeMemberList,
            ),
          ),
        );

        // Should still render correctly with large lists
        expect(find.text('Family Sharing'), findsOneWidget);
        expect(find.text('100'), findsOneWidget); // Total count
        
        // Should only show first 5 members in overview
        expect(find.text('+95'), findsOneWidget);
      });

      testWidgets('efficiently updates when member list changes', (WidgetTester tester) async {
        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: mockFamilyMembers,
            ),
          ),
        );

        expect(find.text('2'), findsOneWidget); // Initial count

        // Update with new member list
        final updatedMembers = [...mockFamilyMembers, 
          FamilyMember(
            id: '3',
            fullName: 'New Member',
            email: '<EMAIL>',
            role: 'caregiver',
            joinedAt: DateTime.now(),
            status: FamilyMemberStatus.active,
            permissions: {},
          ),
        ];

        await tester.pumpWidget(
          createTestWidget(
            FamilySharingCard(
              familyMembers: updatedMembers,
            ),
          ),
        );

        expect(find.text('3'), findsOneWidget); // Updated count
      });
    });
  });
}