import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../lib/presentation/growth_charts/widgets/chart_interaction_handler.dart';
import '../../lib/presentation/growth_charts/widgets/chart_gesture_handler.dart';
import '../../lib/presentation/growth_charts/widgets/measurement_tooltip_system.dart';
import '../../lib/presentation/growth_charts/widgets/percentile_curve_hover_system.dart';
import '../../lib/presentation/growth_charts/widgets/chart_data_processor.dart';
import '../../lib/models/measurement.dart';

void main() {
  group('Chart Interaction Handler Tests', () {
    late ChartInteractionHandler interactionHandler;
    late ChartDataProcessor dataProcessor;
    late List<Measurement> testMeasurements;

    setUp(() {
      // Create test measurements
      testMeasurements = [
        Measurement(
          id: '1',
          babyId: 'baby1',
          measurementType: 'weight',
          value: 3.5,
          unit: 'kg',
          measuredAt: DateTime.now().subtract(const Duration(days: 30)),
          ageInMonths: 1.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
        Measurement(
          id: '2',
          babyId: 'baby1',
          measurementType: 'weight',
          value: 4.2,
          unit: 'kg',
          measuredAt: DateTime.now().subtract(const Duration(days: 15)),
          ageInMonths: 1.5,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      ];

      dataProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 45)),
      );

      interactionHandler = ChartInteractionHandler(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 45)),
        measurements: testMeasurements,
        dataProcessor: dataProcessor,
      );
    });

    test('should initialize with default values', () {
      expect(interactionHandler.zoomLevel, equals(1.0));
      expect(interactionHandler.panOffset, equals(Offset.zero));
      expect(interactionHandler.selectedMeasurement, isNull);
    });

    test('should handle chart touch events', () {
      bool tapCallbackCalled = false;
      
      final handler = ChartInteractionHandler(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 45)),
        measurements: testMeasurements,
        dataProcessor: dataProcessor,
        onMeasurementTap: (measurement) {
          tapCallbackCalled = true;
        },
      );

      // Simulate tap event
      final touchEvent = FlTapUpEvent(const Offset(100, 100));
      final touchResponse = LineTouchResponse([
        LineBarSpot(
          LineChartBarData(spots: [const FlSpot(1, 3.5)]),
          7, // Measurement line index
          const FlSpot(1, 3.5),
          0,
        ),
      ]);

      handler.handleChartTouch(touchEvent, touchResponse);
      expect(tapCallbackCalled, isTrue);
    });

    test('should build measurement tooltip correctly', () {
      // This would require a widget test environment
      // For now, we'll test the logic components
      expect(interactionHandler.selectedMeasurement, isNull);
      expect(interactionHandler.tooltipPosition, isNull);
    });
  });

  group('Chart Gesture Handler Tests', () {
    late ChartGestureHandler gestureHandler;

    setUp(() {
      gestureHandler = ChartGestureHandler();
    });

    test('should initialize with default values', () {
      expect(gestureHandler.zoomLevel, equals(1.0));
      expect(gestureHandler.panOffset, equals(Offset.zero));
      expect(gestureHandler.isInteracting, isFalse);
    });

    test('should handle scale gestures', () {
      bool interactionStarted = false;
      bool interactionEnded = false;
      
      final handler = ChartGestureHandler(
        onInteractionStart: () => interactionStarted = true,
        onInteractionEnd: () => interactionEnded = true,
      );

      // Simulate scale start
      handler.handleScaleStart(const ScaleStartDetails());
      expect(handler.isInteracting, isTrue);
      expect(interactionStarted, isTrue);

      // Simulate scale end
      handler.handleScaleEnd(const ScaleEndDetails());
      expect(handler.isInteracting, isFalse);
      expect(interactionEnded, isTrue);
    });

    test('should constrain zoom levels', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      // Test zoom constraints
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 10.0, // Very high scale
        focalPointDelta: Offset.zero,
      ));
      
      expect(gestureHandler.zoomLevel, lessThanOrEqualTo(3.0));
      
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 0.1, // Very low scale
        focalPointDelta: Offset.zero,
      ));
      
      expect(gestureHandler.zoomLevel, greaterThanOrEqualTo(0.5));
    });

    test('should provide zoom percentage correctly', () {
      expect(gestureHandler.zoomPercentage, equals(20.0)); // (1.0 - 0.5) / (3.0 - 0.5) * 100
    });
  });

  group('Chart Data Processor Tests', () {
    late ChartDataProcessor dataProcessor;
    late List<Map<String, dynamic>> testMeasurementData;

    setUp(() {
      dataProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 60)),
      );

      testMeasurementData = [
        {
          'value': 3.5,
          'date': DateTime.now().subtract(const Duration(days: 30)),
        },
        {
          'value': 4.2,
          'date': DateTime.now().subtract(const Duration(days: 15)),
        },
      ];
    });

    test('should convert measurements to spots correctly', () {
      final spots = dataProcessor.convertMeasurementsToSpots(testMeasurementData);
      
      expect(spots.length, equals(2));
      expect(spots.first.y, equals(3.5));
      expect(spots.last.y, equals(4.2));
      
      // Check that spots are sorted by age
      expect(spots.first.x, lessThan(spots.last.x));
    });

    test('should calculate age in months correctly', () {
      final testDate = DateTime.now().subtract(const Duration(days: 30));
      final ageInMonths = dataProcessor.calculateAgeInMonths(testDate);
      
      expect(ageInMonths, closeTo(1.0, 0.1)); // Approximately 1 month
    });

    test('should convert values for display correctly', () {
      // Test metric to imperial conversion for weight
      final imperialProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: false,
        birthDate: DateTime.now().subtract(const Duration(days: 60)),
      );
      
      final convertedValue = imperialProcessor.convertValueForDisplay(3.5); // 3.5 kg
      expect(convertedValue, closeTo(7.72, 0.1)); // ~7.72 lbs
    });

    test('should get correct display units', () {
      expect(dataProcessor.getDisplayUnit(), equals('kg'));
      
      final imperialProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: false,
        birthDate: DateTime.now().subtract(const Duration(days: 60)),
      );
      
      expect(imperialProcessor.getDisplayUnit(), equals('lbs'));
    });

    test('should analyze trends correctly', () {
      final spots = [
        const FlSpot(1.0, 3.5),
        const FlSpot(1.5, 4.2),
        const FlSpot(2.0, 4.8),
      ];
      
      final trendAnalysis = dataProcessor.analyzeTrend(spots);
      
      expect(trendAnalysis['direction'], equals('increasing'));
      expect(trendAnalysis['slope'], greaterThan(0));
      expect(trendAnalysis['consistency'], greaterThan(0.5));
    });
  });

  group('Widget Tests', () {
    testWidgets('MeasurementTooltipSystem should build correctly', (WidgetTester tester) async {
      final animationController = AnimationController(
        duration: const Duration(milliseconds: 200),
        vsync: tester,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MeasurementTooltipSystem(
              measurementType: 'weight',
              gender: 'male',
              isMetric: true,
              animationController: animationController,
            ),
          ),
        ),
      );

      // Should build without errors
      expect(find.byType(MeasurementTooltipSystem), findsOneWidget);
      
      animationController.dispose();
    });

    testWidgets('PercentileCurveHoverSystem should build correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: PercentileCurveHoverSystem(
              measurementType: 'weight',
              gender: 'male',
              isMetric: true,
            ),
          ),
        ),
      );

      // Should build without errors
      expect(find.byType(PercentileCurveHoverSystem), findsOneWidget);
    });

    testWidgets('ChartGestureDetector should handle gestures', (WidgetTester tester) async {
      final gestureHandler = ChartGestureHandler();
      bool gestureDetected = false;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ChartGestureDetector(
              gestureHandler: gestureHandler,
              child: Container(
                width: 200,
                height: 200,
                color: Colors.blue,
              ),
            ),
          ),
        ),
      );

      // Test that the gesture detector is present
      expect(find.byType(ChartGestureDetector), findsOneWidget);
      expect(find.byType(GestureDetector), findsOneWidget);

      gestureHandler.dispose();
    });
  });
}