import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/widgets/family_invitation_dialog.dart';
import '../../lib/models/family_member.dart';

void main() {
  group('FamilyInvitationDialog', () {
    late Function(String, String, Map<String, bool>, String?) mockOnInvite;
    
    setUp(() {
      mockOnInvite = (email, role, permissions, message) {};
    });

    Widget createTestWidget({
      bool isLoading = false,
      int currentMemberCount = 0,
      int maxMembers = 10,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: FamilyInvitationDialog(
            onInvite: mockOnInvite,
            isLoading: isLoading,
            currentMemberCount: currentMemberCount,
            maxMembers: maxMembers,
          ),
        ),
      );
    }

    testWidgets('displays dialog header correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Invite Family Member'), findsOneWidget);
      expect(find.text('Add a new member to your family sharing'), findsOneWidget);
      expect(find.byIcon(Icons.close), findsOneWidget);
    });

    testWidgets('shows member limit information', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        currentMemberCount: 3,
        maxMembers: 5,
      ));

      expect(find.text('Family Members: 3/5'), findsOneWidget);
      expect(find.text('2 slots remaining'), findsOneWidget);
    });

    testWidgets('shows warning when near capacity', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        currentMemberCount: 8,
        maxMembers: 10,
      ));

      expect(find.text('Family Members: 8/10'), findsOneWidget);
      expect(find.text('2 slots remaining'), findsOneWidget);
    });

    testWidgets('shows at capacity message', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        currentMemberCount: 10,
        maxMembers: 10,
      ));

      expect(find.text('Family Members: 10/10'), findsOneWidget);
      expect(find.text('Family is at maximum capacity'), findsOneWidget);
    });

    testWidgets('displays email input field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Email Address'), findsOneWidget);
      expect(find.byType(TextFormField), findsAtLeastNWidgets(1));
    });

    testWidgets('displays role selection options', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Role & Permissions'), findsOneWidget);
      expect(find.text('Parent'), findsOneWidget);
      expect(find.text('Caregiver'), findsOneWidget);
      expect(find.text('Grandparent'), findsOneWidget);
      expect(find.text('Babysitter'), findsOneWidget);
    });

    testWidgets('allows role selection', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap on Parent role
      await tester.tap(find.text('Parent'));
      await tester.pumpAndSettle();

      // Should show check mark for selected role
      expect(find.byIcon(Icons.check_circle), findsOneWidget);
    });

    testWidgets('shows advanced options toggle', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Advanced Options'), findsOneWidget);
      
      // Tap to expand advanced options
      await tester.tap(find.text('Advanced Options'));
      await tester.pumpAndSettle();

      expect(find.text('Custom Permissions'), findsOneWidget);
      expect(find.text('Custom Message (Optional)'), findsOneWidget);
    });

    testWidgets('validates email input', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Find email field and enter invalid email
      final emailField = find.byType(TextFormField).first;
      await tester.enterText(emailField, 'invalid-email');
      
      // Try to send invitation
      await tester.tap(find.text('Send Invitation'));
      await tester.pumpAndSettle();

      expect(find.text('Please enter a valid email address'), findsOneWidget);
    });

    testWidgets('disables send button when at capacity', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        currentMemberCount: 10,
        maxMembers: 10,
      ));

      final sendButton = find.text('Send Invitation');
      expect(tester.widget<ElevatedButton>(find.ancestor(
        of: sendButton,
        matching: find.byType(ElevatedButton),
      )).onPressed, isNull);
    });

    testWidgets('shows loading state', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(isLoading: true));

      expect(find.text('Sending...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('calls onInvite with correct parameters', (WidgetTester tester) async {
      String? capturedEmail;
      String? capturedRole;
      Map<String, bool>? capturedPermissions;
      String? capturedMessage;

      mockOnInvite = (email, role, permissions, message) {
        capturedEmail = email;
        capturedRole = role;
        capturedPermissions = permissions;
        capturedMessage = message;
      };

      await tester.pumpWidget(createTestWidget());

      // Enter email
      final emailField = find.byType(TextFormField).first;
      await tester.enterText(emailField, '<EMAIL>');

      // Select role (Parent should be default)
      await tester.tap(find.text('Parent'));
      await tester.pumpAndSettle();

      // Send invitation
      await tester.tap(find.text('Send Invitation'));
      await tester.pumpAndSettle();

      expect(capturedEmail, equals('<EMAIL>'));
      expect(capturedRole, equals('parent'));
      expect(capturedPermissions, isNotNull);
      expect(capturedPermissions!['view_activities'], isTrue);
      expect(capturedPermissions!['manage_family'], isTrue);
    });

    testWidgets('closes dialog when cancel is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      await tester.tap(find.text('Cancel'));
      await tester.pumpAndSettle();

      // Dialog should be closed (no longer visible)
      expect(find.text('Invite Family Member'), findsNothing);
    });

    testWidgets('closes dialog when X button is pressed', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      await tester.tap(find.byIcon(Icons.close));
      await tester.pumpAndSettle();

      // Dialog should be closed (no longer visible)
      expect(find.text('Invite Family Member'), findsNothing);
    });

    group('Permission Management', () {
      testWidgets('shows default permissions for selected role', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Expand advanced options
        await tester.tap(find.text('Advanced Options'));
        await tester.pumpAndSettle();

        // Select caregiver role
        await tester.tap(find.text('Caregiver'));
        await tester.pumpAndSettle();

        // Check that caregiver permissions are shown
        expect(find.text('View Activities'), findsOneWidget);
        expect(find.text('Add Activities'), findsOneWidget);
        expect(find.text('Manage Family'), findsOneWidget);
      });

      testWidgets('allows custom permission modification', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Expand advanced options
        await tester.tap(find.text('Advanced Options'));
        await tester.pumpAndSettle();

        // Find and toggle a permission switch
        final switches = find.byType(Switch);
        expect(switches, findsAtLeastNWidgets(1));

        // Toggle first switch
        await tester.tap(switches.first);
        await tester.pumpAndSettle();

        // Switch should have changed state
        expect(find.byType(Switch), findsAtLeastNWidgets(1));
      });
    });

    group('Custom Message', () {
      testWidgets('shows custom message field in advanced options', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Expand advanced options
        await tester.tap(find.text('Advanced Options'));
        await tester.pumpAndSettle();

        expect(find.text('Custom Message (Optional)'), findsOneWidget);
        expect(find.text('Add a personal message to the invitation...'), findsOneWidget);
      });

      testWidgets('includes custom message in invitation', (WidgetTester tester) async {
        String? capturedMessage;
        mockOnInvite = (email, role, permissions, message) {
          capturedMessage = message;
        };

        await tester.pumpWidget(createTestWidget());

        // Enter email
        final emailField = find.byType(TextFormField).first;
        await tester.enterText(emailField, '<EMAIL>');

        // Expand advanced options
        await tester.tap(find.text('Advanced Options'));
        await tester.pumpAndSettle();

        // Enter custom message
        final messageFields = find.byType(TextFormField);
        await tester.enterText(messageFields.last, 'Welcome to our family!');

        // Send invitation
        await tester.tap(find.text('Send Invitation'));
        await tester.pumpAndSettle();

        expect(capturedMessage, equals('Welcome to our family!'));
      });
    });
  });
}