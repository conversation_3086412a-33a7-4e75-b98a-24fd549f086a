import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/invitation_status_widget.dart';
import '../../lib/models/family_member.dart';
import '../../lib/models/enums.dart';
import '../../lib/theme/app_theme.dart';

void main() {
  group('InvitationStatusWidget', () {
    late List<FamilyMember> mockPendingInvitations;
    late Function(FamilyMember) mockOnResend;
    late Function(FamilyMember) mockOnCancel;

    setUp(() {
      mockOnResend = (member) {};
      mockOnCancel = (member) {};
      
      mockPendingInvitations = [
        FamilyMember(
          id: '1',
          fullName: '<PERSON>',
          email: '<EMAIL>',
          role: 'caregiver',
          joinedAt: DateTime.now().subtract(const Duration(days: 2)),
          status: FamilyMemberStatus.pending,
          permissions: {'view_activities': true},
          invitationSentAt: DateTime.now().subtract(const Duration(days: 2)),
          invitationExpiresAt: DateTime.now().add(const Duration(days: 5)),
        ),
        FamilyMember(
          id: '2',
          fullName: 'Jane Smith',
          email: '<EMAIL>',
          role: 'grandparent',
          joinedAt: DateTime.now().subtract(const Duration(days: 8)),
          status: FamilyMemberStatus.pending,
          permissions: {'view_activities': true},
          invitationSentAt: DateTime.now().subtract(const Duration(days: 8)),
          invitationExpiresAt: DateTime.now().subtract(const Duration(days: 1)), // Expired
        ),
      ];
    });

    Widget createTestWidget({
      List<FamilyMember>? pendingInvitations,
      bool isLoading = false,
      String? processingInvitationId,
    }) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: InvitationStatusWidget(
                pendingInvitations: pendingInvitations ?? mockPendingInvitations,
                onResendInvitation: mockOnResend,
                onCancelInvitation: mockOnCancel,
                isLoading: isLoading,
                processingInvitationId: processingInvitationId,
              ),
            ),
          );
        },
      );
    }

    testWidgets('does not render when no pending invitations', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(pendingInvitations: []));

      expect(find.text('Pending Invitations'), findsNothing);
    });

    testWidgets('displays header with correct invitation count', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Pending Invitations'), findsOneWidget);
      expect(find.text('2 invitations waiting for response'), findsOneWidget);
    });

    testWidgets('displays singular form for single invitation', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        pendingInvitations: [mockPendingInvitations.first],
      ));

      expect(find.text('1 invitation waiting for response'), findsOneWidget);
    });

    testWidgets('displays invitation details correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Check first invitation
      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('Caregiver'), findsOneWidget);

      // Check second invitation
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('Grandparent'), findsOneWidget);
    });

    testWidgets('shows pending status badge for active invitations', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Pending'), findsOneWidget);
    });

    testWidgets('shows expired status badge for expired invitations', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Expired'), findsOneWidget);
    });

    testWidgets('displays invitation dates', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should show sent and expires dates
      expect(find.textContaining('Sent:'), findsAtLeastNWidgets(1));
      expect(find.textContaining('Expires:'), findsOneWidget);
      expect(find.textContaining('Expired:'), findsOneWidget);
    });

    testWidgets('shows action buttons for each invitation', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('Cancel'), findsAtLeastNWidgets(2));
      expect(find.text('Resend'), findsAtLeastNWidgets(2));
    });

    testWidgets('calls onResendInvitation when resend button is tapped', (WidgetTester tester) async {
      FamilyMember? capturedMember;
      mockOnResend = (member) {
        capturedMember = member;
      };

      await tester.pumpWidget(createTestWidget());

      // Tap first resend button
      await tester.tap(find.text('Resend').first);
      await tester.pumpAndSettle();

      expect(capturedMember, isNotNull);
      expect(capturedMember!.id, equals('1'));
      expect(capturedMember!.email, equals('<EMAIL>'));
    });

    testWidgets('calls onCancelInvitation when cancel button is tapped', (WidgetTester tester) async {
      FamilyMember? capturedMember;
      mockOnCancel = (member) {
        capturedMember = member;
      };

      await tester.pumpWidget(createTestWidget());

      // Tap first cancel button
      await tester.tap(find.text('Cancel').first);
      await tester.pumpAndSettle();

      expect(capturedMember, isNotNull);
      expect(capturedMember!.id, equals('1'));
      expect(capturedMember!.email, equals('<EMAIL>'));
    });

    testWidgets('shows loading state for processing invitation', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        processingInvitationId: '1',
      ));

      expect(find.text('Cancelling...'), findsOneWidget);
      expect(find.text('Resending...'), findsOneWidget);
      expect(find.byType(CircularProgressIndicator), findsAtLeastNWidgets(2));
    });

    testWidgets('disables buttons when loading', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(
        isLoading: true,
      ));

      // Find all buttons and check they're disabled
      final cancelButtons = find.text('Cancel');
      final resendButtons = find.text('Resend');

      for (int i = 0; i < cancelButtons.evaluate().length; i++) {
        final button = tester.widget<ElevatedButton>(
          find.ancestor(
            of: cancelButtons.at(i),
            matching: find.byType(ElevatedButton),
          ),
        );
        expect(button.onPressed, isNull);
      }

      for (int i = 0; i < resendButtons.evaluate().length; i++) {
        final button = tester.widget<ElevatedButton>(
          find.ancestor(
            of: resendButtons.at(i),
            matching: find.byType(ElevatedButton),
          ),
        );
        expect(button.onPressed, isNull);
      }
    });

    testWidgets('displays user avatars correctly', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Should show avatars for each pending invitation
      expect(find.byType(CircleAvatar), findsAtLeastNWidgets(2));
    });

    group('Date Formatting', () {
      testWidgets('formats recent dates correctly', (WidgetTester tester) async {
        final recentInvitation = FamilyMember(
          id: 'recent',
          fullName: 'Recent User',
          email: '<EMAIL>',
          role: 'caregiver',
          joinedAt: DateTime.now(),
          status: FamilyMemberStatus.pending,
          permissions: {'view_activities': true},
          invitationSentAt: DateTime.now(),
          invitationExpiresAt: DateTime.now().add(const Duration(days: 7)),
        );

        await tester.pumpWidget(createTestWidget(
          pendingInvitations: [recentInvitation],
        ));

        expect(find.text('Today'), findsAtLeastNWidgets(1));
      });

      testWidgets('formats yesterday dates correctly', (WidgetTester tester) async {
        final yesterdayInvitation = FamilyMember(
          id: 'yesterday',
          fullName: 'Yesterday User',
          email: '<EMAIL>',
          role: 'caregiver',
          joinedAt: DateTime.now().subtract(const Duration(days: 1)),
          status: FamilyMemberStatus.pending,
          permissions: {'view_activities': true},
          invitationSentAt: DateTime.now().subtract(const Duration(days: 1)),
          invitationExpiresAt: DateTime.now().add(const Duration(days: 6)),
        );

        await tester.pumpWidget(createTestWidget(
          pendingInvitations: [yesterdayInvitation],
        ));

        expect(find.text('Yesterday'), findsAtLeastNWidgets(1));
      });
    });

    group('Visual States', () {
      testWidgets('applies error styling to expired invitations', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Find expired invitation container
        final expiredContainer = find.ancestor(
          of: find.text('Jane Smith'),
          matching: find.byType(Container),
        ).first;

        final container = tester.widget<Container>(expiredContainer);
        final decoration = container.decoration as BoxDecoration;
        
        // Should have error-colored border for expired invitation
        expect(decoration.border, isNotNull);
      });

      testWidgets('shows correct icons for status badges', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Should show schedule icon for pending and error icon for expired
        expect(find.byIcon(Icons.schedule), findsOneWidget);
        expect(find.byIcon(Icons.error), findsOneWidget);
      });
    });
  });
}