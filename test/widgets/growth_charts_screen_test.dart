import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../lib/presentation/growth_charts/growth_charts.dart';
import '../../lib/presentation/growth_charts/widgets/growth_chart_renderer.dart';
import '../../lib/presentation/growth_charts/widgets/measurement_selector_widget.dart';
import '../../lib/presentation/growth_charts/widgets/chart_toolbar_widget.dart';
import '../../lib/presentation/growth_charts/widgets/recent_measurements_widget.dart';
import '../../lib/presentation/growth_charts/widgets/growth_analysis_widget.dart';
import '../../lib/models/baby_profile.dart';
import '../../lib/models/measurement.dart';
import '../../lib/services/growth_analyzer.dart';
import '../../lib/theme/app_theme.dart';
import '../../lib/core/app_export.dart';

void main() {
  group('Growth Charts Screen Tests', () {
    late BabyProfile testBaby;
    late List<Map<String, dynamic>> testMeasurements;

    setUp(() {
      testBaby = BabyProfile(
        id: 'test-baby-1',
        name: 'Test Baby',
        birthDate: DateTime.now().subtract(const Duration(days: 365)),
        gender: 'male',
        birthWeight: 3.2,
        birthHeight: 50.0,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        updatedAt: DateTime.now(),
      );

      testMeasurements = [
        {
          'date': DateTime.now().subtract(const Duration(days: 300)),
          'value': 4.5,
          'unit': 'kg',
          'notes': '2 month checkup',
        },
        {
          'date': DateTime.now().subtract(const Duration(days: 240)),
          'value': 5.8,
          'unit': 'kg',
          'notes': '4 month checkup',
        },
        {
          'date': DateTime.now().subtract(const Duration(days: 180)),
          'value': 7.2,
          'unit': 'kg',
          'notes': '6 month checkup',
        },
        {
          'date': DateTime.now().subtract(const Duration(days: 90)),
          'value': 9.1,
          'unit': 'kg',
          'notes': '9 month checkup',
        },
        {
          'date': DateTime.now().subtract(const Duration(days: 30)),
          'value': 10.5,
          'unit': 'kg',
          'notes': '11 month checkup',
        },
      ];
    });

    Widget _buildTestWidget({
      required Widget child,
      ThemeData? theme,
    }) {
      return Sizer(
        builder: (context, orientation, screenType) {
          return MaterialApp(
            theme: theme ?? AppTheme.lightTheme,
            home: Scaffold(
              body: child,
            ),
          );
        },
      );
    }

    group('Growth Charts Main Screen', () {
      testWidgets('should render without errors', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        expect(find.byType(GrowthCharts), findsOneWidget);
        expect(find.text('Growth Charts'), findsOneWidget);
      });

      testWidgets('should display measurement selector', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        expect(find.byType(MeasurementSelectorWidget), findsOneWidget);
        expect(find.text('Weight'), findsOneWidget);
        expect(find.text('Height'), findsOneWidget);
        expect(find.text('Head'), findsOneWidget);
      });

      testWidgets('should display chart toolbar', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        expect(find.byType(ChartToolbarWidget), findsOneWidget);
        expect(find.text('Time Period'), findsOneWidget);
        expect(find.text('Units'), findsOneWidget);
      });

      testWidgets('should handle measurement type changes', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Tap on height tab
        await tester.tap(find.text('Height'));
        await tester.pumpAndSettle();

        // Should still render without errors
        expect(find.byType(GrowthCharts), findsOneWidget);
      });

      testWidgets('should handle unit system changes', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Find and tap the unit toggle
        final unitToggle = find.text('Imperial');
        if (unitToggle.evaluate().isNotEmpty) {
          await tester.tap(unitToggle);
          await tester.pumpAndSettle();
        }

        // Should still render without errors
        expect(find.byType(GrowthCharts), findsOneWidget);
      });

      testWidgets('should display in dark theme', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
          theme: AppTheme.darkTheme,
        ));

        expect(find.byType(GrowthCharts), findsOneWidget);
        expect(find.text('Growth Charts'), findsOneWidget);
      });
    });

    group('Measurement Selector Widget', () {
      testWidgets('should display all measurement types', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: MeasurementSelectorWidget(
            selectedIndex: 0,
            onChanged: (index) {},
          ),
        ));

        expect(find.text('Weight'), findsOneWidget);
        expect(find.text('Height'), findsOneWidget);
        expect(find.text('Head'), findsOneWidget);
      });

      testWidgets('should highlight selected measurement', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: MeasurementSelectorWidget(
            selectedIndex: 1, // Height selected
            onChanged: (index) {},
          ),
        ));

        // Height should be selected
        expect(find.text('Height'), findsOneWidget);
      });

      testWidgets('should handle tab changes', (WidgetTester tester) async {
        int selectedIndex = 0;
        
        await tester.pumpWidget(_buildTestWidget(
          child: MeasurementSelectorWidget(
            selectedIndex: selectedIndex,
            onChanged: (index) {
              selectedIndex = index;
            },
          ),
        ));

        // Tap on height tab
        await tester.tap(find.text('Height'));
        await tester.pumpAndSettle();

        expect(selectedIndex, equals(1));
      });

      testWidgets('should render properly in dark theme', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: MeasurementSelectorWidget(
            selectedIndex: 0,
            onChanged: (index) {},
          ),
          theme: AppTheme.darkTheme,
        ));

        expect(find.text('Weight'), findsOneWidget);
        expect(find.text('Height'), findsOneWidget);
        expect(find.text('Head'), findsOneWidget);
      });

      testWidgets('should not have layout overflow', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: Container(
            width: 300,
            height: 100,
            child: MeasurementSelectorWidget(
              selectedIndex: 0,
              onChanged: (index) {},
            ),
          ),
        ));

        // Should not have any RenderFlex overflow errors
        expect(tester.takeException(), isNull);
      });
    });

    group('Chart Toolbar Widget', () {
      testWidgets('should display time period selector', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: ChartToolbarWidget(
            selectedDateRange: '1 year',
            isMetric: true,
            onDateRangeChanged: (range) {},
            onUnitToggle: (isMetric) {},
          ),
        ));

        expect(find.text('Time Period'), findsOneWidget);
        expect(find.text('1 year'), findsOneWidget);
      });

      testWidgets('should display unit toggle', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: ChartToolbarWidget(
            selectedDateRange: '1 year',
            isMetric: true,
            onDateRangeChanged: (range) {},
            onUnitToggle: (isMetric) {},
          ),
        ));

        expect(find.text('Units'), findsOneWidget);
        expect(find.text('Metric'), findsOneWidget);
      });

      testWidgets('should handle date range changes', (WidgetTester tester) async {
        String selectedRange = '1 year';
        
        await tester.pumpWidget(_buildTestWidget(
          child: ChartToolbarWidget(
            selectedDateRange: selectedRange,
            isMetric: true,
            onDateRangeChanged: (range) {
              selectedRange = range;
            },
            onUnitToggle: (isMetric) {},
          ),
        ));

        // Tap on date range dropdown
        await tester.tap(find.text('1 year'));
        await tester.pumpAndSettle();

        // Should show dropdown options
        expect(find.text('6 months'), findsOneWidget);
        expect(find.text('2 years'), findsOneWidget);
      });

      testWidgets('should handle unit system toggle', (WidgetTester tester) async {
        bool isMetric = true;
        
        await tester.pumpWidget(_buildTestWidget(
          child: ChartToolbarWidget(
            selectedDateRange: '1 year',
            isMetric: isMetric,
            onDateRangeChanged: (range) {},
            onUnitToggle: (metric) {
              isMetric = metric;
            },
          ),
        ));

        // Tap on unit toggle
        await tester.tap(find.text('Metric'));
        await tester.pumpAndSettle();

        expect(isMetric, isFalse);
      });
    });

    group('Growth Chart Renderer', () {
      testWidgets('should render with measurement data', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: testBaby.birthDate,
          ),
        ));

        expect(find.byType(GrowthChartRenderer), findsOneWidget);
        expect(find.text('Weight Growth Chart'), findsOneWidget);
        expect(find.textContaining('5 measurements'), findsOneWidget);
      });

      testWidgets('should display empty state when no measurements', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthChartRenderer(
            measurements: [],
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: testBaby.birthDate,
          ),
        ));

        expect(find.text('No measurements yet'), findsOneWidget);
        expect(find.textContaining('Add your first weight measurement'), findsOneWidget);
      });

      testWidgets('should display legend', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: testBaby.birthDate,
          ),
        ));

        expect(find.text('Legend'), findsOneWidget);
        expect(find.text('Your Baby'), findsOneWidget);
        expect(find.text('97th'), findsOneWidget);
        expect(find.text('50th (Median)'), findsOneWidget);
        expect(find.text('3rd'), findsOneWidget);
      });

      testWidgets('should display chart controls', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: testBaby.birthDate,
          ),
        ));

        expect(find.text('Curves'), findsOneWidget);
        expect(find.text('Points'), findsOneWidget);
      });

      testWidgets('should handle curve toggle', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: testBaby.birthDate,
          ),
        ));

        // Tap curves toggle
        await tester.tap(find.text('Curves'));
        await tester.pumpAndSettle();

        // Should not throw any errors
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle points toggle', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: testBaby.birthDate,
          ),
        ));

        // Tap points toggle
        await tester.tap(find.text('Points'));
        await tester.pumpAndSettle();

        // Should not throw any errors
        expect(tester.takeException(), isNull);
      });

      testWidgets('should support different measurement types', (WidgetTester tester) async {
        final measurementTypes = [
          {'type': 'weight', 'title': 'Weight Growth Chart'},
          {'type': 'height', 'title': 'Height Growth Chart'},
          {'type': 'head_circumference', 'title': 'Head Circumference Growth Chart'},
        ];

        for (final type in measurementTypes) {
          await tester.pumpWidget(_buildTestWidget(
            child: GrowthChartRenderer(
              measurements: testMeasurements,
              measurementType: type['type'] as String,
              gender: 'male',
              isMetric: true,
              dateRange: '1 year',
              birthDate: testBaby.birthDate,
            ),
          ));

          expect(find.text(type['title'] as String), findsOneWidget);
        }
      });
    });

    group('Recent Measurements Widget', () {
      testWidgets('should display recent measurements', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: RecentMeasurementsWidget(
            measurements: testMeasurements,
            measurementType: 0, // weight
            isMetric: true,
            onEdit: (index) {},
            onDelete: (index) {},
          ),
        ));

        expect(find.text('Recent Measurements'), findsOneWidget);
        expect(find.text('10.5 kg'), findsOneWidget); // Most recent measurement
      });

      testWidgets('should handle empty measurements', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: RecentMeasurementsWidget(
            measurements: [],
            measurementType: 0,
            isMetric: true,
            onEdit: (index) {},
            onDelete: (index) {},
          ),
        ));

        expect(find.text('Recent Measurements'), findsOneWidget);
        expect(find.text('No measurements yet'), findsOneWidget);
      });

      testWidgets('should handle edit action', (WidgetTester tester) async {
        int editedIndex = -1;
        
        await tester.pumpWidget(_buildTestWidget(
          child: RecentMeasurementsWidget(
            measurements: testMeasurements,
            measurementType: 0,
            isMetric: true,
            onEdit: (index) {
              editedIndex = index;
            },
            onDelete: (index) {},
          ),
        ));

        // Find edit button and tap it
        final editButton = find.widgetWithIcon(IconButton, Icons.edit);
        if (editButton.evaluate().isNotEmpty) {
          await tester.tap(editButton.first);
          await tester.pumpAndSettle();
          
          expect(editedIndex, greaterThanOrEqualTo(0));
        }
      });

      testWidgets('should handle delete action', (WidgetTester tester) async {
        int deletedIndex = -1;
        
        await tester.pumpWidget(_buildTestWidget(
          child: RecentMeasurementsWidget(
            measurements: testMeasurements,
            measurementType: 0,
            isMetric: true,
            onEdit: (index) {},
            onDelete: (index) {
              deletedIndex = index;
            },
          ),
        ));

        // Find delete button and tap it
        final deleteButton = find.widgetWithIcon(IconButton, Icons.delete);
        if (deleteButton.evaluate().isNotEmpty) {
          await tester.tap(deleteButton.first);
          await tester.pumpAndSettle();
          
          expect(deletedIndex, greaterThanOrEqualTo(0));
        }
      });
    });

    group('Growth Analysis Widget', () {
      testWidgets('should display growth analysis', (WidgetTester tester) async {
        final mockAnalysis = GrowthAnalysis(
          trends: [],
          alerts: [],
          overallAssessment: 'Normal growth pattern',
          percentileCrossingAnalysis: {},
          growthSummary: 'Your baby is growing well within normal ranges',
          recommendations: ['Continue regular checkups'],
          analyzedAt: DateTime.now(),
        );

        await tester.pumpWidget(_buildTestWidget(
          child: GrowthAnalysisWidget(
            analysis: mockAnalysis,
            onAlertTap: (alert) {},
          ),
        ));

        expect(find.text('Growth Analysis'), findsOneWidget);
        expect(find.text('Normal growth pattern'), findsOneWidget);
        expect(find.text('Your baby is growing well within normal ranges'), findsOneWidget);
      });

      testWidgets('should display alerts when present', (WidgetTester tester) async {
        final mockAnalysis = GrowthAnalysis(
          trends: [],
          alerts: [
            GrowthAlert(
              type: GrowthAlertType.belowNormalRange,
              title: 'Below Normal Range',
              description: 'Weight is below the 3rd percentile',
              severity: 'high',
              recommendations: ['Consult pediatrician'],
              detectedAt: DateTime.now(),
            ),
          ],
          overallAssessment: 'Requires attention',
          percentileCrossingAnalysis: {},
          growthSummary: 'Growth concerns detected',
          recommendations: ['Schedule checkup'],
          analyzedAt: DateTime.now(),
        );

        await tester.pumpWidget(_buildTestWidget(
          child: GrowthAnalysisWidget(
            analysis: mockAnalysis,
            onAlertTap: (alert) {},
          ),
        ));

        expect(find.text('Alerts (1)'), findsOneWidget);
        expect(find.text('Below Normal Range'), findsOneWidget);
        expect(find.text('Weight is below the 3rd percentile'), findsOneWidget);
      });

      testWidgets('should handle alert tap', (WidgetTester tester) async {
        GrowthAlert? tappedAlert;
        
        final mockAnalysis = GrowthAnalysis(
          trends: [],
          alerts: [
            GrowthAlert(
              type: GrowthAlertType.belowNormalRange,
              title: 'Below Normal Range',
              description: 'Weight is below the 3rd percentile',
              severity: 'high',
              recommendations: ['Consult pediatrician'],
              detectedAt: DateTime.now(),
            ),
          ],
          overallAssessment: 'Requires attention',
          percentileCrossingAnalysis: {},
          growthSummary: 'Growth concerns detected',
          recommendations: ['Schedule checkup'],
          analyzedAt: DateTime.now(),
        );

        await tester.pumpWidget(_buildTestWidget(
          child: GrowthAnalysisWidget(
            analysis: mockAnalysis,
            onAlertTap: (alert) {
              tappedAlert = alert;
            },
          ),
        ));

        // Tap on alert
        await tester.tap(find.text('Below Normal Range'));
        await tester.pumpAndSettle();

        expect(tappedAlert, isNotNull);
        expect(tappedAlert!.title, equals('Below Normal Range'));
      });
    });

    group('Integration Tests', () {
      testWidgets('should handle full screen interaction flow', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Change measurement type
        await tester.tap(find.text('Height'));
        await tester.pumpAndSettle();

        // Change unit system
        final unitToggle = find.text('Imperial');
        if (unitToggle.evaluate().isNotEmpty) {
          await tester.tap(unitToggle);
          await tester.pumpAndSettle();
        }

        // Change date range
        final dateRangeButton = find.text('1 year');
        if (dateRangeButton.evaluate().isNotEmpty) {
          await tester.tap(dateRangeButton);
          await tester.pumpAndSettle();
          
          final sixMonthsOption = find.text('6 months');
          if (sixMonthsOption.evaluate().isNotEmpty) {
            await tester.tap(sixMonthsOption);
            await tester.pumpAndSettle();
          }
        }

        // Should still render without errors
        expect(find.byType(GrowthCharts), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should maintain state during orientation changes', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Change to height tab
        await tester.tap(find.text('Height'));
        await tester.pumpAndSettle();

        // Simulate orientation change by rebuilding
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Should still render without errors
        expect(find.byType(GrowthCharts), findsOneWidget);
      });

      testWidgets('should handle error states gracefully', (WidgetTester tester) async {
        // Test with null baby profile
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Should not throw errors
        expect(tester.takeException(), isNull);
      });
    });

    group('Accessibility Tests', () {
      testWidgets('should have proper semantic labels', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Check for semantic labels
        expect(find.text('Weight'), findsOneWidget);
        expect(find.text('Height'), findsOneWidget);
        expect(find.text('Head'), findsOneWidget);
      });

      testWidgets('should support screen reader navigation', (WidgetTester tester) async {
        await tester.pumpWidget(_buildTestWidget(
          child: GrowthCharts(babyProfile: testBaby),
        ));

        // Test tab navigation
        await tester.binding.defaultBinaryMessenger.handlePlatformMessage(
          'flutter/semantics',
          null,
          (data) {},
        );

        // Should not throw errors
        expect(tester.takeException(), isNull);
      });
    });
  });
}
