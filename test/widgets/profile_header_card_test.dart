import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/models/user_profile.dart';
import '../../lib/models/profile_completion_status.dart';
import '../../lib/widgets/profile_header_card.dart';
import '../../lib/widgets/user_avatar_widget.dart';
import '../../lib/theme/app_theme.dart';

void main() {
  group('ProfileHeaderCard Widget Tests', () {
    late UserProfile testUserProfile;
    late ProfileCompletionStatus testCompletionStatus;

    setUp(() {
      testUserProfile = UserProfile(
        id: 'test-id',
        email: '<EMAIL>',
        fullName: '<PERSON>',
        avatarUrl: null,
        role: 'parent',
        signInCount: 5,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        lastSignInAt: DateTime.now().subtract(const Duration(hours: 2)),
      );

      testCompletionStatus = const ProfileCompletionStatus(
        percentage: 75.0,
        completedSteps: ['basic_info', 'baby_profile'],
        remainingSteps: ['profile_photo', 'family_setup'],
        nextRecommendedAction: 'Add a profile photo',
        totalSteps: 4,
        completedCount: 2,
      );
    });

    Widget createTestWidget(Widget child) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: child,
            ),
          );
        },
      );
    }

    testWidgets('displays user profile information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            completionStatus: testCompletionStatus,
          ),
        ),
      );

      // Verify user name is displayed
      expect(find.text('John Doe'), findsOneWidget);
      
      // Verify email is displayed
      expect(find.text('<EMAIL>'), findsOneWidget);
      
      // Verify role badge is displayed
      expect(find.text('Parent'), findsOneWidget);
      
      // Verify completion percentage is displayed
      expect(find.text('75%'), findsOneWidget);
      
      // Verify next action is displayed
      expect(find.textContaining('Add a profile photo'), findsOneWidget);
    });

    testWidgets('displays loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const ProfileHeaderCard(
            isLoading: true,
          ),
        ),
      );

      // Verify loading skeleton is displayed
      expect(find.byType(Container), findsWidgets);
      
      // Verify no user data is displayed
      expect(find.text('John Doe'), findsNothing);
    });

    testWidgets('displays error state when user profile is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const ProfileHeaderCard(
            userProfile: null,
          ),
        ),
      );

      // Verify error message is displayed
      expect(find.text('Unable to load profile'), findsOneWidget);
      expect(find.text('Please check your connection and try again'), findsOneWidget);
      
      // Verify error icon is displayed
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('handles edit profile button tap', (WidgetTester tester) async {
      bool editTapped = false;
      
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            onEditProfile: () {
              editTapped = true;
            },
          ),
        ),
      );

      // Find and tap the edit button
      final editButton = find.text('Edit Profile');
      expect(editButton, findsOneWidget);
      
      await tester.tap(editButton);
      await tester.pump();

      // Verify callback was called
      expect(editTapped, isTrue);
    });

    testWidgets('handles avatar tap', (WidgetTester tester) async {
      bool avatarTapped = false;
      
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            onAvatarTap: () {
              avatarTapped = true;
            },
          ),
        ),
      );

      // Find and tap the avatar
      final avatar = find.byType(GestureDetector).first;
      await tester.tap(avatar);
      await tester.pump();

      // Verify callback was called
      expect(avatarTapped, isTrue);
    });

    testWidgets('displays completion progress correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            completionStatus: testCompletionStatus,
            showCompletionProgress: true,
          ),
        ),
      );

      // Verify progress section is displayed
      expect(find.text('Profile Completion'), findsOneWidget);
      expect(find.text('75%'), findsOneWidget);
      expect(find.byType(LinearProgressIndicator), findsOneWidget);
      
      // Verify status text is displayed
      expect(find.text('Halfway There'), findsOneWidget);
    });

    testWidgets('hides completion progress when disabled', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            completionStatus: testCompletionStatus,
            showCompletionProgress: false,
          ),
        ),
      );

      // Verify progress section is not displayed
      expect(find.text('Profile Completion'), findsNothing);
      expect(find.byType(LinearProgressIndicator), findsNothing);
    });

    testWidgets('displays account dates correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            showAccountDates: true,
          ),
        ),
      );

      // Verify account information section is displayed
      expect(find.text('Account Information'), findsOneWidget);
      expect(find.text('Member since'), findsOneWidget);
      expect(find.text('Last active'), findsOneWidget);
      expect(find.text('Total logins'), findsOneWidget);
      expect(find.text('5'), findsOneWidget);
    });

    testWidgets('hides account dates when disabled', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            showAccountDates: false,
          ),
        ),
      );

      // Verify account information section is not displayed
      expect(find.text('Account Information'), findsNothing);
      expect(find.text('Member since'), findsNothing);
    });

    testWidgets('hides edit button when not editable', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            isEditable: false,
          ),
        ),
      );

      // Verify edit button is not displayed
      expect(find.text('Edit Profile'), findsNothing);
    });

    testWidgets('formats role text correctly', (WidgetTester tester) async {
      final userWithUnderscoreRole = testUserProfile.copyWith(role: 'family_member');
      
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: userWithUnderscoreRole,
          ),
        ),
      );

      // Verify role is formatted correctly
      expect(find.text('Family Member'), findsOneWidget);
    });

    testWidgets('generates correct initials', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
          ),
        ),
      );

      // Find the UserAvatarWidget and verify it has correct initials
      final avatarWidget = tester.widget<UserAvatarWidget>(find.byType(UserAvatarWidget));
      expect(avatarWidget.initials, equals('JD')); // John Doe -> JD
    });

    testWidgets('handles different completion percentages correctly', (WidgetTester tester) async {
      final lowCompletionStatus = const ProfileCompletionStatus(
        percentage: 25.0,
        completedSteps: ['basic_info'],
        remainingSteps: ['profile_photo', 'baby_profile', 'family_setup'],
        nextRecommendedAction: 'Add your first baby profile',
        totalSteps: 4,
        completedCount: 1,
      );

      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            completionStatus: lowCompletionStatus,
            showCompletionProgress: true,
          ),
        ),
      );

      expect(find.text('25%'), findsOneWidget);
      expect(find.text('Getting Started'), findsOneWidget);
      expect(find.textContaining('Add your first baby profile'), findsOneWidget);
    });

    testWidgets('displays different role badges correctly', (WidgetTester tester) async {
      final adminUser = testUserProfile.copyWith(role: 'admin');
      
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: adminUser,
          ),
        ),
      );

      expect(find.text('Admin'), findsOneWidget);
    });

    testWidgets('handles missing last sign in gracefully', (WidgetTester tester) async {
      final userWithoutLastSignIn = testUserProfile.copyWith(lastSignInAt: null);
      
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: userWithoutLastSignIn,
            showAccountDates: true,
          ),
        ),
      );

      expect(find.text('Never'), findsOneWidget);
    });

    testWidgets('shows correct accessibility semantics', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: testUserProfile,
            completionStatus: testCompletionStatus,
          ),
        ),
      );

      // Verify semantic structure exists
      expect(find.byType(Semantics), findsWidgets);
      
      // Check for proper heading structure
      final profileCard = find.byType(ProfileHeaderCard);
      expect(profileCard, findsOneWidget);
    });
  });

  group('ProfileHeaderCard Variants', () {
    late UserProfile testUserProfile;

    setUp(() {
      testUserProfile = UserProfile(
        id: 'test-id',
        email: '<EMAIL>',
        fullName: 'Jane Smith',
        avatarUrl: null,
        role: 'admin',
        signInCount: 10,
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
      );
    });

    Widget createTestWidget(Widget child) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: child,
            ),
          );
        },
      );
    }

    testWidgets('compact variant hides progress and dates', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCardVariants.compact(
            userProfile: testUserProfile,
          ),
        ),
      );

      // Verify user info is displayed
      expect(find.text('Jane Smith'), findsOneWidget);
      
      // Verify progress and dates are hidden
      expect(find.text('Profile Completion'), findsNothing);
      expect(find.text('Account Information'), findsNothing);
    });

    testWidgets('full variant shows all information', (WidgetTester tester) async {
      final completionStatus = const ProfileCompletionStatus(
        percentage: 100.0,
        completedSteps: ['basic_info', 'profile_photo', 'baby_profile', 'family_setup'],
        remainingSteps: [],
        nextRecommendedAction: 'Profile complete!',
        totalSteps: 4,
        completedCount: 4,
      );

      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCardVariants.full(
            userProfile: testUserProfile,
            completionStatus: completionStatus,
          ),
        ),
      );

      // Verify all sections are displayed
      expect(find.text('Jane Smith'), findsOneWidget);
      expect(find.text('Profile Completion'), findsOneWidget);
      expect(find.text('Account Information'), findsOneWidget);
      expect(find.text('100%'), findsOneWidget);
    });

    testWidgets('readOnly variant hides edit functionality', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCardVariants.readOnly(
            userProfile: testUserProfile,
          ),
        ),
      );

      // Verify user info is displayed
      expect(find.text('Jane Smith'), findsOneWidget);
      
      // Verify edit button is hidden
      expect(find.text('Edit Profile'), findsNothing);
      
      // Verify completion progress is hidden but account dates are shown
      expect(find.text('Profile Completion'), findsNothing);
      expect(find.text('Account Information'), findsOneWidget);
    });
  });
}