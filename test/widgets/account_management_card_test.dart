import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/account_management_card.dart';
import '../../lib/models/subscription_info.dart';
import '../../lib/models/enums.dart';
import '../../lib/theme/app_theme.dart';

void main() {
  group('AccountManagementCard Widget Tests', () {
    late SubscriptionInfo mockFreeSubscription;
    late SubscriptionInfo mockPremiumSubscription;
    late SubscriptionInfo mockTrialSubscription;
    late SubscriptionInfo mockExpiredSubscription;
    late PaymentMethod mockPaymentMethod;

    setUpAll(() {
      mockPaymentMethod = const PaymentMethod(
        type: 'card',
        last4: '4242',
        brand: 'visa',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault: true,
      );

      mockFreeSubscription = SubscriptionInfo(
        planId: 'free',
        planName: 'Free',
        status: SubscriptionStatus.free,
        monthlyPrice: 0.0,
        features: [
          'Basic activity tracking',
          'Up to 1 baby profile',
          '1 family member',
          'Basic charts and insights',
        ],
        isTrialActive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        maxFamilyMembers: 1,
        includesAiInsights: false,
        includesDataExport: false,
        includesPremiumSupport: false,
        storageLimit: 1,
      );

      mockPremiumSubscription = SubscriptionInfo(
        planId: 'premium',
        planName: 'Premium',
        status: SubscriptionStatus.active,
        renewalDate: DateTime.now().add(const Duration(days: 15)),
        monthlyPrice: 9.99,
        features: [
          'Unlimited activity tracking',
          'Unlimited baby profiles',
          'Up to 6 family members',
          'AI-powered insights',
          'Advanced charts and analytics',
          'Data export',
          'Premium support',
          'Unlimited storage',
        ],
        isTrialActive: false,
        paymentMethod: mockPaymentMethod,
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
        maxFamilyMembers: 6,
        includesAiInsights: true,
        includesDataExport: true,
        includesPremiumSupport: true,
        storageLimit: null,
      );

      mockTrialSubscription = SubscriptionInfo(
        planId: 'premium',
        planName: 'Premium Trial',
        status: SubscriptionStatus.trial,
        monthlyPrice: 9.99,
        features: [
          'Unlimited activity tracking',
          'Unlimited baby profiles',
          'Up to 6 family members',
          'AI-powered insights',
        ],
        isTrialActive: true,
        trialEndsAt: DateTime.now().add(const Duration(days: 2)),
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        updatedAt: DateTime.now(),
        maxFamilyMembers: 6,
        includesAiInsights: true,
        includesDataExport: true,
        includesPremiumSupport: true,
        storageLimit: null,
      );

      mockExpiredSubscription = SubscriptionInfo(
        planId: 'premium',
        planName: 'Premium',
        status: SubscriptionStatus.expired,
        monthlyPrice: 9.99,
        features: [
          'Unlimited activity tracking',
          'Unlimited baby profiles',
          'Up to 6 family members',
          'AI-powered insights',
        ],
        isTrialActive: false,
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
        maxFamilyMembers: 6,
        includesAiInsights: true,
        includesDataExport: true,
        includesPremiumSupport: true,
        storageLimit: null,
      );
    });

    Widget createTestWidget(Widget child) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: SingleChildScrollView(
                child: child,
              ),
            ),
          );
        },
      );
    }

    testWidgets('displays loading state correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const AccountManagementCard(
            isLoading: true,
          ),
        ),
      );

      expect(find.text('Account Management'), findsOneWidget);
      expect(find.byType(Container), findsWidgets); // Loading skeletons
    });

    testWidgets('displays free subscription correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockFreeSubscription,
            isEmailVerified: true,
            isTwoFactorEnabled: false,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('Account Management'), findsOneWidget);
      expect(find.text('Free plan'), findsOneWidget);
      expect(find.text('Basic activity tracking'), findsOneWidget);
      expect(find.text('Upgrade Available'), findsOneWidget);
      expect(find.text('Upgrade to unlock premium features'), findsOneWidget);
    });

    testWidgets('displays premium subscription correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockPremiumSubscription,
            hasAdminPrivileges: true,
            isEmailVerified: true,
            isTwoFactorEnabled: true,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('Account Management'), findsOneWidget);
      expect(find.text('Premium'), findsOneWidget);
      expect(find.text('\$9.99/month'), findsOneWidget);
      expect(find.text('Unlimited activity tracking'), findsOneWidget);
      expect(find.text('VISA •••• 4242'), findsOneWidget);
      expect(find.text('ACTIVE'), findsOneWidget);
      
      // Should not show upgrade section for active premium
      expect(find.text('Upgrade Available'), findsNothing);
    });

    testWidgets('displays trial subscription with upgrade prompt', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockTrialSubscription,
            isEmailVerified: true,
            isTwoFactorEnabled: false,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('Premium Trial'), findsOneWidget);
      expect(find.text('TRIAL'), findsOneWidget);
      expect(find.textContaining('Trial ends'), findsOneWidget);
      expect(find.text('Upgrade Available'), findsOneWidget);
      expect(find.text('Trial ending soon - upgrade to continue'), findsOneWidget);
      expect(find.text('Upgrade Now'), findsOneWidget);
      expect(find.text('Continue Free'), findsOneWidget);
    });

    testWidgets('displays expired subscription correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockExpiredSubscription,
            isEmailVerified: true,
            isTwoFactorEnabled: false,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('Premium'), findsOneWidget);
      expect(find.text('EXPIRED'), findsOneWidget);
      expect(find.text('Subscription expired'), findsOneWidget);
    });

    testWidgets('displays security section correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockPremiumSubscription,
            isEmailVerified: false,
            isTwoFactorEnabled: true,
            activeSessions: 3,
          ),
        ),
      );

      expect(find.text('Security'), findsOneWidget);
      expect(find.text('Email Verification'), findsOneWidget);
      expect(find.text('Not Verified'), findsOneWidget);
      expect(find.text('Two-Factor Authentication'), findsOneWidget);
      expect(find.text('Enabled'), findsOneWidget);
      expect(find.text('Active Sessions'), findsOneWidget);
      expect(find.text('3 sessions'), findsOneWidget);
    });

    testWidgets('displays preferences section correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockPremiumSubscription,
            isEmailVerified: true,
            isTwoFactorEnabled: true,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('Preferences'), findsOneWidget);
      expect(find.text('Notifications'), findsOneWidget);
      expect(find.text('Push, email, and in-app settings'), findsOneWidget);
      expect(find.text('Privacy'), findsOneWidget);
      expect(find.text('Data sharing and visibility'), findsOneWidget);
      expect(find.text('Data Export'), findsOneWidget);
      expect(find.text('Download your data'), findsOneWidget);
    });

    testWidgets('handles null subscription gracefully', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const AccountManagementCard(
            subscription: null,
            isEmailVerified: true,
            isTwoFactorEnabled: false,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('Account Management'), findsOneWidget);
      expect(find.text('Subscription information unavailable'), findsOneWidget);
      expect(find.text('Security'), findsOneWidget);
      expect(find.text('Preferences'), findsOneWidget);
    });

    testWidgets('calls callbacks when tapped', (WidgetTester tester) async {
      bool manageSubscriptionCalled = false;
      bool securitySettingsCalled = false;
      bool accountPreferencesCalled = false;
      bool upgradeCalled = false;

      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: mockFreeSubscription,
            isEmailVerified: true,
            isTwoFactorEnabled: false,
            activeSessions: 1,
            onManageSubscription: () => manageSubscriptionCalled = true,
            onSecuritySettings: () => securitySettingsCalled = true,
            onAccountPreferences: () => accountPreferencesCalled = true,
            onUpgrade: () => upgradeCalled = true,
          ),
        ),
      );

      // Scroll to make the upgrade button visible
      await tester.scrollUntilVisible(
        find.text('Upgrade Now'),
        500.0,
      );
      
      // Test upgrade button
      await tester.tap(find.text('Upgrade Now'), warnIfMissed: false);
      await tester.pump();
      expect(upgradeCalled, isTrue);

      // Test section headers (they should be tappable if callbacks are provided)
      await tester.scrollUntilVisible(
        find.text('Subscription').first,
        -500.0,
      );
      await tester.tap(find.text('Subscription').first);
      await tester.pump();
      expect(manageSubscriptionCalled, isTrue);
    });

    testWidgets('displays correct number of features', (WidgetTester tester) async {
      // Create subscription with many features
      final manyFeaturesSubscription = mockPremiumSubscription.copyWith(
        features: [
          'Feature 1',
          'Feature 2',
          'Feature 3',
          'Feature 4',
          'Feature 5',
          'Feature 6',
          'Feature 7',
        ],
      );

      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: manyFeaturesSubscription,
            isEmailVerified: true,
            isTwoFactorEnabled: true,
            activeSessions: 1,
          ),
        ),
      );

      // Should show first 4 features
      expect(find.text('Feature 1'), findsOneWidget);
      expect(find.text('Feature 2'), findsOneWidget);
      expect(find.text('Feature 3'), findsOneWidget);
      expect(find.text('Feature 4'), findsOneWidget);
      
      // Should show "+3 more features" text
      expect(find.text('+3 more features'), findsOneWidget);
      
      // Should not show features 5-7 directly
      expect(find.text('Feature 5'), findsNothing);
      expect(find.text('Feature 6'), findsNothing);
      expect(find.text('Feature 7'), findsNothing);
    });

    testWidgets('displays expired payment method correctly', (WidgetTester tester) async {
      final expiredPaymentMethod = PaymentMethod(
        type: 'card',
        last4: '1234',
        brand: 'mastercard',
        expiryMonth: 1,
        expiryYear: 2020, // Expired
        isDefault: true,
      );

      final subscriptionWithExpiredCard = mockPremiumSubscription.copyWith(
        paymentMethod: expiredPaymentMethod,
      );

      await tester.pumpWidget(
        createTestWidget(
          AccountManagementCard(
            subscription: subscriptionWithExpiredCard,
            isEmailVerified: true,
            isTwoFactorEnabled: true,
            activeSessions: 1,
          ),
        ),
      );

      expect(find.text('MASTERCARD •••• 1234'), findsOneWidget);
      expect(find.text('Expired'), findsOneWidget);
    });

    testWidgets('adapts to dark theme correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.darkTheme,
              home: Scaffold(
                body: SingleChildScrollView(
                  child: AccountManagementCard(
                    subscription: mockPremiumSubscription,
                    isEmailVerified: true,
                    isTwoFactorEnabled: true,
                    activeSessions: 1,
                  ),
                ),
              ),
            );
          },
        ),
      );

      // Verify the widget renders without errors in dark theme
      expect(find.text('Account Management'), findsOneWidget);
      expect(find.text('Premium'), findsOneWidget);
      expect(find.text('Security'), findsOneWidget);
      expect(find.text('Preferences'), findsOneWidget);
    });
  });
}