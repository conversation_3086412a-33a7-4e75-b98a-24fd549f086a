import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../lib/presentation/growth_charts/widgets/chart_gesture_handler.dart';
import '../../lib/presentation/growth_charts/widgets/chart_interaction_handler.dart';
import '../../lib/presentation/growth_charts/widgets/chart_data_processor.dart';
import '../../lib/presentation/growth_charts/widgets/measurement_tooltip_system.dart';
import '../../lib/presentation/growth_charts/widgets/percentile_curve_hover_system.dart';
import '../../lib/models/measurement.dart';

void main() {
  group('Chart Gesture Handler Tests', () {
    late ChartGestureHandler gestureHandler;

    setUp(() {
      gestureHandler = ChartGestureHandler();
    });

    test('should initialize with default values', () {
      expect(gestureHandler.zoomLevel, equals(1.0));
      expect(gestureHandler.panOffset, equals(Offset.zero));
      expect(gestureHandler.isInteracting, isFalse);
      expect(gestureHandler.minZoom, equals(0.5));
      expect(gestureHandler.maxZoom, equals(3.0));
    });

    test('should handle scale start correctly', () {
      bool interactionStarted = false;
      
      final handler = ChartGestureHandler(
        onInteractionStart: () => interactionStarted = true,
      );

      handler.handleScaleStart(const ScaleStartDetails(
        focalPoint: Offset(100, 100),
      ));

      expect(handler.isInteracting, isTrue);
      expect(interactionStarted, isTrue);
    });

    test('should handle scale update with zoom', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 2.0,
        focalPoint: const Offset(100, 100),
        focalPointDelta: Offset.zero,
      ));

      expect(gestureHandler.zoomLevel, equals(2.0));
    });

    test('should constrain zoom to min/max limits', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      // Test max zoom constraint
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 10.0, // Way above max
        focalPoint: const Offset(100, 100),
        focalPointDelta: Offset.zero,
      ));
      
      expect(gestureHandler.zoomLevel, equals(3.0)); // Should be clamped to max
      
      // Test min zoom constraint
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 0.1, // Way below min
        focalPoint: const Offset(100, 100),
        focalPointDelta: Offset.zero,
      ));
      
      expect(gestureHandler.zoomLevel, equals(0.5)); // Should be clamped to min
    });

    test('should handle pan gestures correctly', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 1.0,
        focalPoint: const Offset(100, 100),
        focalPointDelta: const Offset(50, 25),
      ));

      expect(gestureHandler.panOffset.dx, equals(50));
      expect(gestureHandler.panOffset.dy, equals(25));
    });

    test('should handle scale end correctly', () {
      bool interactionEnded = false;
      
      final handler = ChartGestureHandler(
        onInteractionEnd: () => interactionEnded = true,
      );

      handler.handleScaleStart(const ScaleStartDetails());
      handler.handleScaleEnd(const ScaleEndDetails());

      expect(handler.isInteracting, isFalse);
      expect(interactionEnded, isTrue);
    });

    test('should calculate zoom percentage correctly', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      // At zoom level 1.0: (1.0 - 0.5) / (3.0 - 0.5) = 0.5 / 2.5 = 0.2 = 20%
      expect(gestureHandler.zoomPercentage, equals(20.0));
      
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 2.0,
        focalPoint: const Offset(100, 100),
        focalPointDelta: Offset.zero,
      ));
      
      // At zoom level 2.0: (2.0 - 0.5) / (3.0 - 0.5) = 1.5 / 2.5 = 0.6 = 60%
      expect(gestureHandler.zoomPercentage, equals(60.0));
    });

    test('should reset zoom and pan', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 2.0,
        focalPoint: const Offset(100, 100),
        focalPointDelta: const Offset(50, 25),
      ));
      
      // Verify values changed
      expect(gestureHandler.zoomLevel, equals(2.0));
      expect(gestureHandler.panOffset, equals(const Offset(50, 25)));
      
      gestureHandler.resetZoomAndPan();
      
      // Verify values reset
      expect(gestureHandler.zoomLevel, equals(1.0));
      expect(gestureHandler.panOffset, equals(Offset.zero));
    });

    test('should handle rapid gesture changes', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      // Rapid sequence of updates
      for (int i = 0; i < 100; i++) {
        gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
          scale: 1.0 + (i * 0.01),
          focalPoint: Offset(100 + i.toDouble(), 100 + i.toDouble()),
          focalPointDelta: Offset(i.toDouble(), i.toDouble()),
        ));
      }
      
      // Should handle without errors and maintain constraints
      expect(gestureHandler.zoomLevel, lessThanOrEqualTo(3.0));
      expect(gestureHandler.zoomLevel, greaterThanOrEqualTo(0.5));
    });

    test('should provide transform matrix', () {
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 2.0,
        focalPoint: const Offset(100, 100),
        focalPointDelta: const Offset(50, 25),
      ));
      
      final transform = gestureHandler.getTransform();
      expect(transform, isNotNull);
      expect(transform.getMaxScaleOnAxis(), equals(2.0));
    });
  });

  group('Chart Data Processor Tests', () {
    late ChartDataProcessor dataProcessor;
    late List<Map<String, dynamic>> testMeasurements;

    setUp(() {
      dataProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 365)),
      );

      testMeasurements = [
        {
          'value': 3.5,
          'date': DateTime.now().subtract(const Duration(days: 300)),
          'notes': 'Birth weight',
        },
        {
          'value': 4.2,
          'date': DateTime.now().subtract(const Duration(days: 270)),
          'notes': '1 month',
        },
        {
          'value': 5.1,
          'date': DateTime.now().subtract(const Duration(days: 240)),
          'notes': '2 months',
        },
      ];
    });

    test('should convert measurements to spots correctly', () {
      final spots = dataProcessor.convertMeasurementsToSpots(testMeasurements);
      
      expect(spots.length, equals(3));
      expect(spots.first.y, equals(3.5));
      expect(spots.last.y, equals(5.1));
      
      // Verify spots are sorted by age (x-axis)
      expect(spots.first.x, lessThan(spots.last.x));
    });

    test('should calculate age in months correctly', () {
      final testDate = DateTime.now().subtract(const Duration(days: 90));
      final ageInMonths = dataProcessor.calculateAgeInMonths(testDate);
      
      expect(ageInMonths, closeTo(3.0, 0.5));
    });

    test('should handle edge cases in measurements', () {
      // Empty measurements
      final emptySpots = dataProcessor.convertMeasurementsToSpots([]);
      expect(emptySpots, isEmpty);
      
      // Single measurement
      final singleSpots = dataProcessor.convertMeasurementsToSpots([
        {
          'value': 3.5,
          'date': DateTime.now().subtract(const Duration(days: 30)),
          'notes': 'Single measurement',
        }
      ]);
      expect(singleSpots.length, equals(1));
      expect(singleSpots.first.y, equals(3.5));
    });

    test('should handle invalid measurement data', () {
      final invalidMeasurements = [
        {
          'value': null,
          'date': DateTime.now(),
          'notes': 'Invalid value',
        },
        {
          'value': 'invalid',
          'date': DateTime.now(),
          'notes': 'String value',
        },
      ];
      
      expect(() => dataProcessor.convertMeasurementsToSpots(invalidMeasurements),
          throwsException);
    });

    test('should support different measurement types', () {
      final processors = [
        ChartDataProcessor(
          measurementType: 'weight',
          gender: 'male',
          isMetric: true,
          birthDate: DateTime.now().subtract(const Duration(days: 365)),
        ),
        ChartDataProcessor(
          measurementType: 'height',
          gender: 'female',
          isMetric: false,
          birthDate: DateTime.now().subtract(const Duration(days: 365)),
        ),
        ChartDataProcessor(
          measurementType: 'head_circumference',
          gender: 'male',
          isMetric: true,
          birthDate: DateTime.now().subtract(const Duration(days: 365)),
        ),
      ];

      for (final processor in processors) {
        final spots = processor.convertMeasurementsToSpots(testMeasurements);
        expect(spots.length, equals(3));
      }
    });

    test('should handle metric and imperial units', () {
      final metricProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 365)),
      );

      final imperialProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: false,
        birthDate: DateTime.now().subtract(const Duration(days: 365)),
      );

      final metricSpots = metricProcessor.convertMeasurementsToSpots(testMeasurements);
      final imperialSpots = imperialProcessor.convertMeasurementsToSpots(testMeasurements);

      expect(metricSpots.length, equals(imperialSpots.length));
      expect(metricSpots.first.x, equals(imperialSpots.first.x)); // Age should be same
    });
  });

  group('Measurement Tooltip System Tests', () {
    late MeasurementTooltipSystem tooltipSystem;

    setUp(() {
      tooltipSystem = MeasurementTooltipSystem();
    });

    test('should create tooltip content correctly', () {
      final measurement = {
        'value': 7.5,
        'date': DateTime.now().subtract(const Duration(days: 30)),
        'notes': 'Monthly checkup',
      };

      final content = tooltipSystem.createTooltipContent(
        measurement,
        'weight',
        true, // isMetric
        1.0, // ageInMonths
        50.0, // percentile
      );

      expect(content, contains('7.5 kg'));
      expect(content, contains('Monthly checkup'));
      expect(content, contains('50th percentile'));
    });

    test('should handle missing data gracefully', () {
      final measurement = {
        'value': 7.5,
        'date': DateTime.now().subtract(const Duration(days: 30)),
        // No notes
      };

      final content = tooltipSystem.createTooltipContent(
        measurement,
        'weight',
        true,
        1.0,
        null, // No percentile
      );

      expect(content, contains('7.5 kg'));
      expect(content, isNot(contains('notes')));
    });

    test('should format different measurement types correctly', () {
      final measurement = {
        'value': 65.5,
        'date': DateTime.now(),
        'notes': 'Test measurement',
      };

      final heightContent = tooltipSystem.createTooltipContent(
        measurement,
        'height',
        true,
        6.0,
        75.0,
      );

      expect(heightContent, contains('65.5 cm'));
      expect(heightContent, contains('Height'));
    });

    test('should handle imperial units', () {
      final measurement = {
        'value': 16.5,
        'date': DateTime.now(),
        'notes': 'Weight check',
      };

      final content = tooltipSystem.createTooltipContent(
        measurement,
        'weight',
        false, // Imperial
        3.0,
        40.0,
      );

      expect(content, contains('16.5 lbs'));
    });

    test('should show tooltip at correct position', () {
      final position = tooltipSystem.calculateTooltipPosition(
        const Offset(100, 100),
        const Size(200, 50),
        const Size(400, 600),
      );

      expect(position.dx, greaterThanOrEqualTo(0));
      expect(position.dy, greaterThanOrEqualTo(0));
      expect(position.dx + 200, lessThanOrEqualTo(400));
      expect(position.dy + 50, lessThanOrEqualTo(600));
    });
  });

  group('Percentile Curve Hover System Tests', () {
    late PercentileCurveHoverSystem hoverSystem;

    setUp(() {
      hoverSystem = PercentileCurveHoverSystem();
    });

    test('should detect curve hover correctly', () {
      final curves = [
        WHOPercentileCurve(
          percentile: '50th',
          points: [
            const FlSpot(0, 3.5),
            const FlSpot(6, 7.5),
            const FlSpot(12, 10.5),
          ],
          curveColor: Colors.green,
          strokeWidth: 3.0,
          label: '50th percentile',
        ),
      ];

      final hoveredCurve = hoverSystem.detectCurveHover(
        const Offset(100, 150),
        curves,
        const Size(400, 300),
      );

      expect(hoveredCurve, isNotNull);
      expect(hoveredCurve!.percentile, equals('50th'));
    });

    test('should handle no curve hover', () {
      final curves = [
        WHOPercentileCurve(
          percentile: '50th',
          points: [
            const FlSpot(0, 3.5),
            const FlSpot(12, 10.5),
          ],
          curveColor: Colors.green,
          strokeWidth: 3.0,
          label: '50th percentile',
        ),
      ];

      final hoveredCurve = hoverSystem.detectCurveHover(
        const Offset(10, 10), // Far from curve
        curves,
        const Size(400, 300),
      );

      expect(hoveredCurve, isNull);
    });

    test('should create curve hover content', () {
      final curve = WHOPercentileCurve(
        percentile: '75th',
        points: [const FlSpot(0, 3.5)],
        curveColor: Colors.blue,
        strokeWidth: 2.0,
        label: '75th percentile',
      );

      final content = hoverSystem.createCurveHoverContent(curve, 8.5, 'weight');

      expect(content, contains('75th percentile'));
      expect(content, contains('8.5'));
      expect(content, contains('weight'));
    });

    test('should handle edge cases in curve detection', () {
      // Empty curves
      final noCurves = hoverSystem.detectCurveHover(
        const Offset(100, 100),
        [],
        const Size(400, 300),
      );
      expect(noCurves, isNull);

      // Single point curve
      final singlePointCurve = WHOPercentileCurve(
        percentile: '50th',
        points: [const FlSpot(0, 3.5)],
        curveColor: Colors.green,
        strokeWidth: 3.0,
        label: '50th percentile',
      );

      final result = hoverSystem.detectCurveHover(
        const Offset(100, 100),
        [singlePointCurve],
        const Size(400, 300),
      );

      // Should handle gracefully
      expect(result, anyOf(isNull, isNotNull));
    });
  });

  group('Integration Tests', () {
    test('should handle complete interaction flow', () {
      final gestureHandler = ChartGestureHandler();
      final dataProcessor = ChartDataProcessor(
        measurementType: 'weight',
        gender: 'male',
        isMetric: true,
        birthDate: DateTime.now().subtract(const Duration(days: 365)),
      );

      final measurements = [
        {
          'value': 7.5,
          'date': DateTime.now().subtract(const Duration(days: 30)),
          'notes': 'Monthly checkup',
        },
      ];

      // Convert measurements
      final spots = dataProcessor.convertMeasurementsToSpots(measurements);
      expect(spots.length, equals(1));

      // Handle gesture
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      gestureHandler.handleScaleUpdate(ScaleUpdateDetails(
        scale: 1.5,
        focalPoint: const Offset(100, 100),
        focalPointDelta: const Offset(25, 25),
      ));

      expect(gestureHandler.zoomLevel, equals(1.5));
      expect(gestureHandler.panOffset, equals(const Offset(25, 25)));

      gestureHandler.handleScaleEnd(const ScaleEndDetails());
      expect(gestureHandler.isInteracting, isFalse);
    });

    test('should handle error recovery', () {
      final gestureHandler = ChartGestureHandler();
      
      // Start interaction
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      expect(gestureHandler.isInteracting, isTrue);
      
      // Simulate error by calling start again without end
      gestureHandler.handleScaleStart(const ScaleStartDetails());
      expect(gestureHandler.isInteracting, isTrue);
      
      // Should still be recoverable
      gestureHandler.handleScaleEnd(const ScaleEndDetails());
      expect(gestureHandler.isInteracting, isFalse);
    });
  });
}

// Mock classes for testing
class WHOPercentileCurve {
  final String percentile;
  final List<FlSpot> points;
  final Color curveColor;
  final double strokeWidth;
  final String label;
  final bool isDashed;

  WHOPercentileCurve({
    required this.percentile,
    required this.points,
    required this.curveColor,
    required this.strokeWidth,
    required this.label,
    this.isDashed = false,
  });
}
