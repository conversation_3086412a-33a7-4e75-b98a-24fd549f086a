import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/widgets/user_avatar_widget.dart';

void main() {
  group('UserAvatarWidget', () {
    testWidgets('displays initials when no image URL provided', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserAvatarWidget(
              initials: 'JD',
              role: 'parent',
            ),
          ),
        ),
      );

      expect(find.text('JD'), findsOneWidget);
    });

    testWidgets('shows loading indicator when isLoading is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserAvatarWidget(
              initials: 'J<PERSON>',
              role: 'parent',
              isLoading: true,
            ),
          ),
        ),
      );

      expect(find.byType(CircularProgressIndicator), findsOneWidget);
    });

    testWidgets('shows edit overlay when isEditable is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserAvatarWidget(
              initials: 'J<PERSON>',
              role: 'parent',
              isEditable: true,
            ),
          ),
        ),
      );

      // Look for the camera icon in the edit overlay
      expect(find.byType(Stack), findsOneWidget);
    });

    testWidgets('calls onTap when avatar is tapped', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserAvatarWidget(
              initials: 'JD',
              role: 'parent',
              onTap: () => tapped = true,
            ),
          ),
        ),
      );

      await tester.tap(find.byType(UserAvatarWidget));
      expect(tapped, isTrue);
    });

    testWidgets('has correct accessibility semantics', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: UserAvatarWidget(
              initials: 'JD',
              role: 'parent',
              isEditable: true,
            ),
          ),
        ),
      );

      final semantics = tester.getSemantics(find.byType(UserAvatarWidget));
      expect(semantics.label, contains('User avatar for parent'));
      expect(semantics.hint, contains('Tap to change profile picture'));
    });

    group('UserAvatarSizes', () {
      test('provides correct predefined sizes', () {
        expect(UserAvatarSizes.extraLarge, 120.0);
        expect(UserAvatarSizes.large, 80.0);
        expect(UserAvatarSizes.medium, 60.0);
        expect(UserAvatarSizes.small, 40.0);
        expect(UserAvatarSizes.extraSmall, 32.0);
      });
    });

    group('UserAvatarVariants', () {
      testWidgets('large variant creates correct size avatar', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: UserAvatarVariants.large(
                initials: 'LG',
                role: 'parent',
              ),
            ),
          ),
        );

        final avatar = tester.widget<UserAvatarWidget>(find.byType(UserAvatarWidget));
        expect(avatar.size, UserAvatarSizes.large);
      });

      testWidgets('medium variant creates correct size avatar', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: UserAvatarVariants.medium(
                initials: 'MD',
                role: 'parent',
              ),
            ),
          ),
        );

        final avatar = tester.widget<UserAvatarWidget>(find.byType(UserAvatarWidget));
        expect(avatar.size, UserAvatarSizes.medium);
      });

      testWidgets('small variant creates correct size avatar', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: UserAvatarVariants.small(
                initials: 'SM',
                role: 'parent',
              ),
            ),
          ),
        );

        final avatar = tester.widget<UserAvatarWidget>(find.byType(UserAvatarWidget));
        expect(avatar.size, UserAvatarSizes.small);
      });
    });
  });
}