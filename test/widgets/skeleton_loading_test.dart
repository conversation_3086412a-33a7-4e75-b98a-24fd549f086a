import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/shared/skeleton_loading.dart';

void main() {
  group('SkeletonLoading Widgets', () {
    Widget createTestWidget(Widget child) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            home: Scaffold(
              body: child,
            ),
          );
        },
      );
    }

    group('SkeletonLoading', () {
      testWidgets('should show child when not loading', (tester) async {
        const testChild = Text('Test Content');
        
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonLoading(
              isLoading: false,
              child: testChild,
            ),
          ),
        );

        expect(find.text('Test Content'), findsOneWidget);
      });

      testWidgets('should show skeleton when loading', (tester) async {
        const testChild = Text('Test Content');
        
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonLoading(
              isLoading: true,
              child: testChild,
            ),
          ),
        );

        // Should still find the child but with shimmer effect
        expect(find.text('Test Content'), findsOneWidget);
        expect(find.byType(AnimatedBuilder), findsWidgets);
        expect(find.byType(ShaderMask), findsOneWidget);
      });

      testWidgets('should start animation when loading', (tester) async {
        const testChild = Text('Test Content');
        
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonLoading(
              isLoading: true,
              child: testChild,
            ),
          ),
        );

        // Pump a few frames to ensure animation starts
        await tester.pump(const Duration(milliseconds: 100));
        await tester.pump(const Duration(milliseconds: 100));

        expect(find.byType(ShaderMask), findsOneWidget);
      });
    });

    group('ProfileHeaderSkeleton', () {
      testWidgets('should render profile header skeleton', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const ProfileHeaderSkeleton(),
          ),
        );

        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(Container), findsWidgets);
      });

      testWidgets('should show completion progress when enabled', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const ProfileHeaderSkeleton(
              showCompletionProgress: true,
            ),
          ),
        );

        // Should have more containers for completion progress
        final containers = find.byType(Container);
        expect(containers, findsWidgets);
      });

      testWidgets('should hide completion progress when disabled', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const ProfileHeaderSkeleton(
              showCompletionProgress: false,
            ),
          ),
        );

        // Should have fewer containers without completion progress
        final containers = find.byType(Container);
        expect(containers, findsWidgets);
      });

      testWidgets('should use custom avatar size', (tester) async {
        const customSize = 100.0;
        
        await tester.pumpWidget(
          createTestWidget(
            const ProfileHeaderSkeleton(
              avatarSize: customSize,
            ),
          ),
        );

        // Find the avatar container and verify size
        final containers = find.byType(Container);
        expect(containers, findsWidgets);
      });
    });

    group('FamilySharingSkeleton', () {
      testWidgets('should render family sharing skeleton', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const FamilySharingSkeleton(),
          ),
        );

        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(Container), findsWidgets);
        expect(find.byType(Row), findsWidgets);
        expect(find.byType(Column), findsWidgets);
      });

      testWidgets('should have proper structure', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const FamilySharingSkeleton(),
          ),
        );

        // Should have header, stats, member avatars, and action button sections
        final containers = find.byType(Container);
        expect(containers.evaluate().length, greaterThan(5));
      });
    });

    group('AccountManagementSkeleton', () {
      testWidgets('should render account management skeleton', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            SingleChildScrollView(
              child: const AccountManagementSkeleton(),
            ),
          ),
        );

        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(Container), findsWidgets);
        expect(find.byType(Column), findsWidgets);
      });

      testWidgets('should have subscription and settings sections', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            SingleChildScrollView(
              child: const AccountManagementSkeleton(),
            ),
          ),
        );

        // Should have multiple sections with containers
        final containers = find.byType(Container);
        expect(containers.evaluate().length, greaterThan(10));
      });
    });

    group('SkeletonBox', () {
      testWidgets('should render with custom dimensions', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonBox(
              width: 100,
              height: 50,
            ),
          ),
        );

        final container = tester.widget<Container>(find.byType(Container));
        expect(container.constraints?.maxWidth, 100);
        expect(container.constraints?.maxHeight, 50);
      });

      testWidgets('should use custom border radius', (tester) async {
        const customRadius = BorderRadius.all(Radius.circular(8));
        
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonBox(
              width: 100,
              height: 50,
              borderRadius: customRadius,
            ),
          ),
        );

        final container = tester.widget<Container>(find.byType(Container));
        final decoration = container.decoration as BoxDecoration;
        expect(decoration.borderRadius, customRadius);
      });
    });

    group('SkeletonListItem', () {
      testWidgets('should render with avatar by default', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonListItem(),
          ),
        );

        expect(find.byType(Container), findsWidgets);
        expect(find.byType(Row), findsOneWidget);
        expect(find.byType(Column), findsWidgets);
      });

      testWidgets('should hide avatar when disabled', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonListItem(
              showAvatar: false,
            ),
          ),
        );

        // Should have fewer containers without avatar
        final containers = find.byType(Container);
        expect(containers, findsWidgets);
      });

      testWidgets('should hide trailing when disabled', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const SkeletonListItem(
              showTrailing: false,
            ),
          ),
        );

        // Should have fewer containers without trailing
        final containers = find.byType(Container);
        expect(containers, findsWidgets);
      });
    });
  });
}