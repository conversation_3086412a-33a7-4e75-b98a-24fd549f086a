import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../../lib/widgets/profile_header_card/profile_header_card.dart';
import '../../../lib/models/user_profile.dart';
import '../../../lib/models/profile_completion_status.dart';

void main() {
  group('ProfileHeaderCard', () {
    late UserProfile mockUserProfile;
    late ProfileCompletionStatus mockCompletionStatus;

    setUp(() {
      mockUserProfile = UserProfile(
        id: '1',
        email: '<EMAIL>',
        fullName: '<PERSON>',
        role: 'parent',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        signInCount: 5,
        lastSignInAt: DateTime.now().subtract(const Duration(hours: 2)),
      );

      mockCompletionStatus = ProfileCompletionStatus(
        percentage: 75.0,
        completedSteps: ['basic_info', 'profile_photo'],
        remainingSteps: ['phone_number'],
        nextRecommendedAction: 'Add phone number',
        totalSteps: 3,
        completedCount: 2,
      );
    });

    Widget createTestWidget(Widget child) {
      return Sizer(
        builder: (context, orientation, deviceType) {
          return MaterialApp(
            home: Scaffold(body: child),
          );
        },
      );
    }

    testWidgets('displays user profile information correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: mockUserProfile,
            completionStatus: mockCompletionStatus,
          ),
        ),
      );

      expect(find.text('John Doe'), findsOneWidget);
      expect(find.text('<EMAIL>'), findsOneWidget);
      expect(find.text('Parent'), findsOneWidget);
    });

    testWidgets('shows loading state when isLoading is true', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const ProfileHeaderCard(isLoading: true),
        ),
      );

      expect(find.byType(ProfileSkeletonLoader), findsOneWidget);
    });

    testWidgets('shows error state when userProfile is null', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          const ProfileHeaderCard(userProfile: null),
        ),
      );

      expect(find.text('Unable to load profile'), findsOneWidget);
      expect(find.byIcon(Icons.error_outline), findsOneWidget);
    });

    testWidgets('hides completion progress when showCompletionProgress is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: mockUserProfile,
            completionStatus: mockCompletionStatus,
            showCompletionProgress: false,
          ),
        ),
      );

      expect(find.text('Profile Completion'), findsNothing);
      expect(find.text('75%'), findsNothing);
    });

    testWidgets('calls onEditProfile when edit button is tapped', (WidgetTester tester) async {
      bool editPressed = false;

      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: mockUserProfile,
            onEditProfile: () => editPressed = true,
          ),
        ),
      );

      await tester.tap(find.text('Edit Profile'));
      expect(editPressed, isTrue);
    });

    testWidgets('hides edit button when isEditable is false', (WidgetTester tester) async {
      await tester.pumpWidget(
        createTestWidget(
          ProfileHeaderCard(
            userProfile: mockUserProfile,
            isEditable: false,
          ),
        ),
      );

      expect(find.text('Edit Profile'), findsNothing);
    });
  });
}