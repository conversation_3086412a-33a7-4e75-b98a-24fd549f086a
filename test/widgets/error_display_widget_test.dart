import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/shared/error_display_widget.dart';
import '../../lib/services/error_handling_service.dart';

void main() {
  group('ErrorDisplayWidget', () {
    late Widget Function(Widget) createTestWidget;
    
    setUpAll(() {
      createTestWidget = (Widget child) {
        return Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              home: Scaffold(
                body: child,
              ),
            );
          },
        );
      };
    });

    // Helper functions for creating common error types
    AccountProfileError createNetworkError({
      String title = 'Network Error',
      String message = 'Unable to connect to the server',
      bool isRetryable = true,
      String? operation,
    }) {
      return AccountProfileError(
        type: AccountProfileErrorType.networkError,
        title: title,
        message: message,
        isRetryable: isRetryable,
        operation: operation,
      );
    }

    AccountProfileError createValidationError({
      String title = 'Validation Error',
      String message = 'Invalid data format',
      bool isRetryable = false,
      String? operation,
    }) {
      return AccountProfileError(
        type: AccountProfileErrorType.validationError,
        title: title,
        message: message,
        isRetryable: isRetryable,
        operation: operation,
      );
    }

    group('Full Error Display', () {
      testWidgets('should display error information correctly', (tester) async {
        final error = createNetworkError(
          operation: 'loadProfile',
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              onRetry: () {},
            ),
          ),
        );

        expect(find.text('Network Error'), findsOneWidget);
        expect(find.text('Unable to connect to the server'), findsOneWidget);
        expect(find.text('Operation: loadProfile'), findsOneWidget);
        expect(find.text('Try Again'), findsOneWidget);
      });

      testWidgets('should show retry button for retryable errors', (tester) async {
        final error = createNetworkError(
          message: 'Connection failed',
        );

        bool retryPressed = false;

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              onRetry: () => retryPressed = true,
            ),
          ),
        );

        expect(find.text('Try Again'), findsOneWidget);
        
        await tester.tap(find.text('Try Again'));
        await tester.pump();

        expect(retryPressed, true);
      });

      testWidgets('should show dismiss button when enabled', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.networkError,
          title: 'Network Error',
          message: 'Connection failed',
          isRetryable: true,
        );

        bool dismissPressed = false;

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              onDismiss: () => dismissPressed = true,
              showDismiss: true,
            ),
          ),
        );

        expect(find.text('Dismiss'), findsOneWidget);
        
        await tester.tap(find.text('Dismiss'));
        await tester.pump();

        expect(dismissPressed, true);
      });

      testWidgets('should show support button for non-retryable errors', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.validationError,
          title: 'Validation Error',
          message: 'Invalid data format',
          isRetryable: false,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
            ),
          ),
        );

        expect(find.text('Get Help'), findsOneWidget);
      });

      testWidgets('should use warning colors for warning errors', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.networkError, // This is a warning type
          title: 'Network Error',
          message: 'Connection failed',
          isRetryable: true,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
            ),
          ),
        );

        // Should render without throwing and use appropriate colors
        expect(find.byType(Card), findsOneWidget);
        expect(find.byType(Container), findsWidgets);
      });
    });

    group('Compact Error Display', () {
      testWidgets('should display compact error correctly', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.timeoutError,
          title: 'Timeout Error',
          message: 'Request took too long',
          isRetryable: true,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              isCompact: true,
              onRetry: () {},
            ),
          ),
        );

        expect(find.text('Timeout Error'), findsOneWidget);
        expect(find.text('Request took too long'), findsOneWidget);
        expect(find.byType(IconButton), findsWidgets); // Retry and dismiss buttons
      });

      testWidgets('should show retry icon button in compact mode', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.serverError,
          title: 'Server Error',
          message: 'Server unavailable',
          isRetryable: true,
        );

        bool retryPressed = false;

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              isCompact: true,
              onRetry: () => retryPressed = true,
            ),
          ),
        );

        final retryButton = find.byIcon(Icons.refresh);
        expect(retryButton, findsOneWidget);
        
        await tester.tap(retryButton);
        await tester.pump();

        expect(retryPressed, true);
      });

      testWidgets('should truncate long messages in compact mode', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.unknown,
          title: 'Error',
          message: 'This is a very long error message that should be truncated in compact mode to prevent overflow',
          isRetryable: false,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              isCompact: true,
            ),
          ),
        );

        // Should render without overflow
        expect(find.text('Error'), findsOneWidget);
        expect(find.byType(Text), findsWidgets);
      });
    });

    group('NetworkStatusBanner', () {
      testWidgets('should not show when online', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const NetworkStatusBanner(
              isOnline: true,
            ),
          ),
        );

        expect(find.byType(Container), findsNothing);
        expect(find.text('No Internet Connection'), findsNothing);
      });

      testWidgets('should show when offline', (tester) async {
        await tester.pumpWidget(
          createTestWidget(
            const NetworkStatusBanner(
              isOnline: false,
            ),
          ),
        );

        expect(find.text('No Internet Connection'), findsOneWidget);
        expect(find.text('Some features may not work properly'), findsOneWidget);
      });

      testWidgets('should show retry button when callback provided', (tester) async {
        bool retryPressed = false;

        await tester.pumpWidget(
          createTestWidget(
            NetworkStatusBanner(
              isOnline: false,
              onRetry: () => retryPressed = true,
            ),
          ),
        );

        expect(find.text('Retry'), findsOneWidget);
        
        await tester.tap(find.text('Retry'));
        await tester.pump();

        expect(retryPressed, true);
      });
    });

    group('ErrorBoundary', () {
      testWidgets('should show child when no error', (tester) async {
        const testChild = Text('Test Content');

        await tester.pumpWidget(
          createTestWidget(
            const ErrorBoundary(
              child: testChild,
            ),
          ),
        );

        expect(find.text('Test Content'), findsOneWidget);
      });

      testWidgets('should use custom error builder when provided', (tester) async {
        const testChild = Text('Test Content');

        await tester.pumpWidget(
          createTestWidget(
            ErrorBoundary(
              errorBuilder: (error, stackTrace) => const Text('Custom Error'),
              child: testChild,
            ),
          ),
        );

        // Initially should show child
        expect(find.text('Test Content'), findsOneWidget);
      });
    });

    group('Error Types and Icons', () {
      testWidgets('should display correct icons for different error types', (tester) async {
        final errorTypes = [
          AccountProfileErrorType.networkError,
          AccountProfileErrorType.authError,
          AccountProfileErrorType.validationError,
          AccountProfileErrorType.serverError,
          AccountProfileErrorType.timeoutError,
        ];

        for (final errorType in errorTypes) {
          final error = AccountProfileError(
            type: errorType,
            title: 'Test Error',
            message: 'Test message',
            isRetryable: true,
          );

          await tester.pumpWidget(
            createTestWidget(
              ErrorDisplayWidget(
                error: error,
                key: ValueKey(errorType.name),
              ),
            ),
          );

          // Should render appropriate icon for each error type
          expect(find.byType(Container), findsWidgets);
          
          await tester.pumpWidget(Container()); // Clear widget
        }
      });
    });

    group('Support Dialog', () {
      testWidgets('should show support dialog when get help is tapped', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.dataError,
          title: 'Data Error',
          message: 'Corrupted data',
          isRetryable: false,
          operation: 'loadData',
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
            ),
          ),
        );

        await tester.tap(find.text('Get Help'));
        await tester.pumpAndSettle();

        expect(find.text('Need Help?'), findsOneWidget);
        expect(find.text('Error Details:'), findsOneWidget);
        expect(find.text('Type: dataError'), findsOneWidget);
        expect(find.textContaining('Operation: loadData'), findsWidgets);
      });

      testWidgets('should close support dialog when close is tapped', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.dataError,
          title: 'Data Error',
          message: 'Corrupted data',
          isRetryable: false,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
            ),
          ),
        );

        await tester.tap(find.text('Get Help'));
        await tester.pumpAndSettle();

        expect(find.text('Need Help?'), findsOneWidget);

        await tester.tap(find.text('Close'));
        await tester.pumpAndSettle();

        expect(find.text('Need Help?'), findsNothing);
      });
    });

    group('Edge Cases and Error Handling', () {
      testWidgets('should handle null error gracefully', (tester) async {
        // This test ensures the widget doesn't crash with null error
        await tester.pumpWidget(
          createTestWidget(
            const ErrorDisplayWidget(
              error: null,
            ),
          ),
        );

        // Should show a generic error message
        expect(find.text('An unexpected error occurred'), findsOneWidget);
      });

      testWidgets('should handle empty error messages', (tester) async {
        final error = AccountProfileError(
          type: AccountProfileErrorType.unknown,
          title: '',
          message: '',
          isRetryable: false,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
            ),
          ),
        );

        // Should show fallback messages
        expect(find.text('Unknown Error'), findsOneWidget);
        expect(find.text('An error occurred'), findsOneWidget);
      });

      testWidgets('should handle very long error messages', (tester) async {
        final longMessage = 'A' * 1000; // Very long message
        final error = AccountProfileError(
          type: AccountProfileErrorType.serverError,
          title: 'Server Error',
          message: longMessage,
          isRetryable: true,
        );

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
            ),
          ),
        );

        // Should render without overflow
        expect(find.byType(ErrorDisplayWidget), findsOneWidget);
      });

      testWidgets('should handle rapid retry button taps', (tester) async {
        int retryCount = 0;
        final error = createNetworkError();

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              onRetry: () => retryCount++,
            ),
          ),
        );

        // Tap retry button multiple times rapidly
        for (int i = 0; i < 5; i++) {
          await tester.tap(find.text('Try Again'));
          await tester.pump(const Duration(milliseconds: 10));
        }

        // Should handle multiple taps gracefully
        expect(retryCount, greaterThan(0));
      });
    });

    group('Accessibility', () {
      testWidgets('should have proper semantic labels', (tester) async {
        final error = createNetworkError();

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              onRetry: () {},
            ),
          ),
        );

        // Check for semantic elements
        expect(find.byType(Semantics), findsWidgets);
      });

      testWidgets('should support keyboard navigation', (tester) async {
        final error = createNetworkError();

        await tester.pumpWidget(
          createTestWidget(
            ErrorDisplayWidget(
              error: error,
              onRetry: () {},
            ),
          ),
        );

        // Verify focusable elements exist
        expect(find.byType(TextButton), findsWidgets);
      });
    });
  });
}