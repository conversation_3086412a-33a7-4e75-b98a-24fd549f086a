import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

import '../../lib/widgets/account_profile_theme_helper.dart';
import '../../lib/widgets/shared/animated_profile_widgets.dart';
import '../../lib/theme/app_theme.dart';

void main() {
  group('AccountProfileThemeHelper', () {
    testWidgets('should provide consistent card decoration', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: Builder(
                  builder: (context) {
                    final decoration = AccountProfileThemeHelper.getCardDecoration(context);
                    
                    expect(decoration.color, isNotNull);
                    expect(decoration.borderRadius, isNotNull);
                    expect(decoration.boxShadow, isNotNull);
                    expect(decoration.border, isNotNull);
                    
                    return Container(decoration: decoration);
                  },
                ),
              ),
            );
          },
        ),
      );
    });

    testWidgets('should provide role-based avatar theming', (WidgetTester tester) async {
      await tester.pumpWidget(
        <PERSON>zer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: Builder(
                  builder: (context) {
                    final adminConfig = AccountProfileThemeHelper.getAvatarThemeConfig(
                      context,
                      'admin',
                    );
                    final parentConfig = AccountProfileThemeHelper.getAvatarThemeConfig(
                      context,
                      'parent',
                    );
                    
                    // Admin should have thicker border
                    expect(adminConfig.borderWidth, greaterThan(parentConfig.borderWidth));
                    
                    // Both should have valid colors
                    expect(adminConfig.borderColor, isNotNull);
                    expect(parentConfig.borderColor, isNotNull);
                    expect(adminConfig.backgroundColor, isNotNull);
                    expect(parentConfig.backgroundColor, isNotNull);
                    
                    return const SizedBox();
                  },
                ),
              ),
            );
          },
        ),
      );
    });

    testWidgets('should provide status badge theming', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: Builder(
                  builder: (context) {
                    final activeConfig = AccountProfileThemeHelper.getStatusBadgeConfig(
                      context,
                      'active',
                    );
                    final pendingConfig = AccountProfileThemeHelper.getStatusBadgeConfig(
                      context,
                      'pending',
                    );
                    
                    // Different statuses should have different colors
                    expect(activeConfig.backgroundColor, isNot(equals(pendingConfig.backgroundColor)));
                    
                    // All configs should have valid properties
                    expect(activeConfig.textColor, isNotNull);
                    expect(activeConfig.borderColor, isNotNull);
                    expect(activeConfig.fontSize, greaterThan(0));
                    expect(activeConfig.padding, isNotNull);
                    
                    return const SizedBox();
                  },
                ),
              ),
            );
          },
        ),
      );
    });

    testWidgets('should provide progress theming based on percentage', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: Builder(
                  builder: (context) {
                    final lowProgress = AccountProfileThemeHelper.getProgressConfig(context, 25);
                    final highProgress = AccountProfileThemeHelper.getProgressConfig(context, 95);
                    
                    // Different progress levels should have different colors
                    expect(lowProgress.progressColor, isNot(equals(highProgress.progressColor)));
                    
                    // All configs should have valid properties
                    expect(lowProgress.backgroundColor, isNotNull);
                    expect(lowProgress.height, greaterThan(0));
                    expect(lowProgress.borderRadius, greaterThan(0));
                    expect(lowProgress.animationDuration, isNotNull);
                    
                    return const SizedBox();
                  },
                ),
              ),
            );
          },
        ),
      );
    });

    testWidgets('should provide responsive spacing', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: Builder(
                  builder: (context) {
                    final normalSpacing = AccountProfileThemeHelper.getSectionSpacing(context);
                    final compactSpacing = AccountProfileThemeHelper.getSectionSpacing(
                      context,
                      isCompact: true,
                    );
                    
                    // Compact spacing should be smaller
                    expect(compactSpacing.cardSpacing, lessThan(normalSpacing.cardSpacing));
                    expect(compactSpacing.sectionSpacing, lessThan(normalSpacing.sectionSpacing));
                    expect(compactSpacing.elementSpacing, lessThan(normalSpacing.elementSpacing));
                    
                    // All spacing values should be positive
                    expect(normalSpacing.cardSpacing, greaterThan(0));
                    expect(normalSpacing.sectionSpacing, greaterThan(0));
                    expect(normalSpacing.elementSpacing, greaterThan(0));
                    
                    return const SizedBox();
                  },
                ),
              ),
            );
          },
        ),
      );
    });

    testWidgets('should work in dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.darkTheme,
              home: Scaffold(
                body: Builder(
                  builder: (context) {
                    final decoration = AccountProfileThemeHelper.getCardDecoration(context);
                    final avatarConfig = AccountProfileThemeHelper.getAvatarThemeConfig(
                      context,
                      'parent',
                    );
                    final badgeConfig = AccountProfileThemeHelper.getStatusBadgeConfig(
                      context,
                      'active',
                    );
                    
                    // All should work without errors in dark theme
                    expect(decoration.color, isNotNull);
                    expect(avatarConfig.borderColor, isNotNull);
                    expect(badgeConfig.backgroundColor, isNotNull);
                    
                    return const SizedBox();
                  },
                ),
              ),
            );
          },
        ),
      );
    });
  });

  group('AnimatedProfileWidgets', () {
    testWidgets('should create animated card', (WidgetTester tester) async {
      bool tapped = false;
      
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: AnimatedProfileWidgets.animatedCard(
                  context: context,
                  onTap: () => tapped = true,
                  child: const Text('Test Card'),
                ),
              ),
            );
          },
        ),
      );

      expect(find.text('Test Card'), findsOneWidget);
      
      await tester.tap(find.text('Test Card'));
      expect(tapped, isTrue);
    });

    testWidgets('should create animated button', (WidgetTester tester) async {
      bool pressed = false;
      
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: AnimatedProfileWidgets.animatedButton(
                  context: context,
                  onPressed: () => pressed = true,
                  child: const Text('Test Button'),
                ),
              ),
            );
          },
        ),
      );

      expect(find.text('Test Button'), findsOneWidget);
      
      await tester.tap(find.text('Test Button'));
      expect(pressed, isTrue);
    });

    testWidgets('should create animated progress', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: AnimatedProfileWidgets.animatedProgress(
                  value: 0.5,
                  context: context,
                ),
              ),
            );
          },
        ),
      );

      // Should render without errors
      await tester.pumpAndSettle();
    });

    testWidgets('should create animated badge', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: AnimatedProfileWidgets.animatedBadge(
                  context: context,
                  status: 'active',
                  child: const Text('Active'),
                ),
              ),
            );
          },
        ),
      );

      expect(find.text('Active'), findsOneWidget);
      
      // Should animate in
      await tester.pumpAndSettle();
    });

    testWidgets('should create animated list item', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              home: Scaffold(
                body: AnimatedProfileWidgets.animatedListItem(
                  context: context,
                  index: 0,
                  child: const Text('List Item'),
                ),
              ),
            );
          },
        ),
      );

      expect(find.text('List Item'), findsOneWidget);
      
      // Should animate in
      await tester.pumpAndSettle();
    });
  });
}