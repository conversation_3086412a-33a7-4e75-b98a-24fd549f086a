import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:babytracker_pro/theme/app_theme.dart';
import 'package:babytracker_pro/theme/theme_aware_colors.dart';

void main() {
  group('Accessibility Contrast Ratio Tests', () {
    testWidgets('Light theme meets WCAG contrast requirements', (WidgetTester tester) async {
      await _testThemeContrastRatios(tester, AppTheme.lightTheme, 'Light Theme');
    });

    testWidgets('Dark theme meets WCAG contrast requirements', (WidgetTester tester) async {
      await _testThemeContrastRatios(tester, AppTheme.darkTheme, 'Dark Theme');
    });

    test('Primary colors have sufficient contrast in light theme', () {
      final primaryColor = AppTheme.primaryLight;
      final backgroundColor = AppTheme.backgroundLight;
      final onPrimaryColor = AppTheme.onPrimaryLight;
      
      // Test primary on background
      final primaryOnBackgroundRatio = _calculateContrastRatio(primaryColor, backgroundColor);
      expect(primaryOnBackgroundRatio, greaterThan(3.0), 
        reason: 'Primary color on background should have at least 3:1 contrast ratio');
      
      // Test on-primary on primary
      final onPrimaryRatio = _calculateContrastRatio(onPrimaryColor, primaryColor);
      expect(onPrimaryRatio, greaterThan(4.5), 
        reason: 'On-primary color should have at least 4.5:1 contrast ratio');
    });

    test('Primary colors have sufficient contrast in dark theme', () {
      final primaryColor = AppTheme.primaryDark;
      final backgroundColor = AppTheme.backgroundDark;
      final onPrimaryColor = AppTheme.onPrimaryDark;
      
      // Test primary on background
      final primaryOnBackgroundRatio = _calculateContrastRatio(primaryColor, backgroundColor);
      expect(primaryOnBackgroundRatio, greaterThan(3.0), 
        reason: 'Primary color on background should have at least 3:1 contrast ratio');
      
      // Test on-primary on primary
      final onPrimaryRatio = _calculateContrastRatio(onPrimaryColor, primaryColor);
      expect(onPrimaryRatio, greaterThan(4.5), 
        reason: 'On-primary color should have at least 4.5:1 contrast ratio');
    });

    test('Text colors have sufficient contrast in light theme', () {
      final textPrimary = AppTheme.textPrimaryLight;
      final textSecondary = AppTheme.textSecondaryLight;
      final background = AppTheme.backgroundLight;
      final surface = AppTheme.surfaceLight;
      
      // Primary text on background
      final primaryTextRatio = _calculateContrastRatio(textPrimary, background);
      expect(primaryTextRatio, greaterThan(4.5), 
        reason: 'Primary text should have at least 4.5:1 contrast ratio on background');
      
      // Primary text on surface
      final primaryTextSurfaceRatio = _calculateContrastRatio(textPrimary, surface);
      expect(primaryTextSurfaceRatio, greaterThan(4.5), 
        reason: 'Primary text should have at least 4.5:1 contrast ratio on surface');
      
      // Secondary text on background (can be lower for secondary content)
      final secondaryTextRatio = _calculateContrastRatio(textSecondary, background);
      expect(secondaryTextRatio, greaterThan(3.0), 
        reason: 'Secondary text should have at least 3:1 contrast ratio');
    });

    test('Text colors have sufficient contrast in dark theme', () {
      final textPrimary = AppTheme.textPrimaryDark;
      final textSecondary = AppTheme.textSecondaryDark;
      final background = AppTheme.backgroundDark;
      final surface = AppTheme.surfaceDark;
      
      // Primary text on background
      final primaryTextRatio = _calculateContrastRatio(textPrimary, background);
      expect(primaryTextRatio, greaterThan(4.5), 
        reason: 'Primary text should have at least 4.5:1 contrast ratio on background');
      
      // Primary text on surface
      final primaryTextSurfaceRatio = _calculateContrastRatio(textPrimary, surface);
      expect(primaryTextSurfaceRatio, greaterThan(4.5), 
        reason: 'Primary text should have at least 4.5:1 contrast ratio on surface');
      
      // Secondary text on background
      final secondaryTextRatio = _calculateContrastRatio(textSecondary, background);
      expect(secondaryTextRatio, greaterThan(3.0), 
        reason: 'Secondary text should have at least 3:1 contrast ratio');
    });

    test('Error colors have sufficient contrast in both themes', () {
      // Light theme error colors
      final lightErrorRatio = _calculateContrastRatio(AppTheme.errorLight, AppTheme.backgroundLight);
      expect(lightErrorRatio, greaterThan(3.0), 
        reason: 'Light theme error color should have sufficient contrast');
      
      final lightOnErrorRatio = _calculateContrastRatio(AppTheme.onErrorLight, AppTheme.errorLight);
      expect(lightOnErrorRatio, greaterThan(4.5), 
        reason: 'Light theme on-error color should have sufficient contrast');
      
      // Dark theme error colors
      final darkErrorRatio = _calculateContrastRatio(AppTheme.errorDark, AppTheme.backgroundDark);
      expect(darkErrorRatio, greaterThan(3.0), 
        reason: 'Dark theme error color should have sufficient contrast');
      
      final darkOnErrorRatio = _calculateContrastRatio(AppTheme.onErrorDark, AppTheme.errorDark);
      expect(darkOnErrorRatio, greaterThan(4.5), 
        reason: 'Dark theme on-error color should have sufficient contrast');
    });

    test('Success and warning colors have sufficient contrast', () {
      // Light theme
      final lightSuccessRatio = _calculateContrastRatio(AppTheme.successLight, AppTheme.backgroundLight);
      expect(lightSuccessRatio, greaterThan(3.0), 
        reason: 'Light theme success color should have sufficient contrast');
      
      final lightWarningRatio = _calculateContrastRatio(AppTheme.warningLight, AppTheme.backgroundLight);
      expect(lightWarningRatio, greaterThan(3.0), 
        reason: 'Light theme warning color should have sufficient contrast');
      
      // Dark theme
      final darkSuccessRatio = _calculateContrastRatio(AppTheme.successDark, AppTheme.backgroundDark);
      expect(darkSuccessRatio, greaterThan(3.0), 
        reason: 'Dark theme success color should have sufficient contrast');
      
      final darkWarningRatio = _calculateContrastRatio(AppTheme.warningDark, AppTheme.backgroundDark);
      expect(darkWarningRatio, greaterThan(3.0), 
        reason: 'Dark theme warning color should have sufficient contrast');
    });

    test('Card and surface colors provide adequate separation', () {
      // Light theme
      final lightCardBackgroundRatio = _calculateContrastRatio(AppTheme.cardLight, AppTheme.backgroundLight);
      expect(lightCardBackgroundRatio, greaterThan(1.2), 
        reason: 'Light theme cards should be distinguishable from background');
      
      final lightSurfaceBackgroundRatio = _calculateContrastRatio(AppTheme.surfaceLight, AppTheme.backgroundLight);
      expect(lightSurfaceBackgroundRatio, greaterThan(1.1), 
        reason: 'Light theme surfaces should be distinguishable from background');
      
      // Dark theme
      final darkCardBackgroundRatio = _calculateContrastRatio(AppTheme.cardDark, AppTheme.backgroundDark);
      expect(darkCardBackgroundRatio, greaterThan(1.2), 
        reason: 'Dark theme cards should be distinguishable from background');
      
      final darkSurfaceBackgroundRatio = _calculateContrastRatio(AppTheme.surfaceDark, AppTheme.backgroundDark);
      expect(darkSurfaceBackgroundRatio, greaterThan(1.1), 
        reason: 'Dark theme surfaces should be distinguishable from background');
    });
  });
}

/// Test theme contrast ratios for a specific theme
Future<void> _testThemeContrastRatios(WidgetTester tester, ThemeData theme, String themeName) async {
  await tester.pumpWidget(
    MaterialApp(
      theme: theme,
      home: Builder(
        builder: (context) {
          final colorScheme = Theme.of(context).colorScheme;
          
          // Test primary color combinations
          final primaryOnBackgroundRatio = _calculateContrastRatio(
            colorScheme.primary, 
            colorScheme.surface,
          );
          expect(primaryOnBackgroundRatio, greaterThan(3.0), 
            reason: '$themeName: Primary on surface should have at least 3:1 contrast');
          
          final onPrimaryRatio = _calculateContrastRatio(
            colorScheme.onPrimary, 
            colorScheme.primary,
          );
          expect(onPrimaryRatio, greaterThan(4.5), 
            reason: '$themeName: On-primary should have at least 4.5:1 contrast');
          
          // Test secondary color combinations
          final secondaryOnBackgroundRatio = _calculateContrastRatio(
            colorScheme.secondary, 
            colorScheme.surface,
          );
          expect(secondaryOnBackgroundRatio, greaterThan(3.0), 
            reason: '$themeName: Secondary on surface should have at least 3:1 contrast');
          
          // Test error color combinations
          final errorOnBackgroundRatio = _calculateContrastRatio(
            colorScheme.error, 
            colorScheme.surface,
          );
          expect(errorOnBackgroundRatio, greaterThan(3.0), 
            reason: '$themeName: Error on surface should have at least 3:1 contrast');
          
          final onErrorRatio = _calculateContrastRatio(
            colorScheme.onError, 
            colorScheme.error,
          );
          expect(onErrorRatio, greaterThan(4.5), 
            reason: '$themeName: On-error should have at least 4.5:1 contrast');
          
          // Test surface and background combinations
          final onSurfaceRatio = _calculateContrastRatio(
            colorScheme.onSurface, 
            colorScheme.surface,
          );
          expect(onSurfaceRatio, greaterThan(4.5), 
            reason: '$themeName: On-surface should have at least 4.5:1 contrast');
          
          return const Scaffold(
            body: Center(child: Text('Theme Test')),
          );
        },
      ),
    ),
  );
  
  await tester.pumpAndSettle();
}

/// Calculate contrast ratio between two colors
/// Based on WCAG 2.1 guidelines
double _calculateContrastRatio(Color color1, Color color2) {
  final luminance1 = color1.computeLuminance();
  final luminance2 = color2.computeLuminance();
  
  final lighter = luminance1 > luminance2 ? luminance1 : luminance2;
  final darker = luminance1 > luminance2 ? luminance2 : luminance1;
  
  return (lighter + 0.05) / (darker + 0.05);
}

/// Generate a comprehensive contrast report
String generateContrastReport() {
  final report = StringBuffer();
  report.writeln('# Theme Contrast Ratio Report\n');
  
  // Light theme report
  report.writeln('## Light Theme');
  report.writeln('| Element | Foreground | Background | Ratio | Status |');
  report.writeln('|---------|------------|------------|-------|--------|');
  
  _addContrastReportRow(report, 'Primary Text', AppTheme.textPrimaryLight, AppTheme.backgroundLight);
  _addContrastReportRow(report, 'Secondary Text', AppTheme.textSecondaryLight, AppTheme.backgroundLight);
  _addContrastReportRow(report, 'Primary Button', AppTheme.onPrimaryLight, AppTheme.primaryLight);
  _addContrastReportRow(report, 'Error Text', AppTheme.onErrorLight, AppTheme.errorLight);
  _addContrastReportRow(report, 'Success Color', AppTheme.successLight, AppTheme.backgroundLight);
  _addContrastReportRow(report, 'Warning Color', AppTheme.warningLight, AppTheme.backgroundLight);
  
  report.writeln('\n## Dark Theme');
  report.writeln('| Element | Foreground | Background | Ratio | Status |');
  report.writeln('|---------|------------|------------|-------|--------|');
  
  _addContrastReportRow(report, 'Primary Text', AppTheme.textPrimaryDark, AppTheme.backgroundDark);
  _addContrastReportRow(report, 'Secondary Text', AppTheme.textSecondaryDark, AppTheme.backgroundDark);
  _addContrastReportRow(report, 'Primary Button', AppTheme.onPrimaryDark, AppTheme.primaryDark);
  _addContrastReportRow(report, 'Error Text', AppTheme.onErrorDark, AppTheme.errorDark);
  _addContrastReportRow(report, 'Success Color', AppTheme.successDark, AppTheme.backgroundDark);
  _addContrastReportRow(report, 'Warning Color', AppTheme.warningDark, AppTheme.backgroundDark);
  
  report.writeln('\n## WCAG Guidelines');
  report.writeln('- **AA Normal Text**: 4.5:1 minimum');
  report.writeln('- **AA Large Text**: 3:1 minimum');
  report.writeln('- **AAA Normal Text**: 7:1 minimum');
  report.writeln('- **AAA Large Text**: 4.5:1 minimum');
  
  return report.toString();
}

void _addContrastReportRow(StringBuffer report, String element, Color foreground, Color background) {
  final ratio = _calculateContrastRatio(foreground, background);
  final status = _getContrastStatus(ratio);
  
  report.writeln('| $element | ${_colorToHex(foreground)} | ${_colorToHex(background)} | ${ratio.toStringAsFixed(2)}:1 | $status |');
}

String _getContrastStatus(double ratio) {
  if (ratio >= 7.0) return '✅ AAA';
  if (ratio >= 4.5) return '✅ AA';
  if (ratio >= 3.0) return '⚠️ AA Large';
  return '❌ Fail';
}

String _colorToHex(Color color) {
  return '#${color.value.toRadixString(16).padLeft(8, '0').substring(2).toUpperCase()}';
}