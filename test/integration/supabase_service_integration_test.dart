import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import '../../lib/services/supabase_service.dart';
import '../../lib/models/activity_log.dart';
import '../../lib/models/milestone.dart';

/// Integration tests for SupabaseService
/// These tests require a test database connection
void main() {
  group('SupabaseService Integration Tests', () {
    late SupabaseService supabaseService;
    
    setUpAll(() async {
      // Initialize test environment
      TestWidgetsFlutterBinding.ensureInitialized();
      
      // Mock environment variables for testing
      const MethodChannel('flutter/platform')
          .setMockMethodCallHandler((MethodCall methodCall) async {
        if (methodCall.method == 'SystemChrome.setApplicationSwitcherDescription') {
          return null;
        }
        return null;
      });
      
      await SupabaseService.initialize();
      supabaseService = SupabaseService();
    });

    group('Activity Operations', () {
      test('should insert and retrieve activity log', () async {
        // Create test activity
        final testActivity = ActivityLog.fromRawData(
          babyId: 'test-baby-id',
          type: 'feeding',
          data: {
            'startTime': DateTime.now(),
            'amount': 120,
            'unit': 'ml',
            'notes': 'Test feeding activity',
          },
        );

        // Insert activity
        final insertResult = await supabaseService.insertActivityLog(testActivity);
        expect(insertResult, isNotNull);
        expect(insertResult['id'], isNotNull);

        // Retrieve activities
        final activities = await supabaseService.getRecentActivities(
          'test-baby-id',
          limit: 1,
        );
        
        expect(activities, isNotEmpty);
        expect(activities.first.type, equals('feeding'));
      });

      test('should handle invalid baby ID gracefully', () async {
        expect(
          () => supabaseService.getRecentActivities('invalid-id'),
          throwsA(isA<ArgumentError>()),
        );
      });
    });

    group('Milestone Operations', () {
      test('should insert and retrieve milestone', () async {
        final testMilestone = Milestone(
          id: 'test-milestone-id',
          babyId: 'test-baby-id',
          title: 'First Smile',
          description: 'Baby smiled for the first time',
          category: MilestoneCategory.social,
          type: MilestoneType.standard,
          achievedDate: DateTime.now(),
          ageInMonths: 2,
          ageInDays: 60,
          isCustom: false,
        );

        // Insert milestone
        await supabaseService.insertMilestone(testMilestone);

        // Retrieve milestones
        final milestones = await supabaseService.getMilestones('test-baby-id');
        
        expect(milestones, isNotEmpty);
        expect(milestones.any((m) => m.title == 'First Smile'), isTrue);
      });
    });

    group('Error Handling', () {
      test('should handle network errors gracefully', () async {
        // This would require mocking network failures
        // Implementation depends on your testing strategy
      });

      test('should validate input parameters', () async {
        expect(
          () => supabaseService.getAllScheduledActivities(''),
          throwsA(isA<ArgumentError>()),
        );
        
        expect(
          () => supabaseService.getAllScheduledActivities('invalid-uuid'),
          throwsA(isA<ArgumentError>()),
        );
      });
    });
  });
}