import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'package:babytracker_pro/core/app_export.dart';
import 'package:babytracker_pro/presentation/main_navigation/main_navigation_screen.dart';
import 'package:babytracker_pro/presentation/settings/settings.dart';
import 'package:babytracker_pro/presentation/auth/sign_in_screen.dart';
import 'package:babytracker_pro/presentation/tracker_screen/tracker_screen.dart';
import 'package:babytracker_pro/presentation/milestones/milestones_screen.dart';
import 'package:babytracker_pro/presentation/ai_insights_dashboard/ai_insights_dashboard.dart';
import 'package:babytracker_pro/presentation/growth_charts/growth_charts.dart';
import 'package:babytracker_pro/models/baby_profile.dart';

void main() {
  group('Comprehensive Theme Validation Tests', () {
    late ThemeService themeService;
    
    setUp(() {
      themeService = ThemeService();
    });

    testWidgets('Theme switching works across all major screens', (WidgetTester tester) async {
      // Test theme switching on main navigation screen
      await _testThemeSwitchingOnScreen(
        tester,
        themeService,
        'MainNavigationScreen',
        const MainNavigationScreen(),
      );
      
      // Test theme switching on settings screen
      await _testThemeSwitchingOnScreen(
        tester,
        themeService,
        'SettingsScreen',
        const Settings(),
      );
      
      // Test theme switching on sign in screen
      await _testThemeSwitchingOnScreen(
        tester,
        themeService,
        'SignInScreen',
        const SignInScreen(),
      );
    });

    testWidgets('Navigation components maintain theme consistency', (WidgetTester tester) async {
      await _buildAppWithTheme(tester, themeService, ThemeMode.light);
      
      // Test bottom navigation bar in light theme
      final bottomNavBar = find.byType(BottomNavigationBar);
      expect(bottomNavBar, findsOneWidget);
      
      final BottomNavigationBar navBarWidget = tester.widget(bottomNavBar);
      expect(navBarWidget.selectedItemColor, equals(AppTheme.primaryLight));
      expect(navBarWidget.unselectedItemColor, isNotNull);
      
      // Switch to dark theme
      await _switchTheme(tester, themeService, ThemeMode.dark);
      
      // Verify bottom navigation bar adapts to dark theme
      final darkBottomNavBar = find.byType(BottomNavigationBar);
      expect(darkBottomNavBar, findsOneWidget);
      
      final BottomNavigationBar darkNavBarWidget = tester.widget(darkBottomNavBar);
      expect(darkNavBarWidget.selectedItemColor, equals(AppTheme.primaryDark));
    });

    testWidgets('Interactive elements maintain proper contrast in both themes', (WidgetTester tester) async {
      // Test in light theme
      await _buildAppWithTheme(tester, themeService, ThemeMode.light);
      await _verifyInteractiveElementsContrast(tester, Brightness.light);
      
      // Test in dark theme
      await _switchTheme(tester, themeService, ThemeMode.dark);
      await _verifyInteractiveElementsContrast(tester, Brightness.dark);
    });

    testWidgets('Form components work seamlessly with both themes', (WidgetTester tester) async {
      await _testFormComponentsInBothThemes(tester, themeService);
    });

    testWidgets('Cards and containers use proper theme colors', (WidgetTester tester) async {
      await _testCardAndContainerThemes(tester, themeService);
    });

    testWidgets('Text elements have sufficient contrast in both themes', (WidgetTester tester) async {
      await _testTextContrastInBothThemes(tester, themeService);
    });

    testWidgets('Dialog and modal components respect theme settings', (WidgetTester tester) async {
      await _testDialogAndModalThemes(tester, themeService);
    });

    testWidgets('Theme switching provides immediate visual feedback', (WidgetTester tester) async {
      await _testImmediateThemeFeedback(tester, themeService);
    });

    testWidgets('Navigation flow maintains theme consistency', (WidgetTester tester) async {
      await _testNavigationThemeConsistency(tester, themeService);
    });

    testWidgets('Accessibility compliance for contrast ratios', (WidgetTester tester) async {
      await _testAccessibilityCompliance(tester, themeService);
    });
  });
}

/// Helper function to test theme switching on a specific screen
Future<void> _testThemeSwitchingOnScreen(
  WidgetTester tester,
  ThemeService themeService,
  String screenName,
  Widget screen,
) async {
  // Test light theme
  await _buildAppWithTheme(tester, themeService, ThemeMode.light);
  await tester.pumpWidget(_wrapWithMaterialApp(screen, ThemeMode.light));
  await tester.pumpAndSettle();
  
  // Verify light theme is applied
  final lightScaffold = find.byType(Scaffold);
  if (lightScaffold.evaluate().isNotEmpty) {
    final Scaffold scaffoldWidget = tester.widget(lightScaffold.first);
    expect(scaffoldWidget.backgroundColor, anyOf(
      equals(AppTheme.backgroundLight),
      isNull, // Uses theme default
    ));
  }
  
  // Switch to dark theme
  await tester.pumpWidget(_wrapWithMaterialApp(screen, ThemeMode.dark));
  await tester.pumpAndSettle();
  
  // Verify dark theme is applied
  final darkScaffold = find.byType(Scaffold);
  if (darkScaffold.evaluate().isNotEmpty) {
    final Scaffold darkScaffoldWidget = tester.widget(darkScaffold.first);
    expect(darkScaffoldWidget.backgroundColor, anyOf(
      equals(AppTheme.backgroundDark),
      isNull, // Uses theme default
    ));
  }
}

/// Helper function to build app with specific theme
Future<void> _buildAppWithTheme(
  WidgetTester tester,
  ThemeService themeService,
  ThemeMode themeMode,
) async {
  themeService.setThemeMode(themeMode);
  
  await tester.pumpWidget(
    ChangeNotifierProvider.value(
      value: themeService,
      child: Consumer<ThemeService>(
        builder: (context, themeService, child) {
          return Sizer(
            builder: (context, orientation, screenType) {
              return MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeService.themeMode,
                home: const MainNavigationScreen(),
              );
            },
          );
        },
      ),
    ),
  );
  await tester.pumpAndSettle();
}

/// Helper function to switch theme
Future<void> _switchTheme(
  WidgetTester tester,
  ThemeService themeService,
  ThemeMode newTheme,
) async {
  themeService.setThemeMode(newTheme);
  await tester.pumpAndSettle();
}

/// Helper function to wrap widget with MaterialApp
Widget _wrapWithMaterialApp(Widget child, ThemeMode themeMode) {
  return Sizer(
    builder: (context, orientation, screenType) {
      return MaterialApp(
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: themeMode,
        home: child,
      );
    },
  );
}

/// Verify interactive elements have proper contrast
Future<void> _verifyInteractiveElementsContrast(
  WidgetTester tester,
  Brightness brightness,
) async {
  // Find buttons and verify they have appropriate colors
  final elevatedButtons = find.byType(ElevatedButton);
  for (final buttonFinder in elevatedButtons.evaluate()) {
    final ElevatedButton button = buttonFinder.widget as ElevatedButton;
    final ButtonStyle? style = button.style;
    
    if (style != null) {
      final backgroundColor = style.backgroundColor?.resolve({});
      final foregroundColor = style.foregroundColor?.resolve({});
      
      if (backgroundColor != null && foregroundColor != null) {
        // Verify contrast exists (basic check)
        expect(backgroundColor, isNot(equals(foregroundColor)));
      }
    }
  }
  
  // Find text buttons and verify colors
  final textButtons = find.byType(TextButton);
  for (final buttonFinder in textButtons.evaluate()) {
    final TextButton button = buttonFinder.widget as TextButton;
    final ButtonStyle? style = button.style;
    
    if (style != null) {
      final foregroundColor = style.foregroundColor?.resolve({});
      expect(foregroundColor, isNotNull);
    }
  }
}

/// Test form components in both themes
Future<void> _testFormComponentsInBothThemes(
  WidgetTester tester,
  ThemeService themeService,
) async {
  final formWidget = Scaffold(
    body: Column(
      children: [
        TextField(
          decoration: InputDecoration(
            labelText: 'Test Field',
            hintText: 'Enter text',
          ),
        ),
        ElevatedButton(
          onPressed: () {},
          child: Text('Submit'),
        ),
        Switch(
          value: true,
          onChanged: (value) {},
        ),
        Checkbox(
          value: true,
          onChanged: (value) {},
        ),
      ],
    ),
  );
  
  // Test in light theme
  await tester.pumpWidget(_wrapWithMaterialApp(formWidget, ThemeMode.light));
  await tester.pumpAndSettle();
  
  // Verify form elements exist and are visible
  expect(find.byType(TextField), findsOneWidget);
  expect(find.byType(ElevatedButton), findsOneWidget);
  expect(find.byType(Switch), findsOneWidget);
  expect(find.byType(Checkbox), findsOneWidget);
  
  // Test in dark theme
  await tester.pumpWidget(_wrapWithMaterialApp(formWidget, ThemeMode.dark));
  await tester.pumpAndSettle();
  
  // Verify form elements still exist and are visible
  expect(find.byType(TextField), findsOneWidget);
  expect(find.byType(ElevatedButton), findsOneWidget);
  expect(find.byType(Switch), findsOneWidget);
  expect(find.byType(Checkbox), findsOneWidget);
}

/// Test card and container themes
Future<void> _testCardAndContainerThemes(
  WidgetTester tester,
  ThemeService themeService,
) async {
  final cardWidget = Scaffold(
    body: Column(
      children: [
        Card(
          child: Padding(
            padding: EdgeInsets.all(16),
            child: Text('Test Card'),
          ),
        ),
        Container(
          padding: EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text('Test Container'),
        ),
      ],
    ),
  );
  
  // Test in light theme
  await tester.pumpWidget(_wrapWithMaterialApp(cardWidget, ThemeMode.light));
  await tester.pumpAndSettle();
  
  final lightCard = find.byType(Card);
  expect(lightCard, findsOneWidget);
  
  final Card lightCardWidget = tester.widget(lightCard);
  expect(lightCardWidget.color, anyOf(
    equals(AppTheme.cardLight),
    isNull, // Uses theme default
  ));
  
  // Test in dark theme
  await tester.pumpWidget(_wrapWithMaterialApp(cardWidget, ThemeMode.dark));
  await tester.pumpAndSettle();
  
  final darkCard = find.byType(Card);
  expect(darkCard, findsOneWidget);
}

/// Test text contrast in both themes
Future<void> _testTextContrastInBothThemes(
  WidgetTester tester,
  ThemeService themeService,
) async {
  final textWidget = Scaffold(
    body: Column(
      children: [
        Text('Primary Text', style: TextStyle(fontSize: 16)),
        Text('Secondary Text', style: TextStyle(fontSize: 14, color: Colors.grey)),
        Text('Disabled Text', style: TextStyle(fontSize: 12, color: Colors.grey[400])),
      ],
    ),
  );
  
  // Test in light theme
  await tester.pumpWidget(_wrapWithMaterialApp(textWidget, ThemeMode.light));
  await tester.pumpAndSettle();
  
  final lightTexts = find.byType(Text);
  expect(lightTexts, findsNWidgets(3));
  
  // Test in dark theme
  await tester.pumpWidget(_wrapWithMaterialApp(textWidget, ThemeMode.dark));
  await tester.pumpAndSettle();
  
  final darkTexts = find.byType(Text);
  expect(darkTexts, findsNWidgets(3));
}

/// Test dialog and modal themes
Future<void> _testDialogAndModalThemes(
  WidgetTester tester,
  ThemeService themeService,
) async {
  final dialogWidget = Scaffold(
    body: Builder(
      builder: (context) => ElevatedButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Test Dialog'),
              content: Text('This is a test dialog'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text('OK'),
                ),
              ],
            ),
          );
        },
        child: Text('Show Dialog'),
      ),
    ),
  );
  
  // Test dialog in light theme
  await tester.pumpWidget(_wrapWithMaterialApp(dialogWidget, ThemeMode.light));
  await tester.pumpAndSettle();
  
  await tester.tap(find.text('Show Dialog'));
  await tester.pumpAndSettle();
  
  expect(find.byType(AlertDialog), findsOneWidget);
  
  // Close dialog
  await tester.tap(find.text('OK'));
  await tester.pumpAndSettle();
  
  // Test dialog in dark theme
  await tester.pumpWidget(_wrapWithMaterialApp(dialogWidget, ThemeMode.dark));
  await tester.pumpAndSettle();
  
  await tester.tap(find.text('Show Dialog'));
  await tester.pumpAndSettle();
  
  expect(find.byType(AlertDialog), findsOneWidget);
  
  // Close dialog
  await tester.tap(find.text('OK'));
  await tester.pumpAndSettle();
}

/// Test immediate theme feedback
Future<void> _testImmediateThemeFeedback(
  WidgetTester tester,
  ThemeService themeService,
) async {
  await _buildAppWithTheme(tester, themeService, ThemeMode.light);
  
  // Verify light theme is active
  final lightTheme = Theme.of(tester.element(find.byType(MaterialApp)));
  expect(lightTheme.brightness, equals(Brightness.light));
  
  // Switch to dark theme
  await _switchTheme(tester, themeService, ThemeMode.dark);
  
  // Verify dark theme is immediately active
  final darkTheme = Theme.of(tester.element(find.byType(MaterialApp)));
  expect(darkTheme.brightness, equals(Brightness.dark));
}

/// Test navigation theme consistency
Future<void> _testNavigationThemeConsistency(
  WidgetTester tester,
  ThemeService themeService,
) async {
  await _buildAppWithTheme(tester, themeService, ThemeMode.dark);
  
  // Verify main navigation is in dark theme
  final darkTheme = Theme.of(tester.element(find.byType(MaterialApp)));
  expect(darkTheme.brightness, equals(Brightness.dark));
  
  // Switch theme and verify consistency is maintained
  await _switchTheme(tester, themeService, ThemeMode.light);
  
  final lightTheme = Theme.of(tester.element(find.byType(MaterialApp)));
  expect(lightTheme.brightness, equals(Brightness.light));
}

/// Test accessibility compliance
Future<void> _testAccessibilityCompliance(
  WidgetTester tester,
  ThemeService themeService,
) async {
  // Test light theme accessibility
  await _buildAppWithTheme(tester, themeService, ThemeMode.light);
  
  // Verify contrast ratios meet minimum requirements
  final lightPrimary = AppTheme.primaryLight;
  final lightBackground = AppTheme.backgroundLight;
  
  // Basic contrast check (simplified)
  expect(lightPrimary.computeLuminance(), isNot(equals(lightBackground.computeLuminance())));
  
  // Test dark theme accessibility
  await _switchTheme(tester, themeService, ThemeMode.dark);
  
  final darkPrimary = AppTheme.primaryDark;
  final darkBackground = AppTheme.backgroundDark;
  
  // Basic contrast check (simplified)
  expect(darkPrimary.computeLuminance(), isNot(equals(darkBackground.computeLuminance())));
}