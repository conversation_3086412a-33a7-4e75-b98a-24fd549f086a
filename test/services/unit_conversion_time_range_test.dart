import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../lib/services/unit_conversion_service.dart';
import '../../lib/services/time_range_service.dart';
import '../../lib/services/responsive_chart_service.dart';
import '../../lib/services/offline_who_data_cache.dart';

void main() {
  group('Unit Conversion Service Tests', () {
    test('Weight conversion - kg to lbs', () {
      final result = UnitConversionService.convertWeight(
        value: 10.0,
        fromUnit: 'kg',
        toUnit: 'lbs',
      );
      
      expect(result.value, closeTo(22.05, 0.1));
      expect(result.unit, equals('lbs'));
      expect(result.metricValue, equals(10.0));
      expect(result.precision, equals(1));
    });

    test('Weight conversion - lbs to kg', () {
      final result = UnitConversionService.convertWeight(
        value: 22.0,
        fromUnit: 'lbs',
        toUnit: 'kg',
      );
      
      expect(result.value, closeTo(9.98, 0.1));
      expect(result.unit, equals('kg'));
      expect(result.metricValue, closeTo(9.98, 0.1));
      expect(result.precision, equals(2));
    });

    test('Length conversion - cm to inches', () {
      final result = UnitConversionService.convertLength(
        value: 100.0,
        fromUnit: 'cm',
        toUnit: 'in',
      );
      
      expect(result.value, closeTo(39.37, 0.1));
      expect(result.unit, equals('in'));
      expect(result.metricValue, equals(100.0));
      expect(result.precision, equals(1));
    });

    test('Length conversion - inches to cm', () {
      final result = UnitConversionService.convertLength(
        value: 39.37,
        fromUnit: 'in',
        toUnit: 'cm',
      );
      
      expect(result.value, closeTo(100.0, 0.1));
      expect(result.unit, equals('cm'));
      expect(result.metricValue, closeTo(100.0, 0.1));
      expect(result.precision, equals(0));
    });

    test('Measurement validation - valid weight', () {
      final result = UnitConversionService.validateMeasurement(
        value: 5.0,
        unit: 'kg',
        measurementType: 'weight',
        ageInMonths: 6.0,
      );
      
      expect(result.isValid, isTrue);
      expect(result.severity, equals(ValidationSeverity.none));
    });

    test('Measurement validation - invalid weight (too high)', () {
      final result = UnitConversionService.validateMeasurement(
        value: 60.0,
        unit: 'kg',
        measurementType: 'weight',
        ageInMonths: 6.0,
      );
      
      expect(result.isValid, isFalse);
      expect(result.severity, equals(ValidationSeverity.error));
    });

    test('Get default units - metric weight', () {
      final units = UnitConversionService.getDefaultUnits('weight', true);
      
      expect(units.primary, equals('kg'));
      expect(units.secondary, equals('g'));
      expect(units.display, equals('kg'));
    });

    test('Get default units - imperial height', () {
      final units = UnitConversionService.getDefaultUnits('height', false);
      
      expect(units.primary, equals('in'));
      expect(units.secondary, equals('ft'));
      expect(units.display, equals('in'));
    });

    test('Format measurement with unit', () {
      final formatted = UnitConversionService.formatMeasurement(
        value: 12.345,
        unit: 'kg',
        includeUnit: true,
      );
      
      expect(formatted, equals('12.35 kg'));
    });
  });

  group('Time Range Service Tests', () {
    test('Get time range by ID', () {
      final timeRange = TimeRangeService.getTimeRangeById('1_year');
      
      expect(timeRange, isNotNull);
      expect(timeRange!.label, equals('1 Year'));
      expect(timeRange.maxAgeMonths, equals(12.0));
      expect(timeRange.intervalMonths, equals(2.0));
    });

    test('Get relevant time ranges for baby age', () {
      final ranges = TimeRangeService.getRelevantTimeRanges(8.0); // 8 months old
      
      expect(ranges.length, greaterThan(3));
      expect(ranges.any((r) => r.id == '6_months'), isTrue);
      expect(ranges.any((r) => r.id == '1_year'), isTrue);
      expect(ranges.any((r) => r.id == 'all'), isTrue);
    });

    test('Calculate chart scaling with measurements', () {
      final measurements = [
        {'ageInMonths': 3.0, 'value': 5.5},
        {'ageInMonths': 6.0, 'value': 7.2},
        {'ageInMonths': 9.0, 'value': 8.8},
      ];
      
      final timeRange = TimeRangeService.getTimeRangeById('1_year')!;
      final scaling = TimeRangeService.calculateChartScaling(
        timeRange: timeRange,
        measurements: measurements,
        measurementType: 'weight',
        isMetric: true,
      );
      
      expect(scaling.minX, equals(0.0));
      expect(scaling.maxX, equals(12.0));
      expect(scaling.minY, lessThan(5.5));
      expect(scaling.maxY, greaterThan(8.8));
      expect(scaling.xInterval, equals(2.0));
    });

    test('Format age - months only', () {
      final formatted = TimeRangeService.formatAge(15.0, AgeDisplayFormat.monthsOnly);
      expect(formatted, equals('15m'));
    });

    test('Format age - years and months', () {
      final formatted = TimeRangeService.formatAge(15.0, AgeDisplayFormat.yearsAndMonths);
      expect(formatted, equals('1y 3m'));
    });

    test('Format age - decimal years', () {
      final formatted = TimeRangeService.formatAge(18.0, AgeDisplayFormat.decimalYears);
      expect(formatted, equals('1.5y'));
    });

    test('Format age - adaptive (young)', () {
      final formatted = TimeRangeService.formatAge(8.0, AgeDisplayFormat.adaptive);
      expect(formatted, equals('8m'));
    });

    test('Format age - adaptive (older)', () {
      final formatted = TimeRangeService.formatAge(30.0, AgeDisplayFormat.adaptive);
      expect(formatted, equals('2y 6m'));
    });

    test('Get optimal age format for time range', () {
      final shortRange = TimeRangeService.getTimeRangeById('6_months')!;
      final mediumRange = TimeRangeService.getTimeRangeById('2_years')!;
      final longRange = TimeRangeService.getTimeRangeById('5_years')!;
      
      expect(TimeRangeService.getOptimalAgeFormat(shortRange), 
             equals(AgeDisplayFormat.monthsOnly));
      expect(TimeRangeService.getOptimalAgeFormat(mediumRange), 
             equals(AgeDisplayFormat.yearsAndMonths));
      expect(TimeRangeService.getOptimalAgeFormat(longRange), 
             equals(AgeDisplayFormat.adaptive));
    });

    test('Generate age labels', () {
      final timeRange = TimeRangeService.getTimeRangeById('1_year')!;
      final labels = TimeRangeService.generateAgeLabels(
        timeRange, 
        AgeDisplayFormat.monthsOnly
      );
      
      expect(labels.length, greaterThan(6));
      expect(labels.first.ageInMonths, equals(0.0));
      expect(labels.last.ageInMonths, equals(12.0));
      expect(labels.any((l) => l.isMinor), isTrue);
    });
  });

  group('Responsive Chart Service Tests', () {
    testWidgets('Get chart layout for mobile', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            // Mock mobile screen size
            tester.view.physicalSize = const Size(375, 667);
            tester.view.devicePixelRatio = 2.0;
            
            final layout = ResponsiveChartService.getChartLayout(context);
            
            expect(layout.showExtendedControls, isFalse);
            expect(layout.chartHeight, lessThan(400));
            expect(layout.fontSize.title, lessThan(20));
            
            return Container();
          },
        ),
      );
    });

    testWidgets('Get chart layout for tablet', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            // Mock tablet screen size
            tester.view.physicalSize = const Size(768, 1024);
            tester.view.devicePixelRatio = 2.0;
            
            final layout = ResponsiveChartService.getChartLayout(context);
            
            expect(layout.showExtendedControls, isTrue);
            expect(layout.showDetailedLegend, isTrue);
            expect(layout.chartHeight, greaterThan(300));
            
            return Container();
          },
        ),
      );
    });

    testWidgets('Calculate optimal dimensions', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            final dimensions = ResponsiveChartService.calculateOptimalDimensions(
              context: context,
              dataPointCount: 25,
              timeRange: '1_year',
              hasLegend: true,
              hasControls: true,
            );
            
            expect(dimensions.totalHeight, greaterThan(200));
            expect(dimensions.chartHeight, greaterThan(100));
            expect(dimensions.width, greaterThan(200));
            expect(dimensions.aspectRatio, greaterThan(1.0));
            
            return Container();
          },
        ),
      );
    });

    testWidgets('Get controls grid config', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            final gridConfig = ResponsiveChartService.getControlsGridConfig(context);
            
            expect(gridConfig.crossAxisCount, greaterThan(1));
            expect(gridConfig.childAspectRatio, greaterThan(1.0));
            expect(gridConfig.crossAxisSpacing, greaterThan(0));
            
            return Container();
          },
        ),
      );
    });

    testWidgets('Get legend config', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            final legendConfig = ResponsiveChartService.getLegendConfig(context);
            
            expect(legendConfig.itemsPerRow, greaterThan(2));
            expect(legendConfig.showIcons, isTrue);
            expect(legendConfig.showLabels, isTrue);
            
            return Container();
          },
        ),
      );
    });
  });

  group('Offline WHO Data Cache Tests', () {
    test('Cache statistics initial state', () async {
      final stats = await OfflineWHODataCache.getCacheStatistics();
      
      expect(stats.entryCount, greaterThanOrEqualTo(0));
      expect(stats.totalSizeBytes, greaterThanOrEqualTo(0));
      expect(stats.formattedSize, isNotEmpty);
    });

    test('Cache validity check', () async {
      final isValid = await OfflineWHODataCache.isCacheValid();
      
      // Should be boolean
      expect(isValid, isA<bool>());
    });

    test('Initialize cache', () async {
      // This should not throw an exception
      await OfflineWHODataCache.initializeCache();
      
      // After initialization, cache should exist
      final stats = await OfflineWHODataCache.getCacheStatistics();
      expect(stats.entryCount, greaterThanOrEqualTo(0));
    });

    test('Get cached percentile curve', () async {
      // Initialize cache first
      await OfflineWHODataCache.initializeCache();
      
      final curvePoints = await OfflineWHODataCache.getCachedPercentileCurve(
        percentile: 50.0,
        measurementType: 'weight',
        gender: 'male',
      );
      
      // Should return either null or a list of points
      if (curvePoints != null) {
        expect(curvePoints, isA<List<Map<String, double>>>());
        expect(curvePoints.length, greaterThan(0));
      }
    });

    test('Calculate percentile offline', () async {
      // Initialize cache first
      await OfflineWHODataCache.initializeCache();
      
      final percentile = await OfflineWHODataCache.calculatePercentileOffline(
        value: 7.0,
        ageInMonths: 6.0,
        measurementType: 'weight',
        gender: 'male',
      );
      
      // Should return either null or a valid percentile
      if (percentile != null) {
        expect(percentile, greaterThanOrEqualTo(0.1));
        expect(percentile, lessThanOrEqualTo(99.9));
      }
    });

    test('Clear cache', () async {
      await OfflineWHODataCache.clearCache();
      
      final stats = await OfflineWHODataCache.getCacheStatistics();
      expect(stats.entryCount, equals(0));
      expect(stats.totalSizeBytes, equals(0));
    });
  });

  group('Integration Tests', () {
    test('Unit conversion maintains WHO percentile accuracy', () {
      // Test that converting units and back maintains accuracy
      const originalValue = 7.5; // kg
      
      // Convert to imperial
      final imperialResult = UnitConversionService.convertWeight(
        value: originalValue,
        fromUnit: 'kg',
        toUnit: 'lbs',
      );
      
      // Convert back to metric
      final backToMetricResult = UnitConversionService.convertWeight(
        value: imperialResult.value,
        fromUnit: 'lbs',
        toUnit: 'kg',
      );
      
      // Should be very close to original
      expect(backToMetricResult.value, closeTo(originalValue, 0.01));
    });

    test('Time range scaling adapts to measurement type and units', () {
      final timeRange = TimeRangeService.getTimeRangeById('1_year')!;
      
      // Test metric weight scaling
      final metricWeightScaling = TimeRangeService.calculateChartScaling(
        timeRange: timeRange,
        measurements: [],
        measurementType: 'weight',
        isMetric: true,
      );
      
      // Test imperial weight scaling
      final imperialWeightScaling = TimeRangeService.calculateChartScaling(
        timeRange: timeRange,
        measurements: [],
        measurementType: 'weight',
        isMetric: false,
      );
      
      // Imperial should have higher max values (lbs vs kg)
      expect(imperialWeightScaling.maxY, greaterThan(metricWeightScaling.maxY));
      expect(imperialWeightScaling.yAxisTitle, contains('lbs'));
      expect(metricWeightScaling.yAxisTitle, contains('kg'));
    });

    test('Age formatting consistency across time ranges', () {
      final ages = [3.0, 6.0, 12.0, 18.0, 24.0, 36.0];
      
      for (final age in ages) {
        final ranges = TimeRangeService.getRelevantTimeRanges(age);
        
        for (final range in ranges) {
          final format = TimeRangeService.getOptimalAgeFormat(range);
          final formatted = TimeRangeService.formatAge(age, format);
          
          // Should always produce a non-empty string
          expect(formatted, isNotEmpty);
          // Should contain either 'm' or 'y'
          expect(formatted, anyOf(contains('m'), contains('y')));
        }
      }
    });
  });
}