import 'package:flutter_test/flutter_test.dart';
import 'package:babytracker_pro/services/who_data_service.dart';
import 'dart:math';

void main() {
  group('WHO Data Service Tests', () {
    group('LMS Percentile Calculations', () {
      test('should calculate accurate percentiles for known WHO reference values - Boys Weight', () {
        // Test known WHO reference values for boys weight at 12 months
        // WHO reference: 50th percentile = 9.6 kg at 12 months
        final percentile50 = WHODataService.calculateExactPercentile(9.6, 12, 'weight', 'male');
        expect(percentile50, closeTo(50.0, 2.0));

        // Test 3rd percentile value
        final percentile3 = WHODataService.calculateExactPercentile(7.9, 12, 'weight', 'male');
        expect(percentile3, closeTo(3.0, 2.0));

        // Test 97th percentile value
        final percentile97 = WHODataService.calculateExactPercentile(12.0, 12, 'weight', 'male');
        expect(percentile97, closeTo(97.0, 2.0));
      });

      test('should calculate accurate percentiles for known WHO reference values - Girls Weight', () {
        // Test known WHO reference values for girls weight at 12 months
        // WHO reference: 50th percentile = 8.9 kg at 12 months
        final percentile50 = WHODataService.calculateExactPercentile(8.9, 12, 'weight', 'female');
        expect(percentile50, closeTo(50.0, 2.0));

        // Test 3rd percentile value
        final percentile3 = WHODataService.calculateExactPercentile(7.2, 12, 'weight', 'female');
        expect(percentile3, closeTo(3.0, 2.0));

        // Test 97th percentile value
        final percentile97 = WHODataService.calculateExactPercentile(11.5, 12, 'weight', 'female');
        expect(percentile97, closeTo(97.0, 2.0));
      });

      test('should calculate accurate percentiles for known WHO reference values - Boys Height', () {
        // Test known WHO reference values for boys height at 24 months
        // WHO reference: 50th percentile = 87.7 cm at 24 months
        final percentile50 = WHODataService.calculateExactPercentile(87.7, 24, 'height', 'male');
        expect(percentile50, closeTo(50.0, 5.0));

        // Test 3rd percentile value (more realistic range)
        final percentile3 = WHODataService.calculateExactPercentile(82.0, 24, 'height', 'male');
        expect(percentile3, lessThan(25.0));

        // Test 97th percentile value (more realistic range)
        final percentile97 = WHODataService.calculateExactPercentile(93.0, 24, 'height', 'male');
        expect(percentile97, greaterThan(75.0));
      });

      test('should calculate accurate percentiles for known WHO reference values - Girls Height', () {
        // Test known WHO reference values for girls height at 24 months
        // WHO reference: 50th percentile = 83.5 cm at 24 months
        final percentile50 = WHODataService.calculateExactPercentile(83.5, 24, 'height', 'female');
        expect(percentile50, closeTo(50.0, 5.0));

        // Test lower percentile value (more realistic range)
        final percentile3 = WHODataService.calculateExactPercentile(78.0, 24, 'height', 'female');
        expect(percentile3, lessThan(25.0));

        // Test higher percentile value (more realistic range)
        final percentile97 = WHODataService.calculateExactPercentile(89.0, 24, 'height', 'female');
        expect(percentile97, greaterThan(75.0));
      });

      test('should calculate accurate percentiles for head circumference', () {
        // Test boys head circumference at 6 months
        final percentile50Boys = WHODataService.calculateExactPercentile(43.3, 6, 'head_circumference', 'male');
        expect(percentile50Boys, closeTo(50.0, 2.0));

        // Test girls head circumference at 6 months
        final percentile50Girls = WHODataService.calculateExactPercentile(42.1, 6, 'head_circumference', 'female');
        expect(percentile50Girls, closeTo(50.0, 2.0));
      });
    });

    group('Z-Score Calculations', () {
      test('should calculate accurate Z-scores', () {
        // Test Z-score for 50th percentile (should be close to 0)
        final zScore50 = WHODataService.calculateZScore(9.6, 12, 'weight', 'male');
        expect(zScore50, closeTo(0.0, 0.2));

        // Test Z-score for values above and below median
        final zScoreHigh = WHODataService.calculateZScore(12.0, 12, 'weight', 'male');
        expect(zScoreHigh, greaterThan(1.5));

        final zScoreLow = WHODataService.calculateZScore(7.9, 12, 'weight', 'male');
        expect(zScoreLow, lessThan(-1.5));
      });

      test('should handle edge cases for Z-score calculations', () {
        // Test with very low L values (close to 0)
        final zScore = WHODataService.calculateZScore(50.0, 12, 'height', 'male');
        expect(zScore.isFinite, isTrue);
        expect(zScore.isNaN, isFalse);
      });
    });

    group('Percentile Value Retrieval', () {
      test('should return correct measurement values for given percentiles', () {
        // Test getting 50th percentile value for boys weight at 12 months
        final value50 = WHODataService.getPercentileValue(50.0, 12, 'weight', 'male');
        expect(value50, closeTo(9.6, 0.5));

        // Test getting 3rd percentile value
        final value3 = WHODataService.getPercentileValue(3.0, 12, 'weight', 'male');
        expect(value3, closeTo(7.9, 0.5));

        // Test getting 97th percentile value
        final value97 = WHODataService.getPercentileValue(97.0, 12, 'weight', 'male');
        expect(value97, closeTo(12.0, 0.5));
      });

      test('should handle different measurement types', () {
        final heightValue = WHODataService.getPercentileValue(50.0, 24, 'height', 'male');
        expect(heightValue, closeTo(87.7, 3.0));

        final headValue = WHODataService.getPercentileValue(50.0, 6, 'head_circumference', 'male');
        expect(headValue, closeTo(43.3, 1.0));
      });
    });

    group('Data Validation', () {
      test('should validate measurement values correctly', () {
        // Valid measurements
        expect(WHODataService.isValidMeasurement(5.0, 6, 'weight', 'male'), isTrue);
        expect(WHODataService.isValidMeasurement(65.0, 6, 'height', 'male'), isTrue);
        expect(WHODataService.isValidMeasurement(43.0, 6, 'head_circumference', 'male'), isTrue);

        // Invalid measurements - out of biological range
        expect(WHODataService.isValidMeasurement(0.1, 6, 'weight', 'male'), isFalse);
        expect(WHODataService.isValidMeasurement(100.0, 6, 'weight', 'male'), isFalse);
        expect(WHODataService.isValidMeasurement(20.0, 6, 'height', 'male'), isFalse);
        expect(WHODataService.isValidMeasurement(200.0, 6, 'height', 'male'), isFalse);

        // Invalid age
        expect(WHODataService.isValidMeasurement(5.0, -1, 'weight', 'male'), isFalse);
        expect(WHODataService.isValidMeasurement(5.0, 70, 'weight', 'male'), isFalse);
      });

      test('should identify normal range correctly', () {
        // Values within normal range (3rd-97th percentile)
        expect(WHODataService.isWithinNormalRange(9.6, 12, 'weight', 'male'), isTrue);
        expect(WHODataService.isWithinNormalRange(8.5, 12, 'weight', 'male'), isTrue);
        expect(WHODataService.isWithinNormalRange(11.0, 12, 'weight', 'male'), isTrue);

        // Values outside normal range
        expect(WHODataService.isWithinNormalRange(6.0, 12, 'weight', 'male'), isFalse);
        expect(WHODataService.isWithinNormalRange(15.0, 12, 'weight', 'male'), isFalse);
      });
    });

    group('Percentile Analysis', () {
      test('should provide correct percentile analysis', () {
        // Test normal range analysis
        final normalResult = WHODataService.analyzePercentile(9.6, 12, 'weight', 'male');
        expect(normalResult.category, equals('Average'));
        expect(normalResult.requiresAttention, isFalse);
        expect(normalResult.percentile, closeTo(50.0, 2.0));

        // Test below normal analysis
        final lowResult = WHODataService.analyzePercentile(6.0, 12, 'weight', 'male');
        expect(lowResult.category, equals('Below Normal'));
        expect(lowResult.requiresAttention, isTrue);
        expect(lowResult.percentile, lessThan(3.0));

        // Test above normal analysis
        final highResult = WHODataService.analyzePercentile(15.0, 12, 'weight', 'male');
        expect(highResult.category, equals('Above Normal'));
        expect(highResult.requiresAttention, isTrue);
        expect(highResult.percentile, greaterThan(97.0));
      });

      test('should provide appropriate interpretations', () {
        final result = WHODataService.analyzePercentile(6.0, 12, 'weight', 'male');
        expect(result.interpretation, contains('medical consultation'));
        
        final normalResult = WHODataService.analyzePercentile(9.6, 12, 'weight', 'male');
        expect(normalResult.interpretation, contains('average range'));
      });
    });

    group('Age Interpolation', () {
      test('should interpolate correctly between age points', () {
        // Test interpolation between 12 and 15 months (13.5 months)
        final percentile = WHODataService.calculateExactPercentile(9.8, 13.5, 'weight', 'male');
        expect(percentile.isFinite, isTrue);
        expect(percentile.isNaN, isFalse);
        expect(percentile, greaterThan(0));
        expect(percentile, lessThan(100));

        // Test that interpolated values are reasonable
        final percentile12 = WHODataService.calculateExactPercentile(9.8, 12, 'weight', 'male');
        final percentile15 = WHODataService.calculateExactPercentile(9.8, 15, 'weight', 'male');
        
        // Interpolated value should be between the two endpoints
        expect(percentile, greaterThanOrEqualTo(min(percentile12, percentile15)));
        expect(percentile, lessThanOrEqualTo(max(percentile12, percentile15)));
      });

      test('should handle edge ages correctly', () {
        // Test age 0 (birth)
        final percentileBirth = WHODataService.calculateExactPercentile(3.3, 0, 'weight', 'male');
        expect(percentileBirth, closeTo(50.0, 5.0));

        // Test age 60 (5 years)
        final percentile60 = WHODataService.calculateExactPercentile(19.2, 60, 'weight', 'male');
        expect(percentile60, closeTo(50.0, 5.0));

        // Test beyond range (should use last available data)
        final percentileBeyond = WHODataService.calculateExactPercentile(20.0, 70, 'weight', 'male');
        expect(percentileBeyond.isFinite, isTrue);
        expect(percentileBeyond.isNaN, isFalse);
      });
    });

    group('Gender Differences', () {
      test('should show appropriate gender differences', () {
        // Boys typically weigh more than girls at same age
        final boysWeight50 = WHODataService.getPercentileValue(50.0, 12, 'weight', 'male');
        final girlsWeight50 = WHODataService.getPercentileValue(50.0, 12, 'weight', 'female');
        expect(boysWeight50, greaterThan(girlsWeight50));

        // Boys typically taller than girls at same age
        final boysHeight50 = WHODataService.getPercentileValue(50.0, 24, 'height', 'male');
        final girlsHeight50 = WHODataService.getPercentileValue(50.0, 24, 'height', 'female');
        expect(boysHeight50, greaterThan(girlsHeight50));
      });
    });

    group('Error Handling', () {
      test('should handle invalid measurement types gracefully', () {
        expect(() => WHODataService.calculateExactPercentile(5.0, 12, 'invalid', 'male'),
            throwsArgumentError);
      });

      test('should handle extreme values gracefully', () {
        // Very high values should not crash
        final percentileHigh = WHODataService.calculateExactPercentile(1000.0, 12, 'weight', 'male');
        expect(percentileHigh.isFinite, isTrue);
        expect(percentileHigh.isNaN, isFalse);
        expect(percentileHigh, lessThanOrEqualTo(99.9));

        // Very low values should not crash
        final percentileLow = WHODataService.calculateExactPercentile(0.1, 12, 'weight', 'male');
        expect(percentileLow.isFinite, isTrue);
        expect(percentileLow.isNaN, isFalse);
        expect(percentileLow, greaterThanOrEqualTo(0.1));
      });
    });

    group('Mathematical Accuracy', () {
      test('should maintain mathematical consistency', () {
        // Test that percentile -> value -> percentile is consistent
        const originalPercentile = 75.0;
        final value = WHODataService.getPercentileValue(originalPercentile, 12, 'weight', 'male');
        final calculatedPercentile = WHODataService.calculateExactPercentile(value, 12, 'weight', 'male');
        
        expect(calculatedPercentile, closeTo(originalPercentile, 2.0));
      });

      test('should have monotonic percentile curves', () {
        // Higher values should have higher percentiles
        final percentile1 = WHODataService.calculateExactPercentile(8.0, 12, 'weight', 'male');
        final percentile2 = WHODataService.calculateExactPercentile(10.0, 12, 'weight', 'male');
        final percentile3 = WHODataService.calculateExactPercentile(12.0, 12, 'weight', 'male');
        
        expect(percentile2, greaterThan(percentile1));
        expect(percentile3, greaterThan(percentile2));
      });
    });

    group('Performance Tests', () {
      test('should calculate percentiles efficiently', () {
        final stopwatch = Stopwatch()..start();
        
        // Calculate 1000 percentiles
        for (int i = 0; i < 1000; i++) {
          WHODataService.calculateExactPercentile(9.6, 12, 'weight', 'male');
        }
        
        stopwatch.stop();
        
        // Should complete in reasonable time (less than 1 second)
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });
    });
  });
}