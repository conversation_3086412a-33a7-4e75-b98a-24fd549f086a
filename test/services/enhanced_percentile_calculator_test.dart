import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/enhanced_percentile_calculator.dart';
import '../../lib/services/who_data_service.dart';

void main() {
  group('EnhancedPercentileCalculator', () {
    
    group('LMS-based Percentile Calculations', () {
      test('should calculate accurate percentiles for known WHO reference values', () {
        // Test with known WHO reference values for weight
        // 6-month-old boy with median weight (7.9340 kg) should be ~50th percentile
        final result = EnhancedPercentileCalculator.calculateLMSPercentile(
          7.9340, // WHO median weight for 6-month boy
          6.0, // 6 months
          'weight',
          'male',
        );
        
        expect(result.percentile, closeTo(50.0, 2.0));
        expect(result.zScore, closeTo(0.0, 0.2));
        expect(result.category, equals('Average'));
        expect(result.requiresAttention, isFalse);
      });

      test('should calculate percentiles for girls weight accurately', () {
        // Test with known WHO reference values for weight
        // 12-month-old girl with median weight (8.9481 kg) should be ~50th percentile
        final result = EnhancedPercentileCalculator.calculateLMSPercentile(
          8.9481, // WHO median weight for 12-month girl
          12.0, // 12 months
          'weight',
          'female',
        );
        
        expect(result.percentile, closeTo(50.0, 2.0));
        expect(result.zScore, closeTo(0.0, 0.2));
        expect(result.category, equals('Average'));
      });

      test('should calculate percentiles for height measurements', () {
        // Test height calculation for 18-month boy
        final result = EnhancedPercentileCalculator.calculateLMSPercentile(
          80.7244, // WHO median height for 18-month boy
          18.0, // 18 months
          'height',
          'male',
        );
        
        expect(result.percentile, closeTo(50.0, 2.0));
        expect(result.category, equals('Average'));
      });

      test('should calculate percentiles for head circumference', () {
        // Test head circumference calculation for 6-month girl
        final result = EnhancedPercentileCalculator.calculateLMSPercentile(
          42.1011, // WHO median head circumference for 6-month girl
          6.0, // 6 months
          'head_circumference',
          'female',
        );
        
        expect(result.percentile, closeTo(50.0, 2.0));
        expect(result.category, equals('Average'));
      });

      test('should identify measurements requiring attention', () {
        // Test very low percentile (below 3rd percentile)
        final lowResult = EnhancedPercentileCalculator.calculateLMSPercentile(
          5.0, // Very low weight for 6-month boy
          6.0,
          'weight',
          'male',
        );
        
        expect(lowResult.percentile, lessThan(3.0));
        expect(lowResult.requiresAttention, isTrue);
        expect(lowResult.category, equals('Below Normal'));

        // Test very high percentile (above 97th percentile)
        final highResult = EnhancedPercentileCalculator.calculateLMSPercentile(
          12.0, // Very high weight for 6-month boy
          6.0,
          'weight',
          'male',
        );
        
        expect(highResult.percentile, greaterThan(97.0));
        expect(highResult.requiresAttention, isTrue);
        expect(highResult.category, equals('Above Normal'));
      });

      test('should handle invalid measurements gracefully', () {
        // Test with invalid age
        final invalidAgeResult = EnhancedPercentileCalculator.calculateLMSPercentile(
          8.0,
          -1.0, // Invalid negative age
          'weight',
          'male',
        );
        
        expect(invalidAgeResult.category, equals('Error'));
        expect(invalidAgeResult.requiresAttention, isTrue);

        // Test with invalid measurement value
        final invalidValueResult = EnhancedPercentileCalculator.calculateLMSPercentile(
          100.0, // Impossible weight for baby
          6.0,
          'weight',
          'male',
        );
        
        expect(invalidValueResult.category, equals('Error'));
        expect(invalidValueResult.requiresAttention, isTrue);
      });
    });

    group('Z-Score Calculations', () {
      test('should calculate accurate Z-scores', () {
        // Test Z-score for median value (should be ~0)
        final zScore = EnhancedPercentileCalculator.calculateZScore(
          7.9340, // WHO median weight for 6-month boy
          6.0,
          'weight',
          'male',
        );
        
        expect(zScore, closeTo(0.0, 0.2));
      });

      test('should calculate Z-scores for different measurement types', () {
        // Test height Z-score
        final heightZScore = EnhancedPercentileCalculator.calculateZScore(
          80.7244, // WHO median height for 18-month boy
          18.0,
          'height',
          'male',
        );
        
        expect(heightZScore, closeTo(0.0, 0.2));

        // Test head circumference Z-score
        final headZScore = EnhancedPercentileCalculator.calculateZScore(
          42.1011, // WHO median head circumference for 6-month girl
          6.0,
          'head_circumference',
          'female',
        );
        
        expect(headZScore, closeTo(0.0, 0.2));
      });

      test('should handle invalid Z-score calculations', () {
        final zScore = EnhancedPercentileCalculator.calculateZScore(
          -1.0, // Invalid weight
          6.0,
          'weight',
          'male',
        );
        
        expect(zScore, equals(0.0)); // Should return 0.0 for invalid calculations
      });
    });

    group('Percentile Trend Analysis', () {
      test('should calculate percentile trends for multiple measurements', () {
        final measurements = [
          MeasurementData(
            value: 3.5,
            ageInMonths: 0.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 4.5,
            ageInMonths: 1.0,
            date: DateTime(2024, 2, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 5.6,
            ageInMonths: 2.0,
            date: DateTime(2024, 3, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        final trends = EnhancedPercentileCalculator.calculatePercentileTrends(measurements);
        
        expect(trends.length, equals(3));
        expect(trends[0].ageInMonths, equals(0.0));
        expect(trends[1].ageInMonths, equals(1.0));
        expect(trends[2].ageInMonths, equals(2.0));
        
        // Verify trends are sorted by date
        expect(trends[0].date.isBefore(trends[1].date), isTrue);
        expect(trends[1].date.isBefore(trends[2].date), isTrue);
        
        // Verify percentiles are calculated
        expect(trends[0].percentile, greaterThan(0.0));
        expect(trends[1].percentile, greaterThan(0.0));
        expect(trends[2].percentile, greaterThan(0.0));
      });

      test('should handle empty measurement list', () {
        final trends = EnhancedPercentileCalculator.calculatePercentileTrends([]);
        expect(trends.isEmpty, isTrue);
      });

      test('should skip invalid measurements in trend calculation', () {
        final measurements = [
          MeasurementData(
            value: 3.5,
            ageInMonths: 0.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: -1.0, // Invalid weight
            ageInMonths: 1.0,
            date: DateTime(2024, 2, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 5.6,
            ageInMonths: 2.0,
            date: DateTime(2024, 3, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        final trends = EnhancedPercentileCalculator.calculatePercentileTrends(measurements);
        
        // Should skip the invalid measurement
        expect(trends.length, equals(2));
        expect(trends[0].value, equals(3.5));
        expect(trends[1].value, equals(5.6));
      });
    });

    group('Growth Velocity Calculations', () {
      test('should calculate growth velocity between measurements', () {
        final measurements = [
          MeasurementData(
            value: 7.0,
            ageInMonths: 6.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 8.0,
            ageInMonths: 7.0,
            date: DateTime(2024, 2, 1), // 31 days later
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
        
        expect(velocity, isNotNull);
        expect(velocity!.velocityPerMonth, greaterThan(0.0));
        expect(velocity.timePeriod.inDays, equals(31));
        expect(velocity.velocityCategory, isNotEmpty);
        expect(velocity.interpretation, isNotEmpty);
      });

      test('should return null for insufficient measurements', () {
        final measurements = [
          MeasurementData(
            value: 7.0,
            ageInMonths: 6.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
        expect(velocity, isNull);
      });

      test('should identify concerning growth velocities', () {
        // Test very slow growth
        final slowGrowthMeasurements = [
          MeasurementData(
            value: 7.0,
            ageInMonths: 6.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 7.01, // Minimal growth
            ageInMonths: 9.0,
            date: DateTime(2024, 4, 1), // 3 months later
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        final slowVelocity = EnhancedPercentileCalculator.calculateGrowthVelocity(slowGrowthMeasurements);
        expect(slowVelocity, isNotNull);
        expect(slowVelocity!.velocityPerMonth, lessThan(0.1));
        expect(slowVelocity.requiresAttention, isTrue);
      });

      test('should handle same-date measurements', () {
        final measurements = [
          MeasurementData(
            value: 7.0,
            ageInMonths: 6.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 8.0,
            ageInMonths: 6.0,
            date: DateTime(2024, 1, 1), // Same date
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
        expect(velocity, isNull); // Should return null for zero time difference
      });
    });

    group('Percentile Crossing Analysis', () {
      test('should detect significant percentile crossing', () {
        final trends = [
          PercentileTrend(
            date: DateTime(2024, 1, 1),
            ageInMonths: 6.0,
            value: 7.0,
            percentile: 25.0,
            zScore: -0.67,
            measurementType: 'weight',
          ),
          PercentileTrend(
            date: DateTime(2024, 2, 1),
            ageInMonths: 7.0,
            value: 8.5,
            percentile: 75.0, // 50 percentile point increase
            zScore: 0.67,
            measurementType: 'weight',
          ),
        ];

        final analysis = EnhancedPercentileCalculator.analyzePercentileCrossing(trends);
        
        expect(analysis['hasCrossing'], isTrue);
        expect(analysis['crossingMagnitude'], equals(50.0));
        expect(analysis['crossingDirection'], equals('upward'));
        expect(analysis['requiresAttention'], isTrue);
        expect(analysis['interpretation'], contains('Significant percentile crossing'));
      });

      test('should detect stable growth patterns', () {
        final trends = [
          PercentileTrend(
            date: DateTime(2024, 1, 1),
            ageInMonths: 6.0,
            value: 7.0,
            percentile: 50.0,
            zScore: 0.0,
            measurementType: 'weight',
          ),
          PercentileTrend(
            date: DateTime(2024, 2, 1),
            ageInMonths: 7.0,
            value: 7.5,
            percentile: 52.0, // Small change
            zScore: 0.05,
            measurementType: 'weight',
          ),
        ];

        final analysis = EnhancedPercentileCalculator.analyzePercentileCrossing(trends);
        
        expect(analysis['hasCrossing'], isFalse);
        expect(analysis['crossingDirection'], equals('stable'));
        expect(analysis['requiresAttention'], isFalse);
        expect(analysis['interpretation'], contains('consistent percentile curve'));
      });

      test('should handle insufficient data', () {
        final trends = [
          PercentileTrend(
            date: DateTime(2024, 1, 1),
            ageInMonths: 6.0,
            value: 7.0,
            percentile: 50.0,
            zScore: 0.0,
            measurementType: 'weight',
          ),
        ];

        final analysis = EnhancedPercentileCalculator.analyzePercentileCrossing(trends);
        
        expect(analysis['hasCrossing'], isFalse);
        expect(analysis['interpretation'], contains('Insufficient data'));
      });
    });

    group('Growth Summary Generation', () {
      test('should generate comprehensive growth summary', () {
        final trends = [
          PercentileTrend(
            date: DateTime(2024, 1, 1),
            ageInMonths: 6.0,
            value: 7.0,
            percentile: 40.0,
            zScore: -0.25,
            measurementType: 'weight',
          ),
          PercentileTrend(
            date: DateTime(2024, 2, 1),
            ageInMonths: 7.0,
            value: 7.5,
            percentile: 50.0, // 10 percentile point increase for clear upward trend
            zScore: 0.0,
            measurementType: 'weight',
          ),
        ];

        final velocity = GrowthVelocityAnalysis(
          velocityPerMonth: 0.5,
          velocityPercentile: 60.0,
          interpretation: 'Normal growth velocity',
          isNormal: true,
          timePeriod: Duration(days: 31),
          velocityCategory: 'Normal Growth',
          requiresAttention: false,
        );

        final summary = EnhancedPercentileCalculator.generateGrowthSummary(trends, velocity);
        
        expect(summary['currentPercentile'], equals(50.0));
        expect(summary['trend'], equals('upward'));
        expect(summary['velocity'], equals('Normal Growth'));
        expect(summary['overallAssessment'], isNotEmpty);
        expect(summary['recommendations'], isA<List<String>>());
        expect((summary['recommendations'] as List).isNotEmpty, isTrue);
      });

      test('should handle empty trends', () {
        final summary = EnhancedPercentileCalculator.generateGrowthSummary([], null);
        
        expect(summary['summary'], contains('No measurement data'));
        expect(summary['overallAssessment'], equals('Insufficient data'));
        expect(summary['recommendations'], contains('Add measurements to track growth patterns'));
      });

      test('should identify concerning patterns', () {
        final trends = [
          PercentileTrend(
            date: DateTime(2024, 1, 1),
            ageInMonths: 6.0,
            value: 5.0,
            percentile: 1.0, // Very low percentile
            zScore: -2.33,
            measurementType: 'weight',
          ),
        ];

        final summary = EnhancedPercentileCalculator.generateGrowthSummary(trends, null);
        
        expect(summary['overallAssessment'], equals('Requires medical attention'));
        expect(summary['recommendations'], contains('Consult healthcare provider about current percentile'));
      });
    });

    group('Utility Functions', () {
      test('should validate measurement data correctly', () {
        final validMeasurement = MeasurementData(
          value: 7.0,
          ageInMonths: 6.0,
          date: DateTime(2024, 1, 1),
          measurementType: 'weight',
          gender: 'male',
        );

        final invalidMeasurement = MeasurementData(
          value: -1.0, // Invalid weight
          ageInMonths: 6.0,
          date: DateTime(2024, 1, 1),
          measurementType: 'weight',
          gender: 'male',
        );

        expect(EnhancedPercentileCalculator.validateMeasurementData(validMeasurement), isTrue);
        expect(EnhancedPercentileCalculator.validateMeasurementData(invalidMeasurement), isFalse);
      });

      test('should calculate age in months correctly', () {
        final birthDate = DateTime(2024, 1, 1);
        final measurementDate = DateTime(2024, 7, 1); // 6 months later
        
        final ageInMonths = EnhancedPercentileCalculator.calculateAgeInMonths(birthDate, measurementDate);
        
        expect(ageInMonths, closeTo(6.0, 0.5)); // Allow some tolerance for month length variations
      });

      test('should handle edge cases in age calculation', () {
        final birthDate = DateTime(2024, 1, 1);
        final sameDate = DateTime(2024, 1, 1);
        
        final ageInMonths = EnhancedPercentileCalculator.calculateAgeInMonths(birthDate, sameDate);
        
        expect(ageInMonths, equals(0.0));
      });
    });

    group('Integration Tests', () {
      test('should perform end-to-end calculation workflow', () {
        // Create a realistic measurement sequence
        final measurements = [
          MeasurementData(
            value: 3.3,
            ageInMonths: 0.0,
            date: DateTime(2024, 1, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 4.5,
            ageInMonths: 1.0,
            date: DateTime(2024, 2, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 5.6,
            ageInMonths: 2.0,
            date: DateTime(2024, 3, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
          MeasurementData(
            value: 6.4,
            ageInMonths: 3.0,
            date: DateTime(2024, 4, 1),
            measurementType: 'weight',
            gender: 'male',
          ),
        ];

        // Calculate trends
        final trends = EnhancedPercentileCalculator.calculatePercentileTrends(measurements);
        expect(trends.length, equals(4));

        // Calculate velocity
        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
        expect(velocity, isNotNull);

        // Analyze crossing
        final crossingAnalysis = EnhancedPercentileCalculator.analyzePercentileCrossing(trends);
        expect(crossingAnalysis, isNotNull);

        // Generate summary
        final summary = EnhancedPercentileCalculator.generateGrowthSummary(trends, velocity);
        expect(summary['overallAssessment'], isNotEmpty);
        expect(summary['recommendations'], isA<List<String>>());

        // Verify all calculations completed successfully
        expect(trends.every((t) => t.percentile > 0), isTrue);
        expect(velocity!.velocityPerMonth, isA<double>());
        expect(summary['currentPercentile'], isA<double>());
      });
    });
  });
}