import 'package:flutter_test/flutter_test.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:io';
import 'dart:async';

import '../../lib/services/error_handling_service.dart';

void main() {
  group('ErrorHandlingService', () {
    late ErrorHandlingService errorService;

    setUp(() {
      errorService = ErrorHandlingService();
    });

    tearDown(() {
      errorService.dispose();
    });

    group('Error Classification', () {
      test('should classify network errors correctly', () {
        final error = SocketException('Network unreachable');
        final result = errorService.handleError(error, operation: 'test');

        expect(result.type, AccountProfileErrorType.networkError);
        expect(result.title, 'Connection Failed');
        expect(result.isRetryable, true);
        expect(result.operation, 'test');
      });

      test('should classify Postgrest errors correctly', () {
        final error = PostgrestException(
          message: 'Unique constraint violation',
          code: '23505',
        );
        final result = errorService.handleError(error, operation: 'invite');

        expect(result.type, AccountProfileErrorType.validationError);
        expect(result.title, 'Duplicate Entry');
        expect(result.isRetryable, false);
      });

      test('should classify auth errors correctly', () {
        final error = AuthException(
          'Invalid credentials',
          statusCode: '401',
        );
        final result = errorService.handleError(error, operation: 'login');

        expect(result.type, AccountProfileErrorType.authError);
        expect(result.title, 'Authentication Required');
        expect(result.isRetryable, false);
      });

      test('should classify timeout errors correctly', () {
        final error = TimeoutException('Request timeout', const Duration(seconds: 30));
        final result = errorService.handleError(error, operation: 'load');

        expect(result.type, AccountProfileErrorType.timeoutError);
        expect(result.title, 'Request Timeout');
        expect(result.isRetryable, true);
      });

      test('should classify unknown errors correctly', () {
        final error = Exception('Unknown error');
        final result = errorService.handleError(error, operation: 'unknown');

        expect(result.type, AccountProfileErrorType.unknown);
        expect(result.title, 'Something Went Wrong');
        expect(result.isRetryable, true);
      });
    });

    group('Operation-Specific Messages', () {
      test('should provide operation-specific error messages', () {
        final networkMessage = errorService.getOperationErrorMessage(
          'loadUserProfile',
          AccountProfileErrorType.networkError,
        );
        expect(networkMessage, contains('profile'));
        expect(networkMessage, contains('connection'));

        final inviteMessage = errorService.getOperationErrorMessage(
          'inviteFamilyMember',
          AccountProfileErrorType.validationError,
        );
        expect(inviteMessage, contains('email'));
        expect(inviteMessage, contains('member'));
      });

      test('should provide fallback message for unknown operations', () {
        final message = errorService.getOperationErrorMessage(
          'unknownOperation',
          AccountProfileErrorType.networkError,
        );
        expect(message, 'An error occurred while performing this action.');
      });
    });

    group('Retry Logic', () {
      test('should retry retryable operations', () async {
        int attempts = 0;
        
        try {
          await errorService.withRetry(
            () async {
              attempts++;
              if (attempts < 3) {
                throw SocketException('Network error');
              }
              return 'success';
            },
            maxRetries: 3,
            operationName: 'test',
          );
        } catch (e) {
          // Should not reach here
          fail('Should have succeeded after retries');
        }

        expect(attempts, 3);
      });

      test('should not retry non-retryable operations', () async {
        int attempts = 0;
        
        try {
          await errorService.withRetry(
            () async {
              attempts++;
              throw const AuthException('Auth error', statusCode: '401');
            },
            maxRetries: 3,
            operationName: 'test',
          );
          fail('Should have thrown an error');
        } catch (e) {
          expect(e, isA<AuthException>());
        }

        expect(attempts, 1); // Should not retry auth errors
      });

      test('should respect max retries', () async {
        int attempts = 0;
        
        try {
          await errorService.withRetry(
            () async {
              attempts++;
              throw SocketException('Network error');
            },
            maxRetries: 2,
            operationName: 'test',
          );
          fail('Should have thrown an error');
        } catch (e) {
          expect(e, isA<Exception>());
        }

        expect(attempts, 2);
      });
    });

    group('AccountProfileError', () {
      test('should provide correct icon names for error types', () {
        final networkError = AccountProfileError(
          type: AccountProfileErrorType.networkError,
          title: 'Network Error',
          message: 'No connection',
          isRetryable: true,
        );
        expect(networkError.iconName, 'wifi_off');

        final authError = AccountProfileError(
          type: AccountProfileErrorType.authError,
          title: 'Auth Error',
          message: 'Not authenticated',
          isRetryable: false,
        );
        expect(authError.iconName, 'lock');
      });

      test('should identify warning vs error types', () {
        final networkError = AccountProfileError(
          type: AccountProfileErrorType.networkError,
          title: 'Network Error',
          message: 'No connection',
          isRetryable: true,
        );
        expect(networkError.isWarning, true);

        final serverError = AccountProfileError(
          type: AccountProfileErrorType.serverError,
          title: 'Server Error',
          message: 'Server down',
          isRetryable: true,
        );
        expect(serverError.isWarning, false);
      });

      test('should provide string representation', () {
        final error = AccountProfileError(
          type: AccountProfileErrorType.validationError,
          title: 'Validation Error',
          message: 'Invalid input',
          isRetryable: false,
          operation: 'testOp',
        );

        final errorString = error.toString();
        expect(errorString, contains('validationError'));
        expect(errorString, contains('Validation Error'));
        expect(errorString, contains('testOp'));
      });
    });
  });
}