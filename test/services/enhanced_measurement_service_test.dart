import 'package:flutter_test/flutter_test.dart';
import '../../lib/models/measurement.dart';
import '../../lib/models/baby_profile.dart';
import '../../lib/services/measurement_validation_service.dart';
import '../../lib/services/enhanced_percentile_calculator.dart';

void main() {
  group('Enhanced Measurement System Tests', () {
    late BabyProfile testBabyProfile;

    setUp(() {
      testBabyProfile = BabyProfile(
        id: 'test-baby-123',
        name: 'Test Baby',
        birthDate: DateTime.now().subtract(const Duration(days: 180)), // 6 months old
        gender: 'female',
        birthWeight: 3.2,
        birthHeight: 50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    group('Measurement Model Tests', () {
      test('should create measurement with all fields', () {
        final measurement = Measurement(
          id: 'test-measurement-1',
          babyId: testBabyProfile.id,
          measurementType: 'weight',
          value: 7.5,
          unit: 'kg',
          measuredAt: DateTime.now(),
          ageInMonths: 6.0,
          notes: 'Test measurement',
          percentile: 50.0,
          zScore: 0.0,
          flaggedForReview: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(measurement.id, equals('test-measurement-1'));
        expect(measurement.measurementType, equals('weight'));
        expect(measurement.value, equals(7.5));
        expect(measurement.percentile, equals(50.0));
        expect(measurement.requiresAttention, isFalse);
      });

      test('should detect measurements requiring attention', () {
        final lowPercentileMeasurement = Measurement(
          id: 'test-measurement-2',
          babyId: testBabyProfile.id,
          measurementType: 'weight',
          value: 4.0,
          unit: 'kg',
          measuredAt: DateTime.now(),
          ageInMonths: 6.0,
          percentile: 2.0, // Below 3rd percentile
          zScore: -2.5,
          flaggedForReview: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(lowPercentileMeasurement.requiresAttention, isTrue);
      });

      test('should format display values correctly', () {
        final weightMeasurement = Measurement(
          id: 'test-measurement-3',
          babyId: testBabyProfile.id,
          measurementType: 'weight',
          value: 7.55,
          unit: 'kg',
          measuredAt: DateTime.now(),
          ageInMonths: 6.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(weightMeasurement.displayValue, equals('7.55 kg'));

        final heightMeasurement = Measurement(
          id: 'test-measurement-4',
          babyId: testBabyProfile.id,
          measurementType: 'height',
          value: 67.8,
          unit: 'cm',
          measuredAt: DateTime.now(),
          ageInMonths: 6.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        expect(heightMeasurement.displayValue, equals('67.8 cm'));
      });

      test('should convert to MeasurementData correctly', () {
        final measurement = Measurement(
          id: 'test-measurement-5',
          babyId: testBabyProfile.id,
          measurementType: 'weight',
          value: 7.5,
          unit: 'kg',
          measuredAt: DateTime.now(),
          ageInMonths: 6.0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final measurementData = measurement.toMeasurementData('girls');

        expect(measurementData.value, equals(7.5));
        expect(measurementData.ageInMonths, equals(6.0));
        expect(measurementData.measurementType, equals('weight'));
        expect(measurementData.gender, equals('girls'));
      });
    });

    group('Measurement Validation Tests', () {
      test('should validate normal measurements', () {
        final validation = MeasurementValidationService.validateMeasurement(
          7.5, // Normal weight for 6 months
          6.0, // 6 months old
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        expect(validation.isValid, isTrue);
        expect(validation.errors, isEmpty);
      });

      test('should detect invalid measurements', () {
        final validation = MeasurementValidationService.validateMeasurement(
          -1.0, // Invalid negative weight
          6.0,
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        expect(validation.isValid, isFalse);
        expect(validation.errors, isNotEmpty);
        expect(validation.errors.first, contains('positive'));
      });

      test('should detect concerning measurements', () {
        final validation = MeasurementValidationService.validateMeasurement(
          3.0, // Very low weight for 6 months
          6.0,
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        expect(validation.isValid, isTrue); // Valid but concerning
        expect(validation.warnings, isNotEmpty);
        expect(validation.requiresAttention, isTrue);
      });

      test('should validate age ranges', () {
        final validation = MeasurementValidationService.validateMeasurement(
          7.5,
          70.0, // Invalid age > 60 months
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        expect(validation.isValid, isFalse);
        expect(validation.errors, contains('Age must be between 0 and 60 months'));
      });

      test('should validate measurement types', () {
        final validation = MeasurementValidationService.validateMeasurement(
          7.5,
          6.0,
          'invalid_type', // Invalid measurement type
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        expect(validation.isValid, isFalse);
        expect(validation.errors, contains('Invalid measurement type'));
      });

      test('should validate future dates', () {
        final futureDate = DateTime.now().add(const Duration(days: 1));
        final validation = MeasurementValidationService.validateMeasurement(
          7.5,
          6.0,
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: futureDate,
        );

        expect(validation.isValid, isFalse);
        expect(validation.errors, contains('Measurement date cannot be in the future'));
      });

      test('should validate dates before birth', () {
        final beforeBirthDate = testBabyProfile.birthDate.subtract(const Duration(days: 1));
        final validation = MeasurementValidationService.validateMeasurement(
          7.5,
          6.0,
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: beforeBirthDate,
        );

        expect(validation.isValid, isFalse);
        expect(validation.errors, contains('Measurement date cannot be before birth date'));
      });

      test('should flag measurements for review based on validation', () {
        final concerningValidation = MeasurementValidationService.validateMeasurement(
          2.5, // Very low weight
          6.0,
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        final shouldFlag = MeasurementValidationService.shouldFlagForReview(concerningValidation);
        expect(shouldFlag, isTrue);

        final normalValidation = MeasurementValidationService.validateMeasurement(
          7.5, // Normal weight
          6.0,
          'weight',
          'girls',
          testBabyProfile,
          measurementDate: DateTime.now(),
        );

        final shouldNotFlag = MeasurementValidationService.shouldFlagForReview(normalValidation);
        expect(shouldNotFlag, isFalse);
      });
    });

    group('Enhanced Percentile Calculator Tests', () {
      test('should validate measurement data', () {
        final validMeasurement = MeasurementData(
          value: 7.5,
          ageInMonths: 6.0,
          date: DateTime.now(),
          measurementType: 'weight',
          gender: 'girls',
        );

        expect(EnhancedPercentileCalculator.validateMeasurementData(validMeasurement), isTrue);

        final invalidMeasurement = MeasurementData(
          value: -1.0, // Invalid negative value
          ageInMonths: 6.0,
          date: DateTime.now(),
          measurementType: 'weight',
          gender: 'girls',
        );

        expect(EnhancedPercentileCalculator.validateMeasurementData(invalidMeasurement), isFalse);
      });

      test('should calculate percentile trends', () {
        final measurements = [
          MeasurementData(
            value: 3.2,
            ageInMonths: 0.0,
            date: testBabyProfile.birthDate,
            measurementType: 'weight',
            gender: 'girls',
          ),
          MeasurementData(
            value: 5.5,
            ageInMonths: 3.0,
            date: testBabyProfile.birthDate.add(const Duration(days: 90)),
            measurementType: 'weight',
            gender: 'girls',
          ),
          MeasurementData(
            value: 7.5,
            ageInMonths: 6.0,
            date: testBabyProfile.birthDate.add(const Duration(days: 180)),
            measurementType: 'weight',
            gender: 'girls',
          ),
        ];

        final trends = EnhancedPercentileCalculator.calculatePercentileTrends(measurements);

        expect(trends, hasLength(3));
        expect(trends.first.ageInMonths, equals(0.0));
        expect(trends.last.ageInMonths, equals(6.0));
        
        // All trends should have calculated percentiles
        for (final trend in trends) {
          expect(trend.percentile, isNotNull);
          expect(trend.zScore, isNotNull);
        }
      });

      test('should calculate growth velocity', () {
        final measurements = [
          MeasurementData(
            value: 5.5,
            ageInMonths: 3.0,
            date: testBabyProfile.birthDate.add(const Duration(days: 90)),
            measurementType: 'weight',
            gender: 'girls',
          ),
          MeasurementData(
            value: 7.5,
            ageInMonths: 6.0,
            date: testBabyProfile.birthDate.add(const Duration(days: 180)),
            measurementType: 'weight',
            gender: 'girls',
          ),
        ];

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);

        expect(velocity, isNotNull);
        expect(velocity!.velocityPerMonth, greaterThan(0));
        expect(velocity.timePeriod.inDays, greaterThan(0));
        expect(velocity.interpretation, isNotEmpty);
      });

      test('should handle empty measurement lists', () {
        final trends = EnhancedPercentileCalculator.calculatePercentileTrends([]);
        expect(trends, isEmpty);

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity([]);
        expect(velocity, isNull);
      });

      test('should handle single measurement for velocity', () {
        final measurements = [
          MeasurementData(
            value: 7.5,
            ageInMonths: 6.0,
            date: DateTime.now(),
            measurementType: 'weight',
            gender: 'girls',
          ),
        ];

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
        expect(velocity, isNull);
      });
    });

    group('Biological Range Validation Tests', () {
      test('should validate weight ranges', () {
        // Test minimum weight validation
        final tooLowValidation = MeasurementValidationService.validateMeasurement(
          0.3, // Below minimum viable weight
          0.0,
          'weight',
          'girls',
          testBabyProfile,
        );
        expect(tooLowValidation.errors, contains('Weight too low - minimum viable weight is 0.5kg'));

        // Test maximum weight validation
        final tooHighValidation = MeasurementValidationService.validateMeasurement(
          55.0, // Above maximum expected weight
          6.0,
          'weight',
          'girls',
          testBabyProfile,
        );
        expect(tooHighValidation.errors, contains('Weight too high - maximum expected weight is 50kg for this age range'));
      });

      test('should validate height ranges', () {
        // Test minimum height validation
        final tooShortValidation = MeasurementValidationService.validateMeasurement(
          20.0, // Below minimum expected height
          6.0,
          'height',
          'girls',
          testBabyProfile,
        );
        expect(tooShortValidation.errors, contains('Height/Length too low - minimum expected is 25cm'));

        // Test maximum height validation
        final tooTallValidation = MeasurementValidationService.validateMeasurement(
          160.0, // Above maximum expected height
          6.0,
          'height',
          'girls',
          testBabyProfile,
        );
        expect(tooTallValidation.errors, contains('Height/Length too high - maximum expected is 150cm for this age range'));
      });

      test('should validate head circumference ranges', () {
        // Test minimum HC validation
        final tooSmallValidation = MeasurementValidationService.validateMeasurement(
          20.0, // Below minimum expected HC
          6.0,
          'head_circumference',
          'girls',
          testBabyProfile,
        );
        expect(tooSmallValidation.errors, contains('Head circumference too low - minimum expected is 25cm'));

        // Test maximum HC validation
        final tooLargeValidation = MeasurementValidationService.validateMeasurement(
          70.0, // Above maximum expected HC
          6.0,
          'head_circumference',
          'girls',
          testBabyProfile,
        );
        expect(tooLargeValidation.errors, contains('Head circumference too high - maximum expected is 65cm'));
      });
    });

    group('JSON Serialization Tests', () {
      test('should serialize and deserialize measurement correctly', () {
        final originalMeasurement = Measurement(
          id: 'test-measurement-json',
          babyId: testBabyProfile.id,
          measurementType: 'weight',
          value: 7.5,
          unit: 'kg',
          measuredAt: DateTime.now(),
          ageInMonths: 6.0,
          notes: 'Test measurement',
          percentile: 50.0,
          zScore: 0.0,
          flaggedForReview: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          metadata: {'test': 'value'},
        );

        final json = originalMeasurement.toJson();
        final deserializedMeasurement = Measurement.fromJson(json);

        expect(deserializedMeasurement.id, equals(originalMeasurement.id));
        expect(deserializedMeasurement.measurementType, equals(originalMeasurement.measurementType));
        expect(deserializedMeasurement.value, equals(originalMeasurement.value));
        expect(deserializedMeasurement.percentile, equals(originalMeasurement.percentile));
        expect(deserializedMeasurement.flaggedForReview, equals(originalMeasurement.flaggedForReview));
        expect(deserializedMeasurement.metadata, equals(originalMeasurement.metadata));
      });

      test('should handle null values in JSON', () {
        final jsonWithNulls = {
          'id': 'test-id',
          'baby_id': 'test-baby-id',
          'measurement_type': 'weight',
          'value': 7.5,
          'unit': 'kg',
          'measured_at': DateTime.now().toIso8601String(),
          'age_in_months': 6.0,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
          // Null values
          'notes': null,
          'percentile': null,
          'z_score': null,
          'velocity_data': null,
          'percentile_analysis': null,
          'validation_results': null,
          'metadata': null,
        };

        final measurement = Measurement.fromJson(jsonWithNulls);

        expect(measurement.notes, isNull);
        expect(measurement.percentile, isNull);
        expect(measurement.zScore, isNull);
        expect(measurement.velocityFromPrevious, isNull);
        expect(measurement.percentileAnalysis, isNull);
        expect(measurement.validationResults, isNull);
        expect(measurement.metadata, isNull);
      });
    });
  });
}