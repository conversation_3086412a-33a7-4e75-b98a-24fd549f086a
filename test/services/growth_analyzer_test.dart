import 'package:flutter_test/flutter_test.dart';
import '../../lib/services/growth_analyzer.dart';
import '../../lib/services/enhanced_percentile_calculator.dart';
import '../../lib/models/baby_profile.dart';

void main() {
  group('GrowthAnalyzer Tests', () {
    late BabyProfile testBaby;
    late List<MeasurementData> testMeasurements;

    setUp(() {
      // Create test baby profile
      testBaby = BabyProfile(
        id: 'test-baby-1',
        name: 'Test Baby',
        birthDate: DateTime.now().subtract(const Duration(days: 180)), // 6 months old
        gender: 'male',
        birthWeight: 3.2,
        birthHeight: 50.0,
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        updatedAt: DateTime.now(),
      );

      // Create test measurements (normal growth pattern)
      testMeasurements = [
        MeasurementData(
          value: 3.2,
          ageInMonths: 0,
          date: testBaby.birthDate,
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 4.5,
          ageInMonths: 1,
          date: testBaby.birthDate.add(const Duration(days: 30)),
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 5.8,
          ageInMonths: 2,
          date: testBaby.birthDate.add(const Duration(days: 60)),
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 6.8,
          ageInMonths: 3,
          date: testBaby.birthDate.add(const Duration(days: 90)),
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 7.5,
          ageInMonths: 4,
          date: testBaby.birthDate.add(const Duration(days: 120)),
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 8.1,
          ageInMonths: 5,
          date: testBaby.birthDate.add(const Duration(days: 150)),
          measurementType: 'weight',
          gender: 'male',
        ),
      ];
    });

    test('should analyze normal growth pattern without alerts', () {
      final analysis = GrowthAnalyzer.analyzeGrowthPattern(testMeasurements, testBaby);

      expect(analysis.trends.length, equals(6));
      // The assessment might show concerns due to percentile calculations, so let's check for non-critical
      expect(analysis.alerts.where((a) => a.severity == 'critical').length, equals(0));
      expect(analysis.growthSummary, isNotEmpty);
      expect(analysis.recommendations, isNotEmpty);
      expect(analysis.overallAssessment, isNotEmpty);
    });

    test('should detect percentile crossing alerts', () {
      // Create measurements with significant percentile drop
      final crossingMeasurements = [
        MeasurementData(
          value: 8.0, // ~50th percentile
          ageInMonths: 3,
          date: testBaby.birthDate.add(const Duration(days: 90)),
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 6.0, // Much lower percentile
          ageInMonths: 4,
          date: testBaby.birthDate.add(const Duration(days: 120)),
          measurementType: 'weight',
          gender: 'male',
        ),
      ];

      final analysis = GrowthAnalyzer.analyzeGrowthPattern(crossingMeasurements, testBaby);

      final crossingAlerts = analysis.alerts.where((a) => a.type == GrowthAlertType.percentileCrossing);
      expect(crossingAlerts.length, greaterThan(0));
    });

    test('should detect below normal range alerts', () {
      // Create measurements below 3rd percentile
      final lowMeasurements = [
        MeasurementData(
          value: 2.0, // Very low weight
          ageInMonths: 3,
          date: testBaby.birthDate.add(const Duration(days: 90)),
          measurementType: 'weight',
          gender: 'male',
        ),
      ];

      final analysis = GrowthAnalyzer.analyzeGrowthPattern(lowMeasurements, testBaby);

      final belowNormalAlerts = analysis.alerts.where((a) => a.type == GrowthAlertType.belowNormalRange);
      expect(belowNormalAlerts.length, equals(1));
      expect(belowNormalAlerts.first.severity, equals('high'));
    });

    test('should detect above normal range alerts', () {
      // Create measurements above 97th percentile
      final highMeasurements = [
        MeasurementData(
          value: 12.0, // Very high weight for 3 months
          ageInMonths: 3,
          date: testBaby.birthDate.add(const Duration(days: 90)),
          measurementType: 'weight',
          gender: 'male',
        ),
      ];

      final analysis = GrowthAnalyzer.analyzeGrowthPattern(highMeasurements, testBaby);

      final aboveNormalAlerts = analysis.alerts.where((a) => a.type == GrowthAlertType.aboveNormalRange);
      expect(aboveNormalAlerts.length, equals(1));
      expect(aboveNormalAlerts.first.severity, equals('medium'));
    });

    test('should detect missing measurements alert', () {
      final analysis = GrowthAnalyzer.analyzeGrowthPattern([], testBaby);

      final missingAlerts = analysis.alerts.where((a) => a.type == GrowthAlertType.missingMeasurements);
      expect(missingAlerts.length, equals(1));
      expect(missingAlerts.first.title, equals('No Growth Data'));
    });

    test('should generate growth summary correctly', () {
      final trends = [
        PercentileTrend(
          date: DateTime.now(),
          ageInMonths: 6,
          value: 8.0,
          percentile: 50.0,
          zScore: 0.0,
          measurementType: 'weight',
        ),
      ];

      final summary = GrowthAnalyzer.generateGrowthSummary(
        trends,
        null,
        {'crossingDirection': 'stable', 'totalChange': 2.0},
      );

      expect(summary, contains('average range'));
      expect(summary, contains('50.0th percentile'));
      expect(summary, isNotEmpty);
    });

    test('should provide percentile interpretation with color coding', () {
      // Test different percentile ranges
      final interpretations = [
        GrowthAnalyzer.getPercentileInterpretation(1.0), // Below normal
        GrowthAnalyzer.getPercentileInterpretation(5.0), // Low normal
        GrowthAnalyzer.getPercentileInterpretation(15.0), // Lower average
        GrowthAnalyzer.getPercentileInterpretation(50.0), // Average
        GrowthAnalyzer.getPercentileInterpretation(85.0), // Higher average
        GrowthAnalyzer.getPercentileInterpretation(95.0), // High normal
        GrowthAnalyzer.getPercentileInterpretation(99.0), // Above normal
      ];

      for (final interpretation in interpretations) {
        expect(interpretation['category'], isNotEmpty);
        expect(interpretation['interpretation'], isNotEmpty);
        expect(interpretation['colorCode'], startsWith('#'));
        expect(interpretation['textColor'], startsWith('#'));
        expect(interpretation.containsKey('requiresAttention'), isTrue);
      }

      // Check specific cases
      expect(interpretations[0]['requiresAttention'], isTrue); // Below normal should require attention
      expect(interpretations[3]['requiresAttention'], isFalse); // Average should not require attention
      expect(interpretations[6]['requiresAttention'], isTrue); // Above normal should require attention
    });

    test('should generate alert summary correctly', () {
      final alerts = [
        GrowthAlert(
          type: GrowthAlertType.belowNormalRange,
          title: 'Test Alert 1',
          description: 'Test description',
          severity: 'high',
          recommendations: ['Test recommendation'],
          detectedAt: DateTime.now(),
        ),
        GrowthAlert(
          type: GrowthAlertType.percentileCrossing,
          title: 'Test Alert 2',
          description: 'Test description',
          severity: 'medium',
          recommendations: ['Test recommendation'],
          detectedAt: DateTime.now(),
        ),
      ];

      final summary = GrowthAnalyzer.generateAlertSummary(alerts);

      expect(summary['totalAlerts'], equals(2));
      expect(summary['highCount'], equals(1));
      expect(summary['mediumCount'], equals(1));
      expect(summary['hasUrgentAlerts'], isTrue);
      expect(summary['mostSevereAlert'], isNotNull);
      expect((summary['alertTypes'] as List).length, equals(2));
    });

    test('should handle empty measurements gracefully', () {
      final analysis = GrowthAnalyzer.analyzeGrowthPattern([], testBaby);

      expect(analysis.trends, isEmpty);
      expect(analysis.velocityAnalysis, isNull);
      expect(analysis.alerts.length, equals(1)); // Should have missing measurements alert
      expect(analysis.overallAssessment, equals('Insufficient data for assessment'));
      expect(analysis.growthSummary, contains('No growth data available'));
    });

    test('should detect slow growth velocity', () {
      // Create measurements with very slow growth
      final slowGrowthMeasurements = [
        MeasurementData(
          value: 7.0,
          ageInMonths: 3,
          date: testBaby.birthDate.add(const Duration(days: 90)),
          measurementType: 'weight',
          gender: 'male',
        ),
        MeasurementData(
          value: 7.05, // Very small increase
          ageInMonths: 4,
          date: testBaby.birthDate.add(const Duration(days: 120)),
          measurementType: 'weight',
          gender: 'male',
        ),
      ];

      final analysis = GrowthAnalyzer.analyzeGrowthPattern(slowGrowthMeasurements, testBaby);

      // Check if slow growth velocity is detected
      expect(analysis.velocityAnalysis, isNotNull);
      if (analysis.velocityAnalysis!.requiresAttention) {
        final velocityAlerts = analysis.alerts.where((a) => a.type == GrowthAlertType.slowGrowthVelocity);
        expect(velocityAlerts.length, greaterThanOrEqualTo(0)); // May or may not trigger based on exact percentile
      }
    });

    test('should provide age-appropriate recommendations', () {
      // Test with young infant
      final youngBaby = BabyProfile(
        id: 'young-baby',
        name: 'Young Baby',
        birthDate: DateTime.now().subtract(const Duration(days: 60)), // 2 months old
        gender: 'female',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
      );

      final analysis = GrowthAnalyzer.analyzeGrowthPattern(testMeasurements, youngBaby);

      expect(analysis.recommendations.any((r) => r.contains('weekly')), isTrue);
      expect(analysis.recommendations.any((r) => r.contains('feeding frequency')), isTrue);
    });

    test('should calculate highest severity correctly', () {
      final analysis = GrowthAnalysis(
        trends: [],
        alerts: [
          GrowthAlert(
            type: GrowthAlertType.belowNormalRange,
            title: 'Test',
            description: 'Test',
            severity: 'medium',
            recommendations: [],
            detectedAt: DateTime.now(),
          ),
          GrowthAlert(
            type: GrowthAlertType.percentileCrossing,
            title: 'Test',
            description: 'Test',
            severity: 'high',
            recommendations: [],
            detectedAt: DateTime.now(),
          ),
        ],
        overallAssessment: 'Test',
        percentileCrossingAnalysis: {},
        growthSummary: 'Test',
        recommendations: [],
        analyzedAt: DateTime.now(),
      );

      expect(analysis.highestSeverity, equals('high'));
      expect(analysis.hasUrgentAlerts, isTrue);
      
      final alertCounts = analysis.alertCountBySeverity;
      expect(alertCounts['high'], equals(1));
      expect(alertCounts['medium'], equals(1));
    });
  });

  group('GrowthAlert Tests', () {
    test('should provide correct color indicators', () {
      final alerts = [
        GrowthAlert(
          type: GrowthAlertType.belowNormalRange,
          title: 'Test',
          description: 'Test',
          severity: 'low',
          recommendations: [],
          detectedAt: DateTime.now(),
        ),
        GrowthAlert(
          type: GrowthAlertType.belowNormalRange,
          title: 'Test',
          description: 'Test',
          severity: 'medium',
          recommendations: [],
          detectedAt: DateTime.now(),
        ),
        GrowthAlert(
          type: GrowthAlertType.belowNormalRange,
          title: 'Test',
          description: 'Test',
          severity: 'high',
          recommendations: [],
          detectedAt: DateTime.now(),
        ),
        GrowthAlert(
          type: GrowthAlertType.belowNormalRange,
          title: 'Test',
          description: 'Test',
          severity: 'critical',
          recommendations: [],
          detectedAt: DateTime.now(),
        ),
      ];

      expect(alerts[0].colorIndicator, equals('#4CAF50')); // Green for low
      expect(alerts[1].colorIndicator, equals('#FF9800')); // Orange for medium
      expect(alerts[2].colorIndicator, equals('#F44336')); // Red for high
      expect(alerts[3].colorIndicator, equals('#9C27B0')); // Purple for critical

      expect(alerts[0].requiresImmediateAttention, isFalse);
      expect(alerts[1].requiresImmediateAttention, isFalse);
      expect(alerts[2].requiresImmediateAttention, isTrue);
      expect(alerts[3].requiresImmediateAttention, isTrue);
    });
  });
}