import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../lib/utils/user_avatar_theme_helper.dart';

void main() {
  group('UserAvatarThemeHelper', () {
    late BuildContext context;

    setUp(() {
      // Create a test widget to get BuildContext
      testWidgets('setup context', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (BuildContext ctx) {
                context = ctx;
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('getRoleBorderColor', () {
      testWidgets('returns correct color for admin role', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (BuildContext ctx) {
                final color = UserAvatarThemeHelper.getRoleBorderColor(ctx, 'admin');
                expect(color, isA<Color>());
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('returns correct color for parent role', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (BuildContext ctx) {
                final color = UserAvatarThemeHelper.getRoleBorderColor(ctx, 'parent');
                expect(color, Theme.of(ctx).colorScheme.primary);
                return Container();
              },
            ),
          ),
        );
      });

      testWidgets('returns primary color for unknown role', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (BuildContext ctx) {
                final color = UserAvatarThemeHelper.getRoleBorderColor(ctx, 'unknown');
                expect(color, Theme.of(ctx).colorScheme.primary);
                return Container();
              },
            ),
          ),
        );
      });
    });

    group('getBorderWidth', () {
      test('returns custom width when provided', () {
        final width = UserAvatarThemeHelper.getBorderWidth('parent', customWidth: 5.0);
        expect(width, 5.0);
      });

      test('returns correct width for admin role', () {
        final width = UserAvatarThemeHelper.getBorderWidth('admin');
        expect(width, 3.0);
      });

      test('returns correct width for parent role', () {
        final width = UserAvatarThemeHelper.getBorderWidth('parent');
        expect(width, 2.5);
      });

      test('returns default width for unknown role', () {
        final width = UserAvatarThemeHelper.getBorderWidth('unknown');
        expect(width, 2.0);
      });
    });

    group('getRoleConfig', () {
      testWidgets('returns complete role configuration', (WidgetTester tester) async {
        await tester.pumpWidget(
          MaterialApp(
            home: Builder(
              builder: (BuildContext ctx) {
                final config = UserAvatarThemeHelper.getRoleConfig(ctx, 'parent');
                expect(config, isA<RoleThemeConfig>());
                expect(config.borderColor, isA<Color>());
                expect(config.backgroundColor, isA<Color>());
                expect(config.borderWidth, isA<double>());
                return Container();
              },
            ),
          ),
        );
      });
    });
  });

  group('RoleThemeConfig', () {
    test('creates instance with all required properties', () {
      const config = RoleThemeConfig(
        borderColor: Colors.blue,
        backgroundColor: Colors.lightBlue,
        borderWidth: 2.0,
      );

      expect(config.borderColor, Colors.blue);
      expect(config.backgroundColor, Colors.lightBlue);
      expect(config.borderWidth, 2.0);
    });
  });
}