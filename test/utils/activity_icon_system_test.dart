import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:babytracker_pro/utils/activity_type_config.dart';
import 'package:babytracker_pro/utils/activity_icon_manager.dart';

void main() {
  group('Activity Icon System Tests', () {
    
    test('ActivityTypeConfig should provide consistent data for all activity types', () {
      final activityTypes = [
        'feeding',
        'sleep',
        'diaper',
        'medicine',
        'vaccination',
        'temperature',
        'potty',
        'bath',
        'tummy_time',
        'story_time',
        'screen_time',
        'skin_to_skin',
        'outdoor_play',
        'indoor_play',
        'brush_teeth',
        'pumping',
        'growth',
        'milestone',
        'custom',
      ];
      
      for (final type in activityTypes) {
        // Test that each activity type has valid configuration
        expect(ActivityTypeConfig.getConfig(type), isNotNull, 
            reason: 'Activity type $type should have valid configuration');
        
        // Test that icons are not empty
        expect(ActivityTypeConfig.getIcon(type), isNotEmpty,
            reason: 'Activity type $type should have a valid icon');
        
        // Test that labels are not empty
        expect(ActivityTypeConfig.getLabel(type), isNotEmpty,
            reason: 'Activity type $type should have a valid label');
        
        // Test that colors are valid
        expect(ActivityTypeConfig.getColor(type), isA<Color>(),
            reason: 'Activity type $type should have a valid color');
        
        // Test that descriptions are not empty
        expect(ActivityTypeConfig.getDescription(type), isNotEmpty,
            reason: 'Activity type $type should have a valid description');
      }
    });
    
    test('ActivityTypeConfig should handle unknown activity types gracefully', () {
      const unknownType = 'unknown_activity';
      
      // Should return null for unknown types
      expect(ActivityTypeConfig.getConfig(unknownType), isNull);
      
      // Should return default values for unknown types
      expect(ActivityTypeConfig.getIcon(unknownType), equals('add_circle'));
      expect(ActivityTypeConfig.getColor(unknownType), equals(const Color(0xFF6366F1)));
      expect(ActivityTypeConfig.getLabel(unknownType), isNotEmpty);
      expect(ActivityTypeConfig.getDescription(unknownType), equals('Activity tracking'));
    });
    
    test('ActivityIconManager should validate activity types correctly', () {
      // Valid activity types
      expect(ActivityIconManager.hasValidIcon('feeding'), isTrue);
      expect(ActivityIconManager.hasValidIcon('sleep'), isTrue);
      expect(ActivityIconManager.hasValidIcon('diaper'), isTrue);
      
      // Invalid activity types
      expect(ActivityIconManager.hasValidIcon('invalid_type'), isFalse);
      expect(ActivityIconManager.hasValidIcon(''), isFalse);
    });
    
    test('ActivityIconManager should provide consistent icon names and colors', () {
      const testType = 'feeding';
      
      // Test icon name consistency
      expect(ActivityIconManager.getIconName(testType), 
          equals(ActivityTypeConfig.getIcon(testType)));
      
      // Test color consistency
      expect(ActivityIconManager.getIconColor(testType), 
          equals(ActivityTypeConfig.getColor(testType)));
    });
    
    test('All activity types should have unique icons', () {
      final allConfigs = ActivityTypeConfig.getAllConfigs();
      final icons = allConfigs.map((config) => config['icon']).toList();
      final uniqueIcons = icons.toSet();
      
      // Most icons should be unique (some might be shared intentionally)
      expect(uniqueIcons.length, greaterThan(icons.length * 0.7),
          reason: 'Most activity types should have unique icons');
    });
    
    test('Color scheme should be consistent and accessible', () {
      final allConfigs = ActivityTypeConfig.getAllConfigs();
      
      for (final config in allConfigs) {
        final color = config['color'] as Color;
        
        // Test that colors are not completely transparent
        expect(color.alpha, greaterThan(0));
        
        // Test that colors are not pure white or black (should have some hue)
        expect(color, isNot(equals(Colors.white)));
        expect(color, isNot(equals(Colors.black)));
      }
    });
    
    test('getAllActivityTypes should return all configured types', () {
      final allTypes = ActivityIconManager.getAllActivityTypes();
      
      // Should contain core activity types
      expect(allTypes, contains('feeding'));
      expect(allTypes, contains('sleep'));
      expect(allTypes, contains('diaper'));
      expect(allTypes, contains('medicine'));
      
      // Should not be empty
      expect(allTypes, isNotEmpty);
      
      // Should not contain duplicates
      expect(allTypes.toSet().length, equals(allTypes.length));
    });
    
    test('Activity type names should follow naming conventions', () {
      final allTypes = ActivityIconManager.getAllActivityTypes();
      
      for (final type in allTypes) {
        // Should use snake_case for multi-word types
        expect(type, matches(r'^[a-z][a-z0-9_]*$'),
            reason: 'Activity type $type should follow snake_case naming convention');
        
        // Should not start or end with underscore
        expect(type, isNot(startsWith('_')));
        expect(type, isNot(endsWith('_')));
      }
    });
  });
  
  group('Icon Consistency Tests', () {
    
    test('Feeding icon should be consistent across all components', () {
      const feedingType = 'feeding';
      const expectedIcon = 'restaurant';
      const expectedColor = Color(0xFF4A90A4);
      
      expect(ActivityTypeConfig.getIcon(feedingType), equals(expectedIcon));
      expect(ActivityTypeConfig.getColor(feedingType), equals(expectedColor));
      expect(ActivityIconManager.getIconName(feedingType), equals(expectedIcon));
      expect(ActivityIconManager.getIconColor(feedingType), equals(expectedColor));
    });
    
    test('Sleep icon should be consistent across all components', () {
      const sleepType = 'sleep';
      const expectedIcon = 'bedtime';
      const expectedColor = Color(0xFFF4A261);
      
      expect(ActivityTypeConfig.getIcon(sleepType), equals(expectedIcon));
      expect(ActivityTypeConfig.getColor(sleepType), equals(expectedColor));
      expect(ActivityIconManager.getIconName(sleepType), equals(expectedIcon));
      expect(ActivityIconManager.getIconColor(sleepType), equals(expectedColor));
    });
    
    test('Diaper icon should be consistent across all components', () {
      const diaperType = 'diaper';
      const expectedIcon = 'child_care';
      const expectedColor = Color(0xFF7FB069);
      
      expect(ActivityTypeConfig.getIcon(diaperType), equals(expectedIcon));
      expect(ActivityTypeConfig.getColor(diaperType), equals(expectedColor));
      expect(ActivityIconManager.getIconName(diaperType), equals(expectedIcon));
      expect(ActivityIconManager.getIconColor(diaperType), equals(expectedColor));
    });
    
    test('Medicine icon should be consistent across all components', () {
      const medicineType = 'medicine';
      const expectedIcon = 'medication';
      const expectedColor = Color(0xFFE76F51);
      
      expect(ActivityTypeConfig.getIcon(medicineType), equals(expectedIcon));
      expect(ActivityTypeConfig.getColor(medicineType), equals(expectedColor));
      expect(ActivityIconManager.getIconName(medicineType), equals(expectedIcon));
      expect(ActivityIconManager.getIconColor(medicineType), equals(expectedColor));
    });
  });
}
