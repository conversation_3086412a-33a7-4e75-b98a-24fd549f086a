import 'package:flutter_test/flutter_test.dart';
import 'package:babytracker_pro/services/ai_insights_state_manager.dart';
import 'package:babytracker_pro/models/baby_profile.dart';

void main() {
  group('AI Insights Dynamic Timestamp Tests', () {
    late AIInsightsStateManager stateManager;

    setUp(() {
      stateManager = AIInsightsStateManager();
      // Reset all test values before each test
      stateManager.setLastUpdateTimeForTesting(null);
      stateManager.setLastManualRefreshTimeForTesting(null);
      stateManager.stopTimestampUpdates();
    });

    tearDown(() {
      stateManager.stopTimestampUpdates();
      // Don't dispose singleton - just clean up
      stateManager.setLastUpdateTimeForTesting(null);
      stateManager.setLastManualRefreshTimeForTesting(null);
    });

    test('formattedLastUpdate returns null when no timestamp is set', () {
      expect(stateManager.formattedLastUpdate, isNull);
    });

    test('formattedLastUpdate calculates correct time difference', () {
      // Simulate a timestamp from 5 minutes ago
      final fiveMinutesAgo = DateTime.now().subtract(Duration(minutes: 5));
      
      // Set the internal state manually for testing
      stateManager.setLastUpdateTimeForTesting(fiveMinutesAgo);
      
      final result = stateManager.formattedLastUpdate;
      expect(result, contains('5 minute'));
      expect(result, contains('ago'));
    });

    test('formattedLastUpdate prefers manual refresh time over update time', () {
      final tenMinutesAgo = DateTime.now().subtract(Duration(minutes: 10));
      final fiveMinutesAgo = DateTime.now().subtract(Duration(minutes: 5));
      
      // Set both timestamps, manual refresh more recent
      stateManager.setLastUpdateTimeForTesting(tenMinutesAgo);
      stateManager.setLastManualRefreshTimeForTesting(fiveMinutesAgo);
      
      final result = stateManager.formattedLastUpdate;
      expect(result, contains('5 minute'));
    });

    test('formattedLastUpdate handles hours correctly', () {
      final twoHoursAgo = DateTime.now().subtract(Duration(hours: 2, minutes: 30));
      stateManager.setLastUpdateTimeForTesting(twoHoursAgo);
      
      final result = stateManager.formattedLastUpdate;
      expect(result, contains('2 hour'));
      expect(result, contains('30 minute'));
      expect(result, contains('ago'));
    });

    test('formattedLastUpdate handles days correctly', () {
      final oneDayAgo = DateTime.now().subtract(Duration(days: 1, hours: 3));
      stateManager.setLastUpdateTimeForTesting(oneDayAgo);
      
      final result = stateManager.formattedLastUpdate;
      expect(result, contains('1 day'));
      expect(result, contains('3 hour'));
      expect(result, contains('ago'));
    });

    test('formattedLastUpdate handles zero minutes', () {
      final thirtySecondsAgo = DateTime.now().subtract(Duration(seconds: 30));
      stateManager.setLastUpdateTimeForTesting(thirtySecondsAgo);
      
      final result = stateManager.formattedLastUpdate;
      expect(result, equals('just now'));
    });

    test('timer starts and stops correctly', () {
      expect(stateManager.timestampTimer, isNull);
      
      stateManager.startTimestampUpdates();
      expect(stateManager.timestampTimer, isNotNull);
      
      stateManager.stopTimestampUpdates();
      expect(stateManager.timestampTimer, isNull);
    });

    test('timer notifies listeners when started', () async {
      var notificationCount = 0;
      
      stateManager.addListener(() {
        notificationCount++;
      });
      
      stateManager.startTimestampUpdates();
      
      // Wait slightly more than 1 minute to ensure timer fires
      // Note: In a real test environment, you might want to use fake_async
      // to control time more precisely
      await Future.delayed(Duration(seconds: 2));
      
      // The timer should be running (we can't easily test the actual firing 
      // without fake_async, but we can verify it's set up)
      expect(stateManager.timestampTimer, isNotNull);
      expect(stateManager.timestampTimer!.isActive, isTrue);
      
      stateManager.stopTimestampUpdates();
    });

    test('mostRecentTimestamp returns correct value', () {
      final tenMinutesAgo = DateTime.now().subtract(Duration(minutes: 10));
      final fiveMinutesAgo = DateTime.now().subtract(Duration(minutes: 5));
      
      // Test with only update time
      stateManager.setLastUpdateTimeForTesting(tenMinutesAgo);
      expect(stateManager.mostRecentTimestamp, equals(tenMinutesAgo));
      
      // Test with both timestamps - should return more recent
      stateManager.setLastManualRefreshTimeForTesting(fiveMinutesAgo);
      expect(stateManager.mostRecentTimestamp, equals(fiveMinutesAgo));
      
      // Test with only manual refresh time
      stateManager.setLastUpdateTimeForTesting(null);
      expect(stateManager.mostRecentTimestamp, equals(fiveMinutesAgo));
      
      // Test with no timestamps
      stateManager.setLastManualRefreshTimeForTesting(null);
      expect(stateManager.mostRecentTimestamp, isNull);
    });

    test('dynamic timestamp calculation changes over time', () async {
      final now = DateTime.now();
      stateManager.setLastUpdateTimeForTesting(now);
      
      // First check - should be 'just now' for current time
      final result1 = stateManager.formattedLastUpdate;
      expect(result1, equals('just now'));
      
      // Simulate time passing by manually adjusting the timestamp
      stateManager.setLastUpdateTimeForTesting(now.subtract(Duration(minutes: 1)));
      
      // Second check - should be 1 minute
      final result2 = stateManager.formattedLastUpdate;
      expect(result2, equals('1 minute ago'));
      
      // Simulate more time passing
      stateManager.setLastUpdateTimeForTesting(now.subtract(Duration(minutes: 2)));
      
      // Third check - should be 2 minutes
      final result3 = stateManager.formattedLastUpdate;
      expect(result3, equals('2 minutes ago'));
    });

    test('timer cleanup on dispose', () {
      stateManager.startTimestampUpdates();
      expect(stateManager.timestampTimer, isNotNull);
      
      stateManager.dispose();
      // Timer should be cleaned up after dispose
      // Note: We can't directly test if the timer is cancelled since
      // that's an internal implementation detail, but disposal should handle it
    });
  });
}
