import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:sizer/sizer.dart';

// Import the sleep entry widget
import 'lib/presentation/quick_log_bottom_sheet/widgets/sleep_entry_widget.dart';
import 'lib/widgets/modern_date_time_picker.dart';

void main() {
  group('Quick Log Sleep Entry Widget Tests', () {
    
    testWidgets('SleepEntryWidget uses ModernDateTimePicker.formatDateTime', (WidgetTester tester) async {
      // Create a test app that wraps the widget
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              home: Scaffold(
                body: SleepEntryWidget(
                  onDataChanged: (data) {
                    print('Sleep entry data changed: $data');
                  },
                ),
              ),
            );
          },
        ),
      );

      // Wait for the widget to build
      await tester.pump();

      // Check if the widget displays the current time using the centralized formatter
      final now = DateTime.now();
      final expectedFormat = ModernDateTimePicker.formatDateTime(now, tester.element(find.byType(MaterialApp)));
      
      print('Expected format: $expectedFormat');
      
      // Look for text that contains the formatted date/time
      expect(find.textContaining(expectedFormat), findsOneWidget);
    });

    test('ModernDateTimePicker.formatDateTime produces consistent format', () {
      final testDateTime = DateTime(2025, 7, 9, 13, 49, 0);
      
      // Mock context (this is a simplified test)
      // In a real test, you'd need to provide a proper BuildContext
      
      // Test the format - it should produce something like "Date: 7/9/2025 at 1:49 PM"
      print('Test DateTime: $testDateTime');
      
      // Check that the date time can be formatted (this would require BuildContext in real usage)
      expect(testDateTime.year, equals(2025));
      expect(testDateTime.month, equals(7));
      expect(testDateTime.day, equals(9));
      expect(testDateTime.hour, equals(13));
      expect(testDateTime.minute, equals(49));
    });
  });
}
