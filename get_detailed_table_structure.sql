-- Get detailed table structure for all relevant tables
-- Run this in your Supabase SQL editor

-- Get columns for baby_profiles
SELECT 'baby_profiles' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'baby_profiles'
ORDER BY ordinal_position

UNION ALL

-- Get columns for activity_logs  
SELECT 'activity_logs' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'activity_logs'
ORDER BY ordinal_position

UNION ALL

-- Get columns for growth_measurements
SELECT 'growth_measurements' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'growth_measurements'
ORDER BY ordinal_position

UNION ALL

-- Get columns for medicine_logs
SELECT 'medicine_logs' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'medicine_logs'
ORDER BY ordinal_position

UNION ALL

-- Get columns for vaccination_logs
SELECT 'vaccination_logs' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'vaccination_logs'
ORDER BY ordinal_position

UNION ALL

-- Get columns for user_profiles
SELECT 'user_profiles' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'user_profiles'
ORDER BY ordinal_position

UNION ALL

-- Get columns for scheduled_activities
SELECT 'scheduled_activities' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'scheduled_activities'
ORDER BY ordinal_position

UNION ALL

-- Get columns for milestones
SELECT 'milestones' as table_name, column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'milestones'
ORDER BY ordinal_position

ORDER BY table_name, column_name;