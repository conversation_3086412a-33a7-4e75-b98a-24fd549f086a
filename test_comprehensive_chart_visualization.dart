import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'lib/presentation/growth_charts/widgets/growth_chart_renderer.dart';
import 'lib/services/who_data_service.dart';

void main() {
  group('Comprehensive Chart Visualization Tests', () {
    testWidgets('GrowthChartRenderer displays distinct percentile curves', (WidgetTester tester) async {
      // Test data
      final measurements = [
        {
          'date': DateTime(2024, 1, 1),
          'value': 3.5,
          'unit': 'kg',
          'notes': 'Birth weight',
        },
        {
          'date': DateTime(2024, 2, 1),
          'value': 4.2,
          'unit': 'kg',
          'notes': '1 month',
        },
        {
          'date': DateTime(2024, 3, 1),
          'value': 5.1,
          'unit': 'kg',
          'notes': '2 months',
        },
      ];

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GrowthChartRenderer(
              measurements: measurements,
              measurementType: 'weight',
              gender: 'male',
              isMetric: true,
              dateRange: '1 year',
              birthDate: DateTime(2024, 1, 1),
            ),
          ),
        ),
      );

      // Verify the chart renders
      expect(find.byType(GrowthChartRenderer), findsOneWidget);
      
      // Verify chart header is displayed
      expect(find.text('Weight Growth Chart'), findsOneWidget);
      
      // Verify measurement count is shown
      expect(find.textContaining('3 measurements'), findsOneWidget);
      
      // Verify legend is displayed
      expect(find.text('Legend'), findsOneWidget);
      expect(find.text('Your Baby'), findsOneWidget);
      expect(find.text('50th (Median)'), findsOneWidget);
      expect(find.text('97th'), findsOneWidget);
      expect(find.text('3rd'), findsOneWidget);
    });

    test('WHO percentile curves have distinct styling', () {
      // Test percentile curve generation
      final curves = _generateTestWHOPercentileCurves();
      
      expect(curves.length, equals(7)); // 3rd, 10th, 25th, 50th, 75th, 90th, 97th
      
      // Verify distinct colors
      final colors = curves.map((c) => c.curveColor).toSet();
      expect(colors.length, equals(7)); // All curves should have different colors
      
      // Verify stroke widths - median should be thicker
      final medianCurve = curves.firstWhere((c) => c.percentile == '50th');
      expect(medianCurve.strokeWidth, equals(3.0));
      
      // Verify boundary curves (3rd, 97th) are solid
      final thirdCurve = curves.firstWhere((c) => c.percentile == '3rd');
      final ninetySeventhCurve = curves.firstWhere((c) => c.percentile == '97th');
      expect(thirdCurve.isDashed, isFalse);
      expect(ninetySeventhCurve.isDashed, isFalse);
    });

    test('Measurement points have visual indicators for normal ranges', () {
      // Test measurement with different percentiles
      final measurements = [
        {'value': 2.0, 'ageInMonths': 0.0}, // Below 3rd percentile
        {'value': 3.5, 'ageInMonths': 0.0}, // Normal range
        {'value': 5.0, 'ageInMonths': 0.0}, // Above 97th percentile
      ];

      for (final measurement in measurements) {
        final percentile = WHODataService.calculateExactPercentile(
          measurement['value'] as double,
          measurement['ageInMonths'] as double,
          'weight',
          'male',
        );

        // Verify percentile calculation
        expect(percentile, isA<double>());
        expect(percentile, greaterThanOrEqualTo(0.1));
        expect(percentile, lessThanOrEqualTo(99.9));
      }
    });

    test('Unit conversion support works correctly', () {
      // Test metric to imperial conversion
      const metricWeight = 5.0; // kg
      const expectedImperialWeight = 11.0231; // lbs (approximately)
      
      final convertedWeight = metricWeight * 2.20462;
      expect(convertedWeight, closeTo(expectedImperialWeight, 0.01));
      
      // Test metric to imperial height conversion
      const metricHeight = 60.0; // cm
      const expectedImperialHeight = 23.622; // inches (approximately)
      
      final convertedHeight = metricHeight / 2.54;
      expect(convertedHeight, closeTo(expectedImperialHeight, 0.01));
    });

    test('Axis scaling and labeling works for different date ranges', () {
      final dateRanges = ['6 months', '1 year', '2 years', '3 years', '4 years', '5 years'];
      final expectedMaxAges = [6.0, 12.0, 24.0, 36.0, 48.0, 60.0];
      
      for (int i = 0; i < dateRanges.length; i++) {
        final maxAge = _getMaxAgeForDateRange(dateRanges[i]);
        expect(maxAge, equals(expectedMaxAges[i]));
      }
    });

    test('Trend direction calculation works correctly', () {
      // Test positive trend
      final positiveTrendSpots = [
        FlSpot(0, 3.0),
        FlSpot(1, 4.0),
        FlSpot(2, 5.0),
      ];
      expect(_calculateTrendDirection(positiveTrendSpots), isTrue);
      
      // Test negative trend
      final negativeTrendSpots = [
        FlSpot(0, 5.0),
        FlSpot(1, 4.0),
        FlSpot(2, 3.0),
      ];
      expect(_calculateTrendDirection(negativeTrendSpots), isFalse);
      
      // Test single point (should default to positive)
      final singleSpot = [FlSpot(0, 3.0)];
      expect(_calculateTrendDirection(singleSpot), isTrue);
    });
  });
}

// Helper functions for testing
List<WHOPercentileCurve> _generateTestWHOPercentileCurves() {
  final percentiles = [3.0, 10.0, 25.0, 50.0, 75.0, 90.0, 97.0];
  final colors = [
    const Color(0xFFDC2626), // 3rd - Dark Red
    const Color(0xFFEA580C), // 10th - Orange Red
    const Color(0xFFF59E0B), // 25th - Amber
    const Color(0xFF059669), // 50th - Green (median)
    const Color(0xFF0284C7), // 75th - Sky Blue
    const Color(0xFF7C3AED), // 90th - Violet
    const Color(0xFF9333EA), // 97th - Purple
  ];
  final strokeWidths = [2.0, 1.5, 1.5, 3.0, 1.5, 1.5, 2.0];
  final dashPatterns = [null, [6, 4], [4, 3], null, [4, 3], [6, 4], null];

  return percentiles.asMap().entries.map((entry) {
    final index = entry.key;
    final percentile = entry.value;
    
    return WHOPercentileCurve(
      percentile: '${percentile.toInt()}${_getPercentileSuffix(percentile.toInt())}',
      points: [FlSpot(0, percentile), FlSpot(12, percentile + 2)], // Mock points
      curveColor: colors[index],
      strokeWidth: strokeWidths[index],
      label: '${percentile.toInt()}${_getPercentileSuffix(percentile.toInt())} percentile',
      isDashed: dashPatterns[index] != null,
    );
  }).toList();
}

double _getMaxAgeForDateRange(String dateRange) {
  switch (dateRange) {
    case '6 months':
      return 6.0;
    case '1 year':
      return 12.0;
    case '2 years':
      return 24.0;
    case '3 years':
      return 36.0;
    case '4 years':
      return 48.0;
    case '5 years':
      return 60.0;
    default:
      return 12.0;
  }
}

bool _calculateTrendDirection(List<FlSpot> spots) {
  if (spots.length < 2) return true;
  
  double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
  final n = spots.length;
  
  for (final spot in spots) {
    sumX += spot.x;
    sumY += spot.y;
    sumXY += spot.x * spot.y;
    sumX2 += spot.x * spot.x;
  }
  
  final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
  return slope >= 0;
}

String _getPercentileSuffix(int percentile) {
  if (percentile >= 11 && percentile <= 13) return 'th';
  switch (percentile % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}