/// Utility class for formatting profile display data
class ProfileDisplayFormatter {
  const ProfileDisplayFormatter._();
  
  // Cache for expensive operations
  static final Map<String, String> _initialsCache = {};
  static final Map<String, String> _roleCache = {};
  static final Map<DateTime, String> _dateCache = {};

  /// Get user initials from full name (cached)
  static String getInitials(String fullName) {
    return _initialsCache.putIfAbsent(fullName, () {
      final names = fullName.trim().split(' ');
      if (names.isEmpty) return 'U';
      if (names.length == 1) return names[0][0].toUpperCase();
      return '${names.first[0]}${names.last[0]}'.toUpperCase();
    });
  }

  /// Format role text for display (cached)
  static String formatRoleText(String role) {
    return _roleCache.putIfAbsent(role, () {
      return role.split('_').map((word) => 
        word[0].toUpperCase() + word.substring(1).toLowerCase()
      ).join(' ');
    });
  }

  /// Format date for display with relative time
  static String formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }

  /// Format last activity for display with more granular time
  static String formatLastActivity(DateTime lastActivity) {
    final now = DateTime.now();
    final difference = now.difference(lastActivity);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hr ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return formatDate(lastActivity);
    }
  }
}