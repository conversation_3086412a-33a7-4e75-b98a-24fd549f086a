# Naming Conventions

## Classes
- Use PascalCase: `UserProfileService`, `ActivityRepository`
- Suffix with purpose: `Service`, `Repository`, `Controller`, `Widget`

## Methods
- Use camelCase: `getUserProfile()`, `loadFamilyMembers()`
- Prefix with action: `get`, `load`, `save`, `delete`, `update`
- Private methods start with underscore: `_initializeController()`

## Variables
- Use camelCase: `userProfile`, `isLoading`, `familyMembers`
- Boolean variables: `isLoading`, `hasError`, `canManage`
- Private variables start with underscore: `_client`, `_isInitialized`

## Constants
- Use SCREAMING_SNAKE_CASE: `MAX_RETRY_ATTEMPTS`, `DEFAULT_TIMEOUT`

## Files and Directories
- Use snake_case: `user_profile_service.dart`, `activity_repository.dart`
- Group by feature: `services/database/`, `widgets/shared/`

## Database
- Tables: snake_case plural: `user_profiles`, `activity_logs`
- Columns: snake_case: `created_at`, `full_name`, `is_active`
- Functions: snake_case: `get_recent_activities`, `validate_user_permissions`