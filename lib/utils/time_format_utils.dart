import 'package:intl/intl.dart';
import '../services/settings_service.dart';

class TimeFormatUtils {
  static final SettingsService _settingsService = SettingsService.instance;

  /// Formats a DateTime object according to user's time format preference
  static String formatTime(DateTime dateTime) {
    try {
      if (_settingsService.is24HourFormat) {
        return DateFormat('HH:mm').format(dateTime);
      } else {
        return DateFormat('h:mm a').format(dateTime);
      }
    } catch (e) {
      // Fallback to default format if settings service is not initialized
      return DateFormat('h:mm a').format(dateTime);
    }
  }

  /// Formats a DateTime object with date and time according to user's preference
  static String formatDateTime(DateTime dateTime) {
    try {
      if (_settingsService.is24HourFormat) {
        return DateFormat('MMM d, HH:mm').format(dateTime);
      } else {
        return DateFormat('MMM d, h:mm a').format(dateTime);
      }
    } catch (e) {
      // Fallback to default format if settings service is not initialized
      return DateFormat('MMM d, h:mm a').format(dateTime);
    }
  }

  /// Formats a DateTime object for display with "Today", "Yesterday", or date
  static String formatRelativeDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);

    String timeString;
    try {
      if (_settingsService.is24HourFormat) {
        timeString = DateFormat('HH:mm').format(dateTime);
      } else {
        timeString = DateFormat('h:mm a').format(dateTime);
      }
    } catch (e) {
      timeString = DateFormat('h:mm a').format(dateTime);
    }

    if (dateOnly == today) {
      return 'Today $timeString';
    } else if (dateOnly == yesterday) {
      return 'Yesterday $timeString';
    } else {
      return '${DateFormat('MMM d').format(dateTime)} $timeString';
    }
  }

  /// Formats time duration in a readable format
  static String formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      return '${duration.inDays}d ${duration.inHours % 24}h ${duration.inMinutes % 60}m';
    } else if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }

  /// Formats time range with user's preference
  static String formatTimeRange(DateTime start, DateTime end) {
    try {
      if (_settingsService.is24HourFormat) {
        return '${DateFormat('HH:mm').format(start)} - ${DateFormat('HH:mm').format(end)}';
      } else {
        return '${DateFormat('h:mm a').format(start)} - ${DateFormat('h:mm a').format(end)}';
      }
    } catch (e) {
      return '${DateFormat('h:mm a').format(start)} - ${DateFormat('h:mm a').format(end)}';
    }
  }
}
