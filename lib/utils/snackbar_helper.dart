import 'package:flutter/material.dart';
import '../theme/theme_aware_colors.dart';

/// Centralized helper for consistent SnackBar styling and behavior
class SnackBarHelper {
  SnackBarHelper._();

  /// Show success message with consistent styling
  static void showSuccess(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 2),
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: ThemeAwareColors.getSuccessColor(context),
      duration: duration,
    );
  }

  /// Show warning message with consistent styling
  static void showWarning(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: ThemeAwareColors.getWarningColor(context),
      duration: duration,
    );
  }

  /// Show error message with consistent styling
  static void showError(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 4),
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: ThemeAwareColors.getErrorColor(context),
      duration: duration,
    );
  }

  /// Show info message with consistent styling
  static void showInfo(
    BuildContext context,
    String message, {
    Duration duration = const Duration(seconds: 3),
  }) {
    _showSnackBar(
      context,
      message,
      backgroundColor: Theme.of(context).colorScheme.primary,
      duration: duration,
    );
  }

  /// Show AI analysis in progress message
  static void showAIAnalysisInProgress(BuildContext context) {
    showWarning(
      context,
      'AI analysis already in progress...',
      duration: const Duration(seconds: 2),
    );
  }

  /// Show rate limit message with time remaining
  static void showRateLimit(
    BuildContext context,
    Duration timeRemaining,
  ) {
    final timeMessage = _formatDuration(timeRemaining);
    showWarning(
      context,
      'No updates available. Last update was recently. Please log more activities or wait $timeMessage to refresh insights.',
      duration: const Duration(seconds: 5),
    );
  }

  /// Show AI refresh success message
  static void showAIRefreshSuccess(
    BuildContext context, {
    bool isDevMode = false,
  }) {
    final message = isDevMode 
        ? '✅ AI insights refreshed successfully (Dev mode override)'
        : '✅ AI insights refreshed successfully';
    
    final backgroundColor = isDevMode 
        ? ThemeAwareColors.getWarningColor(context)
        : ThemeAwareColors.getSuccessColor(context);
    
    _showSnackBar(
      context,
      message,
      backgroundColor: backgroundColor,
      duration: const Duration(seconds: 2),
    );
  }

  /// Show no new insights available message
  static void showNoNewInsights(BuildContext context) {
    showInfo(
      context,
      'No new insights available. Continue logging activities to receive updated insights and recommendations.',
      duration: const Duration(seconds: 4),
    );
  }


  static void _showSnackBar(
    BuildContext context,
    String message, {
    required Color backgroundColor,
    required Duration duration,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: const TextStyle(color: Colors.white),
        ),
        backgroundColor: backgroundColor,
        duration: duration,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  static String _formatDuration(Duration duration) {
    if (duration.inDays > 0) {
      final days = duration.inDays;
      final hours = duration.inHours % 24;
      if (hours > 0) {
        return '$days day${days > 1 ? 's' : ''} $hours hour${hours > 1 ? 's' : ''}';
      }
      return '$days day${days > 1 ? 's' : ''}';
    } else if (duration.inHours > 0) {
      final hours = duration.inHours;
      final minutes = duration.inMinutes % 60;
      if (minutes > 0) {
        return '$hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''}';
      }
      return '$hours hour${hours > 1 ? 's' : ''}';
    } else {
      final minutes = duration.inMinutes;
      return '$minutes minute${minutes > 1 ? 's' : ''}';
    }
  }
}