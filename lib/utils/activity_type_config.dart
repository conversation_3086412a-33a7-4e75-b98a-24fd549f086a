import 'package:flutter/material.dart';
import '../theme/app_theme.dart';
import 'activity_configs.dart';

/// Configuration manager for activity types including colors, icons, and metadata.
/// 
/// This class provides theme-aware access to activity configurations, supporting
/// both light and dark themes. Use [getColor] with context for theme-aware colors,
/// or [getColorLight]/[getColorDark] when context is unavailable.
/// 
/// Example usage:
/// ```dart
/// // Theme-aware (recommended)
/// Color color = ActivityTypeConfig.getColor('feeding', context);
/// 
/// // Non-theme-aware (when context unavailable)
/// Color lightColor = ActivityTypeConfig.getColorLight('feeding');
/// ```
class ActivityTypeConfig {

  /// Get activity configuration
  static ActivityConfig getConfig(String activityType) {
    return ActivityConfigs.configs[activityType] ?? ActivityConfigs.defaultConfig;
  }

  /// Get color for activity type
  /// 
  /// When called with context, returns theme-aware color.
  /// When called without context, defaults to light theme color.
  /// 
  /// Examples:
  /// ```dart
  /// // Theme-aware (recommended when context available)
  /// Color color = ActivityTypeConfig.getColor('feeding', context);
  /// 
  /// // Fallback (when context unavailable)
  /// Color color = ActivityTypeConfig.getColor('feeding');
  /// ```
  static Color getColor(String activityType, [BuildContext? context]) {
    final config = getConfig(activityType);
    if (context != null) {
      final isDark = Theme.of(context).brightness == Brightness.dark;
      return isDark ? config.darkColor : config.lightColor;
    }
    // Default to light theme when context is not provided
    return config.lightColor;
  }

  /// Get color for activity type (without context - defaults to light theme)
  static Color getColorLight(String activityType) {
    final config = getConfig(activityType);
    return config.lightColor;
  }

  /// Get color for activity type (without context - defaults to dark theme)
  static Color getColorDark(String activityType) {
    final config = getConfig(activityType);
    return config.darkColor;
  }

  /// Get icon for activity type
  static String getIcon(String activityType) {
    return getConfig(activityType).icon;
  }

  /// Validate if an activity type exists
  static bool isValidActivityType(String activityType) {
    return ActivityConfigs.configs.containsKey(activityType);
  }

  /// Get label for activity type
  static String getLabel(String activityType) {
    return getConfig(activityType).label;
  }

  /// Get description for activity type
  static String getDescription(String activityType) {
    return getConfig(activityType).description;
  }

  /// Cached list of all configurations for performance
  static List<Map<String, dynamic>>? _cachedConfigs;

  /// Get all configurations as a list of maps (for backward compatibility)
  static List<Map<String, dynamic>> getAllConfigs() {
    return _cachedConfigs ??= ActivityConfigs.configs.entries.map((entry) {
      final config = entry.value;
      return {
        'type': entry.key,
        'label': config.label,
        'icon': config.icon,
        'description': config.description,
        'lightColor': config.lightColor,
        'darkColor': config.darkColor,
      };
    }).toList();
  }

  /// Get all activity types
  static List<String> getAllTypes() {
    return ActivityConfigs.configs.keys.toList();
  }
}

