import 'package:flutter/material.dart';

/// Singleton registry for icon mappings to improve performance
class IconRegistry {
  static final IconRegistry _instance = IconRegistry._internal();
  factory IconRegistry() => _instance;
  IconRegistry._internal();

  static final Map<String, IconData> _iconMap = {
    // Essential baby care icons
    'baby_changing_station': Icons.baby_changing_station,
    'health_and_safety': Icons.health_and_safety,
    'child_friendly': Icons.child_friendly,
    'star': Icons.star,
    'category': Icons.category,
    'restaurant': Icons.restaurant,
    'bedtime': Icons.bedtime,
    'child_care': Icons.child_care,
    'medication': Icons.medication,
    'vaccines': Icons.vaccines,
    'thermostat': Icons.thermostat,
    'wc': Icons.wc,
    'fitness_center': Icons.fitness_center,
    'menu_book': Icons.menu_book,
    'tv': Icons.tv,
    'favorite': Icons.favorite,
    'park': Icons.park,
    'toys': Icons.toys,
    'local_drink': Icons.local_drink,
    'trending_up': Icons.trending_up,
    'emoji_events': Icons.emoji_events,
    'add_circle': Icons.add_circle,
    'arrow_back': Icons.arrow_back,
    'refresh': Icons.refresh,
    'expand_less': Icons.expand_less,
    'expand_more': Icons.expand_more,
    // Add more icons as needed
  };

  /// Get icon by name with fallback
  IconData getIcon(String iconName, {IconData fallback = Icons.help_outline}) {
    return _iconMap[iconName] ?? fallback;
  }

  /// Check if icon exists
  bool hasIcon(String iconName) {
    return _iconMap.containsKey(iconName);
  }
}