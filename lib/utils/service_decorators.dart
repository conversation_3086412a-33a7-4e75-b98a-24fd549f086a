import 'package:flutter/foundation.dart';
import '../services/logging_service.dart';
import '../services/error_handling_service.dart';

/// Decorator for handling common service operation patterns
class ServiceDecorators {
  /// Execute a database operation with standardized error handling and logging
  static Future<T> withDatabaseErrorHandling<T>(
    Future<T> Function() operation, {
    required String operationName,
    String? tag,
    Map<String, dynamic>? context,
  }) async {
    try {
      LoggingService.debug('Starting $operationName', tag);
      final result = await operation();
      LoggingService.success('Completed $operationName', tag);
      return result;
    } catch (e) {
      LoggingService.error('Failed $operationName', e, tag);
      
      // Enhance error with context
      if (context != null) {
        LoggingService.debug('Operation context: $context', tag);
      }
      
      // Re-throw with enhanced message
      throw Exception('$operationName failed: ${e.toString()}');
    }
  }

  /// Execute operation with retry logic
  static Future<T> withRetry<T>(
    Future<T> Function() operation, {
    required String operationName,
    int maxRetries = 3,
    Duration delay = const Duration(seconds: 1),
    String? tag,
  }) async {
    int attempts = 0;
    
    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (e) {
        attempts++;
        
        if (attempts >= maxRetries) {
          LoggingService.error('$operationName failed after $maxRetries attempts', e, tag);
          rethrow;
        }
        
        LoggingService.warning('$operationName attempt $attempts failed, retrying...', tag);
        await Future.delayed(delay * attempts); // Exponential backoff
      }
    }
    
    throw Exception('$operationName failed after $maxRetries attempts');
  }

  /// Execute operation with loading state management
  static Future<T> withLoadingState<T>(
    Future<T> Function() operation, {
    required void Function(bool) setLoading,
    required String operationName,
    String? tag,
  }) async {
    try {
      setLoading(true);
      LoggingService.debug('Starting $operationName with loading state', tag);
      
      final result = await operation();
      
      LoggingService.success('Completed $operationName', tag);
      return result;
    } catch (e) {
      LoggingService.error('$operationName failed', e, tag);
      rethrow;
    } finally {
      setLoading(false);
    }
  }
}