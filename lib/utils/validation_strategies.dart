import 'dart:convert';

/// Validation strategies for common data types and operations
class ValidationStrategies {
  /// Safely parse DateTime with fallback
  static DateTime? parseDateTime(dynamic value, {DateTime? fallback}) {
    if (value == null) return fallback;
    
    try {
      if (value is DateTime) return value;
      if (value is String) {
        // Handle common datetime string formats
        final cleanValue = value
            .replaceAll('+00:00', '')
            .replaceAll('Z', '');
        return DateTime.parse(cleanValue);
      }
      return fallback;
    } catch (e) {
      return fallback;
    }
  }

  /// Validate email format
  static bool isValidEmail(String? email) {
    if (email == null || email.isEmpty) return false;
    
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    return emailRegex.hasMatch(email);
  }

  /// Validate UUID format
  static bool isValidUuid(String? uuid) {
    if (uuid == null || uuid.isEmpty) return false;
    
    final uuidRegex = RegExp(
      r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$',
    );
    return uuidRegex.hasMatch(uuid);
  }

  /// Safely extract string from dynamic value
  static String? safeString(dynamic value, {String? fallback}) {
    if (value == null) return fallback;
    if (value is String) return value.isEmpty ? fallback : value;
    return value.toString();
  }

  /// Safely extract integer from dynamic value
  static int? safeInt(dynamic value, {int? fallback}) {
    if (value == null) return fallback;
    if (value is int) return value;
    if (value is double) return value.toInt();
    if (value is String) {
      return int.tryParse(value) ?? fallback;
    }
    return fallback;
  }

  /// Safely extract double from dynamic value
  static double? safeDouble(dynamic value, {double? fallback}) {
    if (value == null) return fallback;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) {
      return double.tryParse(value) ?? fallback;
    }
    return fallback;
  }

  /// Validate temperature value
  static bool isValidTemperature(double? temperature, {String unit = 'celsius'}) {
    if (temperature == null) return false;
    
    switch (unit.toLowerCase()) {
      case 'celsius':
        return temperature >= 30.0 && temperature <= 50.0;
      case 'fahrenheit':
        return temperature >= 86.0 && temperature <= 122.0;
      default:
        return false;
    }
  }

  /// Validate baby age in months
  static bool isValidBabyAge(int? ageInMonths) {
    if (ageInMonths == null) return false;
    return ageInMonths >= 0 && ageInMonths <= 60; // 0-5 years
  }

  /// Validate activity type
  static bool isValidActivityType(String? activityType) {
    if (activityType == null || activityType.isEmpty) return false;
    
    const validTypes = [
      'feeding', 'sleep', 'diaper', 'medicine', 'vaccination',
      'growth', 'milestone', 'temperature', 'note', 'photo'
    ];
    
    return validTypes.contains(activityType.toLowerCase());
  }

  /// Validate baby ID parameter with detailed error messages
  static void validateBabyId(String? babyId, [String? operationName]) {
    if (babyId == null || babyId.isEmpty) {
      throw ArgumentError('Baby ID cannot be null or empty${operationName != null ? ' for $operationName' : ''}');
    }
    if (!isValidUuid(babyId)) {
      throw ArgumentError('Invalid baby ID format${operationName != null ? ' for $operationName' : ''}');
    }
  }

  /// Validate activity type parameter with detailed error messages
  static void validateActivityTypeParam(String? activityType, [String? operationName]) {
    if (activityType == null || activityType.isEmpty) {
      throw ArgumentError('Activity type cannot be null or empty${operationName != null ? ' for $operationName' : ''}');
    }
    if (!isValidActivityType(activityType)) {
      const validTypes = [
        'feeding', 'sleep', 'diaper', 'medicine', 'vaccination',
        'growth', 'milestone', 'temperature', 'note', 'photo'
      ];
      throw ArgumentError('Invalid activity type: $activityType. Valid types: ${validTypes.join(', ')}');
    }
  }

  /// Sanitize user input
  static String sanitizeInput(String? input) {
    if (input == null) return '';
    
    return input
        .trim()
        .replaceAll(RegExp(r'[<>"\']'), '') // Remove potential XSS characters
        .replaceAll(RegExp(r'\s+'), ' '); // Normalize whitespace
  }

  /// Validate JSON structure
  static bool isValidJson(dynamic value) {
    if (value == null) return false;
    if (value is Map<String, dynamic>) return true;
    if (value is List) return true;
    
    try {
      if (value is String) {
        // Try to parse as JSON
        final decoded = jsonDecode(value);
        return decoded is Map || decoded is List;
      }
      return false;
    } catch (e) {
      return false;
    }
  }
}