import 'package:flutter/material.dart';

/// Utility class for managing user avatar role-based styling
class UserAvatarThemeHelper {
  /// Get role-based border color
  static Color getRoleBorderColor(BuildContext context, String role) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    switch (role.toLowerCase()) {
      case 'admin':
      case 'owner':
        return isDark ? Colors.amber[300]! : Colors.amber[600]!;
      case 'parent':
      case 'mother':
      case 'father':
        return theme.colorScheme.primary;
      case 'caregiver':
      case 'babysitter':
        return isDark ? Colors.green[300]! : Colors.green[600]!;
      case 'grandparent':
      case 'grandmother':
      case 'grandfather':
        return isDark ? Colors.purple[300]! : Colors.purple[600]!;
      case 'family':
      case 'relative':
        return isDark ? Colors.blue[300]! : Colors.blue[600]!;
      default:
        return theme.colorScheme.primary;
    }
  }

  /// Get role-based background color for initials
  static Color getRoleBackgroundColor(BuildContext context, String role) {
    return getRoleBorderColor(context, role).withValues(alpha: 0.1);
  }

  /// Get border width based on role importance
  static double getBorderWidth(String role, {double? customWidth}) {
    if (customWidth != null) return customWidth;
    
    switch (role.toLowerCase()) {
      case 'admin':
      case 'owner':
        return 3.0;
      case 'parent':
      case 'mother':
      case 'father':
        return 2.5;
      default:
        return 2.0;
    }
  }

  /// Role configuration data class
  static RoleThemeConfig getRoleConfig(BuildContext context, String role) {
    return RoleThemeConfig(
      borderColor: getRoleBorderColor(context, role),
      backgroundColor: getRoleBackgroundColor(context, role),
      borderWidth: getBorderWidth(role),
    );
  }
}

/// Data class for role theme configuration
class RoleThemeConfig {
  final Color borderColor;
  final Color backgroundColor;
  final double borderWidth;

  const RoleThemeConfig({
    required this.borderColor,
    required this.backgroundColor,
    required this.borderWidth,
  });
}