import 'package:flutter/foundation.dart';

/// Performance monitoring utility for subscription system
class PerformanceMonitor {
  static const bool _enableMonitoring = kDebugMode;
  static final Map<String, DateTime> _operationStartTimes = {};
  static final Map<String, List<int>> _operationDurations = {};
  
  /// Start monitoring an operation
  static void startOperation(String operationName) {
    if (!_enableMonitoring) return;
    _operationStartTimes[operationName] = DateTime.now();
  }
  
  /// End monitoring an operation and log duration
  static void endOperation(String operationName) {
    if (!_enableMonitoring) return;
    
    final startTime = _operationStartTimes[operationName];
    if (startTime == null) return;
    
    final duration = DateTime.now().difference(startTime).inMilliseconds;
    _operationDurations.putIfAbsent(operationName, () => []).add(duration);
    
    // Log if operation is slow
    if (duration > 100) {
      debugPrint('⚠️ Slow operation: $operationName took ${duration}ms');
    }
    
    _operationStartTimes.remove(operationName);
  }
  
  /// Get performance statistics
  static Map<String, Map<String, double>> getStatistics() {
    if (!_enableMonitoring) return {};
    
    final stats = <String, Map<String, double>>{};
    
    for (final entry in _operationDurations.entries) {
      final durations = entry.value;
      if (durations.isEmpty) continue;
      
      final sum = durations.reduce((a, b) => a + b);
      final avg = sum / durations.length;
      final max = durations.reduce((a, b) => a > b ? a : b);
      final min = durations.reduce((a, b) => a < b ? a : b);
      
      stats[entry.key] = {
        'average': avg,
        'max': max.toDouble(),
        'min': min.toDouble(),
        'count': durations.length.toDouble(),
      };
    }
    
    return stats;
  }
  
  /// Clear all performance data
  static void clearStatistics() {
    _operationDurations.clear();
    _operationStartTimes.clear();
  }
  
  /// Log current performance statistics
  static void logStatistics() {
    if (!_enableMonitoring) return;
    
    final stats = getStatistics();
    if (stats.isEmpty) return;
    
    debugPrint('📊 Performance Statistics:');
    for (final entry in stats.entries) {
      final operationStats = entry.value;
      debugPrint('  ${entry.key}:');
      debugPrint('    Average: ${operationStats['average']?.toStringAsFixed(1)}ms');
      debugPrint('    Max: ${operationStats['max']?.toStringAsFixed(1)}ms');
      debugPrint('    Min: ${operationStats['min']?.toStringAsFixed(1)}ms');
      debugPrint('    Count: ${operationStats['count']?.toInt()}');
    }
  }
}

/// Mixin for adding performance monitoring to services
mixin PerformanceMonitoringMixin {
  /// Execute an operation with performance monitoring
  Future<T> monitoredOperation<T>(
    String operationName,
    Future<T> Function() operation,
  ) async {
    PerformanceMonitor.startOperation(operationName);
    try {
      return await operation();
    } finally {
      PerformanceMonitor.endOperation(operationName);
    }
  }
  
  /// Execute a synchronous operation with performance monitoring
  T monitoredSync<T>(
    String operationName,
    T Function() operation,
  ) {
    PerformanceMonitor.startOperation(operationName);
    try {
      return operation();
    } finally {
      PerformanceMonitor.endOperation(operationName);
    }
  }
}