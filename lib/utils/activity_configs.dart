import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Optimized centralized activity configurations with theme-aware colors.
/// 
/// This class provides performance-optimized activity configurations that use
/// the main AppTheme color system for consistency and better theme integration.
/// All colors are designed to maintain accessibility and visual hierarchy.
class ActivityConfigs {
  /// Activity configurations using optimized AppTheme colors
  /// 
  /// Performance optimization: Uses const constructor and AppTheme constants
  /// to avoid creating new Color objects on each access.
  static const Map<String, ActivityConfig> configs = {
    // Core activities - using primary theme colors for consistency
    'feeding': ActivityConfig(
      icon: 'restaurant',
      label: 'Feeding',
      description: 'Breast, bottle, or solid feeding',
      lightColor: AppTheme.primaryLight,
      darkColor: AppTheme.primaryDark,
    ),
    'sleep': ActivityConfig(
      icon: 'bedtime',
      label: 'Sleep',
      description: 'Sleep sessions and quality',
      lightColor: AppTheme.accentLight,
      darkColor: AppTheme.accentDark,
    ),
    'diaper': ActivityConfig(
      icon: 'child_care',
      label: 'Diaper',
      description: 'Wet, dry, or both',
      lightColor: AppTheme.secondaryLight,
      darkColor: AppTheme.secondaryDark,
    ),
    
    // Health activities - using semantic colors for better UX
    'medicine': ActivityConfig(
      icon: 'medication',
      label: 'Medicine',
      description: 'Medications and dosages',
      lightColor: AppTheme.errorLight,
      darkColor: AppTheme.errorDark,
    ),
    'vaccination': ActivityConfig(
      icon: 'vaccines',
      label: 'Vaccination',
      description: 'Vaccine records and immunizations',
      lightColor: AppTheme.successLight,
      darkColor: AppTheme.successDark,
    ),
    'temperature': ActivityConfig(
      icon: 'thermostat',
      label: 'Temperature',
      description: 'Body temperature readings',
      lightColor: AppTheme.errorLight,
      darkColor: AppTheme.errorDark,
    ),
    
    // Development activities - using warning colors for attention
    'potty': ActivityConfig(
      icon: 'wc',
      label: 'Potty',
      description: 'Potty training progress',
      lightColor: AppTheme.warningLight,
      darkColor: AppTheme.warningDark,
    ),
    'growth': ActivityConfig(
      icon: 'trending_up',
      label: 'Growth',
      description: 'Weight, height measurements',
      lightColor: AppTheme.secondaryLight,
      darkColor: AppTheme.secondaryDark,
    ),
    'milestone': ActivityConfig(
      icon: 'emoji_events',
      label: 'Milestones',
      description: 'Developmental milestones tracking',
      lightColor: AppTheme.warningLight,
      darkColor: AppTheme.warningDark,
    ),
    
    // Care activities - using theme colors for consistency
    'pumping': ActivityConfig(
      icon: 'local_drink',
      label: 'Pumping',
      description: 'Breast milk pumping sessions',
      lightColor: AppTheme.accentLight,
      darkColor: AppTheme.accentDark,
    ),
    'tummy_time': ActivityConfig(
      icon: 'fitness_center',
      label: 'Tummy Time',
      description: 'Supervised tummy time',
      lightColor: AppTheme.successLight,
      darkColor: AppTheme.successDark,
    ),
    'story_time': ActivityConfig(
      icon: 'menu_book',
      label: 'Story Time',
      description: 'Reading and storytelling',
      lightColor: AppTheme.warningLight,
      darkColor: AppTheme.warningDark,
    ),
    'screen_time': ActivityConfig(
      icon: 'tv',
      label: 'Screen Time',
      description: 'Educational screen time',
      lightColor: AppTheme.primaryVariantLight,
      darkColor: AppTheme.primaryVariantDark,
    ),
    'skin_to_skin': ActivityConfig(
      icon: 'favorite',
      label: 'Skin to Skin',
      description: 'Bonding time',
      lightColor: AppTheme.errorLight,
      darkColor: AppTheme.errorDark,
    ),
    
    // Play activities - using secondary theme colors
    'outdoor_play': ActivityConfig(
      icon: 'park',
      label: 'Outdoor Play',
      description: 'Fresh air and nature',
      lightColor: AppTheme.successLight,
      darkColor: AppTheme.successDark,
    ),
    'indoor_play': ActivityConfig(
      icon: 'toys',
      label: 'Indoor Play',
      description: 'Indoor activities and games',
      lightColor: AppTheme.secondaryVariantLight,
      darkColor: AppTheme.secondaryVariantDark,
    ),
    
    // Hygiene activities - using primary variants
    'brush_teeth': ActivityConfig(
      icon: 'tooth',
      label: 'Brush Teeth',
      description: 'Dental hygiene routine',
      lightColor: AppTheme.primaryVariantLight,
      darkColor: AppTheme.primaryVariantDark,
    ),
    'bath': ActivityConfig(
      icon: 'bathtub',
      label: 'Bath',
      description: 'Bath time and hygiene',
      lightColor: AppTheme.primaryLight,
      darkColor: AppTheme.primaryDark,
    ),
    
    // Custom - using neutral theme colors
    'custom': ActivityConfig(
      icon: 'add_circle',
      label: 'Custom',
      description: 'Create your own logs',
      lightColor: AppTheme.textSecondaryLight,
      darkColor: AppTheme.textSecondaryDark,
    ),
    
    // Custom schedule types
    'doctor_appointment': ActivityConfig(
      icon: 'local_hospital',
      label: 'Doctor Appointment',
      description: 'Medical visits and checkups',
      lightColor: AppTheme.primaryLight,
      darkColor: AppTheme.primaryDark,
    ),
    'shopping_trip': ActivityConfig(
      icon: 'shopping_cart',
      label: 'Shopping Trip',
      description: 'Errands and shopping',
      lightColor: AppTheme.warningLight,
      darkColor: AppTheme.warningDark,
    ),
    'nap_time': ActivityConfig(
      icon: 'bedtime',
      label: 'Nap Time',
      description: 'Daytime naps',
      lightColor: AppTheme.accentLight,
      darkColor: AppTheme.accentDark,
    ),
    'meal_time': ActivityConfig(
      icon: 'restaurant',
      label: 'Meal Time',
      description: 'Solid food meals',
      lightColor: AppTheme.primaryLight,
      darkColor: AppTheme.primaryDark,
    ),
    'walk_time': ActivityConfig(
      icon: 'directions_walk',
      label: 'Walk Time',
      description: 'Walks and outdoor time',
      lightColor: AppTheme.successLight,
      darkColor: AppTheme.successDark,
    ),
    'nursing_session': ActivityConfig(
      icon: 'child_care',
      label: 'Nursing Session',
      description: 'Breastfeeding sessions',
      lightColor: AppTheme.errorLight,
      darkColor: AppTheme.errorDark,
    ),
    'bottle_feeding': ActivityConfig(
      icon: 'baby_changing_station',
      label: 'Bottle Feeding',
      description: 'Bottle feeding sessions',
      lightColor: AppTheme.accentLight,
      darkColor: AppTheme.accentDark,
    ),
    'scheduled_activity': ActivityConfig(
      icon: 'schedule',
      label: 'Scheduled Activity',
      description: 'Scheduled activities and reminders',
      lightColor: AppTheme.warningLight,
      darkColor: AppTheme.warningDark,
    ),
  };
  
  /// Default configuration for unknown activity types
  static const ActivityConfig defaultConfig = ActivityConfig(
    icon: 'help_outline',
    label: 'Unknown',
    description: 'Unknown activity type',
    lightColor: AppTheme.textSecondaryLight,
    darkColor: AppTheme.textSecondaryDark,
  );
}

/// Configuration for a single activity type
class ActivityConfig {
  final String icon;
  final String label;
  final String description;
  final Color lightColor;
  final Color darkColor;

  const ActivityConfig({
    required this.icon,
    required this.label,
    required this.description,
    required this.lightColor,
    required this.darkColor,
  });
}