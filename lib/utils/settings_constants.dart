class SettingsConstants {
  // UI Constants
  static const double cardBorderRadius = 12.0;
  static const double dialogBorderRadius = 16.0;
  static const double iconSize = 24.0;
  static const double smallIconSize = 20.0;
  static const double avatarSize = 15.0; // in terms of screen width percentage
  
  // Animation durations
  static const int expansionAnimationDuration = 300;
  
  // Default values
  static const String defaultTheme = 'System';
  static const String appVersion = '1.0.0 (Build 1)';
  
  // User roles
  static const List<String> userRoles = [
    'admin',
    'parent',
    'grandparent',
    'babysitter',
    'other_carer',
  ];
  
  // Theme options
  static const List<Map<String, String>> themeOptions = [
    {'title': 'Light', 'value': 'Light', 'icon': 'light_mode'},
    {'title': 'Dark', 'value': 'Dark', 'icon': 'dark_mode'},
    {'title': 'System', 'value': 'System', 'icon': 'settings_brightness'},
  ];
  
  
  // Error messages
  static const String profileUpdateError = 'Failed to update profile';
  static const String profileUpdateSuccess = 'Profile updated successfully!';
  static const String themeChangeError = 'Failed to change theme';
  static const String noUserProfileError = 'No user profile found';
  static const String themeServiceUnavailable = 'Theme service not available';
}