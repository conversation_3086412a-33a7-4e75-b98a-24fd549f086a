/// Extensions for better null safety handling
extension NullSafetyExtensions on Object? {
  /// Safe cast with fallback
  T? safeCast<T>() {
    return this is T ? this as T : null;
  }
  
  /// Check if object is not null and not empty (for strings/collections)
  bool get isNotNullOrEmpty {
    if (this == null) return false;
    if (this is String) return (this as String).isNotEmpty;
    if (this is Iterable) return (this as Iterable).isNotEmpty;
    if (this is Map) return (this as Map).isNotEmpty;
    return true;
  }
}

extension StringNullSafety on String? {
  /// Get string or default value
  String orDefault([String defaultValue = '']) {
    return this?.isNotEmpty == true ? this! : defaultValue;
  }
  
  /// Check if string is null or empty
  bool get isNullOrEmpty => this?.isEmpty ?? true;
  
  /// Check if string is not null and not empty
  bool get isNotNullOrEmpty => !isNullOrEmpty;
}

extension ListNullSafety<T> on List<T>? {
  /// Get list or empty list
  List<T> orEmpty() => this ?? <T>[];
  
  /// Check if list is null or empty
  bool get isNullOrEmpty => this?.isEmpty ?? true;
  
  /// Safe access to first element
  T? get firstOrNull => isNullOrEmpty ? null : this!.first;
  
  /// Safe access to last element
  T? get lastOrNull => isNullOrEmpty ? null : this!.last;
}