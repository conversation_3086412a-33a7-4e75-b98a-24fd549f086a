import 'package:flutter/material.dart';
import '../widgets/custom_icon_widget.dart';
import 'activity_type_config.dart';

/// Centralized activity icon management system
/// This ensures consistent icon usage across all components
class ActivityIconManager {
  
  /// Get a consistent icon widget for any activity type
  static Widget getActivityIcon({
    required String activityType,
    required double size,
    Color? color,
    String? fallbackActivityType,
  }) {
    // Try the primary activity type first
    String iconName = ActivityTypeConfig.getIcon(activityType);
    Color iconColor = color ?? ActivityTypeConfig.getColor(activityType);
    
    // If the primary type doesn't have a valid config and we have a fallback, try the fallback
    if (iconName == 'help_outline' && fallbackActivityType != null) {
      final fallbackIconName = ActivityTypeConfig.getIcon(fallbackActivityType);
      if (fallbackIconName != 'help_outline') {
        iconName = fallbackIconName;
        iconColor = color ?? ActivityTypeConfig.getColor(fallbackActivityType);
      }
    }
    
    return CustomIconWidget(
      iconName: iconName,
      size: size,
      color: iconColor,
    );
  }
  
  /// Get a consistent icon widget with background container
  static Widget getActivityIconWithBackground({
    required String activityType,
    required double size,
    Color? color,
    double? containerPadding,
    double? backgroundOpacity,
  }) {
    final iconColor = color ?? ActivityTypeConfig.getColor(activityType);
    final padding = containerPadding ?? size * 0.3;
    final opacity = backgroundOpacity ?? 0.1;
    
    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: opacity),
        borderRadius: BorderRadius.circular(8),
      ),
      child: getActivityIcon(
        activityType: activityType,
        size: size,
        color: iconColor,
      ),
    );
  }
  
  /// Get a consistent circular icon widget with background
  static Widget getActivityIconCircular({
    required String activityType,
    required double size,
    Color? color,
    double? containerPadding,
    double? backgroundOpacity,
  }) {
    final iconColor = color ?? ActivityTypeConfig.getColor(activityType);
    final padding = containerPadding ?? size * 0.3;
    final opacity = backgroundOpacity ?? 0.1;
    
    return Container(
      padding: EdgeInsets.all(padding),
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: opacity),
        shape: BoxShape.circle,
      ),
      child: getActivityIcon(
        activityType: activityType,
        size: size,
        color: iconColor,
      ),
    );
  }
  
  /// Validate if an activity type has a valid icon configuration
  static bool hasValidIcon(String activityType) {
    return ActivityTypeConfig.getConfig(activityType) != null;
  }
  
  /// Get all available activity types that have icon configurations
  static List<String> getAllActivityTypes() {
    return ActivityTypeConfig.getAllConfigs()
        .map((config) => config['type'] as String)
        .toList();
  }
  
  /// Get icon name as string for a given activity type
  static String getIconName(String activityType) {
    return ActivityTypeConfig.getIcon(activityType);
  }
  
  /// Get icon color for a given activity type
  static Color getIconColor(String activityType) {
    return ActivityTypeConfig.getColor(activityType);
  }
}
