import 'package:flutter/material.dart';
import '../theme/theme_aware_colors.dart';

/// Helper class for creating consistent theme-aware form components
class FormThemeHelper {
  /// Creates a theme-aware InputDecoration for text fields
  static InputDecoration getInputDecoration(
    BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
    String? suffixText,
    bool filled = true,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      suffixText: suffixText,
      filled: filled,
      fillColor: Theme.of(context).colorScheme.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Theme.of(context).colorScheme.error),
      ),
    );
  }

  /// Creates a theme-aware Switch widget
  static Switch getThemedSwitch(
    BuildContext context, {
    required bool value,
    required ValueChanged<bool>? onChanged,
  }) {
    return Switch(
      value: value,
      onChanged: onChanged,
      activeColor: Theme.of(context).colorScheme.primary,
      activeTrackColor: Theme.of(context).colorScheme.primary.withOpacity(0.5),
      inactiveThumbColor: Theme.of(context).colorScheme.outline,
      inactiveTrackColor: Theme.of(context).colorScheme.surfaceVariant,
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// Creates a theme-aware Checkbox widget
  static Checkbox getThemedCheckbox(
    BuildContext context, {
    required bool? value,
    required ValueChanged<bool?>? onChanged,
  }) {
    return Checkbox(
      value: value,
      onChanged: onChanged,
      activeColor: Theme.of(context).colorScheme.primary,
      checkColor: Theme.of(context).colorScheme.onPrimary,
      side: BorderSide(
        color: ThemeAwareColors.getOutlineColor(context),
        width: 2,
      ),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// Creates a theme-aware Radio widget
  static Radio<T> getThemedRadio<T>(
    BuildContext context, {
    required T value,
    required T? groupValue,
    required ValueChanged<T?>? onChanged,
  }) {
    return Radio<T>(
      value: value,
      groupValue: groupValue,
      onChanged: onChanged,
      activeColor: Theme.of(context).colorScheme.primary,
      fillColor: MaterialStateProperty.resolveWith<Color>((states) {
        if (states.contains(MaterialState.selected)) {
          return Theme.of(context).colorScheme.primary;
        }
        return ThemeAwareColors.getOutlineColor(context);
      }),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// Creates a theme-aware ElevatedButton
  static ElevatedButton getThemedElevatedButton(
    BuildContext context, {
    required VoidCallback? onPressed,
    required Widget child,
    bool isPrimary = true,
  }) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: isPrimary 
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.secondary,
        foregroundColor: isPrimary
            ? Theme.of(context).colorScheme.onPrimary
            : Theme.of(context).colorScheme.onSecondary,
        disabledBackgroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.12),
        disabledForegroundColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.38),
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: child,
    );
  }

  /// Creates a theme-aware OutlinedButton
  static OutlinedButton getThemedOutlinedButton(
    BuildContext context, {
    required VoidCallback? onPressed,
    required Widget child,
  }) {
    return OutlinedButton(
      onPressed: onPressed,
      style: OutlinedButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
        side: BorderSide(color: Theme.of(context).colorScheme.primary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: child,
    );
  }

  /// Creates a theme-aware TextButton
  static TextButton getThemedTextButton(
    BuildContext context, {
    required VoidCallback? onPressed,
    required Widget child,
  }) {
    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: child,
    );
  }

  /// Creates a theme-aware DropdownButtonFormField
  static DropdownButtonFormField<T> getThemedDropdown<T>(
    BuildContext context, {
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required ValueChanged<T?>? onChanged,
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
  }) {
    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      decoration: getInputDecoration(
        context,
        labelText: labelText,
        hintText: hintText,
        prefixIcon: prefixIcon,
      ),
      dropdownColor: Theme.of(context).colorScheme.surface,
      style: Theme.of(context).textTheme.bodyMedium,
      icon: Icon(
        Icons.arrow_drop_down,
        color: ThemeAwareColors.getIconColor(context),
      ),
    );
  }

  /// Creates a theme-aware container for form sections
  static Container getFormSectionContainer(
    BuildContext context, {
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getOutlineColor(context).withOpacity(0.5),
        ),
      ),
      child: child,
    );
  }

  /// Creates a theme-aware form field label
  static Widget getFormFieldLabel(
    BuildContext context, {
    required String text,
    bool isRequired = false,
  }) {
    return Row(
      children: [
        Text(
          text,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        if (isRequired) ...[
          const SizedBox(width: 4),
          Text(
            '*',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ],
    );
  }
}