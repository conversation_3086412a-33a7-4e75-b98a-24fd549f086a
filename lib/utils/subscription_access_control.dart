import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../presentation/subscription/controllers/subscription_controller.dart';
import '../services/feature_access_service.dart';
import '../models/feature_access.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';
import '../presentation/subscription/subscription_screen.dart';

/// Utility class for subscription-based access control throughout the app
class SubscriptionAccessControl {
  // Cache for frequently accessed subscription data
  static SubscriptionInfo? _cachedSubscription;
  static DateTime? _cacheTimestamp;
  static const Duration _cacheTimeout = Duration(seconds: 30);
  
  /// Get cached subscription or fetch fresh data
  static SubscriptionInfo _getCachedSubscription(BuildContext context) {
    final now = DateTime.now();
    if (_cachedSubscription != null && 
        _cacheTimestamp != null && 
        now.difference(_cacheTimestamp!) < _cacheTimeout) {
      return _cachedSubscription!;
    }
    
    final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
    _cachedSubscription = subscriptionController.currentSubscription;
    _cacheTimestamp = now;
    return _cachedSubscription!;
  }
  
  /// Clear cache when subscription changes
  static void _clearCache() {
    _cachedSubscription = null;
    _cacheTimestamp = null;
  }
  /// Check if user has access to a specific feature
  static bool hasFeatureAccess(BuildContext context, String featureName) {
    try {
      final subscription = _getCachedSubscription(context);
      // Use cached subscription data instead of repeated Provider lookups
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.hasFeature(featureName);
    } catch (e) {
      debugPrint('Error checking feature access for $featureName: $e');
      return false; // Fail closed - deny access if error
    }
  }

  /// Check if user can access a specific screen
  static bool canAccessScreen(BuildContext context, String screenName) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.canAccessScreen(screenName);
    } catch (e) {
      debugPrint('Error checking screen access for $screenName: $e');
      return false; // Fail closed - deny access if error
    }
  }

  /// Get restriction message for a feature
  static String getRestrictionMessage(BuildContext context, String featureName) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.getRestrictionMessage(featureName);
    } catch (e) {
      debugPrint('Error getting restriction message for $featureName: $e');
      return 'This feature requires Premium plan. Upgrade to unlock all features.';
    }
  }

  /// Get feature limit for a specific feature
  static int? getFeatureLimit(BuildContext context, String featureName) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.getFeatureLimit(featureName);
    } catch (e) {
      debugPrint('Error getting feature limit for $featureName: $e');
      return 0; // Fail closed - no access if error
    }
  }

  /// Show upgrade prompt for a specific feature
  static void showUpgradePrompt(
    BuildContext context, 
    String featureName, {
    String? customTitle,
    String? customMessage,
  }) {
    final title = customTitle ?? 'Upgrade to Premium';
    final message = customMessage ?? getRestrictionMessage(context, featureName);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.star,
          color: Theme.of(context).colorScheme.primary,
          size: 48,
        ),
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(message),
            const SizedBox(height: 16),
            Text(
              'Upgrade to Premium to unlock this feature and many more!',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 14,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Maybe Later'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(
                    initialFocus: 'premium',
                  ),
                ),
              );
            },
            child: Text('Upgrade Now'),
          ),
        ],
      ),
    );
  }

  /// Show upgrade banner for a specific feature
  static Widget buildUpgradeBanner(
    BuildContext context,
    String featureName, {
    String? customMessage,
    VoidCallback? onUpgrade,
  }) {
    final message = customMessage ?? getRestrictionMessage(context, featureName);
    
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withOpacity(0.1),
            Theme.of(context).colorScheme.secondary.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.star,
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Premium Feature',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 14,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          ElevatedButton(
            onPressed: onUpgrade ?? () => showUpgradePrompt(context, featureName),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
            child: Text('Upgrade'),
          ),
        ],
      ),
    );
  }

  /// Wrap a widget with access control
  static Widget wrapWithAccessControl(
    BuildContext context,
    String featureName,
    Widget child, {
    Widget? fallback,
    bool showUpgradePrompt = true,
  }) {
    if (hasFeatureAccess(context, featureName)) {
      return child;
    }

    if (fallback != null) {
      return fallback;
    }

    if (showUpgradePrompt) {
      return buildUpgradeBanner(context, featureName);
    }

    return const SizedBox.shrink();
  }

  /// Check if user is on premium plan
  static bool isPremiumUser(BuildContext context) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.hasPremiumAccess;
    } catch (e) {
      debugPrint('Error checking premium status: $e');
      return false;
    }
  }

  /// Check if user is on free plan
  static bool isFreeUser(BuildContext context) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.isOnFreePlan;
    } catch (e) {
      debugPrint('Error checking free plan status: $e');
      return true; // Default to free if error
    }
  }

  /// Get current subscription plan name
  static String getCurrentPlanName(BuildContext context) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.currentSubscription.planName;
    } catch (e) {
      debugPrint('Error getting plan name: $e');
      return 'Free';
    }
  }

  /// Get subscription status message
  static String getSubscriptionStatusMessage(BuildContext context) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.currentSubscription.statusMessage;
    } catch (e) {
      debugPrint('Error getting status message: $e');
      return 'Free plan';
    }
  }

  /// Check if subscription needs attention
  static bool needsAttention(BuildContext context) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.currentSubscription.needsAttention;
    } catch (e) {
      debugPrint('Error checking attention status: $e');
      return false;
    }
  }

  /// Get upgrade message if applicable
  static String? getUpgradeMessage(BuildContext context) {
    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      return subscriptionController.currentSubscription.upgradeMessage;
    } catch (e) {
      debugPrint('Error getting upgrade message: $e');
      return null;
    }
  }
}

/// Widget that conditionally shows content based on subscription access
class SubscriptionGate extends StatelessWidget {
  final String featureName;
  final Widget child;
  final Widget? fallback;
  final bool showUpgradePrompt;
  final String? customUpgradeMessage;

  const SubscriptionGate({
    super.key,
    required this.featureName,
    required this.child,
    this.fallback,
    this.showUpgradePrompt = true,
    this.customUpgradeMessage,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SubscriptionController>(
      builder: (context, subscriptionController, _) {
        if (subscriptionController.hasFeature(featureName)) {
          return child;
        }

        if (fallback != null) {
          return fallback!;
        }

        if (showUpgradePrompt) {
          return SubscriptionAccessControl.buildUpgradeBanner(
            context,
            featureName,
            customMessage: customUpgradeMessage,
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}

/// Widget that shows subscription status and upgrade options
class SubscriptionStatusWidget extends StatelessWidget {
  final bool showUpgradeButton;
  final VoidCallback? onUpgrade;

  const SubscriptionStatusWidget({
    super.key,
    this.showUpgradeButton = true,
    this.onUpgrade,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<SubscriptionController>(
      builder: (context, subscriptionController, _) {
        final subscription = subscriptionController.currentSubscription;
        final theme = Theme.of(context);

        return Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: subscription.isPremium
                ? theme.colorScheme.primaryContainer
                : theme.colorScheme.surfaceVariant,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(
                subscription.isPremium ? Icons.star : Icons.star_border,
                color: subscription.isPremium
                    ? theme.colorScheme.primary
                    : theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      subscription.planName,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: subscription.isPremium
                            ? theme.colorScheme.onPrimaryContainer
                            : theme.colorScheme.onSurfaceVariant,
                      ),
                    ),
                    Text(
                      subscription.statusMessage,
                      style: TextStyle(
                        fontSize: 12,
                        color: subscription.isPremium
                            ? theme.colorScheme.onPrimaryContainer.withOpacity(0.8)
                            : theme.colorScheme.onSurfaceVariant.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              if (showUpgradeButton && !subscription.isPremium)
                ElevatedButton(
                  onPressed: onUpgrade ?? () {
                    Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const SubscriptionScreen(
                          initialFocus: 'premium',
                        ),
                      ),
                    );
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: Text('Upgrade'),
                ),
            ],
          ),
        );
      },
    );
  }
}