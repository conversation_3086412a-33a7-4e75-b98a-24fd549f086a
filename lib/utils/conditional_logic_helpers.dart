import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import '../core/app_export.dart';
import '../services/ai_insights_state_manager.dart';

/// Helper class for complex conditional logic to improve readability
class ConditionalLogicHelpers {
  ConditionalLogicHelpers._();

  /// Check if user can perform manual AI refresh
  static bool canPerformManualRefresh(
    AIInsightsStateManager aiInsightsManager, {
    bool isDevMode = false,
  }) {
    if (aiInsightsManager.isUpdating) return false;
    if (isDevMode) return true;
    return aiInsightsManager.canManualRefresh();
  }

  /// Check if insights data is sufficient for analysis
  static bool hasValidInsightsData(Map<String, dynamic>? insights) {
    if (insights == null || insights.isEmpty) return false;
    
    // Check for minimum required data points
    final requiredCategories = ['sleepAnalysis', 'feedingAnalysis'];
    for (final category in requiredCategories) {
      final analysis = insights[category];
      if (analysis is Map<String, dynamic>) {
        final confidence = analysis['confidence'] as num?;
        if (confidence != null && confidence > 0.1) {
          return true;
        }
      }
    }
    
    return false;
  }

  /// Check if baby profile is valid for AI analysis
  static bool isValidBabyProfile(BabyProfile? profile) {
    if (profile == null) return false;
    if (profile.id.isEmpty) return false;
    if (profile.name.isEmpty) return false;
    
    // Check if baby is old enough for meaningful analysis (at least 1 week)
    final age = DateTime.now().difference(profile.birthDate);
    return age.inDays >= 7;
  }

  /// Check if user should see demo mode option (deprecated)
  static bool shouldShowDemoMode(
    BabyProfile? profile,
    List<ActivityLog> activities,
  ) {
    // Show demo if no valid profile or insufficient activity data
    if (!isValidBabyProfile(profile)) return true;
    if (activities.length < 10) return true;
    
    // Show demo if activities are too old (older than 7 days)
    final recentActivities = activities.where((activity) {
      final age = DateTime.now().difference(activity.timestamp);
      return age.inDays <= 7;
    });
    
    return recentActivities.length < 5;
  }

  /// Check if error should be shown to user vs logged silently
  static bool shouldShowErrorToUser(dynamic error) {
    if (error == null) return false;
    
    final errorString = error.toString().toLowerCase();
    
    // Don't show network errors in debug mode (likely dev environment issues)
    if (kDebugMode && errorString.contains('network')) return false;
    
    // Don't show rate limiting errors (handled separately)
    if (errorString.contains('rate limit')) return false;
    
    // Don't show authentication errors (handled by auth service)
    if (errorString.contains('unauthorized') || errorString.contains('401')) return false;
    
    return true;
  }

  /// Check if widget should use loading state
  static bool shouldShowLoading(
    bool isLoading,
    bool hasData,
    String? errorMessage,
  ) {
    return isLoading && !hasData && errorMessage == null;
  }

  /// Check if widget should show error state
  static bool shouldShowError(
    bool isLoading,
    String? errorMessage,
  ) {
    return !isLoading && errorMessage != null;
  }

  /// Check if widget should show empty state
  static bool shouldShowEmptyState(
    bool isLoading,
    bool hasData,
    String? errorMessage,
  ) {
    return !isLoading && !hasData && errorMessage == null;
  }

  /// Check if theme color should use high contrast
  static bool shouldUseHighContrast(
    BuildContext context,
    bool isImportantElement,
  ) {
    final brightness = Theme.of(context).brightness;
    final isAccessibilityEnabled = MediaQuery.of(context).accessibleNavigation;
    
    return isImportantElement || isAccessibilityEnabled || brightness == Brightness.dark;
  }

  /// Check if animation should be reduced for accessibility
  static bool shouldReduceAnimations(BuildContext context) {
    return MediaQuery.of(context).disableAnimations;
  }

  /// Check if text should be larger for accessibility
  static bool shouldUseLargerText(BuildContext context) {
    final textScaleFactor = MediaQuery.of(context).textScaleFactor;
    return textScaleFactor > 1.2;
  }

  /// Check if user is in development environment
  static bool isDevelopmentEnvironment() {
    return kDebugMode || const bool.fromEnvironment('DEVELOPMENT_MODE');
  }

  /// Check if feature flag is enabled
  static bool isFeatureEnabled(String featureName) {
    // This could be extended to check remote feature flags
    switch (featureName) {
      case 'ai_insights_demo_mode':
        return true;
      case 'advanced_analytics':
        return isDevelopmentEnvironment();
      case 'beta_features':
        return const bool.fromEnvironment('ENABLE_BETA_FEATURES');
      default:
        return false;
    }
  }

  /// Check if data is fresh enough for display
  static bool isDataFresh(
    DateTime? lastUpdate, {
    Duration maxAge = const Duration(hours: 24),
  }) {
    if (lastUpdate == null) return false;
    
    final age = DateTime.now().difference(lastUpdate);
    return age <= maxAge;
  }

  /// Check if user activity level warrants AI analysis
  static bool hasMinimumActivityForAnalysis(List<ActivityLog> activities) {
    if (activities.length < 20) return false;
    
    // Check for activity diversity (at least 3 different types)
    final activityTypes = activities.map((a) => a.type).toSet();
    if (activityTypes.length < 3) return false;
    
    // Check for recent activity (within last 3 days)
    final recentActivities = activities.where((activity) {
      final age = DateTime.now().difference(activity.timestamp);
      return age.inDays <= 3;
    });
    
    return recentActivities.length >= 5;
  }

  /// Check if insights should be refreshed automatically
  static bool shouldAutoRefreshInsights(
    DateTime? lastUpdate,
    List<ActivityLog> newActivities,
  ) {
    if (lastUpdate == null) return true;
    
    // Don't refresh if updated recently (within 6 hours)
    final timeSinceUpdate = DateTime.now().difference(lastUpdate);
    if (timeSinceUpdate.inHours < 6) return false;
    
    // Refresh if significant new activity since last update
    final activitiesSinceUpdate = newActivities.where((activity) {
      return activity.timestamp.isAfter(lastUpdate);
    });
    
    return activitiesSinceUpdate.length >= 10;
  }
}