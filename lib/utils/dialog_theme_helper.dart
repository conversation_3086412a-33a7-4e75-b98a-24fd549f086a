import 'package:flutter/material.dart';
import '../theme/theme_aware_colors.dart';

/// Helper class for applying consistent theme-aware styling to dialogs, modals, and bottom sheets
class DialogThemeHelper {
  /// Creates a theme-aware AlertDialog with consistent styling
  static AlertDialog createThemedAlertDialog({
    required BuildContext context,
    Widget? title,
    Widget? content,
    List<Widget>? actions,
    EdgeInsetsGeometry? contentPadding,
    EdgeInsetsGeometry? actionsPadding,
    EdgeInsetsGeometry? titlePadding,
    EdgeInsetsGeometry? insetPadding,
    ShapeBorder? shape,
    Color? backgroundColor,
    Color? surfaceTintColor,
    double? elevation,
    String? semanticLabel,
    EdgeInsetsGeometry? buttonPadding,
    MainAxisAlignment? actionsAlignment,
    OverflowBarAlignment? actionsOverflowAlignment,
    VerticalDirection? actionsOverflowDirection,
    double? actionsOverflowButtonSpacing,
    bool scrollable = false,
    Clip clipBehavior = Clip.none,
    AlignmentGeometry? alignment,
  }) {
    return AlertDialog(
      title: title,
      content: content,
      actions: actions,
      contentPadding: contentPadding,
      actionsPadding: actionsPadding,
      titlePadding: titlePadding,
      insetPadding: insetPadding as EdgeInsets?,
      shape: shape ?? RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: backgroundColor ?? Theme.of(context).dialogBackgroundColor,
      surfaceTintColor: surfaceTintColor ?? Theme.of(context).colorScheme.surfaceTint,
      elevation: elevation ?? 6,
      semanticLabel: semanticLabel,
      buttonPadding: buttonPadding,
      actionsAlignment: actionsAlignment,
      actionsOverflowAlignment: actionsOverflowAlignment,
      actionsOverflowDirection: actionsOverflowDirection,
      actionsOverflowButtonSpacing: actionsOverflowButtonSpacing,
      scrollable: scrollable,
      clipBehavior: clipBehavior,
      alignment: alignment,
    );
  }

  /// Creates a theme-aware SnackBar with consistent styling
  static SnackBar createThemedSnackBar({
    required BuildContext context,
    required Widget content,
    Color? backgroundColor,
    double? elevation,
    EdgeInsetsGeometry? margin,
    EdgeInsetsGeometry? padding,
    double? width,
    ShapeBorder? shape,
    SnackBarBehavior? behavior,
    SnackBarAction? action,
    Duration? duration,
    Animation<double>? animation,
    VoidCallback? onVisible,
    DismissDirection dismissDirection = DismissDirection.down,
    Clip clipBehavior = Clip.hardEdge,
    bool showCloseIcon = false,
    Color? closeIconColor,
  }) {
    return SnackBar(
      content: content,
      backgroundColor: backgroundColor ?? Theme.of(context).snackBarTheme.backgroundColor,
      elevation: elevation,
      margin: margin,
      padding: padding,
      width: width,
      shape: shape ?? RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      behavior: behavior ?? SnackBarBehavior.floating,
      action: action,
      duration: duration ?? const Duration(seconds: 4),
      animation: animation,
      onVisible: onVisible,
      dismissDirection: dismissDirection,
      clipBehavior: clipBehavior,
      showCloseIcon: showCloseIcon,
      closeIconColor: closeIconColor ?? Theme.of(context).colorScheme.onInverseSurface,
    );
  }

  /// Creates theme-aware success SnackBar
  static SnackBar createSuccessSnackBar({
    required BuildContext context,
    required String message,
    SnackBarAction? action,
    Duration? duration,
  }) {
    return createThemedSnackBar(
      context: context,
      content: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.white,
        ),
      ),
      backgroundColor: ThemeAwareColors.getSuccessColor(context),
      action: action,
      duration: duration,
    );
  }

  /// Creates theme-aware error SnackBar
  static SnackBar createErrorSnackBar({
    required BuildContext context,
    required String message,
    SnackBarAction? action,
    Duration? duration,
  }) {
    return createThemedSnackBar(
      context: context,
      content: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Colors.white,
        ),
      ),
      backgroundColor: Theme.of(context).colorScheme.error,
      action: action,
      duration: duration,
    );
  }

  /// Creates theme-aware info SnackBar
  static SnackBar createInfoSnackBar({
    required BuildContext context,
    required String message,
    SnackBarAction? action,
    Duration? duration,
  }) {
    return createThemedSnackBar(
      context: context,
      content: Text(
        message,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onPrimary,
        ),
      ),
      backgroundColor: Theme.of(context).colorScheme.primary,
      action: action,
      duration: duration,
    );
  }

  /// Creates theme-aware bottom sheet decoration
  static BoxDecoration createBottomSheetDecoration(BuildContext context) {
    return BoxDecoration(
      color: Theme.of(context).bottomSheetTheme.backgroundColor ?? 
             Theme.of(context).scaffoldBackgroundColor,
      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      boxShadow: [
        BoxShadow(
          color: Theme.of(context).shadowColor.withOpacity(0.1),
          blurRadius: 10,
          offset: const Offset(0, -2),
        ),
      ],
    );
  }

  /// Creates theme-aware modal barrier color
  static Color getModalBarrierColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.black54
        : Colors.black26;
  }

  /// Creates theme-aware dialog title style
  static TextStyle? getDialogTitleStyle(BuildContext context) {
    return Theme.of(context).textTheme.titleLarge?.copyWith(
      fontWeight: FontWeight.w600,
      color: Theme.of(context).colorScheme.onSurface,
    );
  }

  /// Creates theme-aware dialog content style
  static TextStyle? getDialogContentStyle(BuildContext context) {
    return Theme.of(context).textTheme.bodyMedium?.copyWith(
      color: Theme.of(context).colorScheme.onSurface,
    );
  }

  /// Creates theme-aware dialog action button style
  static ButtonStyle getDialogActionButtonStyle(BuildContext context, {bool isPrimary = false}) {
    if (isPrimary) {
      return ElevatedButton.styleFrom(
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      );
    } else {
      return TextButton.styleFrom(
        foregroundColor: Theme.of(context).colorScheme.primary,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      );
    }
  }

  /// Creates theme-aware destructive action button style
  static ButtonStyle getDestructiveActionButtonStyle(BuildContext context) {
    return TextButton.styleFrom(
      foregroundColor: Theme.of(context).colorScheme.error,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
    );
  }
}