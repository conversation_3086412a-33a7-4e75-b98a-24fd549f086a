/// Constants for growth chart configuration and calculations
class ChartConstants {
  ChartConstants._();
  
  /// Average days per month for age calculations
  static const double averageDaysPerMonth = 30.44;
  
  /// Default chart animation duration
  static const Duration chartAnimationDuration = Duration(milliseconds: 600);
  
  /// Chart loading delay for performance optimization
  static const Duration chartLoadingDelay = Duration(milliseconds: 100);
  
  /// Chart update delay for smooth transitions
  static const Duration chartUpdateDelay = Duration(milliseconds: 200);
  
  /// WHO percentile line colors
  static const Map<String, int> percentileColors = {
    '3rd': 0xFFE53E3E,   // Bright Red
    '15th': 0xFFFF8C00,  // Bright Orange
    '50th': 0xFF2563EB,  // Bright Blue
    '85th': 0xFF059669,  // Bright Green
    '97th': 0xFF7C3AED,  // Bright Purple
  };
  
  /// Measurement type constants
  static const int measurementTypeWeight = 0;
  static const int measurementTypeHeight = 1;
  static const int measurementTypeHeadCircumference = 2;
  
  /// Chart dimensions
  static const double chartHeight = 260.0;
  static const double chartPadding = 12.0;
  static const double legendItemSpacing = 16.0;
  
  /// Animation curves
  // static const Curve chartAnimationCurve = Curves.easeInOutCubic; // Commented out - missing import
}