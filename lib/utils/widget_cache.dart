import 'package:flutter/material.dart';

/// Widget caching utilities to improve performance
class WidgetCache {
  static final Map<String, Widget> _cache = {};
  
  /// Cache a widget with a key
  static void cache(String key, Widget widget) {
    _cache[key] = widget;
  }
  
  /// Get cached widget
  static Widget? get(String key) {
    return _cache[key];
  }
  
  /// Clear cache
  static void clear() {
    _cache.clear();
  }
  
  /// Clear specific cache entry
  static void remove(String key) {
    _cache.remove(key);
  }
  
  /// Get or create cached widget
  static Widget getOrCreate(String key, Widget Function() builder) {
    return _cache[key] ??= builder();
  }
}

/// Mixin for widgets that need caching capabilities
mixin CacheableWidget on Widget {
  String get cacheKey;
  
  Widget buildCached(BuildContext context) {
    return WidgetCache.getOrCreate(cacheKey, () => build(context));
  }
}

/// Performance-optimized list item widget
class OptimizedListItem extends StatelessWidget {
  final String id;
  final Widget child;
  final VoidCallback? onTap;
  
  const OptimizedListItem({
    super.key,
    required this.id,
    required this.child,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: GestureDetector(
        onTap: onTap,
        child: child,
      ),
    );
  }
}

/// Optimized image widget with caching
class CachedImageWidget extends StatelessWidget {
  final String imageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  
  const CachedImageWidget({
    super.key,
    required this.imageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
  });
  
  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: Image.network(
        imageUrl,
        width: width,
        height: height,
        fit: fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return placeholder ?? const CircularProgressIndicator();
        },
        errorBuilder: (context, error, stackTrace) {
          return errorWidget ?? const Icon(Icons.error);
        },
      ),
    );
  }
}