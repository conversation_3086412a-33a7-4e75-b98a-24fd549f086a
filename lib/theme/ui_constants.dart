import 'package:sizer/sizer.dart';
import 'package:flutter/material.dart';

/// UI constants for consistent spacing and sizing
class UIConstants {
  // Spacing
  static double get smallSpacing => 0.5.h;
  static double get mediumSpacing => 1.h;
  static double get largeSpacing => 2.h;
  static double get extraLargeSpacing => 3.h;
  
  // Quick Action Button Constants
  static double get quickActionButtonHeight => 8.h;
  static double get quickActionIconPadding => 2.5.w;
  static double get quickActionIconSize => 5.w;
  static double get quickActionSpacing => 2.w;
  
  // Border Radius
  static double get defaultBorderRadius => 12.0;
  static double get smallBorderRadius => 8.0;
  static double get largeBorderRadius => 16.0;
  
  // Shadow
  static double get defaultShadowBlur => 6.0;
  static Offset get defaultShadowOffset => const Offset(0, 2);
  static double get defaultShadowOpacity => 0.03;
  
  // Page Indicator
  static double get pageIndicatorSize => 1.5.w;
  static double get pageIndicatorSpacing => 0.5.w;
  static double get pageIndicatorOpacity => 0.4;
  
  // Form Elements
  static double get formFieldPadding => 3.w;
  static double get formFieldVerticalPadding => 2.h;
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
}