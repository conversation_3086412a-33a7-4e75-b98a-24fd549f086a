import 'package:flutter/material.dart';
import 'app_theme.dart';

/// Optimized helper class for theme-aware color access throughout the app.
/// 
/// This class provides high-performance access to theme-appropriate colors by
/// leveraging Flutter's built-in Material 3 ColorScheme where possible, with
/// fallback to custom AppTheme colors for brand-specific requirements.
/// 
/// ## Performance Optimizations:
/// - Uses Material 3 ColorScheme for optimal performance and consistency
/// - Caches theme brightness detection to avoid repeated system calls
/// - Provides const constructors and static methods to minimize object creation
/// - Leverages Flutter's built-in theme inheritance for automatic updates
/// 
/// ## Theme-Aware Implementation Patterns:
/// 
/// ### 1. Primary Theme Colors (Recommended)
/// ```dart
/// // Use Material 3 ColorScheme for best performance
/// color: Theme.of(context).colorScheme.primary
/// backgroundColor: Theme.of(context).scaffoldBackgroundColor
/// 
/// // Or use helper methods for consistency
/// color: ThemeAwareColors.getPrimaryColor(context)
/// backgroundColor: ThemeAwareColors.getBackgroundColor(context)
/// ```
/// 
/// ### 2. Custom Brand Colors
/// ```dart
/// // Use AppTheme colors for brand-specific requirements
/// successColor: ThemeAwareColors.getSuccessColor(context)
/// warningColor: ThemeAwareColors.getWarningColor(context)
/// ```
/// 
/// ### 3. Container and Surface Styling
/// ```dart
/// // Use helper methods for consistent container styling
/// decoration: ThemeAwareColors.getContainerDecoration(context)
/// selectedDecoration: ThemeAwareColors.getSelectedContainerDecoration(
///   context, 
///   isSelected: true
/// )
/// ```
/// 
/// ### 4. State-Based Colors
/// ```dart
/// // Use state-aware color methods for interactive elements
/// iconColor: ThemeAwareColors.getIconColorForState(
///   context,
///   isSelected: isActive,
/// )
/// ```
/// 
/// ## Best Practices:
/// - Always pass BuildContext to ensure proper theme inheritance
/// - Use Material 3 colors (primary, secondary, surface) when possible
/// - Use custom AppTheme colors only for brand-specific requirements
/// - Leverage helper methods for consistent styling patterns
/// - Test all colors in both light and dark themes for accessibility
/// 
/// ## Accessibility Compliance:
/// All colors provided by this class maintain WCAG contrast ratios and
/// are designed to work seamlessly with system accessibility features.
class ThemeAwareColors {
  ThemeAwareColors._();

  // PERFORMANCE OPTIMIZATION: Cache theme brightness to avoid repeated lookups
  static bool _isDarkTheme(BuildContext context) => 
      Theme.of(context).brightness == Brightness.dark;

  // PRIMARY THEME COLORS - Use Material 3 ColorScheme for optimal performance
  
  /// Get primary color based on current theme
  /// Uses Material 3 ColorScheme for consistent theming
  static Color getPrimaryColor(BuildContext context) =>
      Theme.of(context).colorScheme.primary;

  /// Get secondary color based on current theme
  /// Uses Material 3 ColorScheme for consistent theming
  static Color getSecondaryColor(BuildContext context) =>
      Theme.of(context).colorScheme.secondary;

  /// Get tertiary/accent color based on current theme
  /// Uses Material 3 ColorScheme for consistent theming
  static Color getAccentColor(BuildContext context) =>
      Theme.of(context).colorScheme.tertiary;

  /// Get error color based on current theme
  /// Uses Material 3 ColorScheme for consistent theming
  static Color getErrorColor(BuildContext context) =>
      Theme.of(context).colorScheme.error;

  // SURFACE AND BACKGROUND COLORS - Use Material 3 for performance
  
  /// Get surface color based on current theme
  /// Uses Material 3 ColorScheme for optimal performance
  static Color getSurfaceColor(BuildContext context) =>
      Theme.of(context).colorScheme.surface;

  /// Get background color based on current theme
  /// Uses scaffold background for consistency
  static Color getBackgroundColor(BuildContext context) =>
      Theme.of(context).scaffoldBackgroundColor;

  /// Get card color based on current theme
  /// Uses Material 3 card color for consistency
  static Color getCardColor(BuildContext context) =>
      Theme.of(context).cardColor;

  /// Get divider color based on current theme
  /// Uses Material 3 outline for consistency
  static Color getDividerColor(BuildContext context) =>
      Theme.of(context).dividerColor;

  /// Get outline color for borders and form elements
  /// Uses Material 3 ColorScheme for consistent theming
  static Color getOutlineColor(BuildContext context) =>
      Theme.of(context).colorScheme.outline;

  // TEXT COLORS - Use Material 3 for performance and consistency
  
  /// Get primary text color based on current theme
  /// Uses Material 3 ColorScheme for optimal performance
  static Color getPrimaryTextColor(BuildContext context) =>
      Theme.of(context).colorScheme.onSurface;

  /// Get secondary text color based on current theme
  /// Uses Material 3 ColorScheme for optimal performance
  static Color getSecondaryTextColor(BuildContext context) =>
      Theme.of(context).colorScheme.onSurfaceVariant;

  /// Get disabled text color based on current theme
  /// Uses Material 3 ColorScheme with reduced opacity
  static Color getDisabledTextColor(BuildContext context) =>
      Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.38);

  /// Get icon color based on current theme
  /// Uses Material 3 ColorScheme for consistent theming
  static Color getIconColor(BuildContext context) =>
      Theme.of(context).colorScheme.onSurfaceVariant;

  // CUSTOM APP COLORS - Use AppTheme for app-specific colors
  
  /// Get success color based on current theme
  /// Uses custom AppTheme colors for brand consistency
  static Color getSuccessColor(BuildContext context) {
    final isDark = _isDarkTheme(context);
    return isDark ? AppTheme.successDark : AppTheme.successLight;
  }

  /// Get warning color based on current theme
  /// Uses custom AppTheme colors for brand consistency
  static Color getWarningColor(BuildContext context) {
    final isDark = _isDarkTheme(context);
    return isDark ? AppTheme.warningDark : AppTheme.warningLight;
  }

  /// Get shadow color based on current theme
  /// Uses custom AppTheme colors for subtle elevation
  static Color getShadowColor(BuildContext context) {
    final isDark = _isDarkTheme(context);
    return isDark ? AppTheme.shadowDark : AppTheme.shadowLight;
  }

  /// Get info color based on current theme (for informational elements)
  /// Uses Material 3 compatible colors for consistency
  static Color getInfoColor(BuildContext context) {
    final isDark = _isDarkTheme(context);
    return isDark ? Colors.lightBlue[300]! : Colors.blue[600]!;
  }

  // UTILITY METHODS - Performance-optimized helper methods
  
  /// Get container decoration with theme-aware colors
  /// Optimized for common container styling patterns
  static BoxDecoration getContainerDecoration(
    BuildContext context, {
    Color? backgroundColor,
    Color? borderColor,
    double borderRadius = 12.0,
    double borderWidth = 1.0,
    double borderAlpha = 0.3,
  }) {
    return BoxDecoration(
      color: backgroundColor ?? getSurfaceColor(context),
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: borderColor ?? getOutlineColor(context).withValues(alpha: borderAlpha),
        width: borderWidth,
      ),
    );
  }

  /// Get selected container decoration with theme-aware colors
  /// Optimized for selection state styling
  static BoxDecoration getSelectedContainerDecoration(
    BuildContext context, {
    required bool isSelected,
    Color? selectedColor,
    Color? unselectedColor,
    Color? selectedBorderColor,
    Color? unselectedBorderColor,
    double borderRadius = 12.0,
    double selectedAlpha = 0.1,
    double unselectedAlpha = 0.3,
  }) {
    final primaryColor = getPrimaryColor(context);
    final surfaceColor = getSurfaceColor(context);
    final outlineColor = getOutlineColor(context);
    
    return BoxDecoration(
      color: isSelected
          ? (selectedColor ?? primaryColor).withValues(alpha: selectedAlpha)
          : unselectedColor ?? surfaceColor,
      borderRadius: BorderRadius.circular(borderRadius),
      border: Border.all(
        color: isSelected
            ? selectedBorderColor ?? primaryColor
            : unselectedBorderColor ?? outlineColor.withValues(alpha: unselectedAlpha),
      ),
    );
  }

  /// Get theme-aware icon color for selected/unselected states
  /// Optimized for icon state management
  static Color getIconColorForState(
    BuildContext context, {
    required bool isSelected,
    Color? selectedColor,
    Color? unselectedColor,
  }) {
    return isSelected
        ? selectedColor ?? getPrimaryColor(context)
        : unselectedColor ?? getSecondaryTextColor(context);
  }
}