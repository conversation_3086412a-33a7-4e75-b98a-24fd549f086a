import 'package:flutter/material.dart';
import '../theme/theme_aware_colors.dart';

/// DEPRECATED: This class contains redundant theme utilities.
/// Use ThemeAwareColors, FormThemeHelper, and DialogThemeHelper instead.
/// 
/// This class will be removed in a future version to reduce code duplication
/// and improve performance by using the optimized theme-aware helpers.
@Deprecated('Use ThemeAwareColors, FormThemeHelper, and DialogThemeHelper instead')
class UIImprovements {
  UIImprovements._();

  /// DEPRECATED: Use ThemeAwareColors.getContainerDecoration() instead
  @Deprecated('Use ThemeAwareColors.getContainerDecoration() instead')
  static BoxDecoration getCardDecoration(BuildContext context, {
    Color? backgroundColor,
    double borderRadius = 12.0,
    bool hasShadow = true,
  }) {
    return ThemeAwareColors.getContainerDecoration(
      context,
      backgroundColor: backgroundColor,
      borderRadius: borderRadius,
    );
  }

  /// DEPRECATED: Use FormThemeHelper.getThemedElevatedButton() instead
  @Deprecated('Use FormThemeHelper.getThemedElevatedButton() instead')
  static ButtonStyle getPrimaryButtonStyle(BuildContext context, {
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
  }) {
    return ElevatedButton.styleFrom(
      backgroundColor: backgroundColor ?? Theme.of(context).colorScheme.primary,
      foregroundColor: foregroundColor ?? Theme.of(context).colorScheme.onPrimary,
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      elevation: 2,
    );
  }

  /// DEPRECATED: Use FormThemeHelper.getThemedOutlinedButton() instead
  @Deprecated('Use FormThemeHelper.getThemedOutlinedButton() instead')
  static ButtonStyle getSecondaryButtonStyle({
    Color? backgroundColor,
    Color? foregroundColor,
    EdgeInsetsGeometry? padding,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Use FormThemeHelper.getThemedOutlinedButton() instead');
  }

  /// DEPRECATED: Use FormThemeHelper.getInputDecoration() instead
  @Deprecated('Use FormThemeHelper.getInputDecoration() instead')
  static InputDecoration getTextFieldDecoration({
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Use FormThemeHelper.getInputDecoration() instead');
  }

  /// DEPRECATED: Use DialogThemeHelper.createThemedSnackBar() instead
  @Deprecated('Use DialogThemeHelper.createThemedSnackBar() instead')
  static SnackBar getSnackBar({
    required String message,
    bool isError = false,
    bool isSuccess = false,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Use DialogThemeHelper.createThemedSnackBar() instead');
  }

  /// DEPRECATED: Use standard AppBar with theme-aware colors instead
  @Deprecated('Use standard AppBar with theme-aware colors instead')
  static AppBar getAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    Color? backgroundColor,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Use standard AppBar with theme-aware colors instead');
  }

  /// DEPRECATED: Use standard CircularProgressIndicator with theme colors instead
  @Deprecated('Use standard CircularProgressIndicator with theme colors instead')
  static Widget getLoadingIndicator({
    Color? color,
    double? size,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Use standard CircularProgressIndicator with theme colors instead');
  }

  /// DEPRECATED: Create custom empty state widgets with theme-aware colors instead
  @Deprecated('Create custom empty state widgets with theme-aware colors instead')
  static Widget getEmptyState({
    required String title,
    required String description,
    Widget? icon,
    Widget? action,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Create custom empty state widgets with theme-aware colors instead');
  }

  /// DEPRECATED: Create custom section headers with theme-aware colors instead
  @Deprecated('Create custom section headers with theme-aware colors instead')
  static Widget getSectionHeader({
    required String title,
    String? subtitle,
    Widget? action,
  }) {
    // This method is deprecated and should not be used
    throw UnimplementedError('Create custom section headers with theme-aware colors instead');
  }
}