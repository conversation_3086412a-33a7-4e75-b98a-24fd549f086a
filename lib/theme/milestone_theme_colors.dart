import 'package:flutter/material.dart';
import 'app_theme.dart';

/// Optimized theme-aware colors specifically for milestone statistics.
/// 
/// This class provides performance-optimized access to milestone-specific colors
/// by leveraging the main AppTheme color system and Material 3 ColorScheme.
/// Colors are designed to maintain visual hierarchy and accessibility in both themes.
class MilestoneThemeColors {
  MilestoneThemeColors._();

  // PERFORMANCE OPTIMIZATION: Cache theme brightness to avoid repeated lookups
  static bool _isDarkTheme(BuildContext context) => 
      Theme.of(context).brightness == Brightness.dark;

  /// Get total milestones color - uses primary color variants for consistency
  /// 
  /// Performance optimized to use AppTheme constants directly rather than
  /// creating new Color objects on each call.
  static Color getTotalMilestonesColor(BuildContext context) {
    return _isDarkTheme(context) 
        ? AppTheme.primaryDark 
        : AppTheme.primaryLight;
  }

  /// Get this month milestones color - uses secondary color for differentiation
  /// 
  /// Maintains visual hierarchy while ensuring accessibility compliance.
  static Color getThisMonthColor(BuildContext context) {
    return _isDarkTheme(context) 
        ? AppTheme.secondaryDark 
        : AppTheme.secondaryLight;
  }

  /// Get baby age color - uses accent/tertiary color for warmth
  /// 
  /// Provides visual warmth appropriate for baby-related statistics.
  static Color getBabyAgeColor(BuildContext context) {
    return _isDarkTheme(context) 
        ? AppTheme.accentDark 
        : AppTheme.accentLight;
  }

  /// Get average milestone color - uses Material 3 ColorScheme for consistency
  /// 
  /// Performance optimized to use theme's built-in color system.
  static Color getAverageColor(BuildContext context) {
    return Theme.of(context).colorScheme.tertiary;
  }

  /// Get progress background color - uses Material 3 surface variants
  /// 
  /// Optimized to use theme's surface color system for better integration.
  static Color getProgressBackgroundColor(BuildContext context) {
    return Theme.of(context).colorScheme.surfaceVariant;
  }

  /// Get milestone card background color - uses theme-aware surface colors
  /// 
  /// Provides consistent card styling across the milestone system.
  static Color getMilestoneCardColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  /// Get milestone text color - uses theme-aware text colors
  /// 
  /// Ensures proper contrast and readability in both themes.
  static Color getMilestoneTextColor(BuildContext context) {
    return Theme.of(context).colorScheme.onSurface;
  }

  /// Get milestone secondary text color - uses theme-aware secondary text
  /// 
  /// Provides visual hierarchy for less important milestone information.
  static Color getMilestoneSecondaryTextColor(BuildContext context) {
    return Theme.of(context).colorScheme.onSurfaceVariant;
  }

  /// Get milestone border color - uses theme-aware outline colors
  /// 
  /// Provides subtle borders that work in both light and dark themes.
  static Color getMilestoneBorderColor(BuildContext context) {
    return Theme.of(context).colorScheme.outline.withOpacity(0.3);
  }
}