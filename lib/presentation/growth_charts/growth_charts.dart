import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/app_export.dart';
import '../../services/who_percentile_service.dart';
import '../../services/measurement_units_service.dart';
import '../../utils/subscription_access_control.dart';
import '../../presentation/subscription/controllers/subscription_controller.dart';
import '../../presentation/subscription/widgets/premium_feature_card.dart';
import '../../presentation/subscription/subscription_screen.dart';
import './widgets/chart_toolbar_widget.dart';
import './widgets/growth_chart_widget.dart';
import './widgets/measurement_entry_sheet.dart';
import './widgets/measurement_selector_widget.dart';
import './widgets/recent_measurements_widget.dart';

class GrowthCharts extends StatefulWidget {
  final BabyProfile babyProfile;

  const GrowthCharts({super.key, required this.babyProfile});

  @override
  State<GrowthCharts> createState() => _GrowthChartsState();
}

class _GrowthChartsState extends State<GrowthCharts>
    with TickerProviderStateMixin {
  static const String _selectedTypeKey = 'selected_measurement_type';
  int _selectedMeasurementType =
      0; // 0: Weight, 1: Height, 2: Head Circumference
  String _selectedDateRange = '1 year';
  late AnimationController _animationController;
  bool _isLoading = false;
  bool _isChartLoading = false; // Add separate loading state for chart
  final ScrollController _scrollController = ScrollController();

  // Data from Supabase
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();

  // Real data loaded from Supabase  
  DateTime get _babyBirthDate => widget.babyProfile.birthDate;
  
  List<Map<String, dynamic>> _weightData = [];
  List<Map<String, dynamic>> _heightData = [];
  List<Map<String, dynamic>> _headCircumferenceData = [];

  List<Map<String, dynamic>> get _getCurrentGrowthData {
    switch (_selectedMeasurementType) {
      case 0: return _weightData;
      case 1: return _heightData;
      case 2: return _headCircumferenceData;
      default: return _weightData;
    }
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadSavedState();
    _loadGrowthMeasurements();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedState() async {
    final prefs = await SharedPreferences.getInstance();
    final savedType = prefs.getInt(_selectedTypeKey);
    if (savedType != null && mounted) {
      setState(() => _selectedMeasurementType = savedType);
    }
  }

  Future<void> _saveSelectedType(int type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_selectedTypeKey, type);
    if (mounted) {
      setState(() => _selectedMeasurementType = type);
    }
  }

  Future<void> _loadGrowthMeasurements() async {
    setState(() => _isLoading = true);
    try {
      // Query measurements for the selected baby
      final measurements = await _supabaseService.select(
        'growth_measurements',
        filters: {'baby_id': widget.babyProfile.id},
        orderBy: 'measured_at',
        ascending: true,
      ).catchError((error) async {
        debugPrint('Trying fallback column name: $error');
        return await _supabaseService.select(
          'growth_measurements',
          filters: {'baby_id': widget.babyProfile.id},
          orderBy: 'measurement_date',
          ascending: true,
        );
      });

      // Group measurements by type
      _weightData.clear();
      _heightData.clear();
      _headCircumferenceData.clear();

      for (final measurement in measurements) {
        final dateStr = measurement['measured_at'] ?? measurement['measurement_date'];
        if (dateStr == null) continue;
        double? value = (measurement['value'] as num?)?.toDouble();
        String? unit = measurement['unit'];
        final type = measurement['measurement_type'];
        if (value == null) {
          if (type == 'weight' && measurement['weight_kg'] != null) {
            value = (measurement['weight_kg'] as num).toDouble();
            unit = 'kg';
          } else if ((type == 'height' || type == 'length') && measurement['height_cm'] != null) {
            value = (measurement['height_cm'] as num).toDouble();
            unit = 'cm';
          } else if (type == 'head_circumference' && measurement['head_circumference_cm'] != null) {
            value = (measurement['head_circumference_cm'] as num).toDouble();
            unit = 'cm';
          } else {
            continue;
          }
        }
        final data = {
          'id': measurement['id'],
          'date': DateTime.parse(dateStr),
          'value': value,
          'unit': unit,
          'notes': measurement['notes'] ?? '',
          'ageInMonths': _calculateAgeInMonths(DateTime.parse(dateStr)),
          'percentile': (measurement['percentile'] as num?)?.toDouble(),
        };
        switch (type) {
          case 'weight':
            _weightData.add(data);
            break;
          case 'height':
          case 'length':
            _heightData.add(data);
            break;
          case 'head_circumference':
            _headCircumferenceData.add(data);
            break;
        }
      }
      // Add birth measurements
      if (widget.babyProfile.birthWeight != null) {
        final birthWeightPercentile = WHOPercentileService.calculatePercentile(
          widget.babyProfile.birthWeight!,
          0.0,
          'weight',
          _normalizeGender(widget.babyProfile.gender),
        );
        _weightData.insert(0, {
          'date': widget.babyProfile.birthDate,
          'value': widget.babyProfile.birthWeight,
          'unit': 'kg',
          'notes': 'Birth Weight',
          'ageInMonths': 0.0,
          'percentile': birthWeightPercentile,
        });
      }
      if (widget.babyProfile.birthHeight != null) {
        final birthHeightPercentile = WHOPercentileService.calculatePercentile(
          widget.babyProfile.birthHeight!,
          0.0,
          'height',
          _normalizeGender(widget.babyProfile.gender),
        );
        _heightData.insert(0, {
          'date': widget.babyProfile.birthDate,
          'value': widget.babyProfile.birthHeight,
          'unit': 'cm',
          'notes': 'Birth Height',
          'ageInMonths': 0.0,
          'percentile': birthHeightPercentile,
        });
      }

      if (mounted) {
        setState(() => _isLoading = false);
        _animationController.forward();
      }
    } catch (e) {
      debugPrint('Error loading growth measurements: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  double _calculateAgeInMonths(DateTime measurementDate) {
    final difference = measurementDate.difference(widget.babyProfile.birthDate);
    return difference.inDays / 30.44;
  }

  List<Map<String, dynamic>> get _allMeasurements {
    final all = <Map<String, dynamic>>[];
    
    // Add all measurements with type info
    for (final measurement in _weightData) {
      all.add({
        ...measurement,
        'type': 'Weight',
        'icon': 'monitor_weight',
      });
    }
    
    for (final measurement in _heightData) {
      all.add({
        ...measurement,
        'type': 'Height',
        'icon': 'height',
      });
    }
    
    for (final measurement in _headCircumferenceData) {
      all.add({
        ...measurement,
        'type': 'Head Circumference',
        'icon': 'account_circle',
      });
    }
    
    // Sort by date (most recent first)
    all.sort((a, b) => (b['date'] as DateTime).compareTo(a['date'] as DateTime));
    
    return all;
  }

  Future<void> _showMeasurementEntrySheet() async {
    String title = _selectedMeasurementType == 0 
        ? 'Weight' 
        : _selectedMeasurementType == 1 
            ? 'Height' 
            : 'Head Circumference';
            
    final bool? success = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MeasurementEntrySheet(
        title: 'Add $title',
        subtitle: 'Track your baby\'s growth with WHO percentiles',
        measurementType: _selectedMeasurementType,
        isMetric: context.read<MeasurementUnitsService>().isMetric,
        onSave: (double value, String unit, String notes, DateTime measurementDate) async {
          final bool result = await _saveMeasurement(value, unit, notes, measurementDate);
          if (result) {
            await _loadGrowthMeasurements(); // Refresh the data
          }
          return result;
        },
      ),
    );

    if (success != null) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Measurement saved successfully'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving measurement'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<bool> _saveMeasurement(double value, String unit, String notes, DateTime measurementDate) async {
    final unitsService = context.read<MeasurementUnitsService>();
    // Convert to metric if needed
    final metricValue = unitsService.isMetric ? value : _convertToMetric(value, unit);
    final metricUnit = _getMetricUnit();
    
    // Calculate WHO percentile
    final ageInMonths = _calculateAgeInMonths(measurementDate);
    final percentile = WHOPercentileService.calculatePercentile(
      metricValue,
      ageInMonths,
      _getMeasurementTypeString(_selectedMeasurementType),
      _normalizeGender(widget.babyProfile.gender),
    );
    
    final insertData = {
      'baby_id': widget.babyProfile.id,
      'measurement_type': _getMeasurementTypeString(_selectedMeasurementType),
      'value': metricValue,
      'unit': metricUnit,
      'notes': notes,
      'measured_at': measurementDate.toIso8601String(),
      'measurement_date': measurementDate.toIso8601String(), // Add this for backward compatibility
      'percentile': percentile,
      if (_selectedMeasurementType == 0) 'weight_kg': metricValue,
      if (_selectedMeasurementType == 1) 'height_cm': metricValue,
      if (_selectedMeasurementType == 2) 'head_circumference_cm': metricValue,
    };
    debugPrint('Inserting measurement: ' + insertData.toString());
    try {
      final result = await _supabaseService.insert('growth_measurements', insertData);
      debugPrint('Insert result: $result');
      if (result == null || (result is Map && result['error'] != null)) {
        throw Exception('Insert failed: $result');
      }
      return true;
    } catch (e) {
      debugPrint('Error inserting measurement: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save measurement: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
      return false;
    }
  }
  
  String _normalizeGender(String gender) {
    final lowercaseGender = gender.toLowerCase();
    if (lowercaseGender == 'male' || lowercaseGender == 'boy' || lowercaseGender == 'boys') {
      return 'boys';
    } else if (lowercaseGender == 'female' || lowercaseGender == 'girl' || lowercaseGender == 'girls') {
      return 'girls';
    }
    // Default to boys if gender is unclear
    return 'boys';
  }

  String _getMeasurementTypeString(int type) {
    switch (type) {
      case 0:
        return 'weight';
      case 1:
        return 'height';
      case 2:
        return 'head_circumference';
      default:
        return 'weight';
    }
  }

  Future<void> _deleteMeasurement(int index) async {
    try {
      final measurement = _allMeasurements[index];
      final measurementId = measurement['id'];

      if (measurementId != null) {
        await _supabaseService.delete('growth_measurements', 'id', measurementId);
        await _loadGrowthMeasurements();
        
        if (!mounted) return;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Measurement deleted'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error deleting measurement: $e');
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete measurement'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  Future<void> _editMeasurement(int index) async {
    final measurement = _allMeasurements[index];
    
    String title = _selectedMeasurementType == 0 
        ? 'Weight' 
        : _selectedMeasurementType == 1 
            ? 'Height' 
            : 'Head Circumference';
            
    final bool? success = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MeasurementEntrySheet(
        title: 'Edit $title',
        subtitle: 'Track your baby\'s growth with WHO percentiles',
        measurementType: _selectedMeasurementType,
        isMetric: context.read<MeasurementUnitsService>().isMetric,
        onSave: (double value, String unit, String notes, DateTime measurementDate) async {
          final bool result = await _saveMeasurement(value, unit, notes, measurementDate);
          if (result) {
            await _loadGrowthMeasurements(); // Refresh the data
          }
          return result;
        },
      ),
    );

    if (success != null) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Measurement updated successfully'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating measurement'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  double _convertToMetric(double value, String unit) {
    switch (unit) {
      case 'lbs':
        return value * 0.45359237; // Convert pounds to kg
      case 'oz':
        return value * 0.0283495; // Convert ounces to kg
      case 'in':
        return value * 2.54; // Convert inches to cm
      case 'ft':
        return value * 30.48; // Convert feet to cm
      default:
        return value;
    }
  }

  String _getMetricUnit() {
    switch (_selectedMeasurementType) {
      case 0: // Weight
        return 'kg';
      case 1: // Height
        return 'cm';
      case 2: // Head Circumference
        return 'cm';
      default:
        return 'kg';
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    if (_isLoading) {
      return Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Growth Charts',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                'for ${widget.babyProfile.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          iconTheme: IconThemeData(color: colorScheme.onSurface),
          systemOverlayStyle: isDarkMode 
            ? SystemUiOverlayStyle.light 
            : SystemUiOverlayStyle.dark,
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              if (mounted && Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                Navigator.pushReplacementNamed(context, '/main-navigation');
              }
            },
          ),
        ),
        body: Container(
          color: colorScheme.surface,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    colorScheme.primary,
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  'Loading growth data...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Scaffold(
        backgroundColor: colorScheme.surface,
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Growth Charts',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                'for ${widget.babyProfile.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
          backgroundColor: colorScheme.surface,
          elevation: 0,
          iconTheme: IconThemeData(color: colorScheme.onSurface),
          systemOverlayStyle: isDarkMode 
            ? SystemUiOverlayStyle.light 
            : SystemUiOverlayStyle.dark,
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              if (mounted && Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                Navigator.pushReplacementNamed(context, '/main-navigation');
              }
            },
          ),
          actions: [
          IconButton(
            onPressed: () async {
              context.read<MeasurementUnitsService>().toggleMeasurementSystem();
            },
            icon: Container(
              padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
              decoration: BoxDecoration(
                color: colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                context.watch<MeasurementUnitsService>().isMetric ? 'metric' : 'imperial',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: 2.w),
        ],
      ),
      body: Container(
        color: colorScheme.surface,
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              children: [
                // Chart Section
                Container(
                  margin: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: isDarkMode 
                          ? Colors.black.withValues(alpha: 0.3)
                          : Colors.grey.withValues(alpha: 0.2),
                        blurRadius: 10,
                        offset: Offset(0, 2),
                      ),
                    ],
                  ),
                child: Column(
                  children: [
                    // Chart Toolbar
                    Consumer<SubscriptionController>(
                      builder: (context, subscriptionController, _) {
                        return ChartToolbarWidget(
                          selectedDateRange: _selectedDateRange,
                          isMetric: context.watch<MeasurementUnitsService>().isMetric,
                          isFreePlan: subscriptionController.isOnFreePlan,
                          onDateRangeChanged: (range) {
                            if (subscriptionController.isOnFreePlan) {
                              _showUpgradeDialog();
                            } else {
                              setState(() => _selectedDateRange = range);
                            }
                          },
                          onUnitToggle: (metric) {
                            context.read<MeasurementUnitsService>().setMeasurementSystem(metric);
                          },
                          onSharePressed: () {
                            if (subscriptionController.isOnFreePlan) {
                              _showUpgradeDialog();
                            } else {
                              _showExportOptions();
                            }
                          },
                        );
                      },
                    ),

                    // Measurement Type Selector
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
                      child: MeasurementSelectorWidget(
                        selectedIndex: _selectedMeasurementType,
                        onChanged: (index) {
                          _saveSelectedType(index);
                        },
                      ),
                    ),

                    // Chart Area - Show premium card for free users
                    Consumer<SubscriptionController>(
                      builder: (context, subscriptionController, _) {
                        if (subscriptionController.isOnFreePlan) {
                          return PremiumFeatureCard(
                            title: 'Unlock WHO Growth Charts',
                            description: 'Track your baby\'s growth with official WHO percentile charts',
                            icon: Icons.show_chart,
                            compact: false,
                          );
                        }
                        
                        // Show chart for premium users
                        return _getCurrentGrowthData.isEmpty
                            ? _buildEmptyChartState()
                            : _isChartLoading
                                ? Center(
                                    child: CircularProgressIndicator(
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Theme.of(context).colorScheme.primary,
                                      ),
                                    ),
                                  )
                                : GrowthChartWidget(
                                    data: _getCurrentGrowthData,
                                    measurementType: _selectedMeasurementType,
                                    dateRange: _selectedDateRange,
                                    isMetric: context.watch<MeasurementUnitsService>().isMetric,
                                    title: _getMeasurementTitle(_selectedMeasurementType),
                                    birthDate: _babyBirthDate,
                                    babyProfile: widget.babyProfile,
                                  );
                      },
                    ),
                    ],
                  ),
                ),

                // Recent Measurements Section
                Container(
                  margin: EdgeInsets.fromLTRB(4.w, 0, 4.w, 4.w),
                  child: RecentMeasurementsWidget(
                    measurements: _allMeasurements,
                    measurementType: _selectedMeasurementType,
                    isMetric: context.watch<MeasurementUnitsService>().isMetric,
                    onDelete: _deleteMeasurement,
                    onEdit: _editMeasurement,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showMeasurementEntrySheet,
        icon: CustomIconWidget(
          iconName: 'add',
          color: Theme.of(context).colorScheme.onPrimary,
          size: 24,
        ),
        label: Text(
          'Add Measurement',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: Theme.of(context).colorScheme.onPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  Widget _buildEmptyChartState() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'trending_up',
              color: colorScheme.onSurface.withValues(alpha: 0.3),
              size: 64,
            ),
            SizedBox(height: 2.h),
            Text(
              'No Growth Data Yet',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface.withValues(alpha: 0.8),
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Start tracking your baby\'s growth by adding the first measurement',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showExportOptions() {
    final colorScheme = Theme.of(context).colorScheme;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 10.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Export Growth Data',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'picture_as_pdf',
                color: colorScheme.primary,
                size: 24,
              ),
              title: Text(
                'Export as PDF',
                style: TextStyle(color: colorScheme.onSurface),
              ),
              subtitle: Text(
                'Comprehensive growth report',
                style: TextStyle(color: colorScheme.onSurface.withValues(alpha: 0.7)),
              ),
              onTap: () {
                Navigator.pop(context);
                _exportToPDF();
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'share',
                color: colorScheme.primary,
                size: 24,
              ),
              title: Text(
                'Share with Healthcare Provider',
                style: TextStyle(color: colorScheme.onSurface),
              ),
              subtitle: Text(
                'Send to pediatrician',
                style: TextStyle(color: colorScheme.onSurface.withValues(alpha: 0.7)),
              ),
              onTap: () {
                Navigator.pop(context);
                _shareWithProvider();
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  void _exportToPDF() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('PDF export functionality coming soon'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  void _shareWithProvider() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Share functionality coming soon'),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }

  String _getMeasurementTitle(int type) {
    switch (type) {
      case 0:
        return 'Weight';
      case 1:
        return 'Height';
      case 2:
        return 'Head Circumference';
      default:
        return 'Weight';
    }
  }

  /// Show upgrade dialog when free users try to use premium features
  void _showUpgradeDialog() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? Colors.grey.shade900 : Colors.white,
        title: Row(
          children: [
            Icon(
              Icons.star,
              color: Colors.orange.shade600,
              size: 24,
            ),
            SizedBox(width: 8),
            Text(
              'Premium Feature',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                color: Colors.orange.shade600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Unlock WHO Growth Charts',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Track your baby\'s growth with official WHO percentile charts and export features.',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
              ),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark ? Colors.orange.shade900.withAlpha(100) : Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isDark ? Colors.orange.shade700 : Colors.orange.shade200,
                ),
              ),
              child: Column(
                children: [
                  _buildUpgradeFeature(Icons.show_chart, 'WHO Growth Charts', isDark),
                  SizedBox(height: 8),
                  _buildUpgradeFeature(Icons.timeline, 'Time Period Selection', isDark),
                  SizedBox(height: 8),
                  _buildUpgradeFeature(Icons.share, 'Export & Share', isDark),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Maybe Later',
              style: GoogleFonts.inter(
                color: isDark ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(
                    initialFocus: 'premium',
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Upgrade to Premium',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build feature item for upgrade dialog
  Widget _buildUpgradeFeature(IconData icon, String title, bool isDark) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.orange.shade600,
          size: 16,
        ),
        SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.orange.shade100 : Colors.grey.shade700,
          ),
        ),
      ],
    );
  }
}
