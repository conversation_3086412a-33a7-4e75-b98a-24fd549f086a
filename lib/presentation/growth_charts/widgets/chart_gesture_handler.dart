import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Comprehensive gesture handler for growth chart zoom, pan, and smooth animations
class ChartGestureHandler {
  // Gesture state
  double _zoomLevel = 1.0;
  Offset _panOffset = Offset.zero;
  bool _isInteracting = false;
  
  // Animation controllers
  AnimationController? _zoomAnimationController;
  AnimationController? _panAnimationController;
  AnimationController? _bounceAnimationController;
  
  // Gesture constraints
  static const double minZoom = 0.5;
  static const double maxZoom = 3.0;
  static const double maxPanX = 150.0;
  static const double maxPanY = 100.0;
  static const double snapThreshold = 0.15;
  
  // Callbacks
  final Function(double)? onZoomChanged;
  final Function(Offset)? onPanChanged;
  final VoidCallback? onInteractionStart;
  final VoidCallback? onInteractionEnd;
  final Function(double, Offset)? onTransformChanged;

  ChartGestureHandler({
    this.onZoomChanged,
    this.onPanChanged,
    this.onInteractionStart,
    this.onInteractionEnd,
    this.onTransformChanged,
  });

  // Getters
  double get zoomLevel => _zoomLevel;
  Offset get panOffset => _panOffset;
  bool get isInteracting => _isInteracting;

  /// Initialize animation controllers
  void initializeAnimations(TickerProvider vsync) {
    _zoomAnimationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: vsync,
    );
    
    _panAnimationController = AnimationController(
      duration: const Duration(milliseconds: 350),
      vsync: vsync,
    );
    
    _bounceAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: vsync,
    );
  }

  /// Dispose animation controllers
  void dispose() {
    try {
      _zoomAnimationController?.dispose();
      _zoomAnimationController = null;
    } catch (e) {
      // Controller already disposed
    }
    try {
      _panAnimationController?.dispose();
      _panAnimationController = null;
    } catch (e) {
      // Controller already disposed
    }
    try {
      _bounceAnimationController?.dispose();
      _bounceAnimationController = null;
    } catch (e) {
      // Controller already disposed
    }
  }

  /// Handle scale gesture start
  void handleScaleStart(ScaleStartDetails details) {
    _isInteracting = true;
    onInteractionStart?.call();
    
    // Stop any ongoing animations
    _zoomAnimationController?.stop();
    _panAnimationController?.stop();
    _bounceAnimationController?.stop();
    
    // Provide haptic feedback
    HapticFeedback.selectionClick();
  }

  /// Handle scale gesture update with smooth constraints
  void handleScaleUpdate(ScaleUpdateDetails details) {
    if (!_isInteracting) return;
    
    // Calculate new zoom level
    final newZoomLevel = (_zoomLevel * details.scale).clamp(minZoom, maxZoom);
    
    // Calculate new pan offset with zoom-aware constraints
    final zoomFactor = _zoomLevel / 1.0; // Relative to default zoom
    final maxPanXAdjusted = maxPanX * zoomFactor;
    final maxPanYAdjusted = maxPanY * zoomFactor;
    
    final newPanOffset = Offset(
      (_panOffset.dx + details.focalPointDelta.dx).clamp(-maxPanXAdjusted, maxPanXAdjusted),
      (_panOffset.dy + details.focalPointDelta.dy).clamp(-maxPanYAdjusted, maxPanYAdjusted),
    );
    
    // Apply changes if they're different
    bool changed = false;
    
    if (newZoomLevel != _zoomLevel) {
      _zoomLevel = newZoomLevel;
      onZoomChanged?.call(_zoomLevel);
      changed = true;
    }
    
    if (newPanOffset != _panOffset) {
      _panOffset = newPanOffset;
      onPanChanged?.call(_panOffset);
      changed = true;
    }
    
    if (changed) {
      onTransformChanged?.call(_zoomLevel, _panOffset);
    }
  }

  /// Handle scale gesture end with smooth animations and snapping
  void handleScaleEnd(ScaleEndDetails details) {
    if (!_isInteracting) return;
    
    _isInteracting = false;
    onInteractionEnd?.call();
    
    // Animate to snap positions if close enough
    final snappedZoom = _getSnappedZoom(_zoomLevel);
    final constrainedPan = _getConstrainedPan(_panOffset, _zoomLevel);
    
    bool needsZoomAnimation = (snappedZoom - _zoomLevel).abs() > 0.01;
    bool needsPanAnimation = (constrainedPan - _panOffset).distance > 1.0;
    
    if (needsZoomAnimation && needsPanAnimation) {
      _animateToTransform(snappedZoom, constrainedPan);
    } else if (needsZoomAnimation) {
      _animateZoomTo(snappedZoom);
    } else if (needsPanAnimation) {
      _animatePanTo(constrainedPan);
    }
    
    // Provide completion haptic feedback
    HapticFeedback.lightImpact();
  }

  /// Handle tap gestures for zoom controls
  void handleDoubleTap(TapDownDetails details) {
    HapticFeedback.mediumImpact();
    
    if (_zoomLevel > 1.5) {
      // Zoom out to fit
      resetZoomAndPan();
    } else {
      // Zoom in to 2x at tap location
      _zoomToPoint(2.0, details.localPosition);
    }
  }

  /// Zoom to a specific point with smooth animation
  void _zoomToPoint(double targetZoom, Offset focalPoint) {
    // Calculate the pan offset needed to keep the focal point centered
    final currentCenter = Offset.zero; // Assuming chart center
    final offsetFromCenter = focalPoint - currentCenter;
    final scaleFactor = targetZoom / _zoomLevel;
    final newPanOffset = _panOffset - (offsetFromCenter * (scaleFactor - 1));
    
    _animateToTransform(targetZoom, _getConstrainedPan(newPanOffset, targetZoom));
  }

  /// Get snapped zoom level for common zoom values
  double _getSnappedZoom(double zoom) {
    const snapLevels = [0.5, 0.75, 1.0, 1.25, 1.5, 2.0, 2.5, 3.0];
    
    for (final level in snapLevels) {
      if ((zoom - level).abs() < snapThreshold) {
        return level;
      }
    }
    return zoom;
  }

  /// Get constrained pan offset based on zoom level
  Offset _getConstrainedPan(Offset pan, double zoom) {
    final zoomFactor = zoom / 1.0;
    final maxPanXAdjusted = maxPanX * zoomFactor;
    final maxPanYAdjusted = maxPanY * zoomFactor;
    
    return Offset(
      pan.dx.clamp(-maxPanXAdjusted, maxPanXAdjusted),
      pan.dy.clamp(-maxPanYAdjusted, maxPanYAdjusted),
    );
  }

  /// Animate zoom to target level
  void _animateZoomTo(double targetZoom) {
    if (_zoomAnimationController == null) return;
    
    final startZoom = _zoomLevel;
    final animation = Tween<double>(
      begin: startZoom,
      end: targetZoom,
    ).animate(CurvedAnimation(
      parent: _zoomAnimationController!,
      curve: Curves.easeOutCubic,
    ));

    animation.addListener(() {
      _zoomLevel = animation.value;
      onZoomChanged?.call(_zoomLevel);
      onTransformChanged?.call(_zoomLevel, _panOffset);
    });

    _zoomAnimationController!.forward(from: 0);
  }

  /// Animate pan to target offset
  void _animatePanTo(Offset targetOffset) {
    if (_panAnimationController == null) return;
    
    final startOffset = _panOffset;
    final animation = Tween<Offset>(
      begin: startOffset,
      end: targetOffset,
    ).animate(CurvedAnimation(
      parent: _panAnimationController!,
      curve: Curves.easeOutCubic,
    ));

    animation.addListener(() {
      _panOffset = animation.value;
      onPanChanged?.call(_panOffset);
      onTransformChanged?.call(_zoomLevel, _panOffset);
    });

    _panAnimationController!.forward(from: 0);
  }

  /// Animate both zoom and pan simultaneously
  void _animateToTransform(double targetZoom, Offset targetPan) {
    if (_zoomAnimationController == null || _panAnimationController == null) return;
    
    final startZoom = _zoomLevel;
    final startPan = _panOffset;
    
    final zoomAnimation = Tween<double>(
      begin: startZoom,
      end: targetZoom,
    ).animate(CurvedAnimation(
      parent: _zoomAnimationController!,
      curve: Curves.easeOutCubic,
    ));
    
    final panAnimation = Tween<Offset>(
      begin: startPan,
      end: targetPan,
    ).animate(CurvedAnimation(
      parent: _panAnimationController!,
      curve: Curves.easeOutCubic,
    ));

    void updateTransform() {
      _zoomLevel = zoomAnimation.value;
      _panOffset = panAnimation.value;
      onZoomChanged?.call(_zoomLevel);
      onPanChanged?.call(_panOffset);
      onTransformChanged?.call(_zoomLevel, _panOffset);
    }

    zoomAnimation.addListener(updateTransform);
    panAnimation.addListener(updateTransform);

    // Synchronize both animations
    _zoomAnimationController!.forward(from: 0);
    _panAnimationController!.forward(from: 0);
  }

  /// Reset zoom and pan to default values with smooth animation
  void resetZoomAndPan() {
    _animateToTransform(1.0, Offset.zero);
  }

  /// Zoom to fit content with optimal zoom level
  void zoomToFit({double padding = 0.1}) {
    // Calculate optimal zoom level (implementation depends on chart content)
    final optimalZoom = _calculateOptimalZoom(padding);
    _animateZoomTo(optimalZoom);
  }

  /// Calculate optimal zoom level based on content
  double _calculateOptimalZoom(double padding) {
    // This would typically analyze the chart data to determine optimal zoom
    // For now, return a reasonable default
    return 1.2;
  }

  /// Zoom in by a factor
  void zoomIn({double factor = 1.5}) {
    final targetZoom = (_zoomLevel * factor).clamp(minZoom, maxZoom);
    _animateZoomTo(targetZoom);
  }

  /// Zoom out by a factor
  void zoomOut({double factor = 0.75}) {
    final targetZoom = (_zoomLevel * factor).clamp(minZoom, maxZoom);
    _animateZoomTo(targetZoom);
  }

  /// Pan to a specific offset
  void panTo(Offset targetOffset) {
    final constrainedOffset = _getConstrainedPan(targetOffset, _zoomLevel);
    _animatePanTo(constrainedOffset);
  }

  /// Center the chart
  void centerChart() {
    _animatePanTo(Offset.zero);
  }

  /// Get current transform matrix for external use
  Matrix4 getTransformMatrix() {
    return Matrix4.identity()
      ..translate(_panOffset.dx, _panOffset.dy)
      ..scale(_zoomLevel);
  }

  /// Apply transform from external source
  void applyTransform(double zoom, Offset pan) {
    _zoomLevel = zoom.clamp(minZoom, maxZoom);
    _panOffset = _getConstrainedPan(pan, _zoomLevel);
    
    onZoomChanged?.call(_zoomLevel);
    onPanChanged?.call(_panOffset);
    onTransformChanged?.call(_zoomLevel, _panOffset);
  }

  /// Create a bounce effect for visual feedback
  void createBounceEffect() {
    if (_bounceAnimationController == null) return;
    
    final bounceAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _bounceAnimationController!,
      curve: Curves.elasticOut,
    ));

    bounceAnimation.addListener(() {
      // Apply temporary scale effect
      onTransformChanged?.call(_zoomLevel * bounceAnimation.value, _panOffset);
    });

    _bounceAnimationController!.forward().then((_) {
      _bounceAnimationController!.reverse();
    });
  }

  /// Check if current transform is at default state
  bool get isAtDefault => _zoomLevel == 1.0 && _panOffset == Offset.zero;

  /// Check if zoom is at minimum
  bool get isAtMinZoom => _zoomLevel <= minZoom + 0.01;

  /// Check if zoom is at maximum
  bool get isAtMaxZoom => _zoomLevel >= maxZoom - 0.01;

  /// Get zoom percentage (0-100)
  double get zoomPercentage => ((_zoomLevel - minZoom) / (maxZoom - minZoom) * 100).clamp(0, 100);
}

/// Gesture detector widget that wraps the chart with comprehensive gesture handling
class ChartGestureDetector extends StatefulWidget {
  final Widget child;
  final ChartGestureHandler gestureHandler;
  final bool enableZoom;
  final bool enablePan;
  final bool enableDoubleTap;

  const ChartGestureDetector({
    super.key,
    required this.child,
    required this.gestureHandler,
    this.enableZoom = true,
    this.enablePan = true,
    this.enableDoubleTap = true,
  });

  @override
  State<ChartGestureDetector> createState() => _ChartGestureDetectorState();
}

class _ChartGestureDetectorState extends State<ChartGestureDetector>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    widget.gestureHandler.initializeAnimations(this);
  }

  @override
  void dispose() {
    widget.gestureHandler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onScaleStart: widget.enableZoom || widget.enablePan 
          ? widget.gestureHandler.handleScaleStart 
          : null,
      onScaleUpdate: widget.enableZoom || widget.enablePan 
          ? widget.gestureHandler.handleScaleUpdate 
          : null,
      onScaleEnd: widget.enableZoom || widget.enablePan 
          ? widget.gestureHandler.handleScaleEnd 
          : null,
      onDoubleTapDown: widget.enableDoubleTap 
          ? widget.gestureHandler.handleDoubleTap 
          : null,
      child: widget.child,
    );
  }
}