import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/measurement.dart';
import '../../../services/who_data_service.dart';
import 'chart_data_processor.dart';

/// Comprehensive tooltip system for growth chart measurements and percentile curves
class MeasurementTooltipSystem extends StatefulWidget {
  final Measurement? selectedMeasurement;
  final String? hoveredPercentile;
  final double? hoveredPercentileAge;
  final Offset? tooltipPosition;
  final String measurementType;
  final String gender;
  final bool isMetric;
  final AnimationController animationController;
  final VoidCallback? onDismiss;
  final Function(Measurement)? onEditMeasurement;

  const MeasurementTooltipSystem({
    super.key,
    this.selectedMeasurement,
    this.hoveredPercentile,
    this.hoveredPercentileAge,
    this.tooltipPosition,
    required this.measurementType,
    required this.gender,
    required this.isMetric,
    required this.animationController,
    this.onDismiss,
    this.onEditMeasurement,
  });

  @override
  State<MeasurementTooltipSystem> createState() => _MeasurementTooltipSystemState();
}

class _MeasurementTooltipSystemState extends State<MeasurementTooltipSystem>
    with SingleTickerProviderStateMixin {
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.easeOut,
    ));
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(MeasurementTooltipSystem oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Animate in when tooltip appears
    if (widget.tooltipPosition != null && oldWidget.tooltipPosition == null) {
      _scaleController.forward();
    }
    
    // Animate out when tooltip disappears
    if (widget.tooltipPosition == null && oldWidget.tooltipPosition != null) {
      _scaleController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.tooltipPosition == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Positioned(
          left: _calculateTooltipX(context),
          top: _calculateTooltipY(context),
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: _buildTooltipContent(),
            ),
          ),
        );
      },
    );
  }

  /// Calculate tooltip X position with screen bounds checking
  double _calculateTooltipX(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final tooltipWidth = 160.0; // Estimated tooltip width
    final padding = 4.w;
    
    double x = widget.tooltipPosition!.dx - (tooltipWidth / 2);
    
    // Keep tooltip within screen bounds
    if (x < padding) {
      x = padding;
    } else if (x + tooltipWidth > screenWidth - padding) {
      x = screenWidth - tooltipWidth - padding;
    }
    
    return x;
  }

  /// Calculate tooltip Y position with screen bounds checking
  double _calculateTooltipY(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    final tooltipHeight = 140.0; // Estimated tooltip height
    final padding = 2.h;
    
    double y = widget.tooltipPosition!.dy - tooltipHeight - 10;
    
    // If tooltip would go above screen, show below the point
    if (y < padding) {
      y = widget.tooltipPosition!.dy + 20;
    }
    
    // Ensure tooltip doesn't go below screen
    if (y + tooltipHeight > screenHeight - padding) {
      y = screenHeight - tooltipHeight - padding;
    }
    
    return y;
  }

  /// Build the appropriate tooltip content
  Widget _buildTooltipContent() {
    if (widget.selectedMeasurement != null) {
      return _buildMeasurementTooltip();
    } else if (widget.hoveredPercentile != null) {
      return _buildPercentileTooltip();
    }
    return const SizedBox.shrink();
  }

  /// Build detailed measurement tooltip
  Widget _buildMeasurementTooltip() {
    final measurement = widget.selectedMeasurement!;
    final percentile = measurement.percentile ?? 
        _calculatePercentile(measurement.value, measurement.ageInMonths);

    return GestureDetector(
      onTap: widget.onDismiss,
      child: Container(
        width: 160,
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: ThemeAwareColors.getCardColor(context),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: ThemeAwareColors.getShadowColor(context),
              blurRadius: 16,
              offset: const Offset(0, 8),
            ),
            BoxShadow(
              color: ThemeAwareColors.getShadowColor(context).withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with measurement type icon
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(1.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getMeasurementIcon(),
                    size: 16,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    _getMeasurementTitle(),
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: ThemeAwareColors.getPrimaryTextColor(context),
                    ),
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 2.h),
            
            // Measurement value
            Text(
              measurement.displayValue,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w700,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            
            SizedBox(height: 1.h),
            
            // Date and age
            Text(
              _formatMeasurementDate(measurement.measuredAt),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
            
            Text(
              'Age: ${measurement.ageInMonths.toStringAsFixed(1)} months',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
            
            SizedBox(height: 1.5.h),
            
            // Percentile badge
            _buildPercentileBadge(percentile),
            
            // Growth velocity if available
            if (measurement.velocityFromPrevious != null) ...[
              SizedBox(height: 1.h),
              _buildVelocityInfo(measurement.velocityFromPrevious!),
            ],
            
            // Notes if available
            if (measurement.notes?.isNotEmpty == true) ...[
              SizedBox(height: 1.h),
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getSurfaceColor(context).withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  measurement.notes!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ),
            ],
            
            SizedBox(height: 2.h),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    'Edit',
                    Icons.edit_outlined,
                    () => widget.onEditMeasurement?.call(measurement),
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: _buildActionButton(
                    'Close',
                    Icons.close_outlined,
                    widget.onDismiss,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build percentile curve tooltip
  Widget _buildPercentileTooltip() {
    final percentile = widget.hoveredPercentile!;
    final ageInMonths = widget.hoveredPercentileAge ?? 0.0;
    
    // Get the percentile value at this age
    final percentileValue = _getPercentileValueAtAge(percentile, ageInMonths);
    
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: _getPercentileColor(percentile).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Percentile header
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12,
                height: 3,
                decoration: BoxDecoration(
                  color: _getPercentileColor(percentile),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              ),
              SizedBox(width: 2.w),
              Text(
                '$percentile Percentile',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: _getPercentileColor(percentile),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 1.h),
          
          // Age and value
          Text(
            'Age: ${ageInMonths.toStringAsFixed(1)} months',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          
          if (percentileValue != null)
            Text(
              'Value: ${percentileValue.toStringAsFixed(1)} ${_getDisplayUnit()}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          
          SizedBox(height: 1.h),
          
          // Percentile interpretation
          Text(
            _getPercentileInterpretation(percentile),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  /// Build percentile badge with color coding
  Widget _buildPercentileBadge(double percentile) {
    final color = _getPercentileColor(percentile);
    final icon = _getPercentileIcon(percentile);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14,
            color: color,
          ),
          SizedBox(width: 1.w),
          Text(
            '${percentile.toStringAsFixed(1)}${_getPercentileSuffix(percentile.toInt())} percentile',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: color,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build growth velocity information
  Widget _buildVelocityInfo(GrowthVelocity velocity) {
    return Container(
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: velocity.isNormal 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                velocity.isNormal ? Icons.trending_up : Icons.warning_outlined,
                size: 14,
                color: velocity.isNormal ? Colors.green : Colors.orange,
              ),
              SizedBox(width: 1.w),
              Text(
                'Growth Velocity',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: velocity.isNormal ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: 0.5.h),
          Text(
            '${velocity.velocityPerMonth.toStringAsFixed(2)} ${_getDisplayUnit()}/month',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          Text(
            velocity.interpretation,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  /// Build action button
  Widget _buildActionButton(String label, IconData icon, VoidCallback? onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 14,
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(width: 1.w),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  double _calculatePercentile(double value, double ageInMonths) {
    try {
      return WHODataService.calculateExactPercentile(
        value, 
        ageInMonths, 
        widget.measurementType, 
        widget.gender
      );
    } catch (e) {
      return 50.0; // Default to median if calculation fails
    }
  }

  double? _getPercentileValueAtAge(String percentile, double ageInMonths) {
    try {
      final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
      var value = WHODataService.getPercentileValue(
        percentileNum,
        ageInMonths,
        widget.measurementType,
        widget.gender,
      );
      
      // Convert to display units if needed
      if (!widget.isMetric) {
        value = _convertValueForDisplay(value);
      }
      
      return value;
    } catch (e) {
      return null;
    }
  }

  double _convertValueForDisplay(double value) {
    if (!widget.isMetric) {
      switch (widget.measurementType.toLowerCase()) {
        case 'weight':
          return value * 2.20462; // kg to lbs
        case 'height':
        case 'length':
        case 'head_circumference':
        case 'head circumference':
          return value / 2.54; // cm to inches
      }
    }
    return value;
  }

  String _getDisplayUnit() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return widget.isMetric ? 'kg' : 'lbs';
      case 'height':
      case 'length':
      case 'head_circumference':
      case 'head circumference':
        return widget.isMetric ? 'cm' : 'in';
      default:
        return widget.isMetric ? 'kg' : 'lbs';
    }
  }

  IconData _getMeasurementIcon() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return Icons.monitor_weight_outlined;
      case 'height':
      case 'length':
        return Icons.height_outlined;
      case 'head_circumference':
      case 'head circumference':
        return Icons.circle_outlined;
      default:
        return Icons.straighten_outlined;
    }
  }

  String _getMeasurementTitle() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return 'Weight';
      case 'height':
      case 'length':
        return 'Height';
      case 'head_circumference':
      case 'head circumference':
        return 'Head Circumference';
      default:
        return 'Measurement';
    }
  }

  Color _getPercentileColor(dynamic percentile) {
    double percentileValue;
    if (percentile is String) {
      percentileValue = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
    } else {
      percentileValue = percentile.toDouble();
    }
    
    if (percentileValue < 3.0) {
      return const Color(0xFFDC2626); // Critical low - red
    } else if (percentileValue > 97.0) {
      return const Color(0xFF9333EA); // Critical high - purple
    } else if (percentileValue < 10.0) {
      return const Color(0xFFEA580C); // Low normal - orange
    } else if (percentileValue > 90.0) {
      return const Color(0xFF7C3AED); // High normal - violet
    } else {
      return const Color(0xFF059669); // Normal range - green
    }
  }

  IconData _getPercentileIcon(double percentile) {
    if (percentile < 3.0 || percentile > 97.0) {
      return Icons.warning_outlined;
    } else if (percentile < 10.0 || percentile > 90.0) {
      return Icons.info_outlined;
    } else {
      return Icons.check_circle_outlined;
    }
  }

  String _getPercentileSuffix(int percentile) {
    if (percentile >= 11 && percentile <= 13) return 'th';
    switch (percentile % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  }

  String _getPercentileInterpretation(String percentile) {
    final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
    
    if (percentileNum == 50.0) {
      return 'This is the median value - exactly average for this age.';
    } else if (percentileNum < 3.0) {
      return 'This is significantly below average and may require medical attention.';
    } else if (percentileNum > 97.0) {
      return 'This is significantly above average and may require medical attention.';
    } else if (percentileNum < 10.0) {
      return 'This is below average but within the normal range.';
    } else if (percentileNum > 90.0) {
      return 'This is above average but within the normal range.';
    } else {
      return 'This is within the normal range for this age.';
    }
  }

  String _formatMeasurementDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks == 1 ? '' : 's'} ago';
    } else {
      final months = (difference.inDays / 30).floor();
      return '$months month${months == 1 ? '' : 's'} ago';
    }
  }
}