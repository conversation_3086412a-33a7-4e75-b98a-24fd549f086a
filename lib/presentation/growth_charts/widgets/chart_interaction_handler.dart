import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/measurement.dart';
import '../../../services/who_data_service.dart';
import 'chart_data_processor.dart';

/// Handles all user interactions with growth charts including tap, zoom, pan, and hover
class ChartInteractionHandler {
  final String measurementType;
  final String gender;
  final bool isMetric;
  final DateTime birthDate;
  final List<Measurement> measurements;
  final ChartDataProcessor dataProcessor;
  
  // Interaction state
  double _zoomLevel = 1.0;
  Offset _panOffset = Offset.zero;
  Measurement? _selectedMeasurement;
  String? _hoveredPercentile;
  Offset? _tooltipPosition;
  
  // Animation controllers
  AnimationController? _zoomAnimationController;
  AnimationController? _panAnimationController;
  AnimationController? _tooltipAnimationController;
  
  // Callbacks
  final Function(Measurement)? onMeasurementTap;
  final Function(Measurement)? onMeasurementEdit;
  final Function(String, double)? onPercentileCurveHover;
  final Function(double)? onZoomChanged;
  final Function(Offset)? onPanChanged;
  final VoidCallback? onInteractionStart;
  final VoidCallback? onInteractionEnd;

  ChartInteractionHandler({
    required this.measurementType,
    required this.gender,
    required this.isMetric,
    required this.birthDate,
    required this.measurements,
    required this.dataProcessor,
    this.onMeasurementTap,
    this.onMeasurementEdit,
    this.onPercentileCurveHover,
    this.onZoomChanged,
    this.onPanChanged,
    this.onInteractionStart,
    this.onInteractionEnd,
  });

  // Getters for current state
  double get zoomLevel => _zoomLevel;
  Offset get panOffset => _panOffset;
  Measurement? get selectedMeasurement => _selectedMeasurement;
  String? get hoveredPercentile => _hoveredPercentile;
  Offset? get tooltipPosition => _tooltipPosition;

  /// Initialize animation controllers
  void initializeAnimations(TickerProvider vsync) {
    _zoomAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: vsync,
    );
    
    _panAnimationController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: vsync,
    );
    
    _tooltipAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: vsync,
    );
  }

  /// Dispose animation controllers
  void dispose() {
    try {
      _zoomAnimationController?.dispose();
      _zoomAnimationController = null;
    } catch (e) {
      // Controller already disposed
    }
    try {
      _panAnimationController?.dispose();
      _panAnimationController = null;
    } catch (e) {
      // Controller already disposed
    }
    try {
      _tooltipAnimationController?.dispose();
      _tooltipAnimationController = null;
    } catch (e) {
      // Controller already disposed
    }
  }

  /// Handle chart touch events
  void handleChartTouch(FlTouchEvent event, LineTouchResponse? response) {
    if (event is FlTapUpEvent) {
      _handleTapUp(event, response);
    } else if (event is FlLongPressStart) {
      _handleLongPressStart(event, response);
    } else if (event is FlPointerHoverEvent) {
      _handleHover(event, response);
    } else if (event is FlPointerExitEvent) {
      _handlePointerExit();
    }
  }

  /// Handle tap up events for measurement selection
  void _handleTapUp(FlTapUpEvent event, LineTouchResponse? response) {
    HapticFeedback.lightImpact();
    
    if (response?.lineBarSpots?.isNotEmpty == true) {
      final spot = response!.lineBarSpots!.first;
      
      // Check if it's a measurement data point (last line in the chart)
      final measurementLineIndex = _getMeasurementLineIndex();
      if (spot.barIndex == measurementLineIndex) {
        final spotIndex = spot.spotIndex;
        if (spotIndex < measurements.length) {
          final measurement = measurements[spotIndex];
          _selectMeasurement(measurement, event.localPosition);
          onMeasurementTap?.call(measurement);
        }
      } else {
        // Tapped on percentile curve
        final percentile = _getPercentileFromBarIndex(spot.barIndex);
        if (percentile != null) {
          _handlePercentileCurveTap(percentile, spot.x);
        }
      }
    } else {
      // Tapped on empty area - clear selection
      _clearSelection();
    }
  }

  /// Handle long press for measurement editing
  void _handleLongPressStart(FlLongPressStart event, LineTouchResponse? response) {
    HapticFeedback.mediumImpact();
    
    if (response?.lineBarSpots?.isNotEmpty == true) {
      final spot = response!.lineBarSpots!.first;
      final measurementLineIndex = _getMeasurementLineIndex();
      
      if (spot.barIndex == measurementLineIndex) {
        final spotIndex = spot.spotIndex;
        if (spotIndex < measurements.length) {
          final measurement = measurements[spotIndex];
          onMeasurementEdit?.call(measurement);
        }
      }
    }
  }

  /// Handle hover events for tooltips
  void _handleHover(FlPointerHoverEvent event, LineTouchResponse? response) {
    if (response?.lineBarSpots?.isNotEmpty == true) {
      final spot = response!.lineBarSpots!.first;
      final measurementLineIndex = _getMeasurementLineIndex();
      
      if (spot.barIndex == measurementLineIndex) {
        // Hovering over measurement point
        final spotIndex = spot.spotIndex;
        if (spotIndex < measurements.length) {
          final measurement = measurements[spotIndex];
          _showMeasurementTooltip(measurement, event.localPosition);
        }
      } else {
        // Hovering over percentile curve
        final percentile = _getPercentileFromBarIndex(spot.barIndex);
        if (percentile != null) {
          _showPercentileTooltip(percentile, spot.x, event.localPosition);
        }
      }
    } else {
      _hideTooltip();
    }
  }

  /// Handle pointer exit events
  void _handlePointerExit() {
    _hideTooltip();
    _hoveredPercentile = null;
  }

  /// Handle scale/zoom gestures
  void handleScaleStart(ScaleStartDetails details) {
    onInteractionStart?.call();
    _panAnimationController?.stop();
    _zoomAnimationController?.stop();
  }

  /// Handle scale/zoom updates with smooth animations
  void handleScaleUpdate(ScaleUpdateDetails details) {
    // Update zoom level with constraints
    final newZoomLevel = (_zoomLevel * details.scale).clamp(0.5, 3.0);
    if (newZoomLevel != _zoomLevel) {
      _zoomLevel = newZoomLevel;
      onZoomChanged?.call(_zoomLevel);
    }

    // Update pan offset with bounds checking
    final newPanOffset = _panOffset + details.focalPointDelta;
    final constrainedPanOffset = _constrainPanOffset(newPanOffset);
    if (constrainedPanOffset != _panOffset) {
      _panOffset = constrainedPanOffset;
      onPanChanged?.call(_panOffset);
    }
  }

  /// Handle scale/zoom end with smooth animations
  void handleScaleEnd(ScaleEndDetails details) {
    onInteractionEnd?.call();
    
    // Animate back to bounds if needed
    final constrainedPanOffset = _constrainPanOffset(_panOffset);
    if (constrainedPanOffset != _panOffset) {
      _animatePanTo(constrainedPanOffset);
    }
    
    // Snap zoom to common levels if close
    final snappedZoom = _snapZoomLevel(_zoomLevel);
    if (snappedZoom != _zoomLevel) {
      _animateZoomTo(snappedZoom);
    }
  }

  /// Select a measurement and show detailed tooltip
  void _selectMeasurement(Measurement measurement, Offset position) {
    _selectedMeasurement = measurement;
    _tooltipPosition = position;
    _tooltipAnimationController?.forward();
  }

  /// Clear measurement selection
  void _clearSelection() {
    _selectedMeasurement = null;
    _tooltipPosition = null;
    _tooltipAnimationController?.reverse();
  }

  /// Show measurement tooltip with detailed information
  void _showMeasurementTooltip(Measurement measurement, Offset position) {
    _selectedMeasurement = measurement;
    _tooltipPosition = position;
    _tooltipAnimationController?.forward();
  }

  /// Show percentile curve tooltip
  void _showPercentileTooltip(String percentile, double ageInMonths, Offset position) {
    _hoveredPercentile = percentile;
    _tooltipPosition = position;
    onPercentileCurveHover?.call(percentile, ageInMonths);
  }

  /// Hide tooltip
  void _hideTooltip() {
    _tooltipAnimationController?.reverse();
  }

  /// Handle percentile curve tap
  void _handlePercentileCurveTap(String percentile, double ageInMonths) {
    HapticFeedback.selectionClick();
    onPercentileCurveHover?.call(percentile, ageInMonths);
  }

  /// Get measurement line index in the chart data
  int _getMeasurementLineIndex() {
    // Assuming WHO percentile curves come first (7 curves), then measurement data
    return 7; // 0-6 for percentile curves, 7 for measurement data
  }

  /// Get percentile string from bar index
  String? _getPercentileFromBarIndex(int barIndex) {
    const percentiles = ['3rd', '10th', '25th', '50th', '75th', '90th', '97th'];
    if (barIndex >= 0 && barIndex < percentiles.length) {
      return percentiles[barIndex];
    }
    return null;
  }

  /// Constrain pan offset to reasonable bounds
  Offset _constrainPanOffset(Offset offset) {
    const maxPan = 100.0;
    return Offset(
      offset.dx.clamp(-maxPan, maxPan),
      offset.dy.clamp(-maxPan, maxPan),
    );
  }

  /// Snap zoom level to common values
  double _snapZoomLevel(double zoom) {
    const snapLevels = [0.5, 1.0, 1.5, 2.0, 3.0];
    const snapThreshold = 0.1;
    
    for (final level in snapLevels) {
      if ((zoom - level).abs() < snapThreshold) {
        return level;
      }
    }
    return zoom;
  }

  /// Animate zoom to target level
  void _animateZoomTo(double targetZoom) {
    final startZoom = _zoomLevel;
    final animation = Tween<double>(
      begin: startZoom,
      end: targetZoom,
    ).animate(CurvedAnimation(
      parent: _zoomAnimationController!,
      curve: Curves.easeOutCubic,
    ));

    animation.addListener(() {
      _zoomLevel = animation.value;
      onZoomChanged?.call(_zoomLevel);
    });

    _zoomAnimationController?.forward(from: 0);
  }

  /// Animate pan to target offset
  void _animatePanTo(Offset targetOffset) {
    final startOffset = _panOffset;
    final animation = Tween<Offset>(
      begin: startOffset,
      end: targetOffset,
    ).animate(CurvedAnimation(
      parent: _panAnimationController!,
      curve: Curves.easeOutCubic,
    ));

    animation.addListener(() {
      _panOffset = animation.value;
      onPanChanged?.call(_panOffset);
    });

    _panAnimationController?.forward(from: 0);
  }

  /// Reset zoom and pan to default values
  void resetZoomAndPan() {
    _animateZoomTo(1.0);
    _animatePanTo(Offset.zero);
  }

  /// Zoom to fit all measurements
  void zoomToFitMeasurements() {
    if (measurements.isEmpty) return;
    
    // Calculate optimal zoom level based on measurement spread
    if (measurements.length < 2) {
      resetZoomAndPan();
      return;
    }
    
    // Find data bounds
    final minX = measurements.map((m) => m.ageInMonths).reduce((a, b) => a < b ? a : b);
    final maxX = measurements.map((m) => m.ageInMonths).reduce((a, b) => a > b ? a : b);
    final minY = measurements.map((m) => m.value).reduce((a, b) => a < b ? a : b);
    final maxY = measurements.map((m) => m.value).reduce((a, b) => a > b ? a : b);
    
    // Calculate zoom level to fit data with some padding
    final xRange = maxX - minX;
    final yRange = maxY - minY;
    
    if (xRange > 0 && yRange > 0) {
      final targetZoom = (1.5).clamp(0.8, 2.5); // Reasonable zoom range
      _animateZoomTo(targetZoom);
    }
  }

  /// Build measurement tooltip widget
  Widget buildMeasurementTooltip(BuildContext context) {
    if (_selectedMeasurement == null || _tooltipPosition == null) {
      return const SizedBox.shrink();
    }

    final measurement = _selectedMeasurement!;
    final percentile = measurement.percentile ?? 50.0;

    return AnimatedBuilder(
      animation: _tooltipAnimationController!,
      builder: (context, child) {
        return Positioned(
          left: (_tooltipPosition!.dx - 75).clamp(10.0, MediaQuery.of(context).size.width - 160),
          top: (_tooltipPosition!.dy - 120).clamp(10.0, MediaQuery.of(context).size.height - 140),
          child: Transform.scale(
            scale: _tooltipAnimationController!.value,
            child: Opacity(
              opacity: _tooltipAnimationController!.value,
              child: _buildTooltipContent(context, measurement, percentile),
            ),
          ),
        );
      },
    );
  }

  /// Build tooltip content
  Widget _buildTooltipContent(BuildContext context, Measurement measurement, double percentile) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Measurement value
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _getMeasurementIcon(),
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 1.w),
              Text(
                measurement.displayValue,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 0.5.h),
          
          // Date and age
          Text(
            _formatMeasurementDate(measurement.measuredAt),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          
          Text(
            'Age: ${measurement.ageInMonths.toStringAsFixed(1)} months',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          
          SizedBox(height: 1.h),
          
          // Percentile information
          Container(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
            decoration: BoxDecoration(
              color: _getPercentileColor(percentile).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  _getPercentileIcon(percentile),
                  size: 14,
                  color: _getPercentileColor(percentile),
                ),
                SizedBox(width: 1.w),
                Text(
                  '${percentile.toStringAsFixed(1)}${_getPercentileSuffix(percentile.toInt())} percentile',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: _getPercentileColor(percentile),
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          
          // Notes if available
          if (measurement.notes?.isNotEmpty == true) ...[
            SizedBox(height: 1.h),
            Text(
              measurement.notes!,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
          
          // Action hint
          SizedBox(height: 1.h),
          Text(
            'Long press to edit',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Get icon for measurement type
  IconData _getMeasurementIcon() {
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return Icons.monitor_weight_outlined;
      case 'height':
      case 'length':
        return Icons.height_outlined;
      case 'head_circumference':
      case 'head circumference':
        return Icons.circle_outlined;
      default:
        return Icons.straighten_outlined;
    }
  }

  /// Get color based on percentile value
  Color _getPercentileColor(double percentile) {
    if (percentile < 3.0) {
      return const Color(0xFFDC2626); // Critical low - red
    } else if (percentile > 97.0) {
      return const Color(0xFF9333EA); // Critical high - purple
    } else if (percentile < 10.0) {
      return const Color(0xFFEA580C); // Low normal - orange
    } else if (percentile > 90.0) {
      return const Color(0xFF7C3AED); // High normal - violet
    } else {
      return const Color(0xFF059669); // Normal range - green
    }
  }

  /// Get icon based on percentile value
  IconData _getPercentileIcon(double percentile) {
    if (percentile < 3.0 || percentile > 97.0) {
      return Icons.warning_outlined;
    } else if (percentile < 10.0 || percentile > 90.0) {
      return Icons.info_outlined;
    } else {
      return Icons.check_circle_outlined;
    }
  }

  /// Get percentile suffix (st, nd, rd, th)
  String _getPercentileSuffix(int percentile) {
    if (percentile >= 11 && percentile <= 13) return 'th';
    switch (percentile % 10) {
      case 1: return 'st';
      case 2: return 'nd';
      case 3: return 'rd';
      default: return 'th';
    }
  }

  /// Format measurement date for display
  String _formatMeasurementDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks == 1 ? '' : 's'} ago';
    } else {
      final months = (difference.inDays / 30).floor();
      return '$months month${months == 1 ? '' : 's'} ago';
    }
  }
}