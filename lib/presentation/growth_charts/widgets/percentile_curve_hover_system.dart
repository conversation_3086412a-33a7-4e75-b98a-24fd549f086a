import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/who_data_service.dart';

/// Handles hover effects and information display for WHO percentile curves
class PercentileCurveHoverSystem extends StatefulWidget {
  final String? hoveredPercentile;
  final double? hoveredAge;
  final Offset? hoverPosition;
  final String measurementType;
  final String gender;
  final bool isMetric;
  final Function(String?)? onPercentileHover;

  const PercentileCurveHoverSystem({
    super.key,
    this.hoveredPercentile,
    this.hoveredAge,
    this.hoverPosition,
    required this.measurementType,
    required this.gender,
    required this.isMetric,
    this.onPercentileHover,
  });

  @override
  State<PercentileCurveHoverSystem> createState() => _PercentileCurveHoverSystemState();
}

class _PercentileCurveHoverSystemState extends State<PercentileCurveHoverSystem>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(PercentileCurveHoverSystem oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.hoveredPercentile != null && oldWidget.hoveredPercentile == null) {
      _animationController.forward();
    } else if (widget.hoveredPercentile == null && oldWidget.hoveredPercentile != null) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.hoveredPercentile == null || widget.hoverPosition == null) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Positioned(
          left: _calculateHoverX(context),
          top: _calculateHoverY(context),
          child: Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _fadeAnimation.value,
              child: _buildHoverCard(),
            ),
          ),
        );
      },
    );
  }

  /// Calculate hover card X position
  double _calculateHoverX(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final cardWidth = 140.0;
    final padding = 4.w;
    
    double x = widget.hoverPosition!.dx - (cardWidth / 2);
    
    if (x < padding) {
      x = padding;
    } else if (x + cardWidth > screenWidth - padding) {
      x = screenWidth - cardWidth - padding;
    }
    
    return x;
  }

  /// Calculate hover card Y position
  double _calculateHoverY(BuildContext context) {
    final cardHeight = 100.0;
    double y = widget.hoverPosition!.dy - cardHeight - 15;
    
    // If card would go above screen, show below the hover point
    if (y < 2.h) {
      y = widget.hoverPosition!.dy + 15;
    }
    
    return y;
  }

  /// Build the hover information card
  Widget _buildHoverCard() {
    final percentile = widget.hoveredPercentile!;
    final age = widget.hoveredAge ?? 0.0;
    final percentileValue = _getPercentileValueAtAge(percentile, age);
    
    return Container(
      width: 140,
      padding: EdgeInsets.all(2.5.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
          BoxShadow(
            color: _getPercentileColor(percentile).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: _getPercentileColor(percentile).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Percentile header with visual indicator
          Row(
            children: [
              Container(
                width: 16,
                height: 3,
                decoration: BoxDecoration(
                  color: _getPercentileColor(percentile),
                  borderRadius: BorderRadius.circular(1.5),
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  '$percentile Percentile',
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: _getPercentileColor(percentile),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 1.5.h),
          
          // Age information
          _buildInfoRow(
            'Age',
            '${age.toStringAsFixed(1)} months',
            Icons.schedule_outlined,
          ),
          
          SizedBox(height: 1.h),
          
          // Value at this percentile
          if (percentileValue != null)
            _buildInfoRow(
              'Value',
              '${percentileValue.toStringAsFixed(1)} ${_getDisplayUnit()}',
              _getMeasurementIcon(),
            ),
          
          SizedBox(height: 1.5.h),
          
          // Percentile interpretation
          _buildPercentileInterpretation(percentile),
        ],
      ),
    );
  }

  /// Build information row with icon
  Widget _buildInfoRow(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(
          icon,
          size: 12,
          color: ThemeAwareColors.getSecondaryTextColor(context),
        ),
        SizedBox(width: 1.5.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  fontSize: 10,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build percentile interpretation badge
  Widget _buildPercentileInterpretation(String percentile) {
    final interpretation = _getPercentileInterpretation(percentile);
    final color = _getPercentileColor(percentile);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.8.h),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            _getPercentileStatusIcon(percentile),
            size: 12,
            color: color,
          ),
          SizedBox(width: 1.w),
          Expanded(
            child: Text(
              interpretation,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 10,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  double? _getPercentileValueAtAge(String percentile, double ageInMonths) {
    try {
      final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
      var value = WHODataService.getPercentileValue(
        percentileNum,
        ageInMonths,
        widget.measurementType,
        widget.gender,
      );
      
      // Convert to display units if needed
      if (!widget.isMetric) {
        value = _convertValueForDisplay(value);
      }
      
      return value;
    } catch (e) {
      return null;
    }
  }

  double _convertValueForDisplay(double value) {
    if (!widget.isMetric) {
      switch (widget.measurementType.toLowerCase()) {
        case 'weight':
          return value * 2.20462; // kg to lbs
        case 'height':
        case 'length':
        case 'head_circumference':
        case 'head circumference':
          return value / 2.54; // cm to inches
      }
    }
    return value;
  }

  String _getDisplayUnit() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return widget.isMetric ? 'kg' : 'lbs';
      case 'height':
      case 'length':
      case 'head_circumference':
      case 'head circumference':
        return widget.isMetric ? 'cm' : 'in';
      default:
        return widget.isMetric ? 'kg' : 'lbs';
    }
  }

  IconData _getMeasurementIcon() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return Icons.monitor_weight_outlined;
      case 'height':
      case 'length':
        return Icons.height_outlined;
      case 'head_circumference':
      case 'head circumference':
        return Icons.circle_outlined;
      default:
        return Icons.straighten_outlined;
    }
  }

  Color _getPercentileColor(String percentile) {
    final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
    
    if (percentileNum <= 3.0) {
      return const Color(0xFFDC2626); // Critical low - red
    } else if (percentileNum >= 97.0) {
      return const Color(0xFF9333EA); // Critical high - purple
    } else if (percentileNum <= 10.0) {
      return const Color(0xFFEA580C); // Low normal - orange
    } else if (percentileNum >= 90.0) {
      return const Color(0xFF7C3AED); // High normal - violet
    } else if (percentileNum == 50.0) {
      return const Color(0xFF059669); // Median - green (emphasized)
    } else {
      return const Color(0xFF0284C7); // Normal range - blue
    }
  }

  IconData _getPercentileStatusIcon(String percentile) {
    final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
    
    if (percentileNum <= 3.0 || percentileNum >= 97.0) {
      return Icons.warning_outlined;
    } else if (percentileNum <= 10.0 || percentileNum >= 90.0) {
      return Icons.info_outlined;
    } else if (percentileNum == 50.0) {
      return Icons.center_focus_strong_outlined;
    } else {
      return Icons.check_circle_outlined;
    }
  }

  String _getPercentileInterpretation(String percentile) {
    final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
    
    if (percentileNum == 50.0) {
      return 'Median';
    } else if (percentileNum <= 3.0) {
      return 'Critical Low';
    } else if (percentileNum >= 97.0) {
      return 'Critical High';
    } else if (percentileNum <= 10.0) {
      return 'Below Average';
    } else if (percentileNum >= 90.0) {
      return 'Above Average';
    } else if (percentileNum == 25.0) {
      return 'Lower Quartile';
    } else if (percentileNum == 75.0) {
      return 'Upper Quartile';
    } else {
      return 'Normal Range';
    }
  }
}

/// Enhanced percentile curve information provider
class PercentileCurveInfo {
  final String percentile;
  final Color color;
  final String description;
  final String medicalSignificance;
  final bool isImportant;
  final bool requiresAttention;

  const PercentileCurveInfo({
    required this.percentile,
    required this.color,
    required this.description,
    required this.medicalSignificance,
    required this.isImportant,
    required this.requiresAttention,
  });

  static PercentileCurveInfo getInfo(String percentile) {
    final percentileNum = double.parse(percentile.replaceAll(RegExp(r'[^\d.]'), ''));
    
    switch (percentileNum.toInt()) {
      case 3:
        return const PercentileCurveInfo(
          percentile: '3rd',
          color: Color(0xFFDC2626),
          description: 'Critical Lower Boundary',
          medicalSignificance: 'Values below this line may indicate growth concerns requiring medical evaluation.',
          isImportant: true,
          requiresAttention: true,
        );
      case 10:
        return const PercentileCurveInfo(
          percentile: '10th',
          color: Color(0xFFEA580C),
          description: 'Lower Normal Boundary',
          medicalSignificance: 'Values below this line are in the lower normal range but should be monitored.',
          isImportant: false,
          requiresAttention: false,
        );
      case 25:
        return const PercentileCurveInfo(
          percentile: '25th',
          color: Color(0xFFF59E0B),
          description: 'Lower Quartile',
          medicalSignificance: 'This represents the lower quartile - 25% of children are below this line.',
          isImportant: false,
          requiresAttention: false,
        );
      case 50:
        return const PercentileCurveInfo(
          percentile: '50th',
          color: Color(0xFF059669),
          description: 'Median (Average)',
          medicalSignificance: 'This is the median line - exactly half of children are above and below this line.',
          isImportant: true,
          requiresAttention: false,
        );
      case 75:
        return const PercentileCurveInfo(
          percentile: '75th',
          color: Color(0xFF0284C7),
          description: 'Upper Quartile',
          medicalSignificance: 'This represents the upper quartile - 75% of children are below this line.',
          isImportant: false,
          requiresAttention: false,
        );
      case 90:
        return const PercentileCurveInfo(
          percentile: '90th',
          color: Color(0xFF7C3AED),
          description: 'Upper Normal Boundary',
          medicalSignificance: 'Values above this line are in the upper normal range but should be monitored.',
          isImportant: false,
          requiresAttention: false,
        );
      case 97:
        return const PercentileCurveInfo(
          percentile: '97th',
          color: Color(0xFF9333EA),
          description: 'Critical Upper Boundary',
          medicalSignificance: 'Values above this line may indicate growth concerns requiring medical evaluation.',
          isImportant: true,
          requiresAttention: true,
        );
      default:
        return const PercentileCurveInfo(
          percentile: 'Unknown',
          color: Color(0xFF6B7280),
          description: 'Unknown Percentile',
          medicalSignificance: 'This percentile is not part of the standard WHO growth chart set.',
          isImportant: false,
          requiresAttention: false,
        );
    }
  }
}