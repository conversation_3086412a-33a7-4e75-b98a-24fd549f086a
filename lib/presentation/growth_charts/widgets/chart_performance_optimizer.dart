import 'dart:isolate';
import 'package:flutter/foundation.dart';

/// Performance optimization utilities for growth charts
class ChartPerformanceOptimizer {
  static final Map<String, dynamic> _cache = {};
  
  /// Memoized calculation of WHO percentile data
  static Future<List<Map<String, dynamic>>> getPercentileData({
    required String cacheKey,
    required Future<List<Map<String, dynamic>>> Function() calculator,
  }) async {
    if (_cache.containsKey(cacheKey)) {
      return _cache[cacheKey];
    }
    
    final result = await calculator();
    _cache[cacheKey] = result;
    return result;
  }
  
  /// Process large datasets in isolate to prevent UI blocking
  static Future<List<Map<String, dynamic>>> processDataInIsolate({
    required List<Map<String, dynamic>> rawData,
    required String dateRange,
    required DateTime birthDate,
  }) async {
    if (rawData.length < 100) {
      // For small datasets, process on main thread
      return _processData(rawData, dateRange, birthDate);
    }
    
    // For large datasets, use isolate
    return await compute(_processDataIsolate, {
      'data': rawData,
      'dateRange': dateRange,
      'birthDate': birthDate,
    });
  }
  
  static List<Map<String, dynamic>> _processData(
    List<Map<String, dynamic>> data,
    String dateRange,
    DateTime birthDate,
  ) {
    // Implementation moved from main widget
    return data; // Simplified for example
  }
  
  static List<Map<String, dynamic>> _processDataIsolate(Map<String, dynamic> params) {
    return _processData(
      params['data'],
      params['dateRange'],
      params['birthDate'],
    );
  }
  
  /// Clear cache when memory pressure is high
  static void clearCache() {
    _cache.clear();
  }
}