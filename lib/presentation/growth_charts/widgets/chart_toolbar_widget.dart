import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ChartToolbarWidget extends StatelessWidget {
  final String selectedDateRange;
  final bool isMetric;
  final bool isFreePlan;
  final Function(String) onDateRangeChanged;
  final Function(bool) onUnitToggle;
  final VoidCallback onSharePressed;

  const ChartToolbarWidget({
    super.key,
    required this.selectedDateRange,
    required this.isMetric,
    required this.isFreePlan,
    required this.onDateRangeChanged,
    required this.onUnitToggle,
    required this.onSharePressed,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    final List<String> dateRanges = [
      '6 months',
      '1 year',
      '2 years',
      '3 years',
      '4 years',
      '5 years'
    ];

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
              ? Colors.black.withValues(alpha: 0.3)
              : Colors.grey.withValues(alpha: 0.2),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        children: [
          // Primary Controls Row
          Row(
            children: [
              // Date Range Selector
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Time Period',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    GestureDetector(
                      onTap: isFreePlan ? () => onDateRangeChanged(selectedDateRange) : null,
                      child: Container(
                        height: 5.h,
                        decoration: BoxDecoration(
                          color: isFreePlan 
                              ? colorScheme.surface.withAlpha(150)
                              : colorScheme.surface,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isFreePlan
                                ? colorScheme.outline.withAlpha(100)
                                : (isDarkMode
                                    ? colorScheme.primary.withValues(alpha: 0.8)
                                    : colorScheme.primary.withValues(alpha: 0.3)),
                            width: 1.5,
                          ),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: selectedDateRange,
                            onChanged: isFreePlan ? null : (value) {
                              if (value != null) {
                                onDateRangeChanged(value);
                              }
                            },
                          items: dateRanges.map((range) {
                            return DropdownMenuItem<String>(
                              value: range,
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 3.w),
                                child: Row(
                                  children: [
                                    CustomIconWidget(
                                      iconName: _getRangeIcon(range),
                                      color: Theme.of(context).colorScheme.primary,
                                      size: 16,
                                    ),
                                    SizedBox(width: 2.w),
                                    Text(
                                      range,
                                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                        fontWeight: FontWeight.w500,
                                        color: isFreePlan 
                                            ? colorScheme.onSurface.withAlpha(100)
                                            : colorScheme.onSurface,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          }).toList(),
                          icon: Padding(
                            padding: EdgeInsets.only(right: 3.w),
                            child: CustomIconWidget(
                              iconName: 'expand_more',
                              color: isFreePlan 
                                  ? Theme.of(context).colorScheme.primary.withAlpha(100)
                                  : Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          dropdownColor: colorScheme.surface,
                          elevation: 8,
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                  ],
                ),
              ),

              SizedBox(width: 4.w),

              // Unit Toggle
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Units',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Container(
                      height: 5.h,
                      decoration: BoxDecoration(
                        color: colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: colorScheme.outline.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        children: [
                          _buildUnitButton('Metric', isMetric, true, context),
                          _buildUnitButton('Imperial', !isMetric, false, context),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Additional Controls Row
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // Chart Information
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.05),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.primary.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      CustomIconWidget(
                        iconName: 'info',
                        color: colorScheme.primary,
                        size: 16,
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          'WHO/CDC Growth Standards - Showing ${_getPercentileDescription()}',
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              SizedBox(width: 3.w),

              // Export/Share Button
              Container(
                height: 4.h,
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getAccentColor(context).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: ThemeAwareColors.getAccentColor(context).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: onSharePressed,
                    borderRadius: BorderRadius.circular(8),
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomIconWidget(
                            iconName: 'share',
                            color: ThemeAwareColors.getAccentColor(context),
                            size: 16,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            'Export',
                            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: isFreePlan 
                                  ? ThemeAwareColors.getAccentColor(context).withAlpha(100)
                                  : ThemeAwareColors.getAccentColor(context),
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildUnitButton(String label, bool isSelected, bool isMetricOption, BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Expanded(
      child: GestureDetector(
        onTap: () => onUnitToggle(isMetricOption),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          margin: EdgeInsets.all(1.w),
          decoration: BoxDecoration(
            color: isSelected
                ? colorScheme.primary
                : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: colorScheme.primary.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: SizedBox(
            height: double.infinity,
            child: Center(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isSelected
                      ? colorScheme.onPrimary
                      : colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getRangeIcon(String range) {
    switch (range) {
      case '6 months':
        return 'calendar_view_month';
      case '1 year':
        return 'calendar_today';
      case '2 years':
        return 'date_range';
      case '3 years':
        return 'timeline';
      default:
        return 'calendar_today';
    }
  }

  String _getPercentileDescription() {
    switch (selectedDateRange) {
      case '6 months':
        return '6-month growth percentiles';
      case '1 year':
        return 'First year growth percentiles';
      case '2 years':
        return 'Early childhood percentiles';
      case '3 years':
        return 'Early childhood percentiles';
      case '4 years':
        return 'Preschool growth percentiles';
      case '5 years':
        return 'Complete WHO percentiles (0-5 years)';
      default:
        return 'Growth percentiles';
    }
  }
}
