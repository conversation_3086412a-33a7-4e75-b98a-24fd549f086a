import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/who_percentile_service.dart';
import '../../../widgets/modern_date_time_picker.dart';

class MeasurementEntrySheet extends StatefulWidget {
  final String title;
  final String subtitle;
  final int measurementType;
  final bool isMetric;
  final Future<bool> Function(double, String, String, DateTime) onSave;

  const MeasurementEntrySheet({
    super.key,
    required this.title,
    required this.subtitle,
    required this.measurementType,
    required this.isMetric,
    required this.onSave,
  });

  @override
  State<MeasurementEntrySheet> createState() => _MeasurementEntrySheetState();
}

class _MeasurementEntrySheetState extends State<MeasurementEntrySheet>
    with TickerProviderStateMixin {
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  late String _selectedUnit;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  double? _calculatedPercentile;
  String _percentileCategory = '';
  bool _isCalculating = false;

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _selectedUnit = _getDefaultUnit();
    _valueController.addListener(_onValueChanged);

    _animationController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _valueController.removeListener(_onValueChanged);
    _valueController.dispose();
    _notesController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onValueChanged() {
    if (_valueController.text.isNotEmpty) {
      final value = double.tryParse(_valueController.text);
      if (value != null) {
        _calculatePercentile(value);
      }
    } else {
      setState(() {
        _calculatedPercentile = null;
        _percentileCategory = '';
      });
    }
  }

  void _calculatePercentile(double value) {
    setState(() {
      _isCalculating = true;
    });

    // Simulate calculation delay for smooth UX
    Future.delayed(Duration(milliseconds: 300), () {
      if (mounted) {
        final ageInMonths = _getAgeInMonths(_selectedDate);
        final percentile = WHOPercentileService.calculatePercentile(
          value,
          ageInMonths,
          _getMeasurementTypeString(),
          'boys', // TODO: Get from baby profile - will be fixed in future update
        );

        setState(() {
          _calculatedPercentile = percentile;
          _percentileCategory = _getPercentileCategory(percentile);
          _isCalculating = false;
        });
      }
    });
  }

  double _getAgeInMonths(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    return difference.inDays / 30.44;
  }

  String _getMeasurementTypeString() {
    switch (widget.measurementType) {
      case 0:
        return 'weight';
      case 1:
        return 'height';
      case 2:
        return 'head_circumference';
      default:
        return 'weight';
    }
  }

  String _getPercentileCategory(double percentile) {
    if (percentile < 3) return 'Below 3rd percentile';
    if (percentile < 10) return 'Below 10th percentile';
    if (percentile < 25) return 'Below average';
    if (percentile < 50) return 'Below average';
    if (percentile < 75) return 'Average';
    if (percentile < 90) return 'Above average';
    if (percentile < 97) return 'Above 90th percentile';
    return 'Above 97th percentile';
  }

  Color _getPercentileColor(double percentile) {
    if (percentile < 3 || percentile > 97) return Colors.red;
    if (percentile < 10 || percentile > 90) return Colors.orange;
    if (percentile < 25 || percentile > 75) {
      return Colors.amber;
    }
    return Colors.green;
  }

  String _getDefaultUnit() {
    switch (widget.measurementType) {
      case 0: // Weight
        return widget.isMetric ? 'kg' : 'lbs';
      case 1: // Height
        return widget.isMetric ? 'cm' : 'in';
      case 2: // Head Circumference
        return widget.isMetric ? 'cm' : 'in';
      default:
        return 'kg';
    }
  }

  String _getMeasurementTitle() {
    switch (widget.measurementType) {
      case 0:
        return 'Weight';
      case 1:
        return 'Height';
      case 2:
        return 'Head Circumference';
      default:
        return 'Weight';
    }
  }

  List<String> _getUnitOptions() {
    switch (widget.measurementType) {
      case 0: // Weight
        return widget.isMetric ? ['kg', 'g'] : ['lbs', 'oz'];
      case 1: // Height
        return widget.isMetric ? ['cm', 'm'] : ['in', 'ft'];
      case 2: // Head Circumference
        return widget.isMetric ? ['cm', 'mm'] : ['in'];
      default:
        return ['kg'];
    }
  }

  String? _validateInput(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a value';
    }

    final numValue = double.tryParse(value);
    if (numValue == null) {
      return 'Please enter a valid number';
    }

    // Validate reasonable ranges
    switch (widget.measurementType) {
      case 0: // Weight
        if (widget.isMetric) {
          if (numValue < 0.5 || numValue > 50) {
            return 'Weight should be between 0.5 and 50 kg';
          }
        } else {
          if (numValue < 1 || numValue > 110) {
            return 'Weight should be between 1 and 110 lbs';
          }
        }
        break;
      case 1: // Height
        if (widget.isMetric) {
          if (numValue < 30 || numValue > 200) {
            return 'Height should be between 30 and 200 cm';
          }
        } else {
          if (numValue < 12 || numValue > 78) {
            return 'Height should be between 12 and 78 inches';
          }
        }
        break;
      case 2: // Head circumference
        if (widget.isMetric) {
          if (numValue < 25 || numValue > 70) {
            return 'Head circumference should be between 25 and 70 cm';
          }
        } else {
          if (numValue < 10 || numValue > 28) {
            return 'Head circumference should be between 10 and 28 inches';
          }
        }
        break;
    }

    return null;
  }


  void _selectDateTime() async {
    final DateTime currentDateTime = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );
    
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: currentDateTime,
      firstDate: DateTime.now().subtract(Duration(days: 365 * 3)),
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != currentDateTime) {
      setState(() {
        _selectedDate = DateTime(picked.year, picked.month, picked.day);
        _selectedTime = TimeOfDay(hour: picked.hour, minute: picked.minute);
      });
      
      // Recalculate percentile with new date
      if (_valueController.text.isNotEmpty) {
        final value = double.tryParse(_valueController.text);
        if (value != null) {
          _calculatePercentile(value);
        }
      }
    }
  }

  void _handleSave() async {
    if (_formKey.currentState!.validate()) {
      final value = double.parse(_valueController.text);
      final notes = _notesController.text;
      
      // Create a DateTime that combines the selected date and time
      final measurementDateTime = DateTime(
        _selectedDate.year,
        _selectedDate.month,
        _selectedDate.day,
        _selectedTime.hour,
        _selectedTime.minute,
      );
      
      final bool success = await widget.onSave(value, _selectedUnit, notes, measurementDateTime);
      Navigator.pop(context, success);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
              0, MediaQuery.of(context).size.height * _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              height: 90.h,
              decoration: BoxDecoration(
                color: ThemeAwareColors.getCardColor(context),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: ThemeAwareColors.getShadowColor(context),
                    blurRadius: 20,
                    offset: Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Handle and Header
                  _buildHeader(),

                  // Content
                  Expanded(
                    child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        padding: EdgeInsets.all(6.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Date and Time Selection
                            _buildDateTimeSection(),

                            SizedBox(height: 4.h),

                            // Value Input with Real-time Validation
                            _buildValueInputSection(),

                            SizedBox(height: 3.h),

                            // Percentile Preview
                            if (_calculatedPercentile != null || _isCalculating)
                              _buildPercentilePreview(),

                            if (_calculatedPercentile != null || _isCalculating)
                              SizedBox(height: 3.h),

                            // Notes Section
                            _buildNotesSection(),

                            SizedBox(height: 4.h),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Save Button
                  _buildSaveButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: ThemeAwareColors.getDividerColor(context),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          SizedBox(height: 2.h),

          // Header content
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: CustomIconWidget(
                  iconName: widget.measurementType == 0
                      ? 'monitor_weight'
                      : widget.measurementType == 1
                          ? 'height'
                          : 'face',
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                    Text(
                      widget.subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                  iconName: 'close',
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  size: 24,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'When was this measured?',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        GestureDetector(
          onTap: _selectDateTime,
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSurfaceColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                width: 1.5,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'event',
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date & Time',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                      ),
                      Text(
                        '${_selectedDate.month}/${_selectedDate.day}/${_selectedDate.year} at ${_selectedTime.format(context)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildValueInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${_getMeasurementTitle()} Measurement',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Value input
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _valueController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                validator: _validateInput,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                decoration: InputDecoration(
                  labelText: 'Enter value',
                  hintText: 'e.g., 7.5',
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(3.w),
                    child: CustomIconWidget(
                      iconName: widget.measurementType == 0
                          ? 'monitor_weight'
                          : widget.measurementType == 1
                              ? 'height'
                              : 'face',
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 2,
                    ),
                  ),
                ),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
            ),

            SizedBox(width: 3.w),

            // Unit selector
            Expanded(
              child: Container(
                height: 7.h,
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getSurfaceColor(context),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                    width: 1.5,
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedUnit,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedUnit = value;
                        });
                        // Recalculate percentile with new unit
                        if (_valueController.text.isNotEmpty) {
                          final inputValue =
                              double.tryParse(_valueController.text);
                          if (inputValue != null) {
                            _calculatePercentile(inputValue);
                          }
                        }
                      }
                    },
                    items: _getUnitOptions().map((unit) {
                      return DropdownMenuItem<String>(
                        value: unit,
                        child: Center(
                          child: Text(
                            unit,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: ThemeAwareColors.getPrimaryTextColor(context),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                    icon: Padding(
                      padding: EdgeInsets.only(right: 2.w),
                      child: CustomIconWidget(
                        iconName: 'keyboard_arrow_down',
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                    ),
                    dropdownColor: ThemeAwareColors.getCardColor(context),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPercentilePreview() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: _isCalculating
            ? ThemeAwareColors.getSurfaceColor(context)
            : _getPercentileColor(_calculatedPercentile!)
                .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isCalculating
              ? ThemeAwareColors.getDividerColor(context)
              : _getPercentileColor(_calculatedPercentile!)
                  .withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: _isCalculating
          ? Row(
              children: [
                SizedBox(
                  width: 5.w,
                  height: 5.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Text(
                  'Calculating percentile...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getPercentileColor(_calculatedPercentile!)
                            .withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: 'analytics',
                        color: _getPercentileColor(_calculatedPercentile!),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'WHO Percentile',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: _getPercentileColor(_calculatedPercentile!),
                            ),
                          ),
                          Text(
                            '${_calculatedPercentile!.toStringAsFixed(1)}th percentile',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: _getPercentileColor(_calculatedPercentile!),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Text(
                  _percentileCategory,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any notes about this measurement...',
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.all(3.w),
              child: CustomIconWidget(
                iconName: 'note',
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
          ),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        height: 6.h,
        child: ElevatedButton(
          onPressed: _handleSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            elevation: 2,
            shadowColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconWidget(
                iconName: 'save',
                color: Theme.of(context).colorScheme.onPrimary,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Save Measurement',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.w700,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
