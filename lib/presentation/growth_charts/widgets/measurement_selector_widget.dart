import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MeasurementSelectorWidget extends StatelessWidget {
  final int selectedIndex;
  final Function(int) onChanged;

  const MeasurementSelectorWidget({
    super.key,
    required this.selectedIndex,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    final List<Map<String, dynamic>> measurementTypes = [
      {
        "title": "Weight",
        "icon": "monitor_weight",
        "color": colorScheme.primary,
      },
      {
        "title": "Height",
        "icon": "height",
        "color": colorScheme.secondary,
      },
      {
        "title": "Head",
        "icon": "face",
        "color": colorScheme.tertiary,
      },
    ];

    return Container(
      height: 8.h,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: List.generate(measurementTypes.length, (index) {
          final isSelected = selectedIndex == index;
          final measurement = measurementTypes[index];

          return Expanded(
            child: GestureDetector(
              onTap: () => onChanged(index),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                margin: EdgeInsets.all(1.w),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (measurement["color"] as Color).withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(10),
                  border: isSelected
                      ? Border.all(
                          color: measurement["color"] as Color,
                          width: 2,
                        )
                      : null,
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: measurement["icon"] as String,
                      color: isSelected
                          ? measurement["color"] as Color
                          : colorScheme.onSurface.withValues(alpha: 0.6),
                      size: 24,
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      measurement["title"] as String,
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        color: isSelected
                            ? measurement["color"] as Color
                            : colorScheme.onSurface.withValues(alpha: 0.8),
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w400,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
