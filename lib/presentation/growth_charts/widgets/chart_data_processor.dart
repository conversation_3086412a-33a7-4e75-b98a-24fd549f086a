import '../../../models/baby_profile.dart';
import '../../../services/who_percentile_service.dart';

/// Handles data processing and filtering for growth charts
class ChartDataProcessor {
  static List<Map<String, dynamic>> filterDataByDateRange(
    List<Map<String, dynamic>> data,
    String dateRange,
    DateTime birthDate,
  ) {
    if (data.isEmpty) return [];

    final maxAgeInMonths = _getMaxAgeForDateRange(dateRange);
    
    final filteredData = data.where((dataPoint) {
      final ageInMonths = dataPoint["ageInMonths"] != null
          ? dataPoint["ageInMonths"] as double
          : _getAgeInMonths(dataPoint["date"] as DateTime, birthDate);
      return ageInMonths <= maxAgeInMonths;
    }).toList();

    // Sort by age for proper chart rendering
    filteredData.sort((a, b) {
      final ageA = a["ageInMonths"] != null
          ? a["ageInMonths"] as double
          : _getAgeInMonths(a["date"] as DateTime, birthDate);
      final ageB = b["ageInMonths"] != null
          ? b["ageInMonths"] as double
          : _getAgeInMonths(b["date"] as DateTime, birthDate);
      return ageA.compareTo(ageB);
    });

    return filteredData;
  }

  static double _getMaxAgeForDateRange(String dateRange) {
    const ageMap = {
      '6 months': 6.0,
      '1 year': 12.0,
      '2 years': 24.0,
      '3 years': 36.0,
      '4 years': 48.0,
      '5 years': 60.0,
    };
    return ageMap[dateRange] ?? 12.0;
  }

  static double _getAgeInMonths(DateTime date, DateTime birthDate) {
    final difference = date.difference(birthDate);
    return difference.inDays / 30.44; // Average days per month
  }
}