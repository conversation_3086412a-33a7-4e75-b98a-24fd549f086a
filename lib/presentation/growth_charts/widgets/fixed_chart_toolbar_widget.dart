import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/time_range_service.dart';
import '../../../services/responsive_chart_service.dart';
import '../../../services/unit_conversion_service.dart';

class ChartToolbarWidget extends StatelessWidget {
  final String selectedDateRange;
  final bool isMetric;
  final Function(String) onDateRangeChanged;
  final Function(bool) onUnitToggle;
  final String measurementType;
  final double? currentBabyAge;
  final AgeDisplayFormat ageDisplayFormat;
  final Function(AgeDisplayFormat)? onAgeFormatChanged;
  final List<Map<String, dynamic>>? measurements;
  final Function()? onExportChart;
  final bool isOfflineMode;

  const ChartToolbarWidget({
    super.key,
    required this.selectedDateRange,
    required this.isMetric,
    required this.onDateRangeChanged,
    required this.onUnitToggle,
    required this.measurementType,
    this.currentBabyAge,
    this.ageDisplayFormat = AgeDisplayFormat.adaptive,
    this.onAgeFormatChanged,
    this.measurements,
    this.onExportChart,
    this.isOfflineMode = false,
  });

  @override
  Widget build(BuildContext context) {
    // Get responsive layout configuration
    final layoutConfig = ResponsiveChartService.getChartLayout(context);
    
    // Get relevant time ranges based on baby's age
    final availableRanges = currentBabyAge != null 
        ? TimeRangeService.getRelevantTimeRanges(currentBabyAge!)
        : TimeRangeService.availableRanges;
    
    // Get measurement units configuration
    final units = UnitConversionService.getDefaultUnits(measurementType, isMetric);

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 4,
            offset: Offset(0, 1),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Primary Controls Row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Date Range Selector
              Expanded(
                flex: 2,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Time Period',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Container(
                      height: 5.h,
                      decoration: BoxDecoration(
                        color: ThemeAwareColors.getSurfaceColor(context),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).brightness == Brightness.dark
                              ? Theme.of(context).primaryColor.withValues(alpha: 0.8)
                              : Theme.of(context).primaryColor.withValues(alpha: 0.3),
                          width: 1.5,
                        ),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: availableRanges.any((r) => r.id == selectedDateRange) 
                              ? selectedDateRange 
                              : availableRanges.first.id,
                          onChanged: (value) {
                            if (value != null) {
                              onDateRangeChanged(value);
                            }
                          },
                          items: availableRanges.map((range) {
                            return DropdownMenuItem<String>(
                              value: range.id,
                              child: Padding(
                                padding: EdgeInsets.symmetric(horizontal: 3.w),
                                child: ConstrainedBox(
                                  constraints: BoxConstraints(maxWidth: 60.w),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      CustomIconWidget(
                                        iconName: range.icon,
                                        color: Theme.of(context).colorScheme.primary,
                                        size: layoutConfig.iconSizes.small,
                                      ),
                                      SizedBox(width: layoutConfig.spacing.small),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Text(
                                              range.label,
                                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                fontWeight: FontWeight.w500,
                                                color: ThemeAwareColors.getPrimaryTextColor(context),
                                                fontSize: layoutConfig.fontSize.axisLabel,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            if (layoutConfig.showExtendedControls)
                                              Text(
                                                range.description,
                                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                  color: ThemeAwareColors.getSecondaryTextColor(context),
                                                  fontSize: layoutConfig.fontSize.axisLabel * 0.8,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                          icon: Padding(
                            padding: EdgeInsets.only(right: 3.w),
                            child: CustomIconWidget(
                              iconName: 'expand_more',
                              color: Theme.of(context).colorScheme.primary,
                              size: 20,
                            ),
                          ),
                          dropdownColor: ThemeAwareColors.getCardColor(context),
                          elevation: 8,
                          borderRadius: BorderRadius.circular(12),
                          isExpanded: false,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              SizedBox(width: 4.w),

              // Unit Toggle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Units',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Container(
                      height: 5.h,
                      decoration: BoxDecoration(
                        color: ThemeAwareColors.getSurfaceColor(context),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: ThemeAwareColors.getDividerColor(context),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          _buildUnitButton('Metric', isMetric, true, context),
                          _buildUnitButton('Imperial', !isMetric, false, context),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Age Display Format Row (if callback provided)
          if (onAgeFormatChanged != null && layoutConfig.showExtendedControls) ...[
            SizedBox(height: 2.h),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Age Display',
                        style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                      ),
                      SizedBox(height: 1.h),
                      Container(
                        height: 4.h,
                        decoration: BoxDecoration(
                          color: ThemeAwareColors.getSurfaceColor(context),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: ThemeAwareColors.getDividerColor(context),
                            width: 1,
                          ),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          children: AgeDisplayFormat.values.map((format) {
                            final isSelected = format == ageDisplayFormat;
                            return Expanded(
                              child: GestureDetector(
                                onTap: () => onAgeFormatChanged!(format),
                                child: AnimatedContainer(
                                  duration: Duration(milliseconds: 200),
                                  margin: EdgeInsets.all(0.5.w),
                                  decoration: BoxDecoration(
                                    color: isSelected
                                        ? Theme.of(context).primaryColor.withValues(alpha: 0.8)
                                        : Colors.transparent,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Center(
                                    child: Text(
                                      _getAgeFormatLabel(format),
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: isSelected
                                            ? Theme.of(context).colorScheme.onPrimary
                                            : ThemeAwareColors.getSecondaryTextColor(context),
                                        fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                                        fontSize: 10.sp,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],

          SizedBox(height: 2.h),

          // Additional Controls Row
          LayoutBuilder(
            builder: (context, constraints) {
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Chart Information with offline indicator
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                      decoration: BoxDecoration(
                        color: isOfflineMode 
                            ? Colors.orange.withValues(alpha: 0.05)
                            : Theme.of(context).primaryColor.withValues(alpha: 0.05),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isOfflineMode
                              ? Colors.orange.withValues(alpha: 0.3)
                              : Theme.of(context).primaryColor.withValues(alpha: 0.2),
                          width: 1,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CustomIconWidget(
                            iconName: isOfflineMode ? 'cloud_off' : 'info',
                            color: isOfflineMode ? Colors.orange : Theme.of(context).primaryColor,
                            size: 16,
                          ),
                          SizedBox(width: 2.w),
                          Expanded(
                            child: Text(
                              isOfflineMode 
                                  ? 'Offline Mode - ${_getPercentileDescription()}'
                                  : 'WHO/CDC Growth Standards - ${_getPercentileDescription()}',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: isOfflineMode ? Colors.orange : Theme.of(context).primaryColor,
                                fontWeight: FontWeight.w500,
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  SizedBox(width: 3.w),

                  // Smart Range Recommendation Button (if measurements available)
                  if (measurements != null && measurements!.isNotEmpty && currentBabyAge != null) ...[
                    Container(
                      height: 4.h,
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: _recommendOptimalRange,
                          borderRadius: BorderRadius.circular(8),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 2.w),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CustomIconWidget(
                                  iconName: 'auto_awesome',
                                  color: Theme.of(context).colorScheme.secondary,
                                  size: 14,
                                ),
                                SizedBox(width: 1.w),
                                Text(
                                  'Auto',
                                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                    color: Theme.of(context).colorScheme.secondary,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 2.w),
                  ],

                  // Export/Share Button
                  Container(
                    height: 4.h,
                    decoration: BoxDecoration(
                      color: ThemeAwareColors.getAccentColor(context).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: ThemeAwareColors.getAccentColor(context).withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: onExportChart ?? () {
                          // Default export action - could show a dialog
                          _showExportOptions(context);
                        },
                        borderRadius: BorderRadius.circular(8),
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 3.w),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CustomIconWidget(
                                iconName: 'share',
                                color: ThemeAwareColors.getAccentColor(context),
                                size: 16,
                              ),
                              SizedBox(width: 1.w),
                              Text(
                                'Export',
                                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                  color: ThemeAwareColors.getAccentColor(context),
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUnitButton(String label, bool isSelected, bool isMetricOption, BuildContext context) {
    return Expanded(
      child: GestureDetector(
        onTap: () => onUnitToggle(isMetricOption),
        child: AnimatedContainer(
          duration: Duration(milliseconds: 200),
          margin: EdgeInsets.all(1.w),
          decoration: BoxDecoration(
            color: isSelected
                ? Theme.of(context).primaryColor
                : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                      blurRadius: 4,
                      offset: Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: SizedBox(
            height: double.infinity,
            child: Center(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: isSelected
                      ? Theme.of(context).colorScheme.onPrimary
                      : ThemeAwareColors.getSecondaryTextColor(context),
                  fontWeight: isSelected ? FontWeight.w700 : FontWeight.w500,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _getPercentileDescription() {
    final timeRange = TimeRangeService.getTimeRangeById(selectedDateRange);
    if (timeRange != null) {
      return timeRange.description;
    }
    
    // Fallback for legacy range IDs
    switch (selectedDateRange) {
      case '6 months':
        return '6-month growth percentiles';
      case '1 year':
        return 'First year growth percentiles';
      case '2 years':
        return 'Early childhood percentiles';
      case '3 years':
        return 'Early childhood percentiles';
      case '4 years':
        return 'Preschool growth percentiles';
      case '5 years':
        return 'Complete WHO percentiles (0-5 years)';
      default:
        return 'Growth percentiles';
    }
  }
  
  String _getAgeFormatLabel(AgeDisplayFormat format) {
    switch (format) {
      case AgeDisplayFormat.monthsOnly:
        return 'Months';
      case AgeDisplayFormat.yearsAndMonths:
        return 'Y+M';
      case AgeDisplayFormat.decimalYears:
        return 'Decimal';
      case AgeDisplayFormat.adaptive:
        return 'Auto';
    }
  }
  
  void _recommendOptimalRange() {
    if (measurements == null || measurements!.isEmpty || currentBabyAge == null) {
      return;
    }
    
    // Extract measurement ages
    final measurementAges = measurements!
        .map((m) => (m['ageInMonths'] as num?)?.toDouble() ?? 0.0)
        .toList();
    
    // Get recommended time range
    final recommendedRange = TimeRangeService.getRecommendedTimeRange(
      currentAgeMonths: currentBabyAge!,
      measurementCount: measurements!.length,
      measurementAges: measurementAges,
    );
    
    // Apply the recommendation
    onDateRangeChanged(recommendedRange.id);
  }
  
  void _showExportOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: ThemeAwareColors.getCardColor(context),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Export Growth Chart',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            SizedBox(height: 3.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'picture_as_pdf',
                color: Colors.red,
                size: 24,
              ),
              title: Text(
                'Export as PDF',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              subtitle: Text(
                'Professional report with charts and analysis',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                if (onExportChart != null) {
                  onExportChart!();
                }
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'image',
                color: Colors.blue,
                size: 24,
              ),
              title: Text(
                'Save as Image',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              subtitle: Text(
                'High-quality chart image',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement image export
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'share',
                color: Colors.green,
                size: 24,
              ),
              title: Text(
                'Share Data',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              subtitle: Text(
                'Share with healthcare provider',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement data sharing
              },
            ),
          ],
        ),
      ),
    );
  }
}