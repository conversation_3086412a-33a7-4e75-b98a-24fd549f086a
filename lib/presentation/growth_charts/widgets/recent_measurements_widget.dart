import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class RecentMeasurementsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> measurements;
  final int measurementType;
  final bool isMetric;
  final Function(int) onEdit;
  final Function(int) onDelete;

  const RecentMeasurementsWidget({
    super.key,
    required this.measurements,
    required this.measurementType,
    required this.isMetric,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: isDarkMode 
              ? Colors.black.withValues(alpha: 0.3)
              : Colors.grey.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Compact Header
          Padding(
            padding: EdgeInsets.fromLTRB(4.w, 3.h, 4.w, 2.h),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Measurements',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.onSurface,
                  ),
                ),
                if (measurements.isNotEmpty)
                  _buildCompactTrendIndicator(context),
              ],
            ),
          ),
          
          // Content Area - Flexible
          measurements.isEmpty
              ? _buildCompactEmptyState(context)
              : _buildMeasurementsList(context),
        ],
      ),
    );
  }

  Widget _buildCompactEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Padding(
      padding: EdgeInsets.fromLTRB(4.w, 0, 4.w, 3.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              shape: BoxShape.circle,
            ),
            child: CustomIconWidget(
              iconName: 'history',
              color: colorScheme.onSurface.withValues(alpha: 0.3),
              size: 28,
            ),
          ),
          SizedBox(height: 1.5.h),
          Text(
            'No measurements yet',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            'Start tracking your baby\'s growth',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsList(BuildContext context) {
    return Padding(
      padding: EdgeInsets.fromLTRB(4.w, 0, 4.w, 2.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(
          measurements.length,
          (index) => Padding(
            padding: EdgeInsets.only(bottom: index < measurements.length - 1 ? 1.h : 0),
            child: _buildCompactMeasurementCard(context, measurements[index], index),
          ),
        ),
      ),
    );
  }

  Widget _buildCompactMeasurementCard(
      BuildContext context, Map<String, dynamic> measurement, int index) {
    final colorScheme = Theme.of(context).colorScheme;
    final date = measurement["date"] as DateTime;
    final value = measurement["value"] as num;
    final unit = measurement["unit"] as String;
    final percentile = measurement["percentile"] as double?;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.5.h),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  '$value $unit',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: colorScheme.primary,
                  ),
                ),
                Text(
                  _formatDate(date),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                if (percentile != null)
                  Container(
                    margin: EdgeInsets.only(top: 0.5.h),
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.3.h),
                    decoration: BoxDecoration(
                      color: _getPercentileColor(percentile).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          '${percentile.toStringAsFixed(1)}th percentile',
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: _getPercentileColor(percentile),
                            fontWeight: FontWeight.w600,
                            fontSize: 10,
                          ),
                        ),
                        Text(
                          _getPercentileCategory(percentile),
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: _getPercentileColor(percentile).withValues(alpha: 0.8),
                            fontWeight: FontWeight.w500,
                            fontSize: 9,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          Builder(
            builder: (context) => GestureDetector(
              onTap: () => _showMeasurementOptions(context, index),
              child: Container(
                padding: EdgeInsets.all(1.w),
                child: CustomIconWidget(
                  iconName: 'more_vert',
                  size: 16,
                  color: colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCompactTrendIndicator(BuildContext context) {
    if (measurements.length < 2) return SizedBox.shrink();

    final latest = measurements[0]["value"] as num;
    final previous = measurements[1]["value"] as num;
    final isIncreasing = latest > previous;

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: (isIncreasing ? Colors.green : Colors.orange).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isIncreasing ? Icons.trending_up : Icons.trending_flat,
            color: isIncreasing ? Colors.green : Colors.orange,
            size: 14,
          ),
          SizedBox(width: 1.w),
          Text(
            isIncreasing ? 'Growing' : 'Stable',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: isIncreasing ? Colors.green : Colors.orange,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _getMeasurementIcon() {
    switch (measurementType) {
      case 0:
        return 'monitor_weight';
      case 1:
        return 'height';
      case 2:
        return 'face';
      default:
        return 'monitor_weight';
    }
  }

  String _getMeasurementTypeString() {
    switch (measurementType) {
      case 0:
        return 'weight';
      case 1:
        return 'height';
      case 2:
        return 'head_circumference';
      default:
        return 'weight';
    }
  }

  double _getAgeInMonths(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    return difference.inDays / 30.44;
  }

  Color _getPercentileColor(double percentile) {
    if (percentile < 3 || percentile > 97) {
      return Colors.red;
    } else if (percentile < 10 || percentile > 90) {
      return Colors.orange;
    } else if (percentile < 25 || percentile > 75) {
      return Colors.amber;
    } else {
      return Colors.green;
    }
  }

  String _getPercentileCategory(double percentile) {
    if (percentile < 3) return 'Below 3rd percentile';
    if (percentile < 10) return 'Below 10th percentile';
    if (percentile < 25) return 'Below 25th percentile';
    if (percentile < 75) return 'Normal range';
    if (percentile < 90) return 'Above 75th percentile';
    if (percentile < 97) return 'Above 90th percentile';
    return 'Above 97th percentile';
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  void _showMeasurementOptions(BuildContext context, int index) {
    final colorScheme = Theme.of(context).colorScheme;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 10.w,
              height: 0.5.h,
              decoration: BoxDecoration(
                color: colorScheme.outline.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(10),
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: Icon(
                Icons.edit,
                color: colorScheme.primary,
              ),
              title: Text(
                'Edit Measurement',
                style: TextStyle(color: colorScheme.onSurface),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement edit functionality
              },
            ),
            ListTile(
              leading: Icon(
                Icons.delete,
                color: Colors.red,
              ),
              title: Text(
                'Delete Measurement',
                style: TextStyle(color: colorScheme.onSurface),
              ),
              onTap: () {
                Navigator.pop(context);
                onDelete(index);
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }
}
