import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/who_percentile_service.dart';

class GrowthChartWidget extends StatefulWidget {
  final List<Map<String, dynamic>> data;
  final int measurementType;
  final String dateRange;
  final bool isMetric;
  final String title;
  final DateTime birthDate;
  final BabyProfile babyProfile;

  const GrowthChartWidget({
    super.key,
    required this.data,
    required this.measurementType,
    required this.dateRange,
    required this.isMetric,
    required this.title,
    required this.birthDate,
    required this.babyProfile,
  });

  @override
  State<GrowthChartWidget> createState() => _GrowthChartWidgetState();
}

class _GrowthChartWidgetState extends State<GrowthChartWidget>
    with SingleTickerProviderStateMixin {
  bool _showAllPercentiles = true;
  bool _isChartReady = false;
  late AnimationController _chartAnimationController;
  late Animation<double> _chartAnimation;
  String _currentDateRange = '';

  // Filtered data based on date range
  List<Map<String, dynamic>> _filteredData = [];

  @override
  void initState() {
    super.initState();
    _currentDateRange = widget.dateRange;
    _chartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _chartAnimation = CurvedAnimation(
      parent: _chartAnimationController,
      curve: Curves.easeInOutCubic,
    );

    _initializeChart();
  }

  @override
  void didUpdateWidget(GrowthChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if date range or measurement type changed
    if (oldWidget.dateRange != widget.dateRange ||
        oldWidget.measurementType != widget.measurementType ||
        oldWidget.data.length != widget.data.length) {
      _handleChartUpdate();
    }
  }

  @override
  void dispose() {
    _chartAnimationController.dispose();
    super.dispose();
  }

  void _initializeChart() {
    _filterDataByDateRange();
    // Delay chart rendering to improve performance
    Future.delayed(Duration(milliseconds: 100), () {
      if (mounted) {
        setState(() {
          _isChartReady = true;
        });
        _chartAnimationController.forward();
      }
    });
  }

  void _handleChartUpdate() {
    setState(() {
      _isChartReady = false;
    });

    // Reset animation
    _chartAnimationController.reset();

    // Update current date range
    _currentDateRange = widget.dateRange;

    // Filter data and re-render chart
    _filterDataByDateRange();

    Future.delayed(Duration(milliseconds: 200), () {
      if (mounted) {
        setState(() {
          _isChartReady = true;
        });
        _chartAnimationController.forward();
      }
    });
  }

  void _filterDataByDateRange() {
    if (widget.data.isEmpty) {
      _filteredData = [];
      debugPrint('No data for chart.');
      return;
    }

    final maxAgeInMonths = _getMaxAgeForDateRange(widget.dateRange);

    _filteredData = widget.data.where((dataPoint) {
      final ageInMonths = dataPoint["ageInMonths"] != null
          ? dataPoint["ageInMonths"] as double
          : _getAgeInMonths(dataPoint["date"] as DateTime);
      return ageInMonths <= maxAgeInMonths;
    }).toList();

    // Sort by age for proper chart rendering
    _filteredData.sort((a, b) {
      final ageA = a["ageInMonths"] != null
          ? a["ageInMonths"] as double
          : _getAgeInMonths(a["date"] as DateTime);
      final ageB = b["ageInMonths"] != null
          ? b["ageInMonths"] as double
          : _getAgeInMonths(b["date"] as DateTime);
      return ageA.compareTo(ageB);
    });

    debugPrint('Filtered chart data:');
    for (final d in _filteredData) {
      debugPrint('  value: [32m${d["value"]}[0m, date: ${d["date"]}, ageInMonths: ${d["ageInMonths"] ?? _getAgeInMonths(d["date"])}');
    }
  }

  double _getMaxAgeForDateRange(String dateRange) {
    switch (dateRange) {
      case '6 months':
        return 6.0;
      case '1 year':
        return 12.0;
      case '2 years':
        return 24.0;
      case '3 years':
        return 36.0;
      case '4 years':
        return 48.0;
      case '5 years':
        return 60.0;
      default:
        return 12.0;
    }
  }

  double _getMaxXForChart() {
    switch (widget.dateRange) {
      case '6 months':
        return 6.0;
      case '1 year':
        return 12.0;
      case '2 years':
        return 24.0;
      case '3 years':
        return 36.0;
      case '4 years':
        return 48.0;
      case '5 years':
        return 60.0;
      default:
        return 12.0;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.data.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Chart Header with Controls
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${widget.title} Growth Chart',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      'WHO/CDC Growth Standards - ${widget.dateRange}',
                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
              ),
              // Chart Controls
              _buildChartControls(),
            ],
          ),

          SizedBox(height: 2.h),

          // Chart Container with Professional Styling (no Expanded)
          SizedBox(
            height: 260,
            child: Container(
              decoration: BoxDecoration(
                color: ThemeAwareColors.getCardColor(context),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ThemeAwareColors.getDividerColor(context),
                  width: 1,
                ),
                boxShadow: [
                  BoxShadow(
                    color: ThemeAwareColors.getShadowColor(context),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              padding: EdgeInsets.all(3.w),
              child: _isChartReady
                  ? FadeTransition(
                      opacity: _chartAnimation,
                      child: _buildChart(),
                    )
                  : _buildChartLoader(),
            ),
          ),

          SizedBox(height: 1.h),

          // Enhanced Legend
          _buildEnhancedLegend(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
        ),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'insert_chart',
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3),
              size: 48,
            ),
            SizedBox(height: 2.h),
            Text(
              'No measurements yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Add your first ${widget.title.toLowerCase()} measurement\nto see growth trends and percentiles',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartLoader() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation(
              Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            'Loading chart for ${widget.dateRange}...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChart() {
    try {
      final maxX = _getMaxXForChart();

      return LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            horizontalInterval: _getHorizontalInterval(),
            verticalInterval: _getVerticalInterval(),
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
                strokeWidth: 0.8,
                dashArray: [3, 3],
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
                strokeWidth: 0.8,
                dashArray: [3, 3],
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 35,
                interval: _getBottomInterval(),
                getTitlesWidget: (value, meta) {
                  return _buildBottomTitle(value);
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 45,
                interval: _getLeftInterval(),
                getTitlesWidget: (value, meta) {
                  return _buildLeftTitle(value);
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(
              color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.5),
              width: 1.5,
            ),
          ),
          minX: 0,
          maxX: maxX,
          minY: _getMinY(),
          maxY: _getMaxY(),
          lineBarsData: [
            // WHO/CDC Percentile lines
            if (_showAllPercentiles) ..._buildWHOPercentileLines(),
            // Baby's actual data (filtered)
            _buildBabyDataLine(),
          ],
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              tooltipBorder: BorderSide(
                color: ThemeAwareColors.getDividerColor(context),
                width: 1,
              ),
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  if (spot.barIndex == (_showAllPercentiles ? 7 : 0)) {
                    // Baby's data line
                    if (spot.spotIndex < _filteredData.length) {
                      final dataPoint = _filteredData[spot.spotIndex];
                      final percentile =
                          WHOPercentileService.calculatePercentile(
                        dataPoint["value"].toDouble(),
                        _getAgeInMonths(dataPoint["date"]),
                        _getMeasurementTypeString(),
                        _normalizeGender(widget.babyProfile.gender),
                      );
                      return LineTooltipItem(
                        '${dataPoint["value"]} ${dataPoint["unit"]}\n${_formatDate(dataPoint["date"])}\n${percentile.toStringAsFixed(1)}th percentile',
                        Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                          fontWeight: FontWeight.w500,
                        ),
                      );
                    }
                  }
                  return null;
                }).toList();
              },
            ),
          ),
          clipData: FlClipData.all(),
        ),
      );
    } catch (e) {
      // Fallback error state
      return _buildErrorState();
    }
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'error',
            color: ThemeAwareColors.getErrorColor(context),
            size: 48,
          ),
          SizedBox(height: 2.h),
          Text(
            'Chart Error',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: ThemeAwareColors.getErrorColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Unable to load chart data for ${widget.dateRange}',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChartControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Percentiles toggle
        Container(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
          decoration: BoxDecoration(
            color: _showAllPercentiles
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : ThemeAwareColors.getSurfaceColor(context),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: _showAllPercentiles
                  ? Theme.of(context).colorScheme.primary
                  : ThemeAwareColors.getDividerColor(context),
              width: 1,
            ),
          ),
          child: GestureDetector(
            onTap: () {
              setState(() {
                _showAllPercentiles = !_showAllPercentiles;
              });
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                CustomIconWidget(
                  iconName:
                      _showAllPercentiles ? 'visibility' : 'visibility_off',
                  color: _showAllPercentiles
                      ? Theme.of(context).colorScheme.primary
                      : ThemeAwareColors.getSecondaryTextColor(context),
                  size: 16,
                ),
                SizedBox(width: 1.w),
                Text(
                  'Percentiles',
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: _showAllPercentiles
                        ? Theme.of(context).colorScheme.primary
                        : ThemeAwareColors.getSecondaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
        SizedBox(width: 2.w),
        // Data points indicator
        Container(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomIconWidget(
                iconName: 'scatter_plot',
                color: Theme.of(context).colorScheme.secondary,
                size: 14,
              ),
              SizedBox(width: 1.w),
              Text(
                '${_filteredData.length} points',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<LineChartBarData> _buildWHOPercentileLines() {
    final percentiles = ['3rd', '15th', '50th', '85th', '97th'];
    final percentileColors = [
      Color(0xFFE53E3E), // 3rd - Bright Red
      Color(0xFFFF8C00), // 15th - Bright Orange  
      Color(0xFF2563EB), // 50th - Bright Blue
      Color(0xFF059669), // 85th - Bright Green
      Color(0xFF7C3AED), // 97th - Bright Purple
    ];

    return percentiles.asMap().entries.map((entry) {
      final index = entry.key;
      final percentile = entry.value;
      final isMainPercentile = percentile == '50th';

      return LineChartBarData(
        spots: _generateWHOPercentileSpots(percentile),
        isCurved: true,
        color: percentileColors[index]
            .withValues(alpha: isMainPercentile ? 0.9 : 0.6),
        barWidth: isMainPercentile ? 2.5 : 1.5,
        isStrokeCapRound: true,
        dotData: FlDotData(show: false),
        belowBarData: BarAreaData(show: false),
        dashArray: isMainPercentile ? null : [4, 4],
      );
    }).toList();
  }

  LineChartBarData _buildBabyDataLine() {
    debugPrint('Building baby data line with ${_filteredData.length} data points.');
    final spots = _filteredData.map((point) {
      final ageInMonths = point["ageInMonths"] != null
          ? point["ageInMonths"] as double
          : _getAgeInMonths(point["date"] as DateTime);
      debugPrint('Chart spot: ageInMonths=$ageInMonths, value=${point["value"]}');
      return FlSpot(
        ageInMonths,
        (point["value"] as num).toDouble(),
      );
    }).toList();
    if (spots.isEmpty) {
      debugPrint('No spots for baby data line.');
    }
    return LineChartBarData(
      spots: spots,
      isCurved: true,
      gradient: LinearGradient(
        colors: [
          Colors.red,
          Colors.red,
        ],
      ),
      barWidth: 4,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          return FlDotCirclePainter(
            radius: 6,
            color: Colors.red,
            strokeWidth: 3,
            strokeColor: ThemeAwareColors.getCardColor(context),
          );
        },
      ),
      belowBarData: BarAreaData(
        show: true,
        gradient: LinearGradient(
          colors: [
            Colors.red.withValues(alpha: 0.15),
            Colors.red.withValues(alpha: 0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }

  List<FlSpot> _generateWHOPercentileSpots(String percentile) {
    try {
      final ageRange = WHOPercentileService.getAgeRange(widget.dateRange);
      final normalizedGender = _normalizeGender(widget.babyProfile.gender);
      final measurementType = _getMeasurementTypeString();
      
      debugPrint('WHO Percentile Debug: percentile=$percentile, originalGender=${widget.babyProfile.gender}, normalizedGender=$normalizedGender, measurementType=$measurementType');

      return ageRange.map((age) {
        final value = WHOPercentileService.getPercentileValue(
          percentile,
          age,
          measurementType,
          normalizedGender,
        );
        debugPrint('Percentile spot for $percentile at age $age: $value');
        return FlSpot(age, value);
      }).toList();
    } catch (e) {
      debugPrint('Error generating WHO percentile spots: $e');
      return [];
    }
  }

  String _normalizeGender(String gender) {
    final lowercaseGender = gender.toLowerCase();
    if (lowercaseGender == 'male' || lowercaseGender == 'boy' || lowercaseGender == 'boys') {
      return 'boys';
    } else if (lowercaseGender == 'female' || lowercaseGender == 'girl' || lowercaseGender == 'girls') {
      return 'girls';
    }
    // Default to boys if gender is unclear
    return 'boys';
  }

  String _getMeasurementTypeString() {
    switch (widget.measurementType) {
      case 0:
        return 'weight';
      case 1:
        return 'height';
      case 2:
        return 'head_circumference';
      default:
        return 'weight';
    }
  }

  double _getAgeInMonths(DateTime date) {
    final birthDate = widget.birthDate;
    final difference = date.difference(birthDate);
    return difference.inDays / 30.44; // Average days per month
  }

  Widget _buildEnhancedLegend() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context).withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Legend - ${widget.dateRange}',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Wrap(
            spacing: 4.w,
            runSpacing: 1.h,
            children: [
              _buildLegendItem(
                'Your Baby\'s Growth',
                Colors.red,
                isMainLine: true,
              ),
              if (_showAllPercentiles) ...[
                _buildLegendItem('97th Percentile', Color(0xFF7C3AED)),
                _buildLegendItem('85th Percentile', Color(0xFF059669)),
                _buildLegendItem('50th Percentile (Median)', Color(0xFF2563EB)),
                _buildLegendItem('15th Percentile', Color(0xFFFF8C00)),
                _buildLegendItem('3rd Percentile', Color(0xFFE53E3E)),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(String label, Color color,
      {bool isMainLine = false}) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 3,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.8),
            borderRadius: BorderRadius.circular(1.5),
          ),
        ),
        if (isMainLine)
          Container(
            margin: EdgeInsets.only(left: 2),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(color: ThemeAwareColors.getCardColor(context), width: 1),
            ),
          ),
        SizedBox(width: 2.w),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            fontWeight: FontWeight.w500,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
      ],
    );
  }

  Widget _buildBottomTitle(double value) {
    return Padding(
      padding: EdgeInsets.only(top: 1.h),
      child: Text(
        '${value.toInt()}m',
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w500,
          color: ThemeAwareColors.getSecondaryTextColor(context),
        ),
      ),
    );
  }

  Widget _buildLeftTitle(double value) {
    final unit = _getUnit();
    return Padding(
      padding: EdgeInsets.only(right: 1.w),
      child: Text(
        '${value.toStringAsFixed(value % 1 == 0 ? 0 : 1)}$unit',
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w500,
          color: ThemeAwareColors.getSecondaryTextColor(context),
        ),
      ),
    );
  }

  String _getUnit() {
    switch (widget.measurementType) {
      case 0: // Weight
        return widget.isMetric ? 'kg' : 'lbs';
      case 1: // Height
        return widget.isMetric ? 'cm' : 'in';
      case 2: // Head Circumference
        return widget.isMetric ? 'cm' : 'in';
      default:
        return 'kg';
    }
  }

  // Improved chart ranges for realistic baby growth based on date range
  double _getMinY() {
    switch (widget.measurementType) {
      case 0: // Weight
        return widget.isMetric ? 2.0 : 4.4; // Start from realistic birth weights
      case 1: // Height
        return widget.isMetric ? 45 : 18;
      case 2: // Head Circumference
        return widget.isMetric ? 32 : 12.5;
      default:
        return 2.0;
    }
  }

  double _getMaxY() {
    final maxX = _getMaxXForChart();
    switch (widget.measurementType) {
      case 0: // Weight - Optimized ranges for better chart utilization
        if (maxX <= 6) return widget.isMetric ? 11 : 24;   // 6 months: 11kg max
        if (maxX <= 12) return widget.isMetric ? 14 : 31;  // 1 year: 14kg max
        if (maxX <= 24) return widget.isMetric ? 18 : 40;  // 2 years: 18kg max
        if (maxX <= 36) return widget.isMetric ? 22 : 48;  // 3 years: 22kg max
        if (maxX <= 48) return widget.isMetric ? 24 : 53;  // 4 years: 24kg max
        return widget.isMetric ? 26 : 57; // 5 years: 26kg max
      case 1: // Height - Optimized ranges
        if (maxX <= 6) return widget.isMetric ? 78 : 31;
        if (maxX <= 12) return widget.isMetric ? 85 : 33;
        if (maxX <= 24) return widget.isMetric ? 95 : 37;
        if (maxX <= 36) return widget.isMetric ? 105 : 41; // 3 years
        if (maxX <= 48) return widget.isMetric ? 115 : 45; // 4 years
        return widget.isMetric ? 125 : 49; // 5 years
      case 2: // Head Circumference - Optimized ranges
        if (maxX <= 6) return widget.isMetric ? 51 : 20.1;
        if (maxX <= 12) return widget.isMetric ? 53 : 20.9;
        if (maxX <= 24) return widget.isMetric ? 56 : 22.0;
        if (maxX <= 36) return widget.isMetric ? 58 : 22.8; // 3 years
        if (maxX <= 48) return widget.isMetric ? 60 : 23.6; // 4 years
        return widget.isMetric ? 62 : 24.4; // 5 years
      default:
        return 16;
    }
  }

  double _getHorizontalInterval() {
    final range = _getMaxY() - _getMinY();
    switch (widget.measurementType) {
      case 0: // Weight
        if (widget.isMetric) {
          if (range <= 10) return 2.0;
          if (range <= 20) return 3.0;
          return 4.0;
        } else {
          if (range <= 20) return 4.0;
          if (range <= 40) return 6.0;
          return 8.0;
        }
      case 1: // Height
        if (widget.isMetric) {
          if (range <= 30) return 5.0;
          if (range <= 60) return 10.0;
          return 15.0;
        } else {
          if (range <= 15) return 2.0;
          if (range <= 25) return 4.0;
          return 5.0;
        }
      case 2: // Head Circumference
        if (widget.isMetric) {
          if (range <= 20) return 3.0;
          if (range <= 30) return 4.0;
          return 5.0;
        } else {
          if (range <= 10) return 1.0;
          if (range <= 15) return 2.0;
          return 2.5;
        }
      default:
        return range / 6;
    }
  }

  double _getVerticalInterval() {
    final maxX = _getMaxXForChart();
    if (maxX <= 6) return 1;
    if (maxX <= 12) return 2;
    if (maxX <= 24) return 3;
    if (maxX <= 36) return 6; // 3 years
    if (maxX <= 48) return 6; // 4 years
    return 12; // 5 years
  }

  double _getBottomInterval() => _getVerticalInterval();
  double _getLeftInterval() => _getHorizontalInterval();

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }
}
