import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/who_data_service.dart';

/// Interactive tooltip for displaying measurement details and percentile information
class MeasurementTooltip extends StatefulWidget {
  final Map<String, dynamic>? measurement;
  final String? percentileCurve;
  final double? ageAtCursor;
  final Offset position;
  final String measurementType;
  final String gender;
  final DateTime birthDate;
  final VoidCallback? onClose;
  final Function(Map<String, dynamic>)? onEdit;
  final Function(Map<String, dynamic>)? onDelete;

  const MeasurementTooltip({
    super.key,
    this.measurement,
    this.percentileCurve,
    this.ageAtCursor,
    required this.position,
    required this.measurementType,
    required this.gender,
    required this.birthDate,
    this.onClose,
    this.onEdit,
    this.onDelete,
  });

  @override
  State<MeasurementTooltip> createState() => _MeasurementTooltipState();
}

class _MeasurementTooltipState extends State<MeasurementTooltip>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));
    
    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: _calculateLeft(),
      top: _calculateTop(),
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: _buildTooltipContent(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTooltipContent() {
    if (widget.measurement != null) {
      return _buildMeasurementTooltip();
    } else if (widget.percentileCurve != null) {
      return _buildPercentileTooltip();
    } else {
      return const SizedBox.shrink();
    }
  }

  Widget _buildMeasurementTooltip() {
    final measurement = widget.measurement!;
    final value = (measurement['value'] as num).toDouble();
    final unit = measurement['unit'] as String;
    final date = measurement['date'] as DateTime;
    final notes = measurement['notes'] as String? ?? '';
    
    // Calculate percentile and analysis
    final ageInMonths = _calculateAgeInMonths(date);
    final percentileResult = WHODataService.analyzePercentile(
      value,
      ageInMonths,
      widget.measurementType,
      widget.gender,
    );

    return Container(
      constraints: BoxConstraints(maxWidth: 70.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with close button
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    'Measurement Details',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: ThemeAwareColors.getPrimaryTextColor(context),
                    ),
                  ),
                ),
                if (widget.onClose != null)
                  GestureDetector(
                    onTap: widget.onClose,
                    child: Icon(
                      Icons.close,
                      size: 18,
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                    ),
                  ),
              ],
            ),
          ),
          
          // Content
          Padding(
            padding: EdgeInsets.all(3.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Value and unit
                Row(
                  children: [
                    Text(
                      '$value $unit',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    const Spacer(),
                    _buildPercentileChip(percentileResult),
                  ],
                ),
                
                SizedBox(height: 1.h),
                
                // Date and age
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 14,
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                    ),
                    SizedBox(width: 1.w),
                    Text(
                      _formatDate(date),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                    const Spacer(),
                    Text(
                      '${ageInMonths.toStringAsFixed(1)} months old',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontStyle: FontStyle.italic,
                      ),
                    ),
                  ],
                ),
                
                SizedBox(height: 1.h),
                
                // Percentile analysis
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: _getPercentileColor(percentileResult).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: _getPercentileColor(percentileResult).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            _getPercentileIcon(percentileResult),
                            size: 16,
                            color: _getPercentileColor(percentileResult),
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            percentileResult.category,
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: _getPercentileColor(percentileResult),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        percentileResult.interpretation,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Notes if available
                if (notes.isNotEmpty) ...[
                  SizedBox(height: 1.h),
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: ThemeAwareColors.getSurfaceColor(context).withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.note,
                          size: 14,
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                        SizedBox(width: 1.w),
                        Expanded(
                          child: Text(
                            notes,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: ThemeAwareColors.getPrimaryTextColor(context),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                // Action buttons
                if (widget.onEdit != null || widget.onDelete != null) ...[
                  SizedBox(height: 2.h),
                  Row(
                    children: [
                      if (widget.onEdit != null)
                        Expanded(
                          child: _buildActionButton(
                            icon: Icons.edit,
                            label: 'Edit',
                            onTap: () => widget.onEdit!(measurement),
                            isPrimary: true,
                          ),
                        ),
                      if (widget.onEdit != null && widget.onDelete != null)
                        SizedBox(width: 2.w),
                      if (widget.onDelete != null)
                        Expanded(
                          child: _buildActionButton(
                            icon: Icons.delete,
                            label: 'Delete',
                            onTap: () => widget.onDelete!(measurement),
                            isPrimary: false,
                            isDestructive: true,
                          ),
                        ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPercentileTooltip() {
    final percentile = widget.percentileCurve!;
    final ageInMonths = widget.ageAtCursor ?? 0.0;
    
    // Get percentile value at cursor position
    final percentileValue = double.tryParse(percentile.replaceAll(RegExp(r'[^\d.]'), '')) ?? 50.0;
    final value = WHODataService.getPercentileValue(
      percentileValue,
      ageInMonths,
      widget.measurementType,
      widget.gender,
    );

    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '$percentile Percentile',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            'At ${ageInMonths.toStringAsFixed(1)} months',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            '${value.toStringAsFixed(1)} ${_getUnit()}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            _getPercentileDescription(percentileValue),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPercentileChip(PercentileResult result) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: _getPercentileColor(result).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPercentileColor(result).withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        '${result.percentile.toStringAsFixed(1)}${_getPercentileSuffix(result.percentile.toInt())}',
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: _getPercentileColor(result),
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    required bool isPrimary,
    bool isDestructive = false,
  }) {
    final color = isDestructive
        ? Theme.of(context).colorScheme.error
        : isPrimary
            ? Theme.of(context).colorScheme.primary
            : ThemeAwareColors.getSecondaryTextColor(context);

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 1.h),
        decoration: BoxDecoration(
          color: isPrimary
              ? color.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 16, color: color),
            SizedBox(width: 1.w),
            Text(
              label,
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods
  double _calculateLeft() {
    final screenWidth = MediaQuery.of(context).size.width;
    final tooltipWidth = 70.w;
    
    // Try to center tooltip on cursor
    double left = widget.position.dx - (tooltipWidth / 2);
    
    // Ensure tooltip stays within screen bounds
    if (left < 4.w) {
      left = 4.w;
    } else if (left + tooltipWidth > screenWidth - 4.w) {
      left = screenWidth - tooltipWidth - 4.w;
    }
    
    return left;
  }

  double _calculateTop() {
    final screenHeight = MediaQuery.of(context).size.height;
    const tooltipHeight = 200.0; // Approximate height
    
    // Try to position tooltip above cursor
    double top = widget.position.dy - tooltipHeight - 2.h;
    
    // If not enough space above, position below
    if (top < 10.h) {
      top = widget.position.dy + 2.h;
    }
    
    // Ensure tooltip stays within screen bounds
    if (top + tooltipHeight > screenHeight - 10.h) {
      top = screenHeight - tooltipHeight - 10.h;
    }
    
    return top;
  }

  double _calculateAgeInMonths(DateTime date) {
    final difference = date.difference(widget.birthDate);
    return difference.inDays / 30.44;
  }

  String _formatDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year}';
  }

  String _getUnit() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return 'kg';
      case 'height':
      case 'length':
        return 'cm';
      case 'head_circumference':
      case 'head circumference':
        return 'cm';
      default:
        return 'kg';
    }
  }

  Color _getPercentileColor(PercentileResult result) {
    if (result.requiresAttention) {
      return Theme.of(context).colorScheme.error;
    } else if (result.percentile < 25 || result.percentile > 75) {
      return Colors.orange;
    } else {
      return Theme.of(context).colorScheme.primary;
    }
  }

  IconData _getPercentileIcon(PercentileResult result) {
    if (result.requiresAttention) {
      return Icons.warning;
    } else if (result.percentile < 25) {
      return Icons.trending_down;
    } else if (result.percentile > 75) {
      return Icons.trending_up;
    } else {
      return Icons.check_circle;
    }
  }

  String _getPercentileDescription(double percentile) {
    if (percentile <= 3) {
      return 'Below normal range - consider medical consultation';
    } else if (percentile <= 10) {
      return 'Low normal - monitor closely';
    } else if (percentile <= 25) {
      return 'Below average but within normal range';
    } else if (percentile <= 75) {
      return 'Average range';
    } else if (percentile <= 90) {
      return 'Above average but within normal range';
    } else if (percentile <= 97) {
      return 'High normal - monitor closely';
    } else {
      return 'Above normal range - consider medical consultation';
    }
  }

  String _getPercentileSuffix(int percentile) {
    if (percentile >= 11 && percentile <= 13) return 'th';
    switch (percentile % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}