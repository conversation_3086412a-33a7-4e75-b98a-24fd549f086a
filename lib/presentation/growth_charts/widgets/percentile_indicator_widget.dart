import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../services/growth_analyzer.dart';

/// Widget to display percentile with color-coded indicators
class PercentileIndicatorWidget extends StatelessWidget {
  final double percentile;
  final String measurementType;
  final bool showLabel;
  final bool showInterpretation;
  final double? size;

  const PercentileIndicatorWidget({
    Key? key,
    required this.percentile,
    required this.measurementType,
    this.showLabel = true,
    this.showInterpretation = false,
    this.size,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final interpretation = GrowthAnalyzer.getPercentileInterpretation(percentile);
    final colorCode = interpretation['colorCode'] as String;
    final textColor = interpretation['textColor'] as String;
    final category = interpretation['category'] as String;
    final requiresAttention = interpretation['requiresAttention'] as bool;

    final indicatorColor = Color(int.parse(colorCode.substring(1), radix: 16) + 0xFF000000);
    final textColorParsed = Color(int.parse(textColor.substring(1), radix: 16) + 0xFF000000);

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: (size ?? 4.w) * 0.75,
            vertical: (size ?? 4.w) * 0.5,
          ),
          decoration: BoxDecoration(
            color: indicatorColor,
            borderRadius: BorderRadius.circular((size ?? 4.w) * 0.5),
            boxShadow: [
              BoxShadow(
                color: indicatorColor.withOpacity(0.3),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (requiresAttention)
                Padding(
                  padding: EdgeInsets.only(right: 1.w),
                  child: Icon(
                    Icons.priority_high,
                    color: textColorParsed,
                    size: (size ?? 4.w) * 0.8,
                  ),
                ),
              Text(
                '${percentile.toStringAsFixed(1)}th',
                style: TextStyle(
                  fontSize: (size ?? 4.w) * 0.8,
                  fontWeight: FontWeight.bold,
                  color: textColorParsed,
                ),
              ),
            ],
          ),
        ),
        if (showLabel) ...[
          SizedBox(height: 1.w),
          Text(
            category,
            style: TextStyle(
              fontSize: (size ?? 4.w) * 0.6,
              fontWeight: FontWeight.w500,
              color: indicatorColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
        if (showInterpretation) ...[
          SizedBox(height: 1.w),
          Container(
            constraints: BoxConstraints(maxWidth: 40.w),
            child: Text(
              interpretation['interpretation'] as String,
              style: TextStyle(
                fontSize: (size ?? 4.w) * 0.5,
                color: Theme.of(context).textTheme.bodySmall?.color,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ],
    );
  }
}

/// Horizontal percentile scale widget showing where current percentile falls
class PercentileScaleWidget extends StatelessWidget {
  final double percentile;
  final double height;
  final bool showLabels;

  const PercentileScaleWidget({
    Key? key,
    required this.percentile,
    this.height = 40,
    this.showLabels = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      child: Column(
        children: [
          if (showLabels) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildScaleLabel('3rd', Colors.red),
                _buildScaleLabel('10th', Colors.orange),
                _buildScaleLabel('25th', Colors.amber),
                _buildScaleLabel('50th', Colors.green),
                _buildScaleLabel('75th', Colors.blue),
                _buildScaleLabel('90th', Colors.purple),
                _buildScaleLabel('97th', Colors.pink),
              ],
            ),
            SizedBox(height: 1.w),
          ],
          Expanded(
            child: Stack(
              children: [
                // Background scale
                Container(
                  height: 8,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    gradient: const LinearGradient(
                      colors: [
                        Colors.red,
                        Colors.orange,
                        Colors.amber,
                        Colors.green,
                        Colors.blue,
                        Colors.purple,
                        Colors.pink,
                      ],
                      stops: [0.03, 0.1, 0.25, 0.5, 0.75, 0.9, 0.97],
                    ),
                  ),
                ),
                // Percentile markers
                ...List.generate(7, (index) {
                  final percentiles = [3.0, 10.0, 25.0, 50.0, 75.0, 90.0, 97.0];
                  final positions = [0.03, 0.1, 0.25, 0.5, 0.75, 0.9, 0.97];
                  return Positioned(
                    left: MediaQuery.of(context).size.width * 0.8 * positions[index] - 1,
                    top: 0,
                    child: Container(
                      width: 2,
                      height: 8,
                      color: Colors.white.withOpacity(0.7),
                    ),
                  );
                }),
                // Current percentile indicator
                Positioned(
                  left: _getPercentilePosition(percentile, context) - 6,
                  top: -4,
                  child: Container(
                    width: 12,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(2),
                      border: Border.all(color: Colors.black, width: 2),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Center(
                      child: Container(
                        width: 4,
                        height: 4,
                        decoration: const BoxDecoration(
                          color: Colors.black,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          if (showLabels) ...[
            SizedBox(height: 1.w),
            Text(
              '${percentile.toStringAsFixed(1)}th percentile',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildScaleLabel(String label, Color color) {
    return Text(
      label,
      style: TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.w500,
        color: color,
      ),
    );
  }

  double _getPercentilePosition(double percentile, BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width * 0.8;
    
    // Convert percentile to position on scale (0-100 to 0-1)
    double normalizedPosition;
    
    if (percentile <= 3) {
      normalizedPosition = 0.03 * (percentile / 3);
    } else if (percentile <= 10) {
      normalizedPosition = 0.03 + (0.07 * ((percentile - 3) / 7));
    } else if (percentile <= 25) {
      normalizedPosition = 0.1 + (0.15 * ((percentile - 10) / 15));
    } else if (percentile <= 50) {
      normalizedPosition = 0.25 + (0.25 * ((percentile - 25) / 25));
    } else if (percentile <= 75) {
      normalizedPosition = 0.5 + (0.25 * ((percentile - 50) / 25));
    } else if (percentile <= 90) {
      normalizedPosition = 0.75 + (0.15 * ((percentile - 75) / 15));
    } else if (percentile <= 97) {
      normalizedPosition = 0.9 + (0.07 * ((percentile - 90) / 7));
    } else {
      normalizedPosition = 0.97 + (0.03 * ((percentile - 97) / 3));
    }
    
    return screenWidth * normalizedPosition.clamp(0.0, 1.0);
  }
}

/// Compact percentile badge for list items
class PercentileBadgeWidget extends StatelessWidget {
  final double percentile;
  final double size;

  const PercentileBadgeWidget({
    Key? key,
    required this.percentile,
    this.size = 24,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final interpretation = GrowthAnalyzer.getPercentileInterpretation(percentile);
    final colorCode = interpretation['colorCode'] as String;
    final textColor = interpretation['textColor'] as String;
    final requiresAttention = interpretation['requiresAttention'] as bool;

    final indicatorColor = Color(int.parse(colorCode.substring(1), radix: 16) + 0xFF000000);
    final textColorParsed = Color(int.parse(textColor.substring(1), radix: 16) + 0xFF000000);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: indicatorColor,
        shape: BoxShape.circle,
        boxShadow: [
          BoxShadow(
            color: indicatorColor.withOpacity(0.3),
            blurRadius: 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Stack(
        children: [
          Center(
            child: Text(
              percentile.toStringAsFixed(0),
              style: TextStyle(
                fontSize: size * 0.35,
                fontWeight: FontWeight.bold,
                color: textColorParsed,
              ),
            ),
          ),
          if (requiresAttention)
            Positioned(
              top: 0,
              right: 0,
              child: Container(
                width: size * 0.3,
                height: size * 0.3,
                decoration: BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 1),
                ),
                child: Icon(
                  Icons.priority_high,
                  color: Colors.white,
                  size: size * 0.2,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

/// Growth trend arrow widget
class GrowthTrendWidget extends StatelessWidget {
  final double currentPercentile;
  final double? previousPercentile;
  final double size;

  const GrowthTrendWidget({
    Key? key,
    required this.currentPercentile,
    this.previousPercentile,
    this.size = 24,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (previousPercentile == null) {
      return SizedBox(width: size, height: size);
    }

    final difference = currentPercentile - previousPercentile!;
    final isSignificant = difference.abs() > 5.0; // 5 percentile points threshold

    IconData trendIcon;
    Color trendColor;

    if (difference > 5.0) {
      trendIcon = Icons.trending_up;
      trendColor = Colors.green;
    } else if (difference < -5.0) {
      trendIcon = Icons.trending_down;
      trendColor = Colors.red;
    } else {
      trendIcon = Icons.trending_flat;
      trendColor = Colors.grey;
    }

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: trendColor.withOpacity(0.1),
        shape: BoxShape.circle,
        border: Border.all(
          color: trendColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Icon(
        trendIcon,
        color: trendColor,
        size: size * 0.6,
      ),
    );
  }
}