import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/measurement.dart';
import 'chart_interaction_handler.dart';
import 'chart_gesture_handler.dart';
import 'measurement_tooltip_system.dart';
import 'percentile_curve_hover_system.dart';
import 'chart_data_processor.dart';

/// Enhanced Growth Chart Renderer with comprehensive interaction handling
class EnhancedGrowthChartRenderer extends StatefulWidget {
  final List<Measurement> measurements;
  final String measurementType;
  final String gender;
  final bool isMetric;
  final String dateRange;
  final DateTime birthDate;
  final Function(Measurement)? onMeasurementTap;
  final Function(Measurement)? onMeasurementEdit;
  final Function(String, double)? onPercentileCurveHover;

  const EnhancedGrowthChartRenderer({
    super.key,
    required this.measurements,
    required this.measurementType,
    required this.gender,
    required this.isMetric,
    required this.dateRange,
    required this.birthDate,
    this.onMeasurementTap,
    this.onMeasurementEdit,
    this.onPercentileCurveHover,
  });

  @override
  State<EnhancedGrowthChartRenderer> createState() => _EnhancedGrowthChartRendererState();
}

class _EnhancedGrowthChartRendererState extends State<EnhancedGrowthChartRenderer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _tooltipAnimationController;
  late Animation<double> _chartAnimation;
  
  // Chart interaction components
  late ChartInteractionHandler _interactionHandler;
  late ChartGestureHandler _gestureHandler;
  late ChartDataProcessor _dataProcessor;
  
  // Chart display state
  bool _showPercentileCurves = true;
  bool _showDataPoints = true;
  
  // Interaction state
  Measurement? _selectedMeasurement;
  String? _hoveredPercentile;
  double? _hoveredPercentileAge;
  Offset? _tooltipPosition;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    
    _tooltipAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _chartAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    
    // Initialize data processor
    _dataProcessor = ChartDataProcessor();
    
    // Initialize gesture handler
    _gestureHandler = ChartGestureHandler(
      onZoomChanged: (zoom) => setState(() {}),
      onPanChanged: (pan) => setState(() {}),
      onInteractionStart: () => setState(() {}),
      onInteractionEnd: () => setState(() {}),
      onTransformChanged: (zoom, pan) => setState(() {}),
    );
    
    // Initialize interaction handler
    _interactionHandler = ChartInteractionHandler(
      measurementType: widget.measurementType,
      gender: widget.gender,
      isMetric: widget.isMetric,
      birthDate: widget.birthDate,
      measurements: widget.measurements,
      dataProcessor: _dataProcessor,
      onMeasurementTap: (measurement) {
        setState(() {
          _selectedMeasurement = measurement;
          _tooltipPosition = _interactionHandler.tooltipPosition;
        });
        widget.onMeasurementTap?.call(measurement);
      },
      onMeasurementEdit: (measurement) {
        widget.onMeasurementEdit?.call(measurement);
      },
      onPercentileCurveHover: (percentile, age) {
        setState(() {
          _hoveredPercentile = percentile;
          _hoveredPercentileAge = age;
          _tooltipPosition = _interactionHandler.tooltipPosition;
        });
        widget.onPercentileCurveHover?.call(percentile, age);
      },
    );
    
    // Initialize animations
    _gestureHandler.initializeAnimations(this);
    _interactionHandler.initializeAnimations(this);
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _tooltipAnimationController.dispose();
    _gestureHandler.dispose();
    _interactionHandler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 450,
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildChartHeader(),
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(3.w),
              child: _buildInteractiveChart(),
            ),
          ),
          _buildChartControls(),
        ],
      ),
    );
  }

  /// Build chart header with title and info
  Widget _buildChartHeader() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_getMeasurementTitle()} Growth Chart',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                  ),
                ),
                Text(
                  'WHO Standards • ${widget.dateRange} • ${widget.measurements.length} measurements',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          _buildZoomControls(),
        ],
      ),
    );
  }

  /// Build zoom and reset controls
  Widget _buildZoomControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        _buildControlButton(
          icon: Icons.zoom_in,
          onTap: () => _gestureHandler.zoomIn(),
          isEnabled: !_gestureHandler.isAtMaxZoom,
        ),
        SizedBox(width: 1.w),
        _buildControlButton(
          icon: Icons.zoom_out,
          onTap: () => _gestureHandler.zoomOut(),
          isEnabled: !_gestureHandler.isAtMinZoom,
        ),
        SizedBox(width: 1.w),
        _buildControlButton(
          icon: Icons.center_focus_strong,
          onTap: () => _gestureHandler.resetZoomAndPan(),
          isEnabled: !_gestureHandler.isAtDefault,
        ),
      ],
    );
  }

  /// Build interactive chart with gesture handling
  Widget _buildInteractiveChart() {
    if (widget.measurements.isEmpty) {
      return _buildEmptyState();
    }

    return Stack(
      children: [
        // Main chart with gesture detection
        ChartGestureDetector(
          gestureHandler: _gestureHandler,
          child: FadeTransition(
            opacity: _chartAnimation,
            child: LineChart(
              _buildChartData(),
              duration: const Duration(milliseconds: 300),
            ),
          ),
        ),
        
        // Measurement tooltip system
        MeasurementTooltipSystem(
          selectedMeasurement: _selectedMeasurement,
          tooltipPosition: _tooltipPosition,
          measurementType: widget.measurementType,
          gender: widget.gender,
          isMetric: widget.isMetric,
          animationController: _tooltipAnimationController,
          onDismiss: () => setState(() {
            _selectedMeasurement = null;
            _tooltipPosition = null;
          }),
          onEditMeasurement: widget.onMeasurementEdit,
        ),
        
        // Percentile curve hover system
        PercentileCurveHoverSystem(
          hoveredPercentile: _hoveredPercentile,
          hoveredAge: _hoveredPercentileAge,
          hoverPosition: _tooltipPosition,
          measurementType: widget.measurementType,
          gender: widget.gender,
          isMetric: widget.isMetric,
        ),
      ],
    );
  }

  /// Build empty state when no measurements
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.insert_chart_outlined,
            size: 48,
            color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3),
          ),
          SizedBox(height: 2.h),
          Text(
            'No measurements yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Add your first ${_getMeasurementTitle().toLowerCase()} measurement\nto see growth trends and percentiles',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  /// Build chart data for fl_chart
  LineChartData _buildChartData() {
    final maxAge = _getMaxAgeForDateRange();
    
    // Ensure measurements are valid before processing
    final validMeasurements = widget.measurements
        .where((m) => m.measuredAt != null && m.value != null)
        .toList();
    
    final spots = _convertMeasurementsToSpots(
      validMeasurements.map((m) => m.toJson()).toList()
    );

    return LineChartData(
      gridData: _buildGridData(),
      titlesData: _buildTitlesData(),
      borderData: _buildBorderData(),
      minX: 0,
      maxX: maxAge,
      minY: _getMinY(),
      maxY: _getMaxY(),
      lineBarsData: [
        // WHO percentile curves
        if (_showPercentileCurves) ..._buildPercentileCurves(),
        // Measurement data line
        if (_showDataPoints && spots.isNotEmpty) _buildMeasurementLine(spots),
      ],
      lineTouchData: LineTouchData(
        enabled: true,
        handleBuiltInTouches: false,
        touchCallback: _interactionHandler.handleChartTouch,
        touchTooltipData: LineTouchTooltipData(
          getTooltipColor: (touchedSpot) => Colors.transparent,
          getTooltipItems: (touchedSpots) => [],
        ),
      ),
      clipData: const FlClipData.all(),
    );
  }

  /// Build WHO percentile curves
  List<LineChartBarData> _buildPercentileCurves() {
    final percentiles = [3.0, 10.0, 25.0, 50.0, 75.0, 90.0, 97.0];
    final colors = [
      const Color(0xFFDC2626), // 3rd - red
      const Color(0xFFEA580C), // 10th - orange
      const Color(0xFFF59E0B), // 25th - amber
      const Color(0xFF059669), // 50th - green
      const Color(0xFF0284C7), // 75th - blue
      const Color(0xFF7C3AED), // 90th - violet
      const Color(0xFF9333EA), // 97th - purple
    ];

    return percentiles.asMap().entries.map((entry) {
      final index = entry.key;
      final percentile = entry.value;
      
      try {
        final points = _generatePercentilePoints(percentile, _getMaxAgeForDateRange());
        
        if (points.isEmpty) {
          return LineChartBarData(spots: []);
        }
        
        return LineChartBarData(
          spots: points,
          isCurved: true,
          color: colors[index].withValues(alpha: percentile == 50.0 ? 1.0 : 0.8),
          barWidth: percentile == 50.0 ? 3.0 : 2.0,
          isStrokeCapRound: true,
          dotData: const FlDotData(show: false),
          belowBarData: BarAreaData(show: false),
          dashArray: percentile == 50.0 || percentile == 3.0 || percentile == 97.0 
              ? null 
              : [6, 4],
        );
      } catch (e) {
        debugPrint('Error generating percentile curve for $percentile: $e');
        return LineChartBarData(spots: []);
      }
    }).toList();
  }

  /// Build measurement data line
  LineChartBarData _buildMeasurementLine(List<FlSpot> spots) {
    return LineChartBarData(
      spots: spots,
      isCurved: true,
      color: Theme.of(context).colorScheme.primary,
      barWidth: 3.5,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          if (index >= widget.measurements.length) {
            return FlDotCirclePainter(
              radius: 5,
              color: Theme.of(context).colorScheme.primary,
            );
          }
          
          final measurement = widget.measurements[index];
          final percentile = _calculatePercentile(
            measurement.value, 
            measurement.ageInMonths
          );
          
          // Color code based on percentile
          Color dotColor;
          double radius = 5.0;
          
          if (percentile < 3.0 || percentile > 97.0) {
            dotColor = const Color(0xFFDC2626);
            radius = 7.0;
          } else if (percentile < 10.0 || percentile > 90.0) {
            dotColor = const Color(0xFFEA580C);
            radius = 6.0;
          } else {
            dotColor = Theme.of(context).colorScheme.primary;
          }
          
          return FlDotCirclePainter(
            radius: radius,
            color: dotColor,
            strokeWidth: 2,
            strokeColor: ThemeAwareColors.getCardColor(context),
          );
        },
      ),
      belowBarData: BarAreaData(
        show: true,
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }

  /// Build chart controls
  Widget _buildChartControls() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context).withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Row(
              children: [
                _buildToggleButton(
                  'Curves',
                  Icons.show_chart,
                  _showPercentileCurves,
                  () => setState(() => _showPercentileCurves = !_showPercentileCurves),
                ),
                SizedBox(width: 2.w),
                _buildToggleButton(
                  'Points',
                  Icons.scatter_plot,
                  _showDataPoints,
                  () => setState(() => _showDataPoints = !_showDataPoints),
                ),
              ],
            ),
          ),
          Text(
            'Zoom: ${_gestureHandler.zoomPercentage.toInt()}%',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  /// Build toggle button
  Widget _buildToggleButton(String label, IconData icon, bool isActive, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isActive
                ? Theme.of(context).colorScheme.primary
                : ThemeAwareColors.getDividerColor(context),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : ThemeAwareColors.getSecondaryTextColor(context),
            ),
            SizedBox(width: 1.w),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : ThemeAwareColors.getSecondaryTextColor(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build control button
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onTap,
    bool isEnabled = true,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onTap : null,
      child: Container(
        padding: EdgeInsets.all(1.5.w),
        decoration: BoxDecoration(
          color: isEnabled
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isEnabled
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                : ThemeAwareColors.getDividerColor(context),
          ),
        ),
        child: Icon(
          icon,
          size: 16,
          color: isEnabled
              ? Theme.of(context).colorScheme.primary
              : ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.5),
        ),
      ),
    );
  }

  // Helper methods
  FlGridData _buildGridData() {
    return FlGridData(
      show: true,
      drawVerticalLine: true,
      drawHorizontalLine: true,
      horizontalInterval: _getHorizontalInterval(),
      verticalInterval: _getVerticalInterval(),
      getDrawingHorizontalLine: (value) => FlLine(
        color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.2),
        strokeWidth: 0.8,
        dashArray: [4, 4],
      ),
      getDrawingVerticalLine: (value) => FlLine(
        color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.2),
        strokeWidth: 0.8,
        dashArray: [4, 4],
      ),
    );
  }

  FlTitlesData _buildTitlesData() {
    return FlTitlesData(
      show: true,
      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 35,
          interval: _getVerticalInterval(),
          getTitlesWidget: (value, meta) => Padding(
            padding: EdgeInsets.only(top: 1.h),
            child: Text(
              '${value.toInt()}m',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ),
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 50,
          interval: _getHorizontalInterval(),
          getTitlesWidget: (value, meta) => Padding(
            padding: EdgeInsets.only(right: 1.w),
            child: Text(
              '${value.toStringAsFixed(0)}${_getDisplayUnit()}',
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ),
        ),
      ),
    );
  }

  FlBorderData _buildBorderData() {
    return FlBorderData(
      show: true,
      border: Border.all(
        color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
        width: 1.5,
      ),
    );
  }

  String _getMeasurementTitle() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return 'Weight';
      case 'height':
      case 'length':
        return 'Height';
      case 'head_circumference':
      case 'head circumference':
        return 'Head Circumference';
      default:
        return 'Weight';
    }
  }

  String _getDisplayUnit() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return widget.isMetric ? 'kg' : 'lbs';
      case 'height':
      case 'length':
      case 'head_circumference':
        return widget.isMetric ? 'cm' : 'in';
      default:
        return '';
    }
  }

  double _getMaxAgeForDateRange() {
    switch (widget.dateRange) {
      case '6 months':
        return 6.0;
      case '1 year':
        return 12.0;
      case '2 years':
        return 24.0;
      case '3 years':
        return 36.0;
      case '4 years':
        return 48.0;
      case '5 years':
        return 60.0;
      default:
        return 12.0;
    }
  }

  double _getMinY() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return widget.isMetric ? 2.0 : 4.4;
      case 'height':
      case 'length':
        return widget.isMetric ? 45.0 : 18.0;
      case 'head_circumference':
      case 'head circumference':
        return widget.isMetric ? 32.0 : 12.5;
      default:
        return widget.isMetric ? 2.0 : 4.4;
    }
  }

  double _getMaxY() {
    final maxAge = _getMaxAgeForDateRange();
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        if (maxAge <= 12) return widget.isMetric ? 14.0 : 31.0;
        if (maxAge <= 24) return widget.isMetric ? 18.0 : 40.0;
        return widget.isMetric ? 26.0 : 57.0;
      case 'height':
      case 'length':
        if (maxAge <= 12) return widget.isMetric ? 85.0 : 33.5;
        if (maxAge <= 24) return widget.isMetric ? 95.0 : 37.5;
        return widget.isMetric ? 125.0 : 49.0;
      case 'head_circumference':
      case 'head circumference':
        if (maxAge <= 12) return widget.isMetric ? 53.0 : 21.0;
        return widget.isMetric ? 62.0 : 24.5;
      default:
        return widget.isMetric ? 16.0 : 35.0;
    }
  }

  double _getHorizontalInterval() {
    final range = _getMaxY() - _getMinY();
    return range / 8; // 8 intervals
  }

  double _getVerticalInterval() {
    final maxAge = _getMaxAgeForDateRange();
    if (maxAge <= 6) return 1.0;
    if (maxAge <= 12) return 2.0;
    if (maxAge <= 24) return 3.0;
    return 6.0;
  }

  // Helper methods to replace missing ChartDataProcessor methods
  List<FlSpot> _convertMeasurementsToSpots(List<Map<String, dynamic>> measurements) {
    return measurements.map((m) {
      final age = m['ageInMonths'] as double? ?? 0.0;
      final value = m['value'] as double? ?? 0.0;
      return FlSpot(age, value);
    }).toList();
  }

  List<FlSpot> _generatePercentilePoints(double percentile, double maxAge) {
    // Simplified percentile curve generation
    final points = <FlSpot>[];
    for (double age = 0; age <= maxAge; age += 0.5) {
      final value = _getPercentileValue(percentile, age);
      points.add(FlSpot(age, value));
    }
    return points;
  }

  double _getPercentileValue(double percentile, double age) {
    // Simplified percentile calculation - in real app, use WHO data
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return 3.0 + (age * 0.5) + (percentile - 50) * 0.1;
      case 'height':
        return 50.0 + (age * 2.0) + (percentile - 50) * 0.2;
      case 'head_circumference':
        return 35.0 + (age * 0.3) + (percentile - 50) * 0.05;
      default:
        return 0.0;
    }
  }

  double _calculatePercentile(double value, double age) {
    // Simplified percentile calculation
    return 50.0; // Default to 50th percentile
  }

}