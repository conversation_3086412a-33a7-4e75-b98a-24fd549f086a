/// Configuration constants and calculations for growth charts
class ChartConfiguration {
  static const Map<String, double> _dateRangeToMaxAge = {
    '6 months': 6.0,
    '1 year': 12.0,
    '2 years': 24.0,
    '3 years': 36.0,
    '4 years': 48.0,
    '5 years': 60.0,
  };

  static double getMaxXForChart(String dateRange) {
    return _dateRangeToMaxAge[dateRange] ?? 12.0;
  }

  static double getMinY(int measurementType, bool isMetric) {
    switch (measurementType) {
      case 0: // Weight
        return isMetric ? 2.0 : 4.4;
      case 1: // Height
        return isMetric ? 45 : 18;
      case 2: // Head Circumference
        return isMetric ? 32 : 12.5;
      default:
        return 2.0;
    }
  }

  static double getMaxY(int measurementType, bool isMetric, String dateRange) {
    final maxX = getMaxXForChart(dateRange);
    
    switch (measurementType) {
      case 0: // Weight
        return _getWeightMaxY(maxX, isMetric);
      case 1: // Height
        return _getHeightMaxY(maxX, isMetric);
      case 2: // Head Circumference
        return _getHeadCircumferenceMaxY(maxX, isMetric);
      default:
        return 16;
    }
  }

  static double _getWeightMaxY(double maxX, bool isMetric) {
    if (maxX <= 6) return isMetric ? 11 : 24;
    if (maxX <= 12) return isMetric ? 14 : 31;
    if (maxX <= 24) return isMetric ? 18 : 40;
    if (maxX <= 36) return isMetric ? 22 : 48;
    if (maxX <= 48) return isMetric ? 24 : 53;
    return isMetric ? 26 : 57;
  }

  static double _getHeightMaxY(double maxX, bool isMetric) {
    if (maxX <= 6) return isMetric ? 78 : 31;
    if (maxX <= 12) return isMetric ? 85 : 33;
    if (maxX <= 24) return isMetric ? 95 : 37;
    if (maxX <= 36) return isMetric ? 105 : 41;
    if (maxX <= 48) return isMetric ? 115 : 45;
    return isMetric ? 125 : 49;
  }

  static double _getHeadCircumferenceMaxY(double maxX, bool isMetric) {
    if (maxX <= 6) return isMetric ? 51 : 20.1;
    if (maxX <= 12) return isMetric ? 53 : 20.9;
    if (maxX <= 24) return isMetric ? 56 : 22.0;
    if (maxX <= 36) return isMetric ? 58 : 22.8;
    if (maxX <= 48) return isMetric ? 60 : 23.6;
    return isMetric ? 62 : 24.4;
  }

  static String getUnit(int measurementType, bool isMetric) {
    switch (measurementType) {
      case 0: // Weight
        return isMetric ? 'kg' : 'lbs';
      case 1: // Height
        return isMetric ? 'cm' : 'in';
      case 2: // Head Circumference
        return isMetric ? 'cm' : 'in';
      default:
        return 'kg';
    }
  }
}