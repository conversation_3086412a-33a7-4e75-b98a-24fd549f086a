import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/who_data_service.dart';
import '../../../services/unit_conversion_service.dart';
import '../../../services/time_range_service.dart';
import '../../../services/responsive_chart_service.dart';
import '../../../services/offline_who_data_cache.dart';
import '../../../models/measurement.dart';
import 'chart_interaction_handler.dart';
import 'chart_gesture_handler.dart';
import 'measurement_tooltip_system.dart';
import 'percentile_curve_hover_system.dart';
import 'chart_data_processor.dart';

/// Custom painter for dashed lines in legend
class DashedLinePainter extends CustomPainter {
  final Color color;
  final double dashWidth;
  final double dashSpace;

  DashedLinePainter({
    required this.color,
    this.dashWidth = 4.0,
    this.dashSpace = 3.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = size.height
      ..style = PaintingStyle.stroke;

    double startX = 0;
    while (startX < size.width) {
      final endX = (startX + dashWidth).clamp(0.0, size.width);
      canvas.drawLine(
        Offset(startX, size.height / 2),
        Offset(endX, size.height / 2),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

/// WHO Percentile Curve data model
class WHOPercentileCurve {
  final String percentile;
  final List<FlSpot> points;
  final Color curveColor;
  final double strokeWidth;
  final String label;
  final bool isDashed;

  const WHOPercentileCurve({
    required this.percentile,
    required this.points,
    required this.curveColor,
    required this.strokeWidth,
    required this.label,
    this.isDashed = false,
  });
}

/// Interactive Growth Chart Renderer with enhanced WHO percentile visualization
class GrowthChartRenderer extends StatefulWidget {
  final List<Map<String, dynamic>> measurements;
  final String measurementType;
  final String gender;
  final bool isMetric;
  final String dateRange;
  final DateTime birthDate;
  final Function(Map<String, dynamic>)? onDataPointTap;
  final Function(String, double)? onPercentileCurveHover;

  const GrowthChartRenderer({
    super.key,
    required this.measurements,
    required this.measurementType,
    required this.gender,
    required this.isMetric,
    required this.dateRange,
    required this.birthDate,
    this.onDataPointTap,
    this.onPercentileCurveHover,
  });

  @override
  State<GrowthChartRenderer> createState() => _GrowthChartRendererState();
}

class _GrowthChartRendererState extends State<GrowthChartRenderer>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;
  
  // Chart interaction components
  late ChartInteractionHandler _interactionHandler;
  late ChartGestureHandler _gestureHandler;
  late ChartDataProcessor _dataProcessor;
  
  // Chart display state
  bool _showPercentileCurves = true;
  bool _showDataPoints = true;
  
  // Interaction state
  Measurement? _selectedMeasurement;
  String? _hoveredPercentile;
  double? _hoveredPercentileAge;
  Offset? _tooltipPosition;
  Map<String, dynamic>? _hoveredDataPoint;
  
  // Chart zoom and pan state
  double _zoomLevel = 1.0;
  Offset _panOffset = Offset.zero;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );
    
    // Initialize data processor
    _dataProcessor = ChartDataProcessor();
    
    // Initialize gesture handler
    _gestureHandler = ChartGestureHandler(
      onZoomChanged: (zoom) => setState(() {}),
      onPanChanged: (pan) => setState(() {}),
      onInteractionStart: () => setState(() {}),
      onInteractionEnd: () => setState(() {}),
      onTransformChanged: (zoom, pan) => setState(() {}),
    );
    
    // Initialize interaction handler
    _interactionHandler = ChartInteractionHandler(
      measurementType: widget.measurementType,
      gender: widget.gender,
      isMetric: widget.isMetric,
      birthDate: widget.birthDate,
      measurements: _convertMeasurementsToModels(),
      dataProcessor: _dataProcessor,
      onMeasurementTap: (measurement) {
        setState(() {
          _selectedMeasurement = measurement;
        });
        widget.onDataPointTap?.call(measurement.toJson());
      },
      onMeasurementEdit: (measurement) {
        // Handle measurement editing
      },
      onPercentileCurveHover: (percentile, age) {
        setState(() {
          _hoveredPercentile = percentile;
          _hoveredPercentileAge = age;
        });
        widget.onPercentileCurveHover?.call(percentile, age);
      },
      onZoomChanged: (zoom) => setState(() {}),
      onPanChanged: (pan) => setState(() {}),
    );
    
    // Initialize animations
    _gestureHandler.initializeAnimations(this);
    _interactionHandler.initializeAnimations(this);
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _gestureHandler.dispose();
    _interactionHandler.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 400,
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildChartHeader(),
          Expanded(
            child: Padding(
              padding: EdgeInsets.all(3.w),
              child: _buildInteractiveChart(),
            ),
          ),
          _buildChartLegend(),
        ],
      ),
    );
  }

  Widget _buildChartHeader() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_getMeasurementTitle()} Growth Chart',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                  ),
                ),
                Text(
                  'WHO Standards • ${widget.dateRange} • ${widget.measurements.length} measurements',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ),
          _buildChartControls(),
        ],
      ),
    );
  }

  Widget _buildChartControls() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Percentile curves toggle
        _buildControlButton(
          icon: _showPercentileCurves ? Icons.show_chart : Icons.show_chart_outlined,
          label: 'Curves',
          isActive: _showPercentileCurves,
          onTap: () => setState(() => _showPercentileCurves = !_showPercentileCurves),
        ),
        SizedBox(width: 2.w),
        // Data points toggle
        _buildControlButton(
          icon: _showDataPoints ? Icons.scatter_plot : Icons.scatter_plot_outlined,
          label: 'Points',
          isActive: _showDataPoints,
          onTap: () => setState(() => _showDataPoints = !_showDataPoints),
        ),
      ],
    );
  }

  Widget _buildControlButton({
    required IconData icon,
    required String label,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: isActive
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isActive
                ? Theme.of(context).colorScheme.primary
                : ThemeAwareColors.getDividerColor(context),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: 16,
              color: isActive
                  ? Theme.of(context).colorScheme.primary
                  : ThemeAwareColors.getSecondaryTextColor(context),
            ),
            SizedBox(width: 1.w),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: isActive
                    ? Theme.of(context).colorScheme.primary
                    : ThemeAwareColors.getSecondaryTextColor(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInteractiveChart() {
    if (widget.measurements.isEmpty) {
      return _buildEmptyState();
    }

    return Stack(
      children: [
        FadeTransition(
          opacity: _animation,
          child: GestureDetector(
            onScaleStart: _handleScaleStart,
            onScaleUpdate: _handleScaleUpdate,
            child: LineChart(
              _buildChartData(),
              duration: const Duration(milliseconds: 300),
            ),
          ),
        ),
        if (_hoveredDataPoint != null && _tooltipPosition != null)
          _buildTooltip(),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.insert_chart_outlined,
            size: 48,
            color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3),
          ),
          SizedBox(height: 2.h),
          Text(
            'No measurements yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Add your first ${_getMeasurementTitle().toLowerCase()} measurement\nto see growth trends and percentiles',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  LineChartData _buildChartData() {
    final maxAge = _getMaxAgeForDateRange();
    final minY = _getMinY();
    final maxY = _getMaxY();

    return LineChartData(
      gridData: _buildGridData(),
      titlesData: _buildTitlesData(),
      borderData: _buildBorderData(),
      minX: 0,
      maxX: maxAge,
      minY: minY,
      maxY: maxY,
      lineBarsData: [
        // WHO percentile curves
        if (_showPercentileCurves) ..._buildWHOPercentileCurves(),
        // Baby's measurement data
        if (_showDataPoints) _buildMeasurementDataLine(),
      ],
      lineTouchData: _buildTouchData(),
      clipData: const FlClipData.all(),
    );
  }

  FlGridData _buildGridData() {
    return FlGridData(
      show: true,
      drawVerticalLine: true,
      drawHorizontalLine: true,
      horizontalInterval: _getHorizontalInterval(),
      verticalInterval: _getVerticalInterval(),
      getDrawingHorizontalLine: (value) => FlLine(
        color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.2),
        strokeWidth: 0.8,
        dashArray: [4, 4],
      ),
      getDrawingVerticalLine: (value) => FlLine(
        color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.2),
        strokeWidth: 0.8,
        dashArray: [4, 4],
      ),
    );
  }

  FlTitlesData _buildTitlesData() {
    return FlTitlesData(
      show: true,
      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 35,
          interval: _getVerticalInterval(),
          getTitlesWidget: (value, meta) => _buildBottomTitle(value),
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 50,
          interval: _getHorizontalInterval(),
          getTitlesWidget: (value, meta) => _buildLeftTitle(value),
        ),
      ),
    );
  }

  FlBorderData _buildBorderData() {
    return FlBorderData(
      show: true,
      border: Border.all(
        color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
        width: 1.5,
      ),
    );
  }

  LineTouchData _buildTouchData() {
    return LineTouchData(
      enabled: true,
      handleBuiltInTouches: false,
      touchCallback: _handleChartTouch,
      touchTooltipData: LineTouchTooltipData(
        getTooltipColor: (touchedSpot) => Colors.transparent,
        getTooltipItems: (touchedSpots) => [],
      ),
    );
  }

  List<LineChartBarData> _buildWHOPercentileCurves() {
    final curves = _generateWHOPercentileCurves();
    return curves.map((curve) => LineChartBarData(
      spots: curve.points,
      isCurved: true,
      color: curve.curveColor,
      barWidth: curve.strokeWidth,
      isStrokeCapRound: true,
      dotData: const FlDotData(show: false),
      belowBarData: BarAreaData(show: false),
      dashArray: curve.isDashed ? [6, 6] : null,
    )).toList();
  }

  LineChartBarData _buildMeasurementDataLine() {
    final spots = widget.measurements.map((measurement) {
      final ageInMonths = _calculateAgeInMonths(measurement['date'] as DateTime);
      var value = (measurement['value'] as num).toDouble();
      
      // Convert to display units if needed
      if (!widget.isMetric) {
        value = _convertValueForDisplay(value);
      }
      
      return FlSpot(ageInMonths, value);
    }).toList();

    // Sort spots by age for proper connecting lines
    spots.sort((a, b) => a.x.compareTo(b.x));

    // Enhanced trend analysis for better visualization
    final trendAnalysis = _analyzeTrend(spots);
    
    return LineChartBarData(
      spots: spots,
      isCurved: true,
      gradient: LinearGradient(
        colors: _getTrendGradientColors(trendAnalysis),
      ),
      barWidth: 3.5,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: true,
        getDotPainter: (spot, percent, barData, index) {
          // Get measurement for this spot
          final measurement = widget.measurements[index];
          final ageInMonths = _calculateAgeInMonths(measurement['date'] as DateTime);
          final value = (measurement['value'] as num).toDouble();
          
          // Calculate percentile for enhanced visual indicators
          final percentile = WHODataService.calculateExactPercentile(
            value, 
            ageInMonths, 
            widget.measurementType, 
            widget.gender
          );
          
          // Enhanced visual indicators for measurements outside normal ranges
          Color dotColor;
          Color strokeColor = ThemeAwareColors.getCardColor(context);
          double radius = 5.0;
          double strokeWidth = 2.5;
          
          if (percentile < 3.0) {
            // Critical low - bright red with thick border
            dotColor = const Color(0xFFDC2626);
            strokeColor = const Color(0xFFDC2626).withValues(alpha: 0.3);
            radius = 8.0;
            strokeWidth = 3.5;
          } else if (percentile > 97.0) {
            // Critical high - bright purple with thick border
            dotColor = const Color(0xFF9333EA);
            strokeColor = const Color(0xFF9333EA).withValues(alpha: 0.3);
            radius = 8.0;
            strokeWidth = 3.5;
          } else if (percentile < 10.0) {
            // Low normal - orange with medium border
            dotColor = const Color(0xFFEA580C);
            strokeColor = const Color(0xFFEA580C).withValues(alpha: 0.2);
            radius = 6.5;
            strokeWidth = 3.0;
          } else if (percentile > 90.0) {
            // High normal - violet with medium border
            dotColor = const Color(0xFF7C3AED);
            strokeColor = const Color(0xFF7C3AED).withValues(alpha: 0.2);
            radius = 6.5;
            strokeWidth = 3.0;
          } else {
            // Normal range - primary color with standard styling
            dotColor = Theme.of(context).colorScheme.primary;
            strokeColor = ThemeAwareColors.getCardColor(context);
            radius = 5.5;
          }
          
          return FlDotCirclePainter(
            radius: radius,
            color: dotColor,
            strokeWidth: strokeWidth,
            strokeColor: strokeColor,
          );
        },
      ),
      belowBarData: BarAreaData(
        show: true,
        gradient: LinearGradient(
          colors: _getTrendAreaGradientColors(trendAnalysis),
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
      ),
    );
  }

  List<WHOPercentileCurve> _generateWHOPercentileCurves() {
    final percentiles = [3.0, 10.0, 25.0, 50.0, 75.0, 90.0, 97.0];
    
    // Enhanced distinct colors following WHO medical chart standards
    final colors = [
      const Color(0xFFDC2626), // 3rd - Dark Red (concerning low)
      const Color(0xFFEA580C), // 10th - Orange Red (low normal)
      const Color(0xFFF59E0B), // 25th - Amber (lower average)
      const Color(0xFF059669), // 50th - Green (median - healthy)
      const Color(0xFF0284C7), // 75th - Sky Blue (higher average)
      const Color(0xFF7C3AED), // 90th - Violet (high normal)
      const Color(0xFF9333EA), // 97th - Purple (concerning high)
    ];

    // Enhanced stroke patterns for better medical chart distinction
    final strokeWidths = [2.8, 2.0, 1.6, 4.0, 1.6, 2.0, 2.8]; // Emphasize boundaries and median
    final dashPatterns = [
      null,        // 3rd - solid (critical boundary)
      [8, 4],      // 10th - long dashed
      [5, 3],      // 25th - medium dashed
      null,        // 50th - solid (median reference)
      [5, 3],      // 75th - medium dashed
      [8, 4],      // 90th - long dashed
      null,        // 97th - solid (critical boundary)
    ];

    return percentiles.asMap().entries.map((entry) {
      final index = entry.key;
      final percentile = entry.value;
      final isMedian = percentile == 50.0;
      final isCriticalBoundary = percentile == 3.0 || percentile == 97.0;
      final isNormalBoundary = percentile == 10.0 || percentile == 90.0;
      
      final points = _generatePercentilePoints(percentile);
      
      // Enhanced alpha values for better visual hierarchy
      double alpha;
      if (isMedian) {
        alpha = 1.0; // Full opacity for median
      } else if (isCriticalBoundary) {
        alpha = 0.95; // High opacity for critical boundaries
      } else if (isNormalBoundary) {
        alpha = 0.85; // Medium-high opacity for normal boundaries
      } else {
        alpha = 0.75; // Medium opacity for quartiles
      }
      
      return WHOPercentileCurve(
        percentile: '${percentile.toInt()}${_getPercentileSuffix(percentile.toInt())}',
        points: points,
        curveColor: colors[index].withValues(alpha: alpha),
        strokeWidth: strokeWidths[index],
        label: '${percentile.toInt()}${_getPercentileSuffix(percentile.toInt())} percentile',
        isDashed: dashPatterns[index] != null,
      );
    }).toList();
  }

  List<FlSpot> _generatePercentilePoints(double percentile) {
    final points = <FlSpot>[];
    final maxAge = _getMaxAgeForDateRange();
    final step = maxAge / 50; // Generate 50 points for smooth curves

    for (double age = 0; age <= maxAge; age += step) {
      try {
        var value = WHODataService.getPercentileValue(
          percentile,
          age,
          widget.measurementType,
          widget.gender,
        );
        
        // Convert to display units if needed
        if (!widget.isMetric) {
          value = _convertValueForDisplay(value);
        }
        
        points.add(FlSpot(age, value));
      } catch (e) {
        // Skip invalid points
        continue;
      }
    }

    return points;
  }

  Widget _buildTooltip() {
    if (_hoveredDataPoint == null || _tooltipPosition == null) {
      return const SizedBox.shrink();
    }

    final measurement = _hoveredDataPoint!;
    final percentile = WHODataService.calculateExactPercentile(
      (measurement['value'] as num).toDouble(),
      _calculateAgeInMonths(measurement['date'] as DateTime),
      widget.measurementType,
      widget.gender,
    );

    return Positioned(
      left: _tooltipPosition!.dx - 75,
      top: _tooltipPosition!.dy - 80,
      child: Container(
        padding: EdgeInsets.all(2.w),
        decoration: BoxDecoration(
          color: ThemeAwareColors.getCardColor(context),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: ThemeAwareColors.getShadowColor(context),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
          border: Border.all(
            color: ThemeAwareColors.getDividerColor(context),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${measurement['value']} ${measurement['unit']}',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            Text(
              _formatDate(measurement['date'] as DateTime),
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
            Text(
              '${percentile.toStringAsFixed(1)}${_getPercentileSuffix(percentile.toInt())} percentile',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (measurement['notes'] != null && measurement['notes'].toString().isNotEmpty)
              Text(
                measurement['notes'].toString(),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  fontStyle: FontStyle.italic,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildChartLegend() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context).withValues(alpha: 0.3),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Legend',
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Wrap(
            spacing: 4.w,
            runSpacing: 0.5.h,
            children: [
              _buildLegendItem(
                'Your Baby',
                Theme.of(context).colorScheme.primary,
                hasCircle: true,
              ),
              if (_showPercentileCurves) ...[
                _buildLegendItem('97th', const Color(0xFF9333EA), isDashed: false, isImportant: true),
                _buildLegendItem('90th', const Color(0xFF7C3AED), isDashed: true),
                _buildLegendItem('75th', const Color(0xFF0284C7), isDashed: true),
                _buildLegendItem('50th (Median)', const Color(0xFF059669), isDashed: false, isMedian: true),
                _buildLegendItem('25th', const Color(0xFFF59E0B), isDashed: true),
                _buildLegendItem('10th', const Color(0xFFEA580C), isDashed: true),
                _buildLegendItem('3rd', const Color(0xFFDC2626), isDashed: false, isImportant: true),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLegendItem(
    String label, 
    Color color, {
    bool hasCircle = false,
    bool isDashed = false,
    bool isImportant = false,
    bool isMedian = false,
  }) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Line representation
        Container(
          width: 16,
          height: isImportant || isMedian ? 3.0 : 2.5,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(isImportant || isMedian ? 1.5 : 1.25),
          ),
          child: isDashed 
            ? CustomPaint(
                painter: DashedLinePainter(color: color),
                size: Size(16, isImportant || isMedian ? 3.0 : 2.5),
              )
            : null,
        ),
        if (hasCircle) ...[
          SizedBox(width: 1.w),
          Container(
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: Border.all(
                color: ThemeAwareColors.getCardColor(context),
                width: 1,
              ),
            ),
          ),
        ],
        SizedBox(width: 1.5.w),
        Text(
          label,
          style: Theme.of(context).textTheme.labelSmall?.copyWith(
            color: ThemeAwareColors.getPrimaryTextColor(context),
            fontWeight: isImportant || isMedian ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildBottomTitle(double value) {
    return Padding(
      padding: EdgeInsets.only(top: 1.h),
      child: Text(
        '${value.toInt()}m',
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: ThemeAwareColors.getSecondaryTextColor(context),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildLeftTitle(double value) {
    final unit = _getUnit();
    final displayValue = _convertValueForDisplay(value);
    
    return Padding(
      padding: EdgeInsets.only(right: 1.w),
      child: Text(
        '${displayValue.toStringAsFixed(_getDecimalPlaces())}$unit',
        style: Theme.of(context).textTheme.labelSmall?.copyWith(
          color: ThemeAwareColors.getSecondaryTextColor(context),
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  // Chart interaction handlers
  void _handleScaleStart(ScaleStartDetails details) {
    // Handle zoom/pan start
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    setState(() {
      _zoomLevel = (details.scale * _zoomLevel).clamp(0.5, 3.0);
      _panOffset += details.focalPointDelta;
    });
  }

  void _handleChartTouch(FlTouchEvent event, LineTouchResponse? response) {
    if (response?.lineBarSpots?.isNotEmpty == true) {
      final spot = response!.lineBarSpots!.first;
      
      // Check if it's a data point (last line bar data)
      if (spot.barIndex == (_showPercentileCurves ? 7 : 0)) {
        final spotIndex = spot.spotIndex;
        if (spotIndex < widget.measurements.length) {
          setState(() {
            _hoveredDataPoint = widget.measurements[spotIndex];
            _tooltipPosition = Offset(
              event.localPosition?.dx ?? 0,
              event.localPosition?.dy ?? 0,
            );
          });
          
          // Call callback if provided
          widget.onDataPointTap?.call(_hoveredDataPoint!);
        }
      }
    } else {
      setState(() {
        _hoveredDataPoint = null;
        _tooltipPosition = null;
      });
    }
  }

  // Helper methods
  String _getMeasurementTitle() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return 'Weight';
      case 'height':
      case 'length':
        return 'Height';
      case 'head_circumference':
      case 'head circumference':
        return 'Head Circumference';
      default:
        return 'Weight';
    }
  }

  String _getUnit() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return widget.isMetric ? 'kg' : 'lbs';
      case 'height':
      case 'length':
        return widget.isMetric ? 'cm' : 'in';
      case 'head_circumference':
      case 'head circumference':
        return widget.isMetric ? 'cm' : 'in';
      default:
        return widget.isMetric ? 'kg' : 'lbs';
    }
  }

  double _getMaxAgeForDateRange() {
    final timeRange = TimeRangeService.getTimeRangeById(widget.dateRange);
    if (timeRange != null) {
      return timeRange.maxAgeMonths;
    }
    
    // Fallback for legacy range IDs
    switch (widget.dateRange) {
      case '3 months':
        return 3.0;
      case '6 months':
        return 6.0;
      case '1 year':
        return 12.0;
      case '18 months':
        return 18.0;
      case '2 years':
        return 24.0;
      case '3 years':
        return 36.0;
      case '4 years':
        return 48.0;
      case '5 years':
        return 60.0;
      default:
        return 12.0;
    }
  }

  double _getMinY() {
    if (widget.measurements.isEmpty) {
      return _getDefaultMinY();
    }
    
    double minValue = double.infinity;
    for (final measurement in widget.measurements) {
      var value = (measurement['value'] as num).toDouble();
      if (!widget.isMetric) {
        value = _convertValueForDisplay(value);
      }
      minValue = minValue < value ? minValue : value;
    }
    
    // Add 10% padding below minimum
    final padding = minValue * 0.1;
    return (minValue - padding).clamp(0.0, double.infinity);
  }

  double _getMaxY() {
    if (widget.measurements.isEmpty) {
      return _getDefaultMaxY();
    }
    
    double maxValue = double.negativeInfinity;
    for (final measurement in widget.measurements) {
      var value = (measurement['value'] as num).toDouble();
      if (!widget.isMetric) {
        value = _convertValueForDisplay(value);
      }
      maxValue = maxValue > value ? maxValue : value;
    }
    
    // Add 10% padding above maximum
    final padding = maxValue * 0.1;
    return maxValue + padding;
  }

  double _getDefaultMinY() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return 0.0;
      case 'height':
      case 'length':
        return widget.isMetric ? 40.0 : 15.0;
      case 'head_circumference':
      case 'head circumference':
        return widget.isMetric ? 30.0 : 12.0;
      default:
        return 0.0;
    }
  }

  double _getDefaultMaxY() {
    final maxAge = _getMaxAgeForDateRange();
    
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        if (widget.isMetric) {
          return maxAge <= 12 ? 15.0 : 25.0;
        } else {
          return maxAge <= 12 ? 35.0 : 55.0;
        }
      case 'height':
      case 'length':
        if (widget.isMetric) {
          return maxAge <= 12 ? 85.0 : 120.0;
        } else {
          return maxAge <= 12 ? 35.0 : 48.0;
        }
      case 'head_circumference':
      case 'head circumference':
        if (widget.isMetric) {
          return maxAge <= 12 ? 50.0 : 60.0;
        } else {
          return maxAge <= 12 ? 20.0 : 24.0;
        }
      default:
        return 100.0;
    }
  }

  double _getHorizontalInterval() {
    final range = _getMaxY() - _getMinY();
    final targetIntervals = 6;
    final rawInterval = range / targetIntervals;
    
    // Round to nice numbers
    if (rawInterval <= 1) return 1;
    if (rawInterval <= 2) return 2;
    if (rawInterval <= 5) return 5;
    if (rawInterval <= 10) return 10;
    if (rawInterval <= 20) return 20;
    return 50;
  }

  double _getVerticalInterval() {
    final timeRange = TimeRangeService.getTimeRangeById(widget.dateRange);
    if (timeRange != null) {
      return timeRange.intervalMonths;
    }
    
    final maxAge = _getMaxAgeForDateRange();
    if (maxAge <= 6) return 1.0;
    if (maxAge <= 12) return 2.0;
    if (maxAge <= 24) return 3.0;
    return 6.0;
  }

  double _convertValueForDisplay(double value) {
    if (!widget.isMetric) {
      final result = UnitConversionService.convertWeight(
        value: value,
        fromUnit: 'kg',
        toUnit: widget.measurementType.toLowerCase() == 'weight' ? 'lbs' : 'in',
      );
      return result.value;
    }
    return value;
  }

  int _getDecimalPlaces() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return widget.isMetric ? 1 : 1;
      case 'height':
      case 'length':
      case 'head_circumference':
      case 'head circumference':
        return widget.isMetric ? 0 : 1;
      default:
        return 1;
    }
  }

  double _calculateAgeInMonths(DateTime date) {
    final difference = date.difference(widget.birthDate);
    return difference.inDays / 30.44;
  }

  String _getPercentileSuffix(int percentile) {
    if (percentile >= 11 && percentile <= 13) {
      return 'th';
    }
    switch (percentile % 10) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }


  List<Measurement> _convertMeasurementsToModels() {
    return widget.measurements.map((data) {
      return Measurement(
        id: data['id']?.toString() ?? '',
        babyId: '',
        measurementType: widget.measurementType,
        value: (data['value'] as num).toDouble(),
        unit: data['unit']?.toString() ?? '',
        measuredAt: data['date'] as DateTime,
        ageInMonths: _calculateAgeInMonths(data['date'] as DateTime),
        percentile: (data['percentile'] as num?)?.toDouble(),
        notes: data['notes']?.toString(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }).toList();
  }

  // Enhanced trend analysis for better visualization
  Map<String, dynamic> _analyzeTrend(List<FlSpot> spots) {
    if (spots.length < 2) {
      return {
        'direction': 'stable',
        'slope': 0.0,
        'velocity': 0.0,
        'consistency': 1.0,
      };
    }
    
    // Calculate overall trend using linear regression approach
    double sumX = 0, sumY = 0, sumXY = 0, sumX2 = 0;
    final n = spots.length;
    
    for (final spot in spots) {
      sumX += spot.x;
      sumY += spot.y;
      sumXY += spot.x * spot.y;
      sumX2 += spot.x * spot.x;
    }
    
    // Prevent division by zero
    final denominator = n * sumX2 - sumX * sumX;
    if (denominator == 0) {
      return {
        'direction': 'stable',
        'slope': 0.0,
        'velocity': 0.0,
        'consistency': 1.0,
      };
    }
    
    // Calculate slope (trend direction)
    final slope = (n * sumXY - sumX * sumY) / denominator;
    
    // Calculate velocity (rate of change)
    final velocity = slope.abs();
    
    // Calculate consistency (how well points fit the trend line)
    double sumSquaredErrors = 0;
    final meanY = sumY / n;
    final meanX = sumX / n;
    
    for (final spot in spots) {
      final predictedY = slope * spot.x + (meanY - slope * meanX);
      final error = spot.y - predictedY;
      sumSquaredErrors += error * error;
    }
    
    final consistency = 1.0 / (1.0 + sumSquaredErrors / n);
    
    String direction;
    if (slope > 0.1) {
      direction = 'increasing';
    } else if (slope < -0.1) {
      direction = 'decreasing';
    } else {
      direction = 'stable';
    }
    
    return {
      'direction': direction,
      'slope': slope,
      'velocity': velocity,
      'consistency': consistency,
    };
  }

  List<Color> _getTrendGradientColors(Map<String, dynamic> trendAnalysis) {
    final direction = trendAnalysis['direction'] as String;
    final consistency = trendAnalysis['consistency'] as double;
    
    switch (direction) {
      case 'increasing':
        return [
          const Color(0xFF059669).withValues(alpha: 0.8 + consistency * 0.2),
          const Color(0xFF0284C7).withValues(alpha: 0.6 + consistency * 0.2),
        ];
      case 'decreasing':
        return [
          const Color(0xFFEA580C).withValues(alpha: 0.8 + consistency * 0.2),
          const Color(0xFFDC2626).withValues(alpha: 0.6 + consistency * 0.2),
        ];
      default:
        return [
          Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
          Theme.of(context).colorScheme.primary.withValues(alpha: 0.6),
        ];
    }
  }

  List<Color> _getTrendAreaGradientColors(Map<String, dynamic> trendAnalysis) {
    final direction = trendAnalysis['direction'] as String;
    final consistency = trendAnalysis['consistency'] as double;
    
    switch (direction) {
      case 'increasing':
        return [
          const Color(0xFF059669).withValues(alpha: 0.15 * consistency),
          const Color(0xFF059669).withValues(alpha: 0.05 * consistency),
        ];
      case 'decreasing':
        return [
          const Color(0xFFEA580C).withValues(alpha: 0.15 * consistency),
          const Color(0xFFDC2626).withValues(alpha: 0.05 * consistency),
        ];
      default:
        return [
          Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        ];
    }
  }
}