import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../services/growth_analyzer.dart';

/// Widget to display comprehensive growth analysis and alerts
class GrowthAnalysisWidget extends StatefulWidget {
  final GrowthAnalysis analysis;
  final VoidCallback? onRefresh;
  final Function(GrowthAlert)? onAlertTap;

  const GrowthAnalysisWidget({
    Key? key,
    required this.analysis,
    this.onRefresh,
    this.onAlertTap,
  }) : super(key: key);

  @override
  State<GrowthAnalysisWidget> createState() => _GrowthAnalysisWidgetState();
}

class _GrowthAnalysisWidgetState extends State<GrowthAnalysisWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  bool _showAllAlerts = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: Container(
        margin: EdgeInsets.all(2.w),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            _buildOverallAssessment(),
            if (widget.analysis.growthSummary.isNotEmpty) _buildGrowthSummary(),
            if (widget.analysis.alerts.isNotEmpty) _buildAlertsSection(),
            if (widget.analysis.recommendations.isNotEmpty) _buildRecommendations(),
            _buildAnalysisDetails(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withOpacity(0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.analytics_outlined,
            color: Theme.of(context).primaryColor,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Growth Analysis',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).primaryColor,
                  ),
                ),
                Text(
                  'Analyzed ${_formatDate(widget.analysis.analyzedAt)}',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Theme.of(context).textTheme.bodySmall?.color,
                  ),
                ),
              ],
            ),
          ),
          if (widget.onRefresh != null)
            IconButton(
              onPressed: widget.onRefresh,
              icon: Icon(
                Icons.refresh,
                color: Theme.of(context).primaryColor,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildOverallAssessment() {
    final severity = widget.analysis.highestSeverity;
    Color assessmentColor;
    IconData assessmentIcon;

    switch (severity) {
      case 'critical':
        assessmentColor = Colors.red;
        assessmentIcon = Icons.warning;
        break;
      case 'high':
        assessmentColor = Colors.orange;
        assessmentIcon = Icons.priority_high;
        break;
      case 'medium':
        assessmentColor = Colors.amber;
        assessmentIcon = Icons.info;
        break;
      case 'normal':
      default:
        assessmentColor = Colors.green;
        assessmentIcon = Icons.check_circle;
        break;
    }

    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: assessmentColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: assessmentColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(
            assessmentIcon,
            color: assessmentColor,
            size: 5.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Overall Assessment',
                  style: TextStyle(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: assessmentColor,
                  ),
                ),
                SizedBox(height: 1.w),
                Text(
                  widget.analysis.overallAssessment,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGrowthSummary() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).dividerColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: Theme.of(context).primaryColor,
                size: 4.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Growth Summary',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).primaryColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.w),
          Text(
            widget.analysis.growthSummary,
            style: TextStyle(
              fontSize: 13.sp,
              color: Theme.of(context).textTheme.bodyMedium?.color,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsSection() {
    final alertsToShow = _showAllAlerts 
        ? widget.analysis.alerts 
        : widget.analysis.alerts.take(3).toList();

    return Container(
      margin: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.notifications_outlined,
                    color: Theme.of(context).primaryColor,
                    size: 4.w,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    'Alerts (${widget.analysis.alerts.length})',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ],
              ),
              if (widget.analysis.alerts.length > 3)
                TextButton(
                  onPressed: () {
                    setState(() {
                      _showAllAlerts = !_showAllAlerts;
                    });
                  },
                  child: Text(
                    _showAllAlerts ? 'Show Less' : 'Show All',
                    style: TextStyle(fontSize: 12.sp),
                  ),
                ),
            ],
          ),
          SizedBox(height: 2.w),
          ...alertsToShow.map((alert) => _buildAlertCard(alert)).toList(),
        ],
      ),
    );
  }

  Widget _buildAlertCard(GrowthAlert alert) {
    Color alertColor;
    IconData alertIcon;

    switch (alert.severity) {
      case 'critical':
        alertColor = Colors.red;
        alertIcon = Icons.error;
        break;
      case 'high':
        alertColor = Colors.orange;
        alertIcon = Icons.warning;
        break;
      case 'medium':
        alertColor = Colors.amber;
        alertIcon = Icons.info;
        break;
      case 'low':
      default:
        alertColor = Colors.blue;
        alertIcon = Icons.info_outline;
        break;
    }

    return Container(
      margin: EdgeInsets.only(bottom: 2.w),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => widget.onAlertTap?.call(alert),
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: alertColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: alertColor.withOpacity(0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      alertIcon,
                      color: alertColor,
                      size: 4.w,
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        alert.title,
                        style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w600,
                          color: alertColor,
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.w),
                      decoration: BoxDecoration(
                        color: alertColor,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        alert.severity.toUpperCase(),
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.w),
                Text(
                  alert.description,
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Theme.of(context).textTheme.bodyMedium?.color,
                    height: 1.3,
                  ),
                ),
                if (alert.recommendations.isNotEmpty) ...[
                  SizedBox(height: 2.w),
                  Text(
                    'Recommendations:',
                    style: TextStyle(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                  SizedBox(height: 1.w),
                  ...alert.recommendations.take(2).map((rec) => Padding(
                    padding: EdgeInsets.only(left: 3.w, bottom: 1.w),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '• ',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: alertColor,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            rec,
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Theme.of(context).textTheme.bodySmall?.color,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )).toList(),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRecommendations() {
    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.green.withOpacity(0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.green.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: Colors.green,
                size: 4.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Recommendations',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: Colors.green,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.w),
          ...widget.analysis.recommendations.take(5).map((rec) => Padding(
            padding: EdgeInsets.only(bottom: 1.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '• ',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: Colors.green,
                  ),
                ),
                Expanded(
                  child: Text(
                    rec,
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Theme.of(context).textTheme.bodyMedium?.color,
                    ),
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildAnalysisDetails() {
    return Container(
      margin: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Analysis Details',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
            ),
          ),
          SizedBox(height: 2.w),
          _buildDetailRow('Measurements Analyzed', '${widget.analysis.trends.length}'),
          if (widget.analysis.velocityAnalysis != null) ...[
            _buildDetailRow(
              'Growth Velocity',
              '${widget.analysis.velocityAnalysis!.velocityCategory} (${widget.analysis.velocityAnalysis!.velocityPercentile.toStringAsFixed(1)}th percentile)',
            ),
            _buildDetailRow(
              'Velocity Period',
              '${widget.analysis.velocityAnalysis!.timePeriod.inDays} days',
            ),
          ],
          _buildDetailRow('Alert Count', '${widget.analysis.alerts.length}'),
          if (widget.analysis.percentileCrossingAnalysis.isNotEmpty)
            _buildDetailRow(
              'Percentile Trend',
              widget.analysis.percentileCrossingAnalysis['crossingDirection'] ?? 'Unknown',
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              color: Theme.of(context).textTheme.bodySmall?.color,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).textTheme.bodyMedium?.color,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Compact growth analysis summary widget for dashboard use
class GrowthAnalysisSummaryWidget extends StatelessWidget {
  final GrowthAnalysis analysis;
  final VoidCallback? onTap;

  const GrowthAnalysisSummaryWidget({
    Key? key,
    required this.analysis,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final alertCounts = analysis.alertCountBySeverity;
    final hasUrgentAlerts = analysis.hasUrgentAlerts;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: hasUrgentAlerts 
                  ? Colors.orange.withOpacity(0.3)
                  : Theme.of(context).dividerColor.withOpacity(0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.analytics_outlined,
                    color: Theme.of(context).primaryColor,
                    size: 5.w,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      'Growth Analysis',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                  if (hasUrgentAlerts)
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.w),
                      decoration: BoxDecoration(
                        color: Colors.orange,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        'ATTENTION',
                        style: TextStyle(
                          fontSize: 10.sp,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                    ),
                ],
              ),
              SizedBox(height: 2.w),
              Text(
                analysis.overallAssessment,
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).textTheme.bodyMedium?.color,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              if (analysis.alerts.isNotEmpty) ...[
                SizedBox(height: 2.w),
                Row(
                  children: [
                    if (alertCounts['critical']! > 0) _buildAlertBadge('Critical', alertCounts['critical']!, Colors.red),
                    if (alertCounts['high']! > 0) _buildAlertBadge('High', alertCounts['high']!, Colors.orange),
                    if (alertCounts['medium']! > 0) _buildAlertBadge('Medium', alertCounts['medium']!, Colors.amber),
                    if (alertCounts['low']! > 0) _buildAlertBadge('Low', alertCounts['low']!, Colors.blue),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAlertBadge(String label, int count, Color color) {
    return Container(
      margin: EdgeInsets.only(right: 2.w),
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        '$count $label',
        style: TextStyle(
          fontSize: 10.sp,
          fontWeight: FontWeight.w500,
          color: color,
        ),
      ),
    );
  }
}