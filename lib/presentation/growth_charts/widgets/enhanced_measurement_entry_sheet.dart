import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/measurement.dart';
import '../../../services/enhanced_measurement_service.dart';
import '../../../services/measurement_validation_service.dart';
import '../../../widgets/modern_date_time_picker.dart';

/// Enhanced measurement entry sheet with validation and percentile calculation
class EnhancedMeasurementEntrySheet extends StatefulWidget {
  final String title;
  final String subtitle;
  final int measurementType;
  final bool isMetric;
  final BabyProfile babyProfile;
  final Measurement? existingMeasurement;
  final Future<bool> Function(Measurement) onSave;

  const EnhancedMeasurementEntrySheet({
    super.key,
    required this.title,
    required this.subtitle,
    required this.measurementType,
    required this.isMetric,
    required this.babyProfile,
    required this.onSave,
    this.existingMeasurement,
  });

  @override
  State<EnhancedMeasurementEntrySheet> createState() => _EnhancedMeasurementEntrySheetState();
}

class _EnhancedMeasurementEntrySheetState extends State<EnhancedMeasurementEntrySheet>
    with TickerProviderStateMixin {
  final TextEditingController _valueController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final EnhancedMeasurementService _measurementService = EnhancedMeasurementService();

  late String _selectedUnit;
  DateTime _selectedDate = DateTime.now();
  TimeOfDay _selectedTime = TimeOfDay.now();
  
  // Enhanced validation and percentile calculation
  MeasurementValidationResult? _validationResult;
  double? _calculatedPercentile;
  String _percentileCategory = '';
  bool _isCalculating = false;
  bool _hasValidationWarnings = false;
  bool _isLoading = false;

  late AnimationController _animationController;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _selectedUnit = _getDefaultUnit();
    _valueController.addListener(_onValueChanged);

    // Initialize with existing measurement data if editing
    if (widget.existingMeasurement != null) {
      _initializeWithExistingMeasurement();
    }

    _animationController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeIn,
    ));

    _animationController.forward();
  }

  void _initializeWithExistingMeasurement() {
    final measurement = widget.existingMeasurement!;
    _valueController.text = measurement.value.toString();
    _selectedUnit = measurement.unit;
    _selectedDate = DateTime(
      measurement.measuredAt.year,
      measurement.measuredAt.month,
      measurement.measuredAt.day,
    );
    _selectedTime = TimeOfDay.fromDateTime(measurement.measuredAt);
    _notesController.text = measurement.notes ?? '';
    
    // Set initial percentile if available
    if (measurement.percentile != null) {
      _calculatedPercentile = measurement.percentile;
      _percentileCategory = _getPercentileCategory(measurement.percentile!);
    }
  }

  @override
  void dispose() {
    _valueController.removeListener(_onValueChanged);
    _valueController.dispose();
    _notesController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _onValueChanged() {
    if (_valueController.text.isNotEmpty) {
      final value = double.tryParse(_valueController.text);
      if (value != null) {
        _validateAndCalculatePercentile(value);
      }
    } else {
      setState(() {
        _calculatedPercentile = null;
        _percentileCategory = '';
        _validationResult = null;
        _hasValidationWarnings = false;
      });
    }
  }

  void _validateAndCalculatePercentile(double value) async {
    setState(() {
      _isCalculating = true;
    });

    try {
      final ageInMonths = _getAgeInMonths(_selectedDate);
      
      // Validate measurement using enhanced validation service
      _validationResult = await _measurementService.validateMeasurementData(
        value: value,
        ageInMonths: ageInMonths,
        measurementType: _getMeasurementTypeString(),
        babyProfile: widget.babyProfile,
        measurementDate: _getCombinedDateTime(),
        measurementId: widget.existingMeasurement?.id,
      );

      // Calculate percentile (this will be done automatically by the validation)
      if (_validationResult != null && _validationResult!.metadata['percentile'] != null) {
        _calculatedPercentile = _validationResult!.metadata['percentile'] as double;
        _percentileCategory = _getPercentileCategory(_calculatedPercentile!);
      }

      setState(() {
        _hasValidationWarnings = _validationResult?.hasWarnings ?? false;
        _isCalculating = false;
      });
    } catch (e) {
      debugPrint('Error validating measurement: $e');
      setState(() {
        _isCalculating = false;
      });
    }
  }

  double _getAgeInMonths(DateTime date) {
    final difference = date.difference(widget.babyProfile.birthDate);
    return difference.inDays / 30.44;
  }

  DateTime _getCombinedDateTime() {
    return DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
      _selectedTime.hour,
      _selectedTime.minute,
    );
  }

  String _getMeasurementTypeString() {
    switch (widget.measurementType) {
      case 0:
        return 'weight';
      case 1:
        return 'height';
      case 2:
        return 'head_circumference';
      default:
        return 'weight';
    }
  }

  String _getPercentileCategory(double percentile) {
    if (percentile < 3) return 'Below 3rd percentile - May need attention';
    if (percentile < 10) return 'Below 10th percentile - Monitor closely';
    if (percentile < 25) return 'Below average range';
    if (percentile < 75) return 'Normal range';
    if (percentile < 90) return 'Above average range';
    if (percentile < 97) return 'Above 90th percentile - Monitor closely';
    return 'Above 97th percentile - May need attention';
  }

  Color _getPercentileColor(double percentile) {
    if (percentile < 3 || percentile > 97) return Colors.red;
    if (percentile < 10 || percentile > 90) return Colors.orange;
    if (percentile < 25 || percentile > 75) return Colors.amber;
    return Colors.green;
  }

  String _getDefaultUnit() {
    switch (widget.measurementType) {
      case 0: // Weight
        return widget.isMetric ? 'kg' : 'lbs';
      case 1: // Height
        return widget.isMetric ? 'cm' : 'in';
      case 2: // Head Circumference
        return widget.isMetric ? 'cm' : 'in';
      default:
        return 'kg';
    }
  }

  String _getMeasurementTitle() {
    switch (widget.measurementType) {
      case 0:
        return 'Weight';
      case 1:
        return 'Height';
      case 2:
        return 'Head Circumference';
      default:
        return 'Weight';
    }
  }

  List<String> _getUnitOptions() {
    switch (widget.measurementType) {
      case 0: // Weight
        return widget.isMetric ? ['kg', 'g'] : ['lbs', 'oz'];
      case 1: // Height
        return widget.isMetric ? ['cm', 'm'] : ['in', 'ft'];
      case 2: // Head Circumference
        return widget.isMetric ? ['cm', 'mm'] : ['in'];
      default:
        return ['kg'];
    }
  }

  String? _validateInput(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a value';
    }

    final numValue = double.tryParse(value);
    if (numValue == null) {
      return 'Please enter a valid number';
    }

    // Enhanced validation will be handled by the validation service
    // Basic range validation here
    switch (widget.measurementType) {
      case 0: // Weight
        if (widget.isMetric) {
          if (numValue < 0.5 || numValue > 50) {
            return 'Weight should be between 0.5 and 50 kg';
          }
        } else {
          if (numValue < 1 || numValue > 110) {
            return 'Weight should be between 1 and 110 lbs';
          }
        }
        break;
      case 1: // Height
        if (widget.isMetric) {
          if (numValue < 30 || numValue > 200) {
            return 'Height should be between 30 and 200 cm';
          }
        } else {
          if (numValue < 12 || numValue > 78) {
            return 'Height should be between 12 and 78 inches';
          }
        }
        break;
      case 2: // Head circumference
        if (widget.isMetric) {
          if (numValue < 25 || numValue > 70) {
            return 'Head circumference should be between 25 and 70 cm';
          }
        } else {
          if (numValue < 10 || numValue > 28) {
            return 'Head circumference should be between 10 and 28 inches';
          }
        }
        break;
    }

    return null;
  }

  void _selectDateTime() async {
    final DateTime currentDateTime = _getCombinedDateTime();
    
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: currentDateTime,
      firstDate: widget.babyProfile.birthDate,
      lastDate: DateTime.now(),
    );
    
    if (picked != null && picked != currentDateTime) {
      setState(() {
        _selectedDate = DateTime(picked.year, picked.month, picked.day);
        _selectedTime = TimeOfDay(hour: picked.hour, minute: picked.minute);
      });
      
      // Recalculate percentile with new date
      if (_valueController.text.isNotEmpty) {
        final value = double.tryParse(_valueController.text);
        if (value != null) {
          _validateAndCalculatePercentile(value);
        }
      }
    }
  }

  void _handleSave() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      
      try {
        final value = double.parse(_valueController.text);
        final notes = _notesController.text.trim();
        final measurementDateTime = _getCombinedDateTime();
        
        // Create measurement object for callback
        final measurement = Measurement(
          id: widget.existingMeasurement?.id ?? '',
          babyId: widget.babyProfile.id,
          measurementType: _getMeasurementTypeString(),
          value: value,
          unit: _selectedUnit,
          measuredAt: measurementDateTime,
          ageInMonths: _getAgeInMonths(_selectedDate),
          notes: notes.isNotEmpty ? notes : null,
          percentile: _calculatedPercentile,
          createdAt: widget.existingMeasurement?.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        final bool success = await widget.onSave(measurement);
        
        setState(() => _isLoading = false);
        Navigator.pop(context, success);
      } catch (e) {
        setState(() => _isLoading = false);
        debugPrint('Error saving measurement: $e');
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Error saving measurement: $e'),
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(
              0, MediaQuery.of(context).size.height * _slideAnimation.value),
          child: Opacity(
            opacity: _fadeAnimation.value,
            child: Container(
              height: 90.h,
              decoration: BoxDecoration(
                color: ThemeAwareColors.getCardColor(context),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: ThemeAwareColors.getShadowColor(context),
                    blurRadius: 20,
                    offset: Offset(0, -5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Handle and Header
                  _buildHeader(),

                  // Content
                  Expanded(
                    child: Form(
                      key: _formKey,
                      child: SingleChildScrollView(
                        padding: EdgeInsets.all(6.w),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Date and Time Selection
                            _buildDateTimeSection(),

                            SizedBox(height: 4.h),

                            // Value Input with Enhanced Validation
                            _buildValueInputSection(),

                            SizedBox(height: 3.h),

                            // Enhanced Percentile Preview with Validation
                            if (_calculatedPercentile != null || _isCalculating)
                              _buildEnhancedPercentilePreview(),

                            if (_calculatedPercentile != null || _isCalculating)
                              SizedBox(height: 3.h),

                            // Validation Warnings
                            if (_hasValidationWarnings && _validationResult != null)
                              _buildValidationWarnings(),

                            if (_hasValidationWarnings && _validationResult != null)
                              SizedBox(height: 3.h),

                            // Notes Section
                            _buildNotesSection(),

                            SizedBox(height: 4.h),
                          ],
                        ),
                      ),
                    ),
                  ),

                  // Save Button
                  _buildSaveButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 12.w,
            height: 0.5.h,
            decoration: BoxDecoration(
              color: ThemeAwareColors.getDividerColor(context),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          SizedBox(height: 2.h),

          // Header content
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: CustomIconWidget(
                  iconName: widget.measurementType == 0
                      ? 'monitor_weight'
                      : widget.measurementType == 1
                          ? 'height'
                          : 'face',
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.title,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                    Text(
                      widget.subtitle,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: CustomIconWidget(
                  iconName: 'close',
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  size: 24,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDateTimeSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'When was this measured?',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        GestureDetector(
          onTap: _selectDateTime,
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSurfaceColor(context),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                width: 1.5,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'event',
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date & Time',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                      ),
                      Text(
                        '${_selectedDate.month}/${_selectedDate.day}/${_selectedDate.year} at ${_selectedTime.format(context)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  size: 20,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildValueInputSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${_getMeasurementTitle()} Measurement',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Value input
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _valueController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                validator: _validateInput,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                ],
                decoration: InputDecoration(
                  labelText: 'Enter value',
                  hintText: 'e.g., 7.5',
                  prefixIcon: Padding(
                    padding: EdgeInsets.all(3.w),
                    child: CustomIconWidget(
                      iconName: widget.measurementType == 0
                          ? 'monitor_weight'
                          : widget.measurementType == 1
                              ? 'height'
                              : 'face',
                      color: Theme.of(context).primaryColor,
                      size: 20,
                    ),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).primaryColor,
                      width: 2,
                    ),
                  ),
                  errorBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Colors.red,
                      width: 1.5,
                    ),
                  ),
                ),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
            ),

            SizedBox(width: 3.w),

            // Unit selector
            Expanded(
              child: Container(
                height: 7.h,
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getSurfaceColor(context),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
                    width: 1.5,
                  ),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: _selectedUnit,
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedUnit = value;
                        });
                        // Recalculate percentile with new unit
                        if (_valueController.text.isNotEmpty) {
                          final inputValue =
                              double.tryParse(_valueController.text);
                          if (inputValue != null) {
                            _validateAndCalculatePercentile(inputValue);
                          }
                        }
                      }
                    },
                    items: _getUnitOptions().map((unit) {
                      return DropdownMenuItem<String>(
                        value: unit,
                        child: Center(
                          child: Text(
                            unit,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: ThemeAwareColors.getPrimaryTextColor(context),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                    icon: Padding(
                      padding: EdgeInsets.only(right: 2.w),
                      child: CustomIconWidget(
                        iconName: 'keyboard_arrow_down',
                        color: Theme.of(context).primaryColor,
                        size: 20,
                      ),
                    ),
                    dropdownColor: ThemeAwareColors.getCardColor(context),
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEnhancedPercentilePreview() {
    return AnimatedContainer(
      duration: Duration(milliseconds: 300),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: _isCalculating
            ? ThemeAwareColors.getSurfaceColor(context)
            : _getPercentileColor(_calculatedPercentile!)
                .withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _isCalculating
              ? ThemeAwareColors.getDividerColor(context)
              : _getPercentileColor(_calculatedPercentile!)
                  .withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: _isCalculating
          ? Row(
              children: [
                SizedBox(
                  width: 5.w,
                  height: 5.w,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation(
                      Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Text(
                  'Calculating percentile and validating...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
              ],
            )
          : Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getPercentileColor(_calculatedPercentile!)
                            .withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: 'analytics',
                        color: _getPercentileColor(_calculatedPercentile!),
                        size: 20,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'WHO Percentile',
                            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: _getPercentileColor(_calculatedPercentile!),
                            ),
                          ),
                          Text(
                            '${_calculatedPercentile!.toStringAsFixed(1)}th percentile',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: _getPercentileColor(_calculatedPercentile!),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Text(
                  _percentileCategory,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
    );
  }

  Widget _buildValidationWarnings() {
    if (_validationResult == null || !_validationResult!.hasWarnings) {
      return SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.orange.withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Validation Warnings',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.orange,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          ...(_validationResult!.warnings ?? []).map((warning) => Padding(
            padding: EdgeInsets.only(bottom: 0.5.h),
            child: Text(
              '• $warning',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
          )),
        ],
      ),
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any notes about this measurement...',
            hintStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
            ),
            prefixIcon: Padding(
              padding: EdgeInsets.all(3.w),
              child: CustomIconWidget(
                iconName: 'note',
                color: Theme.of(context).primaryColor,
                size: 20,
              ),
            ),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor.withValues(alpha: 0.5),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
            ),
          ),
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 10,
            offset: Offset(0, -2),
          ),
        ],
      ),
      child: SizedBox(
        width: double.infinity,
        height: 6.h,
        child: ElevatedButton(
          onPressed: _isLoading ? null : _handleSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.primary,
            foregroundColor: Theme.of(context).colorScheme.onPrimary,
            elevation: 2,
            shadowColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
          ),
          child: _isLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation(
                      Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                )
              : Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'save',
                      color: Theme.of(context).colorScheme.onPrimary,
                      size: 20,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      widget.existingMeasurement != null ? 'Update Measurement' : 'Save Measurement',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onPrimary,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }
}