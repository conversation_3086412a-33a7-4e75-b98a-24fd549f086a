import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../core/app_export.dart';
import '../../models/measurement.dart';
import '../../services/who_data_service.dart';
import '../../services/unit_conversion_service.dart';
import '../../services/time_range_service.dart';
import '../../services/responsive_chart_service.dart';
import '../../services/offline_who_data_cache.dart';
import '../../services/enhanced_measurement_service.dart';
import '../../services/growth_analyzer.dart';
import '../../services/data_export_service.dart';
import './widgets/chart_toolbar_widget.dart';
import './widgets/enhanced_growth_chart_renderer.dart';
import './widgets/growth_analysis_widget.dart';
import './widgets/enhanced_measurement_entry_sheet.dart';
import './widgets/measurement_selector_widget.dart';
import './widgets/recent_measurements_widget.dart';

class GrowthCharts extends StatefulWidget {
  final BabyProfile babyProfile;

  const GrowthCharts({super.key, required this.babyProfile});

  @override
  State<GrowthCharts> createState() => _GrowthChartsState();
}

class _GrowthChartsState extends State<GrowthCharts>
    with TickerProviderStateMixin {
  static const String _selectedTypeKey = 'selected_measurement_type';
  int _selectedMeasurementType =
      0; // 0: Weight, 1: Height, 2: Head Circumference
  String _selectedDateRange = '1 year';
  bool _isMetric = true;
  late AnimationController _animationController;
  bool _isLoading = false;
  bool _isChartLoading = false; // Add separate loading state for chart
  final ScrollController _scrollController = ScrollController();

  // Enhanced services
  final EnhancedMeasurementService _measurementService = EnhancedMeasurementService();

  // Real data loaded from Supabase  
  DateTime get _babyBirthDate => widget.babyProfile.birthDate;
  
  List<Measurement> _measurements = [];
  List<Measurement> _weightMeasurements = [];
  List<Measurement> _heightMeasurements = [];
  List<Measurement> _headCircumferenceMeasurements = [];

  List<Measurement> get _getCurrentMeasurements {
    switch (_selectedMeasurementType) {
      case 0: return _weightMeasurements;
      case 1: return _heightMeasurements;
      case 2: return _headCircumferenceMeasurements;
      default: return _weightMeasurements;
    }
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _initializeServices();
    _loadSavedState();
    _loadGrowthMeasurements();
  }

  /// Initialize enhanced services
  Future<void> _initializeServices() async {
    try {
      // Initialize offline WHO data cache
      await OfflineWHODataCache.initializeCache();
    } catch (e) {
      debugPrint('Failed to initialize offline cache: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _loadSavedState() async {
    final prefs = await SharedPreferences.getInstance();
    final savedType = prefs.getInt(_selectedTypeKey);
    if (savedType != null && mounted) {
      setState(() => _selectedMeasurementType = savedType);
    }
  }

  Future<void> _saveSelectedType(int type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(_selectedTypeKey, type);
    if (mounted) {
      setState(() => _selectedMeasurementType = type);
    }
  }

  Future<void> _loadGrowthMeasurements() async {
    setState(() => _isLoading = true);
    try {
      // Load measurements using enhanced measurement service
      _measurements = await _measurementService.getMeasurements(widget.babyProfile.id);
      
      // Group measurements by type
      _weightMeasurements = [];
      _heightMeasurements = [];
      _headCircumferenceMeasurements = [];

      for (final measurement in _measurements) {
        switch (measurement.measurementType) {
          case 'weight':
            _weightMeasurements.add(measurement);
            break;
          case 'height':
          case 'length':
            _heightMeasurements.add(measurement);
            break;
          case 'head_circumference':
            _headCircumferenceMeasurements.add(measurement);
            break;
        }
      }

      // Add birth measurements if they don't exist
      await _addBirthMeasurementsIfNeeded();

      if (mounted) {
        setState(() => _isLoading = false);
        _animationController.forward();
      }
    } catch (e) {
      debugPrint('Error loading growth measurements: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Add birth measurements if they don't exist and birth data is available
  Future<void> _addBirthMeasurementsIfNeeded() async {
    try {
      // Check if birth weight measurement exists
      if (widget.babyProfile.birthWeight != null && 
          !_weightMeasurements.any((m) => m.ageInMonths == 0.0)) {
        final birthWeightMeasurement = await _measurementService.saveMeasurement(
          babyId: widget.babyProfile.id,
          measurementType: 'weight',
          value: widget.babyProfile.birthWeight!,
          unit: 'kg',
          measuredAt: widget.babyProfile.birthDate,
          babyProfile: widget.babyProfile,
          notes: 'Birth Weight',
        );
        _weightMeasurements.insert(0, birthWeightMeasurement);
      }

      // Check if birth height measurement exists
      if (widget.babyProfile.birthHeight != null && 
          !_heightMeasurements.any((m) => m.ageInMonths == 0.0)) {
        final birthHeightMeasurement = await _measurementService.saveMeasurement(
          babyId: widget.babyProfile.id,
          measurementType: 'height',
          value: widget.babyProfile.birthHeight!,
          unit: 'cm',
          measuredAt: widget.babyProfile.birthDate,
          babyProfile: widget.babyProfile,
          notes: 'Birth Height',
        );
        _heightMeasurements.insert(0, birthHeightMeasurement);
      }
    } catch (e) {
      debugPrint('Error adding birth measurements: $e');
    }
  }

  double _calculateAgeInMonths(DateTime measurementDate) {
    final difference = measurementDate.difference(widget.babyProfile.birthDate);
    return difference.inDays / 30.44;
  }

  List<Measurement> get _allMeasurements {
    final all = <Measurement>[];
    
    // Add all measurements
    all.addAll(_weightMeasurements);
    all.addAll(_heightMeasurements);
    all.addAll(_headCircumferenceMeasurements);
    
    // Sort by date (most recent first)
    all.sort((a, b) => b.measuredAt.compareTo(a.measuredAt));
    
    return all;
  }

  Future<void> _showMeasurementEntrySheet() async {
    String title = _selectedMeasurementType == 0 
        ? 'Weight' 
        : _selectedMeasurementType == 1 
            ? 'Height' 
            : 'Head Circumference';
            
    final bool? success = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EnhancedMeasurementEntrySheet(
        title: 'Add $title',
        subtitle: 'Track your baby\'s growth with WHO percentiles',
        measurementType: _selectedMeasurementType,
        isMetric: _isMetric,
        babyProfile: widget.babyProfile,
        onSave: (measurement) async {
          try {
            await _measurementService.saveMeasurement(
              babyId: widget.babyProfile.id,
              measurementType: measurement.measurementType,
              value: measurement.value,
              unit: measurement.unit,
              measuredAt: measurement.measuredAt,
              babyProfile: widget.babyProfile,
              notes: measurement.notes,
            );
            
            await _loadGrowthMeasurements();
            return true;
          } catch (e) {
            debugPrint('Error saving measurement: $e');
            return false;
          }
        },
      ),
    );

    if (success != null && mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Measurement saved successfully'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving measurement'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _editMeasurement(int index) async {
    final measurement = _allMeasurements[index];
    
    String title = measurement.measurementType == 'weight' 
        ? 'Weight' 
        : measurement.measurementType == 'height' 
            ? 'Height' 
            : 'Head Circumference';
            
    final bool? success = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => EnhancedMeasurementEntrySheet(
        title: 'Edit $title',
        subtitle: 'Update your baby\'s growth measurement',
        measurementType: _getMeasurementTypeIndex(measurement.measurementType),
        isMetric: _isMetric,
        babyProfile: widget.babyProfile,
        existingMeasurement: measurement,
        onSave: (updatedMeasurement) async {
          try {
            await _measurementService.updateMeasurement(
              measurementId: measurement.id,
              babyId: widget.babyProfile.id,
              measurementType: updatedMeasurement.measurementType,
              value: updatedMeasurement.value,
              unit: updatedMeasurement.unit,
              measuredAt: updatedMeasurement.measuredAt,
              babyProfile: widget.babyProfile,
              notes: updatedMeasurement.notes,
            );
            
            await _loadGrowthMeasurements();
            return true;
          } catch (e) {
            debugPrint('Error updating measurement: $e');
            return false;
          }
        },
      ),
    );

    if (success != null && mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Measurement updated successfully'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating measurement'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
  
  String _normalizeGender(String gender) {
    final lowercaseGender = gender.toLowerCase();
    if (lowercaseGender == 'male' || lowercaseGender == 'boy' || lowercaseGender == 'boys') {
      return 'boys';
    } else if (lowercaseGender == 'female' || lowercaseGender == 'girl' || lowercaseGender == 'girls') {
      return 'girls';
    }
    // Default to boys if gender is unclear
    return 'boys';
  }

  String _getMeasurementTypeString(int type) {
    switch (type) {
      case 0:
        return 'weight';
      case 1:
        return 'height';
      case 2:
        return 'head_circumference';
      default:
        return 'weight';
    }
  }

  int _getMeasurementTypeIndex(String measurementType) {
    switch (measurementType) {
      case 'weight':
        return 0;
      case 'height':
      case 'length':
        return 1;
      case 'head_circumference':
        return 2;
      default:
        return 0;
    }
  }

  Future<void> _deleteMeasurement(int index) async {
    try {
      final measurement = _allMeasurements[index];
      
      // Use enhanced measurement service to delete
      await _measurementService.deleteMeasurement(measurement.id);
      await _loadGrowthMeasurements();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Measurement deleted'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error deleting measurement: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to delete measurement'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  double _convertToMetric(double value, String unit) {
    switch (unit) {
      case 'lbs':
        return value * 0.45359237; // Convert pounds to kg
      case 'oz':
        return value * 0.0283495; // Convert ounces to kg
      case 'in':
        return value * 2.54; // Convert inches to cm
      case 'ft':
        return value * 30.48; // Convert feet to cm
      default:
        return value;
    }
  }

  String _getMetricUnit() {
    switch (_selectedMeasurementType) {
      case 0: // Weight
        return 'kg';
      case 1: // Height
        return 'cm';
      case 2: // Head Circumference
        return 'cm';
      default:
        return 'kg';
    }
  }  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Growth Charts',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              Text(
                'for ${widget.babyProfile.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              if (mounted && Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                Navigator.pushReplacementNamed(context, '/main-navigation');
              }
            },
          ),
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Loading growth data...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
        appBar: AppBar(
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Growth Charts',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              Text(
                'for ${widget.babyProfile.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              if (mounted && Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                Navigator.pushReplacementNamed(context, '/main-navigation');
              }
            },
          ),
          actions: [
          IconButton(
            onPressed: () async {
              setState(() => _isMetric = !_isMetric);
            },
            icon: Container(
              padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _isMetric ? 'metric' : 'imperial',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          SizedBox(width: 2.w),
        ],
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // Chart Section
              Container(
                margin: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).cardColor,
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: ThemeAwareColors.getShadowColor(context),
                      blurRadius: 10,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Chart Toolbar
                    ChartToolbarWidget(
                      selectedDateRange: _selectedDateRange,
                      isMetric: _isMetric,
                      onDateRangeChanged: (range) {
                        setState(() => _selectedDateRange = range);
                      },
                      onUnitToggle: (metric) {
                        setState(() => _isMetric = metric);
                      },
                    ),

                    // Enhanced Measurement Type Selector
                    Container(
                      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.w),
                      child: MeasurementSelectorWidget(
                        selectedIndex: _selectedMeasurementType,
                        onChanged: (index) {
                          _saveSelectedType(index);
                        },
                      ),
                    ),

                    // Enhanced Chart Area with Loading States and Error Handling
                    _buildEnhancedChartArea(),
                  ],
                ),
              ),

              // Recent Measurements Section
              Container(
                margin: EdgeInsets.fromLTRB(4.w, 0, 4.w, 4.w),
                child: RecentMeasurementsWidget(
                  measurements: _getCurrentMeasurements.map((m) => m.toJson()).toList(),
                  measurementType: _selectedMeasurementType,
                  isMetric: _isMetric,
                  onDelete: _deleteMeasurement,
                  onEdit: _editMeasurement,
                ),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showMeasurementEntrySheet,
        icon: CustomIconWidget(
          iconName: 'add',
          color: Theme.of(context).colorScheme.onPrimary,
          size: 24,
        ),
        label: Text(
          'Add Measurement',
          style: Theme.of(context).textTheme.labelLarge?.copyWith(
            color: Theme.of(context).colorScheme.onPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.primary,
      ),
    );
  }  Widget _buildEmptyChartState() {
    return Container(
      padding: EdgeInsets.all(4.w),
      height: 300,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'trending_up',
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            size: 64,
          ),
          SizedBox(height: 2.h),
          Text(
            'No Growth Data Yet',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Start tracking your baby\'s growth by adding the first measurement',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.7),
            ),
          ),
        ],
      ),
    );
  }

  String _getMeasurementTitle(int type) {
    switch (type) {
      case 0:
        return 'Weight';
      case 1:
        return 'Height';
      case 2:
        return 'Head Circumference';
      default:
        return 'Weight';
    }
  }

  /// Build enhanced chart area with loading states and error handling
  Widget _buildEnhancedChartArea() {
    if (_getCurrentMeasurements.isEmpty) {
      return _buildEmptyChartState();
    }

    if (_isChartLoading) {
      return Container(
        height: 400,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(height: 2.h),
              Text(
                'Loading chart for ${_selectedDateRange}...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
      );
    }

    // Use enhanced growth chart renderer with error handling
    return Container(
      height: 400,
      child: EnhancedGrowthChartRenderer(
        measurements: _getCurrentMeasurements,
        measurementType: _getMeasurementTypeString(_selectedMeasurementType),
        gender: _normalizeGender(widget.babyProfile.gender),
        isMetric: _isMetric,
        dateRange: _selectedDateRange,
        birthDate: _babyBirthDate,
        onMeasurementTap: _handleMeasurementTap,
        onMeasurementEdit: _handleMeasurementEdit,
        onPercentileCurveHover: _handlePercentileCurveHover,
      ),
    );
  }
  
  /// Handle measurement edit from enhanced chart renderer
  void _handleMeasurementEdit(Measurement measurement) {
    final index = _allMeasurements.indexOf(measurement);
    if (index != -1) {
      _editMeasurement(index);
    }
  }

  /// Handle measurement tap from enhanced chart renderer
  void _handleMeasurementTap(Measurement measurement) {
    // Show measurement details in a dialog or bottom sheet
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: ThemeAwareColors.getCardColor(context),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Handle
            Center(
              child: Container(
                width: 10.w,
                height: 0.5.h,
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getDividerColor(context),
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
            ),
            SizedBox(height: 2.h),
            
            // Title
            Text(
              'Measurement Details',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            SizedBox(height: 2.h),
            
            // Measurement info
            _buildMeasurementDetailRow('Type', _getMeasurementTypeDisplayName(measurement.measurementType)),
            _buildMeasurementDetailRow('Value', measurement.displayValue),
            _buildMeasurementDetailRow('Date', _formatMeasurementDate(measurement.measuredAt)),
            _buildMeasurementDetailRow('Age', '${measurement.ageInMonths.toStringAsFixed(1)} months'),
            
            if (measurement.percentile != null)
              _buildMeasurementDetailRow('Percentile', measurement.percentileDisplay),
            
            if (measurement.zScore != null)
              _buildMeasurementDetailRow('Z-Score', measurement.zScoreDisplay),
            
            if (measurement.notes != null && measurement.notes!.isNotEmpty)
              _buildMeasurementDetailRow('Notes', measurement.notes!),
            
            SizedBox(height: 2.h),
            
            // Action buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _handleMeasurementEdit(measurement);
                    },
                    icon: Icon(Icons.edit, size: 18),
                    label: Text('Edit'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () {
                      Navigator.pop(context);
                      _confirmDeleteMeasurement(measurement);
                    },
                    icon: Icon(Icons.delete, size: 18, color: Colors.red),
                    label: Text('Delete', style: TextStyle(color: Colors.red)),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: Colors.red),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  } 
 /// Handle percentile curve hover from enhanced chart renderer
  void _handlePercentileCurveHover(String percentile, double ageInMonths) {
    // Could show percentile information tooltip
    // For now, just log the interaction
    debugPrint('Percentile curve hovered: $percentile at age $ageInMonths months');
  }

  /// Confirm measurement deletion
  void _confirmDeleteMeasurement(Measurement measurement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Measurement'),
        content: Text('Are you sure you want to delete this ${measurement.measurementType} measurement?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _measurementService.deleteMeasurement(measurement.id);
                await _loadGrowthMeasurements();
                
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Measurement deleted'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              } catch (e) {
                debugPrint('Error deleting measurement: $e');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to delete measurement'),
                      backgroundColor: Theme.of(context).colorScheme.error,
                    ),
                  );
                }
              }
            },
            child: Text('Delete', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  /// Build measurement detail row
  Widget _buildMeasurementDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 0.5.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 20.w,
            child: Text(
              '$label:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Get display name for measurement type
  String _getMeasurementTypeDisplayName(String measurementType) {
    switch (measurementType) {
      case 'weight':
        return 'Weight';
      case 'height':
      case 'length':
        return 'Height';
      case 'head_circumference':
        return 'Head Circumference';
      default:
        return measurementType;
    }
  }

  /// Format measurement date for display
  String _formatMeasurementDate(DateTime date) {
    return '${date.month}/${date.day}/${date.year} at ${TimeOfDay.fromDateTime(date).format(context)}';
  }
}