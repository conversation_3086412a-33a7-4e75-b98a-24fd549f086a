import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../utils/activity_type_config.dart';
import '../../../utils/activity_icon_manager.dart';

class RecentLogsWidget extends StatelessWidget {
  final List<ActivityLog> recentLogs;
  final Function(ActivityLog) onLogTap;

  const RecentLogsWidget({
    super.key,
    required this.recentLogs,
    required this.onLogTap,
  });

  @override
  Widget build(BuildContext context) {
    if (recentLogs.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Logs',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            TextButton(
              onPressed: () {
                // Navigate to full log history
              },
              child: Text(
                'View All',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ),
          ],
        ),
        SizedBox(height: 1.h),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: recentLogs.take(5).length,
          separatorBuilder: (context, index) => SizedBox(height: 1.h),
          itemBuilder: (context, index) {
            final log = recentLogs[index];
            return _buildLogItem(context, log);
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'history',
            color: Theme.of(context).colorScheme.onSurfaceVariant,
            size: 48,
          ),
          SizedBox(height: 2.h),
          Text(
            'No recent logs',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Start logging activities to see them here',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLogItem(BuildContext context, ActivityLog log) {
    return InkWell(
      onTap: () => onLogTap(log),
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
          ),
        ),
        child: Row(
          children: [
            ActivityIconManager.getActivityIconWithBackground(
              activityType: log.type.name,
              size: 20,
              containerPadding: 2.w,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    ActivityTypeConfig.getLabel(log.type.name),
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  // Format the log description with details
                  Text(
                    _getDetailedLogDescription(log),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  _getTimeAgo(log.timestamp),
                  style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                if (log.duration != null) ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    _formatDuration(log.duration!),
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }


  
  String _getDetailedLogDescription(ActivityLog log) {
    switch (log.type) {
      case ActivityType.feeding:
        final List<String> details = [];
        
        // Add amount if available
        if (log.data['quantity'] != null) {
          details.add('${log.data['quantity']} ${log.data['unit'] ?? 'ml'}');
        }
        
        // Add feeding type
        if (log.details?['feeding_type'] != null && log.details!['feeding_type'] != '') {
          details.add(log.details!['feeding_type']);
        }
        
        // Add formula type
        if (log.details?['formula_type'] != null && log.details!['formula_type'] != '') {
          details.add(log.details!['formula_type']);
        }
        
        // Add meal type
        if (log.details?['meal_type'] != null && log.details!['meal_type'] != '') {
          details.add(log.details!['meal_type']);
        }
        
        // Add food items
        if (log.details?['food_items'] != null && log.details!['food_items'] != '') {
          details.add(log.details!['food_items']);
        }
        
        return details.isNotEmpty ? details.join(', ') : 'Feeding session';
      case ActivityType.sleep:
        final quality = log.data['quality'];
        return quality != null
            ? 'Quality: ${quality.toString().toUpperCase()}'
            : 'Sleep session';
      case ActivityType.diaper:
        final diaperType = log.data['diaperType'];
        return diaperType?.toString().toUpperCase() ?? 'Diaper change';
      case ActivityType.medicine:
        final List<String> medicineDetails = [];
        
        // Add medication name
        if (log.details?['medication'] != null && log.details!['medication'] != '') {
          medicineDetails.add(log.details!['medication']);
        }
        
        // Add dosage
        if (log.details?['dosage'] != null && log.details!['dosage'] != '') {
          medicineDetails.add(log.details!['dosage']);
        }
        
        return medicineDetails.isNotEmpty ? medicineDetails.join(' • ') : 'Medicine administered';
      case ActivityType.vaccination:
        final List<String> vaccinationDetails = [];
        
        // Add vaccine name
        if (log.details?['vaccine'] != null && log.details!['vaccine'] != '') {
          vaccinationDetails.add(log.details!['vaccine']);
        }
        
        // Add batch number if available
        if (log.details?['batch_number'] != null && log.details!['batch_number'] != '') {
          vaccinationDetails.add('Batch: ${log.details!['batch_number']}');
        }
        
        // Add provider if available
        if (log.details?['provider'] != null && log.details!['provider'] != '') {
          vaccinationDetails.add('Provider: ${log.details!['provider']}');
        }
        
        return vaccinationDetails.isNotEmpty ? vaccinationDetails.join(' • ') : 'Vaccination administered';
      case ActivityType.milestone:
        final List<String> milestoneDetails = [];
        
        // Add milestone title
        if (log.details?['milestone_title'] != null && log.details!['milestone_title'] != '') {
          milestoneDetails.add(log.details!['milestone_title']);
        }
        
        // Add milestone category
        if (log.details?['milestone_category'] != null && log.details!['milestone_category'] != '') {
          milestoneDetails.add(log.details!['milestone_category']);
        }
        
        return milestoneDetails.isNotEmpty ? milestoneDetails.join(' • ') : 'Milestone achieved';
      case ActivityType.temperature:
        final List<String> temperatureDetails = [];
        
        // Add temperature value
        if (log.data['quantity'] != null) {
          temperatureDetails.add('${log.data['quantity']}°${log.data['unit'] ?? 'C'}');
        }
        
        // Add temperature type
        if (log.details?['temperature_type'] != null && log.details!['temperature_type'] != '') {
          temperatureDetails.add(log.details!['temperature_type']);
        }
        
        return temperatureDetails.isNotEmpty ? temperatureDetails.join(' • ') : 'Temperature recorded';
      case ActivityType.growth:
        final List<String> growthDetails = [];
        
        // Add measurement value
        if (log.data['quantity'] != null) {
          growthDetails.add('${log.data['quantity']} ${log.data['unit'] ?? 'cm'}');
        }
        
        // Add measurement type
        if (log.details?['measurement_type'] != null && log.details!['measurement_type'] != '') {
          growthDetails.add(log.details!['measurement_type']);
        }
        
        return growthDetails.isNotEmpty ? growthDetails.join(' • ') : 'Growth measurement';
      case ActivityType.tummy_time:
        final List<String> tummyTimeDetails = [];
        
        // Add activity
        if (log.details?['activity'] != null && log.details!['activity'] != '') {
          tummyTimeDetails.add('Activity: ${log.details!['activity']}');
        }
        
        // Add position
        if (log.details?['position'] != null && log.details!['position'] != '') {
          tummyTimeDetails.add('Position: ${log.details!['position']}');
        }
        
        // Add mood
        if (log.details?['mood'] != null && log.details!['mood'] != '') {
          tummyTimeDetails.add('Mood: ${log.details!['mood']}');
        }
        
        // Add duration
        if (log.details?['duration'] != null && log.details!['duration'] != '') {
          tummyTimeDetails.add('Duration: ${log.details!['duration']} min');
        }
        
        return tummyTimeDetails.isNotEmpty ? tummyTimeDetails.join(' • ') : 'Tummy time session';
      default:
        return log.notes ?? 'Activity logged';
    }
  }

  String _getTimeAgo(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes} minute${difference.inMinutes != 1 ? 's' : ''} ago';
    } else if (difference.inHours < 24) {
        return '${difference.inHours} hour${difference.inHours != 1 ? 's' : ''} ago';
    } else {
        return '${difference.inDays} day${difference.inDays != 1 ? 's' : ''} ago';
    }
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);

    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
}
