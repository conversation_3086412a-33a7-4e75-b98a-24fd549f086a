import 'package:flutter/material.dart';

import '../../../widgets/shared/quick_action_buttons_widget.dart';
import '../../../utils/activity_type_config.dart';
import '../../../services/activity_configuration_service.dart';

class QuickLogSectionWidget extends StatelessWidget {
  final Function(String) onActivitySelected;

  const QuickLogSectionWidget({
    super.key,
    required this.onActivitySelected,
  });

  @override
  Widget build(BuildContext context) {
    // Use centralized service for activity configurations
    final quickLogActivities = ActivityConfigurationService
        .getAllQuickLogConfigs(context)
        .map((config) => {
          ...config,
          'onTap': () => onActivitySelected(config['type']),
        }).toList();

    return QuickActionButtonsWidget(
      onFeedTap: () => onActivitySelected('feeding'),
      onDiaperTap: () => onActivitySelected('diaper'),
      onSleepTap: () => onActivitySelected('sleep'),
      onMoreTap: () => onActivitySelected('more'),
      allActivities: quickLogActivities,
    );
  }
}
