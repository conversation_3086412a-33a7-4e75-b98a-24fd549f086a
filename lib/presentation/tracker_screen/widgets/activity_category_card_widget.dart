import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

/// Constants for ActivityCategoryCardWidget styling
class _ActivityCardConstants {
  static const double cardBorderRadius = 16.0;
  static const double cardElevation = 2.0;
  static const double iconContainerBorderRadius = 12.0;
  static const double activityCardBorderRadius = 12.0;
  static const double activityIconBorderRadius = 8.0;
  static const double iconSize = 24.0;
  static const double activityIconSize = 20.0;
  static const double gridCrossAxisCount = 2.0;
  static const double gridChildAspectRatio = 1.2;
  static const double gradientAlphaStart = 0.1;
  static const double gradientAlphaEnd = 0.05;
  static const double colorAlphaLight = 0.1;
  static const double colorAlphaMedium = 0.2;
}

class ActivityCategoryCardWidget extends StatefulWidget {
  final String title;
  final String description;
  final Color color;
  final List<Map<String, dynamic>> activities;
  final Function(String) onActivityTap;

  const ActivityCategoryCardWidget({
    super.key,
    required this.title,
    required this.description,
    required this.color,
    required this.activities,
    required this.onActivityTap,
  });

  @override
  State<ActivityCategoryCardWidget> createState() =>
      _ActivityCategoryCardWidgetState();
}

class _ActivityCategoryCardWidgetState
    extends State<ActivityCategoryCardWidget> {
  bool _isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: _ActivityCardConstants.cardElevation,
      margin: EdgeInsets.symmetric(vertical: 1.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(_ActivityCardConstants.cardBorderRadius),
      ),
      child: Column(
        children: [
          _buildHeader(),
          if (_isExpanded) _buildActivitiesGrid(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return InkWell(
      onTap: _toggleExpansion,
      borderRadius: BorderRadius.circular(_ActivityCardConstants.cardBorderRadius),
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: _buildHeaderDecoration(),
        child: Row(
          children: [
            _buildIconContainer(),
            SizedBox(width: 3.w),
            Expanded(child: _buildHeaderContent()),
            _buildExpandIcon(),
          ],
        ),
      ),
    );
  }

  /// Handles expansion toggle with haptic feedback
  void _toggleExpansion() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
    HapticFeedback.selectionClick();
  }

  /// Builds the gradient decoration for the header
  BoxDecoration _buildHeaderDecoration() {
    return BoxDecoration(
      borderRadius: BorderRadius.circular(_ActivityCardConstants.cardBorderRadius),
      gradient: LinearGradient(
        colors: [
          widget.color.withValues(alpha: _ActivityCardConstants.gradientAlphaStart),
          widget.color.withValues(alpha: _ActivityCardConstants.gradientAlphaEnd),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      ),
    );
  }

  /// Builds the icon container for the category
  Widget _buildIconContainer() {
    return Container(
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: widget.color.withValues(alpha: _ActivityCardConstants.colorAlphaMedium),
        borderRadius: BorderRadius.circular(_ActivityCardConstants.iconContainerBorderRadius),
      ),
      child: CustomIconWidget(
        iconName: _getCategoryIcon(),
        color: widget.color,
        size: _ActivityCardConstants.iconSize,
      ),
    );
  }

  /// Builds the header text content
  Widget _buildHeaderContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          widget.title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: widget.color,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          widget.description,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  /// Builds the expand/collapse icon
  Widget _buildExpandIcon() {
    return Container(
      padding: EdgeInsets.all(1.w),
      child: Icon(
        _isExpanded ? Icons.expand_less : Icons.expand_more,
        color: widget.color,
        size: _ActivityCardConstants.iconSize,
      ),
    );
  }

  Widget _buildActivitiesGrid() {
    return Container(
      padding: EdgeInsets.fromLTRB(4.w, 0, 4.w, 4.w),
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: _buildGridDelegate(),
        itemCount: widget.activities.length,
        itemBuilder: (context, index) {
          final activity = widget.activities[index];
          return _buildActivityCard(activity);
        },
      ),
    );
  }

  /// Extracted grid delegate for better maintainability
  SliverGridDelegateWithFixedCrossAxisCount _buildGridDelegate() {
    return SliverGridDelegateWithFixedCrossAxisCount(
      crossAxisCount: 2,
      crossAxisSpacing: 3.w,
      mainAxisSpacing: 2.h,
      childAspectRatio: 1.2,
    );
  }

  Widget _buildActivityCard(Map<String, dynamic> activity) {
    return InkWell(
      onTap: () => _handleActivityTap(activity['type']),
      borderRadius: BorderRadius.circular(_ActivityCardConstants.activityCardBorderRadius),
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: _buildActivityCardDecoration(),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildActivityIcon(activity['icon']),
            SizedBox(height: 1.h),
            _buildActivityLabel(activity['label']),
            SizedBox(height: 0.5.h),
            _buildActivityDescription(activity['description']),
          ],
        ),
      ),
    );
  }

  /// Handles activity tap with haptic feedback
  void _handleActivityTap(String activityType) {
    widget.onActivityTap(activityType);
    HapticFeedback.lightImpact();
  }

  /// Builds the decoration for activity cards
  BoxDecoration _buildActivityCardDecoration() {
    return BoxDecoration(
      color: Theme.of(context).cardColor,
      borderRadius: BorderRadius.circular(_ActivityCardConstants.activityCardBorderRadius),
      border: Border.all(
        color: widget.color.withValues(alpha: _ActivityCardConstants.colorAlphaMedium),
      ),
    );
  }

  /// Builds the icon for an activity
  Widget _buildActivityIcon(String iconName) {
    return Container(
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: widget.color.withValues(alpha: _ActivityCardConstants.colorAlphaLight),
        borderRadius: BorderRadius.circular(_ActivityCardConstants.activityIconBorderRadius),
      ),
      child: CustomIconWidget(
        iconName: iconName,
        color: widget.color,
        size: _ActivityCardConstants.activityIconSize,
      ),
    );
  }

  /// Builds the label text for an activity
  Widget _buildActivityLabel(String label) {
    return Text(
      label,
      style: Theme.of(context).textTheme.labelMedium?.copyWith(
        fontWeight: FontWeight.w600,
        color: Theme.of(context).colorScheme.onSurface,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Builds the description text for an activity
  Widget _buildActivityDescription(String description) {
    return Text(
      description,
      style: Theme.of(context).textTheme.labelSmall?.copyWith(
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
      textAlign: TextAlign.center,
      maxLines: 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  String _getCategoryIcon() {
    switch (widget.title) {
      case 'Essential Logs':
        return 'baby_changing_station';
      case 'Health Monitoring':
        return 'health_and_safety';
      case 'Development Activities':
        return 'child_friendly';
      case 'Special Tracking':
        return 'star';
      default:
        return 'category';
    }
  }
}
