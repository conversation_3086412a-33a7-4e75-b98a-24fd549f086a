import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

import 'package:babytracker_pro/widgets/activity_log_item.dart';
import './widgets/quick_log_section_widget.dart';
import '../quick_log_bottom_sheet/quick_log_bottom_sheet.dart';

class TrackerScreen extends StatefulWidget {
  final int? initialTabIndex;
  const TrackerScreen({super.key, this.initialTabIndex});

  @override
  State<TrackerScreen> createState() => _TrackerScreenState();
}

class _TrackerScreenState extends State<TrackerScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {

  List<Map<String, dynamic>> _getLogCategories(BuildContext context) => [
    {
      'title': 'Essential Logs',
      'description': 'Daily care activities',
      'color': Theme.of(context).colorScheme.primary, // Dynamic theme-aware color
      'activities': [
        {
          'type': 'feeding',
          'label': 'Feeding',
          'icon': 'restaurant',
          'description': 'Breast, bottle, or solid feeding',
        },
        {
          'type': 'sleep',
          'label': 'Sleep',
          'icon': 'bedtime',
          'description': 'Sleep sessions and quality',
        },
        {
          'type': 'diaper',
          'label': 'Diaper',
          'icon': 'child_care',
          'description': 'Wet, dry, or both',
        },
      ],
    },
    {
      'title': 'Health Monitoring',
      'description': 'Medical and health tracking',
      'color': Theme.of(context).colorScheme.tertiary, // Dynamic theme-aware color
      'activities': [
        {
          'type': 'medicine',
          'label': 'Medicine',
          'icon': 'medication',
          'description': 'Medications and dosages',
        },
        {
          'type': 'vaccination',
          'label': 'Vaccination',
          'icon': 'vaccines',
          'description': 'Vaccine records and immunizations',
        },
        {
          'type': 'temperature',
          'label': 'Temperature',
          'icon': 'thermostat',
          'description': 'Body temperature readings',
        },
        {
          'type': 'potty',
          'label': 'Potty',
          'icon': 'wc',
          'description': 'Potty training progress',
        },
      ],
    },
    {
      'title': 'Development Activities',
      'description': 'Growth and learning activities',
      'color': Theme.of(context).colorScheme.secondary, // Dynamic theme-aware color
      'activities': [
        {
          'type': 'tummy_time',
          'label': 'Tummy Time',
          'icon': 'fitness_center',
          'description': 'Supervised tummy time',
        },
        {
          'type': 'story_time',
          'label': 'Story Time',
          'icon': 'menu_book',
          'description': 'Reading and storytelling',
        },
        {
          'type': 'screen_time',
          'label': 'Screen Time',
          'icon': 'tv',
          'description': 'Educational screen time',
        },
        {
          'type': 'skin_to_skin',
          'label': 'Skin to Skin',
          'icon': 'favorite',
          'description': 'Bonding time',
        },
        {
          'type': 'outdoor_play',
          'label': 'Outdoor Play',
          'icon': 'park',
          'description': 'Fresh air and nature',
        },
        {
          'type': 'indoor_play',
          'label': 'Indoor Play',
          'icon': 'toys',
          'description': 'Indoor activities and games',
        },
        {
          'type': 'brush_teeth',
          'label': 'Brush Teeth',
          'icon': 'tooth',
          'description': 'Dental hygiene routine',
        },
      ],
    },
    {
      'title': 'Special Tracking',
      'description': 'Specialized and custom logs',
      'color': Theme.of(context).colorScheme.error, // Dynamic theme-aware color
      'activities': [
        {
          'type': 'pumping',
          'label': 'Pumping',
          'icon': 'local_drink',
          'description': 'Breast milk pumping sessions',
        },
        {
          'type': 'growth',
          'label': 'Growth',
          'icon': 'trending_up',
          'description': 'Weight, height measurements',
        },
        {
          'type': 'milestone',
          'label': 'Milestones',
          'icon': 'emoji_events',
          'description': 'Developmental milestones tracking',
        },
        {
          'type': 'custom',
          'label': 'Custom',
          'icon': 'add_circle',
          'description': 'Create your own logs',
        },
      ],
    },
  ];

  // Real data from Supabase
  List<ActivityLog> _recentLogs = [];
  List<ActivityLog> _todayLogs = [];
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  BabyProfile? _currentBabyProfile;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _loadTrackerData();
  }

  Future<void> _loadTrackerData() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }
    
    try {
      debugPrint('🔄 Loading tracker data...');
      
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      debugPrint('👤 Current user: ${currentUser.id}');

      // Use BabyProfileStateManager to get the same active baby as Home screen
      debugPrint('🔄 Initializing BabyProfileStateManager...');
      if (!_babyProfileManager.hasBabies) {
        await _babyProfileManager.initialize();
      }
      
      if (_babyProfileManager.hasBabies) {
        _currentBabyProfile = _babyProfileManager.activeBaby;
        debugPrint('👶 Using active baby from state manager: ${_currentBabyProfile!.name} (${_currentBabyProfile!.id})');
        
        // Load recent activities
        await _loadRecentActivities();
      } else {
        // Fallback: Try direct query
        debugPrint('⚠️ No active baby from state manager, trying direct query...');
        
        final babyProfiles = await _supabaseService.select(
          'baby_profiles',
          filters: {'user_id': currentUser.id},
          limit: 1,
        );

        debugPrint('👶 Found ${babyProfiles.length} baby profiles from direct query');

        if (babyProfiles.isNotEmpty) {
          _currentBabyProfile = BabyProfile.fromJson(babyProfiles.first);
          debugPrint('👶 Using baby from direct query: ${_currentBabyProfile!.name} (${_currentBabyProfile!.id})');
          
          // Load recent activities
          await _loadRecentActivities();
        } else {
          debugPrint('⚠️ No baby profiles found anywhere');
          if (mounted) {
            setState(() {
              _errorMessage = 'No baby profiles found. Please create a baby profile first.';
              _isLoading = false;
            });
          }
          return;
        }
      }

      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading tracker data: $e');
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load data: ${e.toString()}';
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _loadRecentActivities() async {
    if (_currentBabyProfile == null) {
      debugPrint('⚠️ No current baby profile available for loading recent activities');
      return;
    }

    try {
      debugPrint('🔄 Loading recent activities for baby: ${_currentBabyProfile!.id}');

      // Load general recent activities (for potential future use)
      final List<ActivityLog> activities = await _supabaseService.getRecentActivities(
        _currentBabyProfile!.id,
        limit: 10,
        todayOnly: false,
      );

      // Load today's activities specifically (5 most recent)
      final List<ActivityLog> todayActivities = await _supabaseService.getRecentActivities(
        _currentBabyProfile!.id,
        limit: 5,
        todayOnly: true,
      );

      debugPrint('📊 Found ${activities.length} recent activities, ${todayActivities.length} today activities');
      
      // Debug the first few activities
      for (int i = 0; i < todayActivities.length && i < 3; i++) {
        final activity = todayActivities[i];
        debugPrint('🔍 Today Activity $i: Type=${activity.type}, Time=${activity.timestamp}, ID=${activity.id}');
      }

      if (mounted) {
        setState(() {
          _recentLogs = activities;
          _todayLogs = todayActivities;
        });
      }

      debugPrint('✅ Activities loaded successfully: ${_recentLogs.length} recent, ${_todayLogs.length} today');
    } catch (e) {
      debugPrint('❌ Error loading recent activities: $e');
      if (mounted) {
        setState(() {
          _recentLogs = [];
          _todayLogs = [];
        });
      }
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      // Refresh data when app comes back to foreground
      _loadRecentActivities();
    }
  }

  void _handleQuickLog(String activityType) {
    HapticFeedback.lightImpact();
    // Navigate to specific tracker or show quick log bottom sheet
    switch (activityType) {
      case 'feeding':
        // Use Quick Log for feeding
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'feeding',
            babyProfile: _currentBabyProfile,
            onDataSaved: () {
              debugPrint('🔄 Feeding saved, refreshing tracker data');
              _loadRecentActivities();
            },
          ),
        );
        break;
      case 'sleep':
        // Use Quick Log for sleep
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'sleep',
            babyProfile: _currentBabyProfile,
            onDataSaved: () {
              debugPrint('🔄 Sleep saved, refreshing tracker data');
              _loadRecentActivities();
            },
          ),
        );
        break;
      case 'growth':
        Navigator.pushNamed(context, '/growth-charts', arguments: _currentBabyProfile).then((_) {
          _loadRecentActivities();
        });
        break;
      case 'diaper':
      case 'medicine':
      case 'temperature':
      case 'potty':
      case 'pumping':
      case 'vaccination':  // Add vaccination case here
        // Use Quick Log for these activity types
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: activityType,
            babyProfile: _currentBabyProfile,
            onDataSaved: () {
              debugPrint('🔄 $activityType saved, refreshing tracker data');
              _loadRecentActivities();
            },
          ),
        );
        break;
      case 'milestone':
        // Use Quick Log for milestone
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'milestone',
            babyProfile: _currentBabyProfile,
            onDataSaved: () {
              debugPrint('🔄 Milestone saved, refreshing tracker data');
              _loadRecentActivities();
            },
          ),
        );
        break;
      case 'custom':
      case 'tummy_time':
      case 'story_time':
      case 'screen_time':
      case 'skin_to_skin':
      case 'outdoor_play':
      case 'indoor_play':
      case 'brush_teeth':
        Navigator.pushNamed(
          context, 
          '/quick-log-bottom-sheet',
          arguments: {
            'babyProfile': _currentBabyProfile,
            'initialActivityType': activityType,
          },
        ).then((_) {
          // Refresh data when returning from quick log
          _loadRecentActivities();
        });
        break;
      default:
        _showActivityLogDialog(activityType);
    }
  }

  void _showActivityLogDialog(String activityType) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Log ${activityType.replaceAll('_', ' ').toUpperCase()}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Quick log for ${activityType.replaceAll('_', ' ')}'),
            SizedBox(height: 2.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    _saveQuickLog(activityType);
                  },
                  child: const Text('Log Now'),
                ),
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: const Text('Cancel'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _saveQuickLog(String activityType) {
    HapticFeedback.mediumImpact();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
            '${activityType.replaceAll('_', ' ').toUpperCase()} logged successfully!'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            size: 6.w,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // If we can't pop, navigate to main navigation
              Navigator.pushNamedAndRemoveUntil(
                context, 
                '/main-navigation', 
                (route) => false
              );
            }
          },
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Activity Tracker',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_currentBabyProfile != null)
              Text(
                'for ${_currentBabyProfile!.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
          ],
        ),
        actions: [
          if (!_isLoading)
            IconButton(
              icon: CustomIconWidget(
                iconName: 'refresh',
                size: 6.w,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              onPressed: _loadTrackerData,
            ),
        ],
      ),
      body: SafeArea(
        child: _isLoading
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'Loading activities...',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              )
            : _errorMessage != null
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 48,
                          color: Theme.of(context).colorScheme.error,
                        ),
                        SizedBox(height: 2.h),
                        Text(
                          'Error',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 1.h),
                        Text(
                          _errorMessage!,
                          style: Theme.of(context).textTheme.bodyMedium,
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 2.h),
                        ElevatedButton(
                          onPressed: _loadTrackerData,
                          child: Text('Retry'),
                        ),
                      ],
                    ),
                  )
                : SingleChildScrollView(
                    padding: EdgeInsets.all(4.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Quick Access Section - using shared QuickLogSectionWidget
                        QuickLogSectionWidget(
                          onActivitySelected: (String activityType) {
                            _handleQuickLog(activityType);
                          },
                        ),
                        SizedBox(height: 3.h),
                        
                        // Activity Categories
                        for (var category in _getLogCategories(context)) ...[
                          _buildCategoryCard(category),
                          SizedBox(height: 2.h),
                        ],
                        
                        // Today's Activity Logs Section
                        SizedBox(height: 1.h),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Today\'s Activity Logs',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            TextButton(
                              onPressed: () {
                                Navigator.pushNamed(context, AppRoutes.activityTimeline);
                              },
                              child: Row(
                                children: [
                                  Text(
                                    'View All',
                                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                      color: Theme.of(context).colorScheme.primary,
                                    ),
                                  ),
                                  Icon(
                                    Icons.chevron_right,
                                    color: Theme.of(context).colorScheme.primary,
                                    size: 5.w,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 1.h),
                        if (_todayLogs.isNotEmpty)
                          ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: _todayLogs.length,
                            itemBuilder: (context, index) {
                              final activity = _todayLogs[index];
                              return ActivityLogItem(activity: activity);
                            },
                          )
                        else
                          Container(
                            width: double.infinity,
                            padding: EdgeInsets.all(4.w),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CustomIconWidget(
                                  iconName: 'hourglass_empty',
                                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                                  size: 8.w,
                                ),
                                SizedBox(height: 1.h),
                                Text(
                                  'No Activity Log Yet',
                                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                SizedBox(height: 0.5.h),
                                Text(
                                  'Start logging activities to see them here',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        SizedBox(height: 8.h), // Bottom padding for navigation
                      ],
                    ),
                  ),
      ),
    );
  }

  Widget _buildQuickAccessSection() {
    // Quick access to most common activities
    final quickActivities = [
      {'type': 'feeding', 'label': 'Feed', 'icon': 'restaurant', 'color': Theme.of(context).colorScheme.primary},
      {'type': 'sleep', 'label': 'Sleep', 'icon': 'bedtime', 'color': Theme.of(context).colorScheme.tertiary},
      {'type': 'diaper', 'label': 'Diaper', 'icon': 'child_care', 'color': Theme.of(context).colorScheme.secondary},
      {'type': 'medicine', 'label': 'Medicine', 'icon': 'medication', 'color': Theme.of(context).colorScheme.error},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Log',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: 1.h),
        Row(
          children: quickActivities.map((activity) {
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(right: 2.w),
                child: _buildQuickActionButton(
                  activity['label'] as String,
                  activity['icon'] as String,
                  activity['color'] as Color,
                  () => _handleQuickLog(activity['type'] as String),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickActionButton(
    String label,
    String iconName,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 10.h,
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(2.5.w),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: CustomIconWidget(
                iconName: iconName,
                color: color,
                size: 5.w,
              ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              label,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                color: color,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoryCard(Map<String, dynamic> category) {
    return Container(
      margin: EdgeInsets.only(bottom: 1.h),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
            blurRadius: 6,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ExpansionTile(
        leading: Icon(
          Icons.category,
          color: category['color'],
          size: 6.w,
        ),
        title: Text(
          category['title'],
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Text(
          category['description'],
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
        children: [
          Padding(
            padding: EdgeInsets.all(2.w),
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 2.w,
                mainAxisSpacing: 2.w,
                childAspectRatio: 3,
              ),
              itemCount: category['activities'].length,
              itemBuilder: (context, index) {
                final activity = category['activities'][index];
                return _buildActivityButton(activity, category['color']);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityButton(Map<String, dynamic> activity, Color color) {
    return GestureDetector(
      onTap: () => _handleQuickLog(activity['type']),
      child: Container(
        padding: EdgeInsets.all(2.w),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: activity['icon'],
              color: color,
              size: 4.5.w,
            ),
            SizedBox(width: 2.w),
            Expanded(
              child: Text(
                activity['label'],
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

}
