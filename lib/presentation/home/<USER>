import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:provider/provider.dart';
import 'dart:async';
import 'package:realtime_client/src/types.dart' show PostgresChangeEvent;

import '../../core/app_export.dart';
import '../../services/ai_insights_state_manager.dart';
import '../../services/notification_service.dart';
import '../../services/unified_notification_service.dart';
import '../../services/scheduled_notification_service.dart';
import '../ai_insights_dashboard/ai_insights_dashboard.dart';
import '../../widgets/shared/ai_insights_card_widget.dart';
import '../../widgets/shared/baby_profile_header_widget.dart';
import '../../widgets/shared/recent_activities_widget.dart';
import '../../widgets/shared/recent_logged_and_scheduled_activities_widget.dart';
import '../../widgets/shared/today_summary_card_widget.dart';
import '../../widgets/shared/today_schedules_card_widget.dart';
import '../quick_log_bottom_sheet/quick_log_bottom_sheet.dart';
import '../../widgets/activity_log_item.dart';
import '../tracker_screen/widgets/quick_log_section_widget.dart';
import '../../models/scheduled_activity.dart';
import '../../utils/activity_type_config.dart';
import '../scheduler/widgets/scheduled_activity_card.dart';
import '../../utils/subscription_access_control.dart';
import '../subscription/controllers/subscription_controller.dart';
import '../subscription/widgets/premium_feature_card.dart';

class Home extends StatefulWidget {
  final BabyProfile? babyProfile;
  final List<BabyProfile>? allBabies;
  final Function(BabyProfile)? onBabySelected;
  
  const Home({super.key, this.babyProfile, this.allBabies, this.onBabySelected});

  @override
  State<Home> createState() => _HomeState();
}

class _HomeState extends State<Home> {
  bool _isRefreshing = false;
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  final AIInsightsStateManager _aiInsightsManager = AIInsightsStateManager();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  final NotificationService _notificationService = NotificationService.instance;
  final UnifiedNotificationService _unifiedNotificationService = UnifiedNotificationService.instance;
  final ScheduledNotificationService _scheduledNotificationService = ScheduledNotificationService.instance;
  RealtimeChannel? _activityChannel;
  Timer? _timestampUpdateTimer;
  bool _isLoading = true;
  Timer? _refreshTimer;
  Timer? _updateTimeTimer;
  
  // Track AI insights loading to prevent duplicates
  bool _aiInsightsLoading = false;
  String? _lastLoadedBabyId;

  // Data from Supabase
  BabyProfile? get _currentBabyProfile => _babyProfileManager.activeBaby ?? widget.babyProfile;
  List<ActivityLog> _activityLogs = [];
  List<ActivityLog> _todayActivityLogs = [];
  List<Map<String, dynamic>> _recentActivities = [];
  List<ScheduledActivity> _scheduledActivities = [];

  @override
  void initState() {
    super.initState();
    debugPrint('🏠 Home widget initState called');
    
    // Add listeners
    _babyProfileManager.addListener(_onBabyProfileChanged);
    _aiInsightsManager.addListener(_onAIInsightsUpdated);
    
    // Initialize notification services
    _initializeNotificationService();
    _initializeUnifiedNotificationService();
    
    // Start scheduled notification service
    _scheduledNotificationService.start();
    
    _loadHomeData();
    _setupActivityListener();
    _startTimestampUpdateTimer();
    
    // Set up timer to update relative times every minute
    _updateTimeTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      if (mounted) {
        setState(() {
          // This will trigger a rebuild of the activity items with updated relative times
        });
      }
    });
  }

  @override
  void dispose() {
    _activityChannel?.unsubscribe();
    _refreshTimer?.cancel();
    _updateTimeTimer?.cancel();
    _timestampUpdateTimer?.cancel();
    _babyProfileManager.removeListener(_onBabyProfileChanged);
    _aiInsightsManager.removeListener(_onAIInsightsUpdated);
    _notificationService.removeListener(_onNotificationSettingsChanged);
    _scheduledNotificationService.stop();
    super.dispose();
  }
  
  void _onBabyProfileChanged() {
    if (mounted) {
      debugPrint('🔄 Baby profile changed, reloading home data');
      debugPrint('🔄 Baby count after change: ${_babyProfileManager.babiesCount}');
      debugPrint('🔄 All babies after change: ${_babyProfileManager.allBabies.map((b) => b.name).join(", ")}');
      // Reset AI insights loading state when baby changes
      _aiInsightsLoading = false;
      _lastLoadedBabyId = null;
      _loadHomeData();
    }
  }
  
  void _onScheduleChanged() {
    if (mounted && _currentBabyProfile != null) {
      debugPrint('🔄 Schedule changed, reloading scheduled activities');
      _loadScheduledActivities(_currentBabyProfile!);
    }
  }
  
  void _onAIInsightsUpdated() {
    if (mounted) {
      setState(() {
        // Trigger rebuild when AI insights state changes
      });
    }
  }
  
  Future<void> _initializeNotificationService() async {
    if (!_notificationService.isInitialized) {
      await _notificationService.init();
    }
    _notificationService.addListener(_onNotificationSettingsChanged);
  }
  
  Future<void> _initializeUnifiedNotificationService() async {
    if (!_unifiedNotificationService.isInitialized) {
      await _unifiedNotificationService.init();
    }
    _unifiedNotificationService.addListener(_onNotificationSettingsChanged);
  }
  
  void _onNotificationSettingsChanged() {
    if (mounted) {
      setState(() {
        // Trigger rebuild when notification settings change
      });
    }
  }
  
  
  void _startTimestampUpdateTimer() {
    // Update timestamps every minute to show accurate "X minutes ago" text
    _timestampUpdateTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      if (mounted && _aiInsightsManager.mostRecentTimestamp != null) {
        setState(() {
          // Trigger rebuild to update timestamp display
        });
      }
    });
  }
  

  @override
  void didUpdateWidget(Home oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reload data when baby profile changes
    if (oldWidget.babyProfile?.id != widget.babyProfile?.id) {
      _loadHomeData();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh data when screen comes back into focus (e.g., after profile creation)
    if (_currentBabyProfile == null && !_isLoading) {
      _loadHomeData();
    }
  }

  Future<void> _loadHomeData() async {
    try {
      setState(() => _isLoading = true);

      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      // Use the baby profile passed from main navigation
      if (_currentBabyProfile != null) {
        // Load activity logs
        await _loadActivityLogs(_currentBabyProfile!);
        
        // Load scheduled activities
        await _loadScheduledActivities(_currentBabyProfile!);
        
        // Load growth data
        await _loadGrowthData(_currentBabyProfile!);
        
        // Set loading to false early so user can see app content
        if (mounted) {
          setState(() => _isLoading = false);
        }
        
        // Track user activity and load AI insights asynchronously without blocking UI
        _aiInsightsManager.trackUserActivity(); // Track that user opened the app
        
        // ✅ PROFESSIONAL: Smart insight loading with automatic refresh conditions - NON-BLOCKING
        // Only load insights if not already loaded and user is on premium plan
        final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
        if (!subscriptionController.isOnFreePlan && 
            !_aiInsightsManager.hasInsightsForBaby(_currentBabyProfile!.id) && 
            !_aiInsightsLoading && 
            _lastLoadedBabyId != _currentBabyProfile!.id) {
          _loadAIInsightsAsync();
        } else if (subscriptionController.isOnFreePlan) {
          debugPrint('🚫 Home: Skipping AI insights for free plan user');
        } else {
          debugPrint('🏠 Home: Insights already loaded/loading for baby ${_currentBabyProfile!.name}');
        }
      } else {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    } catch (e) {
      print('Error loading home data: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }
  
  /// Load AI insights asynchronously without blocking the main UI
  Future<void> _loadAIInsightsAsync() async {
    if (_aiInsightsLoading || _currentBabyProfile == null) {
      debugPrint('🏠 Home: AI insights already loading or no baby profile');
      return;
    }
    
    // Check if we already have insights for this baby to prevent unnecessary calls
    if (_aiInsightsManager.hasInsightsForBaby(_currentBabyProfile!.id)) {
      debugPrint('🏠 Home: Using existing insights for baby ${_currentBabyProfile!.name}');
      return;
    }
    
    // Check if this baby was already loaded recently to prevent rapid duplicates
    if (_lastLoadedBabyId == _currentBabyProfile!.id) {
      debugPrint('🏠 Home: Baby ${_currentBabyProfile!.name} insights already processed recently');
      return;
    }
    
    try {
      _aiInsightsLoading = true;
      _lastLoadedBabyId = _currentBabyProfile!.id;
      
      debugPrint('🏠 Home: No insights for baby ${_currentBabyProfile!.name}, loading with professional conditions...');
      await _aiInsightsManager.loadInsights(_currentBabyProfile!);
    } catch (e) {
      debugPrint('❌ Error loading AI insights asynchronously: $e');
    } finally {
      _aiInsightsLoading = false;
    }
  }

  void _setupActivityListener() async {
    if (_currentBabyProfile == null) return;
    try {
      debugPrint('🔄 Setting up activity log listener...');
      final client = await _supabaseService.client;
      _activityChannel = client
        .channel('home_activities')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'activity_logs',
          callback: (dynamic payload) {
            debugPrint('🔔 Activity log update received: ${payload.toString()}');
            final Map<String, dynamic>? record = 
              payload['new'] as Map<String, dynamic>? ?? 
              payload['old'] as Map<String, dynamic>?;
            if (record != null && record['baby_id'] == _currentBabyProfile?.id) {
              debugPrint('✅ Activity belongs to current baby, refreshing data...');
              _refreshData();
            } else {
              debugPrint('⏭️ Activity not for current baby, skipping refresh');
            }
          },
        )
        .subscribe();
      debugPrint('✅ Activity log listener setup complete');
    } catch (e) {
      debugPrint('❌ Error setting up activity log listener: $e');
    }
  }

  Future<void> _loadActivityLogs(BabyProfile babyProfile) async {
    try {
      debugPrint('🔄 Loading activity logs for baby: ${babyProfile.id}');
      final recentActivities = await _supabaseService.getRecentActivities(
        babyProfile.id,
        limit: 5,
        todayOnly: false,
      );
      final todayActivities = await _supabaseService.getRecentActivities(
        babyProfile.id,
        todayOnly: true,
      );
      debugPrint('✅ Loaded ${recentActivities.length} recent activities and ${todayActivities.length} today activities');
      
      if (mounted) {
        setState(() {
          _activityLogs = recentActivities;
          _todayActivityLogs = todayActivities;
          _recentActivities = _combineActivitiesAndSchedules(recentActivities, _scheduledActivities);
          debugPrint('📊 Recent activities updated: ${_recentActivities.length} items (including schedules)');
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading activities: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading activities: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _refreshAIInsights() async {
    if (_isRefreshing || _currentBabyProfile == null) return;

    // Skip AI insights refresh for free users
    final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
    if (subscriptionController.isOnFreePlan) {
      debugPrint('🚫 Skipping AI insights refresh for free plan user');
      return;
    }

    try {
      setState(() => _isRefreshing = true);
      
      debugPrint('🔄 Refreshing AI insights for baby: ${_currentBabyProfile!.id}');
      
      // Development mode check - allow bypassing rate limits
      bool isDevMode = false;
      const bool kDebugMode = bool.fromEnvironment('dart.vm.product') == false;
      
      // Check if we can refresh normally
      bool canRefresh = _aiInsightsManager.canManualRefresh();
      
      if (!canRefresh && kDebugMode) {
        // In debug mode, show option to force refresh
        final timeRemaining = _aiInsightsManager.getTimeUntilNextRefresh();
        if (timeRemaining != null) {
          final hoursRemaining = timeRemaining.inHours;
          final minutesRemaining = timeRemaining.inMinutes % 60;
          
          String timeMessage;
          String waitTimeMessage;
          if (hoursRemaining > 0) {
            timeMessage = '$hoursRemaining hour${hoursRemaining > 1 ? 's' : ''}';
            if (minutesRemaining > 0) {
              timeMessage += ' $minutesRemaining minute${minutesRemaining > 1 ? 's' : ''}';
            }
            waitTimeMessage = 'wait $timeMessage';
          } else {
            timeMessage = '$minutesRemaining minute${minutesRemaining > 1 ? 's' : ''}';
            waitTimeMessage = 'wait $timeMessage';
          }
          
          final shouldForceUpdate = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Rate Limited (Dev Mode)'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Rate limit active: $waitTimeMessage remaining.'),
                  SizedBox(height: 8),
                  Text('Force refresh anyway? (Dev mode only)', 
                       style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold)),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  style: ElevatedButton.styleFrom(backgroundColor: Colors.orange),
                  child: Text('Force Refresh'),
                ),
              ],
            ),
          );
          
          if (shouldForceUpdate == true) {
            isDevMode = true; // Allow bypass
            canRefresh = true; // Override rate limit
          }
        }
      }
      
      if (!canRefresh && !isDevMode) {
        debugPrint('⚠️ Manual refresh failed or rate-limited');
        
        // Get detailed rate limiting information
        final timeRemaining = _aiInsightsManager.getTimeUntilNextRefresh();
        if (timeRemaining != null) {
          final hoursRemaining = timeRemaining.inHours;
          final minutesRemaining = timeRemaining.inMinutes % 60;
          
          String timeMessage;
          String waitTimeMessage;
          if (hoursRemaining > 0) {
            timeMessage = '$hoursRemaining hour${hoursRemaining > 1 ? 's' : ''}';
            if (minutesRemaining > 0) {
              timeMessage += ' $minutesRemaining minute${minutesRemaining > 1 ? 's' : ''}';
            }
            waitTimeMessage = 'wait $timeMessage';
          } else {
            timeMessage = '$minutesRemaining minute${minutesRemaining > 1 ? 's' : ''}';
            waitTimeMessage = 'wait $timeMessage';
          }
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No updates available. Last update was ${_aiInsightsManager.formattedLastUpdate ?? 'recently'}. Please log more activities or $waitTimeMessage to refresh insights.'),
              backgroundColor: Colors.amber,
              duration: Duration(seconds: 5),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('⚠️ Unable to refresh - please log more activities or try again later.'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 3),
            ),
          );
        }
        return; // Exit early if rate limited and not in dev mode
      }
      
      // Manual refresh through state manager
      final success = await _aiInsightsManager.manualRefresh(_currentBabyProfile!, forceRefresh: isDevMode);

      if (success) {
        debugPrint('✅ AI insights manually refreshed successfully');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isDevMode 
              ? '✅ AI insights refreshed successfully (Dev mode override)'
              : '✅ AI insights refreshed successfully'),
            backgroundColor: isDevMode ? Colors.orange : Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        // Check if it's a "no new data" scenario vs rate limiting
        final timeRemaining = _aiInsightsManager.getTimeUntilNextRefresh();
        if (timeRemaining == null) {
          // Not rate limited, so it must be no new data
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('No new insights available. Continue logging activities to receive updated insights and recommendations.'),
              backgroundColor: Colors.blue[600],
              duration: Duration(seconds: 4),
            ),
          );
        } else {
          // Rate limited
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('❌ Manual refresh failed or rate limited'),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
      
    } catch (e) {
      debugPrint('❌ Error refreshing AI insights: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ Error refreshing insights - try again later.'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isRefreshing = false);
    }
  }

  Future<void> _loadScheduledActivities(BabyProfile babyProfile) async {
    try {
      debugPrint('🔄 Loading scheduled activities for baby: ${babyProfile.id}');
      final activities = await _supabaseService.getAllScheduledActivities(babyProfile.id);
      
      debugPrint('✅ Loaded ${activities.length} scheduled activities');
      
      if (mounted) {
        setState(() {
          _scheduledActivities = activities;
          // Update recent activities to include the new scheduled activities
          _recentActivities = _combineActivitiesAndSchedules(_activityLogs, activities);
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading scheduled activities: $e');
      if (mounted) {
        setState(() {
          _scheduledActivities = [];
        });
      }
    }
  }

  Future<void> _loadGrowthData(BabyProfile babyProfile) async {
    try {
      final measurements = await _supabaseService.select(
        'growth_measurements',
        filters: {'baby_id': babyProfile.id},
        orderBy: 'measured_at',
        ascending: false,
        limit: 1,
      ).catchError((error) async {
        debugPrint('Trying fallback column name for growth data: $error');
        return await _supabaseService.select(
          'growth_measurements',
          filters: {'baby_id': babyProfile.id},
          orderBy: 'measurement_date',
          ascending: false,
          limit: 1,
        );
      }).catchError((error) {
        debugPrint('Growth measurements table not found: $error');
        return <Map<String, dynamic>>[];
      });

      if (measurements.isNotEmpty) {
        final latest = measurements.first;
        // Use both possible column names for date
        final dateStr = latest['measured_at'] ?? latest['measurement_date'];
        final measuredAt = dateStr != null ? DateTime.parse(dateStr) : DateTime.now();
        final daysAgo = DateTime.now().difference(measuredAt).inDays;

        _growthData = {
          "currentWeight": "${latest['weight'] ?? latest['value'] ?? 'N/A'} ${latest['unit'] ?? 'kg'}",
          "currentHeight": "${latest['height'] ?? latest['value'] ?? 'N/A'} ${latest['unit'] ?? 'cm'}",
          "lastMeasurement": daysAgo == 0 ? "Today" : "$daysAgo days ago",
          "trend": "stable",
        };
      } else {
        _growthData = {
          "currentWeight": "No data",
          "currentHeight": "No data",
          "lastMeasurement": "Never",
          "trend": "unknown",
        };
      }
    } catch (e) {
      debugPrint('Error loading growth data: $e');
      _growthData = {
        "currentWeight": "Error",
        "currentHeight": "Error",
        "lastMeasurement": "Error",
        "trend": "unknown",
      };
    }
  }

  // Enhanced today's summary - synchronized with all activities
  Map<String, dynamic> get todaySummary {
    final now = DateTime.now();
    final todayActivities = _todayActivityLogs.where((log) {
      final recordedAt = log.timestamp;
      return recordedAt.year == now.year &&
             recordedAt.month == now.month &&
             recordedAt.day == now.day;
    }).toList();

    // Count all sleep logs, even if duration is 0
    final sleepLogs = todayActivities.where((log) => log.type == ActivityType.sleep).toList();
    final sleepDuration = sleepLogs.fold<Duration>(Duration.zero, (total, log) => total + (log.endTime?.difference(log.timestamp) ?? Duration.zero));

    return {
      'totalActivities': todayActivities.length,
      'feedingCount': todayActivities.where((log) => log.type == ActivityType.feeding).length,
      'diaperCount': todayActivities.where((log) => log.type == ActivityType.diaper).length,
      'sleepCount': sleepLogs.length,
      'sleepHours': sleepDuration.inHours,
      'sleepMinutes': sleepDuration.inMinutes.remainder(60),
      'medicineCount': todayActivities.where((log) => log.type == ActivityType.medicine).length,
      'vaccinationCount': todayActivities.where((log) => log.type == ActivityType.vaccination).length,
      'temperatureCount': todayActivities.where((log) => log.type == ActivityType.temperature).length,
      'pottyCount': todayActivities.where((log) => log.type == ActivityType.potty).length,
      'growthCount': todayActivities.where((log) => log.type == ActivityType.growth).length,
      'milestoneCount': todayActivities.where((log) => log.type == ActivityType.milestone).length,
      // Development Activities
      'tummyTimeCount': todayActivities.where((log) => log.type == ActivityType.tummy_time).length,
      'storyTimeCount': todayActivities.where((log) => log.type == ActivityType.story_time).length,
      'screenTimeCount': todayActivities.where((log) => log.type == ActivityType.screen_time).length,
      'skinToSkinCount': todayActivities.where((log) => log.type == ActivityType.skin_to_skin).length,
      'outdoorPlayCount': todayActivities.where((log) => log.type == ActivityType.outdoor_play).length,
      'indoorPlayCount': todayActivities.where((log) => log.type == ActivityType.indoor_play).length,
      'brushTeethCount': todayActivities.where((log) => log.type == ActivityType.brush_teeth).length,
      // Special Tracking
      'pumpingCount': todayActivities.where((log) => log.type == ActivityType.pumping).length,
      'lastActivity': _activityLogs.isNotEmpty ? _activityLogs.first.toRecentActivityMap() : null,
    };
  }


  // Growth data from Supabase
  Map<String, dynamic> _growthData = {};


  // Method to add activity - activities are now loaded from Supabase
  void addActivity(Map<String, dynamic> activity) {
    // Refresh data from Supabase instead of managing local list
    if (_currentBabyProfile != null) {
      _aiInsightsManager.trackUserActivity(); // Track user adding activity
      _loadActivityLogs(_currentBabyProfile!);
    }
  }

  // Method to get activities - now returns real data from Supabase
  List<Map<String, dynamic>> getActivities() {
    return _recentActivities;
  }

  /// Combines activity logs and scheduled activities into a unified recent activities list
  List<Map<String, dynamic>> _combineActivitiesAndSchedules(
    List<ActivityLog> activityLogs, 
    List<ScheduledActivity> scheduledActivities
  ) {
    final List<Map<String, dynamic>> combinedActivities = [];
    
    // Add activity logs
    combinedActivities.addAll(activityLogs.map((log) {
      final map = log.toRecentActivityMap();
      debugPrint('📅 Activity timestamp: ${map['timestamp']}');
      return map;
    }));
    
    // Add scheduled activities (convert to recent activity format)
    combinedActivities.addAll(scheduledActivities.map((schedule) {
      return _convertScheduleToRecentActivity(schedule);
    }));
    
    // Sort by timestamp (most recent first)
    combinedActivities.sort((a, b) {
      final aTime = a['timestamp'] as DateTime?;
      final bTime = b['timestamp'] as DateTime?;
      if (aTime == null && bTime == null) return 0;
      if (aTime == null) return 1;
      if (bTime == null) return -1;
      return bTime.compareTo(aTime);
    });
    
    // Limit to recent items (e.g., last 10)
    return combinedActivities.take(10).toList();
  }

  /// Converts a ScheduledActivity to the format expected by RecentActivitiesWidget
  Map<String, dynamic> _convertScheduleToRecentActivity(ScheduledActivity schedule) {
    // Use the scheduled time as the timestamp
    final timestamp = schedule.scheduledTime;
    
    // Determine if this is upcoming, overdue, or completed
    final now = DateTime.now();
    final isCompleted = schedule.isCompleted;
    final isOverdue = timestamp.isBefore(now) && !isCompleted;
    final isUpcoming = timestamp.isAfter(now) && !isCompleted;
    
    String title = schedule.title;
    if (isCompleted) {
      title = '✅ $title (Completed)';
    } else if (isOverdue) {
      title = '⚠️ $title (Overdue)';
    } else if (isUpcoming) {
      title = '📅 $title (Scheduled)';
    }
    
    return {
      'title': title,
      'timestamp': timestamp,
      'type': 'scheduled_activity', // Custom type for scheduled activities
      'details': {
        'description': schedule.description,
        'is_completed': isCompleted,
        'is_overdue': isOverdue,
        'is_upcoming': isUpcoming,
        'original_type': schedule.type.name,
      },
    };
  }

  Future<void> _loadActivitiesFromSupabase() async {
    try {
      setState(() => _isLoading = true);

      // Load activities from Supabase
      final activities = await _supabaseService.select(
        'activities',
        orderBy: 'started_at',
        ascending: false,
        limit: 20,
      );

      if (mounted) {
        setState(() {
          _recentActivities = activities.map((activity) => {
            'title': activity['title'] ?? 'Activity',
            'timestamp': activity['started_at'] ?? DateTime.now().toIso8601String(),
            'type': activity['type'] ?? 'unknown',
          }).toList();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading activities: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _refreshData() async {
    if (_isRefreshing || _currentBabyProfile == null) return;
    
    try {
      setState(() => _isRefreshing = true);
      _aiInsightsManager.trackUserActivity(); // Track user interaction
      await _loadActivityLogs(_currentBabyProfile!);
    } finally {
      if (mounted) {
        setState(() => _isRefreshing = false);
      }
    }
  }

  // Add public refresh method for external calls
  Future<void> refreshData() async {
    await _refreshData();
  }


  void _navigateToSettings() {
    Navigator.pushNamed(context, '/settings');
  }

  void _navigateToAIChat() {
    if (_currentBabyProfile != null) {
      AppRoutes.navigateToAIChatAssistant(
        context,
        babyProfile: _currentBabyProfile!,
        recentActivities: _activityLogs,
      );
    }
  }

  void _navigateToAIInsights() {
    // VERY OBVIOUS DEBUG - THIS SHOULD SHOW IN CONSOLE
    print('🔥🔥🔥 FULL DASHBOARD BUTTON TAPPED! 🔥🔥🔥');
    debugPrint('🚀 Navigating to AI Insights Dashboard...');
    
    // Show a snackbar to confirm the method is being called
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Full Dashboard button tapped - Opening comprehensive dashboard...'),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
    
    // Navigate to the comprehensive dashboard
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInsightsDashboard(
          babyProfile: _currentBabyProfile!,
        ),
      ),
    ).then((result) {
      debugPrint('✅ Returned from AI Insights Dashboard');
    }).catchError((error) {
      debugPrint('❌ Error navigating to AI Insights Dashboard: $error');
    });
  }

  void _showQuickAddBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => QuickLogBottomSheet(
        babyProfile: _currentBabyProfile,
        onDataSaved: () {
          // Refresh data when new activity is saved
          _aiInsightsManager.trackUserActivity(); // Track user adding new activity
          _refreshData();
        },
      ),
    );
  }

  void _handleQuickLogActivitySelection(String activityType) {
    switch (activityType) {
      case 'feeding':
        // Use Quick Log for feeding instead of separate tracker
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'feeding',
            babyProfile: _currentBabyProfile,
            onDataSaved: () {
              debugPrint('🔄 Feeding saved callback triggered from home');
              _aiInsightsManager.trackUserActivity();
              _refreshData();
            },
          ),
        );
        break;
      case 'sleep':
        // Use Quick Log for sleep instead of separate tracker
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'sleep',
            babyProfile: _currentBabyProfile,
            onDataSaved: () {
              debugPrint('🔄 Sleep saved callback triggered from home');
              _aiInsightsManager.trackUserActivity();
              _refreshData();
            },
          ),
        );
        break;
      case 'diaper':
        Navigator.pushNamed(context, '/diaper-log', arguments: _currentBabyProfile);
        break;
      case 'growth':
        Navigator.pushNamed(context, '/growth-charts', arguments: _currentBabyProfile);
        break;
      case 'more':
        _showQuickAddBottomSheet();
        break;
      default:
        // For all other activities, use the quick log bottom sheet with pre-selected activity type
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            babyProfile: _currentBabyProfile,
            initialActivityType: activityType,
            onDataSaved: () {
              // Refresh data when new activity is saved
              _aiInsightsManager.trackUserActivity();
              _refreshData();
            },
          ),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        body: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      );
    }

    if (_currentBabyProfile == null) {
      return _buildNoBabyProfileState();
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: RefreshIndicator(
          onRefresh: _refreshData,
          color: Theme.of(context).colorScheme.primary,
          child: CustomScrollView(
            slivers: [
              _buildAppBar(),
              SliverPadding(
                padding: EdgeInsets.symmetric(horizontal: 4.w),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    SizedBox(height: 0.2.h),
                    // Debug: Check baby count for switch visibility
                    Builder(builder: (context) {
                      final babiesCount = _babyProfileManager.babiesCount;
                      final hasMultiple = babiesCount > 1;
                      debugPrint('🏠 Baby Card Widget: Total babies: $babiesCount, Has multiple: $hasMultiple');
                      return BabyProfileHeaderWidget(
                        babyProfile: _currentBabyProfile!,
                        hasMultipleBabies: hasMultiple,
                        onTapProfile: () {
                          Navigator.pushNamed(
                            context,
                            '/baby-profile-creation',
                            arguments: _currentBabyProfile,
                          ).then((result) {
                            if (result is BabyProfile) {
                              print('Received updated profile with note: ${result.note}');
                              if (widget.onBabySelected != null) {
                                widget.onBabySelected!(result);
                              }
                            }
                          });
                        },
                        onTapSwitch: () {
                          debugPrint('🔄 Switch button tapped from card! Babies count: $babiesCount');
                          _navigateToBabySelector();
                        },
                      );
                    }),
                    SizedBox(height: 2.h),
                    TodaySummaryCardWidget(
                      summaryData: todaySummary,
                    ),
                    SizedBox(height: 2.h),
                    QuickLogSectionWidget(
                      onActivitySelected: (String activityType) {
                        // Handle activity selection like in the tracker screen
                        _handleQuickLogActivitySelection(activityType);
                      },
                    ),
                    SizedBox(height: 2.h),
                    TodaySchedulesCardWidget(
                      schedules: _scheduledActivities,
                      onScheduleTap: (schedule) {
                        // Navigate to scheduler screen when schedule is tapped
                        Navigator.pushNamed(context, '/scheduler').then((result) {
                          // Refresh schedules when returning from scheduler
                          _onScheduleChanged();
                        });
                      },
                    ),
                    SizedBox(height: 2.h),
                    // AI Insights Section with Premium Access Control
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section Header - Always show title
                        Padding(
                          padding: EdgeInsets.only(bottom: 1.h),
                          child: Text(
                            'AI Insights',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: ThemeAwareColors.getPrimaryTextColor(context),
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        
                        // Content - Premium Card or Full Widget
                        Consumer<SubscriptionController>(
                          builder: (context, subscriptionController, _) {
                            if (subscriptionController.isOnFreePlan) {
                              return PremiumFeatureCard(
                                title: 'Unlock AI Insights',
                                description: 'Get personalized insights and recommendations',
                                icon: Icons.analytics,
                                compact: false,
                              );
                            }
                            
                            // Show AI Insights for premium users (without title since we show it above)
                            return Container(
                              width: double.infinity,
                              padding: EdgeInsets.all(3.w),
                              decoration: BoxDecoration(
                                color: Theme.of(context).cardColor,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: ThemeAwareColors.getDividerColor(context),
                                  width: 1,
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: ThemeAwareColors.getShadowColor(context),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Subtitle and refresh button row
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          _aiInsightsManager.formattedLastUpdate != null 
                                            ? 'Last updated ${_aiInsightsManager.formattedLastUpdate}'
                                            : _aiInsightsManager.isLoading 
                                              ? 'Analyzing patterns...'
                                              : 'No insights yet • Log activities to get started',
                                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                            color: ThemeAwareColors.getSecondaryTextColor(context),
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      if (!(_aiInsightsManager.isLoading || _aiInsightsManager.isUpdating))
                                        IconButton(
                                          onPressed: _refreshAIInsights,
                                          icon: Icon(
                                            Icons.refresh,
                                            size: 5.w,
                                            color: Theme.of(context).colorScheme.primary,
                                          ),
                                          padding: EdgeInsets.all(1.w),
                                          constraints: BoxConstraints(
                                            minWidth: 8.w,
                                            minHeight: 8.w,
                                          ),
                                        ),
                                    ],
                                  ),
                                  
                                  // AI Insights content
                                  if (_aiInsightsManager.isLoading || _aiInsightsManager.isUpdating)
                                    Center(
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(vertical: 2.h),
                                        child: CircularProgressIndicator(
                                          color: Theme.of(context).colorScheme.primary,
                                        ),
                                      ),
                                    )
                                  else if (_aiInsightsManager.getFormattedInsightsForHome().isEmpty)
                                    Center(
                                      child: Padding(
                                        padding: EdgeInsets.symmetric(vertical: 2.h),
                                        child: Column(
                                          children: [
                                            Icon(
                                              Icons.analytics_outlined,
                                              size: 8.w,
                                              color: ThemeAwareColors.getSecondaryTextColor(context),
                                            ),
                                            SizedBox(height: 1.h),
                                            Text(
                                              'No insights available yet',
                                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                                color: ThemeAwareColors.getSecondaryTextColor(context),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    )
                                  else
                                    // Show insights content
                                    Column(
                                      children: _aiInsightsManager.getFormattedInsightsForHome().take(3).map((insight) {
                                        return Container(
                                          margin: EdgeInsets.only(bottom: 1.h),
                                          padding: EdgeInsets.all(2.w),
                                          decoration: BoxDecoration(
                                            color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
                                            borderRadius: BorderRadius.circular(8),
                                          ),
                                          child: Row(
                                            children: [
                                              Icon(
                                                Icons.analytics_outlined,
                                                size: 4.w,
                                                color: Theme.of(context).colorScheme.primary,
                                              ),
                                              SizedBox(width: 2.w),
                                              Expanded(
                                                child: Text(
                                                  insight['title'] ?? 'Insight',
                                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                                    color: ThemeAwareColors.getPrimaryTextColor(context),
                                                  ),
                                                  maxLines: 2,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                    ),
                                  
                                  // Action buttons
                                  SizedBox(height: 1.h),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextButton.icon(
                                          onPressed: _navigateToAIChat,
                                          icon: Icon(Icons.chat_bubble_outline, size: 4.w),
                                          label: Text('Ask AI'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: Theme.of(context).colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                      Expanded(
                                        child: TextButton.icon(
                                          onPressed: _navigateToAIInsights,
                                          icon: Icon(Icons.analytics_outlined, size: 4.w),
                                          label: Text('View All'),
                                          style: TextButton.styleFrom(
                                            foregroundColor: Theme.of(context).colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            );
                          },
                        ),
                      ],
                    ),
                    SizedBox(height: 2.h),
                    RecentLoggedAndScheduledActivitiesWidget(
                      activityLogs: _activityLogs.take(5).toList(),
                      scheduledActivities: _scheduledActivities.take(5).toList(),
                      onScheduledActivityCompleted: _markScheduledActivityCompleted,
                      onScheduledActivityDeleted: _deleteScheduledActivity,
                    ),
                    SizedBox(height: 8.h),
                  ]),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    final userProfile = _authService.userProfile;
    final now = DateTime.now();
    String greeting;
    final hour = now.hour;
    if (hour < 12) {
      greeting = 'Good morning';
    } else if (hour < 18) {
      greeting = 'Good afternoon';
    } else {
      greeting = 'Good evening';
    }
    final userName = userProfile?.fullName?.split(' ').first ?? '';
    final dateStr = '${_weekdayName(now.weekday)}, ${now.day} ${_monthName(now.month)} ${now.year}';
    
    return SliverAppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      toolbarHeight: 80, // Fixed height to prevent overflow
      flexibleSpace: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 4.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '$greeting${userName.isNotEmpty ? ', $userName' : ''}!',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        fontSize: 14.sp, // Reduced from 16.sp to make space for notification icon
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Text(
                      dateStr,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w400,
                        fontSize: 11.sp, // Reduced from 12.sp to make space for notification icon
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Notification button
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    child: Stack(
                      children: [
                        IconButton(
                          onPressed: () => Navigator.pushNamed(context, '/notifications'),
                          icon: AnimatedSwitcher(
                            duration: const Duration(milliseconds: 300),
                            child: Icon(
                              _unifiedNotificationService.notificationIcon,
                              key: ValueKey(_unifiedNotificationService.notificationIcon),
                              color: _unifiedNotificationService.notificationsEnabled
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              size: 5.w,
                            ),
                          ),
                          tooltip: _unifiedNotificationService.notificationsEnabled
                              ? 'View notifications'
                              : 'Notifications disabled - View all',
                        ),
                        // Notification badge
                        if (_unifiedNotificationService.shouldShowBadge)
                          Positioned(
                            right: 8,
                            top: 8,
                            child: Container(
                              padding: EdgeInsets.all(1.w),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.error,
                                shape: BoxShape.circle,
                              ),
                              constraints: BoxConstraints(
                                minWidth: 4.w,
                                minHeight: 4.w,
                              ),
                              child: Text(
                                _unifiedNotificationService.badgeCount > 99 
                                    ? '99+' 
                                    : '${_unifiedNotificationService.badgeCount}',
                                style: TextStyle(
                                  color: Theme.of(context).colorScheme.onError,
                                  fontSize: 8.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  SizedBox(width: 2.w),
                  // Theme toggle button
                  Builder(
                    builder: (context) {
                      try {
                        return Consumer<ThemeService>(
                          builder: (context, themeService, child) {
                            return Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                                ),
                              ),
                              child: IconButton(
                                onPressed: () async {
                                  await themeService.toggleTheme();
                                },
                                icon: AnimatedSwitcher(
                                  duration: const Duration(milliseconds: 300),
                                  child: Icon(
                                    themeService.isDarkMode ? Icons.wb_sunny : Icons.nightlight_round,
                                    key: ValueKey(themeService.isDarkMode),
                                    color: themeService.isDarkMode 
                                      ? Theme.of(context).colorScheme.tertiary 
                                      : Theme.of(context).colorScheme.primary,
                                    size: 5.w,
                                  ),
                                ),
                                tooltip: themeService.isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
                              ),
                            );
                          },
                        );
                      } catch (e) {
                        // Fallback if ThemeService not available
                        return Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                            ),
                          ),
                          child: IconButton(
                            onPressed: () {
                              // Simple theme toggle fallback
                              final brightness = Theme.of(context).brightness;
                              // This won't persist but will show the icon works
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Theme toggle - current: ${brightness.name}')),
                              );
                            },
                            icon: Icon(
                              Theme.of(context).brightness == Brightness.dark 
                                ? Icons.wb_sunny 
                                : Icons.nightlight_round,
                              color: Theme.of(context).brightness == Brightness.dark 
                                ? Theme.of(context).colorScheme.tertiary 
                                : Theme.of(context).colorScheme.primary,
                              size: 5.w,
                            ),
                            tooltip: 'Theme Toggle',
                          ),
                        );
                      }
                    },
                  ),
                  SizedBox(width: 2.w),
                  // Settings button
                  Container(
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.9),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                    child: IconButton(
                      onPressed: () => Navigator.pushNamed(context, '/settings'),
                      icon: CustomIconWidget(
                        iconName: 'settings',
                        size: 5.w,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      tooltip: 'Settings',
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _weekdayName(int weekday) {
    const names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return names[(weekday - 1) % 7];
  }

  String _monthName(int month) {
    const names = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
    return names[(month - 1) % 12];
  }
  
  void _showNotificationQuickMenu() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        margin: EdgeInsets.all(4.w),
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  _notificationService.notificationIcon,
                  color: Theme.of(context).colorScheme.primary,
                  size: 6.w,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Notifications',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Text(
      _notificationService.notificationSummary,
      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
        color: Theme.of(context).colorScheme.onSurfaceVariant,
      ),
    ),
    SizedBox(height: 3.h),
    Row(
      children: [
        Expanded(
          child: Switch(
            value: _notificationService.notificationsEnabled,
            onChanged: (value) async {
              await _notificationService.setNotificationsEnabled(value);
              _notificationService.notify(value ? 'Notifications turned on' : 'Notifications turned off');
            },
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          flex: 2,
          child: Text(
            _notificationService.notificationsEnabled ? 'Enabled' : 'Disabled',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    ),
            SizedBox(height: 3.h),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(context, '/settings');
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(vertical: 2.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(
                  'Notification Settings',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String? _formatTimeDifferenceFromDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays > 0) {
      final days = difference.inDays;
      final hours = difference.inHours % 24;
      if (hours > 0) {
        return '$days day${days > 1 ? 's' : ''} $hours hour${hours > 1 ? 's' : ''} ago';
      }
      return '$days day${days > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      if (minutes > 0) {
        return '$hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''} ago';
      }
      return '$hours hour${hours > 1 ? 's' : ''} ago';
    } else {
      final minutes = difference.inMinutes;
      if (minutes < 1) {
        return 'just now';
      }
      return '$minutes minute${minutes > 1 ? 's' : ''} ago';
    }
  }


  Widget _buildBabyProfileSection() {
    final babiesCount = _babyProfileManager.babiesCount;
    final hasMultiple = babiesCount > 1;
    debugPrint('🏠 Baby Profile Section: Total babies: $babiesCount, Has multiple: $hasMultiple');
    debugPrint('🏠 All babies: ${_babyProfileManager.allBabies.map((b) => b.name).join(", ")}');
    
    return SliverPadding(
      padding: EdgeInsets.all(4.w),
      sliver: SliverToBoxAdapter(
        child: BabyProfileHeaderWidget(
          babyProfile: _currentBabyProfile!,
          hasMultipleBabies: hasMultiple,
          onTapProfile: () {
            Navigator.pushNamed(context, '/baby-profile-view', arguments: _currentBabyProfile);
          },
          onTapSwitch: () async {
            debugPrint('🔄 Switch button tapped! Babies count: $babiesCount');
            if (babiesCount > 1) {
              final selected = await Navigator.pushNamed(context, '/baby-selector-screen', arguments: _currentBabyProfile);
              if (selected is BabyProfile && widget.onBabySelected != null) {
                widget.onBabySelected!(selected);
              }
            }
          },
        ),
      ),
    );
  }

  Widget _buildTodaySummarySection() {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      sliver: SliverToBoxAdapter(
        child: TodaySummaryCardWidget(
          summaryData: todaySummary,
        ),
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return SliverPadding(
      padding: EdgeInsets.all(4.w),
      sliver: SliverToBoxAdapter(
        child: QuickLogSectionWidget(
          onActivitySelected: (String activityType) {
            _handleQuickLogActivitySelection(activityType);
          },
        ),
      ),
    );
  }

  Widget _buildRecentActivitiesSection() {
    return SliverPadding(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      sliver: SliverToBoxAdapter(
        child: RecentActivitiesWidget(
          activities: _recentActivities,
        ),
      ),
    );
  }

  Widget _buildAIInsightsSection() {
    return SliverPadding(
      padding: EdgeInsets.all(4.w),
      sliver: SliverToBoxAdapter(
        child: AIInsightsCardWidget(
          insights: _aiInsightsManager.getFormattedInsightsForHome(),
          overallSummary: _aiInsightsManager.overallSummary,
          lastUpdated: _aiInsightsManager.formattedLastUpdate,
          onAIChatTap: () => Navigator.pushNamed(context, '/ai-chat'),
          onViewAllTap: () => Navigator.pushNamed(context, '/ai-insights'),
          onRefresh: _refreshAIInsights,
          isLoading: _aiInsightsManager.isLoading || _aiInsightsManager.isUpdating,
        ),
      ),
    );
  }

  Widget _buildNoBabyProfileState() {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('No baby profile found'),
            ElevatedButton(
              onPressed: () => Navigator.pushNamed(context, '/baby-profile-creation'),
              child: Text('Create Baby Profile'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTodaySummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Today\'s Summary',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        TodaySummaryCardWidget(
          summaryData: todaySummary,
        ),
      ],
    );
  }

  Widget _buildQuickLog() {
    return QuickLogSectionWidget(
      onActivitySelected: (String activityType) {
        _handleQuickLogActivitySelection(activityType);
      },
    );
  }

  

  

  /// Finds the ScheduledActivity object from the activity map
  ScheduledActivity? _findScheduledActivityFromMap(Map<String, dynamic> activity) {
    final timestamp = activity['timestamp'] as DateTime?;
    if (timestamp == null) return null;
    
    // Find the matching scheduled activity by timestamp
    try {
      return _scheduledActivities.firstWhere(
        (schedule) => schedule.scheduledTime.isAtSameMomentAs(timestamp),
      );
    } catch (e) {
      debugPrint('⚠️ Could not find scheduled activity for timestamp: $timestamp');
      return null;
    }
  }

  /// Marks a scheduled activity as completed
  Future<void> _markScheduledActivityCompleted(ScheduledActivity activity) async {
    try {
      await _supabaseService.markScheduledActivityCompleted(activity.id);
      // Reload data to reflect changes
      if (_currentBabyProfile != null) {
        await _loadScheduledActivities(_currentBabyProfile!);
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Activity marked as completed')),
        );
      }
    } catch (e) {
      debugPrint('❌ Error marking activity as completed: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to mark activity as completed')),
        );
      }
    }
  }

  /// Deletes a scheduled activity
  Future<void> _deleteScheduledActivity(ScheduledActivity activity) async {
    try {
      await _supabaseService.deleteScheduledActivity(activity.id);
      // Reload data to reflect changes
      if (_currentBabyProfile != null) {
        await _loadScheduledActivities(_currentBabyProfile!);
      }
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Activity deleted')),
        );
      }
    } catch (e) {
      debugPrint('❌ Error deleting activity: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to delete activity')),
        );
      }
    }
  }

  void _navigateToBabySelector() {
    final babiesCount = _babyProfileManager.babiesCount;
    debugPrint('🔄 _navigateToBabySelector called. Babies count: $babiesCount');
    if (babiesCount > 1) {
      Navigator.pushNamed(
        context,
        '/baby-selector-screen',
        arguments: _currentBabyProfile?.id,
      ).then((result) {
        if (result is BabyProfile && widget.onBabySelected != null) {
          print('✅ Baby selected from selector: ${result.name} (ID: ${result.id})');
          widget.onBabySelected!(result);
        }
      });
    } else {
      debugPrint('⚠️ Cannot navigate to baby selector - only $babiesCount baby(ies) available');
    }
  }
}
