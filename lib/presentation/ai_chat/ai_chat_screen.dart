import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:uuid/uuid.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';
import '../../models/feature_access.dart';
import '../subscription/controllers/feature_access_controller.dart';
import '../subscription/widgets/feature_gate.dart';
import './widgets/chat_input_widget.dart';
import './widgets/chat_message_widget.dart';
import './widgets/quick_topics_widget.dart';

class AIChatScreen extends StatefulWidget {
  final BabyProfile babyProfile;
  final List<ActivityLog>? recentActivities;

  const AIChatScreen({
    super.key,
    required this.babyProfile,
    this.recentActivities,
  });

  @override
  State<AIChatScreen> createState() => _AIChatScreenState();
}

class _AIChatScreenState extends State<AIChatScreen> {
  final AIChatService _chatService = AIChatService();
  final _uuid = Uuid();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();

  final List<ChatMessage> _messages = [];
  bool _isLoading = false;
  bool _isStreaming = false;
  String _streamingMessage = '';

  @override
  void initState() {
    super.initState();
    _addWelcomeMessage();
  }

  void _addWelcomeMessage() {
    final welcomeMessage = ChatMessage(
      id: _uuid.v4(),
      content:
          "Hi! I'm your AI parenting assistant. I'm here to help you with any questions about ${widget.babyProfile.name}'s care. You can ask me about feeding, sleeping, development, or any other concerns you might have!",
      isFromUser: false,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(welcomeMessage);
    });
  }

  Future<void> _sendMessage(String message) async {
    if (message.trim().isEmpty || _isLoading) return;

    final userMessage = ChatMessage(
      id: _uuid.v4(),
      content: message,
      isFromUser: true,
      timestamp: DateTime.now(),
    );

    setState(() {
      _messages.add(userMessage);
      _isLoading = true;
      _isStreaming = true;
      _streamingMessage = '';
    });

    _messageController.clear();
    _scrollToBottom();

    try {
      final conversationHistory = _messages
          .where((msg) => msg.id != userMessage.id)
          .map((msg) => {
                'role': msg.isFromUser ? 'user' : 'assistant',
                'content': msg.content,
              })
          .toList();

      final aiMessageId = _uuid.v4();

      // Add placeholder for streaming message
      final streamingPlaceholder = ChatMessage(
        id: aiMessageId,
        content: '',
        isFromUser: false,
        timestamp: DateTime.now(),
        isStreaming: true,
      );

      setState(() {
        _messages.add(streamingPlaceholder);
      });

      await for (final chunk in _chatService.streamChatResponse(
        userMessage: message,
        babyProfile: widget.babyProfile,
        recentActivities: widget.recentActivities,
        conversationHistory: conversationHistory,
      )) {
        setState(() {
          _streamingMessage += chunk;
          // Update the last message (streaming placeholder) with accumulated content
          _messages.last = _messages.last.copyWith(content: _streamingMessage);
        });
        _scrollToBottom();
      }

      // Mark streaming as complete
      setState(() {
        _messages.last = _messages.last.copyWith(isStreaming: false);
        _isStreaming = false;
        _streamingMessage = '';
      });
    } catch (e) {
      final errorMessage = ChatMessage(
        id: _uuid.v4(),
        content:
            "I'm sorry, I encountered an error while processing your message. Please try again.",
        isFromUser: false,
        timestamp: DateTime.now(),
        isError: true,
      );

      setState(() {
        _messages.add(errorMessage);
        _isStreaming = false;
        _streamingMessage = '';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
      _scrollToBottom();
    }
  }

  Future<void> _sendQuickTopic(String topic) async {
    final topicQuestions = {
      'Feeding':
          'Can you give me advice about feeding patterns and tips for ${widget.babyProfile.name}?',
      'Sleep':
          'I need help with ${widget.babyProfile.name}\'s sleep schedule and sleep training.',
      'Development':
          'What developmental milestones should I expect for ${widget.babyProfile.name} at this age?',
      'Health':
          'What are some general health tips and warning signs I should watch for?',
      'Crying':
          'My baby has been crying a lot. What could be the reasons and how can I help?',
      'Safety': 'What safety precautions should I take at this age?',
    };

    final question =
        topicQuestions[topic] ?? 'Tell me about $topic for my baby.';
    await _sendMessage(question);
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Assistant',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            Text(
              'Caring for ${widget.babyProfile.name}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            size: 6.w,
          ),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: CustomIconWidget(
              iconName: 'info_outline',
              size: 6.w,
            ),
            onPressed: () => _showInfoDialog(),
          ),
        ],
      ),
      body: FeatureGate(
        feature: AppFeature.aiChat,
        child: Column(
          children: [
          // Quick Topics (shown when no messages or only welcome message)
          if (_messages.length <= 1)
            Container(
              padding: EdgeInsets.all(4.w),
              child: QuickTopicsWidget(
                onTopicSelected: _sendQuickTopic,
              ),
            ),

          // Chat Messages
          Expanded(
            child: ListView.builder(
              controller: _scrollController,
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              itemCount: _messages.length,
              itemBuilder: (context, index) {
                return ChatMessageWidget(
                  message: _messages[index],
                  babyName: widget.babyProfile.name,
                );
              },
            ),
          ),

          // Chat Input
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: ChatInputWidget(
                controller: _messageController,
                onSend: _sendMessage,
                isLoading: _isLoading,
              ),
            ),
          ),
          ],
        ),
        onUpgrade: () => Navigator.of(context).pushNamed('/subscription'),
      ),
    );
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        title: Text(
          'AI Assistant Info',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'This AI assistant provides general parenting guidance based on current pediatric recommendations. '
          'Always consult with your healthcare provider for medical concerns or emergencies.\n\n'
          'The assistant has access to your baby\'s profile and recent activities to provide personalized advice.',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Got it',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ChatMessage {
  final String id;
  final String content;
  final bool isFromUser;
  final DateTime timestamp;
  final bool isError;
  final bool isStreaming;

  ChatMessage({
    required this.id,
    required this.content,
    required this.isFromUser,
    required this.timestamp,
    this.isError = false,
    this.isStreaming = false,
  });

  ChatMessage copyWith({
    String? id,
    String? content,
    bool? isFromUser,
    DateTime? timestamp,
    bool? isError,
    bool? isStreaming,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isFromUser: isFromUser ?? this.isFromUser,
      timestamp: timestamp ?? this.timestamp,
      isError: isError ?? this.isError,
      isStreaming: isStreaming ?? this.isStreaming,
    );
  }
}
