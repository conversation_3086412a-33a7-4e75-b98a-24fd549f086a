import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class ChatInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onSend;
  final bool isLoading;

  const ChatInputWidget({
    super.key,
    required this.controller,
    required this.onSend,
    this.isLoading = false,
  });

  @override
  State<ChatInputWidget> createState() => _ChatInputWidgetState();
}

class _ChatInputWidgetState extends State<ChatInputWidget> {
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _handleSend() {
    final message = widget.controller.text.trim();
    if (message.isNotEmpty && !widget.isLoading) {
      widget.onSend(message);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(6.w),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.controller,
              maxLines: 4,
              minLines: 1,
              enabled: !widget.isLoading,
              style: GoogleFonts.inter(
                fontSize: 12.sp,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
              decoration: InputDecoration(
                hintText: 'Ask me anything about your baby...',
                hintStyle: GoogleFonts.inter(
                  fontSize: 12.sp,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 2.w,
                  vertical: 1.h,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: (_) => _handleSend(),
            ),
          ),
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: _handleSend,
            child: Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                color: _hasText && !widget.isLoading
                    ? ThemeAwareColors.getPrimaryColor(context)
                    : ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(5.w),
              ),
              child: widget.isLoading
                  ? Center(
                      child: SizedBox(
                        width: 4.w,
                        height: 4.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: const AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      ),
                    )
                  : CustomIconWidget(
                      iconName: 'send',
                      color: _hasText ? Colors.white : ThemeAwareColors.getSecondaryTextColor(context),
                      size: 5.w,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}
