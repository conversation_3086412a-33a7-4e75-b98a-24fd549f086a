import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';
import '../ai_chat_screen.dart';

class ChatMessageWidget extends StatelessWidget {
  final ChatMessage message;
  final String babyName;

  const ChatMessageWidget({
    super.key,
    required this.message,
    required this.babyName,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isFromUser) ...[
            _buildAvatar(context, isUser: false),
            SizedBox(width: 3.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: message.isFromUser
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                if (!message.isFromUser)
                  Padding(
                    padding: EdgeInsets.only(bottom: 1.h),
                    child: Text(
                      'AI Assistant',
                      style: Theme.of(context).textTheme.labelMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ),
                _buildMessageBubble(context),
                SizedBox(height: 0.5.h),
                Row(
                  mainAxisAlignment: message.isFromUser
                      ? MainAxisAlignment.end
                      : MainAxisAlignment.start,
                  children: [
                    Text(
                      _formatTime(message.timestamp),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                            fontSize: 10.sp,
                          ),
                    ),
                    if (!message.isFromUser && !message.isError) ...[
                      SizedBox(width: 2.w),
                      GestureDetector(
                        onTap: () => _copyToClipboard(context),
                        child: CustomIconWidget(
                          iconName: 'content_copy',
                          size: 3.5.w,
                          color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                        ),
                      ),
                    ],
                    if (message.isStreaming) ...[
                      SizedBox(width: 2.w),
                      SizedBox(
                        width: 3.w,
                        height: 3.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            ThemeAwareColors.getPrimaryColor(context),
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),
          if (message.isFromUser) ...[
            SizedBox(width: 3.w),
            _buildAvatar(context, isUser: true),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, {required bool isUser}) {
    return Container(
      width: 10.w,
      height: 10.w,
      decoration: BoxDecoration(
        color: isUser
            ? ThemeAwareColors.getPrimaryColor(context)
            : ThemeAwareColors.getSecondaryColor(context),
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: CustomIconWidget(
        iconName: isUser ? 'person' : 'smart_toy',
        color: Colors.white,
        size: 5.w,
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    final isUser = message.isFromUser;
    final isError = message.isError;

    Color backgroundColor;
    Color textColor;

    if (isError) {
      backgroundColor = ThemeAwareColors.getErrorColor(context).withValues(alpha: 0.1);
      textColor = ThemeAwareColors.getErrorColor(context);
    } else if (isUser) {
      backgroundColor = ThemeAwareColors.getPrimaryColor(context);
      textColor = Colors.white;
    } else {
      backgroundColor = ThemeAwareColors.getSurfaceColor(context);
      textColor = ThemeAwareColors.getPrimaryTextColor(context);
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(4.w),
          topRight: Radius.circular(4.w),
          bottomLeft: Radius.circular(isUser ? 4.w : 1.w),
          bottomRight: Radius.circular(isUser ? 1.w : 4.w),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isError)
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'error_outline',
                  color: ThemeAwareColors.getErrorColor(context),
                  size: 4.w,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Error',
                  style: GoogleFonts.inter(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w600,
                    color: ThemeAwareColors.getErrorColor(context),
                  ),
                ),
              ],
            ),
          if (isError) SizedBox(height: 1.h),
          Text(
            message.content,
            style: GoogleFonts.inter(
              fontSize: 12.sp,
              color: textColor,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
