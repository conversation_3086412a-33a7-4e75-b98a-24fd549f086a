import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class QuickTopicsWidget extends StatelessWidget {
  final Function(String) onTopicSelected;

  const QuickTopicsWidget({
    super.key,
    required this.onTopicSelected,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    final topics = [
      {
        'title': 'Feeding',
        'icon': 'restaurant',
        'color': isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4)
      },
      {
        'title': 'Sleep', 
        'icon': 'bedtime', 
        'color': isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261)
      },
      {
        'title': 'Development',
        'icon': 'child_care',
        'color': isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069)
      },
      {
        'title': 'Health',
        'icon': 'health_and_safety',
        'color': isDark ? const Color(0xFFFF8A65) : const Color(0xFFE76F51)
      },
      {
        'title': 'Crying',
        'icon': 'sentiment_very_dissatisfied',
        'color': isDark ? const Color(0xFF4DB6AC) : const Color(0xFF2A9D8F)
      },
      {
        'title': 'Safety', 
        'icon': 'shield', 
        'color': isDark ? const Color(0xFF90A4AE) : const Color(0xFF264653)
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Topics',
          style: GoogleFonts.inter(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        Text(
          'Tap on a topic to get started with expert advice',
          style: GoogleFonts.inter(
            fontSize: 12.sp,
            color: ThemeAwareColors.getSecondaryTextColor(context),
          ),
        ),
        SizedBox(height: 3.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 3.w,
            mainAxisSpacing: 2.h,
            childAspectRatio: 2.5,
          ),
          itemCount: topics.length,
          itemBuilder: (context, index) {
            final topic = topics[index];
            return _buildTopicCard(
              context,
              title: topic['title'] as String,
              icon: topic['icon'] as String,
              color: topic['color'] as Color,
              onTap: () => onTopicSelected(topic['title'] as String),
            );
          },
        ),
        SizedBox(height: 2.h),
        _buildCustomQuestionPrompt(context),
      ],
    );
  }

  Widget _buildTopicCard(
    BuildContext context, {
    required String title,
    required String icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(3.w),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: icon,
              color: color,
              size: 5.w,
            ),
            SizedBox(width: 2.w),
            Flexible(
              child: Text(
                title,
                style: GoogleFonts.inter(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
                textAlign: TextAlign.center,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCustomQuestionPrompt(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(3.w),
        border: Border.all(
          color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'chat',
                color: ThemeAwareColors.getPrimaryColor(context),
                size: 5.w,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Have a specific question?',
                  style: GoogleFonts.inter(
                    fontSize: 13.sp,
                    fontWeight: FontWeight.w600,
                    color: ThemeAwareColors.getPrimaryColor(context),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Text(
            'Feel free to ask me anything about your baby\'s care, development, or any concerns you might have!',
            style: GoogleFonts.inter(
              fontSize: 11.sp,
              color: ThemeAwareColors.getSecondaryTextColor(context),
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }
}
