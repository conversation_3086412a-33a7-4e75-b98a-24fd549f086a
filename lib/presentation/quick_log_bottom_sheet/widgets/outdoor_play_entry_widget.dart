import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class OutdoorPlayEntryWidget extends BaseActivityWidget {
  const OutdoorPlayEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _OutdoorPlayEntryWidgetState();
}

class _OutdoorPlayEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customActivityController = TextEditingController();
  final TextEditingController customLocationController = TextEditingController();
  final TextEditingController customWeatherController = TextEditingController();

  // Outdoor activity types
  final List<String> activityOptions = [
    'Walking',
    'Park Visit',
    'Garden Time',
    'Nature Walk',
    'Playground',
    'Beach/Water Play',
    'Hiking',
    'Picnic',
    'Bike Ride',
    'Sports/Games',
    'Fresh Air Time',
    'Backyard Play',
    'Other',
  ];

  // Location types
  final List<String> locationOptions = [
    'Local Park',
    'Backyard',
    'Garden',
    'Beach',
    'Forest/Woods',
    'Playground',
    'Walking Trail',
    'Sports Field',
    'Neighborhood',
    'Nature Reserve',
    'Other',
  ];

  // Weather conditions
  final List<String> weatherOptions = [
    'Sunny',
    'Partly Cloudy',
    'Cloudy',
    'Light Rain',
    'Windy',
    'Cool',
    'Warm',
    'Hot',
    'Perfect Weather',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customActivityController.addListener(_updateCustomActivity);
    customLocationController.addListener(_updateCustomLocation);
    customWeatherController.addListener(_updateCustomWeather);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      String? errorMessage;
      
      if (duration == null) {
        errorMessage = 'Please enter number only';
      } else if (duration < 0) {
        errorMessage = 'Please enter a positive number';
      }
      
      updateData({
        'duration_text': durationText,
        'duration': duration,
        'duration_error': errorMessage,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomActivity() {
    if (activityData['activity_selection'] == 'Other') {
      final customActivity = customActivityController.text.trim();
      updateData({
        'custom_activity': customActivity,
        'activity': customActivity.isNotEmpty ? customActivity : 'Other',
      });
    }
  }

  void _updateCustomLocation() {
    if (activityData['location_selection'] == 'Other') {
      final customLocation = customLocationController.text.trim();
      updateData({
        'custom_location': customLocation,
        'location': customLocation.isNotEmpty ? customLocation : 'Other',
      });
    }
  }

  void _updateCustomWeather() {
    if (activityData['weather_selection'] == 'Other') {
      final customWeather = customWeatherController.text.trim();
      updateData({
        'custom_weather': customWeather,
        'weather': customWeather.isNotEmpty ? customWeather : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customActivityController.dispose();
    customLocationController.dispose();
    customWeatherController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Outdoor Play';

  @override
  String? getActivityDescription() => 'Track outdoor activities and fresh air time for healthy development';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'activity_selection': 'Park Visit',
      'activity': 'Park Visit',
      'location_selection': 'Local Park',
      'location': 'Local Park',
      'weather_selection': 'Sunny',
      'weather': 'Sunny',
      'duration': null,
      'duration_text': '',
      'notes': '',
      'custom_activity': '',
      'custom_location': '',
      'custom_weather': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isActivityOtherSelected = activityData['activity_selection'] == 'Other';
    final isLocationOtherSelected = activityData['location_selection'] == 'Other';
    final isWeatherOtherSelected = activityData['weather_selection'] == 'Other';
    
    return Column(
      children: [
        // Activity type selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Activity Type',
          value: activityData['activity_selection'],
          items: activityOptions,
          onChanged: (value) {
            updateData({
              'activity_selection': value,
              'activity': value,
            });
            if (value != 'Other') {
              customActivityController.clear();
              updateData({'custom_activity': ''});
            }
          },
        ),
        
        // Show custom activity input when "Other" is selected
        if (isActivityOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Activity',
            controller: customActivityController,
            hint: 'Enter custom outdoor activity...',
          ),
        ],
        
        // Location selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Location',
          value: activityData['location_selection'],
          items: locationOptions,
          onChanged: (value) {
            updateData({
              'location_selection': value,
              'location': value,
            });
            if (value != 'Other') {
              customLocationController.clear();
              updateData({'custom_location': ''});
            }
          },
        ),
        
        // Show custom location input when "Other" is selected
        if (isLocationOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Location',
            controller: customLocationController,
            hint: 'Enter custom location...',
          ),
        ],
        
        // Weather selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Weather Conditions',
          value: activityData['weather_selection'],
          items: weatherOptions,
          onChanged: (value) {
            updateData({
              'weather_selection': value,
              'weather': value,
            });
            if (value != 'Other') {
              customWeatherController.clear();
              updateData({'custom_weather': ''});
            }
          },
        ),
        
        // Show custom weather input when "Other" is selected
        if (isWeatherOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Weather',
            controller: customWeatherController,
            hint: 'Enter weather conditions...',
          ),
        ],
        
        // Duration input
        ActivityFormHelper.buildTextField(
          label: 'Duration (minutes)',
          controller: durationController,
          hint: 'How long was the outdoor play?',
          keyboardType: TextInputType.number,
        ),
        
        // Show duration error if exists
        if (activityData['duration_error'] != null) ...[
          Container(
            margin: EdgeInsets.only(top: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    activityData['duration_error'],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 2.h),
        ],
        
        // Notes
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Any additional details about the outdoor play session...',
          maxLines: 3,
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'outdoor_play';

  @override
  IconData getActivityIcon() => Icons.park;

  @override
  Color getActivityColor() => const Color(0xFF4CAF50);
}