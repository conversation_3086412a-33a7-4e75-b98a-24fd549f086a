import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class SleepEntryWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const SleepEntryWidget({
    super.key,
    required this.onDataChanged,
  });

  @override
  State<SleepEntryWidget> createState() => _SleepEntryWidgetState();
}

class _SleepEntryWidgetState extends State<SleepEntryWidget> {
  DateTime startTime = DateTime.now();
  DateTime? endTime;
  bool isCurrentlySleeping = true;

  @override
  void initState() {
    super.initState();
    _updateData();
  }

  void _updateData() {
    widget.onDataChanged({
      'startTime': startTime,
      'endTime': endTime,
      'isCurrentlySleeping': isCurrentlySleeping,
      'duration': endTime?.difference(startTime).inMinutes,
    });
  }

  void _selectStartTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(startTime),
    );

    if (picked != null) {
      setState(() {
        startTime = DateTime(
          startTime.year,
          startTime.month,
          startTime.day,
          picked.hour,
          picked.minute,
        );
        // Reset end time if it's before start time
        if (endTime != null && endTime!.isBefore(startTime)) {
          endTime = null;
          isCurrentlySleeping = true;
        }
      });
      _updateData();
    }
  }

  void _selectEndTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: endTime != null
          ? TimeOfDay.fromDateTime(endTime!)
          : TimeOfDay.fromDateTime(startTime.add(const Duration(hours: 1))),
    );

    if (picked != null) {
      final newEndTime = DateTime(
        startTime.year,
        startTime.month,
        startTime.day,
        picked.hour,
        picked.minute,
      );

      // If end time is before start time, assume it's the next day
      final adjustedEndTime = newEndTime.isBefore(startTime)
          ? newEndTime.add(const Duration(days: 1))
          : newEndTime;

      setState(() {
        endTime = adjustedEndTime;
        isCurrentlySleeping = false;
      });
      _updateData();
    }
  }

  void _toggleCurrentlySleeping(bool value) {
    setState(() {
      isCurrentlySleeping = value;
      if (value) {
        endTime = null;
      }
    });
    _updateData();
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    if (hours > 0) {
      return '${hours}h ${mins}m';
    }
    return '${mins}m';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sleep Tracking',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // Start Time Selection
        GestureDetector(
          onTap: _selectStartTime,
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'bedtime',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Sleep Start: ${TimeOfDay.fromDateTime(startTime).format(context)}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium,
                ),
                const Spacer(),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 2.h),

        // Currently Sleeping Toggle
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: isCurrentlySleeping
                ? AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.1)
                : AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCurrentlySleeping
                  ? AppTheme.lightTheme.colorScheme.primary
                  : AppTheme.lightTheme.colorScheme.outline
                      .withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              CustomIconWidget(
                iconName: 'nights_stay',
                color: isCurrentlySleeping
                    ? AppTheme.lightTheme.colorScheme.primary
                    : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                size: 20,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Currently Sleeping',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: isCurrentlySleeping
                        ? AppTheme.lightTheme.colorScheme.primary
                        : AppTheme.lightTheme.colorScheme.onSurface,
                    fontWeight:
                        isCurrentlySleeping ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ),
              Switch(
                value: isCurrentlySleeping,
                onChanged: _toggleCurrentlySleeping,
              ),
            ],
          ),
        ),

        // End Time Selection (only if not currently sleeping)
        if (!isCurrentlySleeping) ...[
          SizedBox(height: 2.h),
          GestureDetector(
            onTap: _selectEndTime,
            child: Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppTheme.lightTheme.colorScheme.outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'wb_sunny',
                    color: AppTheme.lightTheme.colorScheme.secondary,
                    size: 20,
                  ),
                  SizedBox(width: 3.w),
                  Text(
                    endTime != null
                        ? 'Wake Up: ${TimeOfDay.fromDateTime(endTime!).format(context)}'
                        : 'Set Wake Up Time',
                    style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                      color: endTime != null
                          ? AppTheme.lightTheme.colorScheme.onSurface
                          : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  CustomIconWidget(
                    iconName: 'keyboard_arrow_right',
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],

        // Duration Display (only if end time is set)
        if (endTime != null) ...[
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: AppTheme.getSuccessColor(true).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.getSuccessColor(true).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'timer',
                  color: AppTheme.getSuccessColor(true),
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Sleep Duration: ${_formatDuration(endTime!.difference(startTime).inMinutes)}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.getSuccessColor(true),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],

        // Currently Sleeping Indicator
        if (isCurrentlySleeping) ...[
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.primary
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.primary
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'schedule',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Sleep in progress since ${TimeOfDay.fromDateTime(startTime).format(context)}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
