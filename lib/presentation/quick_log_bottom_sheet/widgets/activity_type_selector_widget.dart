import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class ActivityTypeSelectorWidget extends StatelessWidget {
  final List<Map<String, dynamic>> activityTypes;
  final String? selectedType;
  final Function(String) onTypeSelected;

  const ActivityTypeSelectorWidget({
    super.key,
    required this.activityTypes,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What would you like to log?',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 3.w,
            mainAxisSpacing: 2.h,
            childAspectRatio: 1.2,
          ),
          itemCount: activityTypes.length,
          itemBuilder: (context, index) {
            final activity = activityTypes[index];
            final isSelected = selectedType == activity['type'];

            return GestureDetector(
              onTap: () => onTypeSelected(activity['type']),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                decoration: BoxDecoration(
                  color: isSelected
                      ? (activity['color'] as Color).withValues(alpha: 0.1)
                      : Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected
                        ? activity['color'] as Color
                        : Theme.of(context).colorScheme.outline
                            .withValues(alpha: 0.3),
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(3.w),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? activity['color'] as Color
                            : (activity['color'] as Color)
                                .withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: CustomIconWidget(
                        iconName: activity['icon'],
                        color: isSelected
                            ? Colors.white
                            : activity['color'] as Color,
                        size: 24,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      activity['label'],
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        color: isSelected
                            ? activity['color'] as Color
                            : Theme.of(context).colorScheme.onSurface,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
