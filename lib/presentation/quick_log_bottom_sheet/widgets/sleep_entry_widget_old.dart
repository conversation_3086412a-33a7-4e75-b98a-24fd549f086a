import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import '../../../theme/theme_aware_colors.dart';

class SleepEntryWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const SleepEntryWidget({
    super.key,
    required this.onDataChanged,
  });

  @override
  State<SleepEntryWidget> createState() => _SleepEntryWidgetState();
}

class _SleepEntryWidgetState extends State<SleepEntryWidget> {
  DateTime startTime = ModernDateTimePicker.getCurrentTime();
  DateTime? endTime;
  bool isCurrentlySleeping = true;

  @override
  void initState() {
    super.initState();
    // Defer initial data update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateData();
      }
    });
  }

  void _updateData() {
    widget.onDataChanged({
      'startTime': startTime,
      'endTime': endTime,
      'isCurrentlySleeping': isCurrentlySleeping,
      'duration': endTime?.difference(startTime).inMinutes,
    });
  }

  void _selectStartTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: startTime,
    );

    if (picked != null) {
      setState(() {
        startTime = picked;
        // Reset end time if it's before start time
        if (endTime != null && endTime!.isBefore(startTime)) {
          endTime = null;
          isCurrentlySleeping = true;
        }
      });
      _updateData();
    }
  }

  void _selectEndTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: endTime ?? startTime.add(const Duration(hours: 1)),
    );

    if (picked != null) {
      setState(() {
        endTime = picked;
        isCurrentlySleeping = false;
      });
      _updateData();
    }
  }

  void _toggleCurrentlySleeping(bool value) {
    setState(() {
      isCurrentlySleeping = value;
      if (value) {
        endTime = null;
      }
    });
    _updateData();
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    if (hours > 0) {
      return '${hours}h ${mins}m';
    }
    return '${mins}m';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sleep Tracking',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // Start Time Selection
        GestureDetector(
          onTap: _selectStartTime,
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'bedtime',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  ModernDateTimePicker.formatDateTime(startTime, context),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 2.h),

        // Currently Sleeping Toggle
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: isCurrentlySleeping
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                : Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCurrentlySleeping
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outline
                      .withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              CustomIconWidget(
                iconName: 'nights_stay',
                color: isCurrentlySleeping
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  'Currently Sleeping',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isCurrentlySleeping
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface,
                    fontWeight:
                        isCurrentlySleeping ? FontWeight.w500 : FontWeight.w400,
                  ),
                ),
              ),
              Switch(
                value: isCurrentlySleeping,
                onChanged: _toggleCurrentlySleeping,
              ),
            ],
          ),
        ),

        // End Time Selection (only if not currently sleeping)
        if (!isCurrentlySleeping) ...[
          SizedBox(height: 2.h),
          GestureDetector(
            onTap: _selectEndTime,
            child: Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'wb_sunny',
                    color: Theme.of(context).colorScheme.secondary,
                    size: 20,
                  ),
                  SizedBox(width: 3.w),
                  Text(
                  endTime != null
                      ? ModernDateTimePicker.formatDateTime(endTime!, context)
                      : 'Set Wake Up Time',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: endTime != null
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  CustomIconWidget(
                    iconName: 'keyboard_arrow_right',
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ],

        // Duration Display (only if end time is set)
        if (endTime != null) ...[
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'timer',
                  color: ThemeAwareColors.getSuccessColor(context),
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Sleep Duration: ${_formatDuration(endTime!.difference(startTime).inMinutes)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getSuccessColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],

        // Currently Sleeping Indicator
        if (isCurrentlySleeping) ...[
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary
                  .withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'schedule',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Sleep in progress since ${TimeOfDay.fromDateTime(startTime).format(context)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
