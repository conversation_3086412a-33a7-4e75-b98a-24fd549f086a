import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';


class MedicineEntryWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const MedicineEntryWidget({
    super.key,
    required this.onDataChanged,
  });

  @override
  State<MedicineEntryWidget> createState() => _MedicineEntryWidgetState();
}

class _MedicineEntryWidgetState extends State<MedicineEntryWidget> {
  String? selectedMedication;
  String customMedication = '';
  double? dosageAmount;
  String dosageUnit = 'ml';
  String customDosageUnit = '';
  String notes = '';
  DateTime selectedTime = ModernDateTimePicker.getCurrentTime();

  final TextEditingController dosageController = TextEditingController();
  final TextEditingController customMedicationController = TextEditingController();
  final TextEditingController customDosageUnitController = TextEditingController();
  final TextEditingController notesController = TextEditingController();

  final List<String> commonMedications = [
    'Paracetamol',
    'Ibuprofen',
    'Vitamin D',
    'Iron Supplement',
    'Probiotics',
    'Gripe Water',
    'Teething Gel',
    'Other',
  ];

  final List<String> dosageUnits = [
    'ml',
    'mg',
    'drops',
    'tablet(s)',
    'tsp',
    'tbsp',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    dosageController.addListener(() {
      final value = double.tryParse(dosageController.text);
      setState(() {
        dosageAmount = value;
      });
      _updateData();
    });
    customMedicationController.addListener(() {
      setState(() {
        customMedication = customMedicationController.text;
      });
      _updateData();
    });
    customDosageUnitController.addListener(() {
      setState(() {
        customDosageUnit = customDosageUnitController.text;
      });
      _updateData();
    });
    notesController.addListener(() {
      setState(() {
        notes = notesController.text;
      });
      _updateData();
    });
  }

  @override
  void dispose() {
    dosageController.dispose();
    customMedicationController.dispose();
    customDosageUnitController.dispose();
    notesController.dispose();
    super.dispose();
  }

  void _updateData() {
    final medicationName = selectedMedication == 'Other' ? customMedication : selectedMedication;
    final finalDosageUnit = dosageUnit == 'Other' ? customDosageUnit : dosageUnit;
    
    widget.onDataChanged({
      'medication': medicationName,
      'quantity': dosageAmount,
      'unit': finalDosageUnit,
      'notes': notes,
      'startTime': selectedTime,
    });
  }

  void _selectTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: selectedTime,
    );

    if (picked != null) {
      setState(() {
        selectedTime = picked;
      });
      _updateData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Medicine Administration',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // 1. Medication Selection (MOVED TO TOP)
        Text(
          'Medication',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline
                  .withValues(alpha: 0.3),
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: selectedMedication,
                hint: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 3.w),
                  child: Text(
                    'Select medication',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                isExpanded: true,
                items: commonMedications.map((medication) {
                  return DropdownMenuItem<String>(
                    value: medication,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                      child: Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'medication',
                            color: ThemeAwareColors.getWarningColor(context),
                            size: 16,
                          ),
                          SizedBox(width: 2.w),
                          Text(
                            medication,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedMedication = value;
                  });
                  _updateData();
                },
              ),
            ),
          ),
        ),

        // 2. Custom Medication Input (for "Other" option)
        if (selectedMedication == 'Other') ...[
          SizedBox(height: 2.h),
          Text(
            'Custom Medication Name',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Material(
            color: Colors.transparent,
            child: TextFormField(
              controller: customMedicationController,
              decoration: InputDecoration(
                hintText: 'Enter medication name',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(3.w),
                  child: CustomIconWidget(
                    iconName: 'medication',
                    color: ThemeAwareColors.getWarningColor(context),
                    size: 20,
                  ),
                ),
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],

        SizedBox(height: 3.h),

        // 3. Dosage Input with Unit Selection
        Text(
          'Dosage',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        Row(
          children: [
            Expanded(
              flex: 2,
              child: Material(
                color: Colors.transparent,
                child: TextFormField(
                  controller: dosageController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    hintText: 'Amount',
                    prefixIcon: Padding(
                      padding: EdgeInsets.all(3.w),
                      child: CustomIconWidget(
                        iconName: 'local_pharmacy',
                        color: ThemeAwareColors.getWarningColor(context),
                        size: 20,
                      ),
                    ),
                  ),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              flex: 1,
              child: Container(
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline
                        .withValues(alpha: 0.3),
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<String>(
                      value: dosageUnit,
                      isExpanded: true,
                      items: dosageUnits.map((unit) {
                        return DropdownMenuItem<String>(
                          value: unit,
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 3.w),
                            child: Text(
                              unit,
                              style: Theme.of(context).textTheme.bodyMedium,
                            ),
                          ),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          dosageUnit = value!;
                        });
                        _updateData();
                      },
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),

        // Custom Dosage Unit Input (for "Other" option)
        if (dosageUnit == 'Other') ...[
          SizedBox(height: 2.h),
          Text(
            'Custom Unit',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Material(
            color: Colors.transparent,
            child: TextFormField(
              controller: customDosageUnitController,
              decoration: InputDecoration(
                hintText: 'Enter custom unit (e.g., capsules, sachets)',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(3.w),
                  child: CustomIconWidget(
                    iconName: 'straighten',
                    color: ThemeAwareColors.getWarningColor(context),
                    size: 20,
                  ),
                ),
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],

        SizedBox(height: 3.h),

        // 4. Time Selection (MOVED BELOW DOSAGE)
        Text(
          'Administration Time',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        GestureDetector(
          onTap: _selectTime,
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'access_time',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  ModernDateTimePicker.formatDateTime(selectedTime, context),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),

        // 5. Notes Section (NEW)
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        Material(
          color: Colors.transparent,
          child: TextFormField(
            controller: notesController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Add any additional notes about the medication...',
              prefixIcon: Padding(
                padding: EdgeInsets.all(3.w),
                child: CustomIconWidget(
                  iconName: 'note_add',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ),
            ),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),

        SizedBox(height: 2.h),

        // Warning Message
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomIconWidget(
                iconName: 'warning',
                color: ThemeAwareColors.getWarningColor(context),
                size: 16,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  'Always consult your pediatrician before administering any medication to your baby.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getWarningColor(context),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Validation Messages
        if (_hasValidationErrors()) ...[
          SizedBox(height: 1.h),
          Container(
            padding: EdgeInsets.all(2.w),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'info',
                  color: Theme.of(context).colorScheme.error,
                  size: 14,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    _getValidationMessage(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  bool _hasValidationErrors() {
    if (selectedMedication == null) return true;
    if (selectedMedication == 'Other' && customMedication.isEmpty) return true;
    if (dosageAmount == null || dosageAmount! <= 0) return true;
    if (dosageUnit == 'Other' && customDosageUnit.isEmpty) return true;
    return false;
  }

  String _getValidationMessage() {
    if (selectedMedication == null) return 'Please select a medication';
    if (selectedMedication == 'Other' && customMedication.isEmpty) return 'Please enter custom medication name';
    if (dosageAmount == null || dosageAmount! <= 0) return 'Please enter a valid dosage amount';
    if (dosageUnit == 'Other' && customDosageUnit.isEmpty) return 'Please enter custom dosage unit';
    return '';
  }

  // Add this method to check if form is valid for the parent widget
  bool get isFormValid => !_hasValidationErrors();
}
