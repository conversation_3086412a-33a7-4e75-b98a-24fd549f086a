import 'dart:async';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import '../../../services/sleep_tracking_service.dart';
import 'base_activity_widget.dart';
import '../../../theme/theme_aware_colors.dart';

class SleepEntryWidget extends BaseActivityWidget {
  const SleepEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _SleepEntryWidgetState();
}

class _SleepEntryWidgetState extends BaseActivityWidgetState {
  DateTime startTime = ModernDateTimePicker.getCurrentTime();
  DateTime? endTime;
  bool isCurrentlySleeping = true;
  bool isLiveSleepMode = false;
  String sleepMode = 'live'; // 'live' or 'past'
  
  // Sleep Environment fields
  String sleepQuality = 'good';
  String sleepLocation = 'crib';
  double roomTemperature = 22.0;
  String customSleepLocation = '';
  
  final SleepTrackingService _sleepService = SleepTrackingService();
  final TextEditingController _customLocationController = TextEditingController();
  final TextEditingController _notesController = TextEditingController();
  
  Timer? _liveSessionTimer;
  Duration? _currentSessionDuration;
  Timer? _alarmTimer;
  DateTime? _alarmTime;
  
  final List<Map<String, dynamic>> qualityOptions = [
    {'value': 'poor', 'label': 'Poor', 'icon': 'sentiment_very_dissatisfied'},
    {'value': 'fair', 'label': 'Fair', 'icon': 'sentiment_dissatisfied'},
    {'value': 'good', 'label': 'Good', 'icon': 'sentiment_satisfied'},
    {'value': 'great', 'label': 'Great', 'icon': 'sentiment_very_satisfied'},
  ];
  
  final List<Map<String, dynamic>> locationOptions = [
    {'value': 'crib', 'label': 'Crib', 'icon': 'bed'},
    {'value': 'bassinet', 'label': 'Bassinet', 'icon': 'bed'},
    {'value': 'parent_bed', 'label': 'Parent Bed', 'icon': 'king_bed'},
    {'value': 'stroller', 'label': 'Stroller', 'icon': 'baby_changing_station'},
    {'value': 'car_seat', 'label': 'Car Seat', 'icon': 'car_rental'},
    {'value': 'other', 'label': 'Other', 'icon': 'other_houses'},
  ];

  @override
  void initState() {
    super.initState();
    _sleepService.initialize();
    _notesController.text = activityData['notes'] ?? '';
    
    // Check if there's an ongoing sleep session
    if (_sleepService.isSleeping) {
      isLiveSleepMode = true;
      isCurrentlySleeping = true;
      sleepMode = 'live';
      if (_sleepService.sleepStartTime != null) {
        startTime = _sleepService.sleepStartTime!;
        _startLiveSessionTimer();
      }
    }
    
    // Set up listeners
    _notesController.addListener(_onNotesChanged);
    _customLocationController.addListener(_onCustomLocationChanged);
    
    // Defer initial data update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateData();
      }
    });
  }

  void _onNotesChanged() {
    _updateDataSafely({'notes': _notesController.text});
  }

  void _onCustomLocationChanged() {
    if (sleepLocation == 'other') {
      _updateDataSafely({'custom_sleep_location': _customLocationController.text});
    }
  }

  void _updateDataSafely(Map<String, dynamic> newData) {
    final updatedData = Map<String, dynamic>.from(activityData);
    updatedData.addAll(newData);
    updateData(updatedData);
  }

  void _updateData() {
    final data = {
      'startTime': startTime,
      'endTime': endTime,
      'isCurrentlySleeping': isCurrentlySleeping,
      'duration': _getCurrentDuration(),
      'quality': sleepQuality,
      'sleepLocation': sleepLocation == 'other' ? _customLocationController.text : sleepLocation,
      'roomTemperature': roomTemperature,
      'sleep_mode': sleepMode,
      'is_live_session': isLiveSleepMode,
      'notes': _notesController.text,
      'time': startTime, // This is needed for the save functionality
    };
    
    updateData(data);
  }

  double? _getCurrentDuration() {
    if (isLiveSleepMode && isCurrentlySleeping) {
      return _currentSessionDuration?.inMinutes.toDouble();
    } else if (endTime != null) {
      return endTime!.difference(startTime).inMinutes.toDouble();
    }
    return null;
  }

  void _startLiveSessionTimer() {
    _liveSessionTimer?.cancel();
    _liveSessionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted && isLiveSleepMode) {
        setState(() {
          _currentSessionDuration = DateTime.now().difference(startTime);
        });
        _updateData();
      }
    });
  }

  void _selectStartTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: startTime,
    );

    if (picked != null) {
      setState(() {
        startTime = picked;
        // Reset end time if it's before start time
        if (endTime != null && endTime!.isBefore(startTime)) {
          endTime = null;
          isCurrentlySleeping = true;
        }
      });
      _updateData();
    }
  }

  void _selectEndTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: endTime ?? startTime.add(const Duration(hours: 1)),
    );

    if (picked != null) {
      setState(() {
        endTime = picked;
        isCurrentlySleeping = false;
      });
      _updateData();
    }
  }

  void _startLiveSleep() async {
    setState(() {
      isLiveSleepMode = true;
      isCurrentlySleeping = true;
      sleepMode = 'live';
      startTime = DateTime.now();
      endTime = null;
      _currentSessionDuration = null;
    });
    
    // Start the sleep tracking service
    await _sleepService.startSleep('current_baby_id'); // Replace with actual baby ID
    _startLiveSessionTimer();
    _updateData();
  }

  void _stopLiveSleep() async {
    setState(() {
      isLiveSleepMode = false;
      isCurrentlySleeping = false;
      endTime = DateTime.now();
    });
    
    _liveSessionTimer?.cancel();
    await _sleepService.stopSleep();
    _updateData();
  }

  void _setPastSleepMode() {
    setState(() {
      sleepMode = 'past';
      isLiveSleepMode = false;
      isCurrentlySleeping = false;
      startTime = DateTime.now().subtract(const Duration(hours: 2));
      endTime = DateTime.now();
    });
    _liveSessionTimer?.cancel();
    _updateData();
  }

  Future<void> _setWakeUpAlarm() async {
    final TimeOfDay? pickedTime = await ModernDateTimePicker.showTimePicker(
      context: context,
      initialTime: TimeOfDay.now().replacing(hour: TimeOfDay.now().hour + 2),
    );

    if (pickedTime != null) {
      final now = DateTime.now();
      final alarmTime = DateTime(
        now.year,
        now.month,
        now.day,
        pickedTime.hour,
        pickedTime.minute,
      );

      // If the alarm time is in the past, set it for tomorrow
      final finalAlarmTime = alarmTime.isBefore(now) 
          ? alarmTime.add(const Duration(days: 1))
          : alarmTime;

      await _scheduleWakeUpAlarm(finalAlarmTime);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Wake up alarm set for ${ModernDateTimePicker.formatTime(finalAlarmTime, context)}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    }
  }

  Future<void> _scheduleWakeUpAlarm(DateTime alarmTime) async {
    // Cancel any existing alarm
    _alarmTimer?.cancel();
    
    // Set the alarm time
    setState(() {
      _alarmTime = alarmTime;
    });
    
    // Calculate the duration until the alarm
    final now = DateTime.now();
    final duration = alarmTime.difference(now);
    
    // Only set timer if the alarm is in the future
    if (duration.isNegative) {
      return;
    }
    
    // Set up the alarm timer
    _alarmTimer = Timer(duration, () {
      if (mounted) {
        // Show wake-up alert
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => AlertDialog(
            title: Row(
              children: [
                Icon(Icons.alarm, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 2.w),
                Text('Wake Up Time!'),
              ],
            ),
            content: Text(
              'Time to wake up your baby and save the sleep session.',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    _alarmTime = null;
                  });
                },
                child: Text('Dismiss'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  _stopLiveSleep();
                },
                child: Text('Stop Sleep'),
              ),
            ],
          ),
        );
      }
    });
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  String _formatDuration(int minutes) {
    final hours = minutes ~/ 60;
    final mins = minutes % 60;
    if (hours > 0) {
      return '${hours}h ${mins}m';
    }
    return '${mins}m';
  }

  String _formatDurationFromDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    }
    return '${minutes}m';
  }

  @override
  void dispose() {
    _liveSessionTimer?.cancel();
    _alarmTimer?.cancel();
    _notesController.removeListener(_onNotesChanged);
    _customLocationController.removeListener(_onCustomLocationChanged);
    _notesController.dispose();
    _customLocationController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Sleep';

  @override
  String? getActivityDescription() => 'Track sleep sessions and environment';

  @override
  String getActivityType() => 'sleep';

  @override
  IconData getActivityIcon() => Icons.bedtime;

  @override
  Color getActivityColor() => const Color(0xFFF4A261);

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'startTime': startTime,
      'endTime': endTime,
      'isCurrentlySleeping': isCurrentlySleeping,
      'duration': _getCurrentDuration(),
      'quality': sleepQuality,
      'sleepLocation': sleepLocation == 'other' ? _customLocationController.text : sleepLocation,
      'roomTemperature': roomTemperature,
      'sleep_mode': sleepMode,
      'is_live_session': isLiveSleepMode,
      'notes': _notesController.text,
      'time': startTime,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildHeader(),
        SizedBox(height: 2.h),
        buildForm(),
        SizedBox(height: 3.h),
        _buildNotesSection(),
        SizedBox(height: 3.h),
        // Only show time selector for past sleep mode (this includes the save button)
        if (sleepMode == 'past') buildTimeSelector(),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),
        TextField(
          controller: _notesController,
          decoration: InputDecoration(
            hintText: 'Add any sleep notes...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  @override
  Widget buildForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sleep Tracking',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // Sleep Mode Selection Buttons
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: isLiveSleepMode ? _stopLiveSleep : _startLiveSleep,
                child: Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: sleepMode == 'live'
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: sleepMode == 'live'
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outline
                              .withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: isLiveSleepMode ? 'stop' : 'play_arrow',
                        color: sleepMode == 'live'
                            ? Colors.white
                            : Theme.of(context).colorScheme.primary,
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        isLiveSleepMode ? 'Stop Sleep' : 'Start Sleep',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: sleepMode == 'live'
                              ? Colors.white
                              : Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: GestureDetector(
                onTap: _setPastSleepMode,
                child: Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: sleepMode == 'past'
                        ? Theme.of(context).colorScheme.secondary
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: sleepMode == 'past'
                          ? Theme.of(context).colorScheme.secondary
                          : Theme.of(context).colorScheme.outline
                              .withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: 'history',
                        color: sleepMode == 'past'
                            ? Colors.white
                            : Theme.of(context).colorScheme.secondary,
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        'Past Sleep',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: sleepMode == 'past'
                              ? Colors.white
                              : Theme.of(context).colorScheme.secondary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 3.h),

        // Live Sleep Session Timer (only if in live mode)
        if (isLiveSleepMode) ...[
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'timer',
                      color: Theme.of(context).colorScheme.primary,
                      size: 24,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Live Sleep Session Active',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            _currentSessionDuration != null
                                ? 'Sleep since ${TimeOfDay.fromDateTime(startTime).format(context)} (${_formatDurationFromDuration(_currentSessionDuration!)})'
                                : 'Started: ${TimeOfDay.fromDateTime(startTime).format(context)}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                          // Show alarm time if set
                          if (_alarmTime != null) ...[
                            SizedBox(height: 0.5.h),
                            Text(
                              'Wake alarm set for ${ModernDateTimePicker.formatTime(_alarmTime!, context)}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.secondary,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).colorScheme.tertiary,
                        size: 16,
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          'Don\'t forget to stop the session when baby wakes up!',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.tertiary,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 2.h),
                // Center the Set Alarm button
                Center(
                  child: Container(
                    width: 60.w, // Make it 60% of screen width
                    child: ElevatedButton.icon(
                      onPressed: _setWakeUpAlarm,
                      icon: Icon(_alarmTime != null ? Icons.alarm : Icons.schedule, size: 18),
                      label: Text(
                        _alarmTime != null ? 'Update Alarm' : 'Set Wake Alarm',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _alarmTime != null 
                            ? Theme.of(context).colorScheme.tertiary
                            : Theme.of(context).colorScheme.secondary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 1.8.h, horizontal: 4.w),
                        elevation: 2,
                        shadowColor: (_alarmTime != null 
                            ? Theme.of(context).colorScheme.tertiary
                            : Theme.of(context).colorScheme.secondary).withOpacity(0.3),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 2.h),
        ],

        // Time Selection (only in past mode)
        if (sleepMode == 'past') ...[
          // Start Time Selection
          GestureDetector(
            onTap: _selectStartTime,
            child: Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'bedtime',
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                  SizedBox(width: 3.w),
                  Text(
                    'Start: ${ModernDateTimePicker.formatDateTime(startTime, context)}',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                  const Spacer(),
                  CustomIconWidget(
                    iconName: 'keyboard_arrow_right',
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 2.h),

          // End Time Selection
          GestureDetector(
            onTap: _selectEndTime,
            child: Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline
                      .withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'wb_sunny',
                    color: Theme.of(context).colorScheme.secondary,
                    size: 20,
                  ),
                  SizedBox(width: 3.w),
                  Text(
                    endTime != null
                        ? 'End: ${ModernDateTimePicker.formatDateTime(endTime!, context)}'
                        : 'Set Wake Up Time',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: endTime != null
                          ? Theme.of(context).colorScheme.onSurface
                          : Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  CustomIconWidget(
                    iconName: 'keyboard_arrow_right',
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
          SizedBox(height: 2.h),
        ],

        // Duration Display (only if end time is set)
        if (endTime != null) ...[
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'timer',
                  color: ThemeAwareColors.getSuccessColor(context),
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Sleep Duration: ${_formatDuration(endTime!.difference(startTime).inMinutes)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getSuccessColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 2.h),
        ],

        // Sleep Environment Section
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline
                  .withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sleep Environment',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 2.h),
              
              // Sleep Quality
              Text(
                'Sleep Quality',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1.h),
              Row(
                children: qualityOptions.map((option) {
                  final isSelected = sleepQuality == option['value'];
                  return Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right: qualityOptions.indexOf(option) == qualityOptions.length - 1 ? 0 : 2.w,
                      ),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            sleepQuality = option['value'];
                          });
                          _updateData();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 1.w),
                          decoration: BoxDecoration(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
                                : Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.outline
                                      .withValues(alpha: 0.3),
                            ),
                          ),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                _getIconData(option['icon']),
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                                size: 20,
                              ),
                              SizedBox(height: 0.5.h),
                              Text(
                                option['label'],
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: isSelected
                                      ? Theme.of(context).colorScheme.primary
                                      : Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ),
              SizedBox(height: 2.h),
              
              // Sleep Location
              Text(
                'Sleep Location',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1.h),
              Wrap(
                spacing: 2.w,
                runSpacing: 1.h,
                children: locationOptions.map((option) {
                  final isSelected = sleepLocation == option['value'];
                  return GestureDetector(
                    onTap: () {
                      setState(() {
                        sleepLocation = option['value'];
                      });
                      _updateData();
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 2.w),
                      decoration: BoxDecoration(
                        color: isSelected
                            ? Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1)
                            : Theme.of(context).colorScheme.surface,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: isSelected
                              ? Theme.of(context).colorScheme.secondary
                              : Theme.of(context).colorScheme.outline
                                  .withValues(alpha: 0.3),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getIconData(option['icon']),
                            color: isSelected
                                ? Theme.of(context).colorScheme.secondary
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                            size: 16,
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            option['label'],
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: isSelected
                                  ? Theme.of(context).colorScheme.secondary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
              ),
              SizedBox(height: 2.h),
              
              // Custom Location Input (only if "Other" is selected)
              if (sleepLocation == 'other') ...[
                TextField(
                  controller: _customLocationController,
                  decoration: InputDecoration(
                    hintText: 'Enter custom sleep location...',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  ),
                ),
                SizedBox(height: 2.h),
              ],
              
              // Room Temperature
              Text(
                'Room Temperature',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1.h),
              Row(
                children: [
                  Expanded(
                    child: Slider(
                      value: roomTemperature,
                      min: 15.0,
                      max: 30.0,
                      divisions: 30,
                      onChanged: (value) {
                        setState(() {
                          roomTemperature = value;
                        });
                        _updateData();
                      },
                      activeColor: Theme.of(context).colorScheme.primary,
                      inactiveColor: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 0.5.h, horizontal: 2.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${roomTemperature.toStringAsFixed(1)}°C',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'sentiment_very_dissatisfied':
        return Icons.sentiment_very_dissatisfied;
      case 'sentiment_dissatisfied':
        return Icons.sentiment_dissatisfied;
      case 'sentiment_satisfied':
        return Icons.sentiment_satisfied;
      case 'sentiment_very_satisfied':
        return Icons.sentiment_very_satisfied;
      case 'bed':
        return Icons.bed;
      case 'king_bed':
        return Icons.king_bed;
      case 'baby_changing_station':
        return Icons.baby_changing_station;
      case 'car_rental':
        return Icons.car_rental;
      case 'other_houses':
        return Icons.other_houses;
      case 'play_arrow':
        return Icons.play_arrow;
      case 'stop':
        return Icons.stop;
      case 'history':
        return Icons.history;
      case 'timer':
        return Icons.timer;
      case 'bedtime':
        return Icons.bedtime;
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'keyboard_arrow_right':
        return Icons.keyboard_arrow_right;
      case 'schedule':
        return Icons.schedule;
      default:
        return Icons.circle;
    }
  }
}
