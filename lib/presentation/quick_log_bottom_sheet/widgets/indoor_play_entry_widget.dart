import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class IndoorPlayEntryWidget extends BaseActivityWidget {
  const IndoorPlayEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _IndoorPlayEntryWidgetState();
}

class _IndoorPlayEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customActivityController = TextEditingController();
  final TextEditingController customLocationController = TextEditingController();
  final TextEditingController customToysController = TextEditingController();

  // Indoor activity types
  final List<String> activityOptions = [
    'Building Blocks',
    'Puzzle Time',
    'Arts & Crafts',
    'Reading Books',
    'Musical Play',
    'Pretend Play',
    'Board Games',
    'Drawing/Coloring',
    'Dancing',
    'Singing',
    'Educational Games',
    'Toy Cars/Trains',
    'Dolls/Action Figures',
    'Sensory Play',
    'Hide and Seek',
    'Indoor Obstacle Course',
    'Other',
  ];

  // Location types
  final List<String> locationOptions = [
    'Living Room',
    'Playroom',
    'Bedroom',
    'Kitchen',
    'Nursery',
    'Basement',
    'Dining Room',
    'Study Room',
    'Hallway',
    'Bathroom',
    'Other',
  ];

  // Toy/Material types
  final List<String> toyOptions = [
    'Building Blocks',
    'Lego/Duplo',
    'Puzzles',
    'Art Supplies',
    'Musical Instruments',
    'Stuffed Animals',
    'Action Figures',
    'Dolls',
    'Cars/Trucks',
    'Books',
    'Educational Toys',
    'Sensory Toys',
    'Board Games',
    'Craft Materials',
    'Play Kitchen',
    'Dress-up Clothes',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customActivityController.addListener(_updateCustomActivity);
    customLocationController.addListener(_updateCustomLocation);
    customToysController.addListener(_updateCustomToys);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      String? errorMessage;
      
      if (duration == null) {
        errorMessage = 'Please enter number only';
      } else if (duration < 0) {
        errorMessage = 'Please enter a positive number';
      }
      
      updateData({
        'duration_text': durationText,
        'duration': duration,
        'duration_error': errorMessage,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomActivity() {
    if (activityData['activity_selection'] == 'Other') {
      final customActivity = customActivityController.text.trim();
      updateData({
        'custom_activity': customActivity,
        'activity': customActivity.isNotEmpty ? customActivity : 'Other',
      });
    }
  }

  void _updateCustomLocation() {
    if (activityData['location_selection'] == 'Other') {
      final customLocation = customLocationController.text.trim();
      updateData({
        'custom_location': customLocation,
        'location': customLocation.isNotEmpty ? customLocation : 'Other',
      });
    }
  }

  void _updateCustomToys() {
    if (activityData['toys_selection'] == 'Other') {
      final customToys = customToysController.text.trim();
      updateData({
        'custom_toys': customToys,
        'toys': customToys.isNotEmpty ? customToys : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customActivityController.dispose();
    customLocationController.dispose();
    customToysController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Indoor Play';

  @override
  String? getActivityDescription() => 'Track indoor activities and creative play for cognitive development';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'activity_selection': 'Building Blocks',
      'activity': 'Building Blocks',
      'location_selection': 'Living Room',
      'location': 'Living Room',
      'toys_selection': 'Building Blocks',
      'toys': 'Building Blocks',
      'duration': null,
      'duration_text': '',
      'notes': '',
      'custom_activity': '',
      'custom_location': '',
      'custom_toys': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isActivityOtherSelected = activityData['activity_selection'] == 'Other';
    final isLocationOtherSelected = activityData['location_selection'] == 'Other';
    final isToysOtherSelected = activityData['toys_selection'] == 'Other';
    
    return Column(
      children: [
        // Activity type selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Activity Type',
          value: activityData['activity_selection'],
          items: activityOptions,
          onChanged: (value) {
            updateData({
              'activity_selection': value,
              'activity': value,
            });
            if (value != 'Other') {
              customActivityController.clear();
              updateData({'custom_activity': ''});
            }
          },
        ),
        
        // Show custom activity input when "Other" is selected
        if (isActivityOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Activity',
            controller: customActivityController,
            hint: 'Enter custom indoor activity...',
          ),
        ],
        
        // Location selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Location',
          value: activityData['location_selection'],
          items: locationOptions,
          onChanged: (value) {
            updateData({
              'location_selection': value,
              'location': value,
            });
            if (value != 'Other') {
              customLocationController.clear();
              updateData({'custom_location': ''});
            }
          },
        ),
        
        // Show custom location input when "Other" is selected
        if (isLocationOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Location',
            controller: customLocationController,
            hint: 'Enter custom location...',
          ),
        ],
        
        // Toys/Materials selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Toys/Materials Used',
          value: activityData['toys_selection'],
          items: toyOptions,
          onChanged: (value) {
            updateData({
              'toys_selection': value,
              'toys': value,
            });
            if (value != 'Other') {
              customToysController.clear();
              updateData({'custom_toys': ''});
            }
          },
        ),
        
        // Show custom toys input when "Other" is selected
        if (isToysOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Toys/Materials',
            controller: customToysController,
            hint: 'Enter toys or materials used...',
          ),
        ],
        
        // Duration input
        ActivityFormHelper.buildTextField(
          label: 'Duration (minutes)',
          controller: durationController,
          hint: 'How long was the indoor play?',
          keyboardType: TextInputType.number,
        ),
        
        // Show duration error if exists
        if (activityData['duration_error'] != null) ...[
          Container(
            margin: EdgeInsets.only(top: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    activityData['duration_error'],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 2.h),
        ],
        
        // Notes
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Any additional details about the indoor play session...',
          maxLines: 3,
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'indoor_play';

  @override
  IconData getActivityIcon() => Icons.toys;

  @override
  Color getActivityColor() => const Color(0xFF9C27B0);
}