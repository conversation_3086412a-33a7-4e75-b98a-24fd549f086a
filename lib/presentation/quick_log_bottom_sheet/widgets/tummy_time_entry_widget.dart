import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class TummyTimeEntryWidget extends BaseActivityWidget {
  const TummyTimeEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _TummyTimeEntryWidgetState();
}

class _TummyTimeEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customPositionController = TextEditingController();
  final TextEditingController customMoodController = TextEditingController();
  final TextEditingController customActivityController = TextEditingController();

  // Tummy time positions
  final List<String> positionOptions = [
    'On Chest (Skin-to-Skin)',
    'On Lap',
    'On Play Mat',
    'On Bed',
    'On Blanket',
    'On Exercise Ball',
    'Other',
  ];

  // Baby's mood during tummy time
  final List<String> moodOptions = [
    'Happy & Engaged',
    'Calm & Relaxed',
    'Fussy but Tolerated',
    'Crying/Upset',
    'Sleepy',
    'Alert & Active',
    'Other',
  ];

  // Tummy time activities
  final List<String> activityOptions = [
    'Just Lying',
    'Looking at Toys',
    'Reaching for Objects',
    'Lifting Head',
    'Pushing Up',
    'Rolling Attempts',
    'Playing with Caregiver',
    'Mirror Play',
    'Music/Singing',
    'Reading Books',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customPositionController.addListener(_updateCustomPosition);
    customMoodController.addListener(_updateCustomMood);
    customActivityController.addListener(_updateCustomActivity);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      updateData({
        'duration_text': durationText, // Keep original for display
        'duration': duration, // Numeric value for validation and storage
        'duration_error': duration == null || duration < 0 ? 'Please enter a valid positive number' : null,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomPosition() {
    if (activityData['position_selection'] == 'Other') {
      final customPosition = customPositionController.text.trim();
      updateData({
        'custom_position': customPosition,
        'position': customPosition.isNotEmpty ? customPosition : 'Other',
      });
    }
  }

  void _updateCustomMood() {
    if (activityData['mood_selection'] == 'Other') {
      final customMood = customMoodController.text.trim();
      updateData({
        'custom_mood': customMood,
        'mood': customMood.isNotEmpty ? customMood : 'Other',
      });
    }
  }

  void _updateCustomActivity() {
    if (activityData['activity_selection'] == 'Other') {
      final customActivity = customActivityController.text.trim();
      updateData({
        'custom_activity': customActivity,
        'activity': customActivity.isNotEmpty ? customActivity : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customPositionController.dispose();
    customMoodController.dispose();
    customActivityController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Tummy Time';

  @override
  String? getActivityDescription() => 'Track tummy time sessions for healthy development';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'position_selection': 'On Play Mat', // For dropdown display
      'position': 'On Play Mat', // For actual saving
      'mood_selection': 'Happy & Engaged', // For dropdown display
      'mood': 'Happy & Engaged', // For actual saving
      'activity_selection': 'Just Lying', // For dropdown display
      'activity': 'Just Lying', // For actual saving
      'duration': null, // Duration in minutes
      'duration_text': '', // Text input for display
      'notes': '',
      'custom_position': '',
      'custom_mood': '',
      'custom_activity': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isPositionOtherSelected = activityData['position_selection'] == 'Other';
    final isMoodOtherSelected = activityData['mood_selection'] == 'Other';
    final isActivityOtherSelected = activityData['activity_selection'] == 'Other';
    
    return Column(
      children: [
        // Activity selector (1st)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Activity',
          value: activityData['activity_selection'],
          items: activityOptions,
          onChanged: (value) {
            updateData({
              'activity_selection': value,
              'activity': value,
            });
            // Clear custom activity if not "Other"
            if (value != 'Other') {
              customActivityController.clear();
              updateData({'custom_activity': ''});
            }
          },
        ),
        
        // Show custom activity input when "Other" is selected
        if (isActivityOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Activity',
            controller: customActivityController,
            hint: 'Enter custom activity...',
          ),
        ],
        
        // Position selector (2nd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Position',
          value: activityData['position_selection'],
          items: positionOptions,
          onChanged: (value) {
            updateData({
              'position_selection': value,
              'position': value,
            });
            // Clear custom position if not "Other"
            if (value != 'Other') {
              customPositionController.clear();
              updateData({'custom_position': ''});
            }
          },
        ),
        
        // Show custom position input when "Other" is selected
        if (isPositionOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Position',
            controller: customPositionController,
            hint: 'Enter custom position...',
          ),
        ],
        
        // Mood selector (3rd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Baby\'s Mood',
          value: activityData['mood_selection'],
          items: moodOptions,
          onChanged: (value) {
            updateData({
              'mood_selection': value,
              'mood': value,
            });
            // Clear custom mood if not "Other"
            if (value != 'Other') {
              customMoodController.clear();
              updateData({'custom_mood': ''});
            }
          },
        ),
        
        // Show custom mood input when "Other" is selected
        if (isMoodOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Mood',
            controller: customMoodController,
            hint: 'Enter custom mood...',
          ),
        ],
        
        // Duration input with validation (4th)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Material(
              child: TextFormField(
                controller: durationController,
                decoration: InputDecoration(
                  labelText: 'Duration (minutes)',
                  hintText: 'Enter duration (e.g., 5, 10, 15)',
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                keyboardType: TextInputType.number,
                inputFormatters: [
                  FilteringTextInputFormatter.digitsOnly,
                ],
              ),
            ),
            // Show validation error if duration is invalid
            if (activityData.containsKey('duration_error') && activityData['duration_error'] != null) ...[
              Padding(
                padding: EdgeInsets.only(top: 1.h, left: 4.w),
                child: Text(
                  activityData['duration_error'].toString(),
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
            SizedBox(height: 2.h),
          ],
        ),
        
        // Development progress indicator
        if (activityData.containsKey('duration') && 
            activityData['duration'] != null && 
            activityData['duration'] is int && 
            activityData['duration'] > 0) ...[
          _buildDevelopmentIndicator(),
        ],
        
        // Notes field (6th)
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Observations, milestones, reactions...',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildDevelopmentIndicator() {
    final duration = activityData['duration'] as int?;
    final mood = activityData['mood'] as String?;
    
    if (duration == null) return Container();
    
    // Determine development assessment based on duration and mood
    String assessment;
    Color assessmentColor;
    IconData assessmentIcon;
    String encouragement;
    
    if (duration >= 15) {
      assessment = 'Excellent Session!';
      assessmentColor = Colors.green;
      assessmentIcon = Icons.star;
      encouragement = 'Great job! This is fantastic for development.';
    } else if (duration >= 10) {
      assessment = 'Good Progress';
      assessmentColor = Colors.lightGreen;
      assessmentIcon = Icons.thumb_up;
      encouragement = 'Well done! Building up nicely.';
    } else if (duration >= 5) {
      assessment = 'Good Start';
      assessmentColor = Colors.blue;
      assessmentIcon = Icons.trending_up;
      encouragement = 'Every minute counts for development!';
    } else if (duration >= 2) {
      assessment = 'Building Tolerance';
      assessmentColor = Colors.orange;
      assessmentIcon = Icons.fitness_center;
      encouragement = 'Keep practicing - tolerance will improve!';
    } else {
      assessment = 'Short Session';
      assessmentColor = Colors.amber;
      assessmentIcon = Icons.timer;
      encouragement = 'Even short sessions help build strength!';
    }
    
    // Adjust assessment based on mood
    if (mood != null && mood.contains('Crying')) {
      assessment = 'Challenging Session';
      assessmentColor = Colors.orange;
      assessmentIcon = Icons.psychology;
      encouragement = 'It\'s okay! Some days are harder than others.';
    } else if (mood != null && mood.contains('Happy')) {
      encouragement = '$encouragement Baby is enjoying it!';
    }
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: assessmentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: assessmentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            assessmentIcon,
            color: assessmentColor,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  assessment,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: assessmentColor,
                  ),
                ),
                Text(
                  encouragement,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: assessmentColor.withValues(alpha: 0.8),
                  ),
                ),
                if (duration > 0) ...[
                  SizedBox(height: 1.h),
                  Text(
                    '${duration} minutes of development time',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: assessmentColor.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  String getActivityType() => 'tummy_time';

  @override
  IconData getActivityIcon() => Icons.fitness_center;

  @override
  Color getActivityColor() => const Color(0xFF10B981);
}