import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class MedicineEntryWidget extends BaseActivityWidget {
  const MedicineEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _MedicineEntryWidgetState();
}

class _MedicineEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController dosageController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customMedicineController = TextEditingController();

  // Common baby and kids medicines (A-Z order)
  final List<String> medicineOptions = [
    'Acetaminophen',
    'Advil',
    'Amoxicillin',
    'Benadryl',
    'Calpol',
    'Cetirizine',
    'Dimetapp',
    'Gripe Water',
    'Ibuprofen',
    'Iron Supplement',
    'Loratadine',
    'Motrin',
    'Mucinex',
    'Nurofen',
    'Paracetamol',
    'Probiotics',
    'Robitussin',
    'Saline Drops',
    'Simethicone',
    'Tylenol',
    'Vitamin D Drops',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    dosageController.addListener(_updateDosage);
    notesController.addListener(_updateNotes);
    customMedicineController.addListener(_updateCustomMedicine);
    
    // Initialize dosage controller with initial data
    if (activityData['dosage'] != null) {
      dosageController.text = activityData['dosage'].toString();
    }
    if (activityData['notes'] != null) {
      notesController.text = activityData['notes'].toString();
    }
  }

  void _updateDosage() {
    final dosageText = dosageController.text.trim();
    if (dosageText.isNotEmpty) {
      // Parse dosage into quantity and unit
      final parsed = _parseDosage(dosageText);
      updateData({
        'dosage': dosageText, // Keep original for display
        'quantity': parsed['quantity'],
        'unit': parsed['unit'],
      });
    } else {
      updateData({
        'dosage': '',
        'quantity': null,
        'unit': null,
      });
    }
  }

  Map<String, dynamic> _parseDosage(String dosageText) {
    // Try to extract number and unit from dosage text
    final RegExp numberRegex = RegExp(r'(\d+(?:\.\d+)?)');
    final match = numberRegex.firstMatch(dosageText);
    
    if (match != null) {
      final numberStr = match.group(1)!;
      final quantity = double.tryParse(numberStr) ?? 1.0;
      
      // Extract unit by removing the number and trimming
      String unit = dosageText.replaceFirst(numberStr, '').trim();
      
      // Handle common unit variations - must match database constraint
      if (unit.isEmpty) {
        unit = 'ml'; // Default unit (must be valid)
      } else if (unit.toLowerCase().contains('tablet')) {
        unit = 'tablet(s)'; // Database expects plural form
      } else if (unit.toLowerCase().contains('ml')) {
        unit = 'ml';
      } else if (unit.toLowerCase().contains('tsp') || unit.toLowerCase().contains('teaspoon')) {
        unit = 'tsp';
      } else if (unit.toLowerCase().contains('tbsp') || unit.toLowerCase().contains('tablespoon')) {
        unit = 'tbsp';
      } else if (unit.toLowerCase().contains('drop')) {
        unit = 'drops';
      } else if (unit.toLowerCase().contains('mg')) {
        unit = 'mg';
      } else {
        // For any unrecognized unit, default to ml to satisfy database constraint
        unit = 'ml';
      }
      
      return {'quantity': quantity, 'unit': unit};
    } else {
      // If no number found, treat as 1 ml (default valid unit)
      return {'quantity': 1.0, 'unit': 'ml'};
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomMedicine() {
    if (activityData['medication_selection'] == 'Other') {
      final customMedicine = customMedicineController.text.trim();
      updateData({
        'custom_medicine': customMedicine,
        // Update the actual medication field for saving, but keep dropdown selection as 'Other'
        'medication': customMedicine.isNotEmpty ? customMedicine : 'Other',
      });
    }
  }

  @override
  void dispose() {
    dosageController.dispose();
    notesController.dispose();
    customMedicineController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Medicine';

  @override
  String? getActivityDescription() => 'Record medication and dosage details';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'medication_selection': 'Paracetamol', // For dropdown display
      'medication': 'Paracetamol', // For actual saving
      'dosage': '',
      'quantity': null, // Numeric quantity for database
      'unit': null, // Unit string for database
      'notes': '',
      'custom_medicine': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isOtherSelected = activityData['medication_selection'] == 'Other';
    
    return Column(
      children: [
        ActivityFormHelper.buildDropdown<String>(
          label: 'Medication',
          value: activityData['medication_selection'],
          items: medicineOptions,
          onChanged: (value) {
            updateData({
              'medication_selection': value,
              'medication': value, // Initially set to same value
            });
            // Clear custom medicine if not "Other"
            if (value != 'Other') {
              customMedicineController.clear();
              updateData({'custom_medicine': ''});
            }
          },
        ),
        
        // Show custom medicine input when "Other" is selected
        if (isOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Medicine Name',
            controller: customMedicineController,
            hint: 'Enter medicine name...',
          ),
        ],
        
        ActivityFormHelper.buildTextField(
          label: 'Dosage',
          controller: dosageController,
          hint: 'Enter dosage (e.g., 5ml, 1 tablet, 3 drops)',
        ),
        
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Additional notes...',
          maxLines: 3,
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'medicine';

  @override
  IconData getActivityIcon() => Icons.medication;

  @override
  Color getActivityColor() => const Color(0xFFE76F51);
}
