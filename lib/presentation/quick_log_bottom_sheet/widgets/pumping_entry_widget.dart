import 'dart:async';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../../core/app_export.dart';
import '../../../services/measurement_units_service.dart';
import 'base_activity_widget.dart';

class PumpingEntryWidget extends BaseActivityWidget {
  const PumpingEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _PumpingEntryWidgetState();
}

class _PumpingEntryWidgetState extends BaseActivityWidgetState {
  bool isCustomAmountActive = false;
  bool isCustomDurationActive = false;
  bool _isSliderUpdating = false; // Flag to prevent text controller interference
  final TextEditingController customAmountController = TextEditingController();
  final TextEditingController customDurationController = TextEditingController();
  final TextEditingController localNotesController = TextEditingController();
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    
    // Initialize all controllers - start empty for custom inputs
    customAmountController.text = '';
    customDurationController.text = '';
    localNotesController.text = activityData['notes'] ?? '';
    
    // Set up listeners that preserve all data
    customAmountController.addListener(_onCustomAmountChanged);
    customDurationController.addListener(_onCustomDurationChanged);
    localNotesController.addListener(_onNotesChanged);
  }

  void _onCustomAmountChanged() {
    // Don't activate custom mode if the slider is updating the text field
    if (_isSliderUpdating) return;
    
    final value = double.tryParse(customAmountController.text);
    if (value != null && value > 0) {
      setState(() {
        isCustomAmountActive = true;
      });
      _updateDataSafely({'amount': value});
    }
  }

  void _onCustomDurationChanged() {
    // Don't activate custom mode if the slider is updating the text field
    if (_isSliderUpdating) return;
    
    final value = double.tryParse(customDurationController.text);
    if (value != null && value > 0) {
      setState(() {
        isCustomDurationActive = true;
      });
      _updateDataSafely({'duration': value});
    }
  }

  void _onNotesChanged() {
    // Use a debounced update to avoid excessive rebuilds while typing
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _updateDataSafely({'notes': localNotesController.text});
    });
  }

  void _updateDataSafely(Map<String, dynamic> newData) {
    // Preserve all existing data and only update specific fields
    final updatedData = Map<String, dynamic>.from(activityData);
    updatedData.addAll(newData);
    updateData(updatedData);
  }

  // Override the base notes section to use our local controller
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildHeader(),
        SizedBox(height: 2.h),
        buildForm(),
        SizedBox(height: 3.h),
        _buildLocalNotesSection(),
        SizedBox(height: 3.h),
        buildTimeSelector(),
      ],
    );
  }

  Widget _buildLocalNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),
        TextField(
          controller: localNotesController,
          decoration: InputDecoration(
            hintText: 'Add any additional notes...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    customAmountController.removeListener(_onCustomAmountChanged);
    customDurationController.removeListener(_onCustomDurationChanged);
    localNotesController.removeListener(_onNotesChanged);
    customAmountController.dispose();
    customDurationController.dispose();
    localNotesController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Pumping';

  @override
  String? getActivityDescription() => 'Record breast milk pumping sessions';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'amount': 120.0,
      'side': 'both',
      'time': DateTime.now(),
      'notes': '',
    };
  }

  @override
  Widget buildForm() {
    return Column(
      children: [
        // Amount Slider (hidden when custom input is active)
        if (!isCustomAmountActive) ...[
          ActivityFormHelper.buildSlider(
            label: 'Amount',
            value: activityData['amount'] ?? 120.0,
            min: 30,
            max: 300,
            divisions: 27,
            unit: context.watch<MeasurementUnitsService>().volumeUnit,
            onChanged: (value) {
              updateData({'amount': value});
            },
          ),
          SizedBox(height: 2.h),
        ],
        
        // Custom Amount Input
        Row(
          children: [
            Text(
              'Custom Amount:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 2.w),
            Expanded(
              child: TextField(
                controller: customAmountController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  hintText: 'Enter amount',
                  suffixText: 'ml',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                ),
                onChanged: (value) {
                  // Remove any non-numeric characters except decimal point
                  final cleanValue = value.replaceAll(RegExp(r'[^0-9.]'), '');
                  
                  if (cleanValue != value) {
                    // Update the text field with cleaned value
                    customAmountController.value = customAmountController.value.copyWith(
                      text: cleanValue,
                      selection: TextSelection.collapsed(offset: cleanValue.length),
                    );
                  }
                  
                  final parsedValue = double.tryParse(cleanValue);
                  if (parsedValue != null && parsedValue > 0) {
                    setState(() {
                      isCustomAmountActive = true;
                    });
                    updateData({'amount': parsedValue});
                  } else if (cleanValue.isEmpty) {
                    setState(() {
                      isCustomAmountActive = false;
                    });
                    updateData({'amount': 120.0}); // Reset to default
                  }
                },
                onTap: () {
                  setState(() {
                    isCustomAmountActive = true;
                  });
                },
              ),
            ),
            if (isCustomAmountActive) ...[
              SizedBox(width: 2.w),
              TextButton(
                onPressed: () {
                  setState(() {
                    isCustomAmountActive = false;
                    customAmountController.text = '';
                  });
                  updateData({'amount': 120.0}); // Reset to default
                },
                child: Text(
                  'Reset',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
        SizedBox(height: 2.h),

        // Duration Section (Optional)
        Text(
          'Duration (Optional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),
        
        // Duration Slider (hidden when custom input is active or no duration set)
        if (!isCustomDurationActive && activityData['duration'] != null) ...[
          ActivityFormHelper.buildSlider(
            label: 'Duration',
            value: activityData['duration'] ?? 15.0,
            min: 5,
            max: 60,
            divisions: 11,
            unit: 'min',
            onChanged: (value) {
              updateData({'duration': value});
            },
          ),
          SizedBox(height: 2.h),
        ],
        
        // Custom Duration Input
        Row(
          children: [
            Text(
              'Duration:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 2.w),
            Expanded(
              child: TextField(
                controller: customDurationController,
                keyboardType: TextInputType.numberWithOptions(decimal: true),
                decoration: InputDecoration(
                  hintText: 'Enter duration (optional)',
                  suffixText: 'min',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                ),
                onChanged: (value) {
                  // Remove any non-numeric characters except decimal point
                  final cleanValue = value.replaceAll(RegExp(r'[^0-9.]'), '');
                  
                  if (cleanValue != value) {
                    // Update the text field with cleaned value
                    customDurationController.value = customDurationController.value.copyWith(
                      text: cleanValue,
                      selection: TextSelection.collapsed(offset: cleanValue.length),
                    );
                  }
                  
                  final parsedValue = double.tryParse(cleanValue);
                  if (parsedValue != null && parsedValue > 0) {
                    setState(() {
                      isCustomDurationActive = true;
                    });
                    updateData({'duration': parsedValue});
                  } else if (cleanValue.isEmpty) {
                    setState(() {
                      isCustomDurationActive = false;
                    });
                    // Remove duration from data when empty
                    final updatedData = Map<String, dynamic>.from(activityData);
                    updatedData.remove('duration');
                    updateData(updatedData);
                  }
                },
                onTap: () {
                  setState(() {
                    isCustomDurationActive = true;
                  });
                },
              ),
            ),
          ],
        ),
        SizedBox(height: 2.h),
        
        ActivityFormHelper.buildSelectionRow(
          label: 'Side',
          options: [
            {'label': 'Left', 'value': 'left', 'icon': 'local_drink'},
            {'label': 'Right', 'value': 'right', 'icon': 'local_drink'},
            {'label': 'Left & Right', 'value': 'both', 'icon': 'local_drink'},
          ],
          selectedValue: activityData['side'] ?? 'both',
          onChanged: (value) => updateData({'side': value}),
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'pumping';

  @override
  IconData getActivityIcon() => Icons.local_drink;

  @override
  Color getActivityColor() => const Color(0xFF9B59B6);
}