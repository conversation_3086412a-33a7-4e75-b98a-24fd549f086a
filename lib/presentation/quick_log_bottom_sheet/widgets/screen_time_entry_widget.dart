import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class ScreenTimeEntryWidget extends BaseActivityWidget {
  const ScreenTimeEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _ScreenTimeEntryWidgetState();
}

class _ScreenTimeEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customContentController = TextEditingController();
  final TextEditingController customDeviceController = TextEditingController();
  final TextEditingController customPurposeController = TextEditingController();

  // Screen time content types
  final List<String> contentOptions = [
    'Educational Videos',
    'Interactive Learning Apps',
    'Music & Songs',
    'Story Time Videos',
    'Animal Videos',
    'Nature Documentaries',
    'Art & Creativity Apps',
    'Simple Games',
    'Video Calls with Family',
    'Calming/Sleep Content',
    'Other',
  ];

  // Device types
  final List<String> deviceOptions = [
    'Tablet',
    'Phone',
    'TV',
    'Computer',
    'Smart Display',
    'Other',
  ];

  // Purpose/Context
  final List<String> purposeOptions = [
    'Educational Learning',
    'Entertainment',
    'Calming/Soothing',
    'Family Connection',
    'Distraction (Medical)',
    'Travel/Car Ride',
    'Meal Time',
    'Quiet Time',
    'Weather/Indoor Day',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customContentController.addListener(_updateCustomContent);
    customDeviceController.addListener(_updateCustomDevice);
    customPurposeController.addListener(_updateCustomPurpose);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      String? errorMessage;
      
      if (duration == null) {
        errorMessage = 'Please enter number only';
      } else if (duration < 0) {
        errorMessage = 'Please enter a positive number';
      }
      
      updateData({
        'duration_text': durationText, // Keep original for display
        'duration': duration, // Numeric value for validation and storage
        'duration_error': errorMessage,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomContent() {
    if (activityData['content_selection'] == 'Other') {
      final customContent = customContentController.text.trim();
      updateData({
        'custom_content': customContent,
        'content': customContent.isNotEmpty ? customContent : 'Other',
      });
    }
  }

  void _updateCustomDevice() {
    if (activityData['device_selection'] == 'Other') {
      final customDevice = customDeviceController.text.trim();
      updateData({
        'custom_device': customDevice,
        'device': customDevice.isNotEmpty ? customDevice : 'Other',
      });
    }
  }

  void _updateCustomPurpose() {
    if (activityData['purpose_selection'] == 'Other') {
      final customPurpose = customPurposeController.text.trim();
      updateData({
        'custom_purpose': customPurpose,
        'purpose': customPurpose.isNotEmpty ? customPurpose : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customContentController.dispose();
    customDeviceController.dispose();
    customPurposeController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Screen Time';

  @override
  String? getActivityDescription() => 'Track educational and entertainment screen time';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'content_selection': 'Educational Videos', // For dropdown display
      'content': 'Educational Videos', // For actual saving
      'device_selection': 'Tablet', // For dropdown display
      'device': 'Tablet', // For actual saving
      'purpose_selection': 'Educational Learning', // For dropdown display
      'purpose': 'Educational Learning', // For actual saving
      'duration': null, // Duration in minutes
      'duration_text': '', // Text input for display
      'notes': '',
      'custom_content': '',
      'custom_device': '',
      'custom_purpose': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isContentOtherSelected = activityData['content_selection'] == 'Other';
    final isDeviceOtherSelected = activityData['device_selection'] == 'Other';
    final isPurposeOtherSelected = activityData['purpose_selection'] == 'Other';
    
    return Column(
      children: [
        // Content type selector (1st)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Content Type',
          value: activityData['content_selection'],
          items: contentOptions,
          onChanged: (value) {
            updateData({
              'content_selection': value,
              'content': value,
            });
            // Clear custom content if not "Other"
            if (value != 'Other') {
              customContentController.clear();
              updateData({'custom_content': ''});
            }
          },
        ),
        
        // Show custom content input when "Other" is selected
        if (isContentOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Content Type',
            controller: customContentController,
            hint: 'Enter custom content type...',
          ),
        ],
        
        // Device selector (2nd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Device Used',
          value: activityData['device_selection'],
          items: deviceOptions,
          onChanged: (value) {
            updateData({
              'device_selection': value,
              'device': value,
            });
            // Clear custom device if not "Other"
            if (value != 'Other') {
              customDeviceController.clear();
              updateData({'custom_device': ''});
            }
          },
        ),
        
        // Show custom device input when "Other" is selected
        if (isDeviceOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Device',
            controller: customDeviceController,
            hint: 'Enter custom device...',
          ),
        ],
        
        // Purpose selector (3rd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Purpose/Context',
          value: activityData['purpose_selection'],
          items: purposeOptions,
          onChanged: (value) {
            updateData({
              'purpose_selection': value,
              'purpose': value,
            });
            // Clear custom purpose if not "Other"
            if (value != 'Other') {
              customPurposeController.clear();
              updateData({'custom_purpose': ''});
            }
          },
        ),
        
        // Show custom purpose input when "Other" is selected
        if (isPurposeOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Purpose',
            controller: customPurposeController,
            hint: 'Enter custom purpose...',
          ),
        ],
        
        // Duration input with validation (4th)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Material(
              child: TextFormField(
                controller: durationController,
                decoration: InputDecoration(
                  labelText: 'Duration (minutes)',
                  hintText: 'Enter duration (e.g., 5, 10, 15)',
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            // Show validation error if duration is invalid
            if (activityData.containsKey('duration_error') && activityData['duration_error'] != null) ...[
              Padding(
                padding: EdgeInsets.only(top: 1.h, left: 4.w),
                child: Text(
                  activityData['duration_error'].toString(),
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
            SizedBox(height: 2.h),
          ],
        ),
        
        // Screen time guidance indicator
        if (activityData.containsKey('duration') && 
            activityData['duration'] != null && 
            activityData['duration'] is int && 
            activityData['duration'] > 0) ...[
          _buildScreenTimeGuidance(),
        ],
        
        // Notes field (6th)
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'App names, reactions, learning outcomes, behavior...',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildScreenTimeGuidance() {
    final duration = activityData['duration'] as int?;
    final purpose = activityData['purpose'] as String?;
    
    if (duration == null) return Container();
    
    // Screen time guidance based on AAP recommendations
    String guidance;
    Color guidanceColor;
    IconData guidanceIcon;
    String recommendation;
    
    if (duration <= 15) {
      guidance = 'Appropriate Duration';
      guidanceColor = Colors.green;
      guidanceIcon = Icons.check_circle;
      recommendation = 'Great! Short, focused screen time is ideal for young children.';
    } else if (duration <= 30) {
      guidance = 'Moderate Duration';
      guidanceColor = Colors.blue;
      guidanceIcon = Icons.info;
      recommendation = 'Good balance. Consider interactive content for longer sessions.';
    } else if (duration <= 60) {
      guidance = 'Extended Session';
      guidanceColor = Colors.orange;
      guidanceIcon = Icons.schedule;
      recommendation = 'Consider breaks and interactive elements for longer sessions.';
    } else {
      guidance = 'Long Session';
      guidanceColor = Colors.amber;
      guidanceIcon = Icons.warning;
      recommendation = 'Very long session. Consider breaking into smaller segments.';
    }
    
    // Adjust guidance based on purpose
    if (purpose != null && purpose.contains('Educational')) {
      recommendation = '$recommendation Educational content adds value!';
    } else if (purpose != null && purpose.contains('Family Connection')) {
      recommendation = '$recommendation Video calls are great for social development!';
    } else if (purpose != null && purpose.contains('Calming')) {
      recommendation = '$recommendation Calming content can be helpful when needed.';
    }
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: guidanceColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: guidanceColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            guidanceIcon,
            color: guidanceColor,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  guidance,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: guidanceColor,
                  ),
                ),
                Text(
                  recommendation,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: guidanceColor.withValues(alpha: 0.8),
                  ),
                ),
                if (duration > 0) ...[
                  SizedBox(height: 1.h),
                  Text(
                    '$duration minutes of screen time',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: guidanceColor.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  String getActivityType() => 'screen_time';

  @override
  IconData getActivityIcon() => Icons.tv;

  @override
  Color getActivityColor() => const Color(0xFF607D8B);
}