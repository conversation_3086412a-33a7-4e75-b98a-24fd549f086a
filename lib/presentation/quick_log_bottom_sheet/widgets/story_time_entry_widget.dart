import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class StoryTimeEntryWidget extends BaseActivityWidget {
  const StoryTimeEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _StoryTimeEntryWidgetState();
}

class _StoryTimeEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customBookController = TextEditingController();
  final TextEditingController customMoodController = TextEditingController();
  final TextEditingController customActivityController = TextEditingController();

  // Story time book types
  final List<String> bookOptions = [
    'Picture Book',
    'Board Book',
    'Interactive Book',
    'Touch & Feel Book',
    'Pop-up Book',
    'Nursery Rhymes',
    'Fairy Tales',
    'Educational Book',
    'Bedtime Stories',
    'Poetry Book',
    'Other',
  ];

  // Baby's engagement during story time
  final List<String> moodOptions = [
    'Highly Engaged',
    'Listening Quietly',
    'Looking at Pictures',
    'Touching/Exploring',
    'Distracted',
    'Fussy',
    'Sleepy',
    'Interactive/Talking',
    'Other',
  ];

  // Story time activities
  final List<String> activityOptions = [
    'Reading Aloud',
    'Looking at Pictures',
    'Pointing & Naming',
    'Making Sound Effects',
    'Singing Songs',
    'Acting Out Story',
    'Asking Questions',
    'Baby "Reading" Alone',
    'Interactive Discussion',
    'Quiet Listening',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customBookController.addListener(_updateCustomBook);
    customMoodController.addListener(_updateCustomMood);
    customActivityController.addListener(_updateCustomActivity);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      String? errorMessage;
      
      if (duration == null) {
        errorMessage = 'Please enter number only';
      } else if (duration < 0) {
        errorMessage = 'Please enter a positive number';
      }
      
      updateData({
        'duration_text': durationText, // Keep original for display
        'duration': duration, // Numeric value for validation and storage
        'duration_error': errorMessage,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomBook() {
    if (activityData['book_selection'] == 'Other') {
      final customBook = customBookController.text.trim();
      updateData({
        'custom_book': customBook,
        'book_type': customBook.isNotEmpty ? customBook : 'Other',
      });
    }
  }

  void _updateCustomMood() {
    if (activityData['mood_selection'] == 'Other') {
      final customMood = customMoodController.text.trim();
      updateData({
        'custom_mood': customMood,
        'engagement': customMood.isNotEmpty ? customMood : 'Other',
      });
    }
  }

  void _updateCustomActivity() {
    if (activityData['activity_selection'] == 'Other') {
      final customActivity = customActivityController.text.trim();
      updateData({
        'custom_activity': customActivity,
        'activity': customActivity.isNotEmpty ? customActivity : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customBookController.dispose();
    customMoodController.dispose();
    customActivityController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Story Time';

  @override
  String? getActivityDescription() => 'Track reading sessions for language development and bonding';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'book_selection': 'Picture Book', // For dropdown display
      'book_type': 'Picture Book', // For actual saving
      'mood_selection': 'Highly Engaged', // For dropdown display
      'engagement': 'Highly Engaged', // For actual saving
      'activity_selection': 'Reading Aloud', // For dropdown display
      'activity': 'Reading Aloud', // For actual saving
      'duration': null, // Duration in minutes
      'duration_text': '', // Text input for display
      'notes': '',
      'custom_book': '',
      'custom_mood': '',
      'custom_activity': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isBookOtherSelected = activityData['book_selection'] == 'Other';
    final isMoodOtherSelected = activityData['mood_selection'] == 'Other';
    final isActivityOtherSelected = activityData['activity_selection'] == 'Other';
    
    return Column(
      children: [
        // Book type selector (1st)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Book Type',
          value: activityData['book_selection'],
          items: bookOptions,
          onChanged: (value) {
            updateData({
              'book_selection': value,
              'book_type': value,
            });
            // Clear custom book if not "Other"
            if (value != 'Other') {
              customBookController.clear();
              updateData({'custom_book': ''});
            }
          },
        ),
        
        // Show custom book input when "Other" is selected
        if (isBookOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Book Type',
            controller: customBookController,
            hint: 'Enter custom book type...',
          ),
        ],
        
        // Activity selector (2nd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Activity',
          value: activityData['activity_selection'],
          items: activityOptions,
          onChanged: (value) {
            updateData({
              'activity_selection': value,
              'activity': value,
            });
            // Clear custom activity if not "Other"
            if (value != 'Other') {
              customActivityController.clear();
              updateData({'custom_activity': ''});
            }
          },
        ),
        
        // Show custom activity input when "Other" is selected
        if (isActivityOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Activity',
            controller: customActivityController,
            hint: 'Enter custom activity...',
          ),
        ],
        
        // Engagement/Mood selector (3rd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Baby\'s Engagement',
          value: activityData['mood_selection'],
          items: moodOptions,
          onChanged: (value) {
            updateData({
              'mood_selection': value,
              'engagement': value,
            });
            // Clear custom mood if not "Other"
            if (value != 'Other') {
              customMoodController.clear();
              updateData({'custom_mood': ''});
            }
          },
        ),
        
        // Show custom engagement input when "Other" is selected
        if (isMoodOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Engagement',
            controller: customMoodController,
            hint: 'Enter custom engagement level...',
          ),
        ],
        
        // Duration input with validation (4th)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Material(
              child: TextFormField(
                controller: durationController,
                decoration: InputDecoration(
                  labelText: 'Duration (minutes)',
                  hintText: 'Enter duration (e.g., 5, 10, 15)',
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            // Show validation error if duration is invalid
            if (activityData.containsKey('duration_error') && activityData['duration_error'] != null) ...[
              Padding(
                padding: EdgeInsets.only(top: 1.h, left: 4.w),
                child: Text(
                  activityData['duration_error'].toString(),
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
            SizedBox(height: 2.h),
          ],
        ),
        
        // Development progress indicator
        if (activityData.containsKey('duration') && 
            activityData['duration'] != null && 
            activityData['duration'] is int && 
            activityData['duration'] > 0) ...[
          _buildDevelopmentIndicator(),
        ],
        
        // Notes field (6th)
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Book title, favorite parts, reactions, new words...',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildDevelopmentIndicator() {
    final duration = activityData['duration'] as int?;
    final engagement = activityData['engagement'] as String?;
    
    if (duration == null) return Container();
    
    // Determine development assessment based on duration and engagement
    String assessment;
    Color assessmentColor;
    IconData assessmentIcon;
    String encouragement;
    
    if (duration >= 20) {
      assessment = 'Excellent Reading Session!';
      assessmentColor = Colors.green;
      assessmentIcon = Icons.star;
      encouragement = 'Amazing! This is wonderful for language development.';
    } else if (duration >= 15) {
      assessment = 'Great Progress';
      assessmentColor = Colors.lightGreen;
      assessmentIcon = Icons.thumb_up;
      encouragement = 'Fantastic! Building strong reading habits.';
    } else if (duration >= 10) {
      assessment = 'Good Session';
      assessmentColor = Colors.blue;
      assessmentIcon = Icons.trending_up;
      encouragement = 'Well done! Every story helps language grow.';
    } else if (duration >= 5) {
      assessment = 'Nice Start';
      assessmentColor = Colors.orange;
      assessmentIcon = Icons.menu_book;
      encouragement = 'Great beginning! Building attention span.';
    } else {
      assessment = 'Quick Story';
      assessmentColor = Colors.amber;
      assessmentIcon = Icons.timer;
      encouragement = 'Even short stories plant seeds for learning!';
    }
    
    // Adjust assessment based on engagement
    if (engagement != null && engagement.contains('Highly Engaged')) {
      encouragement = '$encouragement Baby is really enjoying it!';
    } else if (engagement != null && engagement.contains('Fussy')) {
      assessment = 'Challenging Session';
      assessmentColor = Colors.orange;
      assessmentIcon = Icons.psychology;
      encouragement = 'It\'s okay! Some days are harder for focus.';
    } else if (engagement != null && engagement.contains('Interactive')) {
      encouragement = '$encouragement Great interaction and participation!';
    }
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: assessmentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: assessmentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            assessmentIcon,
            color: assessmentColor,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  assessment,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: assessmentColor,
                  ),
                ),
                Text(
                  encouragement,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: assessmentColor.withValues(alpha: 0.8),
                  ),
                ),
                if (duration > 0) ...[
                  SizedBox(height: 1.h),
                  Text(
                    '$duration minutes of language development',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: assessmentColor.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  String getActivityType() => 'story_time';

  @override
  IconData getActivityIcon() => Icons.menu_book;

  @override
  Color getActivityColor() => const Color(0xFFF9844A);
}