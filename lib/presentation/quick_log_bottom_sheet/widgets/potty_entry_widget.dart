import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class PottyEntryWidget extends BaseActivityWidget {
  const PottyEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _PottyEntryWidgetState();
}

class _PottyEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customPottyTypeController = TextEditingController();
  final TextEditingController customLocationController = TextEditingController();

  // Potty types
  final List<String> pottyTypes = [
    'Pee Only',
    'Poop Only',
    '<PERSON><PERSON> & Poop',
    'Accident (<PERSON>ee)',
    'Accident (Poop)',
    'Accident (<PERSON><PERSON> & Poop)',
    'Attempted (No Result)',
    'Other',
  ];

  // Success levels for tracking progress (dynamic based on potty type)
  List<String> get successLevels {
    final pottyType = activityData['potty_type'] as String?;
    
    if (pottyType != null && pottyType.contains('Accident')) {
      // For accidents, only show accident-related levels
      return [
        'Complete Accident',
        'Partial Accident',
        'Minor Accident',
      ];
    } else if (pottyType == 'Attempted (No Result)') {
      // For attempts, show attempt-related levels
      return [
        'Good Attempt',
        'Brief Attempt',
        'Reluctant Attempt',
      ];
    } else {
      // For successful potty use, show success levels
      return [
        'Successful',
        'Partial Success',
        'Needed Help',
      ];
    }
  }

  // Location options
  final List<String> locationOptions = [
    'Potty Chair',
    'Toilet',
    'Toilet with Seat',
    'Public Restroom',
    'Diaper (Accident)',
    'Underwear (Accident)',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    notesController.addListener(_updateNotes);
    customPottyTypeController.addListener(_updateCustomPottyType);
    customLocationController.addListener(_updateCustomLocation);
    
    // Initialize controllers with initial data
    if (activityData['notes'] != null) {
      notesController.text = activityData['notes'].toString();
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomPottyType() {
    if (activityData['potty_type_selection'] == 'Other') {
      final customType = customPottyTypeController.text.trim();
      updateData({
        'custom_potty_type': customType,
        // Update the actual potty_type field for saving
        'potty_type': customType.isNotEmpty ? customType : 'Other',
      });
    }
  }

  void _updateCustomLocation() {
    if (activityData['location_selection'] == 'Other') {
      final customLocation = customLocationController.text.trim();
      updateData({
        'custom_location': customLocation,
        // Update the actual location field for saving
        'location': customLocation.isNotEmpty ? customLocation : 'Other',
      });
    }
  }

  @override
  void dispose() {
    notesController.dispose();
    customPottyTypeController.dispose();
    customLocationController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Potty';

  @override
  String? getActivityDescription() => 'Track potty training progress and accidents';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'potty_type_selection': 'Pee Only', // For dropdown display
      'potty_type': 'Pee Only', // For actual saving
      'success_level': 'Successful', // Default success level
      'location_selection': 'Potty Chair', // For dropdown display
      'location': 'Potty Chair', // For actual saving
      'assistance_needed': false, // Whether child needed help
      'notes': '',
      'custom_potty_type': '',
      'custom_location': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isOtherTypeSelected = activityData['potty_type_selection'] == 'Other';
    final isOtherLocationSelected = activityData['location_selection'] == 'Other';
    
    return Column(
      children: [
        // Potty type selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Potty Type',
          value: activityData['potty_type_selection'],
          items: pottyTypes,
          onChanged: (value) {
            updateData({
              'potty_type_selection': value,
              'potty_type': value, // Initially set to same value
              // Reset success level when potty type changes
              'success_level': successLevels.first,
            });
            // Clear custom potty type if not "Other"
            if (value != 'Other') {
              customPottyTypeController.clear();
              updateData({'custom_potty_type': ''});
            }
            // Trigger rebuild to update success levels
            setState(() {});
          },
        ),
        
        // Show custom potty type input when "Other" is selected
        if (isOtherTypeSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Potty Type',
            controller: customPottyTypeController,
            hint: 'Enter potty type...',
          ),
        ],
        
        // Success level selector (dynamic based on potty type)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Success Level',
          value: successLevels.contains(activityData['success_level']) 
              ? activityData['success_level'] 
              : successLevels.first,
          items: successLevels,
          onChanged: (value) => updateData({'success_level': value}),
        ),
        
        // Location selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Location',
          value: activityData['location_selection'],
          items: locationOptions,
          onChanged: (value) {
            updateData({
              'location_selection': value,
              'location': value, // Initially set to same value
            });
            // Clear custom location if not "Other"
            if (value != 'Other') {
              customLocationController.clear();
              updateData({'custom_location': ''});
            }
          },
        ),
        
        // Show custom location input when "Other" is selected
        if (isOtherLocationSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Location',
            controller: customLocationController,
            hint: 'Enter location...',
          ),
        ],
        
        // Assistance needed toggle
        _buildAssistanceToggle(),
        
        // Progress indicator
        if (activityData['potty_type'] != null && activityData['success_level'] != null) ...[
          _buildProgressIndicator(),
        ],
        
        // Notes field
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Progress notes, rewards given, etc.',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildAssistanceToggle() {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.help_outline,
            color: Theme.of(context).colorScheme.primary,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Text(
              'Assistance Needed',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Switch(
            value: activityData['assistance_needed'] ?? false,
            onChanged: (value) => updateData({'assistance_needed': value}),
          ),
        ],
      ),
    );
  }

  Widget _buildProgressIndicator() {
    final pottyType = activityData['potty_type'] as String?;
    final successLevel = activityData['success_level'] as String?;
    
    if (pottyType == null || successLevel == null) return Container();
    
    final progressInfo = _getProgressInfo(pottyType, successLevel);
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: progressInfo.color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: progressInfo.color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            progressInfo.icon,
            color: progressInfo.color,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  progressInfo.status,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: progressInfo.color,
                  ),
                ),
                Text(
                  progressInfo.message,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: progressInfo.color.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  _ProgressInfo _getProgressInfo(String pottyType, String successLevel) {
    if (successLevel == 'Successful') {
      if (pottyType.contains('Accident')) {
        return _ProgressInfo(
          status: 'Learning Experience',
          color: Theme.of(context).colorScheme.tertiary,
          icon: Icons.school,
          message: 'Accidents are part of learning!',
        );
      } else if (pottyType == 'Attempted (No Result)') {
        return _ProgressInfo(
          status: 'Good Try!',
          color: Theme.of(context).colorScheme.primary,
          icon: Icons.thumb_up,
          message: 'Trying is the first step to success!',
        );
      } else {
        return _ProgressInfo(
          status: 'Great Success!',
          color: Theme.of(context).colorScheme.secondary,
          icon: Icons.star,
          message: 'Keep up the excellent work!',
        );
      }
    } else if (successLevel == 'Partial Success') {
      return _ProgressInfo(
        status: 'Making Progress',
        color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.8),
        icon: Icons.trending_up,
        message: 'You\'re getting better every day!',
      );
    } else if (successLevel == 'Attempted Only') {
      return _ProgressInfo(
        status: 'Good Effort',
        color: Theme.of(context).colorScheme.primary,
        icon: Icons.emoji_events,
        message: 'Trying is the first step to success!',
      );
    } else {
      return _ProgressInfo(
        status: 'Learning Opportunity',
        color: Theme.of(context).colorScheme.tertiary,
        icon: Icons.lightbulb,
        message: 'Every attempt is progress!',
      );
    }
  }

  @override
  String getActivityType() => 'potty';

  @override
  IconData getActivityIcon() => Icons.wc;

  @override
  Color getActivityColor() => const Color(0xFF9C27B0);
}

/// Helper class to encapsulate progress information
class _ProgressInfo {
  final String status;
  final Color color;
  final IconData icon;
  final String message;

  const _ProgressInfo({
    required this.status,
    required this.color,
    required this.icon,
    required this.message,
  });
}