import 'dart:async';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../../core/app_export.dart';
import '../../../services/measurement_units_service.dart';
import 'base_activity_widget.dart';

class FeedingEntryWidget extends BaseActivityWidget {
  const FeedingEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _FeedingEntryWidgetState();
}

class _FeedingEntryWidgetState extends BaseActivityWidgetState {
  bool isCustomAmountActive = false;
  bool isCustomDurationActive = false;
  bool _isSliderUpdating = false; // Flag to prevent text controller interference
  final TextEditingController customAmountController = TextEditingController();
  final TextEditingController customDurationController = TextEditingController();
  final TextEditingController foodItemsController = TextEditingController();
  final TextEditingController localNotesController = TextEditingController();
  Timer? _debounceTimer;
  Timer? _foodItemsDebounceTimer;

  @override
  void initState() {
    super.initState();
    
    // Initialize all controllers - start empty for custom inputs
    customAmountController.text = '';
    customDurationController.text = '';
    foodItemsController.text = activityData['food_items'] ?? '';
    localNotesController.text = activityData['notes'] ?? '';
    
    // Set up listeners that preserve all data
    customAmountController.addListener(_onCustomAmountChanged);
    customDurationController.addListener(_onCustomDurationChanged);
    foodItemsController.addListener(_onFoodItemsChanged);
    localNotesController.addListener(_onNotesChanged);
  }

  void _onCustomAmountChanged() {
    // Don't activate custom mode if the slider is updating the text field
    if (_isSliderUpdating) return;
    
    final value = double.tryParse(customAmountController.text);
    if (value != null && value > 0) {
      setState(() {
        isCustomAmountActive = true;
      });
      _updateDataSafely({'amount': value});
    }
  }

  void _onCustomDurationChanged() {
    // Don't activate custom mode if the slider is updating the text field
    if (_isSliderUpdating) return;
    
    final value = double.tryParse(customDurationController.text);
    if (value != null && value > 0) {
      setState(() {
        isCustomDurationActive = true;
      });
      _updateDataSafely({'duration': value});
    }
  }

  void _onFoodItemsChanged() {
    // Use a debounced update to avoid excessive rebuilds while typing
    _foodItemsDebounceTimer?.cancel();
    _foodItemsDebounceTimer = Timer(const Duration(milliseconds: 300), () {
      _updateDataSafely({'food_items': foodItemsController.text});
    });
  }

  void _onNotesChanged() {
    // Use a debounced update to avoid excessive rebuilds while typing
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _updateDataSafely({'notes': localNotesController.text});
    });
  }

  void _onFeedingTypeChanged(String feedingType) {
    // Clean up data when switching feeding types to prevent cross-contamination
    final cleanData = <String, dynamic>{
      'feeding_type': feedingType,
      'time': activityData['time'] ?? DateTime.now(),
      'notes': localNotesController.text,
    };

    // Add type-specific default data and reset custom states
    switch (feedingType) {
      case 'bottle':
        cleanData.addAll({
          'amount': 120.0,
        });
        // Reset custom states and clear custom inputs
        setState(() {
          isCustomAmountActive = false;
          isCustomDurationActive = false;
          customAmountController.text = '';
          customDurationController.text = '';
        });
        break;
      case 'breast':
        cleanData.addAll({
          'duration': 15.0,
          'side': 'left',
        });
        // Reset custom states and clear custom inputs
        setState(() {
          isCustomAmountActive = false;
          isCustomDurationActive = false;
          customAmountController.text = '';
          customDurationController.text = '';
        });
        // Remove amount from breast feeding completely
        cleanData.remove('amount');
        break;
      case 'solid':
        cleanData.addAll({
          'meal_type': 'breakfast',
          'food_items': foodItemsController.text,
        });
        // Reset custom states and clear custom inputs
        setState(() {
          isCustomAmountActive = false;
          isCustomDurationActive = false;
          customAmountController.text = '';
          customDurationController.text = '';
        });
        // Remove amount from solid feeding completely
        cleanData.remove('amount');
        break;
    }

    updateData(cleanData);
  }

  void _updateDataSafely(Map<String, dynamic> newData) {
    // Preserve all existing data and only update specific fields
    final updatedData = Map<String, dynamic>.from(activityData);
    updatedData.addAll(newData);
    
    // Remove amount for solid and breast feeding to prevent it from showing in the logs
    if (updatedData['feeding_type'] == 'solid' || updatedData['feeding_type'] == 'breast') {
      updatedData.remove('amount');
    }
    
    updateData(updatedData);
  }

  // Override the base notes section to use our local controller
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildHeader(),
        SizedBox(height: 2.h),
        buildForm(),
        SizedBox(height: 3.h),
        _buildLocalNotesSection(),
        SizedBox(height: 3.h),
        buildTimeSelector(),
      ],
    );
  }

  Widget _buildLocalNotesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),
        TextField(
          controller: localNotesController,
          decoration: InputDecoration(
            hintText: 'Add any additional notes...',
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _foodItemsDebounceTimer?.cancel();
    customAmountController.removeListener(_onCustomAmountChanged);
    customDurationController.removeListener(_onCustomDurationChanged);
    foodItemsController.removeListener(_onFoodItemsChanged);
    localNotesController.removeListener(_onNotesChanged);
    customAmountController.dispose();
    customDurationController.dispose();
    foodItemsController.dispose();
    localNotesController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Feeding';

  @override
  String? getActivityDescription() => 'Record feeding sessions and amounts';

  @override
  Map<String, dynamic> getInitialData() {
    // Only include essential data initially, type-specific data added when needed
    return {
      'feeding_type': 'bottle',
      'amount': 120.0, // Default for bottle feeding
      'time': DateTime.now(),
      'notes': '',
    };
  }

  @override
  Widget buildForm() {
    return Column(
      children: [
        ActivityFormHelper.buildSelectionRow(
          label: 'Feeding Type',
          options: [
            {'label': 'Bottle', 'value': 'bottle', 'icon': 'local_drink'},
            {'label': 'Breast', 'value': 'breast', 'icon': 'favorite'},
            {'label': 'Solid', 'value': 'solid', 'icon': 'restaurant'},
          ],
          selectedValue: activityData['feeding_type'] ?? 'bottle',
          onChanged: (value) => _onFeedingTypeChanged(value),
        ),
        if (activityData['feeding_type'] == 'bottle') ...[
          // Amount Slider (hidden when custom input is active)
          if (!isCustomAmountActive) ...[
            ActivityFormHelper.buildSlider(
              label: 'Amount',
              value: activityData['amount'] ?? 120.0,
              min: 30,
              max: 300,
              divisions: 27,
              unit: context.watch<MeasurementUnitsService>().volumeUnit,
              onChanged: (value) {
                updateData({'amount': value});
              },
            ),
            SizedBox(height: 2.h),
          ],
          
          // Custom Amount Input
          Row(
            children: [
              Text(
                'Custom Amount:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: TextField(
                  controller: customAmountController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    hintText: 'Enter amount',
                    suffixText: 'ml',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  ),
                  onChanged: (value) {
                    // Remove any non-numeric characters except decimal point
                    final cleanValue = value.replaceAll(RegExp(r'[^0-9.]'), '');
                    
                    if (cleanValue != value) {
                      // Update the text field with cleaned value
                      customAmountController.value = customAmountController.value.copyWith(
                        text: cleanValue,
                        selection: TextSelection.collapsed(offset: cleanValue.length),
                      );
                    }
                    
                    final parsedValue = double.tryParse(cleanValue);
                    if (parsedValue != null && parsedValue > 0) {
                      setState(() {
                        isCustomAmountActive = true;
                      });
                      updateData({'amount': parsedValue});
                    } else if (cleanValue.isEmpty) {
                      setState(() {
                        isCustomAmountActive = false;
                      });
                      updateData({'amount': 120.0}); // Reset to default
                    }
                  },
                  onTap: () {
                    setState(() {
                      isCustomAmountActive = true;
                    });
                  },
                ),
              ),
              if (isCustomAmountActive) ...[
                SizedBox(width: 2.w),
                TextButton(
                  onPressed: () {
                    setState(() {
                      isCustomAmountActive = false;
                      customAmountController.text = '';
                    });
                    updateData({'amount': 120.0}); // Reset to default
                  },
                  child: Text(
                    'Reset',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ],
        if (activityData['feeding_type'] == 'breast') ...[
          // Duration Slider (hidden when custom input is active)
          if (!isCustomDurationActive) ...[
            ActivityFormHelper.buildSlider(
              label: 'Duration',
              value: activityData['duration'] ?? 15.0,
              min: 5,
              max: 60,
              divisions: 11,
              unit: 'min',
              onChanged: (value) {
                updateData({'duration': value});
              },
            ),
            SizedBox(height: 2.h),
          ],
          
          // Custom Duration Input
          Row(
            children: [
              Text(
                'Custom Duration:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: TextField(
                  controller: customDurationController,
                  keyboardType: TextInputType.numberWithOptions(decimal: true),
                  decoration: InputDecoration(
                    hintText: 'Enter duration',
                    suffixText: 'min',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  ),
                  onChanged: (value) {
                    // Remove any non-numeric characters except decimal point
                    final cleanValue = value.replaceAll(RegExp(r'[^0-9.]'), '');
                    
                    if (cleanValue != value) {
                      // Update the text field with cleaned value
                      customDurationController.value = customDurationController.value.copyWith(
                        text: cleanValue,
                        selection: TextSelection.collapsed(offset: cleanValue.length),
                      );
                    }
                    
                    final parsedValue = double.tryParse(cleanValue);
                    if (parsedValue != null && parsedValue > 0) {
                      setState(() {
                        isCustomDurationActive = true;
                      });
                      updateData({'duration': parsedValue});
                    } else if (cleanValue.isEmpty) {
                      setState(() {
                        isCustomDurationActive = false;
                      });
                      updateData({'duration': 15.0}); // Reset to default
                    }
                  },
                  onTap: () {
                    setState(() {
                      isCustomDurationActive = true;
                    });
                  },
                ),
              ),
              if (isCustomDurationActive) ...[
                SizedBox(width: 2.w),
                TextButton(
                  onPressed: () {
                    setState(() {
                      isCustomDurationActive = false;
                      customDurationController.text = '';
                    });
                    updateData({'duration': 15.0}); // Reset to default
                  },
                  child: Text(
                    'Reset',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: 2.h),
          
          ActivityFormHelper.buildSelectionRow(
            label: 'Side',
            options: [
              {'label': 'Left', 'value': 'left', 'icon': 'favorite'},
              {'label': 'Right', 'value': 'right', 'icon': 'favorite'},
              {'label': 'Left & Right', 'value': 'both', 'icon': 'favorite'},
            ],
            selectedValue: activityData['side'] ?? 'left',
            onChanged: (value) => updateData({'side': value}),
          ),
        ],

        if (activityData['feeding_type'] == 'solid') ...[
          ActivityFormHelper.buildDropdown<String>(
            label: 'Meal Type',
            value: activityData['meal_type'] ?? 'breakfast',
            items: ['breakfast', 'lunch', 'dinner', 'snack'],
            onChanged: (value) => updateData({'meal_type': value}),
          ),
          
          ActivityFormHelper.buildTextField(
            label: 'Food Items',
            controller: foodItemsController,
            hint: 'e.g., banana, rice cereal, carrots',
            maxLines: 2,
          ),
        ],
      ],
    );
  }

  @override
  String getActivityType() => 'feeding';

  @override
  IconData getActivityIcon() => Icons.restaurant;

  @override
  Color getActivityColor() => const Color(0xFF4A90A4);
}
