import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class SkinToSkinEntryWidget extends BaseActivityWidget {
  const SkinToSkinEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _SkinToSkinEntryWidgetState();
}

class _SkinToSkinEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customPositionController = TextEditingController();
  final TextEditingController customLocationController = TextEditingController();
  final TextEditingController customBenefitController = TextEditingController();

  // Skin-to-skin positions
  final List<String> positionOptions = [
    'Chest to Chest',
    'Tummy to Tummy',
    'Side-lying',
    'Reclined Position',
    'Upright Hold',
    'Kangaroo Care',
    'Football Hold',
    'Cross-cradle',
    'Other',
  ];

  // Location/Setting
  final List<String> locationOptions = [
    'Bedroom',
    'Living Room',
    'Nursery',
    'Hospital Room',
    'Rocking Chair',
    'Bed',
    'Couch/Sofa',
    'Quiet Space',
    'Other',
  ];

  // Benefits observed
  final List<String> benefitOptions = [
    'Baby Calmed Down',
    'Better Sleep After',
    'Improved Feeding',
    'Reduced Crying',
    'Bonding Moment',
    'Relaxation for Both',
    'Temperature Regulation',
    'Heart Rate Stabilized',
    'Stress Relief',
    'Peaceful Time',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customPositionController.addListener(_updateCustomPosition);
    customLocationController.addListener(_updateCustomLocation);
    customBenefitController.addListener(_updateCustomBenefit);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      String? errorMessage;
      
      if (duration == null) {
        errorMessage = 'Please enter number only';
      } else if (duration < 0) {
        errorMessage = 'Please enter a positive number';
      }
      
      updateData({
        'duration_text': durationText, // Keep original for display
        'duration': duration, // Numeric value for validation and storage
        'duration_error': errorMessage,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomPosition() {
    if (activityData['position_selection'] == 'Other') {
      final customPosition = customPositionController.text.trim();
      updateData({
        'custom_position': customPosition,
        'position': customPosition.isNotEmpty ? customPosition : 'Other',
      });
    }
  }

  void _updateCustomLocation() {
    if (activityData['location_selection'] == 'Other') {
      final customLocation = customLocationController.text.trim();
      updateData({
        'custom_location': customLocation,
        'location': customLocation.isNotEmpty ? customLocation : 'Other',
      });
    }
  }

  void _updateCustomBenefit() {
    if (activityData['benefit_selection'] == 'Other') {
      final customBenefit = customBenefitController.text.trim();
      updateData({
        'custom_benefit': customBenefit,
        'benefit': customBenefit.isNotEmpty ? customBenefit : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customPositionController.dispose();
    customLocationController.dispose();
    customBenefitController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Skin to Skin';

  @override
  String? getActivityDescription() => 'Track bonding time for emotional and physical development';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'position_selection': 'Chest to Chest', // For dropdown display
      'position': 'Chest to Chest', // For actual saving
      'location_selection': 'Bedroom', // For dropdown display
      'location': 'Bedroom', // For actual saving
      'benefit_selection': 'Bonding Moment', // For dropdown display
      'benefit': 'Bonding Moment', // For actual saving
      'duration': null, // Duration in minutes
      'duration_text': '', // Text input for display
      'notes': '',
      'custom_position': '',
      'custom_location': '',
      'custom_benefit': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isPositionOtherSelected = activityData['position_selection'] == 'Other';
    final isLocationOtherSelected = activityData['location_selection'] == 'Other';
    final isBenefitOtherSelected = activityData['benefit_selection'] == 'Other';
    
    return Column(
      children: [
        // Position selector (1st)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Position',
          value: activityData['position_selection'],
          items: positionOptions,
          onChanged: (value) {
            updateData({
              'position_selection': value,
              'position': value,
            });
            // Clear custom position if not "Other"
            if (value != 'Other') {
              customPositionController.clear();
              updateData({'custom_position': ''});
            }
          },
        ),
        
        // Show custom position input when "Other" is selected
        if (isPositionOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Position',
            controller: customPositionController,
            hint: 'Enter custom position...',
          ),
        ],
        
        // Location selector (2nd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Location',
          value: activityData['location_selection'],
          items: locationOptions,
          onChanged: (value) {
            updateData({
              'location_selection': value,
              'location': value,
            });
            // Clear custom location if not "Other"
            if (value != 'Other') {
              customLocationController.clear();
              updateData({'custom_location': ''});
            }
          },
        ),
        
        // Show custom location input when "Other" is selected
        if (isLocationOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Location',
            controller: customLocationController,
            hint: 'Enter custom location...',
          ),
        ],
        
        // Benefit selector (3rd)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Benefits Observed',
          value: activityData['benefit_selection'],
          items: benefitOptions,
          onChanged: (value) {
            updateData({
              'benefit_selection': value,
              'benefit': value,
            });
            // Clear custom benefit if not "Other"
            if (value != 'Other') {
              customBenefitController.clear();
              updateData({'custom_benefit': ''});
            }
          },
        ),
        
        // Show custom benefit input when "Other" is selected
        if (isBenefitOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Benefit',
            controller: customBenefitController,
            hint: 'Enter custom benefit observed...',
          ),
        ],
        
        // Duration input with validation (4th)
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Material(
              child: TextFormField(
                controller: durationController,
                decoration: InputDecoration(
                  labelText: 'Duration (minutes)',
                  hintText: 'Enter duration (e.g., 10, 15, 30)',
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline
                          .withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  ),
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            // Show validation error if duration is invalid
            if (activityData.containsKey('duration_error') && activityData['duration_error'] != null) ...[
              Padding(
                padding: EdgeInsets.only(top: 1.h, left: 4.w),
                child: Text(
                  activityData['duration_error'].toString(),
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
            SizedBox(height: 2.h),
          ],
        ),
        
        // Development progress indicator
        if (activityData.containsKey('duration') && 
            activityData['duration'] != null && 
            activityData['duration'] is int && 
            activityData['duration'] > 0) ...[
          _buildDevelopmentIndicator(),
        ],
        
        // Notes field (6th)
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Baby\'s reactions, temperature, comfort level, special moments...',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildDevelopmentIndicator() {
    final duration = activityData['duration'] as int?;
    final benefit = activityData['benefit'] as String?;
    
    if (duration == null) return Container();
    
    // Determine development assessment based on duration and benefits
    String assessment;
    Color assessmentColor;
    IconData assessmentIcon;
    String encouragement;
    
    if (duration >= 30) {
      assessment = 'Excellent Bonding Session!';
      assessmentColor = Colors.green;
      assessmentIcon = Icons.favorite;
      encouragement = 'Amazing! Extended skin-to-skin promotes deep bonding.';
    } else if (duration >= 20) {
      assessment = 'Great Connection Time';
      assessmentColor = Colors.lightGreen;
      assessmentIcon = Icons.thumb_up;
      encouragement = 'Wonderful! Perfect duration for bonding benefits.';
    } else if (duration >= 15) {
      assessment = 'Good Bonding Session';
      assessmentColor = Colors.blue;
      assessmentIcon = Icons.trending_up;
      encouragement = 'Well done! Great for emotional development.';
    } else if (duration >= 10) {
      assessment = 'Nice Connection';
      assessmentColor = Colors.orange;
      assessmentIcon = Icons.child_care;
      encouragement = 'Good start! Building that special bond.';
    } else {
      assessment = 'Sweet Moment';
      assessmentColor = Colors.amber;
      assessmentIcon = Icons.timer;
      encouragement = 'Every moment of closeness matters!';
    }
    
    // Adjust assessment based on benefits
    if (benefit != null && benefit.contains('Baby Calmed Down')) {
      encouragement = '$encouragement Baby found comfort in your touch!';
    } else if (benefit != null && benefit.contains('Better Sleep')) {
      encouragement = '$encouragement Great for promoting better sleep!';
    } else if (benefit != null && benefit.contains('Bonding Moment')) {
      encouragement = '$encouragement Beautiful bonding experience!';
    } else if (benefit != null && benefit.contains('Temperature Regulation')) {
      encouragement = '$encouragement Excellent for baby\'s temperature control!';
    }
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: assessmentColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: assessmentColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            assessmentIcon,
            color: assessmentColor,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  assessment,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: assessmentColor,
                  ),
                ),
                Text(
                  encouragement,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: assessmentColor.withValues(alpha: 0.8),
                  ),
                ),
                if (duration > 0) ...[
                  SizedBox(height: 1.h),
                  Text(
                    '$duration minutes of bonding and development',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: assessmentColor.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  String getActivityType() => 'skin_to_skin';

  @override
  IconData getActivityIcon() => Icons.favorite;

  @override
  Color getActivityColor() => const Color(0xFFE91E63);
}