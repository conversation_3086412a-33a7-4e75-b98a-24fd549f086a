import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class VaccinationEntryWidget extends BaseActivityWidget {
  const VaccinationEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _VaccinationEntryWidgetState();
}

class _VaccinationEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customVaccineController = TextEditingController();

  // Comprehensive list of common baby and kids vaccines (A-Z order)
  final List<String> vaccineOptions = [
    'BCG (Tuberculosis)',
    'COVID-19',
    'DTaP (<PERSON>phtheria, Tetanus, Pertussis)',
    'Hepatitis A',
    'Hepatitis B',
    'Hib (Haemophilus influenzae type b)',
    'HPV (Human Papillomavirus)',
    'Influenza (Flu)',
    'IPV (Inactivated Polio)',
    'Japanese Encephalitis',
    'Meningococcal',
    'MMR (Measles, Mumps, Rubella)',
    'PCV13 (Pneumococcal)',
    'Rabies',
    'Rotavirus',
    'RSV (Respiratory Syncytial Virus)',
    'Tdap (Tetanus, Diphtheria, Pertussis)',
    'Typhoid',
    'Varicella (Chickenpox)',
    'Yellow Fever',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    notesController.addListener(_updateNotes);
    customVaccineController.addListener(_updateCustomVaccine);
    
    // Initialize controllers with initial data
    if (activityData['notes'] != null) {
      notesController.text = activityData['notes'].toString();
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomVaccine() {
    if (activityData['vaccine_selection'] == 'Other') {
      final customVaccine = customVaccineController.text.trim();
      updateData({
        'custom_vaccine': customVaccine,
        // Update the actual vaccine field for saving, but keep dropdown selection as 'Other'
        'vaccine': customVaccine.isNotEmpty ? customVaccine : 'Other',
      });
    }
  }

  @override
  void dispose() {
    notesController.dispose();
    customVaccineController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Vaccination';

  @override
  String? getActivityDescription() => 'Record vaccination and immunization details';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'vaccine_selection': 'Hepatitis B', // For dropdown display
      'vaccine': 'Hepatitis B', // For actual saving
      'notes': '',
      'custom_vaccine': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isOtherSelected = activityData['vaccine_selection'] == 'Other';
    
    return Column(
      children: [
        ActivityFormHelper.buildDropdown<String>(
          label: 'Vaccine',
          value: activityData['vaccine_selection'],
          items: vaccineOptions,
          onChanged: (value) {
            updateData({
              'vaccine_selection': value,
              'vaccine': value, // Initially set to same value
            });
            // Clear custom vaccine if not "Other"
            if (value != 'Other') {
              customVaccineController.clear();
              updateData({'custom_vaccine': ''});
            }
          },
        ),
        
        // Show custom vaccine input when "Other" is selected
        if (isOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Vaccine Name',
            controller: customVaccineController,
            hint: 'Enter vaccine name...',
          ),
        ],
        
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Batch number, side effects, provider, etc.',
          maxLines: 3,
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'vaccination';

  @override
  IconData getActivityIcon() => Icons.vaccines;

  @override
  Color getActivityColor() => const Color(0xFF2E7D32);
}
