import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';

/// Abstract base class for all activity widgets using Template Method Pattern
abstract class BaseActivityWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;
  
  const BaseActivityWidget({
    super.key,
    required this.onDataChanged,
  });
  
  @override
  BaseActivityWidgetState createState();
}

/// Base state class with template method implementation
abstract class BaseActivityWidgetState extends State<BaseActivityWidget> {
  Map<String, dynamic> activityData = {};
  DateTime selectedTime = ModernDateTimePicker.getCurrentTime();
  
  // Track controllers for proper disposal
  final List<TextEditingController> _controllers = [];
  
  @override
  void initState() {
    super.initState();
    // Initialize with default data
    activityData = getInitialData();
    selectedTime = activityData['time'] ?? ModernDateTimePicker.getCurrentTime();
    
    // Defer initial data update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _updateData();
      }
    });
  }
  
  /// Template method - defines the structure of the widget
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildHeader(),
        SizedBox(height: 2.h),
        buildForm(),
        SizedBox(height: 3.h),
        buildTimeSelector(),
      ],
    );
  }
  
  /// Build the header with title and description
  Widget buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          getActivityTitle(),
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        if (getActivityDescription() != null) ...[
          SizedBox(height: 0.5.h),
          Text(
            getActivityDescription()!,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ],
    );
  }
  
  /// Build the time selector
  Widget buildTimeSelector() {
    return GestureDetector(
      onTap: _selectTime,
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: Theme.of(context).colorScheme.outline
                .withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              Icons.access_time,
              color: Theme.of(context).colorScheme.primary,
              size: 20,
            ),
            SizedBox(width: 3.w),
            Text(
              'Time: ${_formatDateTime(selectedTime)}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            Icon(
              Icons.arrow_forward_ios,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  
  /// Select time for the activity
  void _selectTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: selectedTime,
    );

    if (picked != null) {
      setState(() {
        selectedTime = picked;
      });
      updateData({'time': selectedTime});
    }
  }
  
  /// Update activity data and notify parent
  void updateData(Map<String, dynamic> newData) {
    if (!mounted) return;
    
    setState(() {
      activityData.addAll(newData);
      activityData['time'] = selectedTime;
    });
    _updateData();
  }
  
  /// Send updated data to parent
  void _updateData() {
    widget.onDataChanged(activityData);
  }
  
  /// Format DateTime for display
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    if (dateOnly == today) {
      return 'Today ${_formatTime(dateTime)}';
    } else if (dateOnly == yesterday) {
      return 'Yesterday ${_formatTime(dateTime)}';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${_formatTime(dateTime)}';
    }
  }
  
  /// Format time portion
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour == 0 ? 12 : (dateTime.hour > 12 ? dateTime.hour - 12 : dateTime.hour);
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = dateTime.hour >= 12 ? 'PM' : 'AM';
    return '$hour:$minute $period';
  }
  
  /// Abstract methods to be implemented by subclasses
  String getActivityTitle();
  String? getActivityDescription() => null;
  Map<String, dynamic> getInitialData();
  Widget buildForm();
  String getActivityType();
  IconData getActivityIcon();
  Color getActivityColor();
}

/// Helper class for common form elements
class ActivityFormHelper {
  // UI Constants to avoid magic numbers
  static const double _defaultPadding = 12.0;
  static const double _defaultSpacing = 16.0;
  static const double _borderRadius = 12.0;
  static const double _borderAlpha = 0.3;
  /// Build a selection row with icons
  static Widget buildSelectionRow({
    required String label,
    required List<Map<String, dynamic>> options,
    required String selectedValue,
    required Function(String) onChanged,
  }) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(height: 1.h),
            Row(
              children: options.map((option) {
                final isSelected = selectedValue == option['value'];
                return Expanded(
                  child: Padding(
                    padding: EdgeInsets.only(
                      right: options.indexOf(option) == options.length - 1 ? 0 : 3.w,
                    ),
                    child: GestureDetector(
                      onTap: () => onChanged(option['value']),
                      child: Container(
                        padding: EdgeInsets.symmetric(vertical: 2.h),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                                  .withValues(alpha: 0.1)
                              : Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.outline
                                    .withValues(alpha: 0.3),
                          ),
                        ),
                        child: Column(
                          children: [
                            Icon(
                              _getIconData(option['icon']),
                              color: isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              size: 24,
                            ),
                            SizedBox(height: 0.5.h),
                            Text(
                              option['label'],
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: isSelected
                                    ? Theme.of(context).colorScheme.primary
                                    : Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: isSelected
                                    ? FontWeight.w600
                                    : FontWeight.w400,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
            SizedBox(height: 2.h),
          ],
        );
      },
    );
  }
  
  /// Build a slider with label and value
  static Widget buildSlider({
    required String label,
    required double value,
    required double min,
    required double max,
    required Function(double) onChanged,
    String unit = '',
    int? divisions,
  }) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${value.toStringAsFixed(value % 1 == 0 ? 0 : 1)} $unit',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Theme.of(context).colorScheme.outline
                  .withValues(alpha: 0.3),
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(context).colorScheme.primary
                  .withValues(alpha: 0.1),
              valueIndicatorColor: Theme.of(context).colorScheme.primary,
              valueIndicatorTextStyle: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: divisions,
              onChanged: onChanged,
            ),
          ),
          SizedBox(height: 2.h),
        ],
      ),
    );
  }
  
  /// Build a dropdown selection
  static Widget buildDropdown<T>({
    required String label,
    required T? value,
    required List<T> items,
    required Function(T?) onChanged,
    String? hint,
  }) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Material(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(12),
              child: DropdownButtonHideUnderline(
                child: DropdownButtonFormField<T>(
                  value: value,
                  hint: hint != null ? Padding(
                    padding: EdgeInsets.symmetric(horizontal: 3.w),
                    child: Text(
                      hint,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ) : null,
                  isExpanded: true,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.outline
                            .withValues(alpha: 0.3),
                      ),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.outline
                            .withValues(alpha: 0.3),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                  ),
                  items: items.map<DropdownMenuItem<T>>((T item) {
                    return DropdownMenuItem<T>(
                      value: item,
                      child: Text(item.toString()),
                    );
                  }).toList(),
                  onChanged: onChanged,
                ),
              ),
            ),
          ),
          SizedBox(height: 2.h),
        ],
      ),
    );
  }
  
  /// Build a text input field
  static Widget buildTextField({
    required String label,
    required TextEditingController controller,
    String? hint,
    int maxLines = 1,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
  }) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Material(
            child: TextFormField(
              controller: controller,
              decoration: InputDecoration(
                hintText: hint,
                filled: true,
                fillColor: Theme.of(context).colorScheme.surface,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline
                        .withValues(alpha: 0.3),
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.outline
                        .withValues(alpha: 0.3),
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
              maxLines: maxLines,
              keyboardType: keyboardType,
              validator: validator,
            ),
          ),
          SizedBox(height: 2.h),
        ],
      ),
    );
  }
  
  /// Helper method to get icon data
  static IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'local_drink':
        return Icons.local_drink;
      case 'favorite':
        return Icons.favorite;
      case 'medication':
        return Icons.medication;
      case 'vaccines':
        return Icons.vaccines;
      case 'child_care':
        return Icons.child_care;
      case 'bedtime':
        return Icons.bedtime;
      case 'restaurant':
        return Icons.restaurant;
      default:
        return Icons.circle;
    }
  }
}
