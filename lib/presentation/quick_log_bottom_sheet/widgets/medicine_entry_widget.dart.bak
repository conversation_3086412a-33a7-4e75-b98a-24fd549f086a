import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MedicineEntryWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const MedicineEntryWidget({
    super.key,
    required this.onDataChanged,
  });

  @override
  State<MedicineEntryWidget> createState() => _MedicineEntryWidgetState();
}

class _MedicineEntryWidgetState extends State<MedicineEntryWidget> {
  String? selectedMedication;
  String dosage = '';
  DateTime selectedTime = DateTime.now();

  final TextEditingController dosageController = TextEditingController();

  final List<String> commonMedications = [
    'Paracetamol',
    'Ibuprofen',
    'Vitamin D',
    'Iron Supplement',
    'Probiotics',
    'Gripe Water',
    'Teething Gel',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    dosageController.addListener(() {
      dosage = dosageController.text;
      _updateData();
    });
  }

  @override
  void dispose() {
    dosageController.dispose();
    super.dispose();
  }

  void _updateData() {
    widget.onDataChanged({
      'medication': selectedMedication,
      'dosage': dosage,
      'time': selectedTime,
    });
  }

  void _selectTime() async {
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.fromDateTime(selectedTime),
    );

    if (picked != null) {
      setState(() {
        selectedTime = DateTime(
          selectedTime.year,
          selectedTime.month,
          selectedTime.day,
          picked.hour,
          picked.minute,
        );
      });
      _updateData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Medicine Administration',
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // Time Selection
        GestureDetector(
          onTap: _selectTime,
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppTheme.lightTheme.colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'access_time',
                  color: AppTheme.lightTheme.colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  'Time: ${TimeOfDay.fromDateTime(selectedTime).format(context)}',
                  style: AppTheme.lightTheme.textTheme.bodyMedium,
                ),
                const Spacer(),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),

        Text(
          'Medication',
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        // Medication Selection
        Container(
          decoration: BoxDecoration(
            color: AppTheme.lightTheme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppTheme.lightTheme.colorScheme.outline
                  .withValues(alpha: 0.3),
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: selectedMedication,
              hint: Padding(
                padding: EdgeInsets.symmetric(horizontal: 3.w),
                child: Text(
                  'Select medication',
                  style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                    color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
              isExpanded: true,
              items: commonMedications.map((medication) {
                return DropdownMenuItem<String>(
                  value: medication,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 3.w),
                    child: Row(
                      children: [
                        CustomIconWidget(
                          iconName: 'medication',
                          color: AppTheme.getWarningColor(true),
                          size: 16,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          medication,
                          style: AppTheme.lightTheme.textTheme.bodyMedium,
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  selectedMedication = value;
                });
                _updateData();
              },
            ),
          ),
        ),

        SizedBox(height: 3.h),

        // Dosage Input
        Text(
          'Dosage',
          style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        TextFormField(
          controller: dosageController,
          decoration: InputDecoration(
            hintText: 'e.g., 2.5ml, 1 tablet, 5 drops',
            prefixIcon: Padding(
              padding: EdgeInsets.all(3.w),
              child: CustomIconWidget(
                iconName: 'local_pharmacy',
                color: AppTheme.getWarningColor(true),
                size: 20,
              ),
            ),
          ),
          style: AppTheme.lightTheme.textTheme.bodyMedium,
        ),

        SizedBox(height: 2.h),

        // Warning Message
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: AppTheme.getWarningColor(true).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: AppTheme.getWarningColor(true).withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomIconWidget(
                iconName: 'warning',
                color: AppTheme.getWarningColor(true),
                size: 16,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  'Always consult your pediatrician before administering any medication to your baby.',
                  style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                    color: AppTheme.getWarningColor(true),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Validation Messages
        if (selectedMedication == null || dosage.isEmpty) ...[
          SizedBox(height: 1.h),
          Container(
            padding: EdgeInsets.all(2.w),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'info',
                  color: AppTheme.lightTheme.colorScheme.error,
                  size: 14,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    selectedMedication == null
                        ? 'Please select a medication'
                        : 'Please enter dosage information',
                    style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.lightTheme.colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }
}
