import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';

class VaccinationEntryWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const VaccinationEntryWidget({
    super.key,
    required this.onDataChanged,
  });

  @override
  State<VaccinationEntryWidget> createState() => _VaccinationEntryWidgetState();
}

class _VaccinationEntryWidgetState extends State<VaccinationEntryWidget> {
  String? selectedVaccine;
  String customVaccine = '';
  String notes = '';
  DateTime selectedTime = ModernDateTimePicker.getCurrentTime();

  final TextEditingController customVaccineController = TextEditingController();
  final TextEditingController notesController = TextEditingController();

  final List<String> commonVaccines = [
    'Hepatitis B',
    'DTaP (Diphtheria, Tetanus, Pertussis)',
    'Hib (Haemophilus influenzae type b)',
    'Polio (IPV)',
    'PCV13 (Pneumococcal)',
    'Rotavirus',
    'MMR (Measles, Mumps, Rubella)',
    'Varicella (Chickenpox)',
    'Hepatitis A',
    'Meningococcal',
    'HPV (Human Papillomavirus)',
    'Influenza (Flu)',
    'COVID-19',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    customVaccineController.addListener(() {
      setState(() {
        customVaccine = customVaccineController.text;
      });
      _updateData();
    });
    notesController.addListener(() {
      setState(() {
        notes = notesController.text;
      });
      _updateData();
    });
  }

  @override
  void dispose() {
    customVaccineController.dispose();
    notesController.dispose();
    super.dispose();
  }

  void _updateData() {
    final vaccineName = selectedVaccine == 'Other' ? customVaccine : selectedVaccine;
    
    widget.onDataChanged({
      'vaccine': vaccineName,
      'notes': notes,
      'startTime': selectedTime,
    });
  }

  void _selectTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: selectedTime,
    );

    if (picked != null) {
      setState(() {
        selectedTime = picked;
      });
      _updateData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Vaccination Record',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // 1. Vaccine Selection
        Text(
          'Vaccine',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline
                  .withValues(alpha: 0.3),
            ),
          ),
          child: Material(
            color: Colors.transparent,
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: selectedVaccine,
                hint: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 3.w),
                  child: Text(
                    'Select vaccine',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
                isExpanded: true,
                items: commonVaccines.map((vaccine) {
                  return DropdownMenuItem<String>(
                    value: vaccine,
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 3.w),
                      child: Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'vaccines',
                            color: Theme.of(context).colorScheme.primary,
                            size: 16,
                          ),
                          SizedBox(width: 2.w),
                          Expanded(
                            child: Text(
                              vaccine,
                              style: Theme.of(context).textTheme.bodyMedium,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
                onChanged: (value) {
                  setState(() {
                    selectedVaccine = value;
                  });
                  _updateData();
                },
              ),
            ),
          ),
        ),

        // 2. Custom Vaccine Input (for "Other" option)
        if (selectedVaccine == 'Other') ...[
          SizedBox(height: 2.h),
          Text(
            'Custom Vaccine Name',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Material(
            color: Colors.transparent,
            child: TextFormField(
              controller: customVaccineController,
              decoration: InputDecoration(
                hintText: 'Enter vaccine name',
                prefixIcon: Padding(
                  padding: EdgeInsets.all(3.w),
                  child: CustomIconWidget(
                    iconName: 'vaccines',
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],

        SizedBox(height: 3.h),

        // 3. Time Selection
        Text(
          'Vaccination Time',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        GestureDetector(
          onTap: _selectTime,
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'access_time',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  ModernDateTimePicker.formatDateTime(selectedTime, context),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),

        // 4. Notes Section
        Text(
          'Notes (Optional)',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        Material(
          color: Colors.transparent,
          child: TextFormField(
            controller: notesController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Add any additional notes about the vaccination (side effects, batch number, etc.)...',
              prefixIcon: Padding(
                padding: EdgeInsets.all(3.w),
                child: CustomIconWidget(
                  iconName: 'note_add',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
              ),
            ),
            style: Theme.of(context).textTheme.bodyMedium,
          ),
        ),

        SizedBox(height: 2.h),

        // Information Message
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CustomIconWidget(
                iconName: 'info',
                color: Theme.of(context).colorScheme.primary,
                size: 16,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  'Keep track of your baby\'s vaccination schedule. Consult your pediatrician for the recommended vaccination timeline.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Validation Messages
        if (_hasValidationErrors()) ...[
          SizedBox(height: 1.h),
          Container(
            padding: EdgeInsets.all(2.w),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'info',
                  color: Theme.of(context).colorScheme.error,
                  size: 14,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    _getValidationMessage(),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  bool _hasValidationErrors() {
    if (selectedVaccine == null) return true;
    if (selectedVaccine == 'Other' && customVaccine.isEmpty) return true;
    return false;
  }

  String _getValidationMessage() {
    if (selectedVaccine == null) return 'Please select a vaccine';
    if (selectedVaccine == 'Other' && customVaccine.isEmpty) return 'Please enter custom vaccine name';
    return '';
  }

  // Add this method to check if form is valid for the parent widget
  bool get isFormValid => !_hasValidationErrors();
}