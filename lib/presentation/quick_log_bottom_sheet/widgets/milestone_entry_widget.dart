import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/milestone.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class MilestoneEntryWidget extends BaseActivityWidget {
  final BabyProfile? babyProfile;

  const MilestoneEntryWidget({
    super.key,
    required super.onDataChanged,
    this.babyProfile,
  });

  @override
  BaseActivityWidgetState createState() => _MilestoneEntryWidgetState();
}

class _MilestoneEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController titleController = TextEditingController();
  final TextEditingController descriptionController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  
  List<Map<String, dynamic>> availableTemplates = [];
  bool isCustomMilestone = false;
  String? selectedCategory;
  String? selectedMilestone;
  
  // Milestone categories for dropdown
  final List<String> categoryOptions = [
    'Gross Motor',
    'Fine Motor',
    'Cognitive',
    'Language',
    'Social',
    'Emotional',
    'Sensory',
    'Adaptive / Self-Help',
    'Feeding',
    'Sleep',
    'Play',
    'Health',
    'Custom',
  ];
  
  // Milestone examples by category
  final Map<String, List<String>> milestoneExamples = {
    'Gross Motor': [
      'Lifts head',
      'Rolls over',
      'Sits without support',
      'Crawls',
      'Stands with support',
      'Walks independently',
      'Runs',
      'Climbs stairs',
    ],
    'Fine Motor': [
      'Opens hands',
      'Grasps objects',
      'Transfers hand to hand',
      'Uses pincer grasp',
      'Scribbles',
      'Stacks blocks',
      'Turns book pages',
      'Uses utensils',
    ],
    'Cognitive': [
      'Tracks moving objects',
      'Recognizes faces',
      'Explores with mouth',
      'Finds hidden items',
      'Imitates actions',
      'Matches shapes',
      'Follows simple instructions',
      'Understands cause and effect',
    ],
    'Language': [
      'Coos',
      'Babbles',
      'Says "mama" or "dada"',
      'Points to communicate',
      'Uses simple words',
      'Combines 2–3 words',
      'Names objects',
      'Follows verbal commands',
    ],
    'Social': [
      'Smiles socially',
      'Enjoys play with others',
      'Shows stranger anxiety',
      'Waves or claps',
      'Shows affection',
      'Parallel play',
      'Takes turns',
      'Initiates interaction',
    ],
    'Emotional': [
      'Calms with caregiver',
      'Laughs',
      'Shows frustration',
      'Seeks comfort',
      'Displays pride',
      'Recognizes emotions',
      'Shows empathy',
      'Manages brief separation',
    ],
    'Sensory': [
      'Responds to sound',
      'Enjoys textures',
      'Turns to familiar voice',
      'Dislikes loud noise',
      'Explores new tastes',
      'Engages in messy play',
      'Adjusts to light/noise',
      'Prefers certain stimuli',
    ],
    'Adaptive / Self-Help': [
      'Holds bottle',
      'Feeds self with fingers',
      'Drinks from cup',
      'Uses spoon',
      'Removes clothing',
      'Tries to dress',
      'Washes hands with help',
      'Begins toilet training',
    ],
    'Feeding': [
      'Breastfeeds/formula',
      'Starts solids',
      'Eats mashed food',
      'Eats finger food',
      'Drinks from sippy cup',
      'Feeds self',
      'Chews all textures',
      'Uses straw',
    ],
    'Sleep': [
      'Sleeps 14–17 hours/day',
      'Sleeps longer at night',
      'Naps 2–3 times/day',
      'Sleeps through night',
      'Transitions to 1 nap',
      'Self-soothes',
      'Moves to toddler bed',
      'Consistent bedtime routine',
    ],
    'Play': [
      'Watches faces',
      'Bats at toys',
      'Bangs objects',
      'Pushes/pulls toys',
      'Engages in pretend play',
      'Builds block tower',
      'Plays with dolls',
      'Enjoys interactive games',
    ],
    'Health': [
      'Steady weight gain',
      'First tooth erupts',
      'Up-to-date immunizations',
      'Regular bowel movements',
      'Few colds',
      'Tracks growth percentile',
      'Doctor wellness checks',
      'Begins brushing teeth',
    ],
  };
  
  // For custom categories
  final TextEditingController customCategoryController = TextEditingController();
  bool isCustomCategory = false;



  @override
  void initState() {
    super.initState();
    print('🏆 MilestoneEntryWidget: initState called');
    
    // Calculate baby's age and get appropriate templates
    final babyProfile = (widget as MilestoneEntryWidget).babyProfile;
    final ageInMonths = babyProfile != null 
        ? DateTime.now().difference(babyProfile.birthDate).inDays ~/ 30
        : 6; // Default to 6 months if no profile
    
    availableTemplates = MilestoneTemplates.getTemplatesForAge(ageInMonths);
    
    titleController.addListener(_updateTitle);
    descriptionController.addListener(_updateDescription);
    notesController.addListener(_updateNotes);

    // Initialize with existing data
    titleController.text = activityData['title'] ?? '';
    descriptionController.text = activityData['description'] ?? '';
    notesController.text = activityData['notes'] ?? '';
    print('🏆 MilestoneEntryWidget: Initial data loaded - ${activityData.keys.toList()}');
  }

  void _updateTitle() {
    updateData({'title': titleController.text});
  }

  void _updateDescription() {
    updateData({'description': descriptionController.text});
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }
  
  String _getCategoryEnumValue(String? displayName) {
    switch (displayName) {
      case 'Gross Motor':
      case 'Fine Motor':
        return 'motor';
      case 'Cognitive':
        return 'cognitive';
      case 'Language':
        return 'language';
      case 'Social':
        return 'social';
      case 'Emotional':
        return 'emotional';
      case 'Sensory':
        return 'sensory';
      case 'Adaptive / Self-Help':
        return 'adaptive';
      case 'Feeding':
        return 'feeding';
      case 'Sleep':
        return 'sleep';
      case 'Play':
        return 'play';
      case 'Health':
        return 'health';
      case 'Custom':
        return 'custom';
      default:
        return 'motor';
    }
  }
  
  String _getMilestoneType(String? category) {
    switch (category) {
      case 'Gross Motor':
        return 'gross_motor';
      case 'Fine Motor':
        return 'fine_motor';
      case 'Cognitive':
        return 'cognitive_development';
      case 'Language':
        return 'communication';
      case 'Social':
      case 'Emotional':
        return 'personal_social';
      case 'Sensory':
        return 'sensory_development';
      case 'Play':
        return 'personal_social';
      default:
        return 'adaptive_behavior';
    }
  }

  void _onTemplateSelected(String templateTitle) {
    if (templateTitle.isEmpty) return;
    
    // Find the selected template
    final template = availableTemplates.firstWhere(
      (t) => t['title'] == templateTitle,
      orElse: () => {},
    );
    
    if (template.isNotEmpty) {
      // Populate fields with template data
      titleController.text = template['title'] ?? '';
      descriptionController.text = template['description'] ?? '';
      
      updateData({
        'title': template['title'],
        'description': template['description'],
        'category': (template['category'] as MilestoneCategory).name,
        'type': (template['type'] as MilestoneType).name,
        'is_custom': false,
      });
    }
  }

  @override
  void dispose() {
    titleController.dispose();
    descriptionController.dispose();
    notesController.dispose();
    customCategoryController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Milestone';

  @override
  String? getActivityDescription() => 'Record developmental milestones and achievements';

  @override
  Map<String, dynamic> getInitialData() {
    final babyProfile = (widget as MilestoneEntryWidget).babyProfile;
    final ageInDays = babyProfile != null 
        ? DateTime.now().difference(babyProfile.birthDate).inDays 
        : 180; // Default to 6 months
    final ageInMonths = ageInDays ~/ 30;

    return {
      'title': '',
      'description': '',
      'notes': '',
      'category': 'motor',
      'type': 'gross_motor',
      'age_in_months': ageInMonths,
      'age_in_days': ageInDays,
      'milestone_date': ModernDateTimePicker.getCurrentTime(),
      'is_custom': false,
      'time': ModernDateTimePicker.getCurrentTime(),
    };
  }

@override
  Widget buildForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Milestone Category Selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Category',
          value: selectedCategory,
          items: categoryOptions,
          onChanged: (value) => setState(() {
            selectedCategory = value;
            selectedMilestone = null; // Reset milestone selection
            isCustomCategory = (value == 'Custom');
            if (isCustomCategory) {
              customCategoryController.clear();
              isCustomMilestone = true; // For custom categories, always use custom milestone
              titleController.clear();
              descriptionController.clear();
              updateData({
                'title': '',
                'description': '',
                'category': 'custom',
                'type': 'adaptive_behavior',
                'is_custom': true,
              });
            } else {
              isCustomMilestone = false;
            }
          }),
          hint: 'Choose a category...',
        ),
        
        // Custom Category Input
        if (isCustomCategory)
          ActivityFormHelper.buildTextField(
            label: 'Custom Category',
            controller: customCategoryController,
            hint: 'Enter custom category...',
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a category name';
              }
              return null;
            },
          ),

        // Milestone Examples Selector (only show for non-custom categories)
        if (selectedCategory != null && !isCustomCategory)
          ActivityFormHelper.buildDropdown<String>(
            label: 'Choose Milestone',
            value: selectedMilestone,
            items: [
              ...milestoneExamples[selectedCategory] ?? [],
              'Custom Milestone',
            ],
            onChanged: (value) => setState(() {
              selectedMilestone = value;
              if (value == 'Custom Milestone') {
                isCustomMilestone = true;
                titleController.clear();
                descriptionController.clear();
                updateData({
                  'title': '',
                  'description': '',
                  'category': _getCategoryEnumValue(selectedCategory),
                  'type': _getMilestoneType(selectedCategory),
                  'is_custom': true,
                });
              } else {
                isCustomMilestone = false;
                // Set the milestone title and description
                titleController.text = value ?? '';
                descriptionController.text = value ?? '';
                updateData({
                  'title': value ?? '',
                  'description': value ?? '',
                  'category': _getCategoryEnumValue(selectedCategory),
                  'type': _getMilestoneType(selectedCategory),
                  'is_custom': false,
                });
              }
            }),
            hint: 'Select a milestone...',
          ),

        SizedBox(height: 2.h),

        // Title Field (if custom milestone)
        if (isCustomMilestone)
          ActivityFormHelper.buildTextField(
            label: 'Milestone Title',
            controller: titleController,
            hint: 'Enter milestone title...',
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter a title';
              }
              return null;
            },
          ),

        // Description Field
        ActivityFormHelper.buildTextField(
          label: 'Description',
          controller: descriptionController,
          hint: 'Describe the milestone... (optional)',
          maxLines: 2,
          validator: (value) {
            if (value != null && value.length > 100) {
              return 'Max 100 characters';
            }
            return null;
          },
        ),

        // Notes Field
        ActivityFormHelper.buildTextField(
          label: 'Additional Notes',
          controller: notesController,
          hint: 'Any additional observations or context... (optional)',
          maxLines: 3,
          validator: (value) {
            if (value != null && value.length > 150) {
              return 'Max 150 characters';
            }
            return null;
          },
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'milestone';

  @override
  IconData getActivityIcon() => Icons.emoji_events;

  @override
  Color getActivityColor() => const Color(0xFFFFD700);
}
