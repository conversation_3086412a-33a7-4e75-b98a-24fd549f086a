import 'package:flutter/material.dart';

import 'base_activity_widget.dart';
import 'feeding_entry_widget.dart';
import 'sleep_entry_widget.dart';
import 'medicine_entry_widget.dart';
import 'vaccination_entry_widget.dart';
import 'diaper_entry_widget.dart';
import 'milestone_entry_widget.dart';
import 'temperature_entry_widget.dart';
import 'potty_entry_widget.dart';
import 'tummy_time_entry_widget.dart';
import 'story_time_entry_widget.dart';
import 'skin_to_skin_entry_widget.dart';
import 'screen_time_entry_widget.dart';
import 'outdoor_play_entry_widget.dart';
import 'indoor_play_entry_widget.dart';
import 'brush_teeth_entry_widget.dart';
import 'custom_entry_widget.dart';
import 'pumping_entry_widget.dart';
// import '../../../tmp_rovodev_simple_outdoor_play.dart';

/// Centralized registry for activity widgets
class ActivityWidgetRegistry {
  static final Map<String, BaseActivityWidget Function(Function(Map<String, dynamic>))> _widgets = {
    'feeding': (onDataChanged) => FeedingEntryWidget(onDataChanged: onDataChanged),
    'sleep': (onDataChanged) => SleepEntryWidget(onDataChanged: onDataChanged),
    'diaper': (onDataChanged) => DiaperEntryWidget(onDataChanged: onDataChanged),
    'medicine': (onDataChanged) => MedicineEntryWidget(onDataChanged: onDataChanged),
    'vaccination': (onDataChanged) => VaccinationEntryWidget(onDataChanged: onDataChanged),
    'temperature': (onDataChanged) => TemperatureEntryWidget(onDataChanged: onDataChanged),
    'potty': (onDataChanged) => PottyEntryWidget(onDataChanged: onDataChanged),
    'tummy_time': (onDataChanged) => TummyTimeEntryWidget(onDataChanged: onDataChanged),
    'story_time': (onDataChanged) => StoryTimeEntryWidget(onDataChanged: onDataChanged),
    'skin_to_skin': (onDataChanged) => SkinToSkinEntryWidget(onDataChanged: onDataChanged),
    'screen_time': (onDataChanged) => ScreenTimeEntryWidget(onDataChanged: onDataChanged),
    'outdoor_play': (onDataChanged) => OutdoorPlayEntryWidget(onDataChanged: onDataChanged),
    'indoor_play': (onDataChanged) => IndoorPlayEntryWidget(onDataChanged: onDataChanged),
    'brush_teeth': (onDataChanged) => BrushTeethEntryWidget(onDataChanged: onDataChanged),
    'pumping': (onDataChanged) => PumpingEntryWidget(onDataChanged: onDataChanged),
    'custom': (onDataChanged) => CustomEntryWidget(onDataChanged: onDataChanged),
    'milestone': (onDataChanged) => MilestoneEntryWidget(onDataChanged: onDataChanged, babyProfile: null),
  };

  /// Create a widget for the specified activity type
  static BaseActivityWidget? createWidget(String activityType, Function(Map<String, dynamic>) onDataChanged) {
    final factory = _widgets[activityType];
    if (factory != null) {
      return factory(onDataChanged);
    }
    return null;
  }

  /// Get all supported activity types
  static List<String> getSupportedTypes() {
    return _widgets.keys.toList();
  }

  /// Check if an activity type is supported
  static bool isSupported(String activityType) {
    return _widgets.containsKey(activityType);
  }

  /// Register a new widget type
  static void registerWidget(String activityType, BaseActivityWidget Function(Function(Map<String, dynamic>)) factory) {
    _widgets[activityType] = factory;
  }
}

/// Generic activity widget for unsupported types
class GenericActivityWidget extends BaseActivityWidget {
  final String activityType;
  final String title;
  final IconData icon;
  final Color color;

  const GenericActivityWidget({
    super.key,
    required super.onDataChanged,
    required this.activityType,
    required this.title,
    required this.icon,
    required this.color,
  });

  @override
  BaseActivityWidgetState createState() => _GenericActivityWidgetState();
}

class _GenericActivityWidgetState extends BaseActivityWidgetState {
  final TextEditingController _notesController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _notesController.addListener(() {
      updateData({'notes': _notesController.text});
    });
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => (widget as GenericActivityWidget).title;

  @override
  String? getActivityDescription() => 'Record ${getActivityTitle().toLowerCase()} activity details';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'notes': '',
      'time': selectedTime,
    };
  }

  @override
  Widget buildForm() {
    return ActivityFormHelper.buildTextField(
      label: 'Notes',
      controller: _notesController,
      hint: 'Add details about this activity...',
      maxLines: 3,
    );
  }

  @override
  String getActivityType() => (widget as GenericActivityWidget).activityType;

  @override
  IconData getActivityIcon() => (widget as GenericActivityWidget).icon;

  @override
  Color getActivityColor() => (widget as GenericActivityWidget).color;
}
