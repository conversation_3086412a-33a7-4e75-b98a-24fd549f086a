import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class BrushTeethEntryWidget extends BaseActivityWidget {
  const BrushTeethEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _BrushTeethEntryWidgetState();
}

class _BrushTeethEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController durationController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customToothbrushController = TextEditingController();
  final TextEditingController customToothpasteController = TextEditingController();
  final TextEditingController customLocationController = TextEditingController();

  // Toothbrush types
  final List<String> toothbrushOptions = [
    'Baby Finger Brush',
    'Soft Baby Toothbrush',
    'Electric Toothbrush',
    'Silicone Finger Brush',
    'Training Toothbrush',
    'Manual Toothbrush',
    'Washcloth/Gauze',
    'Teething Toothbrush',
    'Other',
  ];

  // Toothpaste types
  final List<String> toothpasteOptions = [
    'Fluoride-Free Baby Toothpaste',
    'Training Toothpaste',
    'Natural Baby Toothpaste',
    'Organic Toothpaste',
    'Gel Toothpaste',
    'Water Only',
    'Xylitol Toothpaste',
    'No Toothpaste',
    'Other',
  ];

  // Location options
  final List<String> locationOptions = [
    'Bathroom',
    'Kitchen Sink',
    'Baby\'s Room',
    'Living Room',
    'Bedroom',
    'Changing Table',
    'High Chair',
    'Other',
  ];

  // Cooperation levels
  final List<String> cooperationOptions = [
    'Very Cooperative',
    'Cooperative',
    'Somewhat Cooperative',
    'Resistant',
    'Very Resistant',
    'Needed Help',
    'Did It Themselves',
  ];

  // Teeth cleaning quality
  final List<String> qualityOptions = [
    'excellent',
    'good',
    'fair',
    'poor',
  ];

  @override
  void initState() {
    super.initState();
    durationController.addListener(_updateDuration);
    notesController.addListener(_updateNotes);
    customToothbrushController.addListener(_updateCustomToothbrush);
    customToothpasteController.addListener(_updateCustomToothpaste);
    customLocationController.addListener(_updateCustomLocation);
  }

  void _updateDuration() {
    final durationText = durationController.text.trim();
    if (durationText.isNotEmpty) {
      final duration = int.tryParse(durationText);
      String? errorMessage;
      
      if (duration == null) {
        errorMessage = 'Please enter number only';
      } else if (duration < 0) {
        errorMessage = 'Please enter a positive number';
      } else if (duration > 30) {
        errorMessage = 'Duration seems too long for teeth brushing';
      }
      
      updateData({
        'duration_text': durationText,
        'duration': duration,
        'duration_error': errorMessage,
      });
    } else {
      updateData({
        'duration_text': '',
        'duration': null,
        'duration_error': null,
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomToothbrush() {
    if (activityData['toothbrush_selection'] == 'Other') {
      final customToothbrush = customToothbrushController.text.trim();
      updateData({
        'custom_toothbrush': customToothbrush,
        'toothbrush': customToothbrush.isNotEmpty ? customToothbrush : 'Other',
      });
    }
  }

  void _updateCustomToothpaste() {
    if (activityData['toothpaste_selection'] == 'Other') {
      final customToothpaste = customToothpasteController.text.trim();
      updateData({
        'custom_toothpaste': customToothpaste,
        'toothpaste': customToothpaste.isNotEmpty ? customToothpaste : 'Other',
      });
    }
  }

  void _updateCustomLocation() {
    if (activityData['location_selection'] == 'Other') {
      final customLocation = customLocationController.text.trim();
      updateData({
        'custom_location': customLocation,
        'location': customLocation.isNotEmpty ? customLocation : 'Other',
      });
    }
  }

  @override
  void dispose() {
    durationController.dispose();
    notesController.dispose();
    customToothbrushController.dispose();
    customToothpasteController.dispose();
    customLocationController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Brush Teeth';

  @override
  String? getActivityDescription() => 'Track dental hygiene and oral care routine for healthy teeth development';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'toothbrush_selection': 'Baby Finger Brush',
      'toothbrush': 'Baby Finger Brush',
      'toothpaste_selection': 'Fluoride-Free Baby Toothpaste',
      'toothpaste': 'Fluoride-Free Baby Toothpaste',
      'location_selection': 'Bathroom',
      'location': 'Bathroom',
      'cooperation_selection': 'Cooperative',
      'cooperation': 'Cooperative',
      'quality_selection': 'good',
      'quality': 'good',
      'duration': null,
      'duration_text': '',
      'notes': '',
      'custom_toothbrush': '',
      'custom_toothpaste': '',
      'custom_location': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isToothbrushOtherSelected = activityData['toothbrush_selection'] == 'Other';
    final isToothpasteOtherSelected = activityData['toothpaste_selection'] == 'Other';
    final isLocationOtherSelected = activityData['location_selection'] == 'Other';
    
    return Column(
      children: [
        // Toothbrush type selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Toothbrush Type',
          value: activityData['toothbrush_selection'],
          items: toothbrushOptions,
          onChanged: (value) {
            updateData({
              'toothbrush_selection': value,
              'toothbrush': value,
            });
            if (value != 'Other') {
              customToothbrushController.clear();
              updateData({'custom_toothbrush': ''});
            }
          },
        ),
        
        // Show custom toothbrush input when "Other" is selected
        if (isToothbrushOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Toothbrush',
            controller: customToothbrushController,
            hint: 'Enter custom toothbrush type...',
          ),
        ],
        
        // Toothpaste type selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Toothpaste/Cleaner',
          value: activityData['toothpaste_selection'],
          items: toothpasteOptions,
          onChanged: (value) {
            updateData({
              'toothpaste_selection': value,
              'toothpaste': value,
            });
            if (value != 'Other') {
              customToothpasteController.clear();
              updateData({'custom_toothpaste': ''});
            }
          },
        ),
        
        // Show custom toothpaste input when "Other" is selected
        if (isToothpasteOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Toothpaste/Cleaner',
            controller: customToothpasteController,
            hint: 'Enter custom toothpaste or cleaner...',
          ),
        ],
        
        // Location selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Location',
          value: activityData['location_selection'],
          items: locationOptions,
          onChanged: (value) {
            updateData({
              'location_selection': value,
              'location': value,
            });
            if (value != 'Other') {
              customLocationController.clear();
              updateData({'custom_location': ''});
            }
          },
        ),
        
        // Show custom location input when "Other" is selected
        if (isLocationOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Location',
            controller: customLocationController,
            hint: 'Enter custom location...',
          ),
        ],
        
        // Cooperation level selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Cooperation Level',
          value: activityData['cooperation_selection'],
          items: cooperationOptions,
          onChanged: (value) {
            updateData({
              'cooperation_selection': value,
              'cooperation': value,
            });
          },
        ),
        
        // Quality selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Cleaning Quality',
          value: activityData['quality_selection'],
          items: qualityOptions,
          onChanged: (value) {
            updateData({
              'quality_selection': value,
              'quality': value,
            });
          },
        ),
        
        // Duration input
        ActivityFormHelper.buildTextField(
          label: 'Duration (minutes)',
          controller: durationController,
          hint: 'How long was the teeth brushing?',
          keyboardType: TextInputType.number,
        ),
        
        // Show duration error if exists
        if (activityData['duration_error'] != null) ...[
          Container(
            margin: EdgeInsets.only(top: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    activityData['duration_error'],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 2.h),
        ],
        
        // Notes
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Any additional details about the teeth brushing session...',
          maxLines: 3,
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'brush_teeth';

  @override
  IconData getActivityIcon() => Icons.cleaning_services;

  @override
  Color getActivityColor() => const Color(0xFF00BCD4);
}