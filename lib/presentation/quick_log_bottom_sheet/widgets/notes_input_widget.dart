import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class NotesInputWidget extends StatefulWidget {
  final Function(String) onNotesChanged;

  const NotesInputWidget({
    super.key,
    required this.onNotesChanged,
  });

  @override
  State<NotesInputWidget> createState() => _NotesInputWidgetState();
}

class _NotesInputWidgetState extends State<NotesInputWidget> {
  final TextEditingController notesController = TextEditingController();
  bool isExpanded = false;


  @override
  void initState() {
    super.initState();
    notesController.addListener(() {
      widget.onNotesChanged(notesController.text);
    });
  }

  @override
  void dispose() {
    notesController.dispose();
    super.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              isExpanded = !isExpanded;
            });
          },
          child: Row(
            children: [
              Text(
                'Notes (Optional)',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              const Spacer(),
              CustomIconWidget(
                iconName: isExpanded ? 'expand_less' : 'expand_more',
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                size: 20,
              ),
            ],
          ),
        ),
        if (isExpanded) ...[
          SizedBox(height: 1.h),

          // Notes Input Field
          Material(
            child: TextFormField(
              controller: notesController,
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'Add any additional notes about this activity...',
                prefixIcon: Padding(
                  padding: EdgeInsets.only(top: 3.w, left: 3.w, right: 3.w),
                  child: CustomIconWidget(
                    iconName: 'edit_note',
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                    size: 20,
                  ),
                ),
                alignLabelWithHint: true,
              ),
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),

          SizedBox(height: 1.h),

          // Character count
          if (notesController.text.isNotEmpty)
            Align(
              alignment: Alignment.centerRight,
              child: Text(
                '${notesController.text.length} characters',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ),
        ],
      ],
    );
  }
}
