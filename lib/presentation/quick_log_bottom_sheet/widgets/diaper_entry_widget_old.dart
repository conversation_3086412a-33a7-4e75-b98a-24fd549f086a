import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';

class DiaperEntryWidget extends StatefulWidget {
  final Function(Map<String, dynamic>) onDataChanged;

  const DiaperEntryWidget({
    super.key,
    required this.onDataChanged,
  });

  @override
  State<DiaperEntryWidget> createState() => _DiaperEntryWidgetState();
}

class _DiaperEntryWidgetState extends State<DiaperEntryWidget> {
  Set<String> selectedTypes = {};
  DateTime selectedTime = ModernDateTimePicker.getCurrentTime();

  final List<Map<String, dynamic>> diaperTypes = [
    {
      'type': 'wet',
      'label': 'Wet',
      'icon': 'water_drop',
      'color': Color(0xFF2196F3),
    },
    {
      'type': 'dirty',
      'label': 'Dirty',
      'icon': 'circle',
      'color': Color(0xFF8D6E63),
    },
    {
      'type': 'mixed',
      'label': 'Both',
      'icon': 'blur_on',
      'color': Color(0xFF9C27B0),
    },
  ];

  @override
  void initState() {
    super.initState();
    // Defer the data update to avoid setState during build
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateData();
    });
  }

  void _updateData() {
    widget.onDataChanged({
      'type': selectedTypes.isNotEmpty ? selectedTypes.toList() : null,
      'time': selectedTime,
    });
  }

  void _selectTime() async {
    final DateTime? picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: selectedTime,
    );

    if (picked != null) {
      setState(() {
        selectedTime = picked;
      });
      _updateData();
    }
  }

  void _toggleDiaperType(String type) {
    setState(() {
      if (type == 'mixed') {
        // If selecting "Both", clear other selections
        selectedTypes.clear();
        selectedTypes.add('mixed');
      } else {
        // If selecting wet or dirty, remove "Both" if present
        selectedTypes.remove('mixed');
        if (selectedTypes.contains(type)) {
          selectedTypes.remove(type);
        } else {
          selectedTypes.add(type);
        }
      }
    });
    _updateData();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Diaper Change',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),

        // Time Selection
        GestureDetector(
          onTap: _selectTime,
          child: Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline
                    .withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'access_time',
                  color: Theme.of(context).colorScheme.primary,
                  size: 20,
                ),
                SizedBox(width: 3.w),
                Text(
                  ModernDateTimePicker.formatDateTime(selectedTime, context),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
                const Spacer(),
                CustomIconWidget(
                  iconName: 'keyboard_arrow_right',
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 20,
                ),
              ],
            ),
          ),
        ),

        SizedBox(height: 3.h),

        Text(
          'Diaper Type',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),

        // Diaper Type Selection
        Column(
          children: diaperTypes.map((diaperType) {
            final isSelected = selectedTypes.contains(diaperType['type']);

            return Container(
              margin: EdgeInsets.only(bottom: 1.h),
              child: GestureDetector(
                onTap: () => _toggleDiaperType(diaperType['type']),
                child: Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? (diaperType['color'] as Color).withValues(alpha: 0.1)
                        : Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? diaperType['color'] as Color
                          : Theme.of(context).colorScheme.outline
                              .withValues(alpha: 0.3),
                      width: isSelected ? 2 : 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(2.w),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? diaperType['color'] as Color
                              : (diaperType['color'] as Color)
                                  .withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: CustomIconWidget(
                          iconName: diaperType['icon'],
                          color: isSelected
                              ? Colors.white
                              : diaperType['color'] as Color,
                          size: 20,
                        ),
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        diaperType['label'],
                        style:
                            Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isSelected
                              ? diaperType['color'] as Color
                              : Theme.of(context).colorScheme.onSurface,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.w400,
                        ),
                      ),
                      const Spacer(),
                      if (isSelected)
                        CustomIconWidget(
                          iconName: 'check_circle',
                          color: diaperType['color'] as Color,
                          size: 20,
                        ),
                    ],
                  ),
                ),
              ),
            );
          }).toList(),
        ),

        if (selectedTypes.isEmpty)
          Container(
            padding: EdgeInsets.all(3.w),
            margin: EdgeInsets.only(top: 1.h),
            decoration: BoxDecoration(
              color: AppTheme.getWarningColor(true).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.getWarningColor(true).withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: 'info',
                  color: AppTheme.getWarningColor(true),
                  size: 16,
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Please select at least one diaper type',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.getWarningColor(true),
                    ),
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }
}
