import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class CustomEntryWidget extends BaseActivityWidget {
  const CustomEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _CustomEntryWidgetState();
}

class _CustomEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController activityNameController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customCategoryController = TextEditingController();

  // Activity categories for better organization (optional)
  final List<String> categoryOptions = [
    'Health & Medical',
    'Development',
    'Social & Family',
    'Learning & Education',
    'Entertainment',
    'Care & Hygiene',
    'Exercise & Movement',
    'Nutrition & Feeding',
    'Sleep & Rest',
    'Safety & Emergency',
    'Travel & Transportation',
    'Special Events',
    'Other',
  ];

  @override
  void initState() {
    super.initState();
    activityNameController.addListener(_updateActivityName);
    notesController.addListener(_updateNotes);
    customCategoryController.addListener(_updateCustomCategory);
  }

  void _updateActivityName() {
    final activityName = activityNameController.text.trim();
    String? errorMessage;
    
    if (activityName.isEmpty) {
      errorMessage = 'Activity name is required';
    } else if (activityName.length < 2) {
      errorMessage = 'Activity name must be at least 2 characters';
    } else if (activityName.length > 100) {
      errorMessage = 'Activity name must be less than 100 characters';
    }
    
    updateData({
      'activity_name_text': activityName,
      'activity_name': activityName,
      'activity_name_error': errorMessage,
    });
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomCategory() {
    if (activityData['category_selection'] == 'Other') {
      final customCategory = customCategoryController.text.trim();
      updateData({
        'custom_category': customCategory,
        'category': customCategory.isNotEmpty ? customCategory : 'Other',
      });
    }
  }

  @override
  void dispose() {
    activityNameController.dispose();
    notesController.dispose();
    customCategoryController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Custom Activity';

  @override
  String? getActivityDescription() => 'Create a custom activity log with your own name and details';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'activity_name': '',
      'activity_name_text': '',
      'category_selection': '', // Optional - no default selection
      'category': '',
      'custom_category': '',
      'notes': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isCategoryOtherSelected = activityData['category_selection'] == 'Other';
    
    return Column(
      children: [
        // Activity name input (required)
        ActivityFormHelper.buildTextField(
          label: 'Activity Name *',
          controller: activityNameController,
          hint: 'Enter the name of your custom activity...',
          maxLines: 1,
        ),
        
        // Show activity name error if exists
        if (activityData['activity_name_error'] != null) ...[
          Container(
            margin: EdgeInsets.only(top: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: Colors.red.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red.shade200),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red.shade600, size: 16),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    activityData['activity_name_error'],
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red.shade600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 2.h),
        ],
        
        // Category selector (optional)
        ActivityFormHelper.buildDropdown<String>(
          label: 'Category (Optional)',
          value: activityData['category_selection']?.isEmpty == true ? null : activityData['category_selection'],
          items: [''] + categoryOptions, // Add empty option for no selection
          onChanged: (value) {
            updateData({
              'category_selection': value ?? '',
              'category': value ?? '',
            });
            if (value != 'Other') {
              customCategoryController.clear();
              updateData({'custom_category': ''});
            }
          },
        ),
        
        // Show custom category input when "Other" is selected
        if (isCategoryOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Category',
            controller: customCategoryController,
            hint: 'Enter custom category...',
          ),
        ],
        
        // Notes input
        ActivityFormHelper.buildTextField(
          label: 'Notes & Details',
          controller: notesController,
          hint: 'Add any additional details about this activity...',
          maxLines: 4,
        ),
        
        // Helper text
        Container(
          margin: EdgeInsets.only(top: 2.h),
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: Colors.blue.shade50,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.blue.shade200),
          ),
          child: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade600, size: 16),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  'Create your own activity log for anything not covered by the standard activities.',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.blue.shade600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'custom';

  @override
  IconData getActivityIcon() => Icons.add_circle;

  @override
  Color getActivityColor() => const Color(0xFF9E9E9E);
}