import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../../core/app_export.dart';
import '../../../services/measurement_units_service.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';

class TemperatureEntryWidget extends BaseActivityWidget {
  const TemperatureEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _TemperatureEntryWidgetState();
}

class _TemperatureEntryWidgetState extends BaseActivityWidgetState {
  final TextEditingController temperatureController = TextEditingController();
  final TextEditingController notesController = TextEditingController();
  final TextEditingController customMethodController = TextEditingController();

  // Temperature measurement methods (A-Z order)
  final List<String> measurementMethods = [
    'Axillary (Armpit)',
    'Oral',
    'Rectal',
    'Temporal (Forehead)',
    'Tympanic (Ear)',
    'Other',
  ];

  // Temperature units - now using centralized service

  @override
  void initState() {
    super.initState();
    temperatureController.addListener(_updateTemperature);
    notesController.addListener(_updateNotes);
    customMethodController.addListener(_updateCustomMethod);
    
    // Initialize controllers with initial data
    if (activityData['temperature'] != null) {
      temperatureController.text = activityData['temperature'].toString();
    }
    if (activityData['notes'] != null) {
      notesController.text = activityData['notes'].toString();
    }
  }

  void _updateTemperature() {
    final tempText = temperatureController.text.trim();
    if (tempText.isEmpty) {
      updateData({
        'temperature_text': '',
        'temperature': null,
        'temperature_error': null,
      });
      return;
    }

    // Validate that input contains only numbers and decimal point
    final RegExp numberRegex = RegExp(r'^[0-9]*\.?[0-9]*$');
    if (!numberRegex.hasMatch(tempText)) {
      updateData({
        'temperature_text': tempText,
        'temperature': null,
        'temperature_error': 'Please enter numbers only',
      });
      return;
    }

    // Parse temperature value
    final tempValue = double.tryParse(tempText);
    if (tempValue == null) {
      updateData({
        'temperature_text': tempText,
        'temperature': null,
        'temperature_error': 'Please enter a valid temperature',
      });
    } else {
      updateData({
        'temperature_text': tempText, // Keep original for display
        'temperature': tempValue, // Numeric value for validation and storage
        'temperature_error': null, // Clear any previous errors
      });
    }
  }

  void _updateNotes() {
    updateData({'notes': notesController.text});
  }

  void _updateCustomMethod() {
    if (activityData['method_selection'] == 'Other') {
      final customMethod = customMethodController.text.trim();
      updateData({
        'custom_method': customMethod,
        // Update the actual measurement_method field for saving
        'measurement_method': customMethod.isNotEmpty ? customMethod : 'Other',
      });
    }
  }

  @override
  void dispose() {
    temperatureController.dispose();
    notesController.dispose();
    customMethodController.dispose();
    super.dispose();
  }

  @override
  String getActivityTitle() => 'Temperature';

  @override
  String? getActivityDescription() => 'Record body temperature readings';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'method_selection': 'Temporal (Forehead)', // For dropdown display
      'measurement_method': 'Temporal (Forehead)', // For actual saving
      'temperature_unit': 'Celsius (°C)', // Default unit
      'temperature': null, // Numeric temperature value
      'temperature_text': '', // Text input for display
      'notes': '',
      'custom_method': '',
      'startTime': ModernDateTimePicker.getCurrentTime(),
    };
  }

  @override
  Widget buildForm() {
    final isOtherSelected = activityData['method_selection'] == 'Other';
    
    return Column(
      children: [
        // Temperature input with validation
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ActivityFormHelper.buildTextField(
              label: 'Temperature',
              controller: temperatureController,
              hint: 'Enter temperature (e.g., 37.5)',
              keyboardType: TextInputType.numberWithOptions(decimal: true),
            ),
            // Show validation error if temperature is invalid
            if (activityData['temperature_error'] != null) ...[
              Padding(
                padding: EdgeInsets.only(top: 1.h, left: 4.w),
                child: Text(
                  activityData['temperature_error'],
                  style: TextStyle(
                    color: Colors.red,
                    fontSize: 12.sp,
                  ),
                ),
              ),
            ],
          ],
        ),
        
        // Temperature unit display (read-only, controlled by settings)
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Unit',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 0.5.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surface,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'thermostat',
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      context.watch<MeasurementUnitsService>().temperatureUnit,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                    Spacer(),
                    Text(
                      'Set in Settings',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // Measurement method selector
        ActivityFormHelper.buildDropdown<String>(
          label: 'Measurement Method',
          value: activityData['method_selection'],
          items: measurementMethods,
          onChanged: (value) {
            updateData({
              'method_selection': value,
              'measurement_method': value, // Initially set to same value
            });
            // Clear custom method if not "Other"
            if (value != 'Other') {
              customMethodController.clear();
              updateData({'custom_method': ''});
            }
          },
        ),
        
        // Show custom method input when "Other" is selected
        if (isOtherSelected) ...[
          ActivityFormHelper.buildTextField(
            label: 'Custom Measurement Method',
            controller: customMethodController,
            hint: 'Enter measurement method...',
          ),
        ],
        
        // Temperature status indicator
        if (activityData['temperature'] != null) ...[
          _buildTemperatureStatus(),
        ],
        
        // Notes field
        ActivityFormHelper.buildTextField(
          label: 'Notes',
          controller: notesController,
          hint: 'Symptoms, medication given, etc.',
          maxLines: 3,
        ),
      ],
    );
  }

  Widget _buildTemperatureStatus() {
    final temperature = activityData['temperature'] as double?;
    final unit = activityData['temperature_unit'] as String?;
    
    if (temperature == null) return Container();
    
    // Convert to Celsius for evaluation if needed
    double tempInCelsius = temperature;
    if (unit == 'Fahrenheit (°F)') {
      tempInCelsius = (temperature - 32) * 5 / 9;
    }
    
    // Determine temperature status for babies/children
    String status;
    Color statusColor;
    IconData statusIcon;
    
    if (tempInCelsius < 36.0) {
      status = 'Low Temperature';
      statusColor = Colors.blue;
      statusIcon = Icons.ac_unit;
    } else if (tempInCelsius >= 36.0 && tempInCelsius <= 37.5) {
      status = 'Normal Temperature';
      statusColor = Colors.green;
      statusIcon = Icons.check_circle;
    } else if (tempInCelsius > 37.5 && tempInCelsius <= 38.5) {
      status = 'Mild Fever';
      statusColor = Colors.orange;
      statusIcon = Icons.warning;
    } else if (tempInCelsius > 38.5 && tempInCelsius <= 40.0) {
      status = 'High Fever';
      statusColor = Colors.red;
      statusIcon = Icons.local_fire_department;
    } else {
      status = 'Very High Fever - Seek Medical Attention';
      statusColor = Colors.red[800]!;
      statusIcon = Icons.emergency;
    }
    
    return Container(
      margin: EdgeInsets.symmetric(vertical: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: statusColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            statusIcon,
            color: statusColor,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  status,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
                Text(
                  '${temperature.toStringAsFixed(1)}${unit == 'Celsius (°C)' ? '°C' : '°F'}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: statusColor.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  @override
  String getActivityType() => 'temperature';

  @override
  IconData getActivityIcon() => Icons.thermostat;

  @override
  Color getActivityColor() => const Color(0xFFFF6B6B);
}