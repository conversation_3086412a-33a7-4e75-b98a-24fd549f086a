import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import 'base_activity_widget.dart';
import 'notes_input_widget.dart';

class DiaperEntryWidget extends BaseActivityWidget {
  const DiaperEntryWidget({
    super.key,
    required super.onDataChanged,
  });

  @override
  BaseActivityWidgetState createState() => _DiaperEntryWidgetState();
}

class _DiaperEntryWidgetState extends BaseActivityWidgetState {
  @override
  String getActivityTitle() => 'Diaper Change';

  @override
  String? getActivityDescription() => 'Record diaper changes and types';

  @override
  Map<String, dynamic> getInitialData() {
    return {
      'type': 'wet',
      'time': ModernDateTimePicker.getCurrentTime(),
      'notes': '',
    };
  }

  @override
  Widget buildForm() {
    return Column(
      children: [
        ActivityFormHelper.buildSelectionRow(
          label: 'Diaper Type',
          options: [
            {'label': 'Wet', 'value': 'wet', 'icon': 'child_care'},
            {'label': 'Dirty', 'value': 'dirty', 'icon': 'child_care'},
            {'label': 'Wet & Dirty', 'value': 'both', 'icon': 'child_care'},
          ],
          selectedValue: activityData['type'] ?? 'wet',
          onChanged: (value) => updateData({'type': value}),
        ),
        SizedBox(height: 2.h),
        NotesInputWidget(
          onNotesChanged: (notes) => updateData({'notes': notes}),
        ),
      ],
    );
  }

  @override
  String getActivityType() => 'diaper';

  @override
  IconData getActivityIcon() => Icons.child_care;

  @override
  Color getActivityColor() => const Color(0xFF7FB069);
}
