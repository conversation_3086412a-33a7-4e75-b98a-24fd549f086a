import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../utils/activity_type_config.dart';
import '../../utils/activity_icon_manager.dart';
import './widgets/activity_type_selector_widget.dart';
import './widgets/diaper_entry_widget.dart';
import './widgets/feeding_entry_widget.dart';
import './widgets/medicine_entry_widget.dart';
import './widgets/vaccination_entry_widget.dart';
import './widgets/temperature_entry_widget.dart';
import './widgets/potty_entry_widget.dart';
import './widgets/tummy_time_entry_widget.dart';
import './widgets/notes_input_widget.dart';
import './widgets/sleep_entry_widget.dart';
import './widgets/activity_widget_registry.dart';
import './widgets/milestone_entry_widget.dart';
import '../../../models/milestone.dart';

class QuickLogBottomSheet extends StatefulWidget {
  final Function()? onDataSaved;
  final String? initialActivityType; // ✅ Added: Support pre-selecting activity type
  final BabyProfile? babyProfile;
  
  const QuickLogBottomSheet({
    super.key,
    this.onDataSaved,
    this.initialActivityType, // ✅ Added: Initial activity type parameter
    this.babyProfile,
  });

  @override
  State<QuickLogBottomSheet> createState() => _QuickLogBottomSheetState();
}

class _QuickLogBottomSheetState extends State<QuickLogBottomSheet>
    with TickerProviderStateMixin {
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  
  late AnimationController _animationController;
  late Animation<double> _slideAnimation;

  String? selectedActivityType;
  Map<String, dynamic> entryData = {};
  bool isValidEntry = false;
  bool isAdvancedMode = false; // Toggle for advanced features

  BabyProfile? _babyProfile;
  
  // Cache for recent feedings to prevent excessive database calls
  List<ActivityLog>? _cachedRecentFeedings;
  DateTime? _lastFeedingsCacheTime;

  // Get activity types from centralized configuration
  List<Map<String, dynamic>> get activityTypes => ActivityTypeConfig.getAllConfigs();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    // Set initial activity type if provided
    if (widget.initialActivityType != null) {
      print('📥 initState: Setting initial activity type to ${widget.initialActivityType}');
      selectedActivityType = widget.initialActivityType;
      // Initialize entry data for the activity type
      _initializeEntryData();
    }

    // Initialize baby profile state manager if needed
    _initializeBabyProfile();

    _animationController.forward();
  }

  void _initializeEntryData() {
    // Initialize entry data based on activity type
    switch (selectedActivityType) {
      case 'milestone':
        entryData = {
          'title': '',
          'description': '',
          'category': 'motor',
          'type': 'gross_motor',
          'achieved_date': DateTime.now().toIso8601String(),
          'age_in_months': 0,
          'age_in_days': 0,
          'notes': '',
          'is_custom': true,
        };
        break;
      // Add other activity types as needed
      default:
        entryData = {};
    }
    // Update validity
    isValidEntry = _validateEntry();
  }

  Future<void> _initializeBabyProfile() async {
    try {
      // Initialize baby profile manager if not already done
      if (!_babyProfileManager.hasBabies) {
        await _babyProfileManager.initialize();
      }
    } catch (e) {
      debugPrint('❌ Error initializing baby profile: $e');
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_babyProfile == null) {
      _babyProfile = widget.babyProfile;
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is BabyProfile) {
        _babyProfile = args;
      } else if (args is Map<String, dynamic> && args['babyProfile'] != null) {
        _babyProfile = args['babyProfile'] as BabyProfile;
      }
    }
    
    // Handle initial activity type
    if (selectedActivityType == null) {
      String? initialType = widget.initialActivityType;
      if (initialType == null) {
        final args = ModalRoute.of(context)?.settings.arguments;
        print('💬 didChangeDependencies: Route arguments: $args');
        if (args is Map<String, dynamic>) {
          initialType = args['initialActivityType'] as String?;
          print('💬 didChangeDependencies: initialType from args: $initialType');
        }
      }
      if (initialType != null && mounted) {
        print('💬 didChangeDependencies: Setting selectedActivityType to $initialType');
        setState(() {
          selectedActivityType = initialType;
          isValidEntry = _validateEntry();
        });
      }
    }
  }

  void _onActivityTypeSelected(String type) {
    print('🔄 _onActivityTypeSelected: Setting selectedActivityType to $type');
    if (mounted) {
      setState(() {
        selectedActivityType = type;
        entryData.clear();
        isValidEntry = false;
      });
    }
    HapticFeedback.selectionClick();
  }

  void _onEntryDataChanged(Map<String, dynamic> data) {
    if (mounted) {
      setState(() {
        entryData = data;
        isValidEntry = _validateEntry();
      });
    }
  }

  bool _validateEntry() {
    if (selectedActivityType == null) return false;

    switch (selectedActivityType) {
      case 'feeding':
        // Enhanced feeding validation (always used for feeding)
        final feedingType = entryData['feeding_type'];
        if (feedingType == 'bottle') {
          return entryData['amount'] != null && entryData['amount'] > 0;
        } else if (feedingType == 'breastfeeding') {
          return entryData['duration'] != null && entryData['duration'] > 0;
        } else if (feedingType == 'solid') {
          return entryData['food_items'] != null && entryData['food_items'].toString().isNotEmpty;
        }
        return entryData['feeding_type'] != null;
      case 'diaper':
        return entryData['type'] != null;
      case 'sleep':
        // Enhanced sleep validation - disable Save button when in live sleep mode
        // Only allow saving when live sleep is stopped or in past sleep mode
        final isLiveSleeping = entryData['is_live_session'] == true && entryData['isCurrentlySleeping'] == true;
        if (isLiveSleeping) {
          return false; // Disable save button during live sleep
        }
        return entryData['isCurrentlySleeping'] == true || entryData['isSleeping'] == true || entryData['startTime'] != null;
      case 'medicine':
        // Check if we have medication, quantity, and unit
        final hasMedication = entryData['medication'] != null && entryData['medication'].toString().isNotEmpty;
        final hasQuantity = entryData['quantity'] != null && entryData['quantity'] is double && entryData['quantity'] > 0;
        final hasUnit = entryData['unit'] != null && entryData['unit'].toString().isNotEmpty;
        
        // If "Other" is selected, check if custom medicine is provided
        if (entryData['medication_selection'] == 'Other') {
          final hasCustomMedicine = entryData['custom_medicine'] != null && entryData['custom_medicine'].toString().trim().isNotEmpty;
          return hasMedication && hasCustomMedicine && hasQuantity && hasUnit;
        }
        
        return hasMedication && hasQuantity && hasUnit;
      case 'vaccination':
        // Check if we have vaccine name
        final hasVaccine = entryData['vaccine'] != null && entryData['vaccine'].toString().isNotEmpty;
        
        // If "Other" is selected, check if custom vaccine is provided
        if (entryData['vaccine_selection'] == 'Other') {
          final hasCustomVaccine = entryData['custom_vaccine'] != null && entryData['custom_vaccine'].toString().trim().isNotEmpty;
          return hasVaccine && hasCustomVaccine;
        }
        
        return hasVaccine;
      case 'temperature':
        // Check if we have a valid temperature reading
        final hasTemperature = entryData['temperature'] != null && entryData['temperature'] is double;
        final hasMethod = entryData['measurement_method'] != null && entryData['measurement_method'].toString().isNotEmpty;
        final hasUnit = entryData['temperature_unit'] != null && entryData['temperature_unit'].toString().isNotEmpty;
        
        // If "Other" is selected, check if custom method is provided
        if (entryData['method_selection'] == 'Other') {
          final hasCustomMethod = entryData['custom_method'] != null && entryData['custom_method'].toString().trim().isNotEmpty;
          return hasTemperature && hasMethod && hasCustomMethod && hasUnit;
        }
        
        return hasTemperature && hasMethod && hasUnit;
      case 'potty':
        // Check if we have potty type, success level, and location
        final hasPottyType = entryData['potty_type'] != null && entryData['potty_type'].toString().isNotEmpty;
        final hasSuccessLevel = entryData['success_level'] != null && entryData['success_level'].toString().isNotEmpty;
        final hasLocation = entryData['location'] != null && entryData['location'].toString().isNotEmpty;
        
        // If "Other" is selected for potty type, check if custom potty type is provided
        if (entryData['potty_type_selection'] == 'Other') {
          final hasCustomType = entryData['custom_potty_type'] != null && entryData['custom_potty_type'].toString().trim().isNotEmpty;
          if (!hasCustomType) return false;
        }
        
        // If "Other" is selected for location, check if custom location is provided
        if (entryData['location_selection'] == 'Other') {
          final hasCustomLocation = entryData['custom_location'] != null && entryData['custom_location'].toString().trim().isNotEmpty;
          if (!hasCustomLocation) return false;
        }
        
        return hasPottyType && hasSuccessLevel && hasLocation;
      case 'tummy_time':
        // Check if we have duration, position, mood, and activity
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasPosition = entryData['position'] != null && entryData['position'].toString().isNotEmpty;
        final hasMood = entryData['mood'] != null && entryData['mood'].toString().isNotEmpty;
        final hasActivity = entryData['activity'] != null && entryData['activity'].toString().isNotEmpty;
        return hasDuration && hasPosition && hasMood && hasActivity;
      case 'milestone':
        // Check if we have milestone title
        final hasTitle = entryData['title'] != null && entryData['title'].toString().trim().isNotEmpty;
        return hasTitle;
      case 'story_time':
        // Check if we have duration, book type, engagement, and activity
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasBookType = entryData['book_type'] != null && entryData['book_type'].toString().isNotEmpty;
        final hasEngagement = entryData['engagement'] != null && entryData['engagement'].toString().isNotEmpty;
        final hasActivity = entryData['activity'] != null && entryData['activity'].toString().isNotEmpty;
        return hasDuration && hasBookType && hasEngagement && hasActivity;
      case 'skin_to_skin':
        // Check if we have duration, position, location, and benefit
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasPosition = entryData['position'] != null && entryData['position'].toString().isNotEmpty;
        final hasLocation = entryData['location'] != null && entryData['location'].toString().isNotEmpty;
        final hasBenefit = entryData['benefit'] != null && entryData['benefit'].toString().isNotEmpty;
        return hasDuration && hasPosition && hasLocation && hasBenefit;
      case 'screen_time':
        // Check if we have duration, content, device, and purpose
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasContent = entryData['content'] != null && entryData['content'].toString().isNotEmpty;
        final hasDevice = entryData['device'] != null && entryData['device'].toString().isNotEmpty;
        final hasPurpose = entryData['purpose'] != null && entryData['purpose'].toString().isNotEmpty;
        return hasDuration && hasContent && hasDevice && hasPurpose;
      case 'outdoor_play':
        // Check if we have duration, activity, location, and weather
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasActivity = entryData['activity'] != null && entryData['activity'].toString().isNotEmpty;
        final hasLocation = entryData['location'] != null && entryData['location'].toString().isNotEmpty;
        final hasWeather = entryData['weather'] != null && entryData['weather'].toString().isNotEmpty;
        return hasDuration && hasActivity && hasLocation && hasWeather;
      case 'indoor_play':
        // Check if we have duration, activity, location, and toys
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasActivity = entryData['activity'] != null && entryData['activity'].toString().isNotEmpty;
        final hasLocation = entryData['location'] != null && entryData['location'].toString().isNotEmpty;
        final hasToys = entryData['toys'] != null && entryData['toys'].toString().isNotEmpty;
        return hasDuration && hasActivity && hasLocation && hasToys;
      case 'brush_teeth':
        // Check if we have duration, toothbrush, toothpaste, location, cooperation, and quality
        final hasDuration = entryData['duration'] != null && entryData['duration'] is int && entryData['duration'] > 0;
        final hasToothbrush = entryData['toothbrush'] != null && entryData['toothbrush'].toString().isNotEmpty;
        final hasToothpaste = entryData['toothpaste'] != null && entryData['toothpaste'].toString().isNotEmpty;
        final hasLocation = entryData['location'] != null && entryData['location'].toString().isNotEmpty;
        final hasCooperation = entryData['cooperation'] != null && entryData['cooperation'].toString().isNotEmpty;
        final hasQuality = entryData['quality'] != null && entryData['quality'].toString().isNotEmpty;
        return hasDuration && hasToothbrush && hasToothpaste && hasLocation && hasCooperation && hasQuality;
      case 'pumping':
        // Check if we have amount (duration is optional)
        final hasAmount = entryData['amount'] != null && entryData['amount'] is double && entryData['amount'] > 0;
        return hasAmount;
      case 'custom':
        // Check if we have activity name (required only)
        final hasActivityName = entryData['activity_name'] != null && entryData['activity_name'].toString().trim().isNotEmpty;
        return hasActivityName;
      case 'milestone':
        return entryData['title'] != null && 
               entryData['title'].toString().trim().isNotEmpty &&
               entryData['description'] != null && 
               entryData['description'].toString().trim().isNotEmpty;
      default:
        // For simple activities, just require a timestamp
        return true;
    }
  }

  Future<void> _saveActivityLog() async {
    try {
      if (_babyProfile == null) {
        throw Exception('No baby profile selected');
      }
      final babyId = _babyProfile!.id;
      if (babyId == '00000000-0000-0000-0000-000000000000') {
        throw Exception('No valid baby profile selected. Please select a baby first.');
      }

      if (selectedActivityType == 'milestone') {
        // Parse the date properly - it comes as a date-only string
        DateTime achievedDate;
        if (entryData['milestone_date'] != null) {
          if (entryData['milestone_date'] is DateTime) {
            // Use the DateTime directly to preserve the exact timestamp
            achievedDate = entryData['milestone_date'] as DateTime;
          } else if (entryData['milestone_date'] is String) {
            // Parse string format
            final dateStr = entryData['milestone_date'] as String;
            if (dateStr.contains('T')) {
              achievedDate = DateTime.parse(dateStr);
            } else {
              // Date only - use current time
              final dateParts = dateStr.split('-');
              achievedDate = DateTime(
                int.parse(dateParts[0]),
                int.parse(dateParts[1]),
                int.parse(dateParts[2]),
                DateTime.now().hour,
                DateTime.now().minute,
                DateTime.now().second,
              );
            }
          } else {
            achievedDate = DateTime.now();
          }
        } else {
          achievedDate = DateTime.now();
        }
        
        final milestone = Milestone(
          babyId: babyId,
          title: entryData['title'],
          description: entryData['description'],
          category: MilestoneCategory.values.firstWhere(
            (e) => e.name == entryData['category'],
            orElse: () => MilestoneCategory.motor,
          ),
          type: MilestoneType.values.firstWhere(
            (e) => e.name == entryData['type'],
            orElse: () => MilestoneType.gross_motor,
          ),
          achievedDate: achievedDate,
          ageInMonths: entryData['age_in_months'],
          ageInDays: entryData['age_in_days'],
          notes: null, // Notes column doesn't exist in the database
          photoUrls: entryData['photo_urls'] != null ? List<String>.from(entryData['photo_urls']) : null,
          isCustom: entryData['is_custom'] ?? true,
        );
        
        // Save to milestones table only
        await _supabaseService.insertMilestone(milestone);
        debugPrint('✅ Milestone saved to milestones table');
        
        if (widget.onDataSaved != null) {
          widget.onDataSaved!();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Milestone "${milestone.title}" logged successfully!',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                ),
              ),
              backgroundColor: ThemeAwareColors.getSuccessColor(context),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: EdgeInsets.all(4.w),
            ),
          );
          _dismissSheet();
        }
      } else {
        // Try to get baby ID from widget first, then from state manager
        String? babyId = _babyProfile?.id;
        if (babyId == null) {
          babyId = _babyProfileManager.getActiveBabyId();
        }
        
        if (babyId == null) {
          throw Exception('No active baby profile found.');
        }

        // Always provide a valid DateTime for startTime
        final Map<String, dynamic> normalizedData = Map<String, dynamic>.from(entryData);
        if (selectedActivityType == 'feeding') {
          normalizedData['startTime'] = entryData['time'] ?? DateTime.now();
        } else if (selectedActivityType == 'diaper') {
          normalizedData['startTime'] = entryData['time'] ?? DateTime.now();
        } else if (selectedActivityType == 'medicine') {
          normalizedData['startTime'] = entryData['startTime'] ?? DateTime.now();
        } else if (selectedActivityType == 'sleep') {
          normalizedData['startTime'] = entryData['startTime'] ?? DateTime.now();
        } else if (selectedActivityType == 'milestone') {
          normalizedData['startTime'] = DateTime.parse(entryData['achieved_date'] ?? DateTime.now().toIso8601String());
        } else {
          normalizedData['startTime'] = DateTime.now();
        }

        final activity = ActivityLog.fromRawData(
          babyId: babyId,
          type: selectedActivityType!,
          data: normalizedData,
        );

        await _supabaseService.insertActivityLog(activity);

        if (widget.onDataSaved != null) {
          widget.onDataSaved!();
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '${activity.type.toDisplayString()} logged successfully!',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                ),
              ),
              backgroundColor: ThemeAwareColors.getSuccessColor(context),
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: EdgeInsets.all(4.w),
            ),
          );
          _dismissSheet();
        }
      }
    } catch (e) {
      debugPrint('Error saving log: $e');
      if (mounted) {
        String errorMessage = 'Failed to save log. Please try again.';
        if (e.toString().contains('No valid baby profile')) {
          errorMessage = e.toString().replaceFirst('Exception: ', '');
        }
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              errorMessage,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
          ),
        );
      }
      rethrow;
    }
  }

  void _navigateToFullEntry() {
    String route = '/dashboard';
    switch (selectedActivityType) {
      case 'feeding':
        // Open Quick Log in advanced mode for feeding
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'feeding',
            babyProfile: _babyProfile,
            onDataSaved: widget.onDataSaved,
          ),
        );
        return;
      case 'sleep':
        // Open Quick Log in advanced mode for sleep
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'sleep',
            babyProfile: _babyProfile,
            onDataSaved: widget.onDataSaved,
          ),
        );
        return;
      case 'growth':
        route = '/growth-charts';
        break;
      default:
        route = '/tracker-screen';
    }

    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    }
    Navigator.pushNamed(context, route);
  }

  void _dismissSheet() {
    _animationController.reverse().then((_) {
      if (mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      minChildSize: 0.5,
      maxChildSize: 0.95,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Container(
                    margin: EdgeInsets.symmetric(vertical: 2.h),
                    width: 15.w,
                    height: 0.5.h,
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(10),
                    ),
                  ),
                  // Title and close button
                  _buildHeader(),
                  // Main content
                  Expanded(
                    child: SingleChildScrollView(
                      controller: scrollController,
                      padding: EdgeInsets.symmetric(horizontal: 5.w),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          if (selectedActivityType == null && widget.initialActivityType == null) ...[
                            SizedBox(height: 2.h),
                            ActivityTypeSelectorWidget(
                              onTypeSelected: _onActivityTypeSelected,
                              selectedType: selectedActivityType,
                              activityTypes: activityTypes,
                            ),
                          ],
                          if (selectedActivityType != null) ...[
                            SizedBox(height: 3.h),
                            _buildEntryForm(),
                            SizedBox(height: 3.h),
                            _buildRecentLogsSection(),
                          ],
                          SizedBox(height: 3.h),
                        ],
                      ),
                    ),
                  ),
                  // Bottom action buttons
                  Container(
                    padding: EdgeInsets.all(5.w).copyWith(top: 2.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).cardColor,
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                          blurRadius: 5,
                          offset: Offset(0, -3),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: isValidEntry ? _saveActivityLog : null,
                            style: ElevatedButton.styleFrom(
                              padding: EdgeInsets.symmetric(vertical: 2.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text('Save'),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDragHandle() {
    return Container(
      margin: EdgeInsets.only(top: 1.h),
      child: Container(
        width: 10.w,
        height: 0.5.h,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(4.w),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'Quick Log',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.w700,
                      ),
                ),
                if (_babyProfile != null)
                  Text(
                    'for ${_babyProfile!.name}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _dismissSheet,
            child: Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                shape: BoxShape.circle,
              ),
              child: CustomIconWidget(
                iconName: 'close',
                size: 5.w,
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedActivityHeader() {
    final selectedType = activityTypes.firstWhere(
      (type) => type['type'] == selectedActivityType,
    );

    return Row(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              selectedActivityType = null;
              entryData.clear();
              isValidEntry = false;
            });
          },
          child: Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
              shape: BoxShape.circle,
            ),
            child: CustomIconWidget(
              iconName: 'arrow_back',
              size: 5.w,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)!,
            ),
          ),
        ),
        SizedBox(width: 3.w),
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: selectedType['color'].withValues(alpha: 0.15),
            shape: BoxShape.circle,
          ),
          child: CustomIconWidget(
            iconName: selectedType['icon'],
            color: selectedType['color'],
            size: 6.w,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                selectedType['label'],
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
              ),
              Text(
                selectedType['description'],
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEntryForm() {
    if (selectedActivityType == null) {
      return Container();
    }

    print('🔍 _buildEntryForm: selectedActivityType = $selectedActivityType');

    // Use a ValueKey to ensure proper widget recycling
    final key = ValueKey('entry_form_${selectedActivityType}');
    
    switch (selectedActivityType) {
      case 'feeding':
        return FeedingEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'diaper':
        return DiaperEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'sleep':
        return SleepEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'medicine':
        return MedicineEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'vaccination':
        return VaccinationEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'temperature':
        return TemperatureEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'potty':
        return PottyEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'tummy_time':
        return TummyTimeEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
        );
      case 'story_time':
        return ActivityWidgetRegistry.createWidget('story_time', _onEntryDataChanged) ?? Container();
      case 'skin_to_skin':
        return ActivityWidgetRegistry.createWidget('skin_to_skin', _onEntryDataChanged) ?? Container();
      case 'screen_time':
        return ActivityWidgetRegistry.createWidget('screen_time', _onEntryDataChanged) ?? Container();
      case 'outdoor_play':
        return ActivityWidgetRegistry.createWidget('outdoor_play', _onEntryDataChanged) ?? Container();
      case 'indoor_play':
        return ActivityWidgetRegistry.createWidget('indoor_play', _onEntryDataChanged) ?? Container();
      case 'brush_teeth':
        return ActivityWidgetRegistry.createWidget('brush_teeth', _onEntryDataChanged) ?? Container();
      case 'pumping':
        return ActivityWidgetRegistry.createWidget('pumping', _onEntryDataChanged) ?? Container();
      case 'custom':
        return ActivityWidgetRegistry.createWidget('custom', _onEntryDataChanged) ?? Container();
      case 'milestone':
        return MilestoneEntryWidget(
          key: key,
          onDataChanged: _onEntryDataChanged,
          babyProfile: _babyProfile,
        );
      default:
        return Container(
          key: key,
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[200]!),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CustomIconWidget(
                iconName: 'info',
                color: Colors.grey[500]!,
                size: 8.w,
              ),
              SizedBox(height: 1.h),
              Text(
                'Simple Activity Log',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                'This activity will be logged with current timestamp',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        );
    }
  }

  Widget _buildAdvancedModeToggle() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: isAdvancedMode ? 'tune' : 'flash_on',
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isAdvancedMode ? 'Advanced Mode' : 'Simple Mode',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  isAdvancedMode 
                      ? 'Full tracker features with timers, history & environment'
                      : 'Quick entry with basic options',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: isAdvancedMode,
            onChanged: (value) {
              setState(() {
                isAdvancedMode = value;
                // Clear entry data when switching modes to avoid conflicts
                entryData.clear();
                isValidEntry = false;
              });
              HapticFeedback.selectionClick();
            },
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentFeedingsSection() {
    // Get recent feeding activities from the feeding widget
    final feedingWidget = _buildEntryForm();
    if (feedingWidget is FeedingEntryWidget) {
      // We'll need to access the recent feedings data differently
      // For now, let's create a simplified version that matches Recent Activities format
      return FutureBuilder<List<ActivityLog>>(
        future: _loadRecentFeedings(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          
          final recentFeedings = snapshot.data ?? [];
          if (recentFeedings.isEmpty) {
            return Container();
          }

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Recent Feedings',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextButton(
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        AppRoutes.activityTimeline,
                        arguments: {
                          'baby_profile': _babyProfile,
                          'filter_type': 'feeding',
                        },
                      );
                    },
                    child: Text(
                      'View All',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 1.h),
              ...recentFeedings.take(10).map((feeding) {
                final feedingMap = feeding.toRecentActivityMap();
                return _buildRecentLogItem(
                  feedingMap,
                  ActivityIconManager.getActivityIconWithBackground(
                    activityType: 'feeding',
                    size: 5.w,
                  ),
                  _buildFeedingDetails(feedingMap),
                );
              }).toList(),
            ],
          );
        },
      );
    }
    return Container();
  }

  String _buildFeedingDetails(Map<String, dynamic> feeding) {
    // Use the type_detail that's already properly formatted by ActivityLog.toRecentActivityMap()
    // This includes duration for breast feeding, amount for bottle feeding, etc.
    if (feeding['type_detail'] != null && feeding['type_detail'].toString().isNotEmpty) {
      return feeding['type_detail'].toString();
    }
    
    // Fallback to manual building if type_detail is not available
    final List<String> details = [];

    if (feeding['amount'] != null) {
      details.add(feeding['amount']);
    }
    if (feeding['feeding_type'] != null && feeding['feeding_type'] != '') {
      details.add(feeding['feeding_type']);
    }
    if (feeding['duration'] != null && feeding['feeding_type'] == 'breast') {
      details.add(feeding['duration']);
    }
    if (feeding['side'] != null && feeding['feeding_type'] == 'breast') {
      String sideDisplay = feeding['side'];
      if (sideDisplay == 'both') {
        sideDisplay = 'Left & Right';
      } else if (sideDisplay == 'left') {
        sideDisplay = 'Left';
      } else if (sideDisplay == 'right') {
        sideDisplay = 'Right';
      }
      details.add('Side: $sideDisplay');
    }
    if (feeding['formula_type'] != null && feeding['formula_type'] != '') {
      details.add(feeding['formula_type']);
    }
    if (feeding['meal_type'] != null && feeding['meal_type'] != '') {
      details.add(feeding['meal_type']);
    }
    if (feeding['food_items'] != null && feeding['food_items'] != '') {
      details.add(feeding['food_items']);
    }
    if (feeding['mood'] != null && feeding['mood'] != '') {
      details.add('Mood: ${feeding['mood']}');
    }

    return details.join(', ');
  }

  Future<List<ActivityLog>> _loadRecentFeedings() async {
    try {
      // Use cache if it's fresh (less than 30 seconds old)
      final now = DateTime.now();
      if (_cachedRecentFeedings != null && 
          _lastFeedingsCacheTime != null &&
          now.difference(_lastFeedingsCacheTime!).inSeconds < 30) {
        return _cachedRecentFeedings!;
      }

      final babyProfile = _babyProfile ?? _babyProfileManager.activeBaby;
      if (babyProfile == null) return [];

      final activities = await _supabaseService.getRecentActivities(
        babyProfile.id,
        limit: 10,
      );
      
      // Filter for feeding activities and cache the result
      final recentFeedings = activities.where((activity) => activity.type.name == 'feeding').toList();
      _cachedRecentFeedings = recentFeedings;
      _lastFeedingsCacheTime = now;
      
      return recentFeedings;
    } catch (e) {
      debugPrint('❌ Error loading recent feedings: $e');
      return [];
    }
  }

  String _formatRelativeTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Widget _buildRecentSleepSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentSleep(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentSleep = snapshot.data ?? [];
        if (recentSleep.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Sleep Sessions',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'sleep',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentSleep.take(10).map((sleep) {
              final sleepMap = sleep.toRecentActivityMap();
              return _buildRecentLogItem(
                sleepMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'sleep',
                  size: 5.w,
                ),
                _buildSleepDetails(sleepMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildSleepDetails(Map<String, dynamic> sleep) {
    final List<String> details = [];

    if (sleep['duration'] != null && sleep['duration'] != '0m') {
      details.add(sleep['duration']);
    }
    if (sleep['sleep_quality'] != null && sleep['sleep_quality'] != '') {
      details.add('Quality: ${sleep['sleep_quality']}');
    }
    if (sleep['sleep_location'] != null && sleep['sleep_location'] != '') {
      details.add('Location: ${sleep['sleep_location']}');
    }
    if (sleep['room_temperature'] != null && sleep['room_temperature'] != '') {
      details.add('Temp: ${sleep['room_temperature']}C');
    }

    return details.join(', ');
  }

  Future<List<ActivityLog>> _loadRecentSleep() async {
    try {
      final babyProfile = _babyProfile ?? _babyProfileManager.activeBaby;
      if (babyProfile == null) return [];

      final activities = await _supabaseService.getRecentActivities(
        babyProfile.id,
        limit: 10,
      );
      
      // Filter for sleep activities
      return activities.where((activity) => activity.type.name == 'sleep').toList();
    } catch (e) {
      debugPrint('❌ Error loading recent sleep: $e');
      return [];
    }
  }

  Widget _buildRecentLogsSection() {
    if (selectedActivityType == null) return Container();

    switch (selectedActivityType) {
      case 'feeding':
        return _buildRecentFeedingsSection();
      case 'sleep':
        return _buildRecentSleepSection();
      case 'diaper':
        return _buildRecentDiapersSection();
      case 'medicine':
        return _buildRecentMedicineSection();
      case 'vaccination':
        return _buildRecentVaccinationSection();
      case 'temperature':
        return _buildRecentTemperatureSection();
      case 'potty':
        return _buildRecentPottySection();
      case 'tummy_time':
        return _buildRecentTummyTimeSection();
      case 'story_time':
        return _buildRecentStoryTimeSection();
      case 'skin_to_skin':
        return _buildRecentSkinToSkinSection();
      case 'screen_time':
        return _buildRecentScreenTimeSection();
      case 'milestone':
        return _buildRecentMilestonesSection();
      case 'outdoor_play':
        return _buildRecentOutdoorPlaySection();
      case 'indoor_play':
        return _buildRecentIndoorPlaySection();
      case 'brush_teeth':
        return _buildRecentBrushTeethSection();
      case 'pumping':
        return _buildRecentPumpingSection();
      case 'custom':
        return _buildRecentCustomSection();
      default:
        return _buildRecentGenericSection();
    }
  }

  Widget _buildRecentDiapersSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('diaper'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Diapers',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'diaper',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'diaper',
                  size: 5.w,
                ),
                _buildDiaperDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentMedicineSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('medicine'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Medicine',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'medicine',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'medicine',
                  size: 5.w,
                ),
                _buildMedicineDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentVaccinationSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('vaccination'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Vaccinations',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'vaccination',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'vaccination',
                  size: 5.w,
                ),
                _buildVaccinationDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentTemperatureSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('temperature'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Temperature',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'temperature',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'temperature',
                  size: 5.w,
                ),
                _buildTemperatureDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentPottySection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('potty'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Potty',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'potty',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'potty',
                  size: 5.w,
                ),
                _buildPottyDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentTummyTimeSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('tummy_time'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Tummy Time',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'tummy_time',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'tummy_time',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildTummyTimeDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentStoryTimeSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('story_time'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Story Time',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'story_time',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'story_time',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildStoryTimeDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentMilestonesSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('milestone'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Milestones',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'milestone',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'milestone',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? '',
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentGenericSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType(selectedActivityType!),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        final activityType = activityTypes.firstWhere(
          (type) => type['type'] == selectedActivityType,
          orElse: () => <String, Object>{'label': selectedActivityType!, 'icon': 'add_circle', 'color': Colors.grey},
        );

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent ${activityType['label']}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': selectedActivityType,
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: selectedActivityType!,
                  size: 5.w,
                ),
                logMap['notes'] ?? '',
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentLogItem(
    Map<String, dynamic> logMap,
    Widget iconWidget,
    String details,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 1.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 10.w,
            height: 10.w,
            child: iconWidget,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  logMap['title'] ?? 'Activity',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (details.isNotEmpty) ...[
                  Text(
                    details,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
                if (logMap['notes'] != null && logMap['notes'].toString().isNotEmpty) ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    'Note: ${logMap['notes']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ],
            ),
          ),
          Text(
            _formatRelativeTime(logMap['timestamp']),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _buildDiaperDetails(Map<String, dynamic> diaper) {
    final List<String> details = [];
    
    if (diaper['diaper_type'] != null && diaper['diaper_type'] != '') {
      details.add('Type: ${diaper['diaper_type']}');
    }
    
    return details.join(', ');
  }

  String _buildMedicineDetails(Map<String, dynamic> medicine) {
    final List<String> details = [];
    
    // Add medication name
    if (medicine['medication'] != null && medicine['medication'].toString().isNotEmpty) {
      details.add(medicine['medication']);
    }
    
    // Add dosage
    if (medicine['dosage'] != null && medicine['dosage'].toString().isNotEmpty) {
      details.add(medicine['dosage']);
    }
    
    return details.join(', ');
  }

  String _buildVaccinationDetails(Map<String, dynamic> vaccination) {
    final List<String> details = [];
    
    // Add vaccine name
    if (vaccination['vaccine'] != null && vaccination['vaccine'].toString().isNotEmpty) {
      details.add(vaccination['vaccine']);
    }
    
    return details.join(', ');
  }

  String _buildTemperatureDetails(Map<String, dynamic> temperature) {
    final List<String> details = [];
    
    // Add temperature reading
    if (temperature['temperature_reading'] != null && temperature['temperature_reading'].toString().isNotEmpty) {
      details.add(temperature['temperature_reading']);
    }
    
    // Add temperature status
    if (temperature['temperature_status'] != null && temperature['temperature_status'].toString().isNotEmpty) {
      details.add('(${temperature['temperature_status']})');
    }
    
    // Add measurement method
    if (temperature['measurement_method'] != null && temperature['measurement_method'].toString().isNotEmpty) {
      details.add(temperature['measurement_method']);
    }
    
    return details.join(' - ');
  }

  String _buildPottyDetails(Map<String, dynamic> potty) {
    final List<String> details = [];
    
    // Add potty type
    if (potty['potty_type'] != null && potty['potty_type'].toString().isNotEmpty) {
      details.add(potty['potty_type']);
    }
    
    // Add success level
    if (potty['success_level'] != null && potty['success_level'].toString().isNotEmpty) {
      details.add('(${potty['success_level']})');
    }
    
    // Add location
    if (potty['location'] != null && potty['location'].toString().isNotEmpty) {
      details.add(potty['location']);
    }
    
    return details.join(' - ');
  }

  String _buildTummyTimeDetails(Map<String, dynamic> tummyTime) {
    final List<String> details = [];
    
    // Add activity
    if (tummyTime['activity'] != null && tummyTime['activity'].toString().isNotEmpty) {
      details.add('Activity: ${tummyTime['activity']}');
    }
    
    // Add position
    if (tummyTime['position'] != null && tummyTime['position'].toString().isNotEmpty) {
      details.add('Position: ${tummyTime['position']}');
    }
    
    // Add mood
    if (tummyTime['mood'] != null && tummyTime['mood'].toString().isNotEmpty) {
      details.add('Mood: ${tummyTime['mood']}');
    }
    
    // Add duration
    if (tummyTime['duration'] != null && tummyTime['duration'] > 0) {
      details.add('Duration: ${tummyTime['duration']} min');
    }
    
    return details.join(', ');
  }

  String _buildStoryTimeDetails(Map<String, dynamic> storyTime) {
    final List<String> details = [];
    
    // Add book type
    if (storyTime['book_type'] != null && storyTime['book_type'].toString().isNotEmpty) {
      details.add('Book: ${storyTime['book_type']}');
    }
    
    // Add activity
    if (storyTime['activity'] != null && storyTime['activity'].toString().isNotEmpty) {
      details.add('Activity: ${storyTime['activity']}');
    }
    
    // Add engagement
    if (storyTime['engagement'] != null && storyTime['engagement'].toString().isNotEmpty) {
      details.add('Engagement: ${storyTime['engagement']}');
    }
    
    // Add duration
    if (storyTime['duration'] != null && storyTime['duration'] > 0) {
      details.add('Duration: ${storyTime['duration']} min');
    }
    
    return details.join(', ');
  }

  Widget _buildRecentSkinToSkinSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('skin_to_skin'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Skin to Skin',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'skin_to_skin',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'skin_to_skin',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildSkinToSkinDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildSkinToSkinDetails(Map<String, dynamic> skinToSkin) {
    final List<String> details = [];
    
    // Add position
    if (skinToSkin['position'] != null && skinToSkin['position'].toString().isNotEmpty) {
      details.add('Position: ${skinToSkin['position']}');
    }
    
    // Add location
    if (skinToSkin['location'] != null && skinToSkin['location'].toString().isNotEmpty) {
      details.add('Location: ${skinToSkin['location']}');
    }
    
    // Add benefit
    if (skinToSkin['benefit'] != null && skinToSkin['benefit'].toString().isNotEmpty) {
      details.add('Benefit: ${skinToSkin['benefit']}');
    }
    
    // Add duration
    if (skinToSkin['duration'] != null && skinToSkin['duration'] > 0) {
      details.add('Duration: ${skinToSkin['duration']} min');
    }
    
    return details.join(', ');
  }

  Widget _buildRecentScreenTimeSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('screen_time'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Screen Time',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'screen_time',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'screen_time',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildScreenTimeDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildScreenTimeDetails(Map<String, dynamic> screenTime) {
    final List<String> details = [];
    
    // Add content
    if (screenTime['content'] != null && screenTime['content'].toString().isNotEmpty) {
      details.add('Content: ${screenTime['content']}');
    }
    
    // Add device
    if (screenTime['device'] != null && screenTime['device'].toString().isNotEmpty) {
      details.add('Device: ${screenTime['device']}');
    }
    
    // Add purpose
    if (screenTime['purpose'] != null && screenTime['purpose'].toString().isNotEmpty) {
      details.add('Purpose: ${screenTime['purpose']}');
    }
    
    // Add duration
    if (screenTime['duration'] != null && screenTime['duration'] > 0) {
      details.add('Duration: ${screenTime['duration']} min');
    }
    
    return details.join(', ');
  }

  Widget _buildRecentOutdoorPlaySection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('outdoor_play'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Outdoor Play',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'outdoor_play',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'outdoor_play',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildOutdoorPlayDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildOutdoorPlayDetails(Map<String, dynamic> outdoorPlay) {
    final List<String> details = [];
    
    // Add activity type
    if (outdoorPlay['activity'] != null && outdoorPlay['activity'].toString().isNotEmpty) {
      details.add('Activity: ${outdoorPlay['activity']}');
    }
    
    // Add location
    if (outdoorPlay['location'] != null && outdoorPlay['location'].toString().isNotEmpty) {
      details.add('Location: ${outdoorPlay['location']}');
    }
    
    // Add weather
    if (outdoorPlay['weather'] != null && outdoorPlay['weather'].toString().isNotEmpty) {
      details.add('Weather: ${outdoorPlay['weather']}');
    }
    
    // Add duration
    if (outdoorPlay['duration'] != null && outdoorPlay['duration'] > 0) {
      details.add('Duration: ${outdoorPlay['duration']} min');
    }
    
    return details.join(', ');
  }

  Widget _buildRecentIndoorPlaySection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('indoor_play'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Indoor Play',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'indoor_play',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'indoor_play',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildIndoorPlayDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildIndoorPlayDetails(Map<String, dynamic> indoorPlay) {
    final List<String> details = [];
    
    // Add activity type
    if (indoorPlay['activity'] != null && indoorPlay['activity'].toString().isNotEmpty) {
      details.add('Activity: ${indoorPlay['activity']}');
    }
    
    // Add location
    if (indoorPlay['location'] != null && indoorPlay['location'].toString().isNotEmpty) {
      details.add('Location: ${indoorPlay['location']}');
    }
    
    // Add toys/materials
    if (indoorPlay['toys'] != null && indoorPlay['toys'].toString().isNotEmpty) {
      details.add('Toys: ${indoorPlay['toys']}');
    }
    
    // Add duration
    if (indoorPlay['duration'] != null && indoorPlay['duration'] > 0) {
      details.add('Duration: ${indoorPlay['duration']} min');
    }
    
    return details.join(', ');
  }

  Widget _buildRecentBrushTeethSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('brush_teeth'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Brush Teeth',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'brush_teeth',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'brush_teeth',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildBrushTeethDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildBrushTeethDetails(Map<String, dynamic> brushTeeth) {
    final List<String> details = [];
    
    // Add toothbrush type
    if (brushTeeth['toothbrush'] != null && brushTeeth['toothbrush'].toString().isNotEmpty) {
      details.add('Toothbrush: ${brushTeeth['toothbrush']}');
    }
    
    // Add toothpaste
    if (brushTeeth['toothpaste'] != null && brushTeeth['toothpaste'].toString().isNotEmpty) {
      details.add('Toothpaste: ${brushTeeth['toothpaste']}');
    }
    
    // Add cooperation level
    if (brushTeeth['cooperation'] != null && brushTeeth['cooperation'].toString().isNotEmpty) {
      details.add('Cooperation: ${brushTeeth['cooperation']}');
    }
    
    // Add quality
    if (brushTeeth['quality'] != null && brushTeeth['quality'].toString().isNotEmpty) {
      details.add('Quality: ${brushTeeth['quality']}');
    }
    
    // Add duration
    if (brushTeeth['duration'] != null && brushTeeth['duration'] > 0) {
      details.add('Duration: ${brushTeeth['duration']} min');
    }
    
    return details.join(', ');
  }

  Widget _buildRecentPumpingSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('pumping'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Pumping',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'pumping',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'pumping',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildPumpingDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  Widget _buildRecentCustomSection() {
    return FutureBuilder<List<ActivityLog>>(
      future: _loadRecentActivitiesByType('custom'),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        final recentLogs = snapshot.data ?? [];
        if (recentLogs.isEmpty) {
          return Container();
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Recent Custom Activities',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      AppRoutes.activityTimeline,
                      arguments: {
                        'baby_profile': _babyProfile,
                        'filter_type': 'custom',
                      },
                    );
                  },
                  child: Text(
                    'View All',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            ...recentLogs.take(10).map((log) {
              final logMap = log.toRecentActivityMap();
              return _buildRecentLogItem(
                logMap,
                ActivityIconManager.getActivityIconWithBackground(
                  activityType: 'custom',
                  size: 5.w,
                ),
                logMap['type_detail'] ?? _buildCustomDetails(logMap),
              );
            }).toList(),
          ],
        );
      },
    );
  }

  String _buildPumpingDetails(Map<String, dynamic> pumping) {
    final List<String> details = [];
    
    // Add duration
    if (pumping['duration'] != null && pumping['duration'] > 0) {
      details.add('Duration: ${pumping['duration']} min');
    }
    
    // Add amount
    if (pumping['amount'] != null && pumping['amount'] > 0) {
      details.add('Amount: ${pumping['amount']} ml');
    }
    
    // Add side with proper display text
    if (pumping['side'] != null && pumping['side'].toString().isNotEmpty) {
      String sideDisplay = pumping['side'];
      if (sideDisplay == 'both') {
        sideDisplay = 'Left & Right';
      } else if (sideDisplay == 'left') {
        sideDisplay = 'Left';
      } else if (sideDisplay == 'right') {
        sideDisplay = 'Right';
      }
      details.add('Side: $sideDisplay');
    }
    
    return details.join(', ');
  }

  String _buildCustomDetails(Map<String, dynamic> custom) {
    final List<String> details = [];
    
    // Add activity name
    if (custom['activity_name'] != null && custom['activity_name'].toString().isNotEmpty) {
      details.add('${custom['activity_name']}');
    }
    
    // Add category if provided
    if (custom['category'] != null && custom['category'].toString().isNotEmpty) {
      details.add('Category: ${custom['category']}');
    }
    
    return details.isNotEmpty ? details.join(' - ') : 'Custom Activity';
  }

  Future<List<ActivityLog>> _loadRecentActivitiesByType(String activityType) async {
    try {
      final babyProfile = _babyProfile ?? _babyProfileManager.activeBaby;
      if (babyProfile == null) return [];

      final activities = await _supabaseService.getRecentActivities(
        babyProfile.id,
        limit: 10,
      );
      
      // Filter for specific activity type
      return activities.where((activity) => activity.type.name == activityType).toList();
    } catch (e) {
      debugPrint('❌ Error loading recent $activityType: $e');
      return [];
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'restaurant': return Icons.restaurant;
      case 'bedtime': return Icons.bedtime;
      case 'child_care': return Icons.child_care;
      case 'medication': return Icons.medication;
      case 'vaccines': return Icons.vaccines;
      case 'thermostat': return Icons.thermostat;
      case 'wc': return Icons.wc;
      case 'bathtub': return Icons.bathtub;
      case 'fitness_center': return Icons.fitness_center;
      case 'menu_book': return Icons.menu_book;
      case 'tv': return Icons.tv;
      case 'favorite': return Icons.favorite;
      case 'park': return Icons.park;
      case 'toys': return Icons.toys;
      case 'local_drink': return Icons.local_drink;
      case 'trending_up': return Icons.trending_up;
      case 'emoji_events': return Icons.emoji_events;
      default: return Icons.add_circle;
    }
  }

  Widget _buildActionButtons() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        border: Border(
          top: BorderSide(
            color: Colors.grey[200]!,
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton(
              onPressed: isValidEntry ? _saveActivityLog : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: isValidEntry
                    ? Theme.of(context).colorScheme.primary
                    : AppTheme.dividerLight,
                padding: EdgeInsets.symmetric(vertical: 3.h),
                elevation: isValidEntry ? 3 : 0,
              ),
              child: Text(
                'Save',
                style: TextStyle(
                  color: isValidEntry ? Colors.white : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
