import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/notification_item.dart';

class NotificationFilterWidget extends StatelessWidget {
  final NotificationType? selectedFilter;
  final bool showUnreadOnly;
  final Function(NotificationType?) onFilterChanged;
  final Function(bool) onUnreadToggled;

  const NotificationFilterWidget({
    super.key,
    this.selectedFilter,
    required this.showUnreadOnly,
    required this.onFilterChanged,
    required this.onUnreadToggled,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        border: Border(
          bottom: BorderSide(
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter by type dropdown
          Row(
            children: [
              Text(
                'Filter by type:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<NotificationType?>(
                      value: selectedFilter,
                      isExpanded: true,
                      hint: Text('All Types'),
                      onChanged: onFilterChanged,
                      items: [
                        DropdownMenuItem<NotificationType?>(
                          value: null,
                          child: Text('All Types'),
                        ),
                        ...NotificationType.values.map(
                          (type) => DropdownMenuItem<NotificationType?>(
                            value: type,
                            child: Row(
                              children: [
                                CustomIconWidget(
                                  iconName: _getTypeIcon(type),
                                  size: 4.w,
                                  color: _getTypeColor(context, type),
                                ),
                                SizedBox(width: 2.w),
                                Text(type.displayName),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          SizedBox(height: 2.h),
          
          // Show unread only toggle
          Row(
            children: [
              Switch(
                value: showUnreadOnly,
                onChanged: onUnreadToggled,
                activeColor: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 2.w),
              Text(
                'Show unread only',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getTypeIcon(NotificationType type) {
    switch (type) {
      case NotificationType.feeding:
        return 'restaurant';
      case NotificationType.sleep:
        return 'bedtime';
      case NotificationType.milestone:
        return 'celebration';
      case NotificationType.aiInsight:
        return 'psychology';
      case NotificationType.dailySummary:
        return 'today';
      case NotificationType.weeklyReport:
        return 'date_range';
      case NotificationType.medicine:
        return 'medication';
      case NotificationType.vaccination:
        return 'vaccines';
      case NotificationType.appointment:
        return 'event';
      case NotificationType.custom:
        return 'notifications';
    }
  }

  Color _getTypeColor(BuildContext context, NotificationType type) {
    switch (type) {
      case NotificationType.feeding:
        return Colors.green;
      case NotificationType.sleep:
        return Colors.indigo;
      case NotificationType.milestone:
        return Colors.amber;
      case NotificationType.aiInsight:
        return Colors.purple;
      case NotificationType.dailySummary:
        return Colors.blue;
      case NotificationType.weeklyReport:
        return Colors.teal;
      case NotificationType.medicine:
        return Colors.red;
      case NotificationType.vaccination:
        return Colors.orange;
      case NotificationType.appointment:
        return Colors.cyan;
      case NotificationType.custom:
        return Theme.of(context).colorScheme.primary;
    }
  }
}