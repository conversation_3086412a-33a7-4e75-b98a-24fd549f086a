import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/notification_item.dart';
import '../../../utils/time_format_utils.dart';

class NotificationItemWidget extends StatelessWidget {
  final NotificationItem notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;

  const NotificationItemWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      decoration: BoxDecoration(
        color: notification.isRead 
            ? Theme.of(context).colorScheme.surface
            : Theme.of(context).colorScheme.surface.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isRead
              ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)
              : Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          width: notification.isRead ? 1 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header row
                Row(
                  children: [
                    // Notification type icon
                    Container(
                      padding: EdgeInsets.all(2.w),
                      decoration: BoxDecoration(
                        color: _getTypeColor(context).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: CustomIconWidget(
                        iconName: _getTypeIcon(),
                        color: _getTypeColor(context),
                        size: 5.w,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    
                    // Title and priority
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  notification.title,
                                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                    fontWeight: notification.isRead ? FontWeight.w500 : FontWeight.w600,
                                    color: notification.isRead 
                                        ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8)
                                        : Theme.of(context).colorScheme.onSurface,
                                  ),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              if (notification.priority != NotificationPriority.normal)
                                _buildPriorityBadge(context),
                            ],
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            notification.relativeTime,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              fontSize: 10.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Actions
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (!notification.isRead && onMarkAsRead != null)
                          IconButton(
                            onPressed: onMarkAsRead,
                            icon: CustomIconWidget(
                              iconName: 'mark_email_read',
                              color: Theme.of(context).colorScheme.primary,
                              size: 4.w,
                            ),
                            tooltip: 'Mark as read',
                            padding: EdgeInsets.all(1.w),
                            constraints: BoxConstraints(minWidth: 8.w, minHeight: 8.w),
                          ),
                        if (onDelete != null)
                          IconButton(
                            onPressed: onDelete,
                            icon: CustomIconWidget(
                              iconName: 'delete',
                              color: Theme.of(context).colorScheme.error,
                              size: 4.w,
                            ),
                            tooltip: 'Delete',
                            padding: EdgeInsets.all(1.w),
                            constraints: BoxConstraints(minWidth: 8.w, minHeight: 8.w),
                          ),
                      ],
                    ),
                  ],
                ),
                
                SizedBox(height: 2.h),
                
                // Message
                Text(
                  notification.message,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: notification.isRead 
                        ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)
                        : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.9),
                    height: 1.4,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                
                // Additional info for scheduled notifications
                if (notification.scheduledFor != null) ...[
                  SizedBox(height: 1.h),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: notification.isOverdue
                          ? Theme.of(context).colorScheme.error.withValues(alpha: 0.1)
                          : notification.isUpcoming
                              ? Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1)
                              : Theme.of(context).colorScheme.outline.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: notification.isOverdue 
                              ? 'schedule' 
                              : notification.isUpcoming 
                                  ? 'upcoming' 
                                  : 'event',
                          color: notification.isOverdue
                              ? Theme.of(context).colorScheme.error
                              : notification.isUpcoming
                                  ? Theme.of(context).colorScheme.secondary
                                  : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          size: 3.w,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          notification.isOverdue
                              ? 'Overdue'
                              : notification.isUpcoming
                                  ? 'Upcoming'
                                  : 'Scheduled for ${TimeFormatUtils.formatTime(notification.scheduledFor!)}',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: notification.isOverdue
                                ? Theme.of(context).colorScheme.error
                                : notification.isUpcoming
                                    ? Theme.of(context).colorScheme.secondary
                                    : Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                            fontWeight: FontWeight.w500,
                            fontSize: 10.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                
                // Unread indicator
                if (!notification.isRead)
                  Container(
                    margin: EdgeInsets.only(top: 1.h),
                    child: Row(
                      children: [
                        Container(
                          width: 2.w,
                          height: 2.w,
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          'Unread',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                            fontSize: 10.sp,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPriorityBadge(BuildContext context) {
    Color badgeColor;
    String priorityText;
    
    switch (notification.priority) {
      case NotificationPriority.urgent:
        badgeColor = Theme.of(context).colorScheme.error;
        priorityText = 'URGENT';
        break;
      case NotificationPriority.high:
        badgeColor = Colors.orange;
        priorityText = 'HIGH';
        break;
      case NotificationPriority.low:
        badgeColor = Theme.of(context).colorScheme.outline;
        priorityText = 'LOW';
        break;
      default:
        return SizedBox.shrink();
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: badgeColor.withValues(alpha: 0.3)),
      ),
      child: Text(
        priorityText,
        style: TextStyle(
          color: badgeColor,
          fontSize: 8.sp,
          fontWeight: FontWeight.w700,
        ),
      ),
    );
  }

  String _getTypeIcon() {
    switch (notification.type) {
      case NotificationType.feeding:
        return 'restaurant';
      case NotificationType.sleep:
        return 'bedtime';
      case NotificationType.milestone:
        return 'celebration';
      case NotificationType.aiInsight:
        return 'psychology';
      case NotificationType.dailySummary:
        return 'today';
      case NotificationType.weeklyReport:
        return 'date_range';
      case NotificationType.medicine:
        return 'medication';
      case NotificationType.vaccination:
        return 'vaccines';
      case NotificationType.appointment:
        return 'event';
      case NotificationType.custom:
        return 'notifications';
    }
  }

  Color _getTypeColor(BuildContext context) {
    switch (notification.type) {
      case NotificationType.feeding:
        return Colors.green;
      case NotificationType.sleep:
        return Colors.indigo;
      case NotificationType.milestone:
        return Colors.amber;
      case NotificationType.aiInsight:
        return Colors.purple;
      case NotificationType.dailySummary:
        return Colors.blue;
      case NotificationType.weeklyReport:
        return Colors.teal;
      case NotificationType.medicine:
        return Colors.red;
      case NotificationType.vaccination:
        return Colors.pink;
      case NotificationType.appointment:
        return Colors.orange;
      case NotificationType.custom:
        return Theme.of(context).colorScheme.primary;
    }
  }
}