import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../models/notification_item.dart';
import '../../../utils/time_format_utils.dart';

class NotificationCardWidget extends StatelessWidget {
  final NotificationItem notification;
  final VoidCallback? onTap;
  final VoidCallback? onMarkAsRead;
  final VoidCallback? onDelete;

  const NotificationCardWidget({
    super.key,
    required this.notification,
    this.onTap,
    this.onMarkAsRead,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      decoration: BoxDecoration(
        color: notification.isRead 
            ? Theme.of(context).colorScheme.surface
            : Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: notification.isRead
              ? Theme.of(context).colorScheme.outline.withValues(alpha: 0.2)
              : Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
          width: notification.isRead ? 1 : 2,
        ),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with icon, title, and actions
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Notification type icon with priority indicator
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: _getTypeColor(context).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Stack(
                      children: [
                        CustomIconWidget(
                          iconName: _getTypeIcon(),
                          color: _getTypeColor(context),
                          size: 5.w,
                        ),
                        if (notification.priority == NotificationPriority.urgent ||
                            notification.priority == NotificationPriority.high)
                          Positioned(
                            right: -1,
                            top: -1,
                            child: Container(
                              width: 2.w,
                              height: 2.w,
                              decoration: BoxDecoration(
                                color: notification.priority == NotificationPriority.urgent
                                    ? Colors.red
                                    : Colors.orange,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  
                  SizedBox(width: 3.w),
                  
                  // Title and content
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Title with unread indicator
                        Row(
                          children: [
                            if (!notification.isRead)
                              Container(
                                width: 2.w,
                                height: 2.w,
                                margin: EdgeInsets.only(right: 2.w),
                                decoration: BoxDecoration(
                                  color: Theme.of(context).colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                            Expanded(
                              child: Text(
                                notification.title,
                                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                  fontWeight: notification.isRead 
                                      ? FontWeight.w500 
                                      : FontWeight.w600,
                                  color: Theme.of(context).colorScheme.onSurface,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                        
                        SizedBox(height: 1.h),
                        
                        // Message
                        Text(
                          notification.message,
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.8),
                            fontWeight: notification.isRead 
                                ? FontWeight.w400 
                                : FontWeight.w500,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                        
                        SizedBox(height: 1.h),
                        
                        // Time and status indicators
                        Row(
                          children: [
                            // Time
                            Text(
                              notification.relativeTime,
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                              ),
                            ),
                            
                            // Status indicators
                            if (notification.isOverdue) ...[
                              SizedBox(width: 2.w),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                                decoration: BoxDecoration(
                                  color: Colors.red.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Overdue',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.red,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 8.sp,
                                  ),
                                ),
                              ),
                            ],
                            
                            if (notification.isUpcoming) ...[
                              SizedBox(width: 2.w),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                                decoration: BoxDecoration(
                                  color: Colors.orange.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  'Upcoming',
                                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Colors.orange,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 8.sp,
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ],
                    ),
                  ),
                  
                  // Action buttons
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'mark_read':
                          onMarkAsRead?.call();
                          break;
                        case 'delete':
                          onDelete?.call();
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (!notification.isRead)
                        PopupMenuItem(
                          value: 'mark_read',
                          child: Row(
                            children: [
                              CustomIconWidget(
                                iconName: 'mark_email_read',
                                size: 4.w,
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                              SizedBox(width: 2.w),
                              Text('Mark as read'),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            CustomIconWidget(
                              iconName: 'delete',
                              size: 4.w,
                              color: Theme.of(context).colorScheme.error,
                            ),
                            SizedBox(width: 2.w),
                            Text(
                              'Delete',
                              style: TextStyle(
                                color: Theme.of(context).colorScheme.error,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    child: Container(
                      padding: EdgeInsets.all(1.w),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.surface.withValues(alpha: 0.5),
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: CustomIconWidget(
                        iconName: 'more_vert',
                        size: 4.w,
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getTypeIcon() {
    switch (notification.type) {
      case NotificationType.feeding:
        return 'restaurant';
      case NotificationType.sleep:
        return 'bedtime';
      case NotificationType.milestone:
        return 'celebration';
      case NotificationType.aiInsight:
        return 'psychology';
      case NotificationType.dailySummary:
        return 'today';
      case NotificationType.weeklyReport:
        return 'date_range';
      case NotificationType.medicine:
        return 'medication';
      case NotificationType.vaccination:
        return 'vaccines';
      case NotificationType.appointment:
        return 'event';
      case NotificationType.custom:
        return 'notifications';
    }
  }

  Color _getTypeColor(BuildContext context) {
    switch (notification.type) {
      case NotificationType.feeding:
        return Colors.green;
      case NotificationType.sleep:
        return Colors.indigo;
      case NotificationType.milestone:
        return Colors.amber;
      case NotificationType.aiInsight:
        return Colors.purple;
      case NotificationType.dailySummary:
        return Colors.blue;
      case NotificationType.weeklyReport:
        return Colors.teal;
      case NotificationType.medicine:
        return Colors.red;
      case NotificationType.vaccination:
        return Colors.orange;
      case NotificationType.appointment:
        return Colors.cyan;
      case NotificationType.custom:
        return Theme.of(context).colorScheme.primary;
    }
  }
}