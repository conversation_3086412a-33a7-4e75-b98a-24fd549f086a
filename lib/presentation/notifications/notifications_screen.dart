import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';
import '../../models/notification_item.dart';
import '../../services/unified_notification_service.dart';
import '../../theme/theme_aware_colors.dart';
import '../../utils/time_format_utils.dart';
import './widgets/notification_card_widget.dart';
import './widgets/notification_filter_widget.dart';

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with TickerProviderStateMixin {
  late final UnifiedNotificationService _notificationService;
  late final TabController _tabController;
  
  List<NotificationItem> _allNotifications = [];
  List<NotificationItem> _filteredNotifications = [];
  NotificationType? _selectedFilter;
  bool _showUnreadOnly = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _notificationService = UnifiedNotificationService.instance;
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
    _notificationService.addListener(_onNotificationServiceChanged);
  }

  @override
  void dispose() {
    _notificationService.removeListener(_onNotificationServiceChanged);
    _tabController.dispose();
    super.dispose();
  }

  void _onNotificationServiceChanged() {
    if (mounted) {
      _loadNotifications();
    }
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);
    
    try {
      final notifications = await _notificationService.getAllNotifications();
      if (mounted) {
        setState(() {
          _allNotifications = notifications;
          _applyFilters();
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load notifications')),
        );
      }
    }
  }

  void _applyFilters() {
    _filteredNotifications = _allNotifications.where((notification) {
      // Filter by type
      if (_selectedFilter != null && notification.type != _selectedFilter) {
        return false;
      }
      
      // Filter by read status
      if (_showUnreadOnly && notification.isRead) {
        return false;
      }
      
      return true;
    }).toList();

    // Sort by priority and creation time
    _filteredNotifications.sort((a, b) {
      // First sort by priority (urgent first)
      final priorityComparison = _getPriorityWeight(b.priority).compareTo(_getPriorityWeight(a.priority));
      if (priorityComparison != 0) return priorityComparison;
      
      // Then by creation time (newest first)
      return b.createdAt.compareTo(a.createdAt);
    });
  }

  int _getPriorityWeight(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.urgent:
        return 4;
      case NotificationPriority.high:
        return 3;
      case NotificationPriority.normal:
        return 2;
      case NotificationPriority.low:
        return 1;
    }
  }

  List<NotificationItem> get _unreadNotifications =>
      _allNotifications.where((n) => !n.isRead).toList();

  List<NotificationItem> get _todayNotifications {
    final today = DateTime.now();
    return _allNotifications.where((n) {
      final notificationDate = n.createdAt;
      return notificationDate.year == today.year &&
             notificationDate.month == today.month &&
             notificationDate.day == today.day;
    }).toList();
  }

  List<NotificationItem> get _upcomingNotifications =>
      _allNotifications.where((n) => n.isUpcoming).toList();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Notifications',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () => Navigator.pop(context),
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: Theme.of(context).colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          // Mark all as read button
          if (_unreadNotifications.isNotEmpty)
            IconButton(
              onPressed: _markAllAsRead,
              icon: CustomIconWidget(
                iconName: 'mark_email_read',
                color: Theme.of(context).colorScheme.primary,
                size: 24,
              ),
              tooltip: 'Mark all as read',
            ),
          // Clear all notifications button
          IconButton(
            onPressed: _showClearAllDialog,
            icon: CustomIconWidget(
              iconName: 'delete_sweep',
              color: Theme.of(context).colorScheme.error,
              size: 24,
            ),
            tooltip: 'Clear all notifications',
          ),
          SizedBox(width: 2.w),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('All'),
                  if (_allNotifications.isNotEmpty) ...[
                    SizedBox(width: 1.w),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_allNotifications.length}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onPrimary,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Unread'),
                  if (_unreadNotifications.isNotEmpty) ...[
                    SizedBox(width: 1.w),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.error,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_unreadNotifications.length}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onError,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text('Today'),
                  if (_todayNotifications.isNotEmpty) ...[
                    SizedBox(width: 1.w),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.secondary,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_todayNotifications.length}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSecondary,
                          fontSize: 10.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // Filter section
          NotificationFilterWidget(
            selectedFilter: _selectedFilter,
            showUnreadOnly: _showUnreadOnly,
            onFilterChanged: (filter) {
              setState(() {
                _selectedFilter = filter;
                _applyFilters();
              });
            },
            onUnreadToggled: (value) {
              setState(() {
                _showUnreadOnly = value;
                _applyFilters();
              });
            },
          ),
          
          // Content
          Expanded(
            child: _isLoading
                ? Center(
                    child: CircularProgressIndicator(
                      color: Theme.of(context).colorScheme.primary,
                    ),
                  )
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildNotificationsList(_filteredNotifications),
                      _buildNotificationsList(_unreadNotifications),
                      _buildNotificationsList(_todayNotifications),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationsList(List<NotificationItem> notifications) {
    if (notifications.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.builder(
        padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return NotificationCardWidget(
            notification: notification,
            onTap: () => _handleNotificationTap(notification),
            onMarkAsRead: () => _markAsRead(notification),
            onDelete: () => _deleteNotification(notification),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'notifications_none',
            size: 20.w,
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 2.h),
          Text(
            'No notifications',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'You\'re all caught up!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
            ),
          ),
        ],
      ),
    );
  }

  void _handleNotificationTap(NotificationItem notification) {
    // Mark as read when tapped
    if (!notification.isRead) {
      _markAsRead(notification);
    }

    // Handle navigation based on notification type
    switch (notification.type) {
      case NotificationType.feeding:
      case NotificationType.sleep:
      case NotificationType.medicine:
        // Navigate to quick log with pre-selected activity
        Navigator.pushNamed(context, '/tracker');
        break;
      case NotificationType.aiInsight:
        // Navigate to AI insights
        Navigator.pushNamed(context, '/ai-insights');
        break;
      case NotificationType.milestone:
        // Navigate to milestones
        Navigator.pushNamed(context, '/milestones');
        break;
      case NotificationType.appointment:
        // Navigate to scheduler
        Navigator.pushNamed(context, '/scheduler');
        break;
      default:
        // Show notification details
        _showNotificationDetails(notification);
        break;
    }
  }

  void _showNotificationDetails(NotificationItem notification) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(notification.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            SizedBox(height: 2.h),
            Text(
              'Created: ${TimeFormatUtils.formatDateTime(notification.createdAt)}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
              ),
            ),
            if (notification.scheduledFor != null) ...[
              SizedBox(height: 1.h),
              Text(
                'Scheduled: ${TimeFormatUtils.formatDateTime(notification.scheduledFor!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
          if (!notification.isRead)
            ElevatedButton(
              onPressed: () {
                _markAsRead(notification);
                Navigator.pop(context);
              },
              child: Text('Mark as Read'),
            ),
        ],
      ),
    );
  }

  Future<void> _markAsRead(NotificationItem notification) async {
    try {
      await _notificationService.markAsRead(notification.id);
      _loadNotifications();
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
    }
  }

  Future<void> _markAllAsRead() async {
    try {
      await _notificationService.markAllAsRead();
      _loadNotifications();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('All notifications marked as read')),
      );
    } catch (e) {
      debugPrint('Error marking all notifications as read: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to mark all as read')),
      );
    }
  }

  Future<void> _deleteNotification(NotificationItem notification) async {
    try {
      await _notificationService.deleteNotification(notification.id);
      _loadNotifications();
    } catch (e) {
      debugPrint('Error deleting notification: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to delete notification')),
      );
    }
  }

  void _showClearAllDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Clear All Notifications'),
        content: Text('Are you sure you want to clear all notifications? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _notificationService.clearAllNotifications();
                _loadNotifications();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('All notifications cleared')),
                );
              } catch (e) {
                debugPrint('Error clearing all notifications: $e');
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Failed to clear notifications')),
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Clear All'),
          ),
        ],
      ),
    );
  }
}