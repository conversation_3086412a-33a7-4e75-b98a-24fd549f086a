import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../core/app_export.dart';

class BabiesManagementScreen extends StatefulWidget {
  const BabiesManagementScreen({super.key});

  @override
  State<BabiesManagementScreen> createState() => _BabiesManagementScreenState();
}

class _BabiesManagementScreenState extends State<BabiesManagementScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  
  List<BabyProfile> _babies = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBabies();
  }

  Future<void> _loadBabies() async {
    try {
      setState(() => _isLoading = true);

      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      final response = await _supabaseService.select(
        'baby_profiles',
        orderBy: 'created_at',
        ascending: false,
      );

      final babies = response
          .map((data) => BabyProfile.fromJson({
                'id': data['id'],
                'name': data['name'],
                'birthDate': data['birth_date'],
                'gender': data['gender'],
                'photo': data['photo_url'],
                'birthWeight': data['birth_weight']?.toDouble(),
                'birthHeight': data['birth_height']?.toDouble(),
                'allergies': List<String>.from(data['allergies'] ?? []),
                'medications': List<String>.from(data['medications'] ?? []),
                'notes': data['notes'],
                'createdAt': data['created_at'],
                'updatedAt': data['updated_at'],
              }))
          .toList();

      setState(() {
        _babies = babies;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading babies: $e');
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final months = (difference.inDays / 30.44).floor();
    
    if (months > 0) {
      return '$months months old';
    } else {
      return '${difference.inDays} days old';
    }
  }

  void _addNewBaby() {
    Navigator.pushNamed(context, '/baby-profile-creation').then((result) {
      if (result == true) {
        _loadBabies();
      }
    });
  }

  void _editBaby(BabyProfile baby) {
    Navigator.pushNamed(
      context, 
      '/baby-profile-creation',
      arguments: baby,
    ).then((result) {
      if (result == true) {
        _loadBabies();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Manage Babies'),
        backgroundColor: AppTheme.lightTheme.scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: AppTheme.lightTheme.colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _addNewBaby,
            icon: CustomIconWidget(
              iconName: 'add',
              color: AppTheme.lightTheme.primaryColor,
              size: 24,
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadBabies,
        color: AppTheme.lightTheme.primaryColor,
        child: _isLoading
            ? Center(
                child: CircularProgressIndicator(
                  color: AppTheme.lightTheme.primaryColor,
                ),
              )
            : _babies.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: EdgeInsets.all(4.w),
                    itemCount: _babies.length,
                    itemBuilder: (context, index) {
                      final baby = _babies[index];
                      return _buildBabyCard(baby);
                    },
                  ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewBaby,
        backgroundColor: AppTheme.lightTheme.primaryColor,
        child: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'child_care',
            size: 20.w,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          SizedBox(height: 3.h),
          Text(
            'No Babies Added',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Add your first baby profile to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          ElevatedButton.icon(
            onPressed: _addNewBaby,
            icon: CustomIconWidget(
              iconName: 'add',
              color: Colors.white,
              size: 5.w,
            ),
            label: Text('Add Baby Profile'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.lightTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBabyCard(BabyProfile baby) {
    return Card(
      margin: EdgeInsets.only(bottom: 3.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _editBaby(baby),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Row(
            children: [
              // Baby Avatar
              Container(
                width: 15.w,
                height: 15.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: baby.gender == 'boy'
                      ? LinearGradient(colors: [Colors.blue, Colors.cyan])
                      : LinearGradient(colors: [Colors.pink, Colors.purple]),
                ),
                child: baby.photo != null
                    ? ClipOval(
                        child: CustomImageWidget(
                          imageUrl: baby.photo!,
                          width: 15.w,
                          height: 15.w,
                          fit: BoxFit.cover,
                        ),
                      )
                    : Center(
                        child: Text(
                          baby.name.substring(0, 1).toUpperCase(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
              ),
              
              SizedBox(width: 4.w),
              
              // Baby Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      baby.name,
                      style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      _calculateAge(baby.birthDate),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              
              // Edit Button
              IconButton(
                onPressed: () => _editBaby(baby),
                icon: CustomIconWidget(
                  iconName: 'edit',
                  color: AppTheme.lightTheme.primaryColor,
                  size: 20,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
} 