import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'dart:io';
import 'dart:typed_data';
import 'package:crop_your_image/crop_your_image.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';

import '../../../core/app_export.dart';

class ManualImageEditor extends StatefulWidget {
  final String imagePath;
  final Function(String?) onImageEdited;

  const ManualImageEditor({
    super.key,
    required this.imagePath,
    required this.onImageEdited,
  });

  @override
  State<ManualImageEditor> createState() => _ManualImageEditorState();
}

class _ManualImageEditorState extends State<ManualImageEditor> {
  final CropController _cropController = CropController();
  Uint8List? _imageData;
  bool _isLoading = true;
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  Future<void> _loadImage() async {
    try {
      final file = File(widget.imagePath);
      final imageBytes = await file.readAsBytes();
      
      setState(() {
        _imageData = imageBytes;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading image: $e');
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cropImage() async {
    if (_imageData == null) return;
    
    setState(() {
      _isProcessing = true;
    });

    try {
      print('CROP: Starting crop operation...');
      _cropController.crop();
    } catch (e) {
      print('CROP: Error starting crop: $e');
      setState(() {
        _isProcessing = false;
      });
    }
  }

  void _onCropped(CropResult result) {
    print('CROP: Callback triggered with result: ${result.runtimeType}');
    
    if (result is CropSuccess) {
      print('CROP: Success, attempting to access image data...');
      
      Uint8List? croppedData;
      
      try {
        croppedData = (result as dynamic).croppedImage;
        print('CROP: Method 1 successful - croppedImage property');
      } catch (e1) {
        print('CROP: Method 1 failed - $e1');
        try {
          croppedData = (result as dynamic).imageBytes;
          print('CROP: Method 2 successful - imageBytes property');
        } catch (e2) {
          print('CROP: Method 2 failed - $e2');
          try {
            croppedData = (result as dynamic).bytes;
            print('CROP: Method 3 successful - bytes property');
          } catch (e3) {
            print('CROP: Method 3 failed - $e3');
            try {
              croppedData = (result as dynamic).data;
              print('CROP: Method 4 successful - data property');
            } catch (e4) {
              print('CROP: All methods failed - $e1, $e2, $e3, $e4');
              
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to process cropped image'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
              setState(() {
                _isProcessing = false;
              });
              return;
            }
          }
        }
      }
      
      if (croppedData != null) {
        print('CROP: Image data size: ${croppedData.length} bytes');
        _processCroppedImage(croppedData);
      } else {
        print('CROP: No cropped data available');
        setState(() {
          _isProcessing = false;
        });
      }
    } else if (result is CropFailure) {
      print('CROP: Failed - ${result.toString()}');
      setState(() {
        _isProcessing = false;
      });
    } else {
      print('CROP: Unknown result type - ${result.runtimeType}');
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _processCroppedImage(Uint8List croppedData) async {
    print('CROP: Processing cropped image...');
    
    try {
      print('CROP: Compressing and optimizing image...');
      final optimizedImagePath = await _optimizeCroppedImage(croppedData);
      print('CROP: Image optimized successfully: $optimizedImagePath');
      
      print('CROP: Calling onImageEdited callback...');
      widget.onImageEdited(optimizedImagePath);
      
      if (mounted) {
        print('CROP: Closing image editor...');
        Navigator.of(context).pop();
      }
    } catch (e) {
      print('CROP: Error processing cropped image: $e');
      setState(() {
        _isProcessing = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to process image: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  Future<String> _optimizeCroppedImage(Uint8List croppedData) async {
    try {
      final image = img.decodeImage(croppedData);
      if (image == null) {
        throw Exception('Failed to decode cropped image');
      }

      const maxSize = 800;
      int newSize = image.width > maxSize ? maxSize : image.width;
      
      final resizedImage = img.copyResize(
        image,
        width: newSize,
        height: newSize,
        interpolation: img.Interpolation.linear,
      );

      final compressedBytes = img.encodeJpg(resizedImage, quality: 85);

      final directory = await getTemporaryDirectory();
      final optimizedFile = File('${directory.path}/edited_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await optimizedFile.writeAsBytes(compressedBytes);

      print('Cropped and optimized: ${newSize}x${newSize}, ${(compressedBytes.length / 1024).toStringAsFixed(1)} KB');

      return optimizedFile.path;
    } catch (e) {
      print('Error optimizing cropped image: $e');
      final directory = await getTemporaryDirectory();
      final fallbackFile = File('${directory.path}/cropped_${DateTime.now().millisecondsSinceEpoch}.jpg');
      await fallbackFile.writeAsBytes(croppedData);
      return fallbackFile.path;
    }
  }

  void _resetCrop() {
    setState(() {
      _isProcessing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'Edit Baby Photo',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          if (!_isProcessing)
            TextButton(
              onPressed: _resetCrop,
              child: Text(
                'Reset',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            )
          : _imageData == null
              ? const Center(
                  child: Text(
                    'Failed to load image',
                    style: TextStyle(color: Colors.white),
                  ),
                )
              : Column(
                  children: [
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(4.w),
                      color: Colors.black87,
                      child: Text(
                        'Drag to move • Pinch to zoom',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white70,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(4.w),
                        child: Crop(
                          image: _imageData!,
                          controller: _cropController,
                          onCropped: _onCropped,
                          aspectRatio: 1.0,
                          baseColor: Colors.black,
                          maskColor: Colors.black.withOpacity(0.7),
                          radius: 8,
                          cornerDotBuilder: (size, edgeAlignment) => Container(
                            width: size,
                            height: size,
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.primary,
                              borderRadius: BorderRadius.circular(size / 2),
                              border: Border.all(color: Colors.white, width: 2),
                            ),
                          ),
                        ),
                      ),
                    ),
                    
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(6.w),
                      color: Colors.black87,
                      child: SafeArea(
                        child: Row(
                          children: [
                            Expanded(
                              child: OutlinedButton(
                                onPressed: _isProcessing ? null : () {
                                  Navigator.of(context).pop();
                                },
                                style: OutlinedButton.styleFrom(
                                  foregroundColor: Colors.white,
                                  side: const BorderSide(color: Colors.white70),
                                  padding: EdgeInsets.symmetric(vertical: 4.w),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: Text(
                                  'Cancel',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ),
                            
                            SizedBox(width: 4.w),
                            
                            Expanded(
                              flex: 2,
                              child: ElevatedButton(
                                onPressed: _isProcessing ? null : _cropImage,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Theme.of(context).colorScheme.primary,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 4.w),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                                child: _isProcessing
                                    ? Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          SizedBox(
                                            width: 20,
                                            height: 20,
                                            child: CircularProgressIndicator(
                                              strokeWidth: 2,
                                              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                            ),
                                          ),
                                          SizedBox(width: 2.w),
                                          Text(
                                            'Processing...',
                                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                              color: Colors.white,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                        ],
                                      )
                                    : Text(
                                        'Crop & Save',
                                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600,
                                        ),
                                      ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
    );
  }
}