import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/measurement_aware_text_field.dart';

class BabyFormWidget extends StatelessWidget {
  final TextEditingController nameController;
  final DateTime? selectedBirthDate;
  final String? selectedGender;
  final bool useMetricUnits;
  final List<String> genderOptions;
  final VoidCallback onBirthDateTap;
  final Function(String) onGenderSelected;
  final VoidCallback onUnitsToggled;
  final TextEditingController noteController;
  // New measurement fields
  final double? initialWeightKg;
  final double? initialHeightCm;
  final Function(double?) onWeightChanged;
  final Function(double?) onHeightChanged;

  const BabyFormWidget({
    super.key,
    required this.nameController,
    required this.selectedBirthDate,
    required this.selectedGender,
    required this.useMetricUnits,
    required this.genderOptions,
    required this.onBirthDateTap,
    required this.onGenderSelected,
    required this.onUnitsToggled,
    required this.noteController,
    required this.initialWeightKg,
    required this.initialHeightCm,
    required this.onWeightChanged,
    required this.onHeightChanged,
  });

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Required Fields Section
        Text(
          'Basic Information',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),

        SizedBox(height: 2.h),

        // Baby Name Field
        _buildFormField(
          label: 'Baby\'s Name',
          isRequired: true,
          child: TextFormField(
            controller: nameController,
            decoration: InputDecoration(
              hintText: 'Enter baby\'s name',
              counterText: '${nameController.text.length}/50',
              filled: true,
              fillColor: Theme.of(context).colorScheme.surface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
              ),
            ),
            maxLength: 50,
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return 'Please enter baby\'s name';
              }
              if (value.trim().length < 2) {
                return 'Name must be at least 2 characters';
              }
              return null;
            },
          ),
        ),

        SizedBox(height: 3.h),

        // Birth Date Field
        _buildFormField(
          label: 'Birth Date',
          isRequired: true,
          child: GestureDetector(
            onTap: onBirthDateTap,
            child: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border.all(
                  color: ThemeAwareColors.getOutlineColor(context),
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: 'calendar_today',
                    color: selectedBirthDate != null
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.onSurface
                            .withValues(alpha: 0.4),
                    size: 20,
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Text(
                      selectedBirthDate != null
                          ? _formatDate(selectedBirthDate!)
                          : 'Select birth date',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: selectedBirthDate != null
                            ? Theme.of(context).colorScheme.onSurface
                            : Theme.of(context).colorScheme.onSurface
                                .withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                  CustomIconWidget(
                    iconName: 'chevron_right',
                    color: Theme.of(context).colorScheme.onSurface
                        .withValues(alpha: 0.4),
                    size: 20,
                  ),
                ],
              ),
            ),
          ),
        ),

        SizedBox(height: 3.h),

        // Gender Selection
        _buildFormField(
          label: 'Gender',
          isRequired: true,
          child: Column(
            children: genderOptions.map((gender) {
              final isSelected = selectedGender == gender;
              return GestureDetector(
                onTap: () => onGenderSelected(gender),
                child: Container(
                  width: double.infinity,
                  margin: EdgeInsets.only(bottom: 2.h),
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? Theme.of(context).colorScheme.primary
                            .withValues(alpha: 0.1)
                        : Theme.of(context).colorScheme.surface,
                    border: Border.all(
                      color: isSelected
                          ? Theme.of(context).colorScheme.primary
                          : ThemeAwareColors.getOutlineColor(context),
                      width: isSelected ? 2 : 1,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: isSelected
                                ? Theme.of(context).colorScheme.primary
                                : ThemeAwareColors.getOutlineColor(context),
                            width: 2,
                          ),
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Colors.transparent,
                        ),
                        child: isSelected
                            ? Center(
                                child: Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Theme.of(context).colorScheme.onPrimary,
                                  ),
                                ),
                              )
                            : null,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        gender,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurface,
                          fontWeight:
                              isSelected ? FontWeight.w500 : FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),

        SizedBox(height: 4.h),

        // Optional Fields Section
        Row(
          children: [
            Text(
              'Optional Information',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),

            const Spacer(),

            // Units Toggle
            GestureDetector(
              onTap: onUnitsToggled,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CustomIconWidget(
                      iconName: 'swap_horiz',
                      color: Theme.of(context).colorScheme.primary,
                      size: 16,
                    ),
                    SizedBox(width: 1.w),
                    Text(
                      useMetricUnits ? 'Metric' : 'Imperial',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),

        SizedBox(height: 2.h),

        // Birth Weight Field
        _buildFormField(
          label: 'Birth Weight',
          isRequired: false,
          child: MeasurementAwareTextField(
            measurementType: 'weight',
            initialMetricValue: initialWeightKg,
            onMetricValueChanged: onWeightChanged,
            label: 'Birth Weight',
            hintText: useMetricUnits ? 'Enter weight in kg' : 'Enter weight in lbs',
          ),
        ),

        SizedBox(height: 3.h),

        // Birth Height Field
        _buildFormField(
          label: 'Birth Height',
          isRequired: false,
          child: MeasurementAwareTextField(
            measurementType: 'height',
            initialMetricValue: initialHeightCm,
            onMetricValueChanged: onHeightChanged,
            label: 'Birth Height',
            hintText: useMetricUnits ? 'Enter height in cm' : 'Enter height in inches',
          ),
        ),

        SizedBox(height: 3.h),

        // Note Field
        _buildFormField(
          label: 'Note (allergies, medications, etc.)',
          isRequired: false,
          child: TextFormField(
            controller: noteController,
            decoration: InputDecoration(
              hintText: 'Add notes about allergies, medications, or anything important',
              counterText: '${noteController.text.length}/250',
              filled: true,
              fillColor: Theme.of(context).colorScheme.surface,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
              ),
            ),
            maxLength: 250,
            maxLines: 2,
            textCapitalization: TextCapitalization.sentences,
          ),
        ),
      ],
    );
  }

  Widget _buildFormField({
    required String label,
    required bool isRequired,
    required Widget child,
  }) {
    return Builder(
      builder: (context) => Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              if (isRequired) ...[
                SizedBox(width: 1.w),
                Text(
                  '*',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ],
          ),
          SizedBox(height: 1.h),
          child,
        ],
      ),
    );
  }
}
