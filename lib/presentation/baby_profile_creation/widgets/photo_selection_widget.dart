import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'dart:io';

import '../../../core/app_export.dart';
import '../../../services/photo_service.dart';

class PhotoSelectionWidget extends StatefulWidget {
  final String? selectedPhotoPath;
  final Function(String?) onPhotoSelected;

  const PhotoSelectionWidget({
    super.key,
    required this.selectedPhotoPath,
    required this.onPhotoSelected,
  });

  @override
  State<PhotoSelectionWidget> createState() => _PhotoSelectionWidgetState();
}

class _PhotoSelectionWidgetState extends State<PhotoSelectionWidget> {
  final PhotoService _photoService = PhotoService();
  bool _isUploading = false;

  void _showPhotoSelectionSheet(BuildContext context) {
    if (_isUploading) return; // Prevent multiple uploads
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12.w,
                height: 0.5.h,
                margin: EdgeInsets.only(top: 2.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(6.w),
                child: Column(
                  children: [
                    Text(
                      'Add Baby Photo',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      'Choose how you\'d like to add your baby\'s photo',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4.h),
                    _buildPhotoOption(
                      context,
                      icon: 'camera_alt',
                      title: 'Take Photo',
                      subtitle: 'Use camera to capture a new photo',
                      onTap: () => _selectFromCamera(context),
                    ),
                    SizedBox(height: 2.h),
                    _buildPhotoOption(
                      context,
                      icon: 'photo_library',
                      title: 'Choose from Gallery',
                      subtitle: 'Select from existing photos',
                      onTap: () => _selectFromGallery(context),
                    ),
                    if (widget.selectedPhotoPath != null) ...[
                      SizedBox(height: 2.h),
                      _buildPhotoOption(
                        context,
                        icon: 'delete_outline',
                        title: 'Remove Photo',
                        subtitle: 'Remove current photo',
                        onTap: () => _removePhoto(context),
                        isDestructive: true,
                      ),
                    ],
                    SizedBox(height: 3.h),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(
                          'Cancel',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(
                            color: ThemeAwareColors.getSecondaryTextColor(context),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoOption(
    BuildContext context, {
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: isDestructive
                ? Theme.of(context).colorScheme.error.withValues(alpha: 0.3)
                : Theme.of(context).dividerColor,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: isDestructive
                    ? Theme.of(context).colorScheme.error
                        .withValues(alpha: 0.1)
                    : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomIconWidget(
                iconName: icon,
                color: isDestructive
                    ? Theme.of(context).colorScheme.error
                    : Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: isDestructive
                          ? Theme.of(context).colorScheme.error
                          : Theme.of(context).colorScheme.onSurface,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
            ),
            CustomIconWidget(
              iconName: 'chevron_right',
              color: ThemeAwareColors.getIconColor(context),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _selectFromCamera(BuildContext context) async {
    Navigator.of(context).pop();
    HapticFeedback.lightImpact();

    setState(() {
      _isUploading = true;
    });

    try {
      final String? photoUrl = await _photoService.takePhotoFromCamera();
      if (photoUrl != null) {
        widget.onPhotoSelected(photoUrl);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Photo captured and uploaded successfully!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error taking photo: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to take photo: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  void _selectFromGallery(BuildContext context) async {
    Navigator.of(context).pop();
    HapticFeedback.lightImpact();

    setState(() {
      _isUploading = true;
    });

    try {
      final String? photoUrl = await _photoService.selectPhotoFromGallery();
      if (photoUrl != null) {
        widget.onPhotoSelected(photoUrl);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Photo selected and uploaded successfully!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ Error selecting photo: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to select photo: ${e.toString()}'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  void _removePhoto(BuildContext context) async {
    Navigator.of(context).pop();
    HapticFeedback.lightImpact();
    
    // If we have a photo URL, try to delete it from storage
    if (widget.selectedPhotoPath != null) {
      try {
        await _photoService.deletePhoto(widget.selectedPhotoPath!);
      } catch (e) {
        debugPrint('⚠️ Warning: Failed to delete photo from storage: $e');
      }
    }
    
    widget.onPhotoSelected(null);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _isUploading ? null : () => _showPhotoSelectionSheet(context),
      child: Container(
        width: 30.w,
        height: 30.w,
        decoration: BoxDecoration(
          color: widget.selectedPhotoPath != null
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: widget.selectedPhotoPath != null
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                : Theme.of(context).dividerColor,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(15.w),
        ),
        child: _isUploading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                    strokeWidth: 3,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Uploading...',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            : widget.selectedPhotoPath != null
                ? Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15.w),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15.w),
                          child: widget.selectedPhotoPath!.startsWith('http')
                              ? CustomImageWidget(
                                  imageUrl: widget.selectedPhotoPath!,
                                  width: 30.w,
                                  height: 30.w,
                                  fit: BoxFit.cover,
                                )
                              : Image.file(
                                  File(widget.selectedPhotoPath!),
                                  width: 30.w,
                                  height: 30.w,
                                  fit: BoxFit.cover,
                                ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.all(1.5.w),
                          decoration: BoxDecoration(
                            color: ThemeAwareColors.getSuccessColor(context),
                            borderRadius: BorderRadius.circular(6.w),
                            border: Border.all(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              width: 2,
                            ),
                          ),
                          child: CustomIconWidget(
                            iconName: 'check',
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: 'camera_alt',
                        color: ThemeAwareColors.getIconColor(context),
                        size: 32,
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        'Add Photo',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }
}
