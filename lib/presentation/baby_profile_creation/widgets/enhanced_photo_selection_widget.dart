import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'dart:io';
import 'package:gal/gal.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../../../core/app_export.dart';
import 'manual_image_editor.dart';

class EnhancedPhotoSelectionWidget extends StatefulWidget {
  final String? selectedPhotoPath;
  final Function(String?) onPhotoSelected;

  const EnhancedPhotoSelectionWidget({
    super.key,
    required this.selectedPhotoPath,
    required this.onPhotoSelected,
  });

  @override
  State<EnhancedPhotoSelectionWidget> createState() => _EnhancedPhotoSelectionWidgetState();
}

class _EnhancedPhotoSelectionWidgetState extends State<EnhancedPhotoSelectionWidget> {
  bool _isUploading = false;

  void _showPhotoOptionsSheet(BuildContext context) {
    if (_isUploading) return;
    
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 12.w,
                height: 0.5.h,
                margin: EdgeInsets.only(top: 2.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Padding(
                padding: EdgeInsets.all(6.w),
                child: Column(
                  children: [
                    Text(
                      'Baby Photo',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      'Choose how you\'d like to manage your baby\'s photo',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 4.h),
                    
                    if (widget.selectedPhotoPath != null) ...[
                      _buildPhotoOption(
                        context,
                        icon: 'visibility',
                        title: 'View Full Image',
                        subtitle: 'See the full-size photo',
                        onTap: () => _viewFullImage(context),
                      ),
                      SizedBox(height: 2.h),
                    ],
                    
                    _buildPhotoOption(
                      context,
                      icon: 'camera_alt',
                      title: 'Take Photo',
                      subtitle: 'Use camera to capture a new photo',
                      onTap: () => _selectFromCamera(context),
                    ),
                    SizedBox(height: 2.h),
                    _buildPhotoOption(
                      context,
                      icon: 'photo_library',
                      title: 'Choose from Gallery',
                      subtitle: 'Select from existing photos',
                      onTap: () => _selectFromGallery(context),
                    ),
                    
                    if (widget.selectedPhotoPath != null) ...[
                      SizedBox(height: 2.h),
                      _buildPhotoOption(
                        context,
                        icon: 'delete_outline',
                        title: 'Remove Photo',
                        subtitle: 'Remove current photo',
                        onTap: () => _removePhoto(context),
                        isDestructive: true,
                      ),
                    ],
                    
                    SizedBox(height: 3.h),
                    SizedBox(
                      width: double.infinity,
                      child: TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: Text(
                          'Cancel',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: ThemeAwareColors.getSecondaryTextColor(context),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoOption(
    BuildContext context, {
    required String icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          border: Border.all(
            color: isDestructive
                ? Theme.of(context).colorScheme.error.withValues(alpha: 0.3)
                : Theme.of(context).dividerColor,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: isDestructive
                    ? Theme.of(context).colorScheme.error.withValues(alpha: 0.1)
                    : Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: CustomIconWidget(
                iconName: icon,
                color: isDestructive
                    ? Theme.of(context).colorScheme.error
                    : Theme.of(context).colorScheme.primary,
                size: 24,
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: isDestructive
                          ? Theme.of(context).colorScheme.error
                          : Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                    ),
                  ),
                ],
              ),
            ),
            CustomIconWidget(
              iconName: 'chevron_right',
              color: ThemeAwareColors.getIconColor(context),
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  void _viewFullImage(BuildContext context) {
    Navigator.of(context).pop();
    if (widget.selectedPhotoPath != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => _FullImageViewer(
            imagePath: widget.selectedPhotoPath!,
          ),
        ),
      );
    }
  }

  Future<void> _selectFromCamera(BuildContext context) async {
    Navigator.of(context).pop();
    
    setState(() {
      _isUploading = true;
    });

    try {
      final picker = ImagePicker();
      final XFile? photo = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (photo != null) {
        final file = File(photo.path);
        if (await file.exists() && await file.length() > 0) {
          await _showManualImageEditor(photo.path);
        } else {
          throw Exception('Captured image file is invalid or empty');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(context, 'Failed to capture photo: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _selectFromGallery(BuildContext context) async {
    Navigator.of(context).pop();
    
    setState(() {
      _isUploading = true;
    });

    try {
      final picker = ImagePicker();
      final XFile? photo = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
      );

      if (photo != null) {
        final file = File(photo.path);
        if (await file.exists() && await file.length() > 0) {
          await _showManualImageEditor(photo.path);
        } else {
          throw Exception('Selected image file is invalid or empty');
        }
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar(context, 'Failed to select photo: $e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _showManualImageEditor(String imagePath) async {
    print('EDITOR: Opening manual image editor for: $imagePath');
    
    if (mounted) {
      setState(() {
        _isUploading = false;
      });
    }

    try {
      final editedImagePath = await Navigator.of(context).push<String>(
        MaterialPageRoute(
          builder: (context) => ManualImageEditor(
            imagePath: imagePath,
            onImageEdited: (editedPath) {
              print('EDITOR: Image edited, returning path: $editedPath');
              Navigator.of(context).pop(editedPath);
            },
          ),
        ),
      );

      print('EDITOR: Received edited image path: $editedImagePath');

      if (editedImagePath != null && mounted) {
        await _showUploadConfirmation(editedImagePath);
      }
    } catch (e) {
      print('EDITOR: Error in manual image editor: $e');
      if (mounted) {
        _showErrorSnackBar(context, 'Failed to edit image: $e');
      }
    }
  }

  Future<void> _showUploadConfirmation(String imagePath) async {
    print('CONFIRM: Showing upload confirmation for: $imagePath');
    
    if (!mounted) {
      print('CONFIRM: Widget not mounted, skipping confirmation dialog');
      return;
    }
    
    // Store the callback before showing dialog to ensure it's available
    final photoCallback = widget.onPhotoSelected;
    
    final shouldUpload = await showDialog<bool>(
      context: context,
      barrierDismissible: false, // Prevent accidental dismissal
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Upload Photo?',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).dividerColor,
                ),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(12),
                child: Image.file(
                  File(imagePath),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            SizedBox(height: 3.h),
            Text(
              'Are you happy with this photo? It will be uploaded and set as the baby\'s profile picture.',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              print('CONFIRM: User chose to edit again');
              Navigator.of(context).pop(false);
            },
            child: Text(
              'Edit Again',
              style: TextStyle(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              print('CONFIRM: User confirmed upload');
              Navigator.of(context).pop(true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Upload'),
          ),
        ],
      ),
    );

    print('CONFIRM: Dialog result - shouldUpload: $shouldUpload, mounted: $mounted');
    
    if (shouldUpload == true) {
      print('CONFIRM: User confirmed upload, starting immediately');
      // Start upload immediately with stored callback
      try {
        await _processAndUploadImageWithCallback(imagePath, photoCallback);
        print('CONFIRM: Upload process completed successfully');
      } catch (e) {
        print('CONFIRM: Upload failed: $e');
        // Show error even if widget is unmounted
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Upload failed: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } else if (shouldUpload == false) {
      if (mounted) {
        print('CONFIRM: User wants to edit again');
        await _showManualImageEditor(imagePath);
      } else {
        print('CONFIRM: Widget not mounted, cannot edit again');
      }
    } else {
      print('CONFIRM: Dialog was cancelled or returned null');
    }
  }

  Future<void> _processAndUploadImageWithCallback(String imagePath, Function(String?) photoCallback) async {
    print('UPLOAD: Starting upload process for: $imagePath');
    
    // Don't check mounted here - we want upload to proceed even if widget is unmounted
    if (mounted) {
      setState(() {
        _isUploading = true;
      });
    }

    try {
      // Verify file exists before upload
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('Image file does not exist: $imagePath');
      }
      
      final fileSize = await file.length();
      print('UPLOAD: File exists, size: ${(fileSize / 1024).toStringAsFixed(1)} KB');
      
      print('UPLOAD: Uploading image to Supabase...');
      final uploadedUrl = await _uploadProcessedImage(imagePath);
      
      if (uploadedUrl == null || uploadedUrl.isEmpty) {
        throw Exception('Upload returned empty URL');
      }
      
      print('UPLOAD: Upload successful: $uploadedUrl');
      
      print('UPLOAD: Setting photo URL using stored callback...');
      // Use the stored callback instead of widget.onPhotoSelected
      try {
        photoCallback(uploadedUrl);
        print('UPLOAD: Photo URL set successfully using callback');
        HapticFeedback.lightImpact();
      } catch (e) {
        print('UPLOAD: Error setting photo with callback: $e');
        print('UPLOAD: Attempting to update baby profile directly in database...');
        
        // Fallback: Update the baby profile directly in the database (includes old image cleanup)
        try {
          await _updateBabyProfilePhotoInDatabase(uploadedUrl);
          print('UPLOAD: Baby profile updated directly in database with old image cleanup');
        } catch (dbError) {
          print('UPLOAD: Failed to update database directly: $dbError');
        }
      }
      
      if (mounted) {
        print('UPLOAD: Showing success toast...');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Photo uploaded successfully!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
            duration: Duration(seconds: 4),
          ),
        );
        print('UPLOAD: Success toast shown');
      }
    } catch (e) {
      print('UPLOAD: Upload failed with error: $e');
      if (mounted) {
        print('UPLOAD: Showing error toast...');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Failed to upload photo: $e',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
            duration: Duration(seconds: 5),
          ),
        );
        print('UPLOAD: Error toast shown');
      }
    } finally {
      if (mounted) {
        print('UPLOAD: Resetting upload state');
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _processAndUploadImage(String imagePath) async {
    print('UPLOAD: Starting upload process for: $imagePath');
    
    // Don't check mounted here - we want upload to proceed even if widget is unmounted
    if (mounted) {
      setState(() {
        _isUploading = true;
      });
    }

    try {
      // Verify file exists before upload
      final file = File(imagePath);
      if (!await file.exists()) {
        throw Exception('Image file does not exist: $imagePath');
      }
      
      final fileSize = await file.length();
      print('UPLOAD: File exists, size: ${(fileSize / 1024).toStringAsFixed(1)} KB');
      
      print('UPLOAD: Uploading image to Supabase...');
      final uploadedUrl = await _uploadProcessedImage(imagePath);
      
      if (uploadedUrl == null || uploadedUrl.isEmpty) {
        throw Exception('Upload returned empty URL');
      }
      
      print('UPLOAD: Upload successful: $uploadedUrl');
      
      print('UPLOAD: Setting photo URL in widget...');
      // Set the photo URL immediately, even if widget is disposed
      try {
        widget.onPhotoSelected(uploadedUrl);
        print('UPLOAD: Photo URL set successfully');
        HapticFeedback.lightImpact();
      } catch (e) {
        print('UPLOAD: Error setting photo (widget disposed): $e');
        print('UPLOAD: Attempting alternative method to update photo...');
        
        // Alternative: Try to find the parent widget and update directly
        try {
          // The upload was successful, so we need to ensure the URL gets to the parent
          // This is a fallback when the widget callback fails
          print('UPLOAD: Upload successful but widget disposed - URL: $uploadedUrl');
        } catch (e2) {
          print('UPLOAD: Alternative method also failed: $e2');
        }
      }
      
      if (mounted) {
        print('UPLOAD: Showing success toast...');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Photo uploaded successfully!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
            duration: Duration(seconds: 4),
          ),
        );
        print('UPLOAD: Success toast shown');
      }
    } catch (e) {
      print('UPLOAD: Upload failed with error: $e');
      if (mounted) {
        print('UPLOAD: Showing error toast...');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error, color: Colors.white),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    'Failed to upload photo: $e',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
            duration: Duration(seconds: 5),
          ),
        );
        print('UPLOAD: Error toast shown');
      }
    } finally {
      if (mounted) {
        print('UPLOAD: Resetting upload state');
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  Future<void> _updateBabyProfilePhotoInDatabase(String newPhotoUrl) async {
    try {
      print('DATABASE: Updating baby profile photo in database...');
      
      // Get the current user
      final supabaseClient = Supabase.instance.client;
      final user = supabaseClient.auth.currentUser;
      
      if (user == null) {
        throw Exception('User not authenticated');
      }
      
      print('DATABASE: User ID: ${user.id}');
      
      // Get the active baby for this user with current photo URL
      final babyResponse = await supabaseClient
          .from('baby_profiles')
          .select('id, name, photo_url')
          .eq('user_id', user.id)
          .eq('is_active', true)
          .single();
      
      final babyId = babyResponse['id'];
      final babyName = babyResponse['name'];
      final oldPhotoUrl = babyResponse['photo_url'] as String?;
      
      print('DATABASE: Found active baby: $babyName (ID: $babyId)');
      print('DATABASE: Current photo URL: $oldPhotoUrl');
      print('DATABASE: New photo URL: $newPhotoUrl');
      
      // Delete the old image from storage if it exists
      if (oldPhotoUrl != null && oldPhotoUrl.isNotEmpty && oldPhotoUrl != newPhotoUrl) {
        await _deleteOldImageFromStorage(oldPhotoUrl);
      }
      
      // Update the baby's photo URL
      await supabaseClient
          .from('baby_profiles')
          .update({'photo_url': newPhotoUrl})
          .eq('id', babyId);
      
      print('DATABASE: Baby profile photo updated successfully');
      
      // Trigger a refresh of the baby profile state
      try {
        final babyProfileStateManager = BabyProfileStateManager();
        await babyProfileStateManager.refresh();
        print('DATABASE: Baby profile state refreshed');
      } catch (e) {
        print('DATABASE: Error refreshing baby profile state: $e');
      }
      
    } catch (e) {
      print('DATABASE: Error updating baby profile: $e');
      throw e;
    }
  }

  Future<void> _deleteOldImageFromStorage(String oldPhotoUrl) async {
    try {
      print('STORAGE: Attempting to delete old image: $oldPhotoUrl');
      
      // Extract filename from URL
      // URL format: https://snqeizaqnswgpxdhnlkr.supabase.co/storage/v1/object/public/baby-photos/baby_1752890140283.jpg
      final uri = Uri.parse(oldPhotoUrl);
      final pathSegments = uri.pathSegments;
      
      // Find the filename (last segment after 'baby-photos')
      String? fileName;
      for (int i = 0; i < pathSegments.length; i++) {
        if (pathSegments[i] == 'baby-photos' && i + 1 < pathSegments.length) {
          fileName = pathSegments[i + 1];
          break;
        }
      }
      
      if (fileName == null || fileName.isEmpty) {
        print('STORAGE: Could not extract filename from URL: $oldPhotoUrl');
        return;
      }
      
      print('STORAGE: Extracted filename: $fileName');
      
      // Delete the file from Supabase storage
      final supabaseClient = Supabase.instance.client;
      final deleteResponse = await supabaseClient.storage
          .from('baby-photos')
          .remove([fileName]);
      
      print('STORAGE: Delete response: $deleteResponse');
      
      if (deleteResponse.isNotEmpty) {
        print('STORAGE: Successfully deleted old image: $fileName');
      } else {
        print('STORAGE: No files were deleted (file may not exist): $fileName');
      }
      
    } catch (e) {
      print('STORAGE: Error deleting old image: $e');
      print('STORAGE: Error type: ${e.runtimeType}');
      // Don't throw error - deletion failure shouldn't stop the upload process
    }
  }

  Future<String?> _uploadProcessedImage(String imagePath) async {
    try {
      print('SUPABASE: Reading image file...');
      final file = File(imagePath);
      final imageBytes = await file.readAsBytes();
      print('SUPABASE: File read successfully, size: ${imageBytes.length} bytes');
      
      final fileName = 'baby_${DateTime.now().millisecondsSinceEpoch}.jpg';
      print('SUPABASE: Generated filename: $fileName');
      
      print('SUPABASE: Connecting to Supabase storage...');
      final supabaseClient = Supabase.instance.client;
      
      print('SUPABASE: Uploading to baby-photos bucket...');
      final uploadResponse = await supabaseClient.storage
          .from('baby-photos')
          .uploadBinary(fileName, imageBytes);
      
      print('SUPABASE: Upload response: $uploadResponse');

      print('SUPABASE: Getting public URL...');
      final publicUrl = supabaseClient.storage
          .from('baby-photos')
          .getPublicUrl(fileName);

      print('SUPABASE: Public URL generated: $publicUrl');
      
      if (publicUrl.isEmpty) {
        throw Exception('Generated public URL is empty');
      }
      
      return publicUrl;
    } catch (e) {
      print('SUPABASE: Error uploading image: $e');
      print('SUPABASE: Error type: ${e.runtimeType}');
      print('SUPABASE: Error details: ${e.toString()}');
      throw e; // Re-throw to be caught by calling method
    }
  }

  void _removePhoto(BuildContext context) {
    Navigator.of(context).pop();
    widget.onPhotoSelected(null);
    HapticFeedback.lightImpact();
  }

  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          message,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Colors.white,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        margin: EdgeInsets.all(4.w),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _isUploading ? null : () => _showPhotoOptionsSheet(context),
      child: Container(
        width: 30.w,
        height: 30.w,
        decoration: BoxDecoration(
          color: widget.selectedPhotoPath != null
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          border: Border.all(
            color: widget.selectedPhotoPath != null
                ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.3)
                : Theme.of(context).dividerColor,
            width: 2,
          ),
          borderRadius: BorderRadius.circular(15.w),
        ),
        child: _isUploading
            ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: Theme.of(context).colorScheme.primary,
                    strokeWidth: 3,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Processing...',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              )
            : widget.selectedPhotoPath != null
                ? Stack(
                    children: [
                      Container(
                        width: double.infinity,
                        height: double.infinity,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(15.w),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(15.w),
                          child: widget.selectedPhotoPath!.startsWith('http')
                              ? CustomImageWidget(
                                  imageUrl: widget.selectedPhotoPath!,
                                  width: 30.w,
                                  height: 30.w,
                                  fit: BoxFit.cover,
                                )
                              : Image.file(
                                  File(widget.selectedPhotoPath!),
                                  width: 30.w,
                                  height: 30.w,
                                  fit: BoxFit.cover,
                                ),
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        right: 0,
                        child: Container(
                          padding: EdgeInsets.all(1.5.w),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.primary,
                            borderRadius: BorderRadius.circular(6.w),
                            border: Border.all(
                              color: Theme.of(context).scaffoldBackgroundColor,
                              width: 2,
                            ),
                          ),
                          child: CustomIconWidget(
                            iconName: 'camera_alt',
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CustomIconWidget(
                        iconName: 'camera_alt',
                        color: ThemeAwareColors.getIconColor(context),
                        size: 32,
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        'Add Photo',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
      ),
    );
  }
}

class _FullImageViewer extends StatefulWidget {
  final String imagePath;

  const _FullImageViewer({required this.imagePath});

  @override
  State<_FullImageViewer> createState() => _FullImageViewerState();
}

class _FullImageViewerState extends State<_FullImageViewer> {
  bool _isDownloading = false;

  Future<void> _downloadImage() async {
    setState(() {
      _isDownloading = true;
    });

    try {
      if (widget.imagePath.startsWith('http')) {
        await _downloadFromUrl(widget.imagePath);
      } else {
        await _saveLocalFileToGallery(widget.imagePath);
      }
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text(
              'Photo saved to gallery successfully!',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
        HapticFeedback.lightImpact();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to save photo: $e',
              style: const TextStyle(color: Colors.white),
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  Future<void> _downloadFromUrl(String url) async {
    try {
      final response = await http.get(Uri.parse(url));
      if (response.statusCode == 200) {
        final directory = await getTemporaryDirectory();
        final fileName = 'baby_photo_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final tempFile = File('${directory.path}/$fileName');
        await tempFile.writeAsBytes(response.bodyBytes);
        
        await Gal.putImage(tempFile.path, album: 'Baby Photos');
        
        if (await tempFile.exists()) {
          await tempFile.delete();
        }
      } else {
        throw Exception('Failed to download image');
      }
    } catch (e) {
      throw Exception('Failed to save image: $e');
    }
  }

  Future<void> _saveLocalFileToGallery(String filePath) async {
    try {
      final file = File(filePath);
      if (await file.exists()) {
        await Gal.putImage(filePath, album: 'Baby Photos');
      } else {
        throw Exception('Image file not found');
      }
    } catch (e) {
      throw Exception('Failed to save image: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        title: Text(
          'Baby Photo',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Colors.white,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _isDownloading ? null : _downloadImage,
            icon: _isDownloading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.download, color: Colors.white),
          ),
        ],
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: InteractiveViewer(
          panEnabled: true,
          boundaryMargin: const EdgeInsets.all(20),
          minScale: 0.5,
          maxScale: 4.0,
          child: Center(
            child: widget.imagePath.startsWith('http')
                ? Image.network(
                    widget.imagePath,
                    fit: BoxFit.contain,
                    width: double.infinity,
                    height: double.infinity,
                    loadingBuilder: (context, child, loadingProgress) {
                      if (loadingProgress == null) return child;
                      return Center(
                        child: CircularProgressIndicator(
                          value: loadingProgress.expectedTotalBytes != null
                              ? loadingProgress.cumulativeBytesLoaded /
                                  loadingProgress.expectedTotalBytes!
                              : null,
                          valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      );
                    },
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, color: Colors.white, size: 48),
                            SizedBox(height: 16),
                            Text(
                              'Failed to load image',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      );
                    },
                  )
                : Image.file(
                    File(widget.imagePath),
                    fit: BoxFit.contain,
                    width: double.infinity,
                    height: double.infinity,
                    errorBuilder: (context, error, stackTrace) {
                      return const Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(Icons.error, color: Colors.white, size: 48),
                            SizedBox(height: 16),
                            Text(
                              'Failed to load image',
                              style: TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
          ),
        ),
      ),
    );
  }
}