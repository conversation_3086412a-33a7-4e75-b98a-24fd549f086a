import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';
import '../../services/measurement_units_service.dart';
import '../../widgets/measurement_aware_text_field.dart';
import '../../widgets/modern_date_time_picker.dart';
import './widgets/baby_form_widget.dart';
import './widgets/enhanced_photo_selection_widget.dart';

class BabyProfileCreation extends StatefulWidget {
  final BabyProfile? babyProfile;
  
  const BabyProfileCreation({super.key, this.babyProfile});

  @override
  State<BabyProfileCreation> createState() => _BabyProfileCreationState();
}

class _BabyProfileCreationState extends State<BabyProfileCreation> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _noteController = TextEditingController();
  
  // Store metric values for weight and height
  double? _birthWeightKg;
  double? _birthHeightCm;

  DateTime? _selectedBirthDate;
  String? _selectedGender;
  String? _selectedPhotoPath;
  bool _isLoading = false;
  bool _hasUnsavedChanges = false;

  final List<String> _genderOptions = ['Boy', 'Girl', 'Other'];

  final AuthService _authService = AuthService();
  final SupabaseService _supabaseService = SupabaseService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();

  // Track if we're in "add another baby" mode
  bool _isAddingAnotherBaby = false;
  
  bool get _isEditing => widget.babyProfile != null && !_isAddingAnotherBaby;

  @override
  void initState() {
    super.initState();
    _nameController.addListener(_onFormChanged);
    _noteController.addListener(_onFormChanged);

    if (widget.babyProfile != null) {
      // Pre-fill form with existing baby data
      _nameController.text = widget.babyProfile!.name;
      final dbGender = widget.babyProfile!.gender.toLowerCase();
      if (dbGender == 'boy') {
        _selectedGender = 'Boy';
      } else if (dbGender == 'girl') {
        _selectedGender = 'Girl';
      } else {
        _selectedGender = 'Other';
      }
      _selectedBirthDate = widget.babyProfile!.birthDate;
      _birthWeightKg = widget.babyProfile!.birthWeight;
      _birthHeightCm = widget.babyProfile!.birthHeight;
      _noteController.text = widget.babyProfile!.note ?? '';
      _selectedPhotoPath = widget.babyProfile!.photo; // Set existing photo
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _noteController.dispose();
    super.dispose();
  }

  void _onFormChanged() {
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  bool get _isFormValid {
    return _nameController.text.trim().isNotEmpty &&
        _selectedBirthDate != null &&
        _selectedGender != null;
  }

  Future<void> _selectBirthDate() async {
    final DateTime? picked = await ModernDateTimePicker.showDatePicker(
      context: context,
      initialDate: DateTime.now().subtract(const Duration(days: 30)),
      firstDate: DateTime.now().subtract(const Duration(days: 365 * 5)),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _selectedBirthDate) {
      setState(() {
        _selectedBirthDate = picked;
        _hasUnsavedChanges = true;
      });
    }
  }

  void _onPhotoSelected(String? photoPath) {
    setState(() {
      _selectedPhotoPath = photoPath;
      _hasUnsavedChanges = true;
    });
  }

  void _onGenderSelected(String gender) {
    setState(() {
      _selectedGender = gender;
      _hasUnsavedChanges = true;
    });
  }

  void _toggleUnits() {
    context.read<MeasurementUnitsService>().toggleMeasurementSystem();
    setState(() {
      _hasUnsavedChanges = true;
    });
  }

  Future<void> _createProfile() async {
    if (!_isFormValid) return;

    print('🆕 _createProfile called - Creating NEW baby profile');
    
    setState(() {
      _isLoading = true;
    });

    try {
      // Get current user
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      print('Creating baby profile for user: ${currentUser.id}');

      // Create baby profile data
      final babyProfileData = {
        'user_id': currentUser.id,
        'name': _nameController.text.trim(),
        'birth_date': _selectedBirthDate!.toIso8601String().split('T')[0], // Format as YYYY-MM-DD
        'gender': _selectedGender!.toLowerCase(), // 'boy', 'girl', or 'other'
        'birth_weight': _birthWeightKg,
        'birth_height': _birthHeightCm,
        'photo_url': _selectedPhotoPath, // We'll handle photo upload later
        'note': _noteController.text.trim().isNotEmpty ? _noteController.text.trim() : null,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Baby profile data: $babyProfileData');

      // Create baby using centralized state manager
      final newBaby = await _babyProfileManager.addBaby(babyProfileData, makeActive: true);
      
      print('Baby profile created successfully: ${newBaby.name} (ID: ${newBaby.id})');
      
      // Navigate back to main navigation with the new active baby
      Navigator.pushNamedAndRemoveUntil(
        context,
        '/main-navigation',
        (route) => false,
      );
    } catch (e) {
      print('Error creating baby profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to create profile: ${e.toString()}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _updateProfile() async {
    if (!_isFormValid || widget.babyProfile == null) return;

    print('✏️ _updateProfile called - Updating EXISTING baby profile: ${widget.babyProfile!.id}');

    setState(() {
      _isLoading = true;
    });

    try {
      print('Updating baby profile: ${widget.babyProfile!.id}');

      // Create updated baby profile data
      final babyProfileData = {
        'name': _nameController.text.trim(),
        'birth_date': _selectedBirthDate!.toIso8601String().split('T')[0],
        'gender': _selectedGender!.toLowerCase(),
        'birth_weight': _birthWeightKg,
        'birth_height': _birthHeightCm,
        'photo_url': _selectedPhotoPath,
        'note': _noteController.text.trim(),
        'updated_at': DateTime.now().toIso8601String(),
      };

      print('Updated baby profile data: $babyProfileData');

      // Update using centralized state manager
      final updatedBaby = await _babyProfileManager.updateBaby(
        widget.babyProfile!.id,
        babyProfileData,
      );
      
      print('Updated profile note: ${updatedBaby.note}');
      Navigator.pop(context, updatedBaby);

      // Reset form state
      setState(() {
        _hasUnsavedChanges = false;
      });

      // Haptic feedback for success
      HapticFeedback.lightImpact();

      // Show success message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Baby profile updated successfully!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: ThemeAwareColors.getSuccessColor(context),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
          ),
        );

        // Add small delay before navigation
        await Future.delayed(Duration(milliseconds: 300));
      }
    } catch (e) {
      print('Error updating baby profile: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Failed to update profile: ${e.toString()}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.white,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _saveProfile() async {
    print('💾 _saveProfile called - _isEditing: $_isEditing, _isAddingAnotherBaby: $_isAddingAnotherBaby');
    if (_isEditing) {
      await _updateProfile();
    } else {
      await _createProfile();
    }
  }

  Future<void> _addAnotherBaby() async {
    // Reset form and switch to creation mode
    setState(() {
      _nameController.clear();
      _birthWeightKg = null;
      _birthHeightCm = null;
      _noteController.clear(); // Clear notes field
      _selectedBirthDate = null;
      _selectedGender = null;
      _selectedPhotoPath = null;
      _hasUnsavedChanges = false;
      _isAddingAnotherBaby = true; // Switch to creation mode
    });

    // Scroll to top
    if (mounted) {
      Scrollable.ensureVisible(
        context,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Future<bool> _onWillPop() async {
    if (!_hasUnsavedChanges) return true;

    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Unsaved Changes',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'You have unsaved changes. Are you sure you want to leave?',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: const Text('Stay'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: const Text('Leave'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );

    return result ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(_isEditing ? 'Edit Profile' : 'Create Baby Profile'),
          leading: IconButton(
            onPressed: () async {
              final canPop = await _onWillPop();
              if (canPop && mounted) {
                Navigator.of(context).pop();
              }
            },
            icon: CustomIconWidget(
              iconName: 'arrow_back',
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 0,
          systemOverlayStyle: Theme.of(context).brightness == Brightness.dark 
              ? SystemUiOverlayStyle.light 
              : SystemUiOverlayStyle.dark,
        ),
        body: SafeArea(
          child: Form(
            key: _formKey,
            child: Column(
              children: [
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.symmetric(horizontal: 6.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 3.h),

                        // Photo Selection Section
                        Center(
                          child: EnhancedPhotoSelectionWidget(
                            selectedPhotoPath: _selectedPhotoPath,
                            onPhotoSelected: _onPhotoSelected,
                          ),
                        ),

                        SizedBox(height: 4.h),

                        // Form Section
                        BabyFormWidget(
                          nameController: _nameController,
                          selectedBirthDate: _selectedBirthDate,
                          selectedGender: _selectedGender,
                          useMetricUnits: context.watch<MeasurementUnitsService>().isMetric,
                          genderOptions: _genderOptions,
                          onBirthDateTap: _selectBirthDate,
                          onGenderSelected: _onGenderSelected,
                          onUnitsToggled: _toggleUnits,
                          noteController: _noteController,
                          // New measurement fields
                          initialWeightKg: _birthWeightKg,
                          initialHeightCm: _birthHeightCm,
                          onWeightChanged: (value) {
                            _birthWeightKg = value;
                            _onFormChanged();
                          },
                          onHeightChanged: (value) {
                            _birthHeightCm = value;
                            _onFormChanged();
                          },
                        ),

                        SizedBox(height: 4.h),
                      ],
                    ),
                  ),
                ),

                // Bottom Action Section
                Container(
                  padding: EdgeInsets.all(6.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).scaffoldBackgroundColor,
                    border: Border(
                      top: BorderSide(
                        color: Theme.of(context).dividerColor,
                        width: 1,
                      ),
                    ),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: double.infinity,
                        height: 6.h,
                        child: ElevatedButton(
                          onPressed: _isFormValid && !_isLoading
                              ? _saveProfile
                              : null,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: _isFormValid
                                ? Theme.of(context).colorScheme.primary
                                : Theme.of(context).colorScheme.onSurface
                                    .withValues(alpha: 0.12),
                            foregroundColor: _isFormValid
                                ? Theme.of(context).colorScheme.onPrimary
                                : Theme.of(context).colorScheme.onSurface
                                    .withValues(alpha: 0.38),
                            elevation: _isFormValid ? 2 : 0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: _isLoading
                              ? SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      Theme.of(context).colorScheme.onPrimary,
                                    ),
                                  ),
                                )
                              : Text(
                                  _isEditing ? 'Save Profile' : 'Create Profile',
                                  style: Theme.of(context).textTheme.titleMedium
                                      ?.copyWith(
                                    color: _isFormValid
                                        ? Theme.of(context).colorScheme.onPrimary
                                        : Theme.of(context).colorScheme.onSurface
                                            .withValues(alpha: 0.38),
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                        ),
                      ),
                      if (_hasUnsavedChanges && _isFormValid) ...[
                        SizedBox(height: 2.h),
                        TextButton(
                          onPressed: _addAnotherBaby,
                          child: Text(
                            'Add Another Baby',
                            style: Theme.of(context).textTheme.titleMedium
                                ?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
