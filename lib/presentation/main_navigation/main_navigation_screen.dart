import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/baby_profile_state_manager.dart';
import '../../utils/subscription_access_control.dart';
import '../ai_chat_assistant/ai_chat_assistant.dart';
import '../ai_insights_dashboard/ai_insights_dashboard.dart';
import '../growth_charts/growth_charts.dart';
import '../home/<USER>';
import '../scheduler/scheduler_screen.dart';
import '../subscription/widgets/upgrade_required_screen.dart';
import '../tracker_screen/tracker_screen.dart';

class MainNavigationScreen extends StatefulWidget {
  const MainNavigationScreen({super.key});

  @override
  State<MainNavigationScreen> createState() => _MainNavigationScreenState();
}

class _MainNavigationScreenState extends State<MainNavigationScreen> with WidgetsBindingObserver {
  int _selectedIndex = 0;
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _babyProfileManager.addListener(_onBabyProfileStateChanged);
    _initializeBabyProfiles();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _babyProfileManager.removeListener(_onBabyProfileStateChanged);
    super.dispose();
  }

  void _onBabyProfileStateChanged() {
    if (mounted) {
      setState(() {
        // UI will rebuild with latest baby profile state
      });
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (state == AppLifecycleState.resumed) {
      debugPrint('🔄 App resumed, refreshing baby profiles...');
      _refreshBabyProfiles();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Refresh baby profile when screen comes back into focus
    if (!_babyProfileManager.hasActiveBaby && !_babyProfileManager.isLoading) {
      debugPrint('🔄 No active baby found, refreshing...');
      _refreshBabyProfiles();
    }
  }

  Future<void> _initializeBabyProfiles() async {
    try {
      await _babyProfileManager.initialize();
    } catch (e) {
      debugPrint('❌ Error initializing baby profiles: $e');
    }
  }

  Future<void> _refreshBabyProfiles() async {
    try {
      await _babyProfileManager.refresh();
    } catch (e) {
      debugPrint('❌ Error refreshing baby profiles: $e');
    }
  }

  Future<void> _onBabySelected(BabyProfile baby) async {
    debugPrint('🔄 Baby selected: ${baby.name} (ID: ${baby.id})');
    try {
      await _babyProfileManager.setActiveBaby(baby);
      debugPrint('✅ Baby successfully set as active: ${baby.name}');
      
      // Reset to home screen when switching babies
      setState(() {
        _selectedIndex = 0;
      });
      
      // Show success feedback
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Switched to ${baby.name}',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onPrimary,
              ),
            ),
            backgroundColor: Theme.of(context).colorScheme.primary,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            margin: EdgeInsets.all(4.w),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error setting active baby: $e');
      
      // Try to refresh baby profiles and retry once
      try {
        debugPrint('🔄 Refreshing baby profiles and retrying...');
        await _refreshBabyProfiles();
        await _babyProfileManager.setActiveBaby(baby);
        debugPrint('✅ Baby successfully set as active after refresh: ${baby.name}');
        
        setState(() {
          _selectedIndex = 0;
        });
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Switched to ${baby.name}',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: EdgeInsets.all(4.w),
              duration: Duration(seconds: 2),
            ),
          );
        }
      } catch (retryError) {
        debugPrint('❌ Retry failed: $retryError');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to switch baby: Please try again',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.white,
                ),
              ),
              backgroundColor: Theme.of(context).colorScheme.error,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              margin: EdgeInsets.all(4.w),
              duration: Duration(seconds: 3),
              action: SnackBarAction(
                label: 'Retry',
                textColor: Theme.of(context).colorScheme.onError,
                onPressed: () => _onBabySelected(baby),
              ),
            ),
          );
        }
      }
    }
  }

  List<Widget> get _screens => [
        Home(
          key: ValueKey('home_${_babyProfileManager.activeBaby?.id}'),
          babyProfile: _babyProfileManager.activeBaby,
          allBabies: _babyProfileManager.allBabies,
          onBabySelected: _onBabySelected,
        ),
        TrackerScreen(key: ValueKey('tracker_${_babyProfileManager.activeBaby?.id}')),
        SchedulerScreen(key: ValueKey('scheduler_${_babyProfileManager.activeBaby?.id}')),
        // Always show AI Chat Assistant with premium card for free users
        AIChatAssistant(
          key: ValueKey('ai_chat_${_babyProfileManager.activeBaby?.id}'),
          babyProfile: _babyProfileManager.activeBaby,
          recentActivities: const [],
        ),
        // Always show Growth Charts with premium card for free users
        _babyProfileManager.hasActiveBaby
            ? GrowthCharts(
                key: ValueKey('growth_${_babyProfileManager.activeBaby?.id}'),
                babyProfile: _babyProfileManager.activeBaby!,
              )
            : _buildGrowthEmptyState(),
        // Always show AI Insights dashboard with premium cards for free users
        AIInsightsDashboard(
          key: ValueKey('insights_${_babyProfileManager.activeBaby?.id}'),
          babyProfile: _babyProfileManager.activeBaby,
        ),
      ];

  List<BottomNavigationBarItem> get _navigationItems => [
    BottomNavigationBarItem(
      icon: CustomIconWidget(iconName: 'home_filled', size: 6.w),
      activeIcon: CustomIconWidget(iconName: 'home_filled', size: 6.w),
      label: 'Home',
    ),
    BottomNavigationBarItem(
      icon: CustomIconWidget(iconName: 'edit_calendar', size: 6.w),
      activeIcon: CustomIconWidget(iconName: 'edit_calendar', size: 6.w),
      label: 'Tracker',
    ),
    BottomNavigationBarItem(
      icon: CustomIconWidget(iconName: 'schedule', size: 6.w),
      activeIcon: CustomIconWidget(iconName: 'schedule', size: 6.w),
      label: 'Schedule',
    ),
    BottomNavigationBarItem(
      icon: CustomIconWidget(iconName: 'chat_bubble', size: 6.w),
      activeIcon: CustomIconWidget(iconName: 'chat_bubble', size: 6.w),
      label: 'Ask AI',
    ),
    BottomNavigationBarItem(
      icon: CustomIconWidget(iconName: 'show_chart', size: 6.w),
      activeIcon: CustomIconWidget(iconName: 'show_chart', size: 6.w),
      label: 'Growth',
    ),
    BottomNavigationBarItem(
      icon: CustomIconWidget(iconName: 'analytics', size: 6.w),
      activeIcon: CustomIconWidget(iconName: 'analytics', size: 6.w),
      label: 'Insights',
    ),
  ];


  void _onTabTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_babyProfileManager.isLoading) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(color: Theme.of(context).colorScheme.primary),
              SizedBox(height: 2.h),
              Text('Loading baby profiles...'),
            ],
          ),
        ),
      );
    }
    
    if (_babyProfileManager.error != null) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 12.w, color: Theme.of(context).colorScheme.error),
              SizedBox(height: 2.h),
              Text(
                'Error loading baby profiles',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                _babyProfileManager.error!,
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton(
                onPressed: _refreshBabyProfiles,
                child: Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }
    
    if (!_babyProfileManager.hasBabies) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CustomIconWidget(
                iconName: 'child_care',
                size: 20.w,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              ),
              SizedBox(height: 3.h),
              Text(
                'No baby profiles found',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              SizedBox(height: 1.h),
              Text(
                'Create your first baby profile to get started',
                style: Theme.of(context).textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
              SizedBox(height: 3.h),
              ElevatedButton.icon(
                onPressed: () => Navigator.pushNamed(context, '/baby-profile-creation')
                    .then((result) {
                  if (result == true) _refreshBabyProfiles();
                }),
                icon: Icon(Icons.add),
                label: Text('Create Baby Profile'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  foregroundColor: Theme.of(context).colorScheme.onPrimary,
                  padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
                ),
              ),
            ],
          ),
        ),
        bottomNavigationBar: _buildBottomNavigationBar(),
      );
    }
    
    return Scaffold(
      body: _screens[_selectedIndex],
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }
  
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: _onTabTapped,
            type: BottomNavigationBarType.fixed,
            backgroundColor: Colors.transparent,
            elevation: 0,
            selectedItemColor: Theme.of(context).colorScheme.primary,
            unselectedItemColor: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            selectedLabelStyle: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.w600,
            ),
            unselectedLabelStyle: TextStyle(
              fontSize: 10.sp,
              fontWeight: FontWeight.w400,
            ),
            items: _navigationItems,
          ),
        ),
      ),
    );
  }

  Widget _buildAskAIEmptyState() {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Ask AI'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          // Chat messages area with notice
          Expanded(
            child: Center(
              child: Padding(
                padding: EdgeInsets.all(6.w),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          CustomIconWidget(
                            iconName: 'smart_toy',
                            color: Theme.of(context).colorScheme.primary,
                            size: 12.w,
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            'AI Assistant Ready',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            'Please create a baby profile first to use Ask AI',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 2.h),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(context, '/baby-profile-creation');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Theme.of(context).colorScheme.onPrimary,
                              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Create Baby Profile',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Message input area (disabled)
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Text(
                      'Create a baby profile to start chatting...',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 2.w),
                Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                    shape: BoxShape.circle,
                  ),
                  child: CustomIconWidget(
                    iconName: 'send',
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    size: 5.w,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGrowthEmptyState() {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('Growth Charts'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Center(
        child: Padding(
          padding: EdgeInsets.all(6.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  children: [
                    CustomIconWidget(
                      iconName: 'show_chart',
                      color: Theme.of(context).colorScheme.primary,
                      size: 12.w,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'Growth Charts Ready',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      'Please create a baby profile first to use Growth Charts',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 2.h),
                    ElevatedButton(
                      onPressed: () {
                        Navigator.pushNamed(context, '/baby-profile-creation');
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        foregroundColor: Theme.of(context).colorScheme.onPrimary,
                        padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        'Create Baby Profile',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInsightsEmptyState() {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: const Text('AI Insights'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        automaticallyImplyLeading: false,
        actions: [
          IconButton(
            onPressed: null,
            icon: CustomIconWidget(
              iconName: 'refresh',
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              size: 6.w,
            ),
          ),
          IconButton(
            onPressed: null,
            icon: CustomIconWidget(
              iconName: 'share',
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
              size: 6.w,
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Analysis Categories Section (Empty State)
          Container(
            margin: EdgeInsets.all(4.w),
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
                  blurRadius: 10,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                Text(
                  'Analysis Categories',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                  ),
                ),
                SizedBox(height: 2.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildDisabledCategoryButton('Sleep', 'bedtime'),
                    _buildDisabledCategoryButton('Feeding', 'restaurant'),
                    _buildDisabledCategoryButton('Growth', 'trending_up'),
                    _buildDisabledCategoryButton('Development', 'child_care'),
                  ],
                ),
              ],
            ),
          ),

          // Main insights area with notice
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(horizontal: 4.w),
              padding: EdgeInsets.all(6.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.shadow.withOpacity(0.05),
                    blurRadius: 10,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: EdgeInsets.all(4.w),
                      decoration: BoxDecoration(
                        color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          CustomIconWidget(
                            iconName: 'insights',
                            color: Theme.of(context).colorScheme.primary,
                            size: 12.w,
                          ),
                          SizedBox(height: 2.h),
                          Text(
                            'AI Insights Ready',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.w700,
                              color: Theme.of(context).colorScheme.primary,
                            ),
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            'Please create a baby profile and start logging activities to see AI Insights',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
                            ),
                            textAlign: TextAlign.center,
                          ),
                          SizedBox(height: 2.h),
                          ElevatedButton(
                            onPressed: () {
                              Navigator.pushNamed(context, '/baby-profile-creation');
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Theme.of(context).colorScheme.primary,
                              foregroundColor: Theme.of(context).colorScheme.onPrimary,
                              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.h),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Text(
                              'Create Baby Profile',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(height: 4.w),
        ],
      ),
    );
  }


  Widget _buildDisabledCategoryButton(String label, String iconName) {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface.withOpacity(0.5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: CustomIconWidget(
            iconName: iconName,
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.4),
            size: 6.w,
          ),
        ),
        SizedBox(height: 1.h),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}
