import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class AdvancedQuickTopicsWidget extends StatelessWidget {
  final Function(String) onTopicSelected;
  final BabyProfile babyProfile;

  const AdvancedQuickTopicsWidget({
    super.key,
    required this.onTopicSelected,
    required this.babyProfile,
  });

  @override
  Widget build(BuildContext context) {
    final topics = _getPersonalizedTopics();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Removed duplicate header since ExpansionTile provides it
        SizedBox(height: 1.h),
        LayoutBuilder(
          builder: (context, constraints) {
            // Calculate responsive grid layout
            final screenWidth = constraints.maxWidth;
            final crossAxisCount = screenWidth > 600 ? 3 : 2;
            final cardWidth = (screenWidth - (3.w * (crossAxisCount + 1))) / crossAxisCount;
            final aspectRatio = cardWidth > 160 ? 2.2 : 1.8; // Reduced aspect ratio for more height
            
            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: crossAxisCount,
                crossAxisSpacing: 3.w,
                mainAxisSpacing: 2.h,
                childAspectRatio: aspectRatio,
              ),
              itemCount: topics.length,
              itemBuilder: (context, index) {
                final topic = topics[index];
                return _buildAdvancedTopicCard(
                  context,
                  topic: topic,
                  onTap: () => onTopicSelected(topic['title'] as String),
                );
              },
            );
          },
        ),
        SizedBox(height: 3.h),
        _buildPersonalizedPrompt(context),
      ],
    );
  }

  List<Map<String, dynamic>> _getPersonalizedTopics() {
    final ageInMonths = babyProfile.ageInMonths;

    List<Map<String, dynamic>> baseTopics = [
      {
        'title': 'Feeding',
        'icon': 'restaurant',
        'color': const Color(0xFF4A90A4),
        'description': _getFeedingDescription(),
      },
      {
        'title': 'Sleep',
        'icon': 'bedtime',
        'color': const Color(0xFFF4A261),
        'description': _getSleepDescription(),
      },
      {
        'title': 'Development',
        'icon': 'child_care',
        'color': const Color(0xFF7FB069),
        'description': _getDevelopmentDescription(),
      },
      {
        'title': 'Health',
        'icon': 'health_and_safety',
        'color': const Color(0xFFE76F51),
        'description': _getHealthDescription(),
      },
    ];

    // Add age-specific topics
    if (ageInMonths < 6) {
      baseTopics.addAll([
        {
          'title': 'Crying',
          'icon': 'sentiment_very_dissatisfied',
          'color': const Color(0xFF2A9D8F),
          'description': 'Understanding infant cries',
        },
        {
          'title': 'Safety',
          'icon': 'shield',
          'color': const Color(0xFF264653),
          'description': 'Newborn safety basics',
        },
      ]);
    } else if (ageInMonths >= 6 && ageInMonths < 12) {
      baseTopics.addAll([
        {
          'title': 'Growth',
          'icon': 'trending_up',
          'color': const Color(0xFF2A9D8F),
          'description': 'Growth milestones',
        },
        {
          'title': 'Activities',
          'icon': 'toys',
          'color': const Color(0xFF264653),
          'description': 'Age-appropriate play',
        },
      ]);
    } else {
      baseTopics.addAll([
        {
          'title': 'Growth',
          'icon': 'trending_up',
          'color': const Color(0xFF2A9D8F),
          'description': 'Toddler development',
        },
        {
          'title': 'Activities',
          'icon': 'toys',
          'color': const Color(0xFF264653),
          'description': 'Learning activities',
        },
      ]);
    }

    return baseTopics;
  }

  String _getFeedingDescription() {
    final ageInMonths = babyProfile.ageInMonths;
    if (ageInMonths < 6) {
      return 'Breastfeeding & formula tips';
    } else if (ageInMonths < 12) {
      return 'Starting solid foods';
    } else {
      return 'Toddler nutrition';
    }
  }

  String _getSleepDescription() {
    final ageInMonths = babyProfile.ageInMonths;
    if (ageInMonths < 4) {
      return 'Newborn sleep patterns';
    } else if (ageInMonths < 12) {
      return 'Sleep training & schedules';
    } else {
      return 'Toddler sleep routines';
    }
  }

  String _getDevelopmentDescription() {
    final ageInMonths = babyProfile.ageInMonths;
    if (ageInMonths < 6) {
      return 'Early milestones';
    } else if (ageInMonths < 12) {
      return 'Motor skill development';
    } else {
      return 'Language & social skills';
    }
  }

  String _getHealthDescription() {
    final ageInMonths = babyProfile.ageInMonths;
    if (ageInMonths < 6) {
      return 'Newborn health signs';
    } else if (ageInMonths < 12) {
      return 'Infant wellness checks';
    } else {
      return 'Toddler health monitoring';
    }
  }

  Widget _buildAdvancedTopicCard(
    BuildContext context, {
    required Map<String, dynamic> topic,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: (topic['color'] as Color).withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(4.w),
          border: Border.all(
            color: (topic['color'] as Color).withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.03),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(1.5.w),
                  decoration: BoxDecoration(
                    color: topic['color'] as Color,
                    borderRadius: BorderRadius.circular(2.w),
                  ),
                  child: CustomIconWidget(
                    iconName: topic['icon'] as String,
                    color: Colors.white,
                    size: 3.5.w,
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    topic['title'] as String,
                    style: GoogleFonts.inter(
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w600,
                      color: topic['color'] as Color,
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            SizedBox(height: 1.h),
            Flexible(
              child: Text(
                topic['description'] as String,
                style: GoogleFonts.inter(
                  fontSize: 9.sp,
                  color: Colors.grey[600],
                  height: 1.3,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPersonalizedPrompt(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.lightTheme.colorScheme.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(4.w),
        border: Border.all(
          color: AppTheme.lightTheme.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: AppTheme.lightTheme.primaryColor,
                  borderRadius: BorderRadius.circular(2.w),
                ),
                child: CustomIconWidget(
                  iconName: 'chat',
                  color: Colors.white,
                  size: 4.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Ask me anything specific!',
                      style: GoogleFonts.inter(
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.lightTheme.primaryColor,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                    Text(
                      'Personalized for ${babyProfile.name}',
                      style: GoogleFonts.inter(
                        fontSize: 10.sp,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 1.5.h),
          Text(
            'I have access to ${babyProfile.name}\'s profile and recent activities to provide personalized advice. You can also use voice input for hands-free interaction!',
            style: GoogleFonts.inter(
              fontSize: 10.sp,
              color: Colors.grey[600],
              height: 1.3,
            ),
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
