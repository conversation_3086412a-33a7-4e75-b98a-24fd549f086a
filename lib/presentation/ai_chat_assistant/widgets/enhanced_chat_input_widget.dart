import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EnhancedChatInputWidget extends StatefulWidget {
  final TextEditingController controller;
  final Function(String, {bool isVoiceInput})? onSend;
  final bool isLoading;
  final BabyProfile babyProfile;
  final bool isDisabled;
  final String? disabledMessage;

  const EnhancedChatInputWidget({
    super.key,
    required this.controller,
    this.onSend,
    this.isLoading = false,
    required this.babyProfile,
    this.isDisabled = false,
    this.disabledMessage,
  });

  @override
  State<EnhancedChatInputWidget> createState() =>
      _EnhancedChatInputWidgetState();
}

class _EnhancedChatInputWidgetState extends State<EnhancedChatInputWidget> {
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _handleSend() {
    final message = widget.controller.text.trim();
    if (message.isNotEmpty && !widget.isLoading && widget.onSend != null && !widget.isDisabled) {
      widget.onSend!(message, isVoiceInput: false);
      widget.controller.clear();
    }
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final primaryColor = Theme.of(context).primaryColor;
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
      decoration: BoxDecoration(
        color: isDark ? Colors.grey[800] : Colors.grey[50],
        borderRadius: BorderRadius.circular(6.w),
        border: Border.all(
          color: isDark ? Colors.grey[600]! : Colors.grey[200]!,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: widget.controller,
              maxLines: 4,
              minLines: 1,
              enabled: !widget.isLoading && !widget.isDisabled,
              style: GoogleFonts.inter(
                fontSize: 12.sp,
                color: widget.isDisabled 
                    ? (isDark ? Colors.grey[600] : Colors.grey[400])
                    : (isDark ? Colors.grey[200] : Colors.grey[800]),
              ),
              decoration: InputDecoration(
                hintText: widget.isDisabled 
                    ? widget.disabledMessage ?? 'Chat disabled'
                    : 'Ask me anything about ${widget.babyProfile.name}...',
                hintStyle: GoogleFonts.inter(
                  fontSize: widget.isDisabled ? 11.sp : (widget.babyProfile.name.length > 8 ? 10.sp : 12.sp),
                  color: widget.isDisabled 
                      ? (isDark ? Colors.grey[600] : Colors.grey[400])
                      : (isDark ? Colors.grey[400] : Colors.grey[500]),
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 2.w,
                  vertical: 1.h,
                ),
              ),
              textInputAction: TextInputAction.send,
              onSubmitted: widget.isDisabled ? null : (_) => _handleSend(),
            ),
          ),
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: widget.isDisabled ? null : _handleSend,
            child: Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                color: widget.isDisabled
                    ? Colors.grey[300]
                    : (_hasText && !widget.isLoading
                        ? primaryColor
                        : Colors.grey[300]),
                borderRadius: BorderRadius.circular(5.w),
              ),
              child: widget.isLoading
                  ? Center(
                      child: SizedBox(
                        width: 4.w,
                        height: 4.w,
                        child: const CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                    )
                  : CustomIconWidget(
                      iconName: 'send',
                      color: widget.isDisabled 
                          ? Colors.grey[500]!
                          : (_hasText ? Colors.white : Colors.grey[500]!),
                      size: 5.w,
                    ),
            ),
          ),
        ],
      ),
    );
  }
}