import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class TypingIndicatorWidget extends StatelessWidget {
  final AnimationController animation;

  const TypingIndicatorWidget({
    super.key,
    required this.animation,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildAvatar(context),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(bottom: 1.h),
                  child: Text(
                    'AI Assistant',
                    style: Theme.of(context).textTheme.labelMedium?.copyWith(
                          color: Colors.grey[600],
                          fontWeight: FontWeight.w500,
                        ),
                  ),
                ),
                _buildTypingBubble(context),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return Container(
      width: 10.w,
      height: 10.w,
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.secondary,
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: CustomIconWidget(
        iconName: 'smart_toy',
        color: Colors.white,
        size: 5.w,
      ),
    );
  }

  Widget _buildTypingBubble(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark 
            ? Colors.grey[800] 
            : Colors.grey[100],
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(4.w),
          topRight: Radius.circular(4.w),
          bottomLeft: Radius.circular(1.w),
          bottomRight: Radius.circular(4.w),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildTypingDot(0),
          SizedBox(width: 1.w),
          _buildTypingDot(1),
          SizedBox(width: 1.w),
          _buildTypingDot(2),
          SizedBox(width: 3.w),
          Text(
            'AI is thinking...',
            style: GoogleFonts.inter(
              fontSize: 11.sp,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypingDot(int index) {
    return AnimatedBuilder(
      animation: animation,
      builder: (context, child) {
        final animationValue = animation.value;
        final delay = index * 0.2;
        final adjustedValue = (animationValue - delay).clamp(0.0, 1.0);

        return Transform.scale(
          scale: 0.5 + (0.5 * (1 + (adjustedValue * 2 - 1).abs())),
          child: Container(
            width: 2.w,
            height: 2.w,
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.primaryColor.withValues(
                alpha: 0.3 + (0.7 * adjustedValue),
              ),
              borderRadius: BorderRadius.circular(1.w),
            ),
          ),
        );
      },
    );
  }
}
