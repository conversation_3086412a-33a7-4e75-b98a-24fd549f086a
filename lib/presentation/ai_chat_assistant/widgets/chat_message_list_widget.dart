import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../models/baby_profile.dart';
import '../ai_chat_assistant.dart';
import 'enhanced_chat_message_widget.dart';

/// Reusable widget for displaying chat message lists
class ChatMessageListWidget extends StatelessWidget {
  final List<EnhancedChatMessage> messages;
  final BabyProfile? babyProfile;
  final ScrollController? scrollController;
  final EdgeInsets? padding;

  const ChatMessageListWidget({
    super.key,
    required this.messages,
    required this.babyProfile,
    this.scrollController,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      controller: scrollController,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        return babyProfile != null
            ? EnhancedChatMessageWidget(
                message: messages[index],
                babyProfile: babyProfile!,
              )
            : Container(
                padding: EdgeInsets.all(4.w),
                child: Text(
                  'Message available but no baby profile',
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              );
      },
    );
  }
}