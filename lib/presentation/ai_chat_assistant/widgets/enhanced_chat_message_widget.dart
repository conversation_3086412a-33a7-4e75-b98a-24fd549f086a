import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../ai_chat_assistant.dart';

class EnhancedChatMessageWidget extends StatelessWidget {
  final EnhancedChatMessage message;
  final BabyProfile babyProfile;

  const EnhancedChatMessageWidget({
    super.key,
    required this.message,
    required this.babyProfile,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!message.isFromUser) ...[
            _buildAvatar(context, isUser: false),
            SizedBox(width: 3.w),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: message.isFromUser
                  ? CrossAxisAlignment.end
                  : CrossAxisAlignment.start,
              children: [
                if (!message.isFromUser)
                  Padding(
                    padding: EdgeInsets.only(bottom: 1.h),
                    child: Row(
                      children: [
                        Text(
                          'AI Assistant',
                          style:
                              Theme.of(context).textTheme.labelMedium?.copyWith(
                                    color: Colors.grey[600],
                                    fontWeight: FontWeight.w500,
                                  ),
                        ),
                        if (message.messageType != MessageType.assistant) ...[
                          SizedBox(width: 2.w),
                          _buildMessageTypeChip(context),
                        ],
                      ],
                    ),
                  ),
                _buildMessageBubble(context),
                SizedBox(height: 0.5.h),
                _buildMessageFooter(context),
              ],
            ),
          ),
          if (message.isFromUser) ...[
            SizedBox(width: 3.w),
            _buildAvatar(context, isUser: true),
          ],
        ],
      ),
    );
  }

  Widget _buildAvatar(BuildContext context, {required bool isUser}) {
    return Container(
      width: 10.w,
      height: 10.w,
      decoration: BoxDecoration(
        color:
            isUser ? AppTheme.lightTheme.primaryColor : _getMessageTypeColor(),
        borderRadius: BorderRadius.circular(5.w),
      ),
      child: CustomIconWidget(
        iconName: isUser
            ? (message.isVoiceInput ? 'record_voice_over' : 'person')
            : _getMessageTypeIcon(),
        color: Colors.white,
        size: 5.w,
      ),
    );
  }

  Widget _buildMessageTypeChip(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: _getMessageTypeColor().withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(2.w),
      ),
      child: Text(
        _getMessageTypeLabel(),
        style: GoogleFonts.inter(
          fontSize: 9.sp,
          fontWeight: FontWeight.w600,
          color: _getMessageTypeColor(),
        ),
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context) {
    final isUser = message.isFromUser;
    final isError = message.isError;

    Color backgroundColor;
    Color textColor;

    if (isError) {
      backgroundColor = Colors.red[50]!;
      textColor = Colors.red[700]!;
    } else if (isUser) {
      backgroundColor = AppTheme.lightTheme.primaryColor;
      textColor = Colors.white;
    } else {
      backgroundColor = message.hasRichContent 
          ? (Theme.of(context).brightness == Brightness.dark 
              ? Colors.blue[900]! 
              : Colors.blue[50]!)
          : (Theme.of(context).brightness == Brightness.dark 
              ? Colors.grey[800]! 
              : Colors.grey[100]!);
      textColor = Theme.of(context).brightness == Brightness.dark 
          ? Colors.grey[200]! 
          : Colors.grey[800]!;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(4.w),
          topRight: Radius.circular(4.w),
          bottomLeft: Radius.circular(isUser ? 4.w : 1.w),
          bottomRight: Radius.circular(isUser ? 1.w : 4.w),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 5,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (isError)
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'error_outline',
                  color: Colors.red[600],
                  size: 4.w,
                ),
                SizedBox(width: 2.w),
                Text(
                  'Error',
                  style: GoogleFonts.inter(
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.red[600],
                  ),
                ),
              ],
            ),
          if (isError) SizedBox(height: 1.h),
          if (message.isVoiceInput && isUser)
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'mic',
                  color: Colors.white70,
                  size: 3.w,
                ),
                SizedBox(width: 1.w),
                Text(
                  'Voice message',
                  style: GoogleFonts.inter(
                    fontSize: 10.sp,
                    color: Colors.white70,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ),
          if (message.isVoiceInput && isUser) SizedBox(height: 1.h),
          _buildMessageContent(context, textColor),
          if (message.hasRichContent && !isUser)
            _buildRichContentIndicator(context),
        ],
      ),
    );
  }

  Widget _buildMessageContent(BuildContext context, Color textColor) {
    return Text(
      message.content,
      style: GoogleFonts.inter(
        fontSize: 12.sp,
        color: textColor,
        height: 1.4,
      ),
    );
  }

  Widget _buildRichContentIndicator(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 1.h),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: 'auto_awesome',
            color: AppTheme.lightTheme.primaryColor,
            size: 3.w,
          ),
          SizedBox(width: 1.w),
          Text(
            'Enhanced response with detailed guidance',
            style: GoogleFonts.inter(
              fontSize: 9.sp,
              color: AppTheme.lightTheme.primaryColor,
              fontStyle: FontStyle.italic,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageFooter(BuildContext context) {
    return Row(
      mainAxisAlignment:
          message.isFromUser ? MainAxisAlignment.end : MainAxisAlignment.start,
      children: [
        Text(
          _formatTime(message.timestamp),
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[500],
                fontSize: 10.sp,
              ),
        ),
        if (!message.isFromUser && !message.isError) ...[
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: () => _copyToClipboard(context),
            child: CustomIconWidget(
              iconName: 'content_copy',
              size: 3.5.w,
              color: Colors.grey[500],
            ),
          ),
          SizedBox(width: 2.w),
          GestureDetector(
            onTap: () => _shareMessage(context),
            child: CustomIconWidget(
              iconName: 'share',
              size: 3.5.w,
              color: Colors.grey[500],
            ),
          ),
        ],
        if (message.isStreaming) ...[
          SizedBox(width: 2.w),
          SizedBox(
            width: 3.w,
            height: 3.w,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppTheme.lightTheme.primaryColor,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Color _getMessageTypeColor() {
    // Always show consistent AI color for assistant messages
    if (!message.isFromUser) {
      return const Color(0xFF6366F1); // Modern purple/indigo AI color
    }
    
    switch (message.messageType) {
      case MessageType.feeding:
        return const Color(0xFF4A90A4);
      case MessageType.sleep:
        return const Color(0xFFF4A261);
      case MessageType.development:
        return const Color(0xFF7FB069);
      case MessageType.health:
        return const Color(0xFFE76F51);
      case MessageType.welcome:
        return const Color(0xFF2A9D8F);
      case MessageType.error:
        return Colors.red[600]!;
      default:
        return AppTheme.lightTheme.colorScheme.secondary;
    }
  }

  String _getMessageTypeIcon() {
    // Always show AI icon for assistant messages, regardless of topic
    if (!message.isFromUser) {
      return 'auto_awesome'; // Modern AI/sparkle icon
    }
    
    switch (message.messageType) {
      case MessageType.feeding:
        return 'restaurant';
      case MessageType.sleep:
        return 'bedtime';
      case MessageType.development:
        return 'child_care';
      case MessageType.health:
        return 'health_and_safety';
      case MessageType.welcome:
        return 'waving_hand';
      case MessageType.error:
        return 'error_outline';
      default:
        return 'auto_awesome'; // Default AI icon
    }
  }

  String _getMessageTypeLabel() {
    // Don't show duplicate "AI Assistant" label since we already have the name
    if (!message.isFromUser) {
      switch (message.messageType) {
        case MessageType.feeding:
          return 'Feeding Advice';
        case MessageType.sleep:
          return 'Sleep Guidance';
        case MessageType.development:
          return 'Development';
        case MessageType.health:
          return 'Health Tips';
        case MessageType.welcome:
          return 'Welcome';
        case MessageType.error:
          return 'Error';
        default:
          return 'Assistant'; // Don't duplicate "AI Assistant"
      }
    }
    
    switch (message.messageType) {
      case MessageType.feeding:
        return 'Feeding Advice';
      case MessageType.sleep:
        return 'Sleep Guidance';
      case MessageType.development:
        return 'Development';
      case MessageType.health:
        return 'Health Tips';
      case MessageType.welcome:
        return 'Welcome';
      case MessageType.error:
        return 'Error';
      default:
        return 'Assistant';
    }
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${dateTime.day}/${dateTime.month} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  void _copyToClipboard(BuildContext context) {
    Clipboard.setData(ClipboardData(text: message.content));
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Message copied to clipboard'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _shareMessage(BuildContext context) {
    // In a real implementation, you would use share_plus package
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality would be implemented here'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}
