import 'package:flutter/foundation.dart';
import '../../../services/ai_chat_service.dart';
import '../../../models/baby_profile.dart';
import '../../../models/activity_log.dart';
import '../ai_chat_assistant.dart';

/// Manages chat messages, history, and database operations
class ChatMessageManager extends ChangeNotifier {
  final AIChatService _chatService = AIChatService();
  final List<EnhancedChatMessage> _messages = [];
  final Set<String> _storedMessageIds = {};
  
  bool _isLoading = false;
  bool _isStreaming = false;
  bool _hasLoadedHistory = false;
  String _streamingMessage = '';

  List<EnhancedChatMessage> get messages => List.unmodifiable(_messages);
  bool get isLoading => _isLoading;
  bool get isStreaming => _isStreaming;
  bool get hasLoadedHistory => _hasLoadedHistory;
  String get streamingMessage => _streamingMessage;

  /// Load chat history from database
  Future<void> loadChatHistory(String babyId, {int limit = 100}) async {
    if (_hasLoadedHistory) return;
    
    _isLoading = true;
    notifyListeners();

    try {
      final chatHistory = await _chatService.loadChatHistory(
        babyId: babyId,
        limit: limit,
      );

      if (chatHistory.isNotEmpty) {
        final loadedMessages = chatHistory.map((msg) {
          return EnhancedChatMessage(
            id: msg['id'] as String,
            content: msg['content'] as String,
            isFromUser: msg['is_from_user'] as bool,
            timestamp: DateTime.parse(msg['timestamp'] as String),
            isVoiceInput: msg['is_voice_input'] as bool? ?? false,
            messageType: _parseMessageType(msg['message_type'] as String?),
            hasRichContent: _hasRichContent(msg['content'] as String),
          );
        }).toList();

        _messages.clear();
        _messages.addAll(loadedMessages);
        _storedMessageIds.clear();
        _storedMessageIds.addAll(loadedMessages.map((msg) => msg.id));
        _hasLoadedHistory = true;
      }
    } catch (e) {
      debugPrint('Error loading chat history: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Add a new message to the chat
  void addMessage(EnhancedChatMessage message) {
    _messages.add(message);
    notifyListeners();
  }

  /// Update streaming message content
  void updateStreamingMessage(String content) {
    _streamingMessage = content;
    if (_messages.isNotEmpty) {
      _messages.last = _messages.last.copyWith(content: content);
    }
    notifyListeners();
  }

  /// Store message in database
  Future<void> storeMessage(
    String babyId,
    EnhancedChatMessage message,
  ) async {
    if (_storedMessageIds.contains(message.id)) return;

    try {
      await _chatService.storeChatMessage(
        babyId: babyId,
        content: message.content,
        isFromUser: message.isFromUser,
        timestamp: message.timestamp,
        messageId: message.id,
        messageType: message.messageType.name,
        isVoiceInput: message.isVoiceInput,
      );
      _storedMessageIds.add(message.id);
    } catch (e) {
      debugPrint('Error storing message: $e');
    }
  }

  /// Clear all messages
  Future<void> clearHistory(String babyId) async {
    try {
      await _chatService.clearChatHistory(babyId);
      _messages.clear();
      _storedMessageIds.clear();
      _hasLoadedHistory = false;
      notifyListeners();
    } catch (e) {
      debugPrint('Error clearing chat history: $e');
      rethrow;
    }
  }

  MessageType _parseMessageType(String? type) {
    switch (type) {
      case 'user':
        return MessageType.user;
      case 'feeding':
        return MessageType.feeding;
      case 'sleep':
        return MessageType.sleep;
      case 'development':
        return MessageType.development;
      case 'health':
        return MessageType.health;
      case 'welcome':
        return MessageType.welcome;
      case 'error':
        return MessageType.error;
      default:
        return MessageType.assistant;
    }
  }

  bool _hasRichContent(String content) {
    return content.contains('•') ||
        content.contains('1.') ||
        content.contains('-') ||
        content.length > 200;
  }
}