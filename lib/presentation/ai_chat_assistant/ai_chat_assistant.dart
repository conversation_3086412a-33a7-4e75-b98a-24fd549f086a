import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import 'package:uuid/uuid.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';
import '../../presentation/subscription/controllers/subscription_controller.dart';
import '../subscription/widgets/premium_feature_card.dart';
import '../subscription/subscription_screen.dart';
import './widgets/advanced_quick_topics_widget.dart';
import './widgets/chat_search_widget.dart';
import './widgets/enhanced_chat_input_widget.dart';
import './widgets/enhanced_chat_message_widget.dart';

class AIChatAssistant extends StatefulWidget {
  final BabyProfile? babyProfile;
  final List<ActivityLog>? recentActivities;

  const AIChatAssistant({
    super.key,
    this.babyProfile,
    this.recentActivities,
  });

  @override
  State<AIChatAssistant> createState() => _AIChatAssistantState();
}

class _AIChatAssistantState extends State<AIChatAssistant>
    with TickerProviderStateMixin {
  final AIChatService _chatService = AIChatService();
  final _uuid = Uuid();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _messageController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();

  final List<EnhancedChatMessage> _messages = [];
  final List<EnhancedChatMessage> _filteredMessages = [];
  final Set<String> _storedMessageIds = {}; // Track stored message IDs to prevent duplicates
  
  static const int _maxMessagesInMemory = 200; // Prevent memory issues
  bool _isLoading = false;
  bool _isStreaming = false;
  bool _isSearchMode = false;
  bool _isLoadingHistory = false;
  bool _hasLoadedHistory = false;
  String _streamingMessage = '';
  late AnimationController _typingAnimationController;
  BabyProfile? _babyProfile;

  @override
  void initState() {
    super.initState();
    _typingAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _babyProfileManager.addListener(_onBabyProfileChanged);
    _loadActiveBaby();
    
    // Cache clearing disabled - conversational prompt now active
    // ChatCacheService.clearAllCache();
  }

  void _onBabyProfileChanged() {
    if (mounted) {
      debugPrint('🔄 Baby profile changed in AI Chat Assistant, reloading');
      _loadActiveBaby();
    }
  }
  
  Future<void> _loadActiveBaby() async {
    try {
      // Try to get active baby from state manager first
      BabyProfile? activeBaby = _babyProfileManager.activeBaby;
      
      if (activeBaby != null) {
        debugPrint('✅ Using active baby from state manager: ${activeBaby.name} (${activeBaby.id})');
        _babyProfile = activeBaby;
      } else {
        // Fallback to widget parameter
        _babyProfile = widget.babyProfile;
        debugPrint('⚠️ No active baby in state manager, using widget parameter: ${_babyProfile?.name}');
      }
      
      if (_babyProfile != null) {
        await _loadChatHistory();
      } else {
        debugPrint('❌ No baby profile available for AI Chat Assistant');
        _addWelcomeMessage();
      }
    } catch (e) {
      _handleError('loading active baby', e, showSnackbar: false);
      _addWelcomeMessage();
    }
  }

  /// Load existing chat history from database
  Future<void> _loadChatHistory() async {
    if (_hasLoadedHistory || _babyProfile == null) return;
    
    setState(() {
      _isLoadingHistory = true;
    });

    try {
      final chatHistory = await _chatService.loadChatHistory(
        babyId: _babyProfile!.id,
        limit: 100,
      );

      if (chatHistory.isNotEmpty) {
        final loadedMessages = chatHistory.map((msg) {
          return EnhancedChatMessage(
            id: msg['id'] as String,
            content: msg['content'] as String,
            isFromUser: msg['is_from_user'] as bool,
            timestamp: DateTime.parse(msg['timestamp'] as String),
            isVoiceInput: msg['is_voice_input'] as bool? ?? false,
            messageType: _parseMessageType(msg['message_type'] as String?),
            hasRichContent: _hasRichContent(msg['content'] as String),
          );
        }).toList();

        setState(() {
          _messages.clear();
          _messages.addAll(loadedMessages);
          _hasLoadedHistory = true;
          // Track loaded message IDs to prevent duplicates
          _storedMessageIds.clear();
          _storedMessageIds.addAll(loadedMessages.map((msg) => msg.id));
        });

        debugPrint('✅ Loaded ${loadedMessages.length} messages from chat history');
      } else {
        // Add welcome message if no history exists
        _addWelcomeMessage();
      }
    } catch (e) {
      _handleError('loading chat history', e, showSnackbar: false);
      _addWelcomeMessage();
    } finally {
      setState(() {
        _isLoadingHistory = false;
      });
      _updateFilteredMessages();
    }
  }

  MessageType _parseMessageType(String? type) {
    switch (type) {
      case 'user':
        return MessageType.user;
      case 'feeding':
        return MessageType.feeding;
      case 'sleep':
        return MessageType.sleep;
      case 'development':
        return MessageType.development;
      case 'health':
        return MessageType.health;
      case 'welcome':
        return MessageType.welcome;
      case 'error':
        return MessageType.error;
      default:
        return MessageType.assistant;
    }
  }

  void _addWelcomeMessage() {
    final welcomeMessage = EnhancedChatMessage(
      id: _uuid.v4(),
      content:
          "Hello! I'm your enhanced AI parenting assistant for ${_babyProfile?.name ?? 'your baby'}. I can provide personalized guidance based on your baby's activities, answer questions with voice input, and help you search through our conversation history. How can I help you today?",
      isFromUser: false,
      timestamp: DateTime.now(),
      hasRichContent: true,
      messageType: MessageType.welcome,
    );

    setState(() {
      _messages.add(welcomeMessage);
      _hasLoadedHistory = true;
    });

    // Store welcome message in database
    if (_babyProfile != null) {
      _chatService.storeChatMessage(
        babyId: _babyProfile!.id,
        content: welcomeMessage.content,
        isFromUser: false,
        timestamp: welcomeMessage.timestamp,
        messageId: welcomeMessage.id,
        messageType: 'welcome',
      );
    }

    // Update filtered messages and scroll to show welcome message
    _updateFilteredMessages();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _scrollToBottom();
    });
  }

  Future<void> _sendMessage(String message, {bool isVoiceInput = false}) async {
    if (message.trim().isEmpty || _isLoading) return;

    final userMessage = EnhancedChatMessage(
      id: _uuid.v4(),
      content: message,
      isFromUser: true,
      timestamp: DateTime.now(),
      isVoiceInput: isVoiceInput,
      messageType: MessageType.user,
    );

    setState(() {
      _messages.add(userMessage);
      _trimMessagesIfNeeded();
      _isLoading = true;
      _isStreaming = true;
      _streamingMessage = '';
    });

    // Store user message in database (prevent duplicates)
    if (_babyProfile != null && !_storedMessageIds.contains(userMessage.id)) {
      _chatService.storeChatMessage(
        babyId: _babyProfile!.id,
        content: userMessage.content,
        isFromUser: true,
        timestamp: userMessage.timestamp,
        messageId: userMessage.id,
        messageType: 'user',
        isVoiceInput: isVoiceInput,
      );
      _storedMessageIds.add(userMessage.id);
    }

    _messageController.clear();
    _updateFilteredMessages();
    _scrollToBottom();
    _typingAnimationController.repeat();

    try {
      final conversationHistory = _messages
          .where((msg) => msg.id != userMessage.id && !msg.isWelcome)
          .map((msg) => {
                'role': msg.isFromUser ? 'user' : 'assistant',
                'content': msg.content,
              })
          .toList();

      final aiMessageId = _uuid.v4();

      // Add placeholder for streaming message
      final streamingPlaceholder = EnhancedChatMessage(
        id: aiMessageId,
        content: '',
        isFromUser: false,
        timestamp: DateTime.now(),
        isStreaming: true,
        messageType: MessageType.assistant,
      );

      setState(() {
        _messages.add(streamingPlaceholder);
      });

      if (_babyProfile == null) {
        throw Exception('No baby profile available for AI chat');
      }
      
      await for (final chunk in _chatService.streamChatResponse(
        userMessage: message,
        babyProfile: _babyProfile!,
        recentActivities: widget.recentActivities,
        conversationHistory: conversationHistory,
      )) {
        setState(() {
          _streamingMessage += chunk;
          // Update the last message (streaming placeholder) with accumulated content
          _messages.last = _messages.last.copyWith(content: _streamingMessage);
        });
        _scrollToBottom();
      }

      // Mark streaming as complete and analyze content
      final finalMessage = _messages.last.copyWith(
        isStreaming: false,
        hasRichContent: _hasRichContent(_streamingMessage),
        messageType: _determineMessageType(_streamingMessage),
      );

      setState(() {
        _messages.last = finalMessage;
        _isStreaming = false;
        _streamingMessage = '';
      });

      // Store AI response in database (prevent duplicates)
      if (_babyProfile != null && !_storedMessageIds.contains(finalMessage.id)) {
        _chatService.storeChatMessage(
          babyId: _babyProfile!.id,
          content: finalMessage.content,
          isFromUser: false,
          timestamp: finalMessage.timestamp,
          messageId: finalMessage.id,
          messageType: finalMessage.messageType.name,
        );
        _storedMessageIds.add(finalMessage.id);
      }

    } catch (e) {
      final errorMessage = EnhancedChatMessage(
        id: _uuid.v4(),
        content:
            "I apologize, but I encountered an error while processing your message. Please check your connection and try again.",
        isFromUser: false,
        timestamp: DateTime.now(),
        isError: true,
        messageType: MessageType.error,
      );

      setState(() {
        _messages.add(errorMessage);
        _isStreaming = false;
        _streamingMessage = '';
      });

      // Store error message in database (prevent duplicates)
      if (_babyProfile != null && !_storedMessageIds.contains(errorMessage.id)) {
        _chatService.storeChatMessage(
          babyId: _babyProfile!.id,
          content: errorMessage.content,
          isFromUser: false,
          timestamp: errorMessage.timestamp,
          messageId: errorMessage.id,
          messageType: 'error',
        );
        _storedMessageIds.add(errorMessage.id);
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
      _typingAnimationController.stop();
      _updateFilteredMessages();
      _scrollToBottom();
    }
  }

  Future<void> _sendQuickTopic(String topic) async {
    // Check subscription status first
    final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
    
    if (subscriptionController.isOnFreePlan) {
      // Show upgrade dialog for free users
      _showUpgradeDialog();
      return;
    }
    
    final babyName = _babyProfile?.name ?? 'my baby';
    final ageInMonths = _babyProfile?.ageInMonths ?? 0;
    
    final topicQuestions = {
      'Feeding':
          'Can you analyze ${babyName}\'s feeding patterns and provide personalized advice?',
      'Sleep':
          'I need help optimizing ${babyName}\'s sleep schedule. What do you recommend?',
      'Development':
          'What developmental milestones should I expect for ${babyName} at ${ageInMonths} months?',
      'Health':
          'What health indicators should I monitor for a ${ageInMonths}-month-old?',
      'Crying':
          '${babyName} has been crying more than usual. Can you help me understand why?',
      'Safety': 'What safety measures are most important at this age?',
      'Growth': 'How is ${babyName}\'s growth progressing?',
      'Activities':
          'What activities would benefit ${babyName}\'s development right now?',
    };

    final question =
        topicQuestions[topic] ?? 'Tell me about $topic for my baby.';
    await _sendMessage(question);
  }

  /// Show upgrade dialog when free users try to use premium features
  void _showUpgradeDialog() {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: isDark ? Colors.grey.shade900 : Colors.white,
        title: Row(
          children: [
            Icon(
              Icons.star,
              color: Colors.orange.shade600,
              size: 24,
            ),
            SizedBox(width: 8),
            Text(
              'Premium Feature',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
                color: Colors.orange.shade600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Unlock AI Chat Assistant',
              style: GoogleFonts.inter(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black,
              ),
            ),
            SizedBox(height: 8),
            Text(
              'Get personalized AI parenting guidance and support.',
              style: GoogleFonts.inter(
                fontSize: 14,
                color: isDark ? Colors.grey.shade300 : Colors.grey.shade600,
              ),
            ),
            SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isDark ? Colors.orange.shade900.withAlpha(100) : Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isDark ? Colors.orange.shade700 : Colors.orange.shade200,
                ),
              ),
              child: Column(
                children: [
                  _buildUpgradeFeature(Icons.psychology, 'Personalized Advice', isDark),
                  SizedBox(height: 8),
                  _buildUpgradeFeature(Icons.auto_awesome, 'Quick Topics', isDark),
                  SizedBox(height: 8),
                  _buildUpgradeFeature(Icons.history, 'Chat History', isDark),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Maybe Later',
              style: GoogleFonts.inter(
                color: isDark ? Colors.grey.shade400 : Colors.grey.shade600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SubscriptionScreen(
                    initialFocus: 'premium',
                  ),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Upgrade to Premium',
              style: GoogleFonts.inter(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build feature item for upgrade dialog
  Widget _buildUpgradeFeature(IconData icon, String title, bool isDark) {
    return Row(
      children: [
        Icon(
          icon,
          color: Colors.orange.shade600,
          size: 16,
        ),
        SizedBox(width: 8),
        Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: isDark ? Colors.orange.shade100 : Colors.grey.shade700,
          ),
        ),
      ],
    );
  }

  void _searchMessages(String query) {
    if (query.isEmpty) {
      setState(() {
        _filteredMessages.clear();
        _filteredMessages.addAll(_messages);
      });
      return;
    }

    final filtered = _messages
        .where((message) =>
            message.content.toLowerCase().contains(query.toLowerCase()))
        .toList();

    setState(() {
      _filteredMessages.clear();
      _filteredMessages.addAll(filtered);
    });
  }

  void _toggleSearchMode() {
    setState(() {
      _isSearchMode = !_isSearchMode;
      if (!_isSearchMode) {
        _searchController.clear();
        _filteredMessages.clear();
        _filteredMessages.addAll(_messages);
        // Ensure focus returns to message input when exiting search
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            FocusScope.of(context).unfocus();
          }
        });
      } else {
        // When entering search mode, unfocus the message input
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            FocusScope.of(context).unfocus();
          }
        });
      }
    });
  }

  void _updateFilteredMessages() {
    if (!_isSearchMode) {
      setState(() {
        _filteredMessages.clear();
        _filteredMessages.addAll(_messages);
      });
    }
  }

  bool _hasRichContent(String content) {
    return content.contains('•') ||
        content.contains('1.') ||
        content.contains('-') ||
        content.length > 200;
  }

  MessageType _determineMessageType(String content) {
    if (content.toLowerCase().contains('feeding') ||
        content.toLowerCase().contains('milk') ||
        content.toLowerCase().contains('bottle')) {
      return MessageType.feeding;
    } else if (content.toLowerCase().contains('sleep') ||
        content.toLowerCase().contains('nap')) {
      return MessageType.sleep;
    } else if (content.toLowerCase().contains('development') ||
        content.toLowerCase().contains('milestone')) {
      return MessageType.development;
    }
    return MessageType.assistant;
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  /// Build the main content area based on current state
  Widget _buildMainContent() {
    if (_isSearchMode) {
      return _buildSearchResults();
    }
    
    if (_messages.isEmpty || (_messages.length == 1 && _messages.first.isWelcome)) {
      return _buildWelcomeScreen();
    }
    
    return _buildChatConversation();
  }

  /// Build search results view
  Widget _buildSearchResults() {
    return ListView.builder(
      controller: _scrollController,
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      itemCount: _filteredMessages.length,
      itemBuilder: (context, index) {
        return _babyProfile != null
            ? EnhancedChatMessageWidget(
                message: _filteredMessages[index],
                babyProfile: _babyProfile!,
              )
            : _buildNoBabyProfileMessage();
      },
    );
  }

  /// Build welcome screen with quick topics
  Widget _buildWelcomeScreen() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          // Show welcome message if it exists
          if (_messages.isNotEmpty && _messages.first.isWelcome && _babyProfile != null)
            Padding(
              padding: EdgeInsets.only(bottom: 3.h),
              child: EnhancedChatMessageWidget(
                message: _messages.first,
                babyProfile: _babyProfile!,
              ),
            ),
          // Show Quick Topics
          _babyProfile != null
              ? AdvancedQuickTopicsWidget(
                  onTopicSelected: _sendQuickTopic,
                  babyProfile: _babyProfile!,
                )
              : Center(
                  child: Text(
                    'No baby profile available',
                    style: Theme.of(context).textTheme.bodyLarge,
                  ),
                ),
        ],
      ),
    );
  }

  /// Build chat conversation with messages and quick topics
  Widget _buildChatConversation() {
    return Column(
      children: [
        // Chat messages
        Expanded(
          child: ListView.builder(
            controller: _scrollController,
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            itemCount: _messages.length,
            itemBuilder: (context, index) {
              return _babyProfile != null
                  ? EnhancedChatMessageWidget(
                      message: _messages[index],
                      babyProfile: _babyProfile!,
                    )
                  : _buildNoBabyProfileMessage();
            },
          ),
        ),
        // Quick Topics panel at bottom
        if (_shouldShowQuickTopicsPanel())
          _buildQuickTopicsPanel(),
      ],
    );
  }

  /// Build message for when no baby profile is available
  Widget _buildNoBabyProfileMessage() {
    return Container(
      padding: EdgeInsets.all(4.w),
      child: Text(
        'Message available but no baby profile',
        style: Theme.of(context).textTheme.bodyMedium,
      ),
    );
  }

  /// Check if quick topics panel should be shown
  bool _shouldShowQuickTopicsPanel() {
    return _messages.length > 1 || 
           (_messages.length == 1 && !_messages.first.isWelcome);
  }

  /// Build the expandable quick topics panel
  Widget _buildQuickTopicsPanel() {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: ExpansionTile(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Topics for ${_babyProfile?.name ?? 'Baby'}',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: Theme.of(context).primaryColor,
              ),
            ),
            if (_babyProfile != null)
              Text(
                'Age-appropriate guidance at ${_babyProfile!.ageInMonths} months',
                style: GoogleFonts.inter(
                  fontSize: 10.sp,
                  color: Theme.of(context).textTheme.bodySmall?.color?.withValues(alpha: 0.7),
                ),
              ),
          ],
        ),
        leading: Icon(
          Icons.auto_awesome,
          color: Theme.of(context).primaryColor,
          size: 20,
        ),
        initiallyExpanded: false,
        children: [
          Container(
            constraints: const BoxConstraints(maxHeight: 220),
            child: SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.only(
                  left: 4.w,
                  right: 4.w,
                  bottom: 3.h,
                ),
                child: _babyProfile != null
                    ? AdvancedQuickTopicsWidget(
                        onTopicSelected: _sendQuickTopic,
                        babyProfile: _babyProfile!,
                      )
                    : Container(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Handle errors consistently throughout the app
  void _handleError(String operation, dynamic error, {bool showSnackbar = true}) {
    final errorMessage = 'Error in $operation: $error';
    debugPrint('❌ $errorMessage');
    
    if (showSnackbar && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('❌ $operation failed'),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  /// Show success message
  void _showSuccess(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ $message'),
          backgroundColor: Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }

  /// Trim messages if they exceed the maximum limit
  void _trimMessagesIfNeeded() {
    if (_messages.length > _maxMessagesInMemory) {
      final messagesToRemove = _messages.length - _maxMessagesInMemory;
      final removedMessages = _messages.take(messagesToRemove).toList();
      
      // Remove from stored IDs set
      for (final message in removedMessages) {
        _storedMessageIds.remove(message.id);
      }
      
      // Keep only the most recent messages
      _messages.removeRange(0, messagesToRemove);
      
      debugPrint('Trimmed $messagesToRemove old messages to prevent memory issues');
    }
  }

  @override
  void dispose() {
    // Stop any ongoing animations
    _typingAnimationController.stop();
    
    // Dispose controllers
    _scrollController.dispose();
    _messageController.dispose();
    _searchController.dispose();
    _typingAnimationController.dispose();
    
    // Remove listeners
    _babyProfileManager.removeListener(_onBabyProfileChanged);
    
    // Clear collections to help with garbage collection
    _messages.clear();
    _filteredMessages.clear();
    _storedMessageIds.clear();
    
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    // Always show chat interface with improved premium card for free users
    return _buildChatInterface(isDark);
  }

  /// Build the main chat interface
  Widget _buildChatInterface(bool isDark) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Ask AI',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: isDark ? Colors.white : Colors.black,
              ),
            ),
            if (_babyProfile != null)
              Text(
                'for ${_babyProfile!.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.normal,
                  color: (isDark ? Colors.grey[300] : Colors.grey[700])?.withValues(alpha: 0.8),
                ),
              ),
          ],
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        scrolledUnderElevation: 0,
        leading: IconButton(
          icon: Icon(
            Icons.arrow_back,
            color: isDark ? Colors.white : Colors.black,
          ),
          onPressed: () {
            if (mounted && Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              Navigator.pushReplacementNamed(context, '/main-navigation');
            }
          },
        ),
        actions: [
          IconButton(
            icon: CustomIconWidget(
              iconName: _isSearchMode ? 'close' : 'search',
              size: 6.w,
              color: isDark ? Colors.white : Colors.black,
            ),
            onPressed: _toggleSearchMode,
          ),
          IconButton(
            icon: CustomIconWidget(
              iconName: 'history',
              size: 6.w,
              color: isDark ? Colors.white : Colors.black,
            ),
            onPressed: _showChatHistoryOptions,
          ),
          IconButton(
            icon: CustomIconWidget(
              iconName: 'info_outline',
              size: 6.w,
              color: isDark ? Colors.white : Colors.black,
            ),
            onPressed: () => _showInfoDialog(),
          ),
        ],
      ),
      body: Column(
        children: [
          // Search bar (shown in search mode)
          if (_isSearchMode)
            ChatSearchWidget(
              controller: _searchController,
              onSearch: _searchMessages,
              onClear: () {
                _searchController.clear();
                _searchMessages('');
              },
            ),

          // Main Content Area
          Expanded(child: _buildMainContent()),

          // Improved Premium Feature Card (only for free users)
          Consumer<SubscriptionController>(
            builder: (context, subscriptionController, _) {
              if (subscriptionController.isOnFreePlan) {
                return Container(
                  margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                  child: PremiumFeatureCard(
                    title: 'Unlock AI Chat Assistant',
                    description: 'Get personalized AI parenting guidance',
                    icon: Icons.psychology,
                    compact: false, // Make it more prominent
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const SubscriptionScreen(
                            initialFocus: 'premium',
                          ),
                        ),
                      );
                    },
                  ),
                );
              }
              return const SizedBox.shrink();
            },
          ),
          
          // Chat Input
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).scaffoldBackgroundColor,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 10,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: SafeArea(
              child: _babyProfile != null
                  ? Consumer<SubscriptionController>(
                      builder: (context, subscriptionController, _) {
                        return EnhancedChatInputWidget(
                          controller: _messageController,
                          onSend: subscriptionController.isOnFreePlan ? null : _sendMessage,
                          isLoading: _isLoading,
                          babyProfile: _babyProfile!,
                          isDisabled: subscriptionController.isOnFreePlan,
                          disabledMessage: 'Upgrade to Premium to send messages',
                        );
                      },
                    )
                  : Container(
                      padding: EdgeInsets.all(4.w),
                      child: Text(
                        'No baby profile available for chat',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  void _showInfoDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('AI Chat Assistant'),
        content: const Text(
          'Your enhanced AI parenting assistant provides:\n\n'
          '• Personalized advice based on your baby\'s data\n'
          '• Voice input for hands-free interaction\n'
          '• Rich content with charts and recommendations\n'
          '• Searchable conversation history\n'
          '• Real-time streaming responses\n'
          '• Age-appropriate developmental guidance\n\n'
          'Always consult with healthcare professionals for medical concerns.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  /// Show a professional dialog to manage chat history
  void _showChatHistoryOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: BorderRadius.vertical(top: Radius.circular(5.w)),
        ),
        padding: EdgeInsets.all(6.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chat History Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 4.h),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'refresh',
                size: 6.w,
                color: AppTheme.lightTheme.primaryColor,
              ),
              title: const Text('Refresh Chat History'),
              subtitle: const Text('Reload messages from database'),
              onTap: () {
                Navigator.pop(context);
                _refreshChatHistory();
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'history',
                size: 6.w,
                color: Colors.blue,
              ),
              title: Text('${_messages.length} Messages Loaded'),
              subtitle: const Text('Current conversation length'),
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'delete_forever',
                size: 6.w,
                color: Colors.red,
              ),
              title: const Text('Clear Chat History'),
              subtitle: const Text('Delete all messages permanently'),
              onTap: () {
                Navigator.pop(context);
                _showClearHistoryConfirmation();
              },
            ),
            SizedBox(height: 2.h),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text('Cancel'),
            ),
          ],
        ),
      ),
    );
  }

  /// Refresh chat history from database
  Future<void> _refreshChatHistory() async {
    setState(() {
      _isLoadingHistory = true;
      _hasLoadedHistory = false;
    });

    try {
      if (_babyProfile == null) {
        throw Exception('No baby profile available');
      }
      
      final chatHistory = await _chatService.loadChatHistory(
        babyId: _babyProfile!.id,
        limit: 100,
      );

      final loadedMessages = chatHistory.map((msg) {
        return EnhancedChatMessage(
          id: msg['id'] as String,
          content: msg['content'] as String,
          isFromUser: msg['is_from_user'] as bool,
          timestamp: DateTime.parse(msg['timestamp'] as String),
          isVoiceInput: msg['is_voice_input'] as bool? ?? false,
          messageType: _parseMessageType(msg['message_type'] as String?),
          hasRichContent: _hasRichContent(msg['content'] as String),
        );
      }).toList();

      setState(() {
        _messages.clear();
        _messages.addAll(loadedMessages);
        _hasLoadedHistory = true;
      });

      _updateFilteredMessages();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('✅ Refreshed ${loadedMessages.length} messages'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ Error refreshing chat history'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isLoadingHistory = false;
      });
    }
  }

  /// Show confirmation dialog for clearing chat history
  void _showClearHistoryConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat History'),
        content: const Text(
          'Are you sure you want to delete all chat messages? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _clearChatHistory();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );
  }

  /// Clear all chat history
  Future<void> _clearChatHistory() async {
    try {
      if (_babyProfile == null) {
        throw Exception('No baby profile available');
      }
      
      await _chatService.clearChatHistory(_babyProfile!.id);
      
      setState(() {
        _messages.clear();
        _hasLoadedHistory = false;
      });

      _addWelcomeMessage();
      _updateFilteredMessages();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('✅ Chat history cleared'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('❌ Error clearing chat history'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}

class EnhancedChatMessage {
  final String id;
  final String content;
  final bool isFromUser;
  final DateTime timestamp;
  final bool isError;
  final bool isStreaming;
  final bool isVoiceInput;
  final bool hasRichContent;
  final MessageType messageType;

  EnhancedChatMessage({
    required this.id,
    required this.content,
    required this.isFromUser,
    required this.timestamp,
    this.isError = false,
    this.isStreaming = false,
    this.isVoiceInput = false,
    this.hasRichContent = false,
    this.messageType = MessageType.assistant,
  });

  bool get isWelcome => messageType == MessageType.welcome;

  EnhancedChatMessage copyWith({
    String? id,
    String? content,
    bool? isFromUser,
    DateTime? timestamp,
    bool? isError,
    bool? isStreaming,
    bool? isVoiceInput,
    bool? hasRichContent,
    MessageType? messageType,
  }) {
    return EnhancedChatMessage(
      id: id ?? this.id,
      content: content ?? this.content,
      isFromUser: isFromUser ?? this.isFromUser,
      timestamp: timestamp ?? this.timestamp,
      isError: isError ?? this.isError,
      isStreaming: isStreaming ?? this.isStreaming,
      isVoiceInput: isVoiceInput ?? this.isVoiceInput,
      hasRichContent: hasRichContent ?? this.hasRichContent,
      messageType: messageType ?? this.messageType,
    );
  }
}

enum MessageType {
  user,
  assistant,
  welcome,
  error,
  feeding,
  sleep,
  development,
  health,
}
