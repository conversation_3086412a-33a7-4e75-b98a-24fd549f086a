import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/baby_profile_photo_widget.dart';

class BabiesManagementScreen extends StatefulWidget {
  const BabiesManagementScreen({super.key});

  @override
  State<BabiesManagementScreen> createState() => _BabiesManagementScreenState();
}

class _BabiesManagementScreenState extends State<BabiesManagementScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  
  List<BabyProfile> _babies = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadBabies();
  }

  Future<void> _loadBabies() async {
    try {
      setState(() => _isLoading = true);

      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      final response = await _supabaseService.select(
        'baby_profiles',
        filters: {'user_id': currentUser.id},
        orderBy: 'created_at',
        ascending: false,
      );

      final babies = response
          .map((data) => BabyProfile.fromJson({
                'id': data['id'],
                'name': data['name'],
                'birthDate': data['birth_date'],
                'gender': data['gender'],
                'photo': data['photo_url'],
                'birthWeight': data['birth_weight']?.toDouble(),
                'birthHeight': data['birth_height']?.toDouble(),
                'allergies': List<String>.from(data['allergies'] ?? []),
                'medications': List<String>.from(data['medications'] ?? []),
                'notes': data['notes'],
                'createdAt': data['created_at'],
                'updatedAt': data['updated_at'],
              }))
          .toList();

      setState(() {
        _babies = babies;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading babies: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load babies: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final months = (difference.inDays / 30.44).floor();
    final days = difference.inDays % 30;

    if (months > 0) {
      return '$months months, $days days';
    } else {
      return '${difference.inDays} days';
    }
  }

  String _formatBirthDate(DateTime birthDate) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${birthDate.day} ${months[birthDate.month - 1]} ${birthDate.year}';
  }

  void _addNewBaby() {
    Navigator.pushNamed(context, '/baby-profile-creation').then((result) {
      if (result == true) {
        _loadBabies(); // Reload babies after adding new one
      }
    });
  }

  void _editBaby(BabyProfile baby) {
    Navigator.pushNamed(
      context, 
      '/baby-profile-creation',
      arguments: baby,
    ).then((result) {
      if (result == true) {
        _loadBabies(); // Reload babies after editing
      }
    });
  }

  void _deleteBaby(BabyProfile baby) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Delete Baby Profile',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Are you sure you want to delete ${baby.name}\'s profile? This action cannot be undone.',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              await _performDelete(baby);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Theme.of(context).colorScheme.onError,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Delete'),
          ),
        ],
      ),
    );
  }

  Future<void> _performDelete(BabyProfile baby) async {
    try {
      await _supabaseService.delete('baby_profiles', 'id', baby.id);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('${baby.name}\'s profile has been deleted'),
          backgroundColor: ThemeAwareColors.getSuccessColor(context),
        ),
      );
      
      _loadBabies(); // Reload list
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to delete baby profile: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showBabyOptions(BabyProfile baby) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).dialogBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.symmetric(vertical: 2.h),
              decoration: BoxDecoration(
                color: Theme.of(context).dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'edit',
                color: Theme.of(context).colorScheme.primary,
              ),
              title: Text('Edit Profile'),
              onTap: () {
                Navigator.pop(context);
                _editBaby(baby);
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'visibility',
                color: ThemeAwareColors.getSuccessColor(context),
              ),
              title: Text('View Details'),
              onTap: () {
                Navigator.pop(context);
                _showBabyDetails(baby);
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'download',
                color: Colors.blue,
              ),
              title: Text('Export Data'),
              onTap: () {
                Navigator.pop(context);
                _exportBabyData(baby);
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'delete',
                color: Colors.red,
              ),
              title: Text('Delete Profile'),
              onTap: () {
                Navigator.pop(context);
                _deleteBaby(baby);
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }

  void _showBabyDetails(BabyProfile baby) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            CircleAvatar(
              backgroundColor: baby.gender == 'boy' ? Colors.blue : Colors.pink,
              child: Text(
                baby.name.substring(0, 1).toUpperCase(),
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                baby.name,
                style: TextStyle(
                  fontSize: 18.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('Age', _calculateAge(baby.birthDate)),
              _buildDetailRow('Birth Date', _formatBirthDate(baby.birthDate)),
              _buildDetailRow('Gender', baby.gender.toUpperCase()),
              if (baby.birthWeight != null)
                _buildDetailRow('Birth Weight', '${baby.birthWeight} kg'),
              if (baby.birthHeight != null)
                _buildDetailRow('Birth Height', '${baby.birthHeight} cm'),
              if (baby.allergies.isNotEmpty)
                _buildDetailRow('Allergies', baby.allergies.join(', ')),
              if (baby.medications.isNotEmpty)
                _buildDetailRow('Medications', baby.medications.join(', ')),
              if (baby.note != null && baby.note!.isNotEmpty)
                _buildDetailRow('Notes', baby.note!),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            child: Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _editBaby(baby);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Edit'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 25.w,
            child: Text(
              '$label:',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
            ),
          ),
        ],
      ),
    );
  }

  void _exportBabyData(BabyProfile baby) {
    // TODO: Implement data export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Data export for ${baby.name} will be available soon'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text('Manage Babies'),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: Theme.of(context).colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          IconButton(
            onPressed: _addNewBaby,
            icon: CustomIconWidget(
              iconName: 'add',
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: _loadBabies,
        color: Theme.of(context).colorScheme.primary,
        child: _isLoading
            ? Center(
                child: CircularProgressIndicator(
                  color: Theme.of(context).colorScheme.primary,
                ),
              )
            : _babies.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: EdgeInsets.all(4.w),
                    itemCount: _babies.length,
                    itemBuilder: (context, index) {
                      final baby = _babies[index];
                      return _buildBabyCard(baby);
                    },
                  ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _addNewBaby,
        backgroundColor: Theme.of(context).colorScheme.primary,
        child: CustomIconWidget(
          iconName: 'add',
          color: Colors.white,
          size: 24,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'child_care',
            size: 20.w,
            color: Colors.grey[400],
          ),
          SizedBox(height: 3.h),
          Text(
            'No Babies Added',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Add your first baby profile to get started',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4.h),
          ElevatedButton.icon(
            onPressed: _addNewBaby,
            icon: CustomIconWidget(
              iconName: 'add',
              color: Colors.white,
              size: 5.w,
            ),
            label: Text('Add Baby Profile'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBabyCard(BabyProfile baby) {
    return Card(
      margin: EdgeInsets.only(bottom: 3.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: () => _showBabyDetails(baby),
        onLongPress: () => _showBabyOptions(baby),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Row(
            children: [
              // Baby Avatar
              BabyProfilePhotoVariants.medium(
                photoUrl: baby.photo,
                babyName: baby.name,
                gender: baby.gender,
                onTap: () => _showBabyDetails(baby),
              ),
              
              SizedBox(width: 4.w),
              
              // Baby Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      baby.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      _calculateAge(baby.birthDate),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      'Born: ${_formatBirthDate(baby.birthDate)}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
              
              // Action Buttons
              Column(
                children: [
                  IconButton(
                    onPressed: () => _editBaby(baby),
                    icon: CustomIconWidget(
                      iconName: 'edit',
                      color: Theme.of(context).colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  IconButton(
                    onPressed: () => _showBabyOptions(baby),
                    icon: CustomIconWidget(
                      iconName: 'more_vert',
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      size: 20,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
} 