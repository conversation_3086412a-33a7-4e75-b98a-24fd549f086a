import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/baby_profile_state_manager.dart';
import './widgets/add_baby_fab_widget.dart';
import './widgets/baby_card_widget.dart';
import './widgets/baby_search_widget.dart';

class BabySelectorScreen extends StatefulWidget {
  final String? initialBabyId;

  const BabySelectorScreen({super.key, this.initialBabyId});

  @override
  State<BabySelectorScreen> createState() => _BabySelectorScreenState();
}

class _BabySelectorScreenState extends State<BabySelectorScreen>
    with TickerProviderStateMixin {
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  final PageController _pageController = PageController();

  List<BabyProfile> _babies = [];
  List<BabyProfile> _filteredBabies = [];
  BabyProfile? _selectedBaby;
  bool _isLoading = true;
  String _searchQuery = '';
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadBabies();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadBabies() async {
    try {
      setState(() => _isLoading = true);

      // Use centralized baby profile state manager
      if (!_babyProfileManager.hasBabies) {
        await _babyProfileManager.initialize();
      }
      
      final babies = _babyProfileManager.allBabies;
      debugPrint('🔄 Loaded babies from state manager: ${babies.length}');

      setState(() {
        _babies = babies;
        _filteredBabies = babies;
        
        // Set initial selected baby
        if (widget.initialBabyId != null && babies.isNotEmpty) {
          _selectedBaby = babies.firstWhere(
            (baby) => baby.id == widget.initialBabyId,
            orElse: () => babies.first,
          );
          debugPrint('🔄 Setting initial baby: ${_selectedBaby?.name} (ID: ${_selectedBaby?.id})');
        } else if (babies.isNotEmpty) {
          _selectedBaby = _babyProfileManager.activeBaby ?? babies.first;
          debugPrint('🔄 Setting active baby as default: ${_selectedBaby?.name} (ID: ${_selectedBaby?.id})');
        }
        
        _isLoading = false;
      });

      _animationController.forward();

      // Auto-scroll to selected baby
      if (_selectedBaby != null && _filteredBabies.isNotEmpty) {
        final index = _filteredBabies.indexOf(_selectedBaby!);
        if (index != -1) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _pageController.animateToPage(
              index,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeInOut,
            );
          });
        }
      }
    } catch (e) {
      debugPrint('❌ Error loading babies: $e');
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load babies: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleSearch(String query) {
    setState(() {
      _searchQuery = query;
      if (query.isEmpty) {
        _filteredBabies = _babies;
      } else {
        _filteredBabies = _babies
            .where(
                (baby) => baby.name.toLowerCase().contains(query.toLowerCase()))
            .toList();
      }
    });
  }

  void _selectBaby(BabyProfile baby) {
    print('🔄 Selecting baby: ${baby.name} (ID: ${baby.id})');
    HapticFeedback.selectionClick();
    setState(() => _selectedBaby = baby);

    // Animate to selected baby
    final index = _filteredBabies.indexOf(baby);
    if (index != -1) {
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _confirmSelection() async {
    if (_selectedBaby != null) {
      debugPrint('✅ Confirming baby selection: ${_selectedBaby!.name} (ID: ${_selectedBaby!.id})');
      HapticFeedback.lightImpact();
      
      try {
        // Update the active baby in the centralized state manager
        await _babyProfileManager.setActiveBaby(_selectedBaby!);
        debugPrint('✅ Baby set as active in state manager');
        Navigator.pop(context, _selectedBaby);
      } catch (e) {
        debugPrint('❌ Error setting active baby: $e');
        // Still return the selected baby even if setting active fails
        Navigator.pop(context, _selectedBaby);
      }
    }
  }

  void _navigateToAddBaby() {
    Navigator.pushNamed(context, '/baby-profile-creation').then((result) {
      if (result == true) {
        // Refresh the centralized state manager
        _babyProfileManager.refresh().then((_) {
          _loadBabies(); // Reload babies after adding new one
        });
      }
    });
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CustomIconWidget(
            iconName: 'child_care',
            size: 20.w,
            color: Theme.of(context).colorScheme.outline,
          ),
          SizedBox(height: 3.h),
          Text(
            _searchQuery.isEmpty ? 'No babies added yet' : 'No babies found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
          SizedBox(height: 1.h),
          Text(
            _searchQuery.isEmpty
                ? 'Add your first baby profile to get started'
                : 'Try a different search term',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.outline,
                ),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            SizedBox(height: 4.h),
            ElevatedButton.icon(
              onPressed: _navigateToAddBaby,
              icon: CustomIconWidget(
                iconName: 'add',
                color: Colors.white,
                size: 5.w,
              ),
              label: Text('Add Baby Profile'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.lightTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
              ),
            ),
          ],
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: Text(
          'Select Baby',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        actions: [
          if (_selectedBaby != null)
            TextButton(
              onPressed: _confirmSelection,
              child: Text(
                'Select',
                style: TextStyle(
                  color: AppTheme.lightTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 16.sp,
                ),
              ),
            ),
        ],
      ),
      body: _isLoading
          ? Center(
              child: CircularProgressIndicator(
                color: AppTheme.lightTheme.primaryColor,
              ),
            )
          : Column(
              children: [
                // Search Bar
                BabySearchWidget(
                  onSearch: _handleSearch,
                  totalBabies: _babies.length,
                ),

                // Baby Cards Carousel
                Expanded(
                  child: _filteredBabies.isEmpty
                      ? _buildEmptyState()
                      : FadeTransition(
                          opacity: _fadeAnimation,
                          child: PageView.builder(
                            controller: _pageController,
                            itemCount: _filteredBabies.length,
                            onPageChanged: (index) {
                              _selectBaby(_filteredBabies[index]);
                            },
                            itemBuilder: (context, index) {
                              final baby = _filteredBabies[index];
                              final isSelected = _selectedBaby?.id == baby.id;

                              return BabyCardWidget(
                                baby: baby,
                                isSelected: isSelected,
                                onTap: () => _selectBaby(baby),
                                onLongPress: () => _showBabyOptions(baby),
                              );
                            },
                          ),
                        ),
                ),

                // Page Indicators
                if (_filteredBabies.length > 1)
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(
                        _filteredBabies.length,
                        (index) {
                          final isActive = _selectedBaby != null &&
                              _filteredBabies.indexOf(_selectedBaby!) == index;
                          return Container(
                            margin: EdgeInsets.symmetric(horizontal: 1.w),
                            width: isActive ? 8.w : 2.w,
                            height: 1.h,
                            decoration: BoxDecoration(
                              color: isActive
                                  ? AppTheme.lightTheme.primaryColor
                                  : AppTheme.lightTheme.primaryColor
                                      .withValues(alpha: 0.3),
                              borderRadius: BorderRadius.circular(1.h),
                            ),
                          );
                        },
                      ),
                    ),
                  ),
              ],
            ),
      floatingActionButton: AddBabyFabWidget(
        onPressed: _navigateToAddBaby,
      ),
    );
  }

  void _showBabyOptions(BabyProfile baby) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12.w,
              height: 0.5.h,
              margin: EdgeInsets.symmetric(vertical: 2.h),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'edit',
                color: AppTheme.lightTheme.primaryColor,
              ),
              title: Text('Edit Profile'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/baby-profile-creation');
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'download',
                color: AppTheme.getSuccessColor(true),
              ),
              title: Text('Export Data'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement data export
              },
            ),
            ListTile(
              leading: CustomIconWidget(
                iconName: 'archive',
                color: Colors.orange,
              ),
              title: Text('Archive Baby'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Implement archiving
              },
            ),
            SizedBox(height: 2.h),
          ],
        ),
      ),
    );
  }
}
