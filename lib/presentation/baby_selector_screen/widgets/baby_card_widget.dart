import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/baby_profile_photo_widget.dart';

class BabyCardWidget extends StatefulWidget {
  final BabyProfile baby;
  final bool isSelected;
  final VoidCallback onTap;
  final VoidCallback onLongPress;

  const BabyCardWidget({
    super.key,
    required this.baby,
    required this.isSelected,
    required this.onTap,
    required this.onLongPress,
  });

  @override
  State<BabyCardWidget> createState() => _BabyCardWidgetState();
}

class _BabyCardWidgetState extends State<BabyCardWidget>
    with TickerProviderStateMixin {
  final SupabaseService _supabaseService = SupabaseService();
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  Future<void> _loadTodayStats() async {
    // Removed today's stats loading since we're showing birth date and notes instead
  }

  String _calculateAge() {
    final now = DateTime.now();
    final birthDate = widget.baby.birthDate;
    final difference = now.difference(birthDate);

    if (difference.inDays < 30) {
      if (difference.inDays == 0) {
        return 'Born today';
      } else if (difference.inDays == 1) {
        return '1 day old';
      } else {
        return '${difference.inDays} days old';
      }
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      if (months == 1) {
        return '1 month old';
      } else {
        return '$months months old';
      }
    } else {
      final years = (difference.inDays / 365).floor();
      final remainingMonths = ((difference.inDays % 365) / 30).floor();
      
      if (years == 1) {
        if (remainingMonths == 0) {
          return '1 year old';
        } else if (remainingMonths == 1) {
          return '1 year, 1 month old';
        } else {
          return '1 year, $remainingMonths months old';
        }
      } else {
        if (remainingMonths == 0) {
          return '$years years old';
        } else if (remainingMonths == 1) {
          return '$years years, 1 month old';
        } else {
          return '$years years, $remainingMonths months old';
        }
      }
    }
  }

  String _formatBirthDate() {
    final birthDate = widget.baby.birthDate;
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${birthDate.day} ${months[birthDate.month - 1]} ${birthDate.year}';
  }

  Color _getStatusColor() {
    return widget.isSelected
        ? AppTheme.lightTheme.primaryColor
        : Colors.blue;
  }

  String _getStatusText() {
    return widget.isSelected ? 'Selected' : 'Available';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: GestureDetector(
          onTap: () {
            HapticFeedback.selectionClick();
            widget.onTap();
          },
          onLongPress: () {
            HapticFeedback.heavyImpact();
            widget.onLongPress();
          },
          onTapDown: (_) => _scaleController.forward(),
          onTapUp: (_) => _scaleController.reverse(),
          onTapCancel: () => _scaleController.reverse(),
          child: AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(24),
              border: Border.all(
                color: widget.isSelected
                    ? AppTheme.lightTheme.primaryColor
                    : Colors.transparent,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: widget.isSelected
                      ? AppTheme.lightTheme.primaryColor.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.1),
                  blurRadius: widget.isSelected ? 20 : 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Baby Photo
                Stack(
                  children: [
                    BabyProfilePhotoWidget(
                      photoUrl: widget.baby.photo,
                      size: 35.w,
                      showBorder: true,
                      borderColor: widget.isSelected
                          ? AppTheme.lightTheme.primaryColor
                          : Colors.grey.withValues(alpha: 0.3),
                      borderWidth: 4,
                      babyName: widget.baby.name,
                      gender: widget.baby.gender,
                      onTap: () {
                        HapticFeedback.selectionClick();
                        widget.onTap();
                      },
                    ),
                    // Status Indicator
                    Positioned(
                      bottom: 2.w,
                      right: 2.w,
                      child: Container(
                        padding: EdgeInsets.symmetric(
                          horizontal: 2.w,
                          vertical: 0.5.h,
                        ),
                        decoration: BoxDecoration(
                          color: _getStatusColor(),
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              blurRadius: 4,
                            ),
                          ],
                        ),
                        child: Text(
                          _getStatusText(),
                          style:
                              Theme.of(context).textTheme.labelSmall?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 10.sp,
                                  ),
                        ),
                      ),
                    ),
                  ],
                ),

                SizedBox(height: 4.h),

                // Baby Name
                Text(
                  widget.baby.name,
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: widget.isSelected
                            ? AppTheme.lightTheme.primaryColor
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                  textAlign: TextAlign.center,
                ),

                SizedBox(height: 1.h),

                // Age
                Text(
                  _calculateAge(),
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                        fontWeight: FontWeight.w500,
                      ),
                  textAlign: TextAlign.center,
                ),

                SizedBox(height: 4.h),

                // Baby Profile Information
                Container(
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(
                    color: widget.isSelected
                        ? AppTheme.lightTheme.primaryColor
                            .withValues(alpha: 0.1)
                        : Theme.of(context).scaffoldBackgroundColor,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Birth Date
                      Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'cake',
                            color: widget.isSelected
                                ? AppTheme.lightTheme.primaryColor
                                : Theme.of(context).colorScheme.onSurfaceVariant,
                            size: 4.w,
                          ),
                          SizedBox(width: 3.w),
                          Expanded(
                            child: Text(
                              'Born: ${_formatBirthDate()}',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: widget.isSelected
                                    ? AppTheme.lightTheme.primaryColor
                                    : Theme.of(context).colorScheme.onSurface,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      SizedBox(height: 2.h),
                      
                      // Gender indicator
                      Row(
                        children: [
                          CustomIconWidget(
                            iconName: widget.baby.gender.toLowerCase() == 'boy' 
                                ? 'male' 
                                : widget.baby.gender.toLowerCase() == 'girl'
                                    ? 'female'
                                    : 'person',
                            color: widget.baby.gender.toLowerCase() == 'boy'
                                ? Colors.blue
                                : widget.baby.gender.toLowerCase() == 'girl'
                                    ? Colors.pink
                                    : Colors.grey,
                            size: 4.w,
                          ),
                          SizedBox(width: 3.w),
                          Expanded(
                            child: Text(
                              widget.baby.gender.toLowerCase() == 'boy' 
                                  ? 'Baby Boy'
                                  : widget.baby.gender.toLowerCase() == 'girl'
                                      ? 'Baby Girl'
                                      : 'Baby',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: widget.baby.gender.toLowerCase() == 'boy'
                                    ? Colors.blue
                                    : widget.baby.gender.toLowerCase() == 'girl'
                                        ? Colors.pink
                                        : Colors.grey,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      // Notes (if available)
                      if (widget.baby.note != null && widget.baby.note!.isNotEmpty) ...[
                        SizedBox(height: 2.h),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomIconWidget(
                              iconName: 'note',
                              color: widget.isSelected
                                  ? AppTheme.lightTheme.primaryColor
                                  : Theme.of(context).colorScheme.onSurfaceVariant,
                              size: 4.w,
                            ),
                            SizedBox(width: 3.w),
                            Expanded(
                              child: Text(
                                widget.baby.note!,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                  fontStyle: FontStyle.italic,
                                ),
                                maxLines: 3,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
