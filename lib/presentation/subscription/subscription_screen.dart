import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../models/subscription_info.dart';
import '../../models/enums.dart';
import '../../theme/app_theme.dart';
import '../../widgets/custom_elevated_button.dart';
import 'controllers/subscription_controller.dart';
import 'widgets/subscription_plan_card.dart';
import 'widgets/feature_comparison_widget.dart';
import 'widgets/subscription_benefits_widget.dart';
import 'widgets/payment_options_widget.dart';
import 'widgets/subscription_header_widget.dart';

/// Comprehensive subscription screen with Free and Premium plan cards
/// 
/// Features:
/// - Clean, professional design following app theme
/// - Detailed feature comparison
/// - Payment options with annual discount
/// - Clear upgrade flow
/// - Accessibility compliant
class SubscriptionScreen extends StatefulWidget {
  final bool showBackButton;
  final String? initialFocus; // 'free', 'premium', or null
  
  const SubscriptionScreen({
    super.key,
    this.showBackButton = true,
    this.initialFocus,
  });

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen>
    with TickerProviderStateMixin {
  late SubscriptionController _controller;
  late TabController _tabController;
  bool _isAnnual = true; // Default to annual billing
  
  @override
  void initState() {
    super.initState();
    _controller = SubscriptionController();
    _tabController = TabController(length: 2, vsync: this);
    
    // Set initial focus if specified
    if (widget.initialFocus == 'premium') {
      _tabController.index = 1;
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: _buildAppBar(context),
      body: SafeArea(
        child: CustomScrollView(
          slivers: [
            // Header with title and subtitle
            SliverToBoxAdapter(
              child: SubscriptionHeaderWidget(
                onBillingToggle: (isAnnual) {
                  setState(() {
                    _isAnnual = isAnnual;
                  });
                },
                isAnnual: _isAnnual,
              ),
            ),
            
            // Subscription plans
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                child: _buildSubscriptionPlans(context),
              ),
            ),
            
            // Feature comparison
            const SliverToBoxAdapter(
              child: FeatureComparisonWidget(),
            ),
            
            // Benefits section
            const SliverToBoxAdapter(
              child: SubscriptionBenefitsWidget(),
            ),
            
            // Bottom padding
            const SliverToBoxAdapter(
              child: SizedBox(height: 32),
            ),
          ],
        ),
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    final theme = Theme.of(context);
    
    return AppBar(
      title: Text(
        'Choose Your Plan',
        style: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.onSurface,
        ),
      ),
      backgroundColor: theme.colorScheme.surface,
      elevation: 0,
      leading: widget.showBackButton
          ? IconButton(
              icon: Icon(Icons.arrow_back_ios, color: theme.colorScheme.primary),
              onPressed: () => Navigator.of(context).pop(),
              tooltip: 'Back',
            )
          : null,
      actions: [
        TextButton(
          onPressed: () => _handleRestorePurchases(context),
          child: Text(
            'Restore',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSubscriptionPlans(BuildContext context) {
    return Column(
      children: [
        // Free Plan
        SubscriptionPlanCard(
          planName: 'Free',
          price: '\$0',
          period: 'Forever',
          originalPrice: null,
          features: [
            'Basic activity tracking',
            'Basic scheduled activities',
            'Up to 1 baby profile'
          ],
          isCurrentPlan: _controller.currentSubscription.status == SubscriptionStatus.free,
          isPremium: false,
          onTap: () => _handlePlanSelection(context, 'free'),
        ),
        
        const SizedBox(height: 16),
        
        // Premium Plan
        SubscriptionPlanCard(
          planName: 'Premium',
          price: _isAnnual ? '\$9.99' : '\$14.99',
          period: _isAnnual ? 'per month' : 'monthly',
          originalPrice: _isAnnual ? '\$14.99' : null,
          features: [
            'Unlimited activity tracking',
            'Unlimited sheduled activities',
            'Unlimited baby profiles',
            'Family sharing with up to 10 members',
            'WHO Growth Charts',
            'AI insights',
            'Ask AI chat',
            'Data export',
            'Priority customer support',
            'Custom notifications',
          ],
          isCurrentPlan: _controller.currentSubscription.status.isPremium,
          isPremium: true,
          isPopular: true,
          isAnnual: _isAnnual,
          discountPercentage: _isAnnual ? 33 : null,
          onTap: () => _handlePlanSelection(context, 'premium'),
        ),
        
        const SizedBox(height: 24),
        
        // Terms and conditions
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Text(
            'Subscription automatically renews unless auto-renew is turned off at least 24 hours before the end of the current period.',
            style: GoogleFonts.inter(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  void _handlePlanSelection(BuildContext context, String planType) async {
    if (planType == 'free') {
      // If already on free plan, no action needed
      if (_controller.currentSubscription.status == SubscriptionStatus.free) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('You are already on the Free plan'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        );
        return;
      }
      
      // Show downgrade confirmation
      _showDowngradeConfirmation(context);
    } else if (planType == 'premium') {
      if (_controller.currentSubscription.status.isPremium) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('You already have Premium access'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        );
        return;
      }
      
      // Start premium upgrade flow
      await _startPremiumUpgrade(context);
    }
  }

  void _showDowngradeConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Downgrade to Free?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('You will lose access to:'),
            const SizedBox(height: 8),
            Text('• AI insights and chat'),
            Text('• WHO Growth Charts'),
            Text('• Family sharing'),
            Text('• Unlimited baby profiles'),
            Text('• Advanced features'),
            const SizedBox(height: 16),
            Text(
              'Your data will be preserved, but some features will be limited.',
              style: TextStyle(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _processDowngrade(context);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Downgrade'),
          ),
        ],
      ),
    );
  }

  Future<void> _startPremiumUpgrade(BuildContext context) async {
    try {
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      // Start purchase flow
      final success = await _controller.purchasePremium(_isAnnual);
      
      Navigator.pop(context); // Close loading dialog
      
      if (success) {
        _showSuccessDialog(context);
      } else {
        _showErrorDialog(context, 'Purchase failed. Please try again.');
      }
    } catch (e) {
      Navigator.pop(context); // Close loading dialog
      _showErrorDialog(context, 'An error occurred: ${e.toString()}');
    }
  }

  void _processDowngrade(BuildContext context) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => Center(
          child: CircularProgressIndicator(),
        ),
      );

      final success = await _controller.cancelSubscription();
      
      Navigator.pop(context); // Close loading dialog
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Subscription cancelled. You now have the Free plan.'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        );
        setState(() {}); // Refresh UI
      } else {
        _showErrorDialog(context, 'Failed to cancel subscription. Please contact support.');
      }
    } catch (e) {
      Navigator.pop(context);
      _showErrorDialog(context, 'An error occurred: ${e.toString()}');
    }
  }

  void _handlePayment(BuildContext context, String paymentMethod) async {
    // This would integrate with your payment provider (Stripe, RevenueCat, etc.)
    await _startPremiumUpgrade(context);
  }

  void _handleRestorePurchases(BuildContext context) async {
    try {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              const SizedBox(width: 16),
              Text('Restoring purchases...'),
            ],
          ),
        ),
      );

      final restored = await _controller.restorePurchases();
      
      Navigator.pop(context); // Close loading dialog
      
      if (restored) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Purchases restored successfully!'),
            backgroundColor: Theme.of(context).colorScheme.secondary,
          ),
        );
        setState(() {}); // Refresh UI
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No purchases found to restore.'),
            backgroundColor: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        );
      }
    } catch (e) {
      Navigator.pop(context);
      _showErrorDialog(context, 'Failed to restore purchases: ${e.toString()}');
    }
  }

  void _showSuccessDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.check_circle,
          color: Theme.of(context).colorScheme.secondary,
          size: 48,
        ),
        title: Text('Welcome to Premium!'),
        content: Text(
          'You now have access to all premium features. Enjoy unlimited tracking, AI insights, and family sharing!',
        ),
        actions: [
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              Navigator.pop(context); // Close subscription screen
            },
            child: Text('Get Started'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        icon: Icon(
          Icons.error_outline,
          color: Theme.of(context).colorScheme.error,
          size: 48,
        ),
        title: Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }
}
