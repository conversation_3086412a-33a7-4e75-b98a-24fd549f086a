import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../models/subscription_info.dart';
import '../../../models/enums.dart';

import '../../../services/supabase_service.dart';
import '../../../services/feature_mapping_service.dart';
import '../services/subscription_service.dart';

/// Configuration for subscription features and pricing
class SubscriptionConfig {
  static const List<String> premiumFeatures = [
    'Unlimited activity tracking',
    'Unlimited Schedule plan',
    'Unlimited baby profiles',
    'Full family sharing',
    'Up to 10 family members',
    'Full WHO Growth Charts',
    'Full AI insights',
    'Unlimited Ask AI chat',
    'Data export & backup',
    'Priority customer support',
    'Advanced analytics',
    'Custom notifications',
  ];
  
  static const int premiumFamilyMembers = 10;
  static const double monthlyPrice = 14.90;
  static const double annualPrice = 9.90;
  
  static const String monthlyProductId = 'babytracker_premium_monthly';
  static const String annualProductId = 'babytracker_premium_annual';
}

/// Subscription-specific error types
enum SubscriptionError {
  purchaseFailed,
  networkError,
  userCancelled,
  invalidProduct,
  restoreFailed,
  loadFailed,
  cancelFailed,
}

extension SubscriptionErrorExtension on SubscriptionError {
  String get message {
    switch (this) {
      case SubscriptionError.purchaseFailed:
        return 'Unable to complete purchase. Please try again.';
      case SubscriptionError.networkError:
        return 'Network error. Please check your connection.';
      case SubscriptionError.userCancelled:
        return 'Purchase was cancelled.';
      case SubscriptionError.invalidProduct:
        return 'Invalid subscription product.';
      case SubscriptionError.restoreFailed:
        return 'Unable to restore purchases. Please try again.';
      case SubscriptionError.loadFailed:
        return 'Failed to load subscription information.';
      case SubscriptionError.cancelFailed:
        return 'Failed to cancel subscription.';
    }
  }
}

/// Handles subscription purchase operations
class SubscriptionPurchaseManager {
  final SubscriptionService _subscriptionService;
  final SupabaseService _supabaseService;
  
  SubscriptionPurchaseManager(this._subscriptionService, this._supabaseService);
  
  Future<PurchaseResult> purchasePremium(bool isAnnual) async {
    final productId = isAnnual ? SubscriptionConfig.annualProductId : SubscriptionConfig.monthlyProductId;
    return await _subscriptionService.purchaseSubscription(productId);
  }
  
  Future<bool> cancelSubscription(String planId) async {
    return await _subscriptionService.cancelSubscription(planId);
  }
  
  Future<List<SubscriptionInfo>> restorePurchases() async {
    return await _subscriptionService.restorePurchases();
  }
}

/// Controller for managing subscription state and purchase flow
class SubscriptionController extends ChangeNotifier {
  final SubscriptionPurchaseManager _purchaseManager;
  final SupabaseService _supabaseService;
  
  SubscriptionInfo _currentSubscription = SubscriptionPlans.free;
  bool _isLoading = false;
  SubscriptionError? _error;

  /// Constructor with dependency injection for better testability
  SubscriptionController({
    SubscriptionService? subscriptionService,
    SupabaseService? supabaseService,
  }) : _supabaseService = supabaseService ?? SupabaseService(),
       _purchaseManager = SubscriptionPurchaseManager(
         subscriptionService ?? SubscriptionService(),
         supabaseService ?? SupabaseService(),
       );

  /// Factory constructor that ensures service instance reuse
  factory SubscriptionController.withSharedServices({
    SubscriptionService? subscriptionService,
    SupabaseService? supabaseService,
  }) {
    final sharedSupabaseService = supabaseService ?? SupabaseService();
    final sharedSubscriptionService = subscriptionService ?? SubscriptionService();
    
    return SubscriptionController._internal(
      supabaseService: sharedSupabaseService,
      purchaseManager: SubscriptionPurchaseManager(
        sharedSubscriptionService,
        sharedSupabaseService,
      ),
    );
  }

  /// Internal constructor for factory
  SubscriptionController._internal({
    required SupabaseService supabaseService,
    required SubscriptionPurchaseManager purchaseManager,
  }) : _supabaseService = supabaseService,
       _purchaseManager = purchaseManager;

  /// Current user's subscription information
  SubscriptionInfo get currentSubscription => _currentSubscription;
  
  /// Whether a subscription operation is in progress
  bool get isLoading => _isLoading;
  
  /// Current error message, if any
  String? get error => _error?.message;

  /// Whether the user has premium access
  bool get hasPremiumAccess => _currentSubscription.status.isPremium;
  
  /// Whether the user is on a free plan
  bool get isOnFreePlan => _currentSubscription.status == SubscriptionStatus.free;

  /// Initialize the controller and load current subscription
  Future<void> initialize() async {
    await loadCurrentSubscription();
  }

  /// Load the user's current subscription information
  Future<void> loadCurrentSubscription() async {
    await _executeWithLoadingState(() async {
      // Get subscription from Supabase or local storage
      final subscriptionData = await _supabaseService.getCurrentUserSubscription();
      
      if (subscriptionData != null) {
        _currentSubscription = SubscriptionInfo.fromJson(subscriptionData);
      } else {
        // Default to free plan if no subscription found
        _currentSubscription = SubscriptionPlans.free;
      }
    }, SubscriptionError.loadFailed);
  }

  /// Purchase premium subscription
  /// 
  /// Initiates the purchase flow for a premium subscription plan.
  /// This method handles the entire purchase process including validation,
  /// payment processing, and subscription state updates.
  /// 
  /// [isAnnual] determines whether to purchase annual (true) or monthly (false) plan.
  /// 
  /// Returns `true` if the purchase was successful, `false` otherwise.
  /// 
  /// Throws [StateError] if a purchase is already in progress.
  /// 
  /// Example:
  /// ```dart
  /// final success = await controller.purchasePremium(true); // Annual plan
  /// if (success) {
  ///   // Handle successful purchase
  /// } else {
  ///   // Handle purchase failure - check controller.error for details
  /// }
  /// ```
  Future<bool> purchasePremium(bool isAnnual) async {
    if (_isLoading) {
      throw StateError('Purchase already in progress');
    }

    return await _executeWithLoadingState(() async {
      final purchaseResult = await _processPurchase(isAnnual);
      
      if (purchaseResult.success) {
        await _updateSubscriptionAfterPurchase(purchaseResult, isAnnual);
        return true;
      } else {
        _setError(SubscriptionError.purchaseFailed);
        return false;
      }
    }, SubscriptionError.purchaseFailed) ?? false;
  }

  /// Cancel current subscription
  Future<bool> cancelSubscription() async {
    if (_isLoading) {
      throw StateError('Cancellation already in progress');
    }

    return await _executeWithLoadingState(() async {
      final success = await _purchaseManager.cancelSubscription(_currentSubscription.planId);
      
      if (success) {
        _currentSubscription = SubscriptionPlans.free.copyWith(
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
        return true;
      } else {
        _setError(SubscriptionError.cancelFailed);
        return false;
      }
    }, SubscriptionError.cancelFailed) ?? false;
  }

  /// Restore previous purchases
  Future<bool> restorePurchases() async {
    return await _executeWithLoadingState(() async {
      final restoredSubscriptions = await _purchaseManager.restorePurchases();
      
      if (restoredSubscriptions.isNotEmpty) {
        // Find the most recent active subscription
        final activeSubscriptions = restoredSubscriptions
            .where((sub) => sub.status == SubscriptionStatus.active)
            .toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
        
        if (activeSubscriptions.isNotEmpty) {
          _currentSubscription = activeSubscriptions.first;
          await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
          return true;
        }
      }
      
      return false;
    }, SubscriptionError.restoreFailed) ?? false;
  }

  /// Check if a specific feature is available based on subscription plan
  /// @deprecated Use FeatureAccessService.hasFeatureAccess() instead for consistent feature checking
  @Deprecated('Use FeatureAccessService.hasFeatureAccess() instead')
  bool hasFeature(String featureName) {
    // Delegate to the authoritative feature access service
    final feature = FeatureMappingService.stringToFeature(featureName);
    if (feature == null) {
      debugPrint('Warning: Unknown feature name: $featureName');
      return false;
    }
    
    // This should be replaced with proper dependency injection
    // For now, use the mapping service as a bridge
    return FeatureMappingService.checkFeatureAccess(
      feature,
      _currentSubscription.status,
      includesAiInsights: _currentSubscription.includesAiInsights,
      includesDataExport: _currentSubscription.includesDataExport,
      includesPremiumSupport: _currentSubscription.includesPremiumSupport,
      maxFamilyMembers: _currentSubscription.maxFamilyMembers,
    );
  }

  /// Get feature limit for a specific feature
  int? getFeatureLimit(String featureName) {
    final isPremium = _currentSubscription.status.isPremium;
    
    switch (featureName) {
      case 'baby_profiles':
        return isPremium ? null : 1; // null = unlimited, 1 for free
      case 'family_members':
        return isPremium ? 10 : 1; // 10 for premium, 1 for free
      case 'scheduled_activities':
        return isPremium ? null : 5; // unlimited for premium, 5 for free
      case 'data_exports_per_month':
        return isPremium ? null : 0; // unlimited for premium, none for free
      case 'ai_chat_messages_per_day':
        return isPremium ? null : 0; // unlimited for premium, none for free
      case 'storage_gb':
        return _currentSubscription.storageLimit; // from subscription data
      default:
        return isPremium ? null : 0; // unlimited for premium, none for free
    }
  }

  /// Check if user can access a specific screen/feature
  bool canAccessScreen(String screenName) {
    switch (screenName) {
      case 'ai_insights':
      case 'ai_insights_dashboard':
        return hasFeature('ai_insights');
      case 'ai_chat':
      case 'ask_ai':
        return hasFeature('ai_chat');
      case 'growth_charts':
      case 'who_charts':
        return hasFeature('who_growth_charts');
      case 'family_sharing':
      case 'family_management':
        return hasFeature('family_sharing');
      case 'data_export':
        return hasFeature('data_export');
      case 'premium_settings':
        return hasPremiumAccess;
      default:
        return true; // Default to accessible for unknown screens
    }
  }

  /// Get user-friendly restriction message for a feature
  String getRestrictionMessage(String featureName) {
    if (hasFeature(featureName)) return '';
    
    switch (featureName) {
      case 'ai_insights':
        return 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.';
      case 'ai_chat':
      case 'ask_ai':
        return 'AI Chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.';
      case 'who_growth_charts':
      case 'growth_charts':
        return 'WHO Growth Charts require Premium plan. Upgrade to track your baby\'s growth with official WHO percentile charts.';
      case 'family_sharing':
        return 'Family Sharing requires Premium plan. Upgrade to invite up to 10 family members.';
      case 'unlimited_profiles':
      case 'multiple_baby_profiles':
        return 'Multiple baby profiles require Premium plan. Free plan is limited to 1 baby profile.';
      case 'data_export':
        return 'Data Export requires Premium plan. Upgrade to export your data for healthcare providers.';
      case 'unlimited_scheduled_activities':
        return 'Unlimited scheduled activities require Premium plan. Free plan is limited to 5 scheduled activities.';
      default:
        return 'This feature requires Premium plan. Upgrade to unlock all features.';
    }
  }

  /// Get remaining days for trial or subscription
  int? getRemainingDays() {
    if (_currentSubscription.isTrialActive) {
      return _currentSubscription.trialDaysRemaining;
    }
    return _currentSubscription.daysUntilRenewal;
  }

  /// Check if subscription needs attention (expired, cancelled, etc.)
  bool needsAttention() {
    return _currentSubscription.needsAttention;
  }

  /// Get subscription status message
  String getStatusMessage() {
    return _currentSubscription.statusMessage;
  }

  /// Get upgrade message if applicable
  String? getUpgradeMessage() {
    return _currentSubscription.upgradeMessage;
  }

  /// Update state and notify listeners in a single operation
  void _updateStateAndNotify(VoidCallback updateFunction) {
    updateFunction();
    notifyListeners();
  }

  void _setLoading(bool loading) {
    _updateStateAndNotify(() => _isLoading = loading);
  }

  void _setError(SubscriptionError error) {
    _updateStateAndNotify(() => _error = error);
  }

  void _clearError() {
    _error = null;
  }

  // Private helper methods

  /// Execute an operation with loading state management and error handling
  Future<T?> _executeWithLoadingState<T>(
    Future<T> Function() operation,
    SubscriptionError errorType,
  ) async {
    _setLoading(true);
    _clearError();
    
    try {
      final result = await operation();
      return result;
    } catch (e) {
      _setError(errorType);
      return null;
    } finally {
      _setLoading(false);
    }
  }

  /// Process the actual purchase with the subscription service
  Future<PurchaseResult> _processPurchase(bool isAnnual) async {
    return await _purchaseManager.purchasePremium(isAnnual);
  }

  /// Update subscription state after successful purchase
  Future<void> _updateSubscriptionAfterPurchase(PurchaseResult result, bool isAnnual) async {
    _currentSubscription = _createPremiumSubscription(result, isAnnual);
    await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
  }

  /// Create premium subscription info after successful purchase
  SubscriptionInfo _createPremiumSubscription(PurchaseResult result, bool isAnnual) {
    return SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.active,
      renewalDate: DateTime.now().add(Duration(days: isAnnual ? 365 : 30)),
      monthlyPrice: isAnnual ? SubscriptionConfig.annualPrice : SubscriptionConfig.monthlyPrice,
      features: SubscriptionConfig.premiumFeatures,
      isTrialActive: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      maxFamilyMembers: SubscriptionConfig.premiumFamilyMembers,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      paymentMethod: result.paymentMethod,
    );
  }


}

/// Result of a subscription purchase
class PurchaseResult {
  final bool success;
  final String? errorMessage;
  final PaymentMethod? paymentMethod;
  final String? transactionId;

  const PurchaseResult({
    required this.success,
    this.errorMessage,
    this.paymentMethod,
    this.transactionId,
  });
}
