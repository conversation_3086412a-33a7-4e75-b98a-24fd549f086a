import 'package:flutter/foundation.dart';
import '../../../models/subscription_info.dart';
import '../../../models/enums.dart';
import '../../../services/supabase_service.dart';
import '../services/subscription_service.dart';

/// Controller for managing subscription state and purchase flow
class SubscriptionController extends ChangeNotifier {
  final SubscriptionService _subscriptionService = SubscriptionService();
  final SupabaseService _supabaseService = SupabaseService();
  
  SubscriptionInfo _currentSubscription = SubscriptionPlans.free;
  bool _isLoading = false;
  String? _error;

  /// Current user's subscription information
  SubscriptionInfo get currentSubscription => _currentSubscription;
  
  /// Whether a subscription operation is in progress
  bool get isLoading => _isLoading;
  
  /// Current error message, if any
  String? get error => _error;

  /// Whether the user has premium access
  bool get hasPremiumAccess => _currentSubscription.isPremium;
  
  /// Whether the user is on a free plan
  bool get isOnFreePlan => _currentSubscription.status == SubscriptionStatus.free;

  /// Initialize the controller and load current subscription
  Future<void> initialize() async {
    debugPrint('DEBUG: SubscriptionController - Initializing...');
    await loadCurrentSubscription();
  }

  /// Load the user's current subscription information
  Future<void> loadCurrentSubscription() async {
    _setLoading(true);
    _clearError();
    
    try {
      debugPrint('DEBUG: SubscriptionController - Loading current subscription...');
      
      // Get subscription from Supabase or local storage
      final subscriptionData = await _supabaseService.getCurrentUserSubscription();
      
      if (subscriptionData != null) {
        debugPrint('DEBUG: SubscriptionController - Found subscription data: $subscriptionData');
        _currentSubscription = SubscriptionInfo.fromJson(subscriptionData);
        debugPrint('DEBUG: SubscriptionController - Parsed subscription - Plan: ${_currentSubscription.planName}, Status: ${_currentSubscription.status}, isPremium: ${_currentSubscription.isPremium}');
      } else {
        debugPrint('DEBUG: SubscriptionController - No subscription found, defaulting to free plan');
        _currentSubscription = SubscriptionPlans.free;
      }
      
      debugPrint('DEBUG: SubscriptionController - Final subscription state - Plan: ${_currentSubscription.planName}, Status: ${_currentSubscription.status.toString().split('.').last}, isPremium: ${hasPremiumAccess}');
      notifyListeners();
    } catch (e) {
      debugPrint('ERROR: SubscriptionController - Error loading subscription: $e');
      _setError('Failed to load subscription: ${e.toString()}');
    } finally {
      _setLoading(false);
    }
  }

  /// Purchase premium subscription
  Future<bool> purchasePremium(bool isAnnual) async {
    _setLoading(true);
    _clearError();
    
    try {
      // Start purchase flow with the subscription service
      final productId = isAnnual ? 'babytracker_premium_annual' : 'babytracker_premium_monthly';
      final purchaseResult = await _subscriptionService.purchaseSubscription(productId);
      
      if (purchaseResult.success) {
        // Update local subscription state
        _currentSubscription = SubscriptionInfo(
          planId: 'premium',
          planName: 'Premium',
          status: SubscriptionStatus.active,
          renewalDate: DateTime.now().add(Duration(days: isAnnual ? 365 : 30)),
          monthlyPrice: isAnnual ? 9.90 : 14.90,
          features: [
            'Unlimited activity tracking',
            'Unlimited Schedule plan',
            'Unlimited baby profiles',
            'Full family sharing',
            'Up to 10 family members',
            'Full WHO Growth Charts',
            'Full AI insights',
            'Unlimited Ask AI chat',
            'Data export & backup',
            'Priority customer support',
            'Advanced analytics',
            'Custom notifications',
          ],
          isTrialActive: false,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
          maxFamilyMembers: 10,
          includesAiInsights: true,
          includesDataExport: true,
          includesPremiumSupport: true,
          paymentMethod: purchaseResult.paymentMethod,
        );
        
        // Save to Supabase
        await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
        
        notifyListeners();
        return true;
      } else {
        _setError(purchaseResult.errorMessage ?? 'Purchase failed');
        return false;
      }
    } catch (e) {
      _setError('Purchase failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Cancel current subscription
  Future<bool> cancelSubscription() async {
    _setLoading(true);
    _clearError();
    
    try {
      // Cancel with the subscription service
      final success = await _subscriptionService.cancelSubscription(_currentSubscription.planId);
      
      if (success) {
        // Update to free plan
        _currentSubscription = SubscriptionPlans.free.copyWith(
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        // Update in Supabase
        await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
        
        notifyListeners();
        return true;
      } else {
        _setError('Failed to cancel subscription');
        return false;
      }
    } catch (e) {
      _setError('Cancellation failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Restore previous purchases
  Future<bool> restorePurchases() async {
    _setLoading(true);
    _clearError();
    
    try {
      final restoredSubscriptions = await _subscriptionService.restorePurchases();
      
      if (restoredSubscriptions.isNotEmpty) {
        // Use the most recent active subscription
        final activeSubscription = restoredSubscriptions
            .where((sub) => sub.status == SubscriptionStatus.active)
            .toList()
          ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
        
        if (activeSubscription.isNotEmpty) {
          _currentSubscription = activeSubscription.first;
          await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
          notifyListeners();
          return true;
        }
      }
      
      return false;
    } catch (e) {
      _setError('Restore failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Check if a specific feature is available
  bool hasFeature(String featureName) {
    switch (featureName) {
      case 'ai_insights':
        return _currentSubscription.includesAiInsights;
      case 'family_sharing':
        return _currentSubscription.maxFamilyMembers > 1;
      case 'who_charts':
        return _currentSubscription.isPremium;
      case 'unlimited_profiles':
        return _currentSubscription.isPremium;
      case 'data_export':
        return _currentSubscription.includesDataExport;
      case 'premium_support':
        return _currentSubscription.includesPremiumSupport;
      default:
        return false;
    }
  }

  /// Get feature limit for a specific feature
  int? getFeatureLimit(String featureName) {
    switch (featureName) {
      case 'baby_profiles':
        return _currentSubscription.isPremium ? null : 1; // null = unlimited
      case 'family_members':
        return _currentSubscription.maxFamilyMembers;
      case 'storage':
        return _currentSubscription.storageLimit; // null = unlimited
      default:
        return null;
    }
  }

  /// Get remaining days for trial or subscription
  int? getRemainingDays() {
    if (_currentSubscription.isTrialActive) {
      return _currentSubscription.trialDaysRemaining;
    }
    return _currentSubscription.daysUntilRenewal;
  }

  /// Check if subscription needs attention (expired, cancelled, etc.)
  bool needsAttention() {
    return _currentSubscription.needsAttention;
  }

  /// Get subscription status message
  String getStatusMessage() {
    return _currentSubscription.statusMessage;
  }

  /// Get upgrade message if applicable
  String? getUpgradeMessage() {
    return _currentSubscription.upgradeMessage;
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}

/// Result of a subscription purchase
class PurchaseResult {
  final bool success;
  final String? errorMessage;
  final PaymentMethod? paymentMethod;
  
  const PurchaseResult({
    required this.success,
    this.errorMessage,
    this.paymentMethod,
  });
}