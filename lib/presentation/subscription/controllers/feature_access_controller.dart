import 'package:flutter/material.dart';
import '../../../models/feature_access.dart';
import '../../../services/feature_access_service.dart';
import '../widgets/upgrade_prompt_widget.dart';

/// Controller for managing feature access UI interactions
/// 
/// This controller provides a clean interface between the UI and the
/// FeatureAccessService, handling upgrade prompts and user interactions.
class FeatureAccessController extends ChangeNotifier {
  final FeatureAccessService _featureAccessService;
  
  FeatureAccessController(this._featureAccessService) {
    _featureAccessService.addListener(_onFeatureAccessChanged);
  }
  
  /// Handle changes in feature access
  void _onFeatureAccessChanged() {
    notifyListeners();
  }
  
  /// Check if user has access to a feature
  bool canAccessFeature(AppFeature feature) {
    final hasAccess = _featureAccessService.hasFeatureAccess(feature);
    debugPrint('DEBUG: FeatureAccessController - canAccessFeature(${feature.name}) = $hasAccess');
    return hasAccess;
  }
  
  /// Get detailed feature access result
  FeatureAccessResult getFeatureAccessResult(AppFeature feature) {
    return _featureAccessService.checkFeatureAccess(feature);
  }
  
  /// Build upgrade prompt widget for a feature
  Widget buildUpgradePrompt(
    AppFeature feature, {
    VoidCallback? onUpgrade,
    VoidCallback? onDismiss,
  }) {
    final accessResult = _featureAccessService.checkFeatureAccess(feature);
    
    if (accessResult.hasAccess || accessResult.upgradePrompt == null) {
      return const SizedBox.shrink();
    }
    
    return UpgradePromptWidget(
      config: accessResult.upgradePrompt!,
      feature: feature,
      onUpgrade: onUpgrade ?? () => _handleUpgrade(feature),
      onDismiss: onDismiss ?? () => _handleDismiss(feature),
    );
  }
  
  /// Handle upgrade button tap
  void _handleUpgrade(AppFeature feature) {
    // Track upgrade interaction
    debugPrint('User wants to upgrade for feature: ${feature.displayName}');
    
    // Navigate to subscription screen
    // This would typically be handled by the calling widget
  }
  
  /// Handle dismiss button tap
  void _handleDismiss(AppFeature feature) {
    // Track dismiss interaction
    debugPrint('User dismissed upgrade prompt for feature: ${feature.displayName}');
  }
  
  /// Increment usage for a feature
  void incrementFeatureUsage(AppFeature feature) {
    _featureAccessService.incrementUsage(feature);
  }
  
  /// Get current usage for a feature
  int getCurrentUsage(AppFeature feature) {
    return _featureAccessService.getCurrentUsage(feature);
  }
  
  /// Get usage limit for a feature
  int? getFeatureLimit(AppFeature feature) {
    return _featureAccessService.getFeatureLimit(feature);
  }
  
  /// Check if user is near usage limit
  bool isNearUsageLimit(AppFeature feature) {
    return _featureAccessService.isNearUsageLimit(feature);
  }
  
  /// Get all current restrictions
  List<FeatureRestriction> getCurrentRestrictions() {
    return _featureAccessService.getCurrentRestrictions();
  }
  
  /// Get all current benefits
  List<FeatureBenefit> getCurrentBenefits() {
    return _featureAccessService.getCurrentBenefits();
  }
  
  /// Track feature access attempt (for analytics)
  void trackFeatureAccessAttempt(AppFeature feature) {
    debugPrint('Feature access attempt: ${feature.displayName}');
    // This could integrate with analytics service
  }
  
  @override
  void dispose() {
    _featureAccessService.removeListener(_onFeatureAccessChanged);
    super.dispose();
  }
}