import 'package:flutter/foundation.dart';
import '../../../models/subscription_info.dart';
import '../../../models/enums.dart';
import '../../../services/supabase_service.dart';
import '../services/subscription_service.dart';

/// Configuration for subscription features and pricing
class SubscriptionConfig {
  static const List<String> premiumFeatures = [
    'Unlimited activity tracking',
    'Unlimited Schedule plan',
    'Unlimited baby profiles',
    'Full family sharing',
    'Up to 10 family members',
    'Full WHO Growth Charts',
    'Full AI insights',
    'Unlimited Ask AI chat',
    'Data export & backup',
    'Priority customer support',
    'Advanced analytics',
    'Custom notifications',
  ];
  
  static const int premiumFamilyMembers = 10;
  static const double monthlyPrice = 14.90;
  static const double annualPrice = 9.90;
  
  static const String monthlyProductId = 'babytracker_premium_monthly';
  static const String annualProductId = 'babytracker_premium_annual';
}

/// Feature names for consistent access control
enum AppFeature {
  aiInsights,
  familySharing,
  whoCharts,
  unlimitedProfiles,
  dataExport,
  premiumSupport,
  babyProfiles,
  familyMembers,
  storage,
}

/// Subscription-specific error types
enum SubscriptionError {
  purchaseFailed,
  networkError,
  userCancelled,
  invalidProduct,
  restoreFailed,
  loadFailed,
  cancelFailed,
}

extension SubscriptionErrorExtension on SubscriptionError {
  String get message {
    switch (this) {
      case SubscriptionError.purchaseFailed:
        return 'Unable to complete purchase. Please try again.';
      case SubscriptionError.networkError:
        return 'Network error. Please check your connection.';
      case SubscriptionError.userCancelled:
        return 'Purchase was cancelled.';
      case SubscriptionError.invalidProduct:
        return 'Invalid subscription product.';
      case SubscriptionError.restoreFailed:
        return 'Unable to restore purchases. Please try again.';
      case SubscriptionError.loadFailed:
        return 'Failed to load subscription information.';
      case SubscriptionError.cancelFailed:
        return 'Failed to cancel subscription.';
    }
  }
}

/// Improved controller for managing subscription state and purchase flow
class SubscriptionController extends ChangeNotifier {
  final SubscriptionService _subscriptionService;
  final SupabaseService _supabaseService;
  
  SubscriptionInfo _currentSubscription = SubscriptionPlans.free;
  bool _isLoading = false;
  SubscriptionError? _error;

  /// Constructor with dependency injection for better testability
  SubscriptionController({
    SubscriptionService? subscriptionService,
    SupabaseService? supabaseService,
  }) : _subscriptionService = subscriptionService ?? SubscriptionService(),
       _supabaseService = supabaseService ?? SupabaseService();

  // Getters
  SubscriptionInfo get currentSubscription => _currentSubscription;
  bool get isLoading => _isLoading;
  SubscriptionError? get error => _error;
  String? get errorMessage => _error?.message;
  bool get hasPremiumAccess => _currentSubscription.status.isPremium;
  bool get isOnFreePlan => _currentSubscription.status == SubscriptionStatus.free;

  /// Initialize the controller and load current subscription
  Future<void> initialize() async {
    await loadCurrentSubscription();
  }

  /// Load the user's current subscription information
  Future<void> loadCurrentSubscription() async {
    await _executeWithLoadingState(() async {
      final subscriptionData = await _supabaseService.getCurrentUserSubscription();
      
      _currentSubscription = subscriptionData != null
          ? SubscriptionInfo.fromJson(subscriptionData)
          : SubscriptionPlans.free;
    }, SubscriptionError.loadFailed);
  }

  /// Purchase premium subscription
  /// 
  /// [isAnnual] determines whether to purchase annual (true) or monthly (false) plan.
  /// Returns `true` if the purchase was successful, `false` otherwise.
  Future<bool> purchasePremium(bool isAnnual) async {
    if (_isLoading) {
      throw StateError('Purchase already in progress');
    }

    return await _executeWithLoadingState(() async {
      final purchaseResult = await _processPurchase(isAnnual);
      
      if (purchaseResult.success) {
        await _updateSubscriptionAfterPurchase(purchaseResult, isAnnual);
        return true;
      } else {
        _setError(SubscriptionError.purchaseFailed);
        return false;
      }
    }, SubscriptionError.purchaseFailed) ?? false;
  }

  /// Cancel current subscription
  Future<bool> cancelSubscription() async {
    if (_isLoading) {
      throw StateError('Cancellation already in progress');
    }

    return await _executeWithLoadingState(() async {
      final success = await _subscriptionService.cancelSubscription(_currentSubscription.planId);
      
      if (success) {
        _currentSubscription = SubscriptionPlans.free.copyWith(
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        
        await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
        return true;
      } else {
        _setError(SubscriptionError.cancelFailed);
        return false;
      }
    }, SubscriptionError.cancelFailed) ?? false;
  }

  /// Restore previous purchases
  Future<bool> restorePurchases() async {
    return await _executeWithLoadingState(() async {
      final restoredSubscriptions = await _subscriptionService.restorePurchases();
      
      if (restoredSubscriptions.isNotEmpty) {
        final activeSubscription = _findMostRecentActiveSubscription(restoredSubscriptions);
        
        if (activeSubscription != null) {
          _currentSubscription = activeSubscription;
          await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
          return true;
        }
      }
      
      return false;
    }, SubscriptionError.restoreFailed) ?? false;
  }

  /// Check if a specific feature is available
  bool hasFeature(AppFeature feature) {
    switch (feature) {
      case AppFeature.aiInsights:
        return _currentSubscription.includesAiInsights;
      case AppFeature.familySharing:
        return _currentSubscription.maxFamilyMembers > 1;
      case AppFeature.whoCharts:
      case AppFeature.unlimitedProfiles:
        return _currentSubscription.status.isPremium;
      case AppFeature.dataExport:
        return _currentSubscription.includesDataExport;
      case AppFeature.premiumSupport:
        return _currentSubscription.includesPremiumSupport;
      case AppFeature.babyProfiles:
      case AppFeature.familyMembers:
      case AppFeature.storage:
        return true; // These have limits, not binary access
    }
  }

  /// Get feature limit for a specific feature
  int? getFeatureLimit(AppFeature feature) {
    switch (feature) {
      case AppFeature.babyProfiles:
        return _currentSubscription.status.isPremium ? null : 1;
      case AppFeature.familyMembers:
        return _currentSubscription.maxFamilyMembers;
      case AppFeature.storage:
        return _currentSubscription.storageLimit;
      default:
        return null;
    }
  }

  /// Get remaining days for trial or subscription
  int? getRemainingDays() {
    if (_currentSubscription.isTrialActive) {
      return _currentSubscription.trialDaysRemaining;
    }
    return _currentSubscription.daysUntilRenewal;
  }

  /// Check if subscription needs attention
  bool needsAttention() => _currentSubscription.needsAttention;

  /// Get subscription status message
  String getStatusMessage() => _currentSubscription.statusMessage;

  /// Get upgrade message if applicable
  String? getUpgradeMessage() => _currentSubscription.upgradeMessage;

  // Private helper methods

  /// Execute an operation with loading state management and error handling
  Future<T?> _executeWithLoadingState<T>(
    Future<T> Function() operation,
    SubscriptionError errorType,
  ) async {
    _setLoadingState(true);
    _clearError();
    
    try {
      final result = await operation();
      return result;
    } catch (e) {
      _setError(errorType);
      return null;
    } finally {
      _setLoadingState(false);
    }
  }

  /// Process the actual purchase with the subscription service
  Future<PurchaseResult> _processPurchase(bool isAnnual) async {
    final productId = isAnnual 
        ? SubscriptionConfig.annualProductId 
        : SubscriptionConfig.monthlyProductId;
    
    return await _subscriptionService.purchaseSubscription(productId);
  }

  /// Update subscription state after successful purchase
  Future<void> _updateSubscriptionAfterPurchase(PurchaseResult result, bool isAnnual) async {
    _currentSubscription = _createPremiumSubscription(result, isAnnual);
    await _supabaseService.updateUserSubscription(_currentSubscription.toJson());
  }

  /// Create premium subscription info after successful purchase
  SubscriptionInfo _createPremiumSubscription(PurchaseResult result, bool isAnnual) {
    return SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.active,
      renewalDate: DateTime.now().add(Duration(days: isAnnual ? 365 : 30)),
      monthlyPrice: isAnnual ? SubscriptionConfig.annualPrice : SubscriptionConfig.monthlyPrice,
      features: SubscriptionConfig.premiumFeatures,
      isTrialActive: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      maxFamilyMembers: SubscriptionConfig.premiumFamilyMembers,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      paymentMethod: result.paymentMethod,
    );
  }

  /// Find the most recent active subscription from restored purchases
  SubscriptionInfo? _findMostRecentActiveSubscription(List<SubscriptionInfo> subscriptions) {
    final activeSubscriptions = subscriptions
        .where((sub) => sub.status == SubscriptionStatus.active)
        .toList()
      ..sort((a, b) => b.createdAt.compareTo(a.createdAt));
    
    return activeSubscriptions.isNotEmpty ? activeSubscriptions.first : null;
  }

  /// Update state and notify listeners in a single operation
  void _updateStateAndNotify(VoidCallback updateFunction) {
    updateFunction();
    notifyListeners();
  }

  /// Set loading state and notify listeners
  void _setLoadingState(bool loading) {
    _updateStateAndNotify(() => _isLoading = loading);
  }

  /// Set error and notify listeners
  void _setError(SubscriptionError error) {
    _updateStateAndNotify(() => _error = error);
  }

  /// Clear error state
  void _clearError() {
    _error = null;
  }

  @override
  void dispose() {
    super.dispose();
  }
}