import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../models/feature_access.dart';
import '../../../models/subscription_info.dart';
import '../../../models/enums.dart';
import '../controllers/feature_access_controller.dart';
import '../controllers/subscription_controller.dart';

/// Widget that displays current subscription status and feature access overview
/// 
/// This widget provides a comprehensive view of the user's subscription status,
/// available features, and usage limits in a clean, professional interface.
class SubscriptionStatusWidget extends StatelessWidget {
  final bool showUpgradeButton;
  final bool showFeatureList;
  final bool compact;
  
  const SubscriptionStatusWidget({
    super.key,
    this.showUpgradeButton = true,
    this.showFeatureList = true,
    this.compact = false,
  });
  
  @override
  Widget build(BuildContext context) {
    return Consumer2<dynamic, FeatureAccessController>(
      builder: (context, subscriptionController, featureController, child) {
        final subscription = subscriptionController.currentSubscription;
        final theme = Theme.of(context);
        
        if (compact) {
          return _buildCompactView(context, subscription, featureController, theme);
        }
        
        return _buildFullView(context, subscription, featureController, theme);
      },
    );
  }
  
  Widget _buildCompactView(
    BuildContext context,
    SubscriptionInfo subscription,
    FeatureAccessController featureController,
    ThemeData theme,
  ) {
    final isPremium = subscription.status == SubscriptionStatus.active;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: isPremium 
                  ? theme.colorScheme.primaryContainer 
                  : theme.colorScheme.surfaceVariant,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                isPremium ? Icons.star : Icons.person,
                color: isPremium 
                  ? theme.colorScheme.onPrimaryContainer 
                  : theme.colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    subscription.planName,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    isPremium ? 'All features unlocked' : 'Limited features',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            if (!isPremium && showUpgradeButton)
              TextButton(
                onPressed: () => Navigator.of(context).pushNamed('/subscription'),
                child: const Text('Upgrade'),
              ),
          ],
        ),
      ),
    );
  }
  
  Widget _buildFullView(
    BuildContext context,
    SubscriptionInfo subscription,
    FeatureAccessController featureController,
    ThemeData theme,
  ) {
    final isPremium = subscription.status == SubscriptionStatus.active;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isPremium 
                      ? theme.colorScheme.primaryContainer 
                      : theme.colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isPremium ? Icons.star : Icons.person,
                    color: isPremium 
                      ? theme.colorScheme.onPrimaryContainer 
                      : theme.colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        subscription.planName,
                        style: GoogleFonts.inter(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (isPremium && subscription.renewalDate != null)
                        Text(
                          'Renews ${_formatDate(subscription.renewalDate!)}',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        )
                      else
                        Text(
                          'Limited features available',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                    ],
                  ),
                ),
                if (!isPremium)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.errorContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      'FREE',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onErrorContainer,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  )
                else
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.primaryContainer,
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Text(
                      'PREMIUM',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: theme.colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
              ],
            ),
            
            if (showFeatureList) ...[
              const SizedBox(height: 20),
              _buildFeatureOverview(context, featureController, theme, isPremium),
            ],
            
            if (!isPremium && showUpgradeButton) ...[
              const SizedBox(height: 20),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () => Navigator.of(context).pushNamed('/subscription'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: Text(
                    'Upgrade to Premium',
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.onPrimary,
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
  
  Widget _buildFeatureOverview(
    BuildContext context,
    FeatureAccessController featureController,
    ThemeData theme,
    bool isPremium,
  ) {
    final keyFeatures = [
      AppFeature.multipleBabyProfiles,
      AppFeature.familySharing,
      AppFeature.whoGrowthCharts,
      AppFeature.aiInsights,
      AppFeature.dataExport,
    ];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Features',
          style: GoogleFonts.inter(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        ...keyFeatures.map((feature) => _buildFeatureRow(
          context, featureController, theme, feature,
        )),
      ],
    );
  }
  
  Widget _buildFeatureRow(
    BuildContext context,
    FeatureAccessController featureController,
    ThemeData theme,
    AppFeature feature,
  ) {
    final hasAccess = featureController.canAccessFeature(feature);
    final currentUsage = featureController.getCurrentUsage(feature);
    final limit = featureController.getFeatureLimit(feature);
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            hasAccess ? Icons.check_circle : Icons.cancel,
            size: 16,
            color: hasAccess 
              ? theme.colorScheme.primary 
              : theme.colorScheme.outline,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              feature.displayName,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: hasAccess 
                  ? theme.colorScheme.onSurface 
                  : theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ),
          if (limit != null && hasAccess)
            Text(
              '$currentUsage/$limit',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w500,
              ),
            )
          else if (hasAccess)
            Text(
              'Unlimited',
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }
  
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = date.difference(now).inDays;
    
    if (difference == 0) {
      return 'today';
    } else if (difference == 1) {
      return 'tomorrow';
    } else if (difference < 7) {
      return 'in $difference days';
    } else {
      return 'on ${date.day}/${date.month}/${date.year}';
    }
  }
}