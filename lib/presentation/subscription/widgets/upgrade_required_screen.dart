import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../utils/subscription_access_control.dart';
import '../subscription_screen.dart';

/// Screen shown when user tries to access a premium feature without subscription
class UpgradeRequiredScreen extends StatelessWidget {
  final String featureName;
  final String title;
  final String? customMessage;
  final String? customDescription;
  final List<String>? benefits;
  final IconData? icon;

  const UpgradeRequiredScreen({
    super.key,
    required this.featureName,
    required this.title,
    this.customMessage,
    this.customDescription,
    this.benefits,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final message = customMessage ?? 
        SubscriptionAccessControl.getRestrictionMessage(context, featureName);
    
    final featureBenefits = benefits ?? _getDefaultBenefits(featureName);
    final featureIcon = icon ?? _getDefaultIcon(featureName);

    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: _buildAppBar(context, colorScheme),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              Expanded(
                child: _buildContent(context, colorScheme, message, featureBenefits, featureIcon),
              ),
              _buildActionButtons(context, colorScheme),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the app bar
  PreferredSizeWidget _buildAppBar(BuildContext context, ColorScheme colorScheme) {
    return AppBar(
      title: Text(
        title,
        style: GoogleFonts.inter(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: colorScheme.onSurface,
        ),
      ),
      backgroundColor: colorScheme.surface,
      elevation: 0,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios, color: colorScheme.primary),
        onPressed: () {
          // Check if we can pop safely
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          } else {
            // If we can't pop, navigate to the main navigation screen
            Navigator.of(context).pushReplacementNamed('/main-navigation');
          }
        },
      ),
    );
  }

  /// Build the main content area
  Widget _buildContent(
    BuildContext context,
    ColorScheme colorScheme,
    String message,
    List<String> featureBenefits,
    IconData featureIcon,
  ) {
    // Use SingleChildScrollView to prevent overflow
    return SingleChildScrollView(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SizedBox(height: 16),
          _buildFeatureIcon(colorScheme, featureIcon),
          const SizedBox(height: 24),
          _buildPremiumBadge(colorScheme),
          const SizedBox(height: 20),
          _buildTitle(colorScheme),
          const SizedBox(height: 16),
          _buildMessage(colorScheme, message),
          const SizedBox(height: 24),
          if (featureBenefits.isNotEmpty) ...[
            _buildBenefitsList(colorScheme, featureBenefits),
            const SizedBox(height: 24),
          ],
        ],
      ),
    );
  }

  /// Build the feature icon container
  Widget _buildFeatureIcon(ColorScheme colorScheme, IconData featureIcon) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary.withOpacity(0.1),
            colorScheme.secondary.withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(60),
        border: Border.all(
          color: colorScheme.primary.withOpacity(0.2),
          width: 2,
        ),
      ),
      child: Icon(
        featureIcon,
        size: 48,
        color: colorScheme.primary,
      ),
    );
  }

  /// Build the premium badge
  Widget _buildPremiumBadge(ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [colorScheme.primary, colorScheme.secondary],
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: colorScheme.onPrimary,
            size: 16,
          ),
          const SizedBox(width: 4),
          Text(
            'PREMIUM FEATURE',
            style: GoogleFonts.inter(
              fontSize: 12,
              fontWeight: FontWeight.w700,
              color: colorScheme.onPrimary,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Build the title
  Widget _buildTitle(ColorScheme colorScheme) {
    return Text(
      'Upgrade to Premium',
      style: GoogleFonts.inter(
        fontSize: 28,
        fontWeight: FontWeight.w700,
        color: colorScheme.onSurface,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Build the message
  Widget _buildMessage(ColorScheme colorScheme, String message) {
    return Text(
      message,
      style: GoogleFonts.inter(
        fontSize: 16,
        color: colorScheme.onSurfaceVariant,
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// Build the benefits list
  Widget _buildBenefitsList(ColorScheme colorScheme, List<String> featureBenefits) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'What you\'ll get:',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          ...featureBenefits.map((benefit) => _buildBenefitItem(colorScheme, benefit)),
        ],
      ),
    );
  }

  /// Build a single benefit item
  Widget _buildBenefitItem(ColorScheme colorScheme, String benefit) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              benefit,
              style: GoogleFonts.inter(
                fontSize: 14,
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the action buttons
  Widget _buildActionButtons(BuildContext context, ColorScheme colorScheme) {
    return Column(
      children: [
        _buildUpgradeButton(context, colorScheme),
        const SizedBox(height: 12),
        _buildMaybeLaterButton(context, colorScheme),
      ],
    );
  }

  /// Build the upgrade button
  Widget _buildUpgradeButton(BuildContext context, ColorScheme colorScheme) {
    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: () => _navigateToSubscription(context),
        style: ElevatedButton.styleFrom(
          backgroundColor: colorScheme.primary,
          foregroundColor: colorScheme.onPrimary,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.star, size: 20),
            const SizedBox(width: 8),
            Text(
              'Upgrade to Premium',
              style: GoogleFonts.inter(
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the maybe later button
  Widget _buildMaybeLaterButton(BuildContext context, ColorScheme colorScheme) {
    return SizedBox(
      width: double.infinity,
      height: 48,
      child: TextButton(
        onPressed: () {
          // Check if we can pop safely
          if (Navigator.of(context).canPop()) {
            Navigator.of(context).pop();
          } else {
            // If we can't pop, navigate to the main navigation screen
            Navigator.of(context).pushReplacementNamed('/main-navigation');
          }
        },
        style: TextButton.styleFrom(
          foregroundColor: colorScheme.onSurfaceVariant,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          'Maybe Later',
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// Navigate to subscription screen
  void _navigateToSubscription(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SubscriptionScreen(
          initialFocus: 'premium',
        ),
      ),
    );
  }

  List<String> _getDefaultBenefits(String featureName) {
    switch (featureName) {
      case 'ai_insights':
        return [
          'AI-powered pattern analysis',
          'Personalized recommendations',
          'Development milestone predictions',
          'Sleep and feeding insights',
        ];
      case 'ai_chat':
        return [
          '24/7 AI chat assistant',
          'Personalized parenting advice',
          'Evidence-based recommendations',
          'Unlimited conversations',
        ];
      case 'who_growth_charts':
        return [
          'Official WHO growth standards',
          'Percentile tracking and trends',
          'Growth milestone alerts',
          'Pediatrician-ready reports',
        ];
      case 'family_sharing':
        return [
          'Invite up to 10 family members',
          'Real-time activity sharing',
          'Collaborative care tracking',
          'Role-based permissions',
        ];
      case 'unlimited_profiles':
        return [
          'Unlimited baby profiles',
          'Individual tracking for each child',
          'Family-wide insights and analytics',
          'Shared access with family members',
        ];
      case 'data_export':
        return [
          'Unlimited data exports',
          'PDF and CSV formats',
          'Healthcare provider reports',
          'Complete activity history',
        ];
      default:
        return [
          'Access to all premium features',
          'Unlimited usage',
          'Priority customer support',
          'Regular feature updates',
        ];
    }
  }

  IconData _getDefaultIcon(String featureName) {
    switch (featureName) {
      case 'ai_insights':
        return Icons.psychology; // Brain icon for AI insights
      case 'ai_chat':
        return Icons.smart_toy; // Robot icon for AI chat
      case 'who_growth_charts':
        return Icons.show_chart; // Chart icon for growth charts
      case 'family_sharing':
        return Icons.family_restroom; // Family icon
      case 'unlimited_profiles':
        return Icons.face; // Face icon for baby profiles
      case 'data_export':
        return Icons.ios_share; // Share icon for data export
      case 'custom_notifications':
        return Icons.notifications_active; // Notifications icon
      case 'premium_support':
        return Icons.support_agent; // Support icon
      default:
        return Icons.workspace_premium; // Premium icon as default
    }
  }
}