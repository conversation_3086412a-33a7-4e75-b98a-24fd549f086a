import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Widget showcasing benefits of premium subscription
class SubscriptionBenefitsWidget extends StatelessWidget {
  const SubscriptionBenefitsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Why Choose Premium?',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ...benefits.map((benefit) => _buildBenefitCard(context, benefit)),
          
          const SizedBox(height: 16),
          
          _buildTrustIndicators(context),
        ],
      ),
    );
  }

  Widget _buildBenefitCard(BuildContext context, Benefit benefit) {
    final theme = Theme.of(context);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant.withOpacity(0.5),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withOpacity(0.1),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              benefit.icon,
              color: theme.colorScheme.primary,
              size: 24,
            ),
          ),
          
          const SizedBox(width: 16),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  benefit.title,
                  style: GoogleFonts.inter(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  benefit.description,
                  style: GoogleFonts.inter(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTrustIndicators(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          const SizedBox(height: 16),
          
          Text(
            'Unlock Premium features with confidence!',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTrustItem(BuildContext context, IconData icon, String text) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Icon(
          icon,
          color: theme.colorScheme.secondary,
          size: 28,
        ),
        
        const SizedBox(height: 8),
        
        Text(
          text,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  static const List<Benefit> benefits = [
    Benefit(
      icon: Icons.auto_graph,
      title: 'AI-Powered Insights',
      description: 'Get personalized recommendations and insights based on your baby\'s unique patterns and development.',
    ),
    Benefit(
      icon: Icons.family_restroom,
      title: 'Family Collaboration',
      description: 'Share tracking duties with up to 10 family members. Everyone stays connected and informed.',
    ),
    Benefit(
      icon: Icons.analytics,
      title: 'WHO Growth Charts',
      description: 'Track your baby\'s growth against World Health Organization standards with detailed percentile analysis.',
    ),
    Benefit(
      icon: Icons.chat_bubble_outline,
      title: 'Ask AI Assistant',
      description: 'Get instant answers to parenting questions with unlimited access to our AI chat assistant.',
    ),
    Benefit(
      icon: Icons.cloud_download,
      title: 'Data Export & Backup',
      description: 'Export your data anytime and keep secure backups of all your precious baby memories.',
    ),
    Benefit(
      icon: Icons.support_agent,
      title: 'Priority Support',
      description: 'Get fast, dedicated customer support when you need help or have questions.',
    ),
  ];
}

class Benefit {
  final IconData icon;
  final String title;
  final String description;

  const Benefit({
    required this.icon,
    required this.title,
    required this.description,
  });
}
