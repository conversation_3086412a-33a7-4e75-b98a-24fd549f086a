import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';
import '../../../theme/app_theme.dart';
import '../subscription_screen.dart';

/// A reusable premium feature card that can be placed in various screens
/// to indicate premium features and provide an upgrade path
class PremiumFeatureCard extends StatelessWidget {
  /// The title of the premium feature
  final String title;
  
  /// A short description of the premium feature
  final String description;
  
  /// The icon to display for this feature
  final IconData icon;
  
  /// Custom action when the card is tapped
  final VoidCallback? onTap;
  
  /// Whether to use a compact layout
  final bool compact;
  
  /// Custom background color
  final Color? backgroundColor;
  
  /// Custom text color
  final Color? textColor;
  
  /// Custom icon color
  final Color? iconColor;

  const PremiumFeatureCard({
    super.key,
    required this.title,
    required this.description,
    required this.icon,
    this.onTap,
    this.compact = false,
    this.backgroundColor,
    this.textColor,
    this.iconColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Consistent orange colors for both light and dark themes
    final bgColor = backgroundColor ?? 
        (isDark 
            ? Colors.orange.shade900.withAlpha(100)
            : Colors.orange.shade50);
    
    final txtColor = textColor ?? 
        (isDark 
            ? Colors.orange.shade100
            : Colors.grey.shade800);
    
    final icnColor = iconColor ?? Colors.orange.shade600;

    return Card(
      elevation: 0,
      color: Colors.transparent,
      margin: EdgeInsets.symmetric(
        horizontal: compact ? 8 : 16,
        vertical: compact ? 4 : 8,
      ),
      child: InkWell(
        onTap: onTap ?? () => _showUpgradeDialog(context),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: compact ? 12 : 16,
            vertical: compact ? 8 : 12,
          ),
          decoration: BoxDecoration(
            color: bgColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isDark ? Colors.orange.shade700 : Colors.orange.shade200,
              width: 1.5,
            ),
          ),
          child: Row(
            children: [
              // Premium icon
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isDark ? Colors.orange.shade800.withAlpha(150) : Colors.orange.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: icnColor,
                  size: compact ? 16 : 20,
                ),
              ),
              SizedBox(width: compact ? 8 : 12),
              
              // Text content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.orange.shade600,
                          size: compact ? 12 : 14,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'PREMIUM',
                          style: GoogleFonts.inter(
                            fontSize: compact ? 10 : 11,
                            fontWeight: FontWeight.w700,
                            color: Colors.orange.shade600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: compact ? 2 : 4),
                    Text(
                      title,
                      style: GoogleFonts.inter(
                        fontSize: compact ? 13 : 14,
                        fontWeight: FontWeight.w600,
                        color: txtColor,
                      ),
                    ),
                    if (!compact) SizedBox(height: 2),
                    Text(
                      description,
                      style: GoogleFonts.inter(
                        fontSize: compact ? 11 : 12,
                        color: txtColor.withAlpha(200),
                      ),
                      maxLines: compact ? 1 : 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              
              // Upgrade button
              TextButton(
                onPressed: onTap ?? () => _showUpgradeDialog(context),
                style: TextButton.styleFrom(
                  backgroundColor: Colors.orange.shade600,
                  padding: EdgeInsets.symmetric(
                    horizontal: compact ? 8 : 12,
                    vertical: compact ? 4 : 6,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: Text(
                  'Upgrade',
                  style: GoogleFonts.inter(
                    fontSize: compact ? 11 : 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const SubscriptionScreen(
          initialFocus: 'premium',
        ),
      ),
    );
  }
}