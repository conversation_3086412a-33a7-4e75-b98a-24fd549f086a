import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../models/feature_access.dart';

/// Widget that displays upgrade prompts for restricted features
/// 
/// This widget provides a consistent interface for showing upgrade prompts
/// across the application, with customizable styling and actions.
class UpgradePromptWidget extends StatelessWidget {
  final UpgradePromptConfig config;
  final AppFeature feature;
  final VoidCallback? onUpgrade;
  final VoidCallback? onDismiss;
  final PromptStyle? style;
  
  const UpgradePromptWidget({
    super.key,
    required this.config,
    required this.feature,
    this.onUpgrade,
    this.onDismiss,
    this.style,
  });
  
  @override
  Widget build(BuildContext context) {
    final promptStyle = style ?? config.style ?? PromptStyle.card;
    
    switch (promptStyle) {
      case PromptStyle.dialog:
        return _buildDialog(context);
      case PromptStyle.bottomSheet:
        return _buildBottomSheet(context);
      case PromptStyle.banner:
        return _buildBanner(context);
      case PromptStyle.card:
        return _buildCard(context);
      case PromptStyle.standard:
        return _buildStandard(context);
    }
  }
  
  Widget _buildDialog(BuildContext context) {
    return AlertDialog(
      title: Text(config.title),
      content: _buildContent(context),
      actions: _buildActions(context),
    );
  }
  
  Widget _buildBottomSheet(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          _buildContent(context),
          const SizedBox(height: 24),
          _buildActionButtons(context),
        ],
      ),
    );
  }
  
  Widget _buildBanner(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            feature.icon,
            color: theme.colorScheme.onPrimaryContainer,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  config.title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  config.description,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onPrimaryContainer,
                  ),
                ),
              ],
            ),
          ),
          TextButton(
            onPressed: onUpgrade,
            child: Text(
              config.ctaText ?? 'Upgrade',
              style: TextStyle(
                color: theme.colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context),
            const SizedBox(height: 16),
            _buildContent(context),
            const SizedBox(height: 20),
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }
  
  Widget _buildStandard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildHeader(context),
          const SizedBox(height: 16),
          _buildContent(context),
          const SizedBox(height: 20),
          _buildActionButtons(context),
        ],
      ),
    );
  }
  
  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: theme.colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            feature.icon,
            color: theme.colorScheme.onPrimaryContainer,
            size: 24,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Text(
            config.title,
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
      ],
    );
  }
  
  Widget _buildContent(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          config.description,
          style: GoogleFonts.inter(
            fontSize: 16,
            color: theme.colorScheme.onSurfaceVariant,
            height: 1.5,
          ),
        ),
        
        if (config.benefits.isNotEmpty) ...[
          const SizedBox(height: 16),
          Text(
            'What you\'ll get:',
            style: GoogleFonts.inter(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          ...config.benefits.map((benefit) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    benefit,
                    style: GoogleFonts.inter(
                      fontSize: 14,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ],
    );
  }
  
  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        if (onDismiss != null)
          Expanded(
            child: OutlinedButton(
              onPressed: onDismiss,
              child: const Text('Maybe Later'),
            ),
          ),
        if (onDismiss != null) const SizedBox(width: 12),
        Expanded(
          flex: onDismiss != null ? 1 : 2,
          child: ElevatedButton(
            onPressed: onUpgrade,
            style: ElevatedButton.styleFrom(
              backgroundColor: config.accentColor ?? Theme.of(context).colorScheme.primary,
            ),
            child: Text(config.ctaText ?? 'Upgrade Now'),
          ),
        ),
      ],
    );
  }
  
  List<Widget> _buildActions(BuildContext context) {
    return [
      if (onDismiss != null)
        TextButton(
          onPressed: onDismiss,
          child: const Text('Maybe Later'),
        ),
      ElevatedButton(
        onPressed: onUpgrade,
        child: Text(config.ctaText ?? 'Upgrade Now'),
      ),
    ];
  }
}

/// Helper function to show upgrade prompt as dialog
Future<void> showUpgradeDialog(
  BuildContext context, {
  required AppFeature feature,
  required UpgradePromptConfig config,
  VoidCallback? onUpgrade,
}) {
  return showDialog(
    context: context,
    builder: (context) => UpgradePromptWidget(
      config: config,
      feature: feature,
      style: PromptStyle.dialog,
      onUpgrade: () {
        Navigator.of(context).pop();
        onUpgrade?.call();
      },
      onDismiss: () => Navigator.of(context).pop(),
    ),
  );
}

/// Helper function to show upgrade prompt as bottom sheet
Future<void> showUpgradeBottomSheet(
  BuildContext context, {
  required AppFeature feature,
  required UpgradePromptConfig config,
  VoidCallback? onUpgrade,
}) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    builder: (context) => UpgradePromptWidget(
      config: config,
      feature: feature,
      style: PromptStyle.bottomSheet,
      onUpgrade: () {
        Navigator.of(context).pop();
        onUpgrade?.call();
      },
      onDismiss: () => Navigator.of(context).pop(),
    ),
  );
}