import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Header widget for the subscription screen
/// 
/// Displays title, subtitle, and billing period toggle
class SubscriptionHeaderWidget extends StatelessWidget {
  final Function(bool) onBillingToggle;
  final bool isAnnual;

  const SubscriptionHeaderWidget({
    super.key,
    required this.onBillingToggle,
    required this.isAnnual,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            'Unlock Premium Features',
            style: GoogleFonts.inter(
              fontSize: 28,
              fontWeight: FontWeight.w700,
              color: theme.colorScheme.onSurface,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 12),
          
          Text(
            'Choose the plan that works best for your family\'s needs',
            style: GoogleFonts.inter(
              fontSize: 16,
              fontWeight: FontWeight.w400,
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          
          const SizedBox(height: 24),
          
          _buildBillingToggle(context),
        ],
      ),
    );
  }

  Widget _buildBillingToggle(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(4),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildToggleOption(
            context,
            'Monthly',
            !isAnnual,
            () => onBillingToggle(false),
          ),
          _buildToggleOption(
            context,
            'Annual (Save 33%)',
            isAnnual,
            () => onBillingToggle(true),
          ),
        ],
      ),
    );
  }

  Widget _buildToggleOption(
    BuildContext context,
    String text,
    bool isSelected,
    VoidCallback onTap,
  ) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? theme.colorScheme.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          text,
          style: GoogleFonts.inter(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isSelected
                ? theme.colorScheme.onPrimary
                : theme.colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }
}
