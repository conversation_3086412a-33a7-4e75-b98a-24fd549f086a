import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/custom_elevated_button.dart';

/// Widget representing a subscription plan card
/// 
/// Displays plan name, price, features, and actions for selection
class SubscriptionPlanCard extends StatelessWidget {
  final String planName;
  final String price;
  final String period; // monthly, per year, etc.
  final String? originalPrice;
  final List<String> features;
  final bool isCurrentPlan;
  final bool isPremium;
  final bool isPopular;
  final bool isAnnual;
  final int? discountPercentage;
  final void Function()? onTap;

  const SubscriptionPlanCard({
    super.key,
    required this.planName,
    required this.price,
    required this.period,
    this.originalPrice,
    required this.features,
    this.isCurrentPlan = false,
    this.isPremium = false,
    this.isPopular = false,
    this.isAnnual = false,
    this.discountPercentage,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Card(
      color: isCurrentPlan ? colorScheme.primary.withOpacity(0.1) : colorScheme.surface,
      child: Padding(
        padding: EdgeInsets.all(isPopular ? 16.0 : 12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (isPopular)
              Align(
                alignment: Alignment.topRight,
                child: _buildPopularBadge(context),
              ),
              
            _buildHeader(context),
            
            const SizedBox(height: 8),
            
            _buildFeaturesList(context),
            
            const SizedBox(height: 12),
            
            _buildActionButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPopularBadge(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.secondary,
      ),
      child: Text(
        'Popular',
        style: GoogleFonts.inter(
          color: AppTheme.onSecondaryLight,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                planName,
                style: GoogleFonts.inter(
                  fontWeight: FontWeight.w700,
                  fontSize: 18,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    price,
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w500,
                      fontSize: 16,
                      color: theme.colorScheme.primary,
                      decoration: isCurrentPlan ? TextDecoration.lineThrough : TextDecoration.none,
                    ),
                  ),
                  if (isPremium && originalPrice != null && isAnnual)
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        originalPrice!,
                        style: GoogleFonts.inter(
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                          color: theme.colorScheme.onSurfaceVariant,
                          decoration: TextDecoration.lineThrough,
                        ),
                      ),
                    ),
                  const SizedBox(width: 6),
                  Text(
                    period,
                    style: GoogleFonts.inter(
                      fontWeight: FontWeight.w400,
                      fontSize: 12,
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
              // Add annual billing details for premium plan
              if (isPremium && isAnnual) ...[
                const SizedBox(height: 4),
                Text(
                  '(billed at \$119.88 annually)',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '14 days free trial',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.red,
                  ),
                ),
              ],
              // Add cancellation policy for all premium plans (but not if already showing free trial)
              if (isPremium && !isAnnual) ...[
                const SizedBox(height: 4),
                Text(
                  'Cancel anytime, hassle free',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
              ],
              // Also add cancellation policy for annual plans, but below the free trial
              if (isPremium && isAnnual) ...[
                const SizedBox(height: 2),
                Text(
                  'Cancel anytime, hassle free',
                  style: GoogleFonts.inter(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Colors.green,
                  ),
                ),
              ],
            ],
          ),
        ),
        
        if (discountPercentage != null)
          _buildDiscountBadge(context, discountPercentage!),
      ],
    );
  }

  Widget _buildDiscountBadge(BuildContext context, int discountPercentage) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: Theme.of(context).colorScheme.secondary,
      ),
      child: Text(
        '-$discountPercentage%',
        style: GoogleFonts.inter(
          color: AppTheme.onSecondaryLight,
          fontWeight: FontWeight.w600,
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildFeaturesList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: features.map((feature) => _buildFeatureRow(context, feature)).toList(),
    );
  }
  
  Widget _buildFeatureRow(BuildContext context, String feature) {
    // Determine if this feature is available based on the plan type
    bool isAvailable = _isFeatureAvailable(feature, isPremium);
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isAvailable 
                  ? Colors.green
                  : Colors.grey.shade300,
            ),
            child: Icon(
              isAvailable ? Icons.check : Icons.close,
              color: Colors.white,
              size: 12,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              feature,
              style: GoogleFonts.inter(
                fontSize: 14,
                fontWeight: FontWeight.w400,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }
  
  bool _isFeatureAvailable(String feature, bool isPremiumPlan) {
    if (isPremiumPlan) {
      return true; // All features available for premium
    }
    
    // For free plan, only certain features are available
    final freeFeatures = [
      'Basic activity tracking',
      'Basic scheduled activities',
      'Up to 1 baby profile'
    ];
    
    return freeFeatures.any((freeFeature) => 
        feature.toLowerCase().contains(freeFeature.toLowerCase()));
  }

  Widget _buildActionButton(BuildContext context) {
    return CustomElevatedButton(
      text: isCurrentPlan ? 'Current Plan' : 'Choose Plan',
      onPressed: onTap,
      isEnabled: !isCurrentPlan,
    );
  }
}
