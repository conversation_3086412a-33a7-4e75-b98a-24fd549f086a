import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

/// Widget for comparing features between Free and Premium plans
class FeatureComparisonWidget extends StatelessWidget {
  const FeatureComparisonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Feature Comparison',
            style: GoogleFonts.inter(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Table(
            border: TableBorder.all(
              color: theme.colorScheme.outline.withOpacity(0.3),
              width: 1,
            ),
            columnWidths: const {
              0: FlexColumnWidth(2),
              1: FlexColumnWidth(1),
              2: FlexColumnWidth(1),
            },
            children: [
              _buildTableHeader(context),
              ..._features.map((feature) => _buildFeatureRow(context, feature)),
            ],
          ),
        ],
      ),
    );
  }

  TableRow _buildTableHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return TableRow(
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceVariant,
      ),
      children: [
        _buildHeaderCell(context, 'Feature'),
        _buildHeaderCell(context, 'Free'),
        _buildHeaderCell(context, 'Premium'),
      ],
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  TableRow _buildFeatureRow(BuildContext context, FeatureComparison feature) {
    return TableRow(
      children: [
        _buildFeatureCell(context, feature.name),
        _buildStatusCell(context, feature.freeStatus),
        _buildStatusCell(context, feature.premiumStatus),
      ],
    );
  }

  Widget _buildFeatureCell(BuildContext context, String text) {
    return Container(
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: GoogleFonts.inter(
          fontSize: 14,
          fontWeight: FontWeight.w400,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildStatusCell(BuildContext context, FeatureStatus status) {
    final theme = Theme.of(context);
    
    Widget child;
    Color? color;
    
    switch (status.type) {
      case FeatureStatusType.checkmark:
        child = Icon(Icons.check, color: theme.colorScheme.secondary, size: 20);
        break;
      case FeatureStatusType.cross:
        child = Icon(Icons.close, color: theme.colorScheme.error, size: 20);
        break;
      case FeatureStatusType.text:
        child = Text(
          status.text!,
          style: GoogleFonts.inter(
            fontSize: 12,
            fontWeight: FontWeight.w400,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          textAlign: TextAlign.center,
        );
        break;
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      alignment: Alignment.center,
      child: child,
    );
  }

  static const List<FeatureComparison> _features = [
    FeatureComparison(
      name: 'Activity Tracking',
      freeStatus: FeatureStatus(type: FeatureStatusType.text, text: 'Basic'),
      premiumStatus: FeatureStatus(type: FeatureStatusType.text, text: 'Unlimited'),
    ),
    FeatureComparison(
      name: 'Sheduled Activities',
      freeStatus: FeatureStatus(type: FeatureStatusType.text, text: 'Basic'),
      premiumStatus: FeatureStatus(type: FeatureStatusType.text, text: 'Unlimited'),
    ),
    FeatureComparison(
      name: 'Baby Profiles',
      freeStatus: FeatureStatus(type: FeatureStatusType.text, text: '1'),
      premiumStatus: FeatureStatus(type: FeatureStatusType.text, text: 'Unlimited'),
    ),
    FeatureComparison(
      name: 'Family Sharing',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.text, text: '10 members'),
    ),
    FeatureComparison(
      name: 'WHO Growth Charts',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.checkmark),
    ),
    FeatureComparison(
      name: 'AI Insights',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.checkmark),
    ),
    FeatureComparison(
      name: 'Ask AI Chat',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.text, text: 'Unlimited'),
    ),
    FeatureComparison(
      name: 'Data Export',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.checkmark),
    ),
    FeatureComparison(
      name: 'Priority Support',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.checkmark),
    ),
    FeatureComparison(
      name: 'Advanced Analytics',
      freeStatus: FeatureStatus(type: FeatureStatusType.cross),
      premiumStatus: FeatureStatus(type: FeatureStatusType.checkmark),
    ),
  ];
}

class FeatureComparison {
  final String name;
  final FeatureStatus freeStatus;
  final FeatureStatus premiumStatus;

  const FeatureComparison({
    required this.name,
    required this.freeStatus,
    required this.premiumStatus,
  });
}

class FeatureStatus {
  final FeatureStatusType type;
  final String? text;

  const FeatureStatus({
    required this.type,
    this.text,
  });
}

enum FeatureStatusType {
  checkmark,
  cross,
  text,
}
