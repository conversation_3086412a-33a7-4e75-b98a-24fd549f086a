import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../models/feature_access.dart';
import '../controllers/feature_access_controller.dart';

/// Widget that gates content behind subscription requirements
/// 
/// This widget checks feature access and either shows the child content
/// or displays an upgrade prompt based on the user's subscription status.
class FeatureGate extends StatelessWidget {
  final AppFeature feature;
  final Widget child;
  final bool showUpgradePrompt;
  final VoidCallback? onUpgrade;
  final VoidCallback? onDismiss;
  final Widget? fallback;
  
  const FeatureGate({
    super.key,
    required this.feature,
    required this.child,
    this.showUpgradePrompt = true,
    this.onUpgrade,
    this.onDismiss,
    this.fallback,
  });
  
  @override
  Widget build(BuildContext context) {
    debugPrint('DEBUG: FeatureGate - Building gate for feature: ${feature.name}');
    
    return Consumer<FeatureAccessController>(
      builder: (context, controller, _) {
        debugPrint('DEBUG: FeatureGate - Consumer builder called for ${feature.name}');
        final hasAccess = controller.canAccessFeature(feature);
        
        debugPrint('DEBUG: FeatureGate - Feature ${feature.name} hasAccess: $hasAccess');
        
        if (hasAccess) {
          debugPrint('DEBUG: FeatureGate - Showing child content for ${feature.name}');
          // Track successful access
          controller.trackFeatureAccessAttempt(feature);
          return child;
        }
        
        debugPrint('DEBUG: FeatureGate - Access denied for ${feature.name}, showUpgradePrompt: $showUpgradePrompt');
        
        // User doesn't have access
        if (showUpgradePrompt) {
          debugPrint('DEBUG: FeatureGate - Building upgrade prompt for ${feature.name}');
          return controller.buildUpgradePrompt(
            feature,
            onUpgrade: onUpgrade,
            onDismiss: onDismiss,
          );
        }
        
        debugPrint('DEBUG: FeatureGate - Using fallback for ${feature.name}');
        // Return fallback or empty widget
        return fallback ?? const SizedBox.shrink();
      },
    );
  }
}

/// Widget that wraps content with usage limit checking
/// 
/// This widget is useful for features that have usage limits (like baby profiles
/// or data exports) and need to show warnings when approaching limits.
class UsageLimitGate extends StatelessWidget {
  final AppFeature feature;
  final Widget child;
  final Widget? nearLimitWarning;
  final Widget? limitReachedWidget;
  
  const UsageLimitGate({
    super.key,
    required this.feature,
    required this.child,
    this.nearLimitWarning,
    this.limitReachedWidget,
  });
  
  @override
  Widget build(BuildContext context) {
    return Consumer<FeatureAccessController>(
      builder: (context, controller, _) {
        final hasAccess = controller.canAccessFeature(feature);
        final isNearLimit = controller.isNearUsageLimit(feature);
        
        if (!hasAccess) {
          return limitReachedWidget ?? controller.buildUpgradePrompt(feature);
        }
        
        return Column(
          children: [
            if (isNearLimit && nearLimitWarning != null) nearLimitWarning!,
            child,
          ],
        );
      },
    );
  }
}

/// Widget that shows feature access status inline
/// 
/// This widget displays the current usage and limits for a feature,
/// useful in settings screens or feature overview pages.
class FeatureAccessStatus extends StatelessWidget {
  final AppFeature feature;
  final bool showUsage;
  final bool showUpgradeButton;
  
  const FeatureAccessStatus({
    super.key,
    required this.feature,
    this.showUsage = true,
    this.showUpgradeButton = true,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<FeatureAccessController>(
      builder: (context, controller, _) {
        final hasAccess = controller.canAccessFeature(feature);
        final currentUsage = controller.getCurrentUsage(feature);
        final limit = controller.getFeatureLimit(feature);
        
        return Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      feature.icon,
                      color: hasAccess ? theme.colorScheme.primary : theme.colorScheme.outline,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            feature.displayName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            feature.description,
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    _buildStatusChip(context, hasAccess),
                  ],
                ),
                
                if (showUsage && limit != null) ...[
                  const SizedBox(height: 12),
                  _buildUsageIndicator(context, currentUsage, limit),
                ],
                
                if (!hasAccess && showUpgradeButton) ...[
                  const SizedBox(height: 12),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // Navigate to subscription screen
                        Navigator.of(context).pushNamed('/subscription');
                      },
                      child: const Text('Upgrade to Unlock'),
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }
  
  Widget _buildStatusChip(BuildContext context, bool hasAccess) {
    final theme = Theme.of(context);
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: hasAccess 
          ? theme.colorScheme.primaryContainer 
          : theme.colorScheme.errorContainer,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        hasAccess ? 'Available' : 'Premium',
        style: theme.textTheme.labelSmall?.copyWith(
          color: hasAccess 
            ? theme.colorScheme.onPrimaryContainer 
            : theme.colorScheme.onErrorContainer,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
  
  Widget _buildUsageIndicator(BuildContext context, int current, int limit) {
    final theme = Theme.of(context);
    final progress = current / limit;
    final isNearLimit = progress >= 0.8;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Usage',
              style: theme.textTheme.labelMedium,
            ),
            Text(
              '$current / $limit',
              style: theme.textTheme.labelMedium?.copyWith(
                color: isNearLimit 
                  ? theme.colorScheme.error 
                  : theme.colorScheme.onSurfaceVariant,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: progress.clamp(0.0, 1.0),
          backgroundColor: theme.colorScheme.surfaceVariant,
          valueColor: AlwaysStoppedAnimation<Color>(
            isNearLimit ? theme.colorScheme.error : theme.colorScheme.primary,
          ),
        ),
      ],
    );
  }
}