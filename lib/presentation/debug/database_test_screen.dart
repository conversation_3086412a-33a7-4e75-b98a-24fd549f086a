import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../services/database_test_service.dart';
import '../../core/app_export.dart';

class DatabaseTestScreen extends StatefulWidget {
  const DatabaseTestScreen({super.key});

  @override
  State<DatabaseTestScreen> createState() => _DatabaseTestScreenState();
}

class _DatabaseTestScreenState extends State<DatabaseTestScreen> {
  Map<String, dynamic>? testResults;
  bool isRunning = false;
  String? databaseStatus;

  @override
  void initState() {
    super.initState();
    _checkDatabaseStatus();
  }

  Future<void> _checkDatabaseStatus() async {
    try {
      final status = await DatabaseTestService.getDatabaseStatus();
      setState(() {
        databaseStatus = status;
      });
    } catch (e) {
      setState(() {
        databaseStatus = '❌ Error checking database: $e';
      });
    }
  }

  Future<void> _runTests() async {
    setState(() {
      isRunning = true;
      testResults = null;
    });

    try {
      final results = await DatabaseTestService.runDatabaseTests();
      setState(() {
        testResults = results;
        isRunning = false;
      });
      await _checkDatabaseStatus();
    } catch (e) {
      setState(() {
        testResults = {
          'overallSuccess': false,
          'errors': ['Test execution failed: $e'],
          'tests': {},
        };
        isRunning = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Database Test'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runTests,
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Database Status Card
            _buildStatusCard(),
            SizedBox(height: 2.h),
            
            // Test Button
            _buildTestButton(),
            SizedBox(height: 2.h),
            
            // Test Results
            if (testResults != null) _buildTestResults(),
            
            // Instructions
            if (testResults == null || testResults!['overallSuccess'] != true)
              _buildInstructionsCard(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.storage,
                color: Theme.of(context).colorScheme.primary,
                size: 6.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Database Status',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          if (databaseStatus != null)
            Text(
              databaseStatus!,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontFamily: 'monospace',
              ),
            )
          else
            Row(
              children: [
                SizedBox(
                  width: 4.w,
                  height: 4.w,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                SizedBox(width: 2.w),
                Text('Checking database status...'),
              ],
            ),
        ],
      ),
    );
  }

  Widget _buildTestButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: isRunning ? null : _runTests,
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 2.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: isRunning
            ? Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 4.w,
                    height: 4.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Text('Running Tests...'),
                ],
              )
            : Text(
                'Run Database Tests',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildTestResults() {
    final results = testResults!;
    final overallSuccess = results['overallSuccess'] as bool;
    final tests = results['tests'] as Map<String, dynamic>;
    final errors = results['errors'] as List<String>;

    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: overallSuccess
              ? Colors.green.withValues(alpha: 0.3)
              : Colors.red.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                overallSuccess ? Icons.check_circle : Icons.error,
                color: overallSuccess ? Colors.green : Colors.red,
                size: 6.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Test Results',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          
          // Overall Status
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: overallSuccess
                  ? Colors.green.withValues(alpha: 0.1)
                  : Colors.red.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              overallSuccess
                  ? '✅ All tests passed! Database is properly configured.'
                  : '❌ Some tests failed. Database needs migration fixes.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          SizedBox(height: 2.h),
          
          // Individual Test Results
          ...tests.entries.map((entry) => _buildTestItem(entry.key, entry.value)),
          
          // Errors
          if (errors.isNotEmpty) ...[
            SizedBox(height: 2.h),
            Text(
              'Errors:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: Colors.red,
              ),
            ),
            SizedBox(height: 1.h),
            ...errors.map((error) => Padding(
                  padding: EdgeInsets.only(bottom: 0.5.h),
                  child: Text(
                    '• $error',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.red,
                      fontFamily: 'monospace',
                    ),
                  ),
                )),
          ],
        ],
      ),
    );
  }

  Widget _buildTestItem(String testName, dynamic testData) {
    if (testData is! Map<String, dynamic>) return const SizedBox.shrink();
    
    final success = testData['success'] as bool? ?? false;
    final message = testData['message'] as String? ?? 'No message';
    final skipped = testData['skipped'] as bool? ?? false;
    
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            skipped
                ? Icons.remove_circle_outline
                : success
                    ? Icons.check_circle_outline
                    : Icons.error_outline,
            color: skipped
                ? Colors.grey
                : success
                    ? Colors.green
                    : Colors.red,
            size: 5.w,
          ),
          SizedBox(width: 2.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _formatTestName(testName),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  message,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatTestName(String testName) {
    switch (testName) {
      case 'validActivityTypes':
        return 'Valid Activity Types';
      case 'temperatureValidation':
        return 'Temperature Validation';
      case 'enumValues':
        return 'Enum Values';
      case 'databaseSchema':
        return 'Database Schema';
      case 'activityLogging':
        return 'Activity Logging';
      default:
        return testName;
    }
  }

  Widget _buildInstructionsCard() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.blue.withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info,
                color: Colors.blue,
                size: 6.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Migration Instructions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Colors.blue,
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Text(
            'If tests are failing, you need to run the database migrations:',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          SizedBox(height: 1.h),
          Text(
            '1. Go to your Supabase dashboard\n'
            '2. Open the SQL Editor\n'
            '3. Run the migration files in order:\n'
            '   • 20241230160000_create_missing_enums.sql\n'
            '   • 20241230170000_add_activity_type_values.sql\n'
            '   • 20241230180000_create_activity_tables.sql\n'
            '   • 20241230190000_create_enhanced_functions.sql\n'
            '4. Run tests again to verify',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontFamily: 'monospace',
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'See database_setup_instructions.md for detailed steps.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontStyle: FontStyle.italic,
            ),
          ),
        ],
      ),
    );
  }
} 