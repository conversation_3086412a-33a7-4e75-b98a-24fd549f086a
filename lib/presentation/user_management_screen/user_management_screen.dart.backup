import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_icon_widget.dart';
import './widgets/family_member_card_widget.dart';

class UserManagementScreen extends StatefulWidget {
  const UserManagementScreen({super.key});

  @override
  State<UserManagementScreen> createState() => _UserManagementScreenState();
}

class _UserManagementScreenState extends State<UserManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  
  List<UserProfile> _familyMembers = [];
  List<Map<String, dynamic>> _pendingInvites = [];
  List<BabyProfile> _babies = [];
  
  bool _isLoading = true;
  bool _isAdmin = false;
  UserProfile? _currentUser;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this); // Removed Sessions tab
    _loadUserManagementData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadUserManagementData() async {
    setState(() => _isLoading = true);

    try {
      _currentUser = _authService.userProfile;
      
      // If we don't have a user profile loaded, try to create one from auth data
      if (_currentUser == null) {
        debugPrint('❌ No current user profile found in AuthService');
        
        // Try to get authenticated user data directly
        final supabase = await _supabaseService.client;
        final authUser = supabase.auth.currentUser;
        
        if (authUser != null) {
          debugPrint('✅ Found authenticated user: ${authUser.id}');
          debugPrint('📧 User email: ${authUser.email}');
          debugPrint('👤 User metadata: ${authUser.userMetadata}');
          
          // Create a temporary user profile from auth data
          _currentUser = UserProfile(
            id: authUser.id,
            userId: authUser.id,
            email: authUser.email ?? 'Unknown Email',
            fullName: authUser.userMetadata?['full_name'] ?? 'Unknown User',
            role: 'admin', // Default to admin since we know user was promoted
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          
          debugPrint('✅ Created fallback user profile: ${_currentUser!.fullName} (${_currentUser!.role})');
        } else {
          debugPrint('❌ No authenticated user found');
          return;
        }
      } else {
        debugPrint('✅ Current user loaded from AuthService: ${_currentUser!.fullName} (${_currentUser!.role})');
      }

      _isAdmin = _currentUser!.role == 'admin';

      // Load family members from Supabase with fallback
      await _loadFamilyMembers();
      
      // Load pending invitations with fallback
      await _loadPendingInvites();
      
      // Load babies for access management
      await _loadBabies();

    } catch (e) {
      debugPrint('❌ Error loading user management data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to load data: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _loadFamilyMembers() async {
    try {
      debugPrint('🔄 Loading family members...');
      
      final response = await _supabaseService.select(
        'user_profiles',
        orderBy: 'created_at',
        ascending: false,
      );

      debugPrint('📊 Raw user_profiles response: $response');

      _familyMembers = response
          .map((data) => UserProfile.fromJson(data))
          .toList();

      debugPrint('✅ Loaded ${_familyMembers.length} family members');
      for (var member in _familyMembers) {
        debugPrint('   - ${member.fullName} (${member.role})');
      }
    } catch (e) {
      debugPrint('❌ Error loading family members: $e');
      
      // If we can't load from database, at least include current user
      if (_currentUser != null) {
        _familyMembers = [_currentUser!];
        debugPrint('✅ Using current user as fallback family member');
      } else {
        _familyMembers = [];
      }
    }
  }

  Future<void> _loadPendingInvites() async {
    try {
      debugPrint('🔄 Loading pending invitations...');
      final response = await _supabaseService.select(
        'invitations',
        filters: {'status': 'pending'},
        orderBy: 'created_at',
        ascending: false,
      );

      debugPrint('📊 Raw invitations response: $response');
      _pendingInvites = response;
      debugPrint('✅ Loaded ${_pendingInvites.length} pending invitations');
    } catch (e) {
      debugPrint('❌ Error loading invitations: $e');
      _pendingInvites = []; // Set empty if table doesn't exist or error
    }
  }

  Future<void> _loadBabies() async {
    try {
      debugPrint('🔄 Loading babies...');
      final response = await _supabaseService.select(
        'baby_profiles',
        orderBy: 'created_at',
        ascending: false,
      );

      debugPrint('📊 Raw baby_profiles response: $response');

      _babies = response
          .map((data) => BabyProfile.fromJson(data))
          .toList();

      debugPrint('✅ Loaded ${_babies.length} babies');
      for (var baby in _babies) {
        debugPrint('   - ${baby.name} (${baby.gender})');
      }
    } catch (e) {
      debugPrint('❌ Error loading babies: $e');
      _babies = []; // Ensure list is not null
    }
  }

  void _inviteMember() {
    if (!_isAdmin) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Only admins can invite new members'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    _showInviteDialog();
  }

  void _showInviteDialog() {
    final _emailController = TextEditingController();
    String _selectedRole = 'parent';
    bool _canViewData = true;
    bool _canLogActivities = true;
    bool _canViewInsights = false;
    bool _canExportData = false;

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setDialogState) => AlertDialog(
          title: Text('Invite Family Member'),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                TextField(
                  controller: _emailController,
                  decoration: InputDecoration(
                    labelText: 'Email Address',
                    hintText: 'Enter email address',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.email),
                  ),
                  keyboardType: TextInputType.emailAddress,
                ),
                SizedBox(height: 3.h),
                Text(
                  'Role',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 1.h),
                DropdownButtonFormField<String>(
                  value: _selectedRole,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    DropdownMenuItem(value: 'admin', child: Text('Admin')),
                    DropdownMenuItem(value: 'parent', child: Text('Parent')),
                    DropdownMenuItem(value: 'grandparent', child: Text('Grandparent')),
                    DropdownMenuItem(value: 'babysitter', child: Text('Babysitter')),
                    DropdownMenuItem(value: 'other_carer', child: Text('Other Carer')),
                  ],
                  onChanged: (value) {
                    setDialogState(() {
                      _selectedRole = value!;
                      // Set default permissions based on role
                      switch (value) {
                        case 'admin':
                          _canViewData = true;
                          _canLogActivities = true;
                          _canViewInsights = true;
                          _canExportData = true;
                          break;
                        case 'parent':
                          _canViewData = true;
                          _canLogActivities = true;
                          _canViewInsights = true;
                          _canExportData = true;
                          break;
                        case 'grandparent':
                          _canViewData = true;
                          _canLogActivities = true;
                          _canViewInsights = false;
                          _canExportData = false;
                          break;
                        case 'babysitter':
                          _canViewData = true;
                          _canLogActivities = true;
                          _canViewInsights = false;
                          _canExportData = false;
                          break;
                        case 'other_carer':
                          _canViewData = true;
                          _canLogActivities = false;
                          _canViewInsights = false;
                          _canExportData = false;
                          break;
                      }
                    });
                  },
                ),
                SizedBox(height: 3.h),
                Text(
                  'Permissions',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                SizedBox(height: 1.h),
                CheckboxListTile(
                  title: Text('View Data'),
                  value: _canViewData,
                  onChanged: (value) => setDialogState(() => _canViewData = value!),
                ),
                CheckboxListTile(
                  title: Text('Log Activities'),
                  value: _canLogActivities,
                  onChanged: (value) => setDialogState(() => _canLogActivities = value!),
                ),
                CheckboxListTile(
                  title: Text('View AI Insights'),
                  value: _canViewInsights,
                  onChanged: (value) => setDialogState(() => _canViewInsights = value!),
                ),
                CheckboxListTile(
                  title: Text('Export Data'),
                  value: _canExportData,
                  onChanged: (value) => setDialogState(() => _canExportData = value!),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (_emailController.text.trim().isEmpty) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Please enter an email address')),
                  );
                  return;
                }

                Navigator.pop(context);
                await _sendInvitation(
                  _emailController.text.trim(),
                  _selectedRole,
                  {
                    'view_data': _canViewData,
                    'log_activities': _canLogActivities,
                    'view_insights': _canViewInsights,
                    'export_data': _canExportData,
                  },
                );
              },
              child: Text('Send Invitation'),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _sendInvitation(String email, String role, Map<String, bool> permissions) async {
    try {
      // Create invitation record
      final invitation = {
        'email': email,
        'role': role,
        'permissions': permissions,
        'invited_by': _currentUser!.id,
        'status': 'pending',
        'expires_at': DateTime.now().add(Duration(days: 7)).toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabaseService.insert('invitations', invitation);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invitation sent to $email successfully!'),
          backgroundColor: Colors.green,
        ),
      );

      // Reload data
      _loadUserManagementData();

    } catch (e) {
      debugPrint('Error sending invitation: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to send invitation: ${e.toString()}'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _resendInvitation(String inviteId, String email) async {
    try {
      await _supabaseService.update(
        'invitations',
        {
          'expires_at': DateTime.now().add(Duration(days: 7)).toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        'id',
        inviteId,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invitation resent to $email'),
          backgroundColor: Colors.green,
        ),
      );

      _loadUserManagementData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to resend invitation'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _cancelInvitation(String inviteId) async {
    try {
      await _supabaseService.update(
        'invitations',
        {
          'status': 'cancelled',
          'updated_at': DateTime.now().toIso8601String(),
        },
        'id',
        inviteId,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invitation cancelled'),
          backgroundColor: Colors.orange,
        ),
      );

      _loadUserManagementData();
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to cancel invitation'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _removeMember(String memberId, String memberName) async {
    if (!_isAdmin) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Only admins can remove members'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Remove Family Member'),
        content: Text('Are you sure you want to remove $memberName? They will lose access to all baby data.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              try {
                await _supabaseService.delete('user_profiles', 'id', memberId);
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('$memberName has been removed'),
                    backgroundColor: Colors.green,
                  ),
                );
                
                _loadUserManagementData();
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Failed to remove member'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Remove'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Family Management',
          style: GoogleFonts.inter(
            fontSize: 20.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Colors.transparent,
        leading: IconButton(
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: Theme.of(context).colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          if (_isAdmin)
            IconButton(
              onPressed: _inviteMember,
              icon: const Icon(Icons.person_add),
              tooltip: 'Invite Family Member',
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(text: 'Members'),
            Tab(text: 'Invitations'),
            Tab(text: 'Baby Access'),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildMembersTab(),
                _buildInvitationsTab(),
                _buildBabyAccessTab(),
              ],
            ),
    );
  }

  Widget _buildMembersTab() {
    // Always show current user first, then others
    List<Widget> memberWidgets = [];

    if (_currentUser != null) {
      // Current user card with "You" label
      memberWidgets.add(
        Card(
          margin: EdgeInsets.only(bottom: 3.h),
          elevation: 2,
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    // User avatar
                    Container(
                      width: 15.w,
                      height: 15.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: _getRoleColor(_currentUser!.role).withValues(alpha: 0.1),
                        border: Border.all(
                          color: _getRoleColor(_currentUser!.role),
                          width: 2,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          _currentUser!.fullName.substring(0, 1).toUpperCase(),
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.bold,
                            color: _getRoleColor(_currentUser!.role),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                _currentUser!.fullName,
                                style: GoogleFonts.inter(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              SizedBox(width: 2.w),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                                decoration: BoxDecoration(
                                  color: Colors.blue.withValues(alpha: 0.1),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.blue),
                                ),
                                child: Text(
                                  'You',
                                  style: TextStyle(
                                    fontSize: 12.sp,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.blue,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 0.5.h),
                          Text(
                            _currentUser!.email,
                            style: GoogleFonts.inter(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          SizedBox(height: 0.5.h),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                            decoration: BoxDecoration(
                              color: _getRoleColor(_currentUser!.role).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: _getRoleColor(_currentUser!.role)),
                            ),
                            child: Text(
                              _formatRole(_currentUser!.role),
                              style: TextStyle(
                                fontSize: 12.sp,
                                fontWeight: FontWeight.w600,
                                color: _getRoleColor(_currentUser!.role),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Text(
                  'Permissions',
                  style: GoogleFonts.inter(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.grey[700],
                  ),
                ),
                SizedBox(height: 1.h),
                _buildPermissionTags(_currentUser!.role),
              ],
            ),
          ),
        ),
      );
    }

    // Add other family members (excluding current user)
    for (var member in _familyMembers) {
      if (member.id != _currentUser?.id) {
        memberWidgets.add(
          Card(
            margin: EdgeInsets.only(bottom: 3.h),
            child: Padding(
              padding: EdgeInsets.all(4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      // Member avatar
                      Container(
                        width: 15.w,
                        height: 15.w,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: _getRoleColor(member.role).withValues(alpha: 0.1),
                          border: Border.all(
                            color: _getRoleColor(member.role),
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            member.fullName.substring(0, 1).toUpperCase(),
                            style: TextStyle(
                              fontSize: 18.sp,
                              fontWeight: FontWeight.bold,
                              color: _getRoleColor(member.role),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 4.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              member.fullName,
                              style: GoogleFonts.inter(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(height: 0.5.h),
                            Text(
                              member.email,
                              style: GoogleFonts.inter(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                            SizedBox(height: 0.5.h),
                            Container(
                              padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                              decoration: BoxDecoration(
                                color: _getRoleColor(member.role).withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: _getRoleColor(member.role)),
                              ),
                              child: Text(
                                _formatRole(member.role),
                                style: TextStyle(
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w600,
                                  color: _getRoleColor(member.role),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (_isAdmin)
                        PopupMenuButton(
                          itemBuilder: (context) => [
                            PopupMenuItem(
                              value: 'edit',
                              child: Row(
                                children: [
                                  Icon(Icons.edit, size: 20),
                                  SizedBox(width: 2.w),
                                  Text('Edit Permissions'),
                                ],
                              ),
                            ),
                            PopupMenuItem(
                              value: 'remove',
                              child: Row(
                                children: [
                                  Icon(Icons.delete, size: 20, color: Colors.red),
                                  SizedBox(width: 2.w),
                                  Text('Remove', style: TextStyle(color: Colors.red)),
                                ],
                              ),
                            ),
                          ],
                          onSelected: (value) {
                            if (value == 'edit') {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('Permission editing coming soon')),
                              );
                            } else if (value == 'remove') {
                              _removeMember(member.id, member.fullName);
                            }
                          },
                        ),
                    ],
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'Permissions',
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[700],
                    ),
                  ),
                  SizedBox(height: 1.h),
                  _buildPermissionTags(member.role),
                ],
              ),
            ),
          ),
        );
      }
    }

    if (memberWidgets.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'people',
              size: 15.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No Family Members',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Invite family members to help track your baby',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            if (_isAdmin) ...[
              SizedBox(height: 4.h),
              ElevatedButton.icon(
                onPressed: _inviteMember,
                icon: const Icon(Icons.person_add),
                label: const Text('Invite Member'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView(
      padding: EdgeInsets.all(4.w),
      children: [
        ...memberWidgets,
        if (_isAdmin) ...[
          SizedBox(height: 2.h),
          Container(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: _inviteMember,
              icon: const Icon(Icons.person_add),
              label: const Text('Invite Family Member'),
              style: OutlinedButton.styleFrom(
                padding: EdgeInsets.symmetric(vertical: 2.h),
                side: BorderSide(color: Theme.of(context).colorScheme.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildPermissionTags(String role) {
    final permissions = _getUserPermissions(role);
    List<Widget> tags = [];

    permissions.forEach((key, value) {
      if (value) {
        String label = '';
        IconData icon = Icons.check;
        Color color = Colors.green;

        switch (key) {
          case 'view_data':
            label = 'View Data';
            icon = Icons.visibility;
            break;
          case 'log_activities':
            label = 'Log Activities';
            icon = Icons.edit;
            break;
          case 'view_ai_insights':
            label = 'AI Insights';
            icon = Icons.psychology;
            break;
          case 'export_data':
            label = 'Export Data';
            icon = Icons.download;
            break;
          case 'manage_users':
            label = 'Manage Users';
            icon = Icons.people;
            color = Colors.purple;
            break;
          case 'delete_data':
            label = 'Delete Data';
            icon = Icons.delete;
            color = Colors.red;
            break;
        }

        tags.add(
          Container(
            margin: EdgeInsets.only(right: 2.w, bottom: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, size: 14, color: color),
                SizedBox(width: 1.w),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    });

    return Wrap(children: tags);
  }

  Map<String, bool> _getUserPermissions(String role) {
    return _authService.getUserPermissions(role);
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return Colors.purple;
      case 'parent':
        return Colors.blue;
      case 'grandparent':
        return Colors.green;
      case 'babysitter':
        return Colors.orange;
      case 'other_carer':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _formatRole(String role) {
    return role.replaceAll('_', ' ').toUpperCase();
  }

  Widget _buildInvitationsTab() {
    if (_pendingInvites.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'mail_outline',
              size: 15.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No Pending Invitations',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Invite family members to help track your baby',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
            if (_isAdmin) ...[
              SizedBox(height: 4.h),
              ElevatedButton.icon(
                onPressed: _inviteMember,
                icon: const Icon(Icons.person_add),
                label: const Text('Invite Member'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(4.w),
      itemCount: _pendingInvites.length,
      itemBuilder: (context, index) {
        final invite = _pendingInvites[index];
        final expiresAt = DateTime.parse(invite['expires_at']);
        final daysLeft = expiresAt.difference(DateTime.now()).inDays;

        return Card(
          margin: EdgeInsets.only(bottom: 3.h),
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 12.w,
                      height: 12.w,
                      decoration: BoxDecoration(
                        color: Colors.orange.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.schedule,
                        color: Colors.orange,
                        size: 6.w,
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            invite['email'],
                            style: GoogleFonts.inter(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'Role: ${invite['role'].toString().replaceAll('_', ' ').toUpperCase()}',
                            style: GoogleFonts.inter(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            daysLeft > 0 ? 'Expires in $daysLeft days' : 'Expired',
                            style: GoogleFonts.inter(
                              fontSize: 12.sp,
                              color: daysLeft <= 1 ? Colors.red : Colors.orange,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 3.h),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isAdmin ? () => _resendInvitation(invite['id'], invite['email']) : null,
                        child: const Text('Resend'),
                      ),
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isAdmin ? () => _cancelInvitation(invite['id']) : null,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                        child: const Text('Cancel'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildBabyAccessTab() {
    if (_babies.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'child_care',
              size: 15.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No Babies Added',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Add a baby profile to manage access permissions',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(4.w),
      itemCount: _babies.length,
      itemBuilder: (context, index) {
        final baby = _babies[index];
        return Card(
          margin: EdgeInsets.only(bottom: 3.h),
          child: Padding(
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 15.w,
                      height: 15.w,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: baby.gender == 'boy'
                            ? LinearGradient(colors: [Colors.blue, Colors.cyan])
                            : LinearGradient(colors: [Colors.pink, Colors.purple]),
                      ),
                      child: Center(
                        child: Text(
                          baby.name.substring(0, 1).toUpperCase(),
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            baby.name,
                            style: GoogleFonts.inter(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          Text(
                            'Born: ${baby.birthDate.day}/${baby.birthDate.month}/${baby.birthDate.year}',
                            style: GoogleFonts.inter(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                Text(
                  'All family members have access to ${baby.name}\'s data based on their role permissions.',
                  style: GoogleFonts.inter(
                    fontSize: 14.sp,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Helper methods for display
  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return Colors.purple;
      case 'parent':
        return Colors.blue;
      case 'grandparent':
        return Colors.green;
      case 'babysitter':
        return Colors.orange;
      case 'other_carer':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  String _formatRole(String role) {
    return role.replaceAll('_', ' ').toUpperCase();
  }

  Map<String, bool> _getUserPermissions(String role) {
    return _authService.getUserPermissions(role);
  }

  Widget _buildPermissionTags(String role) {
    final permissions = _getUserPermissions(role);
    List<Widget> tags = [];

    permissions.forEach((key, value) {
      if (value) {
        String label = '';
        IconData icon = Icons.check;
        Color color = Colors.green;

        switch (key) {
          case 'view_data':
            label = 'View Data';
            icon = Icons.visibility;
            break;
          case 'log_activities':
            label = 'Log Activities';
            icon = Icons.edit;
            break;
          case 'view_ai_insights':
            label = 'AI Insights';
            icon = Icons.psychology;
            break;
          case 'export_data':
            label = 'Export Data';
            icon = Icons.download;
            break;
          case 'manage_users':
            label = 'Manage Users';
            icon = Icons.people;
            color = Colors.purple;
            break;
          case 'delete_data':
            label = 'Delete Data';
            icon = Icons.delete;
            color = Colors.red;
            break;
        }

        tags.add(
          Container(
            margin: EdgeInsets.only(right: 2.w, bottom: 1.h),
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: color.withValues(alpha: 0.3)),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, size: 14, color: color),
                SizedBox(width: 1.w),
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w500,
                    color: color,
                  ),
                ),
              ],
            ),
          ),
        );
      }
    });

    return Wrap(children: tags);
  }
}
