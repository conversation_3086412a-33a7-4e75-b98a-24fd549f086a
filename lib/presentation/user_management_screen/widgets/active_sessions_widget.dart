import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../widgets/custom_icon_widget.dart';

class ActiveSessionsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> sessions;
  final Function(String sessionId) onEndSession;

  const ActiveSessionsWidget({
    super.key,
    required this.sessions,
    required this.onEndSession,
  });

  String _getDeviceIcon(String deviceInfo) {
    if (deviceInfo.toLowerCase().contains('iphone') ||
        deviceInfo.toLowerCase().contains('android')) {
      return 'smartphone';
    } else if (deviceInfo.toLowerCase().contains('ipad') ||
        deviceInfo.toLowerCase().contains('tablet')) {
      return 'tablet_mac';
    } else if (deviceInfo.toLowerCase().contains('mac') ||
        deviceInfo.toLowerCase().contains('windows') ||
        deviceInfo.toLowerCase().contains('linux')) {
      return 'laptop';
    }
    return 'device_unknown';
  }

  String _formatLastActivity(DateTime lastActivity) {
    final difference = DateTime.now().difference(lastActivity);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  Color _getStatusColor(DateTime lastActivity) {
    final difference = DateTime.now().difference(lastActivity);

    if (difference.inMinutes < 30) {
      return Colors.green; // Active
    } else if (difference.inHours < 24) {
      return Colors.orange; // Recent
    } else {
      return Colors.grey; // Inactive
    }
  }

  String _getStatusText(DateTime lastActivity) {
    final difference = DateTime.now().difference(lastActivity);

    if (difference.inMinutes < 30) {
      return 'Active';
    } else if (difference.inHours < 24) {
      return 'Recent';
    } else {
      return 'Inactive';
    }
  }

  @override
  Widget build(BuildContext context) {
    if (sessions.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'devices',
              size: 15.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No Active Sessions',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Your active sessions will appear here',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(4.w),
      itemCount: sessions.length + 1, // +1 for info card
      itemBuilder: (context, index) {
        if (index == 0) {
          return _buildInfoCard();
        }

        final session = sessions[index - 1];
        return _buildSessionCard(context, session);
      },
    );
  }

  Widget _buildInfoCard() {
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(
            Icons.security,
            color: Colors.blue,
            size: 6.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Session Security',
                  style: GoogleFonts.inter(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w600,
                    color: Colors.blue[700],
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  'Monitor your active sessions for security. End any sessions you don\'t recognize.',
                  style: GoogleFonts.inter(
                    fontSize: 13.sp,
                    color: Colors.blue[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSessionCard(BuildContext context, Map<String, dynamic> session) {
    final lastActivity = session['last_activity'] as DateTime;
    final statusColor = _getStatusColor(lastActivity);
    final isCurrentSession = session['id'] == '1'; // Mock current session

    return Card(
      margin: EdgeInsets.only(bottom: 3.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          children: [
            Row(
              children: [
                // Device Icon
                Container(
                  width: 12.w,
                  height: 12.w,
                  decoration: BoxDecoration(
                    color: statusColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: CustomIconWidget(
                    iconName: _getDeviceIcon(session['device_info']),
                    size: 6.w,
                    color: statusColor,
                  ),
                ),

                SizedBox(width: 4.w),

                // Session Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              session['device_info'],
                              style: GoogleFonts.inter(
                                fontSize: 15.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                          if (isCurrentSession)
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: 2.w,
                                vertical: 0.5.w,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.green.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'CURRENT',
                                style: GoogleFonts.inter(
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w700,
                                  color: Colors.green,
                                ),
                              ),
                            ),
                        ],
                      ),
                      SizedBox(height: 0.5.h),
                      Row(
                        children: [
                          Icon(
                            Icons.location_on,
                            size: 4.w,
                            color: Colors.grey[500],
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            session['location'] ?? 'Unknown Location',
                            style: GoogleFonts.inter(
                              fontSize: 13.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 0.5.h),
                      Row(
                        children: [
                          Icon(
                            Icons.public,
                            size: 4.w,
                            color: Colors.grey[500],
                          ),
                          SizedBox(width: 1.w),
                          Text(
                            session['ip_address'],
                            style: GoogleFonts.inter(
                              fontSize: 13.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 3.h),

            // Status and Last Activity
            Row(
              children: [
                Container(
                  width: 3.w,
                  height: 3.w,
                  decoration: BoxDecoration(
                    color: statusColor,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 2.w),
                Text(
                  _getStatusText(lastActivity),
                  style: GoogleFonts.inter(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                    color: statusColor,
                  ),
                ),
                const Spacer(),
                Text(
                  _formatLastActivity(lastActivity),
                  style: GoogleFonts.inter(
                    fontSize: 13.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),

            // End Session Button (not for current session)
            if (!isCurrentSession) ...[
              SizedBox(height: 3.h),
              SizedBox(
                width: double.infinity,
                child: OutlinedButton.icon(
                  onPressed: () => _showEndSessionDialog(context, session),
                  icon: const Icon(Icons.logout, size: 18),
                  label: const Text('End Session'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: Colors.red,
                    side: const BorderSide(color: Colors.red),
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _showEndSessionDialog(
      BuildContext context, Map<String, dynamic> session) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('End Session'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Are you sure you want to end this session?'),
            SizedBox(height: 2.h),
            Text(
              'Device: ${session['device_info']}',
              style: GoogleFonts.inter(
                fontSize: 13.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              'Location: ${session['location']}',
              style: GoogleFonts.inter(
                fontSize: 13.sp,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              onEndSession(session['id']);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('End Session'),
          ),
        ],
      ),
    );
  }
}
