import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../services/supabase_service.dart';
import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
class BabyAssignmentsWidget extends StatefulWidget {
  final List<Map<String, dynamic>> familyMembers;
  final bool isAdmin;
  final VoidCallback onAssignmentChanged;

  const BabyAssignmentsWidget({
    super.key,
    required this.familyMembers,
    required this.isAdmin,
    required this.onAssignmentChanged,
  });

  @override
  State<BabyAssignmentsWidget> createState() => _BabyAssignmentsWidgetState();
}

class _BabyAssignmentsWidgetState extends State<BabyAssignmentsWidget> {
  List<Map<String, dynamic>> _babies = [];
  Map<String, List<String>> _babyAssignments = {};

  @override
  void initState() {
    super.initState();
    _loadBabyAssignments();
  }

  void _loadBabyAssignments() {
    // Mock baby data
    _babies = [
      {
        'id': '1',
        'name': 'Loading...',
        'birth_date': DateTime(2024, 1, 15),
        'photo_url':
            'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=150&h=150&fit=crop&crop=face',
        'gender': 'female',
      },
      {
        'id': '2',
        'name': 'Baby Oliver',
        'birth_date': DateTime(2023, 12, 1),
        'photo_url':
            'https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?w=150&h=150&fit=crop&crop=face',
        'gender': 'male',
      },
    ];

    // Mock assignments
    _babyAssignments = {
      '1': ['1', '2'], // Emma assigned to admin and grandparent
      '2': ['1', '3'], // Oliver assigned to admin and babysitter
    };
  }

  int _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    return (difference.inDays / 30).floor(); // Approximate months
  }

  void _updateAssignment(String babyId, String memberId, bool isAssigned) {
    setState(() {
      if (isAssigned) {
        _babyAssignments[babyId]?.add(memberId);
      } else {
        _babyAssignments[babyId]?.remove(memberId);
      }
    });
    widget.onAssignmentChanged();
  }

  void _showPermissionDialog(String babyId, String memberId) {
    final baby = _babies.firstWhere((b) => b['id'] == babyId);
    final member = widget.familyMembers.firstWhere((m) => m['id'] == memberId);

    showDialog(
      context: context,
      builder: (context) => _BabyPermissionDialog(
        baby: baby,
        member: member,
        onPermissionsUpdated: () {
          // Refresh assignments
          widget.onAssignmentChanged();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    if (_babies.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.child_care,
              size: 15.w,
              color: Colors.grey[400],
            ),
            SizedBox(height: 2.h),
            Text(
              'No Babies Added',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.w500,
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Add a baby profile to manage carer assignments',
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(4.w),
      itemCount: _babies.length,
      itemBuilder: (context, index) {
        final baby = _babies[index];
        return _buildBabyCard(baby);
      },
    );
  }

  Widget _buildBabyCard(Map<String, dynamic> baby) {
    final babyId = baby['id'] as String;
    final assignedMemberIds = _babyAssignments[babyId] ?? [];

    return Card(
      margin: EdgeInsets.only(bottom: 4.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Baby Info Header
            Row(
              children: [
                Container(
                  width: 15.w,
                  height: 15.w,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    gradient: baby['gender'] == 'female'
                        ? const LinearGradient(
                            colors: [Colors.pink, Colors.purple],
                          )
                        : const LinearGradient(
                            colors: [Colors.blue, Colors.cyan],
                          ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: CustomImageWidget(
                      imageUrl: baby['photo_url'] ?? '',
                      width: 15.w,
                      height: 15.w,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        baby['name'],
                        style: GoogleFonts.inter(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        '${_calculateAge(baby['birth_date'])} months old',
                        style: GoogleFonts.inter(
                          fontSize: 14.sp,
                          color: Colors.grey[600],
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        'Born: ${baby['birth_date'].day}/${baby['birth_date'].month}/${baby['birth_date'].year}',
                        style: GoogleFonts.inter(
                          fontSize: 13.sp,
                          color: Colors.grey[500],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            SizedBox(height: 4.h),

            // Assigned Carers Section
            Text(
              'Assigned Carers',
              style: GoogleFonts.inter(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),

            SizedBox(height: 2.h),

            // Family Members List
            ...widget.familyMembers.map((member) {
              final memberId = member['id'] as String;
              final isAssigned = assignedMemberIds.contains(memberId);
              final isAdmin = member['role'] == 'admin';

              return Container(
                margin: EdgeInsets.only(bottom: 2.h),
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: isAssigned
                      ? AppTheme.lightTheme.primaryColor.withValues(alpha: 0.1)
                      : Colors.grey[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isAssigned
                        ? AppTheme.lightTheme.primaryColor
                            .withValues(alpha: 0.3)
                        : Colors.grey[300]!,
                  ),
                ),
                child: Row(
                  children: [
                    // Member Avatar
                    Container(
                      width: 10.w,
                      height: 10.w,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: CustomImageWidget(
                          imageUrl: member['avatar_url'] ?? '',
                          width: 10.w,
                          height: 10.w,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),

                    SizedBox(width: 3.w),

                    // Member Info
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Text(
                                member['name'],
                                style: GoogleFonts.inter(
                                  fontSize: 15.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              if (isAdmin) ...[
                                SizedBox(width: 2.w),
                                Container(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: 1.5.w,
                                    vertical: 0.5.w,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.purple.withValues(alpha: 0.1),
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    'ADMIN',
                                    style: GoogleFonts.inter(
                                      fontSize: 10.sp,
                                      fontWeight: FontWeight.w700,
                                      color: Colors.purple,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          Text(
                            member['role']
                                .toString()
                                .replaceAll('_', ' ')
                                .toUpperCase(),
                            style: GoogleFonts.inter(
                              fontSize: 12.sp,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Assignment Toggle
                    if (widget.isAdmin && !isAdmin)
                      Switch(
                        value: isAssigned,
                        onChanged: (value) {
                          _updateAssignment(babyId, memberId, value);
                        },
                        activeColor: AppTheme.lightTheme.primaryColor,
                      ),

                    // Permissions Button
                    if (isAssigned && !isAdmin)
                      IconButton(
                        onPressed: () =>
                            _showPermissionDialog(babyId, memberId),
                        icon: Icon(
                          Icons.settings,
                          color: AppTheme.lightTheme.primaryColor,
                        ),
                        tooltip: 'Configure Permissions',
                      ),
                  ],
                ),
              );
            }),

            // Assignment Summary
            if (assignedMemberIds.isNotEmpty) ...[
              SizedBox(height: 3.h),
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: Colors.green.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border:
                      Border.all(color: Colors.green.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.people,
                      color: Colors.green,
                      size: 5.w,
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        '${assignedMemberIds.length} carer(s) can access ${baby['name']}\'s data',
                        style: GoogleFonts.inter(
                          fontSize: 13.sp,
                          color: Colors.green[700],
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

class _BabyPermissionDialog extends StatefulWidget {
  final Map<String, dynamic> baby;
  final Map<String, dynamic> member;
  final VoidCallback onPermissionsUpdated;

  const _BabyPermissionDialog({
    required this.baby,
    required this.member,
    required this.onPermissionsUpdated,
  });

  @override
  State<_BabyPermissionDialog> createState() => _BabyPermissionDialogState();
}

class _BabyPermissionDialogState extends State<_BabyPermissionDialog> {
  Map<String, bool> _permissions = {
    'can_view': true,
    'can_log_activities': false,
    'can_view_medical': false,
  };

  DateTime? _temporaryUntil;

  @override
  void initState() {
    super.initState();
    // Load existing permissions
    _permissions = {
      'can_view': true,
      'can_log_activities': widget.member['role'] != 'other_carer',
      'can_view_medical': widget.member['role'] == 'parent',
    };
  }

  Future<void> _selectTemporaryDate() async {
    final date = await ModernDateTimePicker.showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _temporaryUntil = date;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Baby Access Permissions',
            style: GoogleFonts.inter(
              fontSize: 18.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          Text(
            '${widget.member['name']} → ${widget.baby['name']}',
            style: GoogleFonts.inter(
              fontSize: 14.sp,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildPermissionTile(
              'View Baby Data',
              'Access to all activities, growth charts, and milestones',
              'can_view',
              Icons.visibility,
            ),
            _buildPermissionTile(
              'Log Activities',
              'Record feeding, sleep, diaper changes, and play time',
              'can_log_activities',
              Icons.edit,
            ),
            _buildPermissionTile(
              'View Medical Info',
              'Access to medication records and health notes',
              'can_view_medical',
              Icons.medical_services,
            ),

            SizedBox(height: 3.h),

            // Temporary Access
            if (widget.member['role'] == 'babysitter') ...[
              Divider(),
              SizedBox(height: 2.h),
              Text(
                'Temporary Access',
                style: GoogleFonts.inter(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              InkWell(
                onTap: _selectTemporaryDate,
                child: Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey[300]!),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.schedule, color: Colors.grey[600]),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          _temporaryUntil != null
                              ? 'Until ${_temporaryUntil!.day}/${_temporaryUntil!.month}/${_temporaryUntil!.year}'
                              : 'Set expiration date (optional)',
                          style: GoogleFonts.inter(fontSize: 14.sp),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            // Save permissions
            widget.onPermissionsUpdated();
            Navigator.pop(context);
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Permissions updated'),
                backgroundColor: Colors.green,
              ),
            );
          },
          child: const Text('Save'),
        ),
      ],
    );
  }

  Widget _buildPermissionTile(
      String title, String description, String key, IconData icon) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 5.w),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: GoogleFonts.inter(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _permissions[key] ?? false,
            onChanged: (value) {
              setState(() {
                _permissions[key] = value;
              });
            },
            activeColor: AppTheme.lightTheme.primaryColor,
          ),
        ],
      ),
    );
  }
}
