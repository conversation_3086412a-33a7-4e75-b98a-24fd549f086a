import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

class RolePermissionsWidget extends StatefulWidget {
  final Map<String, dynamic> member;
  final Function(Map<String, bool>) onPermissionsUpdated;

  const RolePermissionsWidget({
    super.key,
    required this.member,
    required this.onPermissionsUpdated,
  });

  @override
  State<RolePermissionsWidget> createState() => _RolePermissionsWidgetState();
}

class _RolePermissionsWidgetState extends State<RolePermissionsWidget> {
  late Map<String, bool> _permissions;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _permissions = Map<String, bool>.from(widget.member['permissions'] ?? {});
  }

  Future<void> _savePermissions() async {
    setState(() => _isLoading = true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      widget.onPermissionsUpdated(_permissions);

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to update permissions: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return Colors.purple;
      case 'parent':
        return Colors.blue;
      case 'grandparent':
        return Colors.green;
      case 'babysitter':
        return Colors.orange;
      case 'other_carer':
        return Colors.teal;
      default:
        return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 90.w,
      constraints: BoxConstraints(maxHeight: 80.h),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color:
                  _getRoleColor(widget.member['role']).withValues(alpha: 0.1),
              borderRadius:
                  const BorderRadius.vertical(top: Radius.circular(20)),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 8.w,
                  backgroundColor: _getRoleColor(widget.member['role']),
                  child: Text(
                    widget.member['name']
                        .toString()
                        .substring(0, 1)
                        .toUpperCase(),
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.member['name'],
                        style: GoogleFonts.inter(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      Text(
                        widget.member['role']
                            .toString()
                            .replaceAll('_', ' ')
                            .toUpperCase(),
                        style: GoogleFonts.inter(
                          fontSize: 14.sp,
                          color: _getRoleColor(widget.member['role']),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Permissions List
          Flexible(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(6.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Permissions',
                    style: GoogleFonts.inter(
                      fontSize: 18.sp,
                      fontWeight: FontWeight.w700,
                    ),
                  ),

                  SizedBox(height: 1.h),

                  Text(
                    'Control what ${widget.member['name']} can access and do',
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),

                  SizedBox(height: 4.h),

                  _buildPermissionCategory(
                    'Data Access',
                    'Control data viewing capabilities',
                    [
                      _buildPermissionTile(
                        'View Baby Data',
                        'Access to all baby information, activities, and growth data',
                        'view_data',
                        Icons.visibility,
                        Colors.blue,
                      ),
                      _buildPermissionTile(
                        'View AI Insights',
                        'Access to AI-generated analysis and recommendations',
                        'view_ai_insights',
                        Icons.psychology,
                        Colors.purple,
                      ),
                    ],
                  ),

                  SizedBox(height: 3.h),

                  _buildPermissionCategory(
                    'Activity Management',
                    'Control activity logging and management',
                    [
                      _buildPermissionTile(
                        'Log Activities',
                        'Record feeding, sleep, diaper changes, and other activities',
                        'log_activities',
                        Icons.edit,
                        Colors.green,
                      ),
                    ],
                  ),

                  SizedBox(height: 3.h),

                  _buildPermissionCategory(
                    'Data Export',
                    'Control data sharing and export capabilities',
                    [
                      _buildPermissionTile(
                        'Export Data',
                        'Download and share baby data reports and summaries',
                        'export_data',
                        Icons.download,
                        Colors.orange,
                      ),
                    ],
                  ),

                  SizedBox(height: 4.h),

                  // Role-specific recommendations
                  Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                      border:
                          Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: Colors.blue,
                          size: 6.w,
                        ),
                        SizedBox(width: 3.w),
                        Expanded(
                          child: Text(
                            _getRoleRecommendation(widget.member['role']),
                            style: GoogleFonts.inter(
                              fontSize: 13.sp,
                              color: Colors.blue[700],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Action Buttons
          Container(
            padding: EdgeInsets.all(6.w),
            decoration: BoxDecoration(
              color: Colors.grey[50],
              borderRadius:
                  const BorderRadius.vertical(bottom: Radius.circular(20)),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _isLoading ? null : () => Navigator.pop(context),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 2.h),
                    ),
                    child: Text(
                      'Cancel',
                      style: GoogleFonts.inter(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _savePermissions,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(vertical: 2.h),
                    ),
                    child: _isLoading
                        ? SizedBox(
                            width: 5.w,
                            height: 5.w,
                            child: const CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                            ),
                          )
                        : Text(
                            'Save Changes',
                            style: GoogleFonts.inter(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPermissionCategory(
      String title, String description, List<Widget> permissions) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.inter(
            fontSize: 16.sp,
            fontWeight: FontWeight.w700,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          description,
          style: GoogleFonts.inter(
            fontSize: 13.sp,
            color: Colors.grey[600],
          ),
        ),
        SizedBox(height: 2.h),
        ...permissions,
      ],
    );
  }

  Widget _buildPermissionTile(
    String title,
    String description,
    String permissionKey,
    IconData icon,
    Color color,
  ) {
    final hasPermission = _permissions[permissionKey] ?? false;

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: hasPermission ? color.withValues(alpha: 0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              hasPermission ? color.withValues(alpha: 0.3) : Colors.grey[300]!,
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 10.w,
            height: 10.w,
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              icon,
              color: color,
              size: 5.w,
            ),
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w600,
                    color: hasPermission ? color : Colors.black,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  description,
                  style: GoogleFonts.inter(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: hasPermission,
            onChanged: (value) {
              setState(() {
                _permissions[permissionKey] = value;
              });
            },
            activeColor: color,
          ),
        ],
      ),
    );
  }

  String _getRoleRecommendation(String role) {
    switch (role) {
      case 'parent':
        return 'Parents typically have full access to all features including AI insights and data export.';
      case 'grandparent':
        return 'Grandparents usually need view and logging access but may not require AI insights or data export.';
      case 'babysitter':
        return 'Babysitters typically need basic logging access during their care periods. Consider temporary access.';
      case 'other_carer':
        return 'Other carers can have customized permissions based on their relationship and involvement.';
      default:
        return 'Adjust permissions based on this member\'s role and involvement in baby care.';
    }
  }
}
