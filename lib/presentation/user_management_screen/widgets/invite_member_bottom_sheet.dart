import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class InviteMemberBottomSheet extends StatefulWidget {
  final Function(String email, String role) onInviteSent;

  const InviteMemberBottomSheet({
    super.key,
    required this.onInviteSent,
  });

  @override
  State<InviteMemberBottomSheet> createState() =>
      _InviteMemberBottomSheetState();
}

class _InviteMemberBottomSheetState extends State<InviteMemberBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  String _selectedRole = 'parent';
  Map<String, bool> _permissions = {
    'view_data': true,
    'log_activities': false,
    'view_ai_insights': false,
    'export_data': false,
  };
  bool _isLoading = false;

  final List<Map<String, dynamic>> _roles = [
    {
      'value': 'parent',
      'label': 'Parent',
      'description': 'Full access to all baby data and features',
      'icon': Icons.family_restroom,
      'color': Colors.blue,
    },
    {
      'value': 'grandparent',
      'label': 'Grandparent',
      'description': 'View data and basic logging capabilities',
      'icon': Icons.elderly,
      'color': Colors.green,
    },
    {
      'value': 'babysitter',
      'label': 'Babysitter',
      'description': 'Temporary access with time restrictions',
      'icon': Icons.child_care,
      'color': Colors.orange,
    },
    {
      'value': 'other_carer',
      'label': 'Other Carer',
      'description': 'Customizable permissions for extended family',
      'icon': Icons.person,
      'color': Colors.teal,
    },
  ];

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  void _updateDefaultPermissions(String role) {
    setState(() {
      _selectedRole = role;
      switch (role) {
        case 'parent':
          _permissions = {
            'view_data': true,
            'log_activities': true,
            'view_ai_insights': true,
            'export_data': true,
          };
          break;
        case 'grandparent':
          _permissions = {
            'view_data': true,
            'log_activities': true,
            'view_ai_insights': false,
            'export_data': false,
          };
          break;
        case 'babysitter':
          _permissions = {
            'view_data': true,
            'log_activities': true,
            'view_ai_insights': false,
            'export_data': false,
          };
          break;
        case 'other_carer':
          _permissions = {
            'view_data': true,
            'log_activities': false,
            'view_ai_insights': false,
            'export_data': false,
          };
          break;
      }
    });
  }

  Future<void> _sendInvitation() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      // Simulate API call
      await Future.delayed(const Duration(seconds: 1));

      widget.onInviteSent(_emailController.text.trim(), _selectedRole);

      if (mounted) {
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send invitation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24)),
      ),
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.8,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return SingleChildScrollView(
            controller: scrollController,
            padding: EdgeInsets.all(6.w),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Handle Bar
                  Center(
                    child: Container(
                      width: 12.w,
                      height: 0.5.h,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(10),
                      ),
                    ),
                  ),

                  SizedBox(height: 3.h),

                  // Header
                  Row(
                    children: [
                      Container(
                        width: 12.w,
                        height: 12.w,
                        decoration: BoxDecoration(
                          color: AppTheme.lightTheme.primaryColor
                              .withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: CustomIconWidget(
                          iconName: 'person_add',
                          size: 6.w,
                          color: AppTheme.lightTheme.primaryColor,
                        ),
                      ),
                      SizedBox(width: 3.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Invite Family Member',
                              style: GoogleFonts.inter(
                                fontSize: 20.sp,
                                fontWeight: FontWeight.w700,
                              ),
                            ),
                            Text(
                              'Add someone to help care for your baby',
                              style: GoogleFonts.inter(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: 4.h),

                  // Email Input
                  Text(
                    'Email Address',
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  TextFormField(
                    controller: _emailController,
                    keyboardType: TextInputType.emailAddress,
                    decoration: const InputDecoration(
                      hintText: 'Enter email address',
                      prefixIcon: Icon(Icons.email_outlined),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Please enter an email address';
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                          .hasMatch(value)) {
                        return 'Please enter a valid email address';
                      }
                      return null;
                    },
                  ),

                  SizedBox(height: 4.h),

                  // Role Selection
                  Text(
                    'Select Role',
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),

                  ...(_roles.map((role) => _buildRoleCard(role))),

                  SizedBox(height: 4.h),

                  // Permissions
                  Text(
                    'Permissions',
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Customize what this member can do',
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                  SizedBox(height: 2.h),

                  _buildPermissionTile(
                    'View Baby Data',
                    'Access to feeding, sleep, growth charts, and activities',
                    'view_data',
                    Icons.visibility,
                  ),
                  _buildPermissionTile(
                    'Log Activities',
                    'Record feeding, sleep, diaper changes, and other activities',
                    'log_activities',
                    Icons.edit,
                  ),
                  _buildPermissionTile(
                    'View AI Insights',
                    'Access to AI analysis and recommendations',
                    'view_ai_insights',
                    Icons.psychology,
                  ),
                  _buildPermissionTile(
                    'Export Data',
                    'Download and share baby data reports',
                    'export_data',
                    Icons.download,
                  ),

                  SizedBox(height: 4.h),

                  // Send Invitation Button
                  SizedBox(
                    width: double.infinity,
                    height: 6.h,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _sendInvitation,
                      child: _isLoading
                          ? SizedBox(
                              width: 5.w,
                              height: 5.w,
                              child: const CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              'Send Invitation',
                              style: GoogleFonts.inter(
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                    ),
                  ),

                  SizedBox(height: 2.h),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildRoleCard(Map<String, dynamic> role) {
    final isSelected = _selectedRole == role['value'];

    return GestureDetector(
      onTap: () => _updateDefaultPermissions(role['value']),
      child: Container(
        margin: EdgeInsets.only(bottom: 2.h),
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isSelected
              ? role['color'].withValues(alpha: 0.1)
              : Colors.grey[50],
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: isSelected ? role['color'] : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: role['color'].withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                role['icon'],
                color: role['color'],
                size: 6.w,
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    role['label'],
                    style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? role['color'] : Colors.black,
                    ),
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    role['description'],
                    style: GoogleFonts.inter(
                      fontSize: 13.sp,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: role['color'],
                size: 6.w,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildPermissionTile(
    String title,
    String description,
    String permissionKey,
    IconData icon,
  ) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: Row(
        children: [
          Icon(
            icon,
            color: Colors.grey[600],
            size: 6.w,
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: GoogleFonts.inter(
                    fontSize: 15.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: GoogleFonts.inter(
                    fontSize: 12.sp,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _permissions[permissionKey] ?? false,
            onChanged: (value) {
              setState(() {
                _permissions[permissionKey] = value;
              });
            },
            activeColor: AppTheme.lightTheme.primaryColor,
          ),
        ],
      ),
    );
  }
}
