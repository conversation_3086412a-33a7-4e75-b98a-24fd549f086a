
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:babytracker_pro/core/app_export.dart';
import 'package:babytracker_pro/widgets/activity_log_item.dart';
import 'package:babytracker_pro/models/scheduled_activity.dart';
import 'package:babytracker_pro/presentation/scheduler/widgets/scheduled_activity_card.dart';
import 'package:sizer/sizer.dart';

class ActivityTimelineScreen extends StatefulWidget {
  const ActivityTimelineScreen({super.key});

  @override
  _ActivityTimelineScreenState createState() => _ActivityTimelineScreenState();
}

class _ActivityTimelineScreenState extends State<ActivityTimelineScreen> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDay;
  List<ActivityLog> _activityLogs = [];
  List<ScheduledActivity> _scheduledActivities = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _selectedDay = _focusedDay;
    _fetchActivitiesForSelectedDate();
  }

  Future<void> _fetchActivitiesForSelectedDate() async {
    setState(() {
      _isLoading = true;
    });

    final supabaseService = SupabaseService();
    final babyId = await supabaseService.getActiveBabyId();

    if (babyId != null && _selectedDay != null) {
      try {
        // Fetch both logged activities and scheduled activities
        final activities = await supabaseService.getActivitiesForDate(babyId, _selectedDay!);
        final scheduledActivities = await supabaseService.getScheduledActivitiesForDate(babyId, _selectedDay!);
        
        setState(() {
          _activityLogs = activities;
          _scheduledActivities = scheduledActivities;
          _isLoading = false;
        });
      } catch (e) {
        debugPrint('❌ Error fetching activities: $e');
        setState(() {
          _activityLogs = [];
          _scheduledActivities = [];
          _isLoading = false;
        });
      }
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          'Activity Timeline',
          style: TextStyle(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        iconTheme: IconThemeData(color: colorScheme.onSurface),
        systemOverlayStyle: isDarkMode 
          ? SystemUiOverlayStyle.light 
          : SystemUiOverlayStyle.dark,
      ),
      body: Column(
        children: [
          // Calendar format selector with theme-aware styling
          Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outline.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildFormatButton(
                  context,
                  'Month',
                  CalendarFormat.month,
                  _calendarFormat == CalendarFormat.month,
                ),
                _buildFormatButton(
                  context,
                  '2 Weeks',
                  CalendarFormat.twoWeeks,
                  _calendarFormat == CalendarFormat.twoWeeks,
                ),
                _buildFormatButton(
                  context,
                  'Week',
                  CalendarFormat.week,
                  _calendarFormat == CalendarFormat.week,
                ),
              ],
            ),
          ),
          
          // Calendar with comprehensive theme support
          Container(
            color: colorScheme.surface,
            child: TableCalendar<ActivityLog>(
              firstDay: DateTime.utc(2020, 1, 1),
              lastDay: DateTime.utc(2030, 12, 31),
              focusedDay: _focusedDay,
              calendarFormat: _calendarFormat,
              selectedDayPredicate: (day) {
                return isSameDay(_selectedDay, day);
              },
              onDaySelected: (selectedDay, focusedDay) {
                setState(() {
                  _selectedDay = selectedDay;
                  _focusedDay = focusedDay;
                });
                _fetchActivitiesForSelectedDate();
              },
              onPageChanged: (focusedDay) {
                _focusedDay = focusedDay;
              },
              
              // Comprehensive theme-aware styling
              headerStyle: HeaderStyle(
                formatButtonVisible: false,
                titleCentered: true,
                leftChevronIcon: Icon(
                  Icons.chevron_left,
                  color: colorScheme.onSurface,
                ),
                rightChevronIcon: Icon(
                  Icons.chevron_right,
                  color: colorScheme.onSurface,
                ),
                titleTextStyle: TextStyle(
                  color: colorScheme.onSurface,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
                headerPadding: EdgeInsets.symmetric(vertical: 1.h),
              ),
              
              daysOfWeekStyle: DaysOfWeekStyle(
                weekdayStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
                weekendStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
              
              calendarStyle: CalendarStyle(
                // Today styling
                todayDecoration: BoxDecoration(
                  color: colorScheme.primary.withValues(alpha: 0.3),
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: colorScheme.primary,
                    width: 2,
                  ),
                ),
                todayTextStyle: TextStyle(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
                
                // Selected day styling
                selectedDecoration: BoxDecoration(
                  color: colorScheme.primary,
                  shape: BoxShape.circle,
                ),
                selectedTextStyle: TextStyle(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                ),
                
                // Default day styling
                defaultTextStyle: TextStyle(
                  color: colorScheme.onSurface,
                ),
                weekendTextStyle: TextStyle(
                  color: colorScheme.onSurface,
                ),
                
                // Outside days (previous/next month)
                outsideTextStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.4),
                ),
                
                // Disabled days
                disabledTextStyle: TextStyle(
                  color: colorScheme.onSurface.withValues(alpha: 0.3),
                ),
                
                // Hover effects
                rangeHighlightColor: colorScheme.primary.withValues(alpha: 0.1),
                
                // Cell padding and margins
                cellMargin: EdgeInsets.all(4),
                cellPadding: EdgeInsets.zero,
                
                // Remove default decorations that don't support theming
                defaultDecoration: BoxDecoration(),
                weekendDecoration: BoxDecoration(),
                outsideDecoration: BoxDecoration(),
                disabledDecoration: BoxDecoration(),
              ),
            ),
          ),
          
          // Divider
          Divider(
            height: 1,
            thickness: 1,
            color: colorScheme.outline.withValues(alpha: 0.2),
          ),
          
          // Activities list with theme-aware styling
          Expanded(
            child: Container(
              color: colorScheme.surface,
              child: _isLoading
                  ? Center(
                      child: CircularProgressIndicator(
                        color: colorScheme.primary,
                      ),
                    )
                  : (_activityLogs.isEmpty && _scheduledActivities.isEmpty)
                      ? _buildEmptyState(context)
                      : _buildActivitiesList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormatButton(
    BuildContext context,
    String label,
    CalendarFormat format,
    bool isSelected,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Expanded(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 1.w),
        child: ElevatedButton(
          onPressed: () {
            setState(() {
              _calendarFormat = format;
            });
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: isSelected 
              ? colorScheme.primary 
              : colorScheme.surface,
            foregroundColor: isSelected 
              ? colorScheme.onPrimary 
              : colorScheme.onSurface,
            elevation: isSelected ? 2 : 0,
            side: BorderSide(
              color: isSelected 
                ? colorScheme.primary 
                : colorScheme.outline.withValues(alpha: 0.3),
              width: 1,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: EdgeInsets.symmetric(vertical: 1.h),
          ),
          child: Text(
            label,
            style: TextStyle(
              fontSize: 12.sp,
              fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildActivitiesList() {
    final colorScheme = Theme.of(context).colorScheme;
    
    return SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Scheduled Activities Section
          if (_scheduledActivities.isNotEmpty) ...[
            Text(
              'Scheduled Activities',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 1.h),
            ..._scheduledActivities.map((activity) => Padding(
              padding: EdgeInsets.only(bottom: 2.h),
              child: ScheduledActivityCard(
                activity: activity,
                onCompleted: () => _markActivityCompleted(activity),
                onDeleted: () => _deleteActivity(activity),
                showTimelineButton: false, // Hide the timeline button since we're already in timeline
              ),
            )),
            if (_activityLogs.isNotEmpty) ...[
              SizedBox(height: 2.h),
              Divider(
                color: colorScheme.outline.withValues(alpha: 0.3),
                thickness: 1,
              ),
              SizedBox(height: 2.h),
            ],
          ],
          
          // Logged Activities Section
          if (_activityLogs.isNotEmpty) ...[
            Text(
              'Logged Activities',
              style: TextStyle(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 1.h),
            ..._activityLogs.map((log) => Padding(
              padding: EdgeInsets.only(bottom: 1.h),
              child: ActivityLogItem(activity: log),
            )),
          ],
        ],
      ),
    );
  }

  Future<void> _markActivityCompleted(ScheduledActivity activity) async {
    try {
      final supabaseService = SupabaseService();
      await supabaseService.markScheduledActivityCompleted(activity.id);
      _fetchActivitiesForSelectedDate(); // Refresh the list
    } catch (e) {
      debugPrint('❌ Error marking activity as completed: $e');
      // Could show snackbar here
    }
  }

  Future<void> _deleteActivity(ScheduledActivity activity) async {
    try {
      final supabaseService = SupabaseService();
      await supabaseService.deleteScheduledActivity(activity.id);
      _fetchActivitiesForSelectedDate(); // Refresh the list
    } catch (e) {
      debugPrint('❌ Error deleting activity: $e');
      // Could show snackbar here
    }
  }

  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.event_note_outlined,
            size: 64,
            color: colorScheme.onSurface.withValues(alpha: 0.3),
          ),
          SizedBox(height: 2.h),
          Text(
            'No activities for this date',
            style: TextStyle(
              fontSize: 16.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Select a different date or add some activities',
            style: TextStyle(
              fontSize: 12.sp,
              color: colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
