import '../../../models/milestone.dart';
import '../../../models/baby_profile.dart';

/// Calculator for milestone statistics and metrics
class MilestoneStatsCalculator {
  final List<Milestone> milestones;
  final BabyProfile babyProfile;
  
  const MilestoneStatsCalculator({
    required this.milestones,
    required this.babyProfile,
  });
  
  /// Calculate total number of milestones
  int get totalMilestones => milestones.length;
  
  /// Calculate milestones achieved in the last 30 days
  int get recentMilestones {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return milestones.where((m) => m.achievedDate.isAfter(thirtyDaysAgo)).length;
  }
  
  /// Calculate baby's age in months
  int get babyAgeInMonths {
    final babyAge = DateTime.now().difference(babyProfile.birthDate);
    return babyAge.inDays ~/ 30;
  }
  
  /// Calculate average milestones per month
  double get averageMilestonesPerMonth {
    final ageInMonths = babyAgeInMonths;
    return ageInMonths > 0 ? totalMilestones / ageInMonths : 0.0;
  }
  
  /// Get milestone breakdown by category
  Map<MilestoneCategory, int> get categoryBreakdown {
    final categoryStats = <MilestoneCategory, int>{};
    for (final milestone in milestones) {
      categoryStats[milestone.category] = (categoryStats[milestone.category] ?? 0) + 1;
    }
    return categoryStats;
  }
  
  /// Get recent milestones sorted by date
  List<Milestone> get recentMilestonesList {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    return milestones
        .where((m) => m.achievedDate.isAfter(thirtyDaysAgo))
        .toList()
      ..sort((a, b) => b.achievedDate.compareTo(a.achievedDate));
  }
  
  /// Calculate development progress metrics
  DevelopmentProgress get developmentProgress {
    final ageInMonths = babyAgeInMonths;
    final expectedMilestones = MilestoneTemplates.getTemplatesForAge(ageInMonths);
    
    return DevelopmentProgress(
      achievedCount: totalMilestones,
      expectedCount: expectedMilestones.length,
      progressPercentage: expectedMilestones.isNotEmpty 
          ? (totalMilestones / expectedMilestones.length).clamp(0.0, 1.0)
          : 0.0,
      isOnTrack: totalMilestones >= expectedMilestones.length,
    );
  }
}

/// Data class for development progress metrics
class DevelopmentProgress {
  final int achievedCount;
  final int expectedCount;
  final double progressPercentage;
  final bool isOnTrack;
  
  const DevelopmentProgress({
    required this.achievedCount,
    required this.expectedCount,
    required this.progressPercentage,
    required this.isOnTrack,
  });
}