import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../models/milestone.dart';
import '../../../theme/theme_aware_colors.dart';

class MilestoneCardWidget extends StatelessWidget {
  final Milestone milestone;
  final bool isCompact;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const MilestoneCardWidget({
    super.key,
    required this.milestone,
    this.isCompact = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.only(bottom: isCompact ? 1.h : 2.h),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(isCompact ? 3.w : 4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: milestone.category.color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      milestone.category.icon,
                      color: milestone.category.color,
                      size: isCompact ? 18.sp : 20.sp,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          milestone.title,
                          style: TextStyle(
                            fontSize: isCompact ? 14.sp : 16.sp,
                            fontWeight: FontWeight.w600,
                            color: ThemeAwareColors.getPrimaryTextColor(context),
                          ),
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          milestone.category.displayName,
                          style: TextStyle(
                            fontSize: isCompact ? 11.sp : 12.sp,
                            color: milestone.category.color,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (!isCompact) ...[
                    PopupMenuButton<String>(
                      onSelected: (value) {
                        switch (value) {
                          case 'edit':
                            onEdit?.call();
                            break;
                          case 'delete':
                            onDelete?.call();
                            break;
                        }
                      },
                      itemBuilder: (context) => [
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit, size: 18),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, size: 18, color: ThemeAwareColors.getErrorColor(context)),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: ThemeAwareColors.getErrorColor(context))),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
              if (!isCompact) ...[
                SizedBox(height: 2.h),
                Text(
                  milestone.description,
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                    height: 1.4,
                  ),
                ),
                SizedBox(height: 2.h),
                Row(
                  children: [
                    Icon(
                      Icons.calendar_today,
                      size: 14.sp,
                      color: ThemeAwareColors.getDisabledTextColor(context),
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      '${milestone.achievedDate.day}/${milestone.achievedDate.month}/${milestone.achievedDate.year}',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(width: 4.w),
                    Icon(
                      Icons.child_care,
                      size: 14.sp,
                      color: ThemeAwareColors.getDisabledTextColor(context),
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      '${milestone.ageInMonths}m ${milestone.ageInDays % 30}d',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
                if (milestone.notes != null && milestone.notes!.isNotEmpty) ...[
                  SizedBox(height: 1.5.h),
                  Container(
                    padding: EdgeInsets.all(3.w),
                    decoration: BoxDecoration(
                      color: ThemeAwareColors.getCardColor(context).withOpacity(0.5),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Icon(
                          Icons.note,
                          size: 14.sp,
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                        SizedBox(width: 2.w),
                        Expanded(
                          child: Text(
                            milestone.notes!,
                            style: TextStyle(
                              fontSize: 12.sp,
                              color: ThemeAwareColors.getSecondaryTextColor(context),
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ],
          ),
        ),
      ),
    );
  }
}