import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../models/milestone.dart';
import '../../../theme/theme_aware_colors.dart';

class MilestoneFilterWidget extends StatefulWidget {
  final MilestoneCategory? selectedCategory;
  final bool showOnlyRecent;
  final Function(MilestoneCategory?, bool) onFilterChanged;

  const MilestoneFilterWidget({
    super.key,
    this.selectedCategory,
    required this.showOnlyRecent,
    required this.onFilterChanged,
  });

  @override
  State<MilestoneFilterWidget> createState() => _MilestoneFilterWidgetState();
}

class _MilestoneFilterWidgetState extends State<MilestoneFilterWidget> {
  MilestoneCategory? _selectedCategory;
  bool _showOnlyRecent = false;

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
    _showOnlyRecent = widget.showOnlyRecent;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Filter Milestones'),
      content: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Category',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: [
                _buildCategoryChip(null, 'All Categories'),
                ...MilestoneCategory.values.map((category) => 
                  _buildCategoryChip(category, category.displayName)
                ),
              ],
            ),
            SizedBox(height: 3.h),
            Text(
              'Time Period',
              style: TextStyle(
                fontSize: 16.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            CheckboxListTile(
              title: const Text('Show only recent (last 30 days)'),
              value: _showOnlyRecent,
              onChanged: (value) {
                setState(() => _showOnlyRecent = value ?? false);
              },
              contentPadding: EdgeInsets.zero,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            widget.onFilterChanged(_selectedCategory, _showOnlyRecent);
            Navigator.pop(context);
          },
          child: const Text('Apply'),
        ),
      ],
    );
  }

  Widget _buildCategoryChip(MilestoneCategory? category, String label) {
    final isSelected = _selectedCategory == category;
    final color = category?.color ?? ThemeAwareColors.getSecondaryTextColor(context);
    
    return GestureDetector(
      onTap: () => setState(() => _selectedCategory = category),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: isSelected ? color : ThemeAwareColors.getSurfaceColor(context),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? color : ThemeAwareColors.getDividerColor(context),
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (category != null) ...[
              Icon(
                category.icon,
                size: 14.sp,
                color: isSelected ? Theme.of(context).colorScheme.onPrimary : color,
              ),
              SizedBox(width: 1.w),
            ],
            Text(
              label,
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
                color: isSelected ? Theme.of(context).colorScheme.onPrimary : ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
          ],
        ),
      ),
    );
  }
}