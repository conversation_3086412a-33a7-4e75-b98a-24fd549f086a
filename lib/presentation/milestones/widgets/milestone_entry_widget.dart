import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../models/milestone.dart';
import '../../../models/baby_profile.dart';
import '../../../widgets/modern_date_time_picker.dart';
import '../../../theme/theme_aware_colors.dart';

class MilestoneEntryWidget extends StatefulWidget {
  final BabyProfile babyProfile;
  final Milestone? milestone; // For editing existing milestone

  const MilestoneEntryWidget({
    super.key,
    required this.babyProfile,
    this.milestone,
  });

  @override
  State<MilestoneEntryWidget> createState() => _MilestoneEntryWidgetState();
}

class _MilestoneEntryWidgetState extends State<MilestoneEntryWidget> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _notesController = TextEditingController();
  
  DateTime _achievedDate = DateTime.now();
  MilestoneCategory _selectedCategory = MilestoneCategory.motor;
  MilestoneType _selectedType = MilestoneType.gross_motor;
  bool _isCustom = false;
  
  List<Map<String, dynamic>> _templates = [];
  Map<String, dynamic>? _selectedTemplate;

  @override
  void initState() {
    super.initState();
    _loadTemplates();
    
    if (widget.milestone != null) {
      _populateFromMilestone(widget.milestone!);
    }
  }

  void _loadTemplates() {
    final ageInMonths = DateTime.now().difference(widget.babyProfile.birthDate).inDays ~/ 30;
    _templates = MilestoneTemplates.getTemplatesForAge(ageInMonths);
  }

  void _populateFromMilestone(Milestone milestone) {
    _titleController.text = milestone.title;
    _descriptionController.text = milestone.description;
    _notesController.text = milestone.notes ?? '';
    _achievedDate = milestone.achievedDate;
    _selectedCategory = milestone.category;
    _selectedType = milestone.type;
    _isCustom = milestone.isCustom;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 90.h,
      decoration: BoxDecoration(
        color: ThemeAwareColors.getBackgroundColor(context),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          _buildHeader(),
          Expanded(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.milestone == null) _buildTemplateSection(),
                    _buildCategorySection(),
                    SizedBox(height: 3.h),
                    _buildTitleField(),
                    SizedBox(height: 3.h),
                    _buildDescriptionField(),
                    SizedBox(height: 3.h),
                    _buildDateSection(),
                    SizedBox(height: 3.h),
                    _buildNotesField(),
                    SizedBox(height: 4.h),
                    _buildSaveButton(),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.close, color: ThemeAwareColors.getPrimaryTextColor(context)),
          ),
          Expanded(
            child: Text(
              widget.milestone == null ? 'Add Milestone' : 'Edit Milestone',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w700,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(width: 12.w), // Balance the close button
        ],
      ),
    );
  }

  Widget _buildTemplateSection() {
    if (_templates.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Templates',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        Text(
          'Choose from age-appropriate milestones',
          style: TextStyle(
            fontSize: 12.sp,
            color: ThemeAwareColors.getSecondaryTextColor(context),
          ),
        ),
        SizedBox(height: 2.h),
        SizedBox(
          height: 12.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _templates.length,
            itemBuilder: (context, index) {
              final template = _templates[index];
              final isSelected = _selectedTemplate == template;
              
              return GestureDetector(
                onTap: () => _selectTemplate(template),
                child: Container(
                  width: 40.w,
                  margin: EdgeInsets.only(right: 3.w),
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: isSelected ? ThemeAwareColors.getPrimaryColor(context).withOpacity(0.1) : ThemeAwareColors.getSurfaceColor(context),
                    borderRadius: BorderRadius.circular(12),
                    border: isSelected ? Border.all(color: ThemeAwareColors.getPrimaryColor(context)) : null,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        template['title'],
                        style: TextStyle(
                          fontSize: 13.sp,
                          fontWeight: FontWeight.w600,
                          color: isSelected ? ThemeAwareColors.getPrimaryColor(context) : ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 1.h),
                      Text(
                        template['description'],
                        style: TextStyle(
                          fontSize: 11.sp,
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: 3.h),
      ],
    );
  }

  Widget _buildCategorySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Category',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),
        Wrap(
          spacing: 2.w,
          runSpacing: 1.h,
          children: MilestoneCategory.values.map((category) {
            final isSelected = _selectedCategory == category;
            return GestureDetector(
              onTap: () => setState(() => _selectedCategory = category),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                decoration: BoxDecoration(
                  color: isSelected ? category.color : ThemeAwareColors.getSurfaceColor(context),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      category.icon,
                      size: 16.sp,
                      color: isSelected ? Theme.of(context).colorScheme.onPrimary : category.color,
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      category.displayName,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: isSelected ? Theme.of(context).colorScheme.onPrimary : ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildTitleField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Title',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: _titleController,
          decoration: InputDecoration(
            hintText: 'e.g., First Steps, First Words',
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a title';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDescriptionField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Description',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: _descriptionController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Describe the milestone achievement...',
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
            ),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter a description';
            }
            return null;
          },
        ),
      ],
    );
  }

  Widget _buildDateSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Achievement Date',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        GestureDetector(
          onTap: _selectDate,
          child: Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              border: Border.all(color: ThemeAwareColors.getOutlineColor(context)),
              borderRadius: BorderRadius.circular(12),
              color: Theme.of(context).colorScheme.surface,
            ),
            child: Row(
              children: [
                Icon(Icons.calendar_today, color: ThemeAwareColors.getSecondaryTextColor(context)),
                SizedBox(width: 3.w),
                Text(
                  '${_achievedDate.day}/${_achievedDate.month}/${_achievedDate.year}',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                  ),
                ),
                const Spacer(),
                Icon(Icons.arrow_drop_down, color: ThemeAwareColors.getSecondaryTextColor(context)),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotesField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notes (Optional)',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: InputDecoration(
            hintText: 'Add any additional notes or context...',
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _saveMilestone,
        style: ElevatedButton.styleFrom(
          backgroundColor: ThemeAwareColors.getPrimaryColor(context),
          foregroundColor: Theme.of(context).colorScheme.onPrimary,
          padding: EdgeInsets.symmetric(vertical: 2.h),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          widget.milestone == null ? 'Save Milestone' : 'Update Milestone',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  void _selectTemplate(Map<String, dynamic> template) {
    setState(() {
      _selectedTemplate = template;
      _titleController.text = template['title'];
      _descriptionController.text = template['description'];
      _selectedCategory = template['category'];
      _selectedType = template['type'];
      _isCustom = false;
    });
  }

  void _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _achievedDate,
              firstDate: widget.babyProfile.birthDate,
      lastDate: DateTime.now(),
    );
    
    if (date != null) {
      setState(() => _achievedDate = date);
    }
  }

  void _saveMilestone() {
    if (!_formKey.currentState!.validate()) return;

          final ageAtAchievement = _achievedDate.difference(widget.babyProfile.birthDate);
    final ageInMonths = ageAtAchievement.inDays ~/ 30;
    final ageInDays = ageAtAchievement.inDays;

    final milestone = Milestone(
      id: widget.milestone?.id,
      babyId: widget.babyProfile.id,
      title: _titleController.text.trim(),
      description: _descriptionController.text.trim(),
      category: _selectedCategory,
      type: _selectedType,
      achievedDate: _achievedDate,
      ageInMonths: ageInMonths,
      ageInDays: ageInDays,
      notes: _notesController.text.trim().isEmpty ? null : _notesController.text.trim(),
      isCustom: _selectedTemplate == null,
      createdAt: widget.milestone?.createdAt,
      updatedAt: DateTime.now(),
    );

    Navigator.pop(context, milestone);
  }
}