import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../models/milestone.dart';
import '../../../models/baby_profile.dart';
import '../../../theme/theme_aware_colors.dart';
import '../../../theme/milestone_theme_colors.dart';
import 'milestone_stats_calculator.dart';

class MilestoneStatsWidget extends StatelessWidget {
  final List<Milestone> milestones;
  final BabyProfile babyProfile;
  late final MilestoneStatsCalculator _calculator;

  MilestoneStatsWidget({
    super.key,
    required this.milestones,
    required this.babyProfile,
  }) {
    _calculator = MilestoneStatsCalculator(
      milestones: milestones,
      babyProfile: babyProfile,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (milestones.isEmpty) {
      return _buildEmptyState(context);
    }

    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverviewCard(context),
          SizedBox(height: 3.h),
          _buildCategoryBreakdown(context),
          SizedBox(height: 3.h),
          _buildRecentAchievements(context),
          SizedBox(height: 3.h),
          _buildDevelopmentProgress(context),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.bar_chart,
            size: 64.sp,
            color: ThemeAwareColors.getDisabledTextColor(context),
          ),
          SizedBox(height: 2.h),
          Text(
            'No statistics available',
            style: TextStyle(
              fontSize: 16.sp,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          Text(
            'Add milestones to see statistics',
            style: TextStyle(
              fontSize: 12.sp,
              color: ThemeAwareColors.getDisabledTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewCard(BuildContext context) {
    final totalMilestones = _calculator.totalMilestones;
    final recentMilestones = _calculator.recentMilestones;
    final ageInMonths = _calculator.babyAgeInMonths;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Overview',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Total Milestones',
                    totalMilestones.toString(),
                    Icons.emoji_events,
                    MilestoneThemeColors.getTotalMilestonesColor(context),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'This Month',
                    recentMilestones.toString(),
                    Icons.trending_up,
                    MilestoneThemeColors.getThisMonthColor(context),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Baby Age',
                    '${ageInMonths}m',
                    Icons.child_care,
                    MilestoneThemeColors.getBabyAgeColor(context),
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    context,
                    'Avg per Month',
                    ageInMonths > 0 ? (totalMilestones / ageInMonths).toStringAsFixed(1) : '0',
                    Icons.analytics,
                    MilestoneThemeColors.getAverageColor(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(BuildContext context, String label, String value, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24.sp),
          SizedBox(height: 1.h),
          Text(
            value,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 11.sp,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryBreakdown(BuildContext context) {
    final categoryStats = _calculator.categoryBreakdown;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Category Breakdown',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(height: 2.h),
            ...categoryStats.entries.map((entry) {
              final category = entry.key;
              final count = entry.value;
              final percentage = (count / milestones.length * 100).round();
              
              return Padding(
                padding: EdgeInsets.only(bottom: 2.h),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Icon(category.icon, color: category.color, size: 20.sp),
                        SizedBox(width: 3.w),
                        Expanded(
                          child: Text(
                            category.displayName,
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        Text(
                          '$count ($percentage%)',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: ThemeAwareColors.getSecondaryTextColor(context),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 1.h),
                    LinearProgressIndicator(
                      value: count / milestones.length,
                      backgroundColor: MilestoneThemeColors.getProgressBackgroundColor(context),
                      valueColor: AlwaysStoppedAnimation<Color>(category.color),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildRecentAchievements(BuildContext context) {
    final recentMilestones = _calculator.recentMilestonesList;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Achievements (Last 30 Days)',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(height: 2.h),
            if (recentMilestones.isEmpty)
              Text(
                'No recent milestones',
                style: TextStyle(
                  fontSize: 14.sp,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              )
            else
              ...recentMilestones.take(5).map((milestone) {
                final daysAgo = DateTime.now().difference(milestone.achievedDate).inDays;
                return Padding(
                  padding: EdgeInsets.only(bottom: 2.h),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(2.w),
                        decoration: BoxDecoration(
                          color: milestone.category.color.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          milestone.category.icon,
                          color: milestone.category.color,
                          size: 16.sp,
                        ),
                      ),
                      SizedBox(width: 3.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              milestone.title,
                              style: TextStyle(
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            Text(
                              daysAgo == 0 ? 'Today' : '$daysAgo days ago',
                              style: TextStyle(
                                fontSize: 12.sp,
                                color: ThemeAwareColors.getSecondaryTextColor(context),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildDevelopmentProgress(BuildContext context) {
    final progress = _calculator.developmentProgress;
    final ageInMonths = _calculator.babyAgeInMonths;

    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Development Progress',
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.w700,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              'Based on typical milestones for ${ageInMonths} months',
              style: TextStyle(
                fontSize: 12.sp,
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progress',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 1.h),
                      LinearProgressIndicator(
                        value: progress.progressPercentage,
                        backgroundColor: MilestoneThemeColors.getProgressBackgroundColor(context),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          progress.isOnTrack 
                              ? ThemeAwareColors.getSuccessColor(context)
                              : ThemeAwareColors.getInfoColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 4.w),
                Text(
                  '${progress.achievedCount} / ${progress.expectedCount}',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.w700,
                    color: progress.isOnTrack 
                        ? ThemeAwareColors.getSuccessColor(context)
                        : ThemeAwareColors.getInfoColor(context),
                  ),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            if (progress.isOnTrack)
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getSuccessColor(context).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.celebration, color: ThemeAwareColors.getSuccessColor(context), size: 20.sp),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        'Great progress! Your baby is meeting expected milestones.',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: ThemeAwareColors.getSuccessColor(context),
                        ),
                      ),
                    ),
                  ],
                ),
              )
            else
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getInfoColor(context).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: ThemeAwareColors.getInfoColor(context), size: 20.sp),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        'Keep tracking! Every baby develops at their own pace.',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: ThemeAwareColors.getInfoColor(context),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}