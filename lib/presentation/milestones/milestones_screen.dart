import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../core/app_export.dart';
import '../../models/milestone.dart';
import '../../models/baby_profile.dart';
import '../../models/activity_log.dart';
import '../../services/supabase_service.dart';
import '../../services/ai_insights_service.dart';
import '../../services/ai_insights_state_manager.dart';
import '../../theme/theme_aware_colors.dart';
import 'widgets/milestone_card_widget.dart';
import 'widgets/milestone_entry_widget.dart';
import 'widgets/milestone_filter_widget.dart';
import 'widgets/milestone_stats_widget.dart';
import '../../widgets/shared/recent_activities_widget.dart';

class MilestonesScreen extends StatefulWidget {
  final BabyProfile babyProfile;

  const MilestonesScreen({
    super.key,
    required this.babyProfile,
  });

  @override
  State<MilestonesScreen> createState() => _MilestonesScreenState();
}

class _MilestonesScreenState extends State<MilestonesScreen>
    with TickerProviderStateMixin {
  final SupabaseService _supabaseService = SupabaseService();
  final AIInsightsService _aiInsightsService = AIInsightsService();
  
  late TabController _tabController;
  List<Milestone> _milestones = [];
  List<Milestone> _filteredMilestones = [];
  List<Map<String, dynamic>> _recentActivities = [];
  MilestoneCategory? _selectedCategory;
  bool _isLoading = true;
  bool _showOnlyRecent = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadMilestones();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadMilestones() async {
    setState(() => _isLoading = true);
    
    try {
      final milestones = await _supabaseService.getMilestones(widget.babyProfile.id);
      final activities = await _supabaseService.getRecentActivities(
        widget.babyProfile.id,
        limit: 10,
      );
      
      setState(() {
        _milestones = milestones;
        _filteredMilestones = milestones;
        _recentActivities = activities.map((a) => a.toRecentActivityMap()).toList();
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading milestones: $e')),
        );
      }
    }
  }

  void _filterMilestones() {
    setState(() {
      _filteredMilestones = _milestones.where((milestone) {
        bool categoryMatch = _selectedCategory == null || 
                           milestone.category == _selectedCategory;
        
        bool recentMatch = !_showOnlyRecent || 
                          DateTime.now().difference(milestone.achievedDate).inDays <= 30;
        
        return categoryMatch && recentMatch;
      }).toList();
      
      // Sort by achieved date (most recent first)
      _filteredMilestones.sort((a, b) => b.achievedDate.compareTo(a.achievedDate));
    });
  }

  Future<void> _addMilestone() async {
    final result = await showModalBottomSheet<Milestone>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MilestoneEntryWidget(
        babyProfile: widget.babyProfile,
      ),
    );

    if (result != null) {
      try {
        await _supabaseService.saveMilestone(result);
        await _loadMilestones();
        
        // Trigger AI insights update
        _triggerAIInsightsUpdate();
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Milestone saved successfully!'),
              backgroundColor: ThemeAwareColors.getSuccessColor(context),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error saving milestone: $e')),
          );
        }
      }
    }
  }

  Future<void> _triggerAIInsightsUpdate() async {
    try {
      // Get recent activities to provide context for AI analysis
      final recentActivities = await _supabaseService.getRecentActivities(
        widget.babyProfile.id,
        limit: 50,
      );
      
      // Use state manager instead of direct service call to prevent duplicate API calls
      final stateManager = AIInsightsStateManager();
      await stateManager.loadInsights(widget.babyProfile);
    } catch (e) {
      print('Error updating AI insights: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeAwareColors.getSurfaceColor(context),
      appBar: AppBar(
        title: Text(
          'Milestones',
          style: TextStyle(
            fontWeight: FontWeight.w700,
            fontSize: 18.sp,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        backgroundColor: ThemeAwareColors.getBackgroundColor(context),
        elevation: 0,
        foregroundColor: ThemeAwareColors.getPrimaryTextColor(context),
        bottom: TabBar(
          controller: _tabController,
          labelColor: ThemeAwareColors.getPrimaryColor(context),
          unselectedLabelColor: ThemeAwareColors.getSecondaryTextColor(context),
          indicatorColor: ThemeAwareColors.getPrimaryColor(context),
          tabs: const [
            Tab(text: 'Timeline'),
            Tab(text: 'Categories'),
            Tab(text: 'Statistics'),
          ],
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildTimelineView(),
                _buildCategoriesView(),
                _buildStatisticsView(),
              ],
            ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addMilestone,
        icon: const Icon(Icons.add),
        label: const Text('Add Milestone'),
        backgroundColor: ThemeAwareColors.getPrimaryColor(context),
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
    );
  }

  Widget _buildTimelineView() {
    if (_filteredMilestones.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadMilestones,
      child: ListView.builder(
        padding: EdgeInsets.all(4.w),
        itemCount: _filteredMilestones.length,
        itemBuilder: (context, index) {
          final milestone = _filteredMilestones[index];
          return MilestoneCardWidget(
            milestone: milestone,
            onTap: () => _viewMilestoneDetails(milestone),
            onEdit: () => _editMilestone(milestone),
            onDelete: () => _deleteMilestone(milestone),
          );
        },
      ),
    );
  }

  Widget _buildCategoriesView() {
    final categorizedMilestones = <MilestoneCategory, List<Milestone>>{};
    
    for (final milestone in _filteredMilestones) {
      categorizedMilestones.putIfAbsent(milestone.category, () => []).add(milestone);
    }

    if (categorizedMilestones.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: EdgeInsets.all(4.w),
      itemCount: categorizedMilestones.length,
      itemBuilder: (context, index) {
        final category = categorizedMilestones.keys.elementAt(index);
        final milestones = categorizedMilestones[category]!;
        
        return Card(
          margin: EdgeInsets.only(bottom: 3.h),
          child: ExpansionTile(
            leading: Icon(
              category.icon,
              color: category.color,
              size: 24.sp,
            ),
            title: Text(
              category.displayName,
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 16.sp,
              ),
            ),
            subtitle: Text('${milestones.length} milestones'),
            children: milestones.map((milestone) => 
              MilestoneCardWidget(
                milestone: milestone,
                isCompact: true,
                onTap: () => _viewMilestoneDetails(milestone),
                onEdit: () => _editMilestone(milestone),
                onDelete: () => _deleteMilestone(milestone),
              )
            ).toList(),
          ),
        );
      },
    );
  }

  Widget _buildStatisticsView() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Milestone statistics
          MilestoneStatsWidget(
            milestones: _milestones,
            babyProfile: widget.babyProfile,
          ),
          
          // Recent activities section
          SizedBox(height: 4.h),
          RecentActivitiesWidget(
            activities: _recentActivities,
          ),
          
          // Bottom padding for floating action button
          SizedBox(height: 10.h),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.emoji_events_outlined,
            size: 64.sp,
            color: ThemeAwareColors.getDisabledTextColor(context),
          ),
          SizedBox(height: 2.h),
          Text(
            'No milestones yet',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Start tracking your baby\'s achievements!',
            style: TextStyle(
              fontSize: 14.sp,
              color: ThemeAwareColors.getDisabledTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          ElevatedButton.icon(
            onPressed: _addMilestone,
            icon: const Icon(Icons.add),
            label: const Text('Add First Milestone'),
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeAwareColors.getPrimaryColor(context),
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
            ),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => MilestoneFilterWidget(
        selectedCategory: _selectedCategory,
        showOnlyRecent: _showOnlyRecent,
        onFilterChanged: (category, showRecent) {
          setState(() {
            _selectedCategory = category;
            _showOnlyRecent = showRecent;
          });
          _filterMilestones();
        },
      ),
    );
  }

  void _viewMilestoneDetails(Milestone milestone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        title: Text(
          milestone.title,
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                milestone.description,
                style: TextStyle(
                  fontSize: 14.sp,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 2.h),
              Row(
                children: [
                  Icon(
                    milestone.category.icon, 
                    size: 16.sp,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    milestone.category.displayName,
                    style: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 1.h),
              Text(
                'Achieved: ${milestone.achievedDate.day}/${milestone.achievedDate.month}/${milestone.achievedDate.year}',
                style: TextStyle(
                  fontSize: 12.sp,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
              if (milestone.notes != null) ...[
                SizedBox(height: 2.h),
                Text(
                  'Notes:',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                Text(
                  milestone.notes!,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Close',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _editMilestone(Milestone milestone) {
    showModalBottomSheet<Milestone>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => MilestoneEntryWidget(
        babyProfile: widget.babyProfile,
        milestone: milestone,
      ),
    ).then((result) {
      if (result != null) {
        _supabaseService.saveMilestone(result).then((_) {
          _loadMilestones();
          _triggerAIInsightsUpdate();
        });
      }
    });
  }

  void _deleteMilestone(Milestone milestone) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        title: Text(
          'Delete Milestone',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Are you sure you want to delete "${milestone.title}"?',
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Cancel',
              style: TextStyle(
                color: Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await _supabaseService.deleteMilestone(milestone.id);
                await _loadMilestones();
                _triggerAIInsightsUpdate();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('Milestone deleted'),
                      backgroundColor: ThemeAwareColors.getErrorColor(context),
                    ),
                  );
                }
              } catch (e) {
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Error deleting milestone: $e')),
                  );
                }
              }
            },
            child: Text(
              'Delete',
              style: TextStyle(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
          ),
        ],
      ),
    );
  }
}