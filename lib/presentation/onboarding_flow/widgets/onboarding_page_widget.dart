import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../widgets/custom_image_widget.dart';

/// Reusable widget for individual onboarding pages
/// Contains illustration, headline, and descriptive text
class OnboardingPageWidget extends StatelessWidget {
  final String imagePath;
  final String title;
  final String description;
  final Color? backgroundColor;

  const OnboardingPageWidget({
    super.key,
    required this.imagePath,
    required this.title,
    required this.description,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(horizontal: 6.w),
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
          // Illustration
          Container(
              width: 70.w,
              height: 40.h,
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: backgroundColor ?? theme.colorScheme.surface),
              child: Center(
                  child: CustomImageWidget(
                      imageUrl: imagePath,
                      width: 60.w,
                      height: 35.h,
                      fit: BoxFit.contain))),

          SizedBox(height: 6.h),

          // Title
          Text(title,
              style: GoogleFonts.inter(
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w700,
                  color: theme.colorScheme.onSurface,
                  letterSpacing: -0.5),
              textAlign: TextAlign.center),

          SizedBox(height: 2.h),

          // Description
          Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text(description,
                  style: GoogleFonts.inter(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w400,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      height: 1.5,
                      letterSpacing: 0.2),
                  textAlign: TextAlign.center)),
        ]));
  }
}
