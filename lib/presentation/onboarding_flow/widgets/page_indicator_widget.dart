import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

/// Widget that displays page indicators (dots) for onboarding flow
/// Shows current page progress with smooth animations
class PageIndicatorWidget extends StatelessWidget {
  final int currentPage;
  final int totalPages;
  final Color? activeColor;
  final Color? inactiveColor;

  const PageIndicatorWidget({
    super.key,
    required this.currentPage,
    required this.totalPages,
    this.activeColor,
    this.inactiveColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final activeIndicatorColor = activeColor ?? theme.colorScheme.primary;
    final inactiveIndicatorColor =
        inactiveColor ?? theme.colorScheme.onSurface.withValues(alpha: 0.3);

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        totalPages,
        (index) => AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          margin: EdgeInsets.symmetric(horizontal: 1.w),
          width: currentPage == index ? 6.w : 2.w,
          height: 2.w,
          decoration: BoxDecoration(
            color: currentPage == index
                ? activeIndicatorColor
                : inactiveIndicatorColor,
            borderRadius: BorderRadius.circular(1.w),
          ),
        ),
      ),
    );
  }
}
