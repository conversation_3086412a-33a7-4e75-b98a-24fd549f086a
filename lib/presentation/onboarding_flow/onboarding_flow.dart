import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../routes/app_routes.dart';
import './widgets/onboarding_page_widget.dart';
import './widgets/page_indicator_widget.dart';

/// Onboarding Flow that introduces new users to core app features
/// Features engaging, swipeable screens with clear value propositions
/// Includes page indicators, navigation controls, and skip functionality
class OnboardingFlow extends StatefulWidget {
  const OnboardingFlow({super.key});

  @override
  State<OnboardingFlow> createState() => _OnboardingFlowState();
}

class _OnboardingFlowState extends State<OnboardingFlow> {
  final PageController _pageController = PageController();
  int _currentPage = 0;
  final int _totalPages = 4;

  // Onboarding page data
  final List<Map<String, String>> _onboardingData = [
    {
      'image':
          'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=800&h=600&fit=crop',
      'title': 'Track Your Baby\'s Journey',
      'description':
          'Easily log feeding times, sleep patterns, diaper changes, and daily activities with our intuitive interface designed for busy parents.',
    },
    {
      'image':
          'https://images.pexels.com/photos/3992213/pexels-photo-3992213.jpeg?w=800&h=600&fit=crop',
      'title': 'AI-Powered Insights',
      'description':
          'Get personalized recommendations and insights about your baby\'s health, development, and patterns using our advanced AI technology.',
    },
    {
      'image':
          'https://images.pixabay.com/photo/2017/11/23/08/25/baby-2972221_1280.jpg?w=800&h=600&fit=crop',
      'title': 'Growth Monitoring',
      'description':
          'Visualize your baby\'s growth with beautiful charts and milestone tracking. Share progress with family and healthcare providers.',
    },
    {
      'image':
          'https://images.unsplash.com/photo-1515488042361-ee00e0ddd4e4?w=800&h=600&fit=crop',
      'title': 'Family Connection',
      'description':
          'Connect with family members, share precious moments, and keep everyone updated on your baby\'s daily activities and milestones.',
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// Handle page changes with haptic feedback
  void _onPageChanged(int page) {
    setState(() => _currentPage = page);
    HapticFeedback.lightImpact();
  }

  /// Navigate to next page or complete onboarding
  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  /// Navigate to previous page
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// Skip onboarding and go to baby profile creation
  void _skipOnboarding() {
    _completeOnboarding();
  }

  /// Complete onboarding and navigate to baby profile creation
  Future<void> _completeOnboarding() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('has_completed_onboarding', true);

      if (mounted) {
        Navigator.of(context)
            .pushReplacementNamed(AppRoutes.babyProfileCreation);
      }
    } catch (error) {
      // Handle error gracefully
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Unable to save progress. Please try again.'),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // Header with skip button
            Padding(
              padding: EdgeInsets.all(4.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Back button (only show if not on first page)
                  SizedBox(
                    width: 12.w,
                    child: _currentPage > 0
                        ? IconButton(
                            onPressed: _previousPage,
                            icon: Icon(
                              Icons.arrow_back_ios,
                              color: theme.colorScheme.onSurface,
                              size: 5.w,
                            ),
                          )
                        : null,
                  ),

                  // Page indicator
                  PageIndicatorWidget(
                    currentPage: _currentPage,
                    totalPages: _totalPages,
                  ),

                  // Skip button
                  TextButton(
                    onPressed: _skipOnboarding,
                    child: Text(
                      'Skip',
                      style: GoogleFonts.inter(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                        color:
                            theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Page view with onboarding content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: _onPageChanged,
                itemCount: _totalPages,
                itemBuilder: (context, index) {
                  final data = _onboardingData[index];
                  return OnboardingPageWidget(
                    imagePath: data['image']!,
                    title: data['title']!,
                    description: data['description']!,
                    backgroundColor: index % 2 == 0
                        ? theme.colorScheme.surface
                        : theme.colorScheme.surface.withValues(alpha: 0.5),
                  );
                },
              ),
            ),

            // Bottom navigation
            Padding(
              padding: EdgeInsets.all(6.w),
              child: Row(
                children: [
                  // Progress text
                  Text(
                    '${_currentPage + 1} of $_totalPages',
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w400,
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
                  ),

                  const Spacer(),

                  // Next/Get Started button
                  ElevatedButton(
                    onPressed: _nextPage,
                    style: ElevatedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8.w,
                        vertical: 3.w,
                      ),
                    ),
                    child: Text(
                      _currentPage == _totalPages - 1 ? 'Get Started' : 'Next',
                      style: GoogleFonts.inter(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
