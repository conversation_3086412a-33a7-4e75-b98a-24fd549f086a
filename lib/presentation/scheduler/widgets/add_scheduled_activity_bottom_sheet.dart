import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';
import '../../../models/scheduled_activity.dart';
import '../../../services/unified_notification_service.dart';
import '../../../widgets/custom_elevated_button.dart';
import '../../../widgets/custom_text_form_field.dart';
import '../../../widgets/modern_date_time_picker.dart';
import '../../../utils/snackbar_helper.dart';

class AddScheduledActivityBottomSheet extends StatefulWidget {
  final BabyProfile babyProfile;
  final ValueChanged<ScheduledActivity> onActivityAdded;
  final ScheduledActivity? existingActivity; // For edit mode

  const AddScheduledActivityBottomSheet({
    super.key,
    required this.babyProfile,
    required this.onActivityAdded,
    this.existingActivity,
  });

  @override
  State<AddScheduledActivityBottomSheet> createState() =>
      _AddScheduledActivityBottomSheetState();
}

class _AddScheduledActivityBottomSheetState
    extends State<AddScheduledActivityBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final SupabaseService _supabaseService = SupabaseService();
  final UnifiedNotificationService _notificationService = UnifiedNotificationService.instance;

  ScheduledActivityType _selectedType = ScheduledActivityType.sleepReminder;
  DateTime _selectedDateTime = DateTime.now().add(Duration(hours: 1));
  int _notifyBeforeMinutes = 10;
  bool _isRecurring = false;
  RecurrencePattern _recurrencePattern = RecurrencePattern.daily;
  int _recurrenceInterval = 1;
  DateTime? _recurrenceEndDate;
  bool _isLoading = false;
  
  bool get _isEditMode => widget.existingActivity != null;

  @override
  void initState() {
    super.initState();
    _initializeForEditMode();
  }

  void _initializeForEditMode() {
    if (_isEditMode && widget.existingActivity != null) {
      final activity = widget.existingActivity!;
      _titleController.text = activity.title;
      _descriptionController.text = activity.description ?? '';
      _selectedType = activity.type;
      _selectedDateTime = activity.scheduledTime;
      _notifyBeforeMinutes = activity.notifyBeforeMinutes;
      _isRecurring = activity.isRecurring;
      _recurrencePattern = activity.recurrencePattern ?? RecurrencePattern.daily;
      _recurrenceInterval = activity.recurrenceInterval ?? 1;
      _recurrenceEndDate = activity.recurrenceEndDate;
    } else {
      // Set default title for new activities
      _titleController.text = _selectedType.displayName;
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDateTime() async {
    final picked = await ModernDateTimePicker.showDateTimePicker(
      context: context,
      initialDateTime: _selectedDateTime,
    );
    if (picked != null) {
      setState(() {
        _selectedDateTime = picked;
      });
    }
  }

  Future<void> _selectRecurrenceEndDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _recurrenceEndDate ?? DateTime.now().add(const Duration(days: 30)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _recurrenceEndDate = picked;
      });
    }
  }

  Future<void> _saveActivity() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
    });

    try {
      final scheduledDateTime = _selectedDateTime;

      if (_isEditMode) {
        // Update existing activity
        final updatedActivity = widget.existingActivity!.copyWith(
          type: _selectedType,
          title: _titleController.text,
          description: _descriptionController.text.isNotEmpty
              ? _descriptionController.text
              : null,
          scheduledTime: scheduledDateTime,
          notifyBeforeMinutes: _notifyBeforeMinutes,
          isRecurring: _isRecurring,
          recurrencePattern: _isRecurring ? _recurrencePattern : RecurrencePattern.none,
          recurrenceInterval: _recurrenceInterval,
          recurrenceEndDate: _isRecurring ? _recurrenceEndDate : null,
          updatedAt: DateTime.now(),
        );

        final savedActivity = await _supabaseService.updateScheduledActivity(updatedActivity);
        
        // Update notification for the scheduled activity
        if (_notifyBeforeMinutes > 0) {
          final notificationTime = scheduledDateTime.subtract(Duration(minutes: _notifyBeforeMinutes));
          
          // Only create notification if it's in the future
          if (notificationTime.isAfter(DateTime.now())) {
            await _notificationService.createScheduledActivityNotification(
              title: '${_titleController.text} Reminder',
              description: _descriptionController.text.isNotEmpty 
                  ? _descriptionController.text 
                  : 'Scheduled activity: ${_titleController.text}',
              scheduledTime: notificationTime,
              babyId: widget.babyProfile.id,
            );
            
            debugPrint('Updated notification for scheduled activity: ${savedActivity.title}');
          }
        } else {
          // Remove existing notification if notify is disabled
          debugPrint('Notification disabled for activity: ${savedActivity.title}');
        }
        
        widget.onActivityAdded(savedActivity);
        
        if (mounted) {
          Navigator.pop(context);
          SnackBarHelper.showSuccess(context, 'Scheduled activity updated successfully');
        }
      } else {
        // Create new activity
        final activity = ScheduledActivity(
          id: '', // Will be generated by database
          babyId: widget.babyProfile.id,
          type: _selectedType,
          title: _titleController.text,
          description: _descriptionController.text.isNotEmpty
              ? _descriptionController.text
              : null,
          scheduledTime: scheduledDateTime,
          notifyBeforeMinutes: _notifyBeforeMinutes,
          isRecurring: _isRecurring,
          recurrencePattern: _isRecurring ? _recurrencePattern : RecurrencePattern.none,
          recurrenceInterval: _recurrenceInterval,
          recurrenceEndDate: _isRecurring ? _recurrenceEndDate : null,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final createdActivity = await _supabaseService.createScheduledActivity(activity);
        
        // Create notification for the scheduled activity
        if (_notifyBeforeMinutes > 0) {
          final notificationTime = scheduledDateTime.subtract(Duration(minutes: _notifyBeforeMinutes));
          
          // Only create notification if it's in the future
          if (notificationTime.isAfter(DateTime.now())) {
            await _notificationService.createScheduledActivityNotification(
              title: '${_titleController.text} Reminder',
              description: _descriptionController.text.isNotEmpty 
                  ? _descriptionController.text 
                  : 'Scheduled activity: ${_titleController.text}',
              scheduledTime: notificationTime,
              babyId: widget.babyProfile.id,
            );
            
            debugPrint('Created notification for scheduled activity: ${createdActivity.title}');
          }
        }
        
        widget.onActivityAdded(createdActivity);
        
        if (mounted) {
          Navigator.pop(context);
          SnackBarHelper.showSuccess(context, 'Scheduled activity created successfully');
        }
      }
    } catch (e) {
      debugPrint('Error ${_isEditMode ? 'updating' : 'creating'} scheduled activity: $e');
      if (mounted) {
        SnackBarHelper.showError(context, 'Failed to ${_isEditMode ? 'update' : 'create'} scheduled activity');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Widget _buildTypeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Activity Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        DropdownButtonFormField<ScheduledActivityType>(
          value: _selectedType,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
          ),
          items: ScheduledActivityType.values.map((type) {
            return DropdownMenuItem(
              value: type,
              child: Text(type.displayName),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedType = value;
                _titleController.text = value.displayName;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildDateTimeSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Date & Time',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        InkWell(
          onTap: _selectDateTime,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
            decoration: BoxDecoration(
              border: Border.all(color: Theme.of(context).colorScheme.outline),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Icon(Icons.schedule, size: 5.w, color: Theme.of(context).colorScheme.primary),
                SizedBox(width: 3.w),
                Text(
                  DateFormat('MMM d, y \'at\' h:mm a').format(_selectedDateTime),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const Spacer(),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Notification',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        DropdownButtonFormField<int>(
          value: _notifyBeforeMinutes,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            contentPadding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
          ),
          items: [0, 5, 10, 15, 30, 60].map((minutes) {
            return DropdownMenuItem(
              value: minutes,
              child: Text(minutes == 0 ? 'No notification' : '$minutes minutes before'),
            );
          }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _notifyBeforeMinutes = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildRecurrenceSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Checkbox(
              value: _isRecurring,
              onChanged: (value) {
                setState(() {
                  _isRecurring = value ?? false;
                });
              },
            ),
            Text(
              'Recurring',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        
        if (_isRecurring) ...[
          SizedBox(height: 1.h),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: DropdownButtonFormField<RecurrencePattern>(
                  value: _recurrencePattern,
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                  ),
                  items: [
                    RecurrencePattern.daily,
                    RecurrencePattern.weekly,
                    RecurrencePattern.monthly,
                  ].map((pattern) {
                    return DropdownMenuItem(
                      value: pattern,
                      child: Text(pattern.displayName),
                    );
                  }).toList(),
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _recurrencePattern = value;
                      });
                    }
                  },
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: TextFormField(
                  initialValue: _recurrenceInterval.toString(),
                  keyboardType: TextInputType.number,
                  decoration: InputDecoration(
                    labelText: 'Every',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    contentPadding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                  ),
                  onChanged: (value) {
                    final interval = int.tryParse(value) ?? 1;
                    setState(() {
                      _recurrenceInterval = interval > 0 ? interval : 1;
                    });
                  },
                ),
              ),
            ],
          ),
          
          SizedBox(height: 2.h),
          InkWell(
            onTap: _selectRecurrenceEndDate,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              decoration: BoxDecoration(
                border: Border.all(color: Theme.of(context).colorScheme.outline),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(Icons.calendar_today, size: 5.w),
                  SizedBox(width: 2.w),
                  Text(_recurrenceEndDate != null
                      ? 'End: ${DateFormat('MMM d, yyyy').format(_recurrenceEndDate!)}'
                      : 'No end date'),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.8,
      maxChildSize: 0.9,
      minChildSize: 0.3,
      builder: (context, scrollController) {
        return Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                width: 15.w,
                height: 1.h,
                margin: EdgeInsets.only(top: 1.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.outline,
                  borderRadius: BorderRadius.circular(10),
                ),
              ),
              
              Padding(
                padding: EdgeInsets.all(4.w),
                child: Row(
                  children: [
                    IconButton(
                      icon: const Icon(Icons.arrow_back),
                      onPressed: () => Navigator.pop(context),
                      tooltip: 'Back',
                    ),
                    Expanded(
                      child: Text(
                        'Add Schedule',
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    SizedBox(width: 48), // Placeholder to center the title
                  ],
                ),
              ),
              
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: EdgeInsets.all(4.w),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTypeSelector(),
                        SizedBox(height: 3.h),
                        
                        CustomTextFormField(
                          controller: _titleController,
                          labelText: 'Title',
                          hintText: 'Enter activity title',
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Please enter a title';
                            }
                            return null;
                          },
                        ),
                        SizedBox(height: 3.h),
                        
                        CustomTextFormField(
                          controller: _descriptionController,
                          labelText: 'Description (Optional)',
                          hintText: 'Enter activity description',
                          maxLines: 3,
                        ),
                        SizedBox(height: 3.h),
                        
                        _buildDateTimeSelector(),
                        SizedBox(height: 3.h),
                        
                        _buildNotificationSelector(),
                        SizedBox(height: 3.h),
                        
                        _buildRecurrenceSelector(),
                        SizedBox(height: 5.h),
                        
                        CustomElevatedButton(
                          text: _isLoading ? 'Creating...' : 'Create Schedule',
                          onPressed: _isLoading ? null : _saveActivity,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}