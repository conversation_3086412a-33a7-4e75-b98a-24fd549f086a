import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../../core/app_export.dart';
import '../../../models/scheduled_activity.dart';
import '../../../utils/activity_icon_manager.dart';
import '../../../utils/activity_type_config.dart';

class ScheduledActivityCard extends StatelessWidget {
  final ScheduledActivity activity;
  final VoidCallback onCompleted;
  final VoidCallback onDeleted;
  final ValueChanged<ScheduledActivity>? onEdit;
  final bool showTimelineButton;

  const ScheduledActivityCard({
    super.key,
    required this.activity,
    required this.onCompleted,
    required this.onDeleted,
    this.onEdit,
    this.showTimelineButton = true,
  });


  String _getFormattedTime() {
    final nextOccurrence = activity.getNextOccurrence() ?? activity.scheduledTime;
    final now = DateTime.now();
    
    if (activity.isCompleted) {
      return 'Completed ${activity.completedAt != null ? DateFormat('MMM d, h:mm a').format(activity.completedAt!) : 'recently'}';
    }
    
    if (nextOccurrence.isBefore(now)) {
      return 'Overdue (${_formatDateTimeDisplay(nextOccurrence)})';
    }
    
    final difference = nextOccurrence.difference(now);
    final timeDisplay = _formatDateTimeDisplay(nextOccurrence);
    
    if (difference.inDays > 0) {
      return 'In ${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ($timeDisplay)';
    } else if (difference.inHours > 0) {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      
      if (minutes > 0) {
        return 'In $hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''} ($timeDisplay)';
      } else {
        return 'In $hours hour${hours > 1 ? 's' : ''} ($timeDisplay)';
      }
    } else if (difference.inMinutes > 0) {
      final minutes = difference.inMinutes;
      return 'In $minutes minute${minutes > 1 ? 's' : ''} ($timeDisplay)';
    } else {
      return 'Now ($timeDisplay)';
    }
  }

  String _formatDateTimeDisplay(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final tomorrow = today.add(Duration(days: 1));
    final yesterday = today.subtract(Duration(days: 1));
    final dateOnly = DateTime(dateTime.year, dateTime.month, dateTime.day);
    
    final timeStr = DateFormat('h:mm a').format(dateTime);
    
    if (dateOnly == today) {
      return 'Today at $timeStr';
    } else if (dateOnly == tomorrow) {
      return 'Tomorrow at $timeStr';
    } else if (dateOnly == yesterday) {
      return 'Yesterday at $timeStr';
    } else {
      return '${DateFormat('M/d/yyyy').format(dateTime)} at $timeStr';
    }
  }

  Widget _buildActionButtons(BuildContext context) {
    if (activity.isCompleted) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          IconButton(
            icon: Icon(Icons.delete, color: Colors.red),
            onPressed: onDeleted,
            tooltip: 'Delete',
          ),
        ],
      );
    }

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (onEdit != null)
          IconButton(
            icon: Icon(Icons.edit, color: Colors.blue),
            onPressed: () => onEdit!(activity),
            tooltip: 'Edit',
          ),
        IconButton(
          icon: Icon(Icons.check_circle, color: Colors.green),
          onPressed: onCompleted,
          tooltip: 'Mark as completed',
        ),
        IconButton(
          icon: Icon(Icons.delete, color: Colors.red),
          onPressed: onDeleted,
          tooltip: 'Delete',
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final activityColor = ActivityTypeConfig.getColor(activity.correspondingQuickLogActivityType, context);
    final nextOccurrence = activity.getNextOccurrence() ?? activity.scheduledTime;
    final now = DateTime.now();
    final isOverdue = nextOccurrence.isBefore(now) && !activity.isCompleted;

    return Card(
      margin: EdgeInsets.zero,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: activity.isCompleted 
            ? colorScheme.outline.withOpacity(0.2)
            : isOverdue 
              ? Colors.red.withOpacity(0.3)
              : activityColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              activity.isCompleted 
                ? colorScheme.surfaceVariant.withOpacity(0.3)
                : isOverdue 
                  ? Colors.red.withOpacity(0.1)
                  : activityColor.withOpacity(0.1),
              colorScheme.surface,
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 12.w,
                    height: 12.w,
                    decoration: BoxDecoration(
                      color: activity.isCompleted 
                        ? colorScheme.surfaceVariant
                        : activityColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: ActivityIconManager.getActivityIcon(
                      activityType: activity.correspondingQuickLogActivityType, // Use the Quick Log equivalent for consistency
                      size: 6.w,
                      color: activity.isCompleted 
                        ? colorScheme.onSurfaceVariant
                        : Colors.white,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          activity.title,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: colorScheme.onSurface,
                            fontWeight: FontWeight.w600,
                            decoration: activity.isCompleted 
                              ? TextDecoration.lineThrough
                              : null,
                          ),
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          activity.type.displayName,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildActionButtons(context),
                ],
              ),
              
              if (activity.description != null && activity.description!.isNotEmpty) ...[
                SizedBox(height: 2.h),
                Text(
                  activity.description!,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
              
              SizedBox(height: 2.h),
              
              Row(
                children: [
                  Icon(
                    Icons.schedule,
                    size: 4.w,
                    color: isOverdue ? Colors.red : colorScheme.onSurfaceVariant,
                  ),
                  SizedBox(width: 1.w),
                  Expanded(
                    child: Text(
                      _getFormattedTime(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: isOverdue ? Colors.red : colorScheme.onSurfaceVariant,
                        fontWeight: isOverdue ? FontWeight.w600 : FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
              
              if (activity.isRecurring) ...[
                SizedBox(height: 1.h),
                Row(
                  children: [
                    Icon(
                      Icons.repeat,
                      size: 4.w,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 1.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            activity.recurrenceDescription,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          if (activity.recurrenceEndDate != null) ...[
                            SizedBox(height: 0.5.h),
                            Text(
                              'Recurring: Start ${DateFormat('MMM d, yyyy').format(activity.scheduledTime)} → End ${DateFormat('MMM d, yyyy').format(activity.recurrenceEndDate!)}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant.withOpacity(0.8),
                                fontSize: 10.sp,
                              ),
                            ),
                          ] else ...[
                            SizedBox(height: 0.5.h),
                            Text(
                              'Recurring: Start ${DateFormat('MMM d, yyyy').format(activity.scheduledTime)} → No end date',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: colorScheme.onSurfaceVariant.withOpacity(0.8),
                                fontSize: 10.sp,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ],
              
              if (activity.notifyBeforeMinutes > 0) ...[
                SizedBox(height: 1.h),
                Row(
                  children: [
                    Icon(
                      Icons.notifications,
                      size: 4.w,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    SizedBox(width: 1.w),
                    Text(
                      'Notify ${activity.notifyBeforeMinutes} min${activity.notifyBeforeMinutes > 1 ? 's' : ''} before',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
              
              // Add View in Timeline button (only show if not already in timeline)
              if (showTimelineButton) ...[
                SizedBox(height: 2.h),
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton.icon(
                        onPressed: () => _navigateToTimeline(context),
                        icon: Icon(
                          Icons.timeline,
                          size: 4.w,
                          color: colorScheme.primary,
                        ),
                        label: Text(
                          'View in Timeline',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: colorScheme.primary.withOpacity(0.5)),
                          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  
  void _navigateToTimeline(BuildContext context) {
    try {
      final babyProfileManager = BabyProfileStateManager();
      final babyProfile = babyProfileManager.activeBaby;
      if (babyProfile != null) {
        Navigator.pushNamed(
          context,
          AppRoutes.activityTimeline,
          arguments: {
            'baby_profile': babyProfile,
            'filter_type': _getActivityTypeForTimeline(),
          },
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('No active baby profile found'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    } catch (e) {
      debugPrint('❌ Error navigating to timeline: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to open timeline'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }
  
  // Helper method to get the activity type for timeline filtering
  String _getActivityTypeForTimeline() {
    switch (activity.type) {
      case ScheduledActivityType.sleepReminder:
        return 'sleep';
      case ScheduledActivityType.feedingReminder:
        return 'feeding';
      case ScheduledActivityType.medicationReminder:
        return 'medicine';
      case ScheduledActivityType.doctorAppointment:
        return 'custom';
      case ScheduledActivityType.vaccinationAppointment:
        return 'vaccination';
      case ScheduledActivityType.diaperChangeReminder:
        return 'diaper';
      case ScheduledActivityType.bathTime:
        return 'custom';
      case ScheduledActivityType.tummyTime:
        return 'tummy_time';
      case ScheduledActivityType.playTime:
        return 'outdoor_play';
      case ScheduledActivityType.shoppingTrip:
        return 'custom';
      case ScheduledActivityType.napTime:
        return 'sleep';
      case ScheduledActivityType.mealTime:
        return 'feeding';
      case ScheduledActivityType.walkTime:
        return 'outdoor_play';
      case ScheduledActivityType.nursingSession:
        return 'feeding';
      case ScheduledActivityType.bottleFeeding:
        return 'feeding';
      case ScheduledActivityType.customReminder:
        return 'custom';
    }
  }
}
