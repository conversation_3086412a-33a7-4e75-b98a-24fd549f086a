import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../models/scheduled_activity.dart';
import '../../widgets/custom_elevated_button.dart';
import '../../utils/snackbar_helper.dart';
import 'widgets/scheduled_activity_card.dart';
import 'widgets/add_scheduled_activity_bottom_sheet.dart';

class SchedulerScreen extends StatefulWidget {
  const SchedulerScreen({super.key});

  @override
  State<SchedulerScreen> createState() => _SchedulerScreenState();
}

class _SchedulerScreenState extends State<SchedulerScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final SupabaseService _supabaseService = SupabaseService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  
  List<ScheduledActivity> _allScheduledActivities = [];
  List<ScheduledActivity> _upcomingActivities = [];
  List<ScheduledActivity> _completedActivities = [];
  List<ScheduledActivity> _recurringActivities = [];
  List<ScheduledActivity> _overdueActivities = [];
  
  bool _isLoading = true;
  String? _errorMessage;
  BabyProfile? _currentBabyProfile;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    _babyProfileManager.addListener(_onBabyProfileStateChanged);
    _initializeData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _babyProfileManager.removeListener(_onBabyProfileStateChanged);
    super.dispose();
  }

  void _onBabyProfileStateChanged() {
    if (mounted) {
      setState(() {
        _currentBabyProfile = _babyProfileManager.activeBaby;
      });
      _loadScheduledActivities();
    }
  }

  Future<void> _initializeData() async {
    try {
      if (!_babyProfileManager.hasBabies) {
        await _babyProfileManager.initialize();
      }
      
      if (_babyProfileManager.hasActiveBaby) {
        _currentBabyProfile = _babyProfileManager.activeBaby;
        await _loadScheduledActivities();
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = 'No active baby profile found. Please create a baby profile first.';
        });
      }
    } catch (e) {
      debugPrint('❌ Error initializing scheduler data: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load data: ${e.toString()}';
      });
    }
  }

  Future<void> _loadScheduledActivities() async {
    if (_currentBabyProfile == null) return;
    
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      debugPrint('🔄 Loading scheduled activities for baby: ${_currentBabyProfile!.id}');
      
      final activities = await _supabaseService.getAllScheduledActivities(_currentBabyProfile!.id);
      
      setState(() {
        _allScheduledActivities = activities;
        _categorizeActivities();
        _isLoading = false;
      });
      
      debugPrint('✅ Loaded ${activities.length} scheduled activities');
    } catch (e) {
      debugPrint('❌ Error loading scheduled activities: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load scheduled activities: ${e.toString()}';
      });
    }
  }

  void _categorizeActivities() {
    final now = DateTime.now();
    
    _upcomingActivities = _allScheduledActivities.where((activity) {
      final nextOccurrence = activity.getNextOccurrence() ?? activity.scheduledTime;
      return nextOccurrence.isAfter(now) && !activity.isCompleted;
    }).toList();
    
    _overdueActivities = _allScheduledActivities.where((activity) {
      final nextOccurrence = activity.getNextOccurrence() ?? activity.scheduledTime;
      return nextOccurrence.isBefore(now) && !activity.isCompleted && !activity.isRecurring;
    }).toList();
    
    _completedActivities = _allScheduledActivities.where((activity) {
      return activity.isCompleted;
    }).toList();
    
    _recurringActivities = _allScheduledActivities.where((activity) {
      return activity.isRecurring && activity.isActive;
    }).toList();
    
    // Sort activities
    _upcomingActivities.sort((a, b) {
      final aTime = a.getNextOccurrence() ?? a.scheduledTime;
      final bTime = b.getNextOccurrence() ?? b.scheduledTime;
      return aTime.compareTo(bTime);
    });
    
    _overdueActivities.sort((a, b) {
      final aTime = a.getNextOccurrence() ?? a.scheduledTime;
      final bTime = b.getNextOccurrence() ?? b.scheduledTime;
      return bTime.compareTo(aTime); // Most overdue first
    });
    
    _completedActivities.sort((a, b) => (b.completedAt ?? b.updatedAt).compareTo(a.completedAt ?? a.updatedAt));
    _recurringActivities.sort((a, b) => a.scheduledTime.compareTo(b.scheduledTime));
  }

  void _showAddScheduledActivityBottomSheet() {
    if (_currentBabyProfile == null) {
      SnackBarHelper.showError(
        context,
        'No active baby profile. Please create a baby profile first.',
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddScheduledActivityBottomSheet(
        babyProfile: _currentBabyProfile!,
        onActivityAdded: (activity) {
          _loadScheduledActivities();
        },
      ),
    );
  }

  Future<void> _markActivityCompleted(ScheduledActivity activity) async {
    try {
      await _supabaseService.markScheduledActivityCompleted(activity.id);
      SnackBarHelper.showSuccess(context, 'Activity marked as completed');
      _loadScheduledActivities();
    } catch (e) {
      debugPrint('❌ Error marking activity as completed: $e');
      SnackBarHelper.showError(context, 'Failed to mark activity as completed');
    }
  }

  Future<void> _deleteActivity(ScheduledActivity activity) async {
    try {
      await _supabaseService.deleteScheduledActivity(activity.id);
      SnackBarHelper.showSuccess(context, 'Activity deleted successfully');
      _loadScheduledActivities();
    } catch (e) {
      debugPrint('❌ Error deleting activity: $e');
      SnackBarHelper.showError(context, 'Failed to delete activity');
    }
  }

  Widget _buildEmptyState(String message, String subMessage) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.schedule,
            size: 15.w,
            color: Theme.of(context).colorScheme.outline,
          ),
          SizedBox(height: 3.h),
          Text(
            message,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 1.h),
          Text(
            subMessage,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildActivityList(List<ScheduledActivity> activities) {
    if (activities.isEmpty) {
      return _buildEmptyState(
        'No activities',
        'Scheduled activities will appear here',
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return Padding(
          padding: EdgeInsets.only(bottom: 2.h),
          child: ScheduledActivityCard(
            activity: activity,
            onCompleted: () => _markActivityCompleted(activity),
            onDeleted: () => _deleteActivity(activity),
            onEdit: (updatedActivity) {
              // TODO: Implement edit functionality
              _loadScheduledActivities();
            },
          ),
        );
      },
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 15.w,
            color: Theme.of(context).colorScheme.error,
          ),
          SizedBox(height: 3.h),
          Text(
            'Error',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Theme.of(context).colorScheme.error,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            _errorMessage ?? 'Something went wrong',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 3.h),
          CustomElevatedButton(
            text: 'Retry',
            onPressed: _loadScheduledActivities,
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
          SizedBox(height: 2.h),
          Text(
            'Loading scheduled activities...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            size: 6.w,
            color: Theme.of(context).colorScheme.onSurface,
          ),
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              // If we can't pop, navigate to main navigation
              Navigator.pushNamedAndRemoveUntil(
                context, 
                '/main-navigation', 
                (route) => false
              );
            }
          },
        ),
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Schedule',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_currentBabyProfile != null)
              Text(
                'for ${_currentBabyProfile!.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
          ],
        ),
        actions: [
          if (!_isLoading)
            IconButton(
              icon: CustomIconWidget(
                iconName: 'refresh',
                size: 6.w,
                color: Theme.of(context).colorScheme.onSurface,
              ),
              onPressed: _loadScheduledActivities,
            ),
        ],
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(8.h),
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: Theme.of(context).cardColor,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
                  blurRadius: 6,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: TabBar(
                controller: _tabController,
                isScrollable: true,
                padding: EdgeInsets.all(0.3.w),
                tabAlignment: TabAlignment.start,
                dividerColor: Colors.transparent,
                labelColor: Theme.of(context).brightness == Brightness.dark 
                    ? Colors.black 
                    : Colors.white,
                unselectedLabelColor: Theme.of(context).colorScheme.onSurfaceVariant,
                indicator: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(8),
                ),
                indicatorSize: TabBarIndicatorSize.tab,
                indicatorPadding: EdgeInsets.all(0.2.w),
                labelPadding: EdgeInsets.symmetric(horizontal: 2.w),
                labelStyle: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 11.sp,
                ),
                unselectedLabelStyle: TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 11.sp,
                ),
                tabs: [
                  _buildSchedulerTab('list', 'All', _allScheduledActivities.length),
                  _buildSchedulerTab('upcoming', 'Upcoming', _upcomingActivities.length),
                  _buildSchedulerTab('warning', 'Overdue', _overdueActivities.length),
                  _buildSchedulerTab('done', 'Completed', _completedActivities.length),
                  _buildSchedulerTab('repeat', 'Recurring', _recurringActivities.length),
                ],
              ),
            ),
          ),
        ),
      ),
      body: _isLoading
          ? _buildLoadingState()
          : _errorMessage != null
              ? _buildErrorState()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildActivityList(_allScheduledActivities),
                    _buildActivityList(_upcomingActivities),
                    _buildActivityList(_overdueActivities),
                    _buildActivityList(_completedActivities),
                    _buildActivityList(_recurringActivities),
                  ],
                ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddScheduledActivityBottomSheet,
        backgroundColor: colorScheme.primary,
        child: Icon(
          Icons.add,
          color: colorScheme.onPrimary,
        ),
      ),
    );
  }

  /// Builds a scheduler tab with icon and label similar to AI Insights dashboard
  Widget _buildSchedulerTab(String iconName, String label, int count) {
    return Tab(
      height: 7.h,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 1.5.w, vertical: 0.8.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: iconName,
              size: 4.w,
            ),
            SizedBox(height: 0.3.h),
            Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    fontSize: 10.sp,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                if (count > 0) ...[
                  SizedBox(width: 0.5.w),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: 1.2.w,
                      vertical: 0.1.h,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).brightness == Brightness.dark 
                          ? Colors.white.withValues(alpha: 0.9)
                          : Colors.black.withValues(alpha: 0.7),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      count.toString(),
                      style: TextStyle(
                        fontSize: 8.sp,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).brightness == Brightness.dark 
                            ? Colors.black
                            : Colors.white,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
