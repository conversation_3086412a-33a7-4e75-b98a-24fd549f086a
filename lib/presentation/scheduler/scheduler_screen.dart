import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../models/baby_profile.dart';
import '../../models/scheduled_activity.dart';
import '../../services/baby_profile_state_manager.dart';
import '../../services/supabase_service.dart';
import '../../theme/theme_aware_colors.dart';
import '../../utils/snackbar_helper.dart';
import 'widgets/add_scheduled_activity_bottom_sheet.dart';
import 'widgets/scheduled_activity_card.dart';

class SchedulerScreen extends StatefulWidget {
  const SchedulerScreen({super.key});

  @override
  State<SchedulerScreen> createState() => _SchedulerScreenState();
}

class _SchedulerScreenState extends State<SchedulerScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  
  List<ScheduledActivity> _scheduledActivities = [];
  bool _isLoading = false;
  BabyProfile? _currentBabyProfile;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  Future<void> _initializeData() async {
    final babyProfileManager = Provider.of<BabyProfileStateManager>(context, listen: false);
    _currentBabyProfile = babyProfileManager.activeBaby;
    
    if (_currentBabyProfile != null) {
      await _loadScheduledActivities();
    }
  }

  Future<void> _loadScheduledActivities() async {
    if (_currentBabyProfile == null) return;

    setState(() => _isLoading = true);

    try {
      final activities = await _supabaseService.getScheduledActivities(_currentBabyProfile!.id);
      if (mounted) {
        setState(() {
          _scheduledActivities = activities;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading scheduled activities: $e');
      if (mounted) {
        setState(() => _isLoading = false);
        SnackBarHelper.showError(
          context,
          'Failed to load scheduled activities: ${e.toString()}',
        );
      }
    }
  }

  Future<void> _markAsCompleted(ScheduledActivity activity) async {
    try {
      final updatedActivity = activity.copyWith(
        isCompleted: true,
        updatedAt: DateTime.now(),
      );

      await _supabaseService.updateScheduledActivity(updatedActivity);
      await _loadScheduledActivities();
      
      if (mounted) {
        SnackBarHelper.showSuccess(
          context,
          'Activity marked as completed!',
        );
      }
    } catch (e) {
      debugPrint('❌ Error marking activity as completed: $e');
      if (mounted) {
        SnackBarHelper.showError(
          context,
          'Failed to mark activity as completed',
        );
      }
    }
  }

  Future<void> _deleteActivity(ScheduledActivity activity) async {
    try {
      await _supabaseService.deleteScheduledActivity(activity.id);
      await _loadScheduledActivities();
      
      if (mounted) {
        SnackBarHelper.showSuccess(
          context,
          'Activity deleted successfully!',
        );
      }
    } catch (e) {
      debugPrint('❌ Error deleting activity: $e');
      if (mounted) {
        SnackBarHelper.showError(
          context,
          'Failed to delete activity',
        );
      }
    }
  }

  void _showAddScheduledActivityBottomSheet() {
    if (_currentBabyProfile == null) {
      SnackBarHelper.showError(
        context,
        'No active baby profile. Please create a baby profile first.',
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddScheduledActivityBottomSheet(
        babyProfile: _currentBabyProfile!,
        onActivityAdded: (activity) {
          _loadScheduledActivities();
        },
      ),
    );
  }

  void _showEditScheduledActivityBottomSheet(ScheduledActivity activity) {
    if (_currentBabyProfile == null) {
      SnackBarHelper.showError(
        context,
        'No active baby profile. Please create a baby profile first.',
      );
      return;
    }

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddScheduledActivityBottomSheet(
        babyProfile: _currentBabyProfile!,
        existingActivity: activity,
        onActivityAdded: (updatedActivity) {
          _loadScheduledActivities();
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BabyProfileStateManager>(
      builder: (context, babyProfileManager, _) {
        // Update current baby profile if it changed
        if (_currentBabyProfile?.id != babyProfileManager.activeBaby?.id) {
          _currentBabyProfile = babyProfileManager.activeBaby;
          WidgetsBinding.instance.addPostFrameCallback((_) {
            _loadScheduledActivities();
          });
        }

        return Scaffold(
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          appBar: AppBar(
            title: Text(
              'Scheduler',
              style: TextStyle(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            backgroundColor: Colors.transparent,
            elevation: 0,
            scrolledUnderElevation: 0,
            actions: [
              IconButton(
                onPressed: _loadScheduledActivities,
                icon: Icon(
                  Icons.refresh,
                  color: Theme.of(context).colorScheme.primary,
                ),
                tooltip: 'Refresh',
              ),
            ],
          ),
          body: _buildBody(),
          floatingActionButton: FloatingActionButton(
            onPressed: _showAddScheduledActivityBottomSheet,
            backgroundColor: Theme.of(context).colorScheme.primary,
            child: Icon(
              Icons.add,
              color: Colors.white,
            ),
            tooltip: 'Add Scheduled Activity',
          ),
        );
      },
    );
  }

  Widget _buildBody() {
    if (_currentBabyProfile == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.baby_changing_station,
              size: 15.w,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
            SizedBox(height: 2.h),
            Text(
              'No Baby Profile Selected',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Please create or select a baby profile to manage scheduled activities.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (_isLoading) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: 2.h),
            Text(
              'Loading scheduled activities...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
      );
    }

    if (_scheduledActivities.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.schedule,
              size: 15.w,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
            SizedBox(height: 2.h),
            Text(
              'No Scheduled Activities',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Tap the + button to create your first scheduled activity.',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadScheduledActivities,
      color: Theme.of(context).colorScheme.primary,
      child: ListView.builder(
        padding: EdgeInsets.all(4.w),
        itemCount: _scheduledActivities.length,
        itemBuilder: (context, index) {
          final activity = _scheduledActivities[index];
          
          return Padding(
            padding: EdgeInsets.only(bottom: 2.h),
            child: ScheduledActivityCard(
              activity: activity,
              onCompleted: () => _markAsCompleted(activity),
              onEdit: (activityToEdit) => _showEditScheduledActivityBottomSheet(activityToEdit),
              onDelete: () => _deleteActivity(activity),
            ),
          );
        },
      ),
    );
  }
}