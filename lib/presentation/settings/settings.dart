import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';

import '../../utils/error_handler.dart';
import '../../widgets/user_profile_account_section.dart';
import '../../services/account_profile_controller.dart';
import './widgets/baby_profile_section_widget.dart';
import './widgets/data_privacy_section_widget.dart';
import './widgets/notification_settings_widget.dart';
import './widgets/settings_tile_widget.dart';
import './widgets/theme_selection_dialog.dart';
import './widgets/measurement_units_section_widget.dart';
import './widgets/contact_support_widget.dart';
import '../../widgets/email_change_dialog.dart';
import '../../services/debug_email_status_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../../models/subscription_info.dart';
import '../subscription/controllers/subscription_controller.dart';
import '../subscription/widgets/subscription_status_widget.dart';


class Settings extends StatefulWidget {
  const Settings({super.key});

  @override
  State<Settings> createState() => _SettingsState();
}

class _SettingsState extends State<Settings> {
  bool _is24HourFormat = true;
  String _selectedTheme = 'System';
  
  // Real data from Supabase
  BabyProfile? _currentBabyProfile;
  UserProfile? _currentUserProfile;
  SubscriptionInfo? _currentSubscription;
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();
  final SettingsService _settingsService = SettingsService.instance;
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  // Subscription controller will be accessed via Provider
  // final SubscriptionController _subscriptionController = SubscriptionController();
  bool _isLoading = true;
  
  // Collapsible sections state
  bool _babyProfileExpanded = false;
  bool _notificationsExpanded = false;
  bool _unitsExpanded = false;
  bool _securityExpanded = false;
  bool _supportExpanded = false;

  @override
  void initState() {
    super.initState();
    _initializeSettings();
  }

  Future<void> _initializeSettings() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Initialize settings service
      await _settingsService.init();
      
      // Load preferences from shared preferences
      _loadPreferences();
      
      // Load user data from Supabase
      await _loadUserData();
    } catch (e) {
      debugPrint('Error initializing settings: $e');
      if (mounted) {
        ErrorHandler.showErrorSnackBar(
          context,
          'Failed to load settings. Please try again.',
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _loadPreferences() {
    try {
      final themeService = Provider.of<ThemeService>(context, listen: false);
      setState(() {
        _is24HourFormat = _settingsService.is24HourFormat;
        _selectedTheme = themeService.themeModeString;
      });
    } catch (e) {
      // Fallback if ThemeService not available
      setState(() {
        _is24HourFormat = _settingsService.is24HourFormat;
        _selectedTheme = 'System'; // Default fallback
      });
    }
  }

  Future<void> _loadUserData() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        return;
      }

      // Load user profile with better error handling and fallback
      _currentUserProfile = _authService.userProfile;
      if (_currentUserProfile == null) {
        // Try to load from Supabase directly
        final userProfiles = await _supabaseService.select(
          'user_profiles',
          filters: {'auth_id': currentUser.id},
          limit: 1,
        );
        
        if (userProfiles.isNotEmpty) {
          _currentUserProfile = UserProfile.fromJson(userProfiles.first);
        } else {
          // Create a basic user profile if none exists
          _currentUserProfile = UserProfile(
            id: currentUser.id,
            email: currentUser.email ?? '',
            fullName: currentUser.userMetadata?['display_name'] as String? ?? 
                        currentUser.userMetadata?['full_name'] as String? ?? 
                        currentUser.email?.split('@').first ?? 'User',
            role: 'parent',
            signInCount: 1,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
          );
          
          // Try to save the profile to Supabase
          try {
            await _supabaseService.insert('user_profiles', _currentUserProfile!.toJson());
          } catch (insertError) {
            debugPrint('Failed to create user profile: $insertError');
          }
        }
      }

      // Load baby profile from state manager
      if (!_babyProfileManager.hasBabies) {
        await _babyProfileManager.initialize();
      }
      
      // Use the active baby from state manager
      _currentBabyProfile = _babyProfileManager.activeBaby;
      
      // Load subscription information (if available)
      await _loadSubscriptionData();
    } catch (e) {
      debugPrint('Error loading user data: $e');
      // Create a minimal fallback profile
      final currentUser = _authService.currentUser;
      if (currentUser != null && _currentUserProfile == null) {
        _currentUserProfile = UserProfile(
          id: currentUser.id,
          email: currentUser.email ?? '',
          fullName: currentUser.email?.split('@').first ?? 'User',
          role: 'parent',
          signInCount: 1,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }
    }
  }

  Color _getRoleColor(String role) {
    switch (role) {
      case 'admin':
        return Colors.purple;
      case 'parent':
        return Colors.blue;
      case 'grandparent':
        return ThemeAwareColors.getSuccessColor(context);
      case 'babysitter':
        return Colors.orange;
      case 'other_carer':
        return Colors.teal;
      default:
        return ThemeAwareColors.getSecondaryTextColor(context);
    }
  }

  String _formatRole(String role) {
    return role.replaceAll('_', ' ').toUpperCase();
  }

  /// Load subscription data from the subscription controller
  Future<void> _loadSubscriptionData() async {
    try {
      // Initialize the subscription controller
      await context.read<SubscriptionController>().initialize();
      
      // Get current subscription info
      _currentSubscription = context.read<SubscriptionController>().currentSubscription;
      
      debugPrint('Loaded subscription: ${_currentSubscription?.planName} (${_currentSubscription?.status.name})');
    } catch (e) {
      debugPrint('Error loading subscription data: $e');
      // Fallback to free plan if loading fails
      _currentSubscription = SubscriptionPlans.free;
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            'Settings',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          elevation: 0,
          leading: IconButton(
            onPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }
            },
            icon: CustomIconWidget(
              iconName: 'arrow_back',
              color: Theme.of(context).colorScheme.onSurface,
              size: 24,
            ),
          ),
        ),
        body: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
          ),
        ),
      );
    }

    return ChangeNotifierProvider(
      create: (context) => AccountProfileController(_authService),
      child: Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Text(
          'Settings',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            color: Theme.of(context).colorScheme.onSurface,
            size: 24,
          ),
        ),
        actions: [
          IconButton(
            onPressed: () => _showSearchDialog(),
            icon: CustomIconWidget(
              iconName: 'search',
              color: Theme.of(context).colorScheme.primary,
              size: 24,
            ),
          ),
          SizedBox(width: 2.w),
        ],
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(vertical: 2.h),
        children: [
          // User Profile & Account Section (moved to top and merged)
          UserProfileAccountSectionVariants.forSettings(
            userProfile: _currentUserProfile,
            subscription: _currentSubscription,
            onEditProfile: () => _showEditUserProfileDialog(),
            onNavigateToUserManagement: () => _navigateToUserManagement(),
            onSubscriptionTap: () => _showSubscriptionDialog(),
            onFamilySharingTap: () => _showFamilySharingDialog(),
          ),

          SizedBox(height: 2.h),

          // Subscription Status Section
          Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            child: const SubscriptionStatusWidget(
              showUpgradeButton: true,
              showFeatureList: false,
              compact: false,
            ),
          ),

          SizedBox(height: 2.h),

          // Baby Profile Section
          _buildCollapsibleSection(
            title: 'Baby Profile',
            icon: 'child_care',
            isExpanded: _babyProfileExpanded,
            onToggle: () => setState(() => _babyProfileExpanded = !_babyProfileExpanded),
            children: [
              BabyProfileSectionWidget(
                babyProfile: _currentBabyProfile,
                onEditProfile: () => _navigateToBabyProfile(),
                onManageBabies: () => _navigateToBabiesManagement(),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Notifications
          _buildCollapsibleSection(
            title: 'Notifications',
            icon: 'notifications',
            isExpanded: _notificationsExpanded,
            onToggle: () => setState(() => _notificationsExpanded = !_notificationsExpanded),
            children: [
              NotificationSettingsWidget(
                onSettingsChanged: (settings) {
                  // Handle notification settings changes
                },
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Units & Preferences
          _buildCollapsibleSection(
            title: 'Units & Preferences',
            icon: 'tune',
            isExpanded: _unitsExpanded,
            onToggle: () => setState(() => _unitsExpanded = !_unitsExpanded),
            children: [
              MeasurementUnitsSectionWidget(),
              SettingsTileWidget(
                title: 'Time Format',
                subtitle: _is24HourFormat ? '24-hour format' : '12-hour format',
                leading: CustomIconWidget(
                  iconName: 'schedule',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                trailing: Switch(
                  value: _is24HourFormat,
                  onChanged: (value) async {
                    setState(() {
                      _is24HourFormat = value;
                    });
                    await _settingsService.setTimeFormat(value);
                  },
                  activeColor: Theme.of(context).colorScheme.primary,
                  activeTrackColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  inactiveThumbColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                  inactiveTrackColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
                ),
              ),
              SettingsTileWidget(
                title: 'Theme',
                subtitle: _selectedTheme,
                leading: CustomIconWidget(
                  iconName: 'palette',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                onTap: () => _showThemeDialog(),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Security
          _buildCollapsibleSection(
            title: 'Security & Privacy',
            icon: 'security',
            isExpanded: _securityExpanded,
            onToggle: () => setState(() => _securityExpanded = !_securityExpanded),
            children: [
              DataPrivacySectionWidget(),
            ],
          ),

          SizedBox(height: 2.h),


          // Support
          _buildCollapsibleSection(
            title: 'Support & About',
            icon: 'help',
            isExpanded: _supportExpanded,
            onToggle: () => setState(() => _supportExpanded = !_supportExpanded),
            children: [
              SettingsTileWidget(
                title: 'Help & Documentation',
                subtitle: 'Get help using the app',
                leading: CustomIconWidget(
                  iconName: 'help_outline',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                onTap: () => _showHelpDialog(),
              ),
              // Contact Support Section
              ContactSupportWidget(
                userProfile: _currentUserProfile,
              ),
              SettingsTileWidget(
                title: 'Rate the App',
                subtitle: 'Share your experience',
                leading: CustomIconWidget(
                  iconName: 'star_rate',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                onTap: () => _showRatingDialog(),
              ),
              SettingsTileWidget(
                title: 'App Version',
                subtitle: 'Version 1.0.0 (Build 1)',
                leading: CustomIconWidget(
                  iconName: 'info',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                onTap: () => _showVersionDialog(),
              ),
            ],
          ),

          SizedBox(height: 4.h),

          // Logout button
          Container(
            margin: EdgeInsets.symmetric(horizontal: 4.w),
            child: ElevatedButton(
              onPressed: () => _showLogoutDialog(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.error,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 2.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomIconWidget(
                    iconName: 'logout',
                    color: Colors.white,
                    size: 20,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    'Sign Out',
                    style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: 4.h),
        ],
      ),
      ),
    );
  }

  Widget _buildCollapsibleSection({
    required String title,
    required String icon,
    required bool isExpanded,
    required VoidCallback onToggle,
    required List<Widget> children,
  }) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Header
          InkWell(
            onTap: onToggle,
            borderRadius: BorderRadius.circular(16),
            child: Padding(
              padding: EdgeInsets.all(4.w),
              child: Row(
                children: [
                  CustomIconWidget(
                    iconName: icon,
                    color: Theme.of(context).colorScheme.primary,
                    size: 24,
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Text(
                      title,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  Icon(
                    isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ],
              ),
            ),
          ),
          // Content
          AnimatedCrossFade(
            firstChild: Container(),
            secondChild: Column(
              children: [
                Divider(height: 0, color: Theme.of(context).dividerColor),
                ...children,
              ],
            ),
            crossFadeState: isExpanded ? CrossFadeState.showSecond : CrossFadeState.showFirst,
            duration: Duration(milliseconds: 300),
          ),
        ],
      ),
    );
  }

  void _navigateToBabiesManagement() {
    // Navigate to baby selector screen which has baby management features
    Navigator.pushNamed(context, '/baby-selector-screen').then((result) {
      if (result != null && result == true) {
        _loadUserData(); // Refresh data
      }
    });
  }

  void _navigateToUserManagement() {
    Navigator.pushNamed(context, '/user-management-screen').then((result) {
      if (result == true) {
        _loadUserData();
      }
    });
  }

  void _navigateToBabyProfile() {
    if (_currentBabyProfile != null) {
      // Edit existing baby profile
      Navigator.pushNamed(
        context,
        '/baby-profile-creation',
        arguments: _currentBabyProfile,
      ).then((result) {
        if (result == true) {
          _loadUserData();
        }
      });
    } else {
      // Create new baby profile
      Navigator.pushNamed(context, '/baby-profile-creation').then((result) {
        if (result == true) {
          _loadUserData();
        }
      });
    }
  }

  void _showEmailChangeDialog() {
    if (_currentUserProfile != null) {
      // First check email status
      DebugEmailStatusService.checkEmailStatus();
      
      showDialog(
        context: context,
        builder: (context) => EmailChangeDialog(
          currentEmail: _currentUserProfile!.email,
          onEmailChangeInitiated: () {
            // Check status and sync if needed
            _checkAndSyncEmail();
          },
        ),
      );
    }
  }
  
  /// Check email status and sync if needed
  Future<void> _checkAndSyncEmail() async {
    await DebugEmailStatusService.checkEmailStatus();
    
    // Try to sync database with auth email
    final synced = await DebugEmailStatusService.syncDatabaseEmail();
    if (synced) {
      // Refresh the UI
      _loadUserData();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Email successfully synchronized!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
  
  /// Force check email change completion with detailed debugging
  Future<void> _forceEmailCheck() async {
    try {
      debugPrint('=== FORCE CHECK DEBUG START ===');
      debugPrint('Force Check: User clicked Force Check button');
      
      // Check current state BEFORE any action
      final userBefore = Supabase.instance.client.auth.currentUser;
      debugPrint('Force Check: BEFORE - Auth Email: ${userBefore?.email}');
      debugPrint('Force Check: BEFORE - New Email: ${userBefore?.newEmail}');
      
      // Check database state BEFORE
      final supabaseService = SupabaseService();
      final profilesBefore = await supabaseService.select(
        'user_profiles',
        filters: {'auth_id': userBefore?.id},
        limit: 1,
      );
      if (profilesBefore.isNotEmpty) {
        debugPrint('Force Check: BEFORE - Database Email: ${profilesBefore.first['email']}');
      }
      
      // Show loading
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Checking email change...'),
            ],
          ),
        ),
      );
      
      debugPrint('Force Check: Starting session refresh...');
      
      // Force refresh session multiple times
      for (int i = 0; i < 3; i++) {
        debugPrint('Force Check: Session refresh ${i + 1}/3');
        await Supabase.instance.client.auth.refreshSession();
        await Future.delayed(Duration(seconds: 1));
        
        final userDuring = Supabase.instance.client.auth.currentUser;
        debugPrint('Force Check: After refresh ${i + 1} - Email: ${userDuring?.email}, NewEmail: ${userDuring?.newEmail}');
      }
      
      final user = Supabase.instance.client.auth.currentUser;
      Navigator.pop(context); // Close loading dialog
      
      debugPrint('Force Check: AFTER refreshes - Auth Email: ${user?.email}');
      debugPrint('Force Check: AFTER refreshes - New Email: ${user?.newEmail}');
      
      if (user != null) {
        if (user.newEmail == null) {
          // Email change completed!
          debugPrint('Force Check: ✅ Email change detected as completed!');
          debugPrint('Force Check: Updating database to: ${user.email}');
          
          // Update database
          await supabaseService.update(
            'user_profiles',
            {
              'email': user.email,
              'updated_at': DateTime.now().toIso8601String(),
            },
            'auth_id',
            user.id,
          );
          
          debugPrint('Force Check: Database update completed');
          
          // Check database state AFTER
          final profilesAfter = await supabaseService.select(
            'user_profiles',
            filters: {'auth_id': user.id},
            limit: 1,
          );
          if (profilesAfter.isNotEmpty) {
            debugPrint('Force Check: AFTER - Database Email: ${profilesAfter.first['email']}');
          }
          
          // Refresh UI
          await _loadUserData();
          debugPrint('Force Check: UI refresh completed');
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.white),
                  SizedBox(width: 12),
                  Text('Email change completed successfully!'),
                ],
              ),
              backgroundColor: Colors.green,
              duration: Duration(seconds: 5),
            ),
          );
        } else {
          // Still pending
          debugPrint('Force Check: ⏳ Email change still pending to: ${user.newEmail}');
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Email change still pending. Please ensure you:'),
                  SizedBox(height: 4),
                  Text('1. Clicked the link in your OLD email first'),
                  Text('2. Then clicked the link in your NEW email'),
                  Text('3. Wait a few minutes for processing'),
                ],
              ),
              backgroundColor: Colors.orange,
              duration: Duration(seconds: 8),
            ),
          );
        }
      }
      
      debugPrint('=== FORCE CHECK DEBUG END ===');
      
    } catch (e) {
      debugPrint('Force Check: ❌ Error: $e');
      Navigator.pop(context); // Close loading dialog if open
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error checking email change: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showEditUserProfileDialog() {
    if (_currentUserProfile != null) {
      final _nameController = TextEditingController(text: _currentUserProfile!.fullName);
      final _emailController = TextEditingController(text: _currentUserProfile!.email);

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(
            'Edit Profile',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  controller: _nameController,
                  decoration: InputDecoration(
                    labelText: 'Full Name',
                    prefixIcon: Icon(
                      Icons.person_outline,
                      color: ThemeAwareColors.getIconColor(context),
                    ),
                    filled: true,
                    fillColor: Theme.of(context).colorScheme.surface,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: ThemeAwareColors.getOutlineColor(context)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
                    ),
                    labelStyle: TextStyle(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 2.h),
                // Email field (read-only with change button)
                Container(
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: ThemeAwareColors.getOutlineColor(context),
                    ),
                  ),
                  child: Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(left: 3.w),
                        child: Icon(
                          Icons.email_outlined,
                          color: ThemeAwareColors.getIconColor(context),
                        ),
                      ),
                      SizedBox(width: 3.w),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 2.h),
                            Text(
                              'Email Address',
                              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                              ),
                            ),
                            SizedBox(height: 0.5.h),
                            Text(
                              _emailController.text,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface,
                              ),
                            ),
                            SizedBox(height: 2.h),
                          ],
                        ),
                      ),
                      Column(
                        children: [
                          TextButton.icon(
                            onPressed: () => _showEmailChangeDialog(),
                            icon: Icon(
                              Icons.edit,
                              size: 16,
                              color: ThemeAwareColors.getPrimaryColor(context),
                            ),
                            label: Text(
                              'Change',
                              style: TextStyle(
                                color: ThemeAwareColors.getPrimaryColor(context),
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            style: TextButton.styleFrom(
                              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(width: 2.w),
                    ],
                  ),
                ),
                SizedBox(height: 2.h),
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceVariant,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.admin_panel_settings_outlined, 
                        color: _getRoleColor(_currentUserProfile!.role),
                        size: 20,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        'Role: ${_formatRole(_currentUserProfile!.role)}',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  // Only update the full name, not email (email changes are handled separately)
                  await _supabaseService.update(
                    'user_profiles',
                    {
                      'full_name': _nameController.text.trim(),
                      'updated_at': DateTime.now().toIso8601String(),
                    },
                    'auth_id',
                    _authService.currentUser!.id,
                  );

                  // Update the local state immediately (only name, email stays the same)
                  setState(() {
                    _currentUserProfile = _currentUserProfile!.copyWith(
                      fullName: _nameController.text.trim(),
                      updatedAt: DateTime.now(),
                    );
                  });

                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Profile updated successfully!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: ThemeAwareColors.getSuccessColor(context),
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      margin: EdgeInsets.all(4.w),
                    ),
                  );
                  
                  // Also refresh data from database
                  _loadUserData();
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Failed to update profile: ${e.toString()}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.white,
                        ),
                      ),
                      backgroundColor: Theme.of(context).colorScheme.error,
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      margin: EdgeInsets.all(4.w),
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: Text('Save'),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          backgroundColor: Theme.of(context).dialogBackgroundColor,
          surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
        ),
      );
    }
  }

  void _showThemeDialog() {
    showDialog(
      context: context,
      builder: (context) => ThemeSelectionDialog(
        currentTheme: _selectedTheme,
        onThemeChanged: (theme) {
          setState(() {
            _selectedTheme = theme;
          });
        },
      ),
    );
  }



  void _showSubscriptionDialog() {
    Navigator.pushNamed(context, '/subscription');
  }

  void _showFamilySharingDialog() {
    Navigator.pushNamed(context, '/user-management-screen');
  }

  void _showHealthcareProviderDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Healthcare Provider Integration',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Connect with your healthcare provider to sync medical records and milestones. This feature will be available in a future update.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('OK'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Help & Documentation',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Need help? Here are some resources:',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              '• User Guide: Learn the basics',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              '• FAQ: Common questions',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              '• Video Tutorials: Step-by-step guides',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              '• Contact Support: Get personal help',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('OK'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }

  

  void _showRatingDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Rate Baby Tracker Pro',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Love using Baby Tracker Pro? Please rate us on the App Store!',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 2.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(5, (index) => 
                Icon(
                  Icons.star,
                  color: Colors.amber,
                  size: 8.w,
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('Later'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Open app store for rating
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(
                    'Thank you for your feedback!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
                  backgroundColor: Theme.of(context).colorScheme.primary,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  margin: EdgeInsets.all(4.w),
                ),
              );
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('Rate Now'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }

  void _showVersionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'App Information',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Baby Tracker Pro',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            Text(
              'Version: 1.0.0',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              'Build: 1',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            Text(
              'Platform: Flutter',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 2.h),
            Text(
              '© 2024 Baby Tracker Pro Team',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('OK'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }

  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Sign Out',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Are you sure you want to sign out?',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              
              // Show loading indicator
              showDialog(
                context: context,
                barrierDismissible: false,
                builder: (context) => AlertDialog(
                  content: Row(
                    children: [
                      CircularProgressIndicator(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      SizedBox(width: 4.w),
                      Text(
                        'Signing out...',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                  backgroundColor: Theme.of(context).dialogBackgroundColor,
                  surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
                ),
              );
              
              try {
                // Perform actual logout
                await _authService.signOut();
                
                // Close loading dialog
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
                
                // Show success message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Signed out successfully',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: ThemeAwareColors.getSuccessColor(context),
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    margin: EdgeInsets.all(4.w),
                  ),
                );
                
                // Navigate to authentication screen
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  '/authentication-screen',
                  (route) => false,
                );
              } catch (e) {
                // Close loading dialog
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
                
                // Show error message
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      'Failed to sign out: $e',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    backgroundColor: Theme.of(context).colorScheme.error,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    margin: EdgeInsets.all(4.w),
                  ),
                );
              }
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: Text('Sign Out'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }

  void _showSearchDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Search Settings',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: TextField(
          decoration: InputDecoration(
            hintText: 'Search settings...',
            prefixIcon: Icon(
              Icons.search,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            hintStyle: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.6),
            ),
            border: OutlineInputBorder(),
          ),
          style: TextStyle(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('Cancel'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }

  Future<void> _updateUserRole() async {
    if (_currentUserProfile == null) return;
    
    try {
      await _supabaseService.update(
        'user_profiles',
        {
          'role': 'admin',
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        _authService.currentUser!.id,
      );

      setState(() {
        _currentUserProfile = _currentUserProfile!.copyWith(role: 'admin');
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Role updated to Admin successfully!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            ),
          ),
          backgroundColor: ThemeAwareColors.getSuccessColor(context),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(4.w),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to update role: $e',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Colors.white,
            ),
          ),
          backgroundColor: Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(4.w),
        ),
      );
    }
  }

  void _showEditFullNameDialog() {
    if (_currentUserProfile != null) {
      final _nameController = TextEditingController(text: _currentUserProfile!.fullName);

      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('Edit Full Name'),
          content: TextField(
            controller: _nameController,
            decoration: InputDecoration(
              labelText: 'Full Name',
              border: OutlineInputBorder(),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await _supabaseService.update(
                    'user_profiles',
                    {
                      'full_name': _nameController.text.trim(),
                      'updated_at': DateTime.now().toIso8601String(),
                    },
                    'auth_id',
                    _authService.currentUser!.id,
                  );

                  setState(() {
                    _currentUserProfile = _currentUserProfile!.copyWith(
                      fullName: _nameController.text.trim(),
                      updatedAt: DateTime.now(),
                    );
                  });

                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Full name updated successfully!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                } catch (e) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Failed to update name: $e'),
                      backgroundColor: Colors.red,
                    ),
                  );
                }
              },
              child: Text('Save'),
            ),
          ],
        ),
      );
    }
  }

  void _showPromoteToAdminDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(
          'Promote to Admin',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Text(
          'Are you sure you want to promote this user to admin?',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              await _updateUserRole();
            },
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.primary,
            ),
            child: Text('Promote'),
          ),
        ],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      ),
    );
  }
}
