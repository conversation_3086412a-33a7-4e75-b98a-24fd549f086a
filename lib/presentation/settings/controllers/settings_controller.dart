import 'package:flutter/foundation.dart';

import '../../../core/app_export.dart';
import '../../../services/settings_service.dart';
import '../../../services/auth_service.dart';

class SettingsController extends ChangeNotifier {
  final SettingsService _settingsService = SettingsService.instance;
  final AuthService _authService = AuthService();
  final SupabaseService _supabaseService = SupabaseService();

  // State variables
  bool _isLoading = true;
  bool _isMetric = true;
  bool _is24HourFormat = true;
  bool _biometricEnabled = false;
  bool _analyticsEnabled = true;
  String _selectedTheme = 'System';
  
  BabyProfile? _currentBabyProfile;
  UserProfile? _currentUserProfile;

  // Expansion states
  final Map<String, bool> _expansionStates = {
    'userProfile': false,
    'babyProfile': false,
    'notifications': false,
    'units': false,
    'security': false,
    'account': false,
    'support': false,
  };

  // Getters
  bool get isLoading => _isLoading;
  bool get isMetric => _isMetric;
  bool get is24HourFormat => _is24HourFormat;
  bool get biometricEnabled => _biometricEnabled;
  bool get analyticsEnabled => _analyticsEnabled;
  String get selectedTheme => _selectedTheme;
  BabyProfile? get currentBabyProfile => _currentBabyProfile;
  UserProfile? get currentUserProfile => _currentUserProfile;

  bool getExpansionState(String key) => _expansionStates[key] ?? false;

  // Initialization
  Future<void> initialize() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _settingsService.init();
      _loadPreferences();
      await _loadUserData();
    } catch (e) {
      debugPrint('Error initializing settings: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  void _loadPreferences() {
    _isMetric = _settingsService.isMetric;
    _is24HourFormat = _settingsService.is24HourFormat;
    _biometricEnabled = _settingsService.biometricEnabled;
    _analyticsEnabled = _settingsService.analyticsEnabled;
  }

  Future<void> _loadUserData() async {
    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      _currentUserProfile = _authService.userProfile;
      if (_currentUserProfile == null) {
        final userProfiles = await _supabaseService.select(
          'user_profiles',
          filters: {'id': currentUser.id},
          limit: 1,
        );
        
        if (userProfiles.isNotEmpty) {
          _currentUserProfile = UserProfile.fromJson(userProfiles.first);
        }
      }

      final babyProfiles = await _supabaseService.select(
        'baby_profiles',
        filters: {'user_id': currentUser.id},
        limit: 1,
      );

      if (babyProfiles.isNotEmpty) {
        _currentBabyProfile = BabyProfile.fromJson(babyProfiles.first);
      }
    } catch (e) {
      debugPrint('Error loading user data: $e');
    }
  }

  // Settings update methods
  Future<void> updateMetricUnits(bool value) async {
    _isMetric = value;
    notifyListeners();
    await _settingsService.setMetricUnits(value);
  }

  Future<void> updateTimeFormat(bool value) async {
    _is24HourFormat = value;
    notifyListeners();
    await _settingsService.setTimeFormat(value);
  }

  Future<void> updateBiometricEnabled(bool value) async {
    _biometricEnabled = value;
    notifyListeners();
    await _settingsService.setBiometricEnabled(value);
  }


  Future<void> updateAnalyticsEnabled(bool value) async {
    _analyticsEnabled = value;
    notifyListeners();
    await _settingsService.setAnalyticsEnabled(value);
  }

  void updateTheme(String theme) {
    _selectedTheme = theme;
    notifyListeners();
  }

  void toggleExpansion(String key) {
    _expansionStates[key] = !(_expansionStates[key] ?? false);
    notifyListeners();
  }

  Future<void> refreshUserData() async {
    await _loadUserData();
    notifyListeners();
  }
}