import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import './settings_tile_widget.dart';

class AccountSectionWidget extends StatelessWidget {
  final VoidCallback? onSubscriptionTap;
  final VoidCallback? onFamilySharingTap;
  final VoidCallback? onHealthcareProviderTap;

  const AccountSectionWidget({
    super.key,
    this.onSubscriptionTap,
    this.onFamilySharingTap,
    this.onHealthcareProviderTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        Container(
          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
          child: Row(
            children: [
              CustomIconWidget(
                iconName: 'account_circle',
                color: Theme.of(context).colorScheme.primary,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Account',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.primary,
                ),
              ),
            ],
          ),
        ),

        // Section content
        Card(
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: Column(
            children: [
              SettingsTileWidget(
                title: 'Subscription',
                subtitle: 'Manage your Pro subscription',
                leading: Container(
                  padding: EdgeInsets.all(1.w),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: 'star',
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                trailing: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'PRO',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                onTap: onSubscriptionTap,
              ),
              SettingsTileWidget(
                title: 'Family Sharing',
                subtitle: 'Share baby data with family members',
                leading: CustomIconWidget(
                  iconName: 'family_restroom',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                trailing: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                  decoration: BoxDecoration(
                    color: Colors.green.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    '3 members',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.green,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                onTap: onFamilySharingTap,
              ),
              SettingsTileWidget(
                title: 'Healthcare Provider',
                subtitle: 'Connect with your pediatrician',
                leading: CustomIconWidget(
                  iconName: 'local_hospital',
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                trailing: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    'Connected',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Colors.blue,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                onTap: onHealthcareProviderTap,
                showDivider: false,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
