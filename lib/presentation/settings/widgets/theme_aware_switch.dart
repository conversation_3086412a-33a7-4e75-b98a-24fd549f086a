import 'package:flutter/material.dart';

/// A theme-aware switch widget that automatically uses the correct colors
/// for the current theme without requiring manual color specification.
class ThemeAwareSwitch extends StatelessWidget {
  final bool value;
  final ValueChanged<bool>? onChanged;

  const ThemeAwareSwitch({
    super.key,
    required this.value,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Switch(
      value: value,
      onChanged: onChanged,
      activeColor: Theme.of(context).colorScheme.primary,
      activeTrackColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
      inactiveThumbColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
      inactiveTrackColor: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.2),
    );
  }
}