import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../widgets/custom_icon_widget.dart';

class ThemeOptionTile extends StatelessWidget {
  final String title;
  final String value;
  final String iconName;
  final String groupValue;
  final ValueChanged<String?> onChanged;

  const ThemeOptionTile({
    super.key,
    required this.title,
    required this.value,
    required this.iconName,
    required this.groupValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return RadioListTile<String>(
      title: Row(
        children: [
          CustomIconWidget(
            iconName: iconName,
            color: Theme.of(context).colorScheme.onSurface,
            size: 20,
          ),
          SizedBox(width: 3.w),
          Text(
            title,
            style: TextStyle(color: Theme.of(context).colorScheme.onSurface),
          ),
        ],
      ),
      value: value,
      groupValue: groupValue,
      activeColor: Theme.of(context).colorScheme.primary,
      fillColor: WidgetStateProperty.resolveWith<Color>((states) {
        if (states.contains(WidgetState.selected)) {
          return Theme.of(context).colorScheme.primary;
        }
        return Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6);
      }),
      onChanged: onChanged,
    );
  }
}