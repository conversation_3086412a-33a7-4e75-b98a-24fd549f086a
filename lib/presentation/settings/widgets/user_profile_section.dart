import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';
import '../../../widgets/custom_icon_widget.dart';
import '../../../models/subscription_info.dart';
import 'current_plan_widget.dart';

class UserProfileSection extends StatelessWidget {
  final UserProfile? userProfile;
  final VoidCallback onEditProfile;
  final VoidCallback onNavigateToUserManagement;
  final VoidCallback onPromoteToAdmin;
  final VoidCallback? onManageSubscription;
  final SubscriptionInfo? currentSubscription;

  const UserProfileSection({
    super.key,
    required this.userProfile,
    required this.onEditProfile,
    required this.onNavigateToUserManagement,
    required this.onPromoteToAdmin,
    this.onManageSubscription,
    this.currentSubscription,
  });

  Color _getRoleColor(BuildContext context, String role) {
    switch (role) {
      case 'admin':
        return Colors.purple;
      case 'parent':
        return Colors.blue;
      case 'grandparent':
        return ThemeAwareColors.getSuccessColor(context);
      case 'babysitter':
        return Colors.orange;
      case 'other_carer':
        return Colors.teal;
      default:
        return ThemeAwareColors.getSecondaryTextColor(context);
    }
  }

  String _formatRole(String role) {
    return role.replaceAll('_', ' ').toUpperCase();
  }

  @override
  Widget build(BuildContext context) {
    if (userProfile == null) {
      return Padding(
        padding: EdgeInsets.all(4.w),
        child: Center(
          child: Text(
            'No user profile found',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          _buildUserInfo(context),
          SizedBox(height: 3.h),
          _buildActionButtons(context),
          // Current Plan section - only show if onManageSubscription is provided
          if (onManageSubscription != null)
            CurrentPlanWidget(
              subscription: currentSubscription,
              onManageSubscription: onManageSubscription,
            ),
        ],
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    return Row(
      children: [
        Container(
          width: 15.w,
          height: 15.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: _getRoleColor(context, userProfile!.role).withValues(alpha: 0.1),
            border: Border.all(
              color: _getRoleColor(context, userProfile!.role),
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              userProfile!.fullName.substring(0, 1).toUpperCase(),
              style: TextStyle(
                fontSize: 18.sp,
                fontWeight: FontWeight.bold,
                color: _getRoleColor(context, userProfile!.role),
              ),
            ),
          ),
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userProfile!.fullName,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                userProfile!.email,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
              SizedBox(height: 1.h),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                decoration: BoxDecoration(
                  color: _getRoleColor(context, userProfile!.role).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: _getRoleColor(context, userProfile!.role),
                  ),
                ),
                child: Text(
                  _formatRole(userProfile!.role),
                  style: TextStyle(
                    fontSize: 12.sp,
                    fontWeight: FontWeight.w600,
                    color: _getRoleColor(context, userProfile!.role),
                  ),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: onEditProfile,
          icon: CustomIconWidget(
            iconName: 'edit',
            color: Theme.of(context).colorScheme.primary,
            size: 20,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Column(
      children: [
        // User Management Button (for admin and parent roles)
        if (userProfile!.role == 'admin' || userProfile!.role == 'parent')
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: onNavigateToUserManagement,
              icon: CustomIconWidget(
                iconName: 'people',
                color: Theme.of(context).colorScheme.primary,
                size: 16,
              ),
              label: Text(userProfile!.role == 'admin' ? 'Manage Users' : 'Family Management'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
                side: BorderSide(color: Theme.of(context).colorScheme.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),

        // Promote to Admin Button (for first user who is not admin)
        if (userProfile!.role != 'admin')
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(top: 1.h),
            child: ElevatedButton.icon(
              onPressed: onPromoteToAdmin,
              icon: const CustomIconWidget(
                iconName: 'admin_panel_settings',
                color: Colors.white,
                size: 16,
              ),
              label: const Text('Promote to Admin'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
      ],
    );
  }
}