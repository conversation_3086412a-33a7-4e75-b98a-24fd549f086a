import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../models/subscription_info.dart';
import '../../../models/enums.dart';
import '../../../theme/theme_aware_colors.dart';
import '../../../widgets/custom_icon_widget.dart';

/// Widget to display current subscription plan and manage subscription button
/// 
/// This widget shows the user's current plan (Free/Premium) and provides
/// a button to manage their subscription, following the requested UI design.
class CurrentPlanWidget extends StatelessWidget {
  /// Current user subscription information
  final SubscriptionInfo? subscription;
  
  /// Callback when manage subscription button is tapped
  final VoidCallback? onManageSubscription;

  const CurrentPlanWidget({
    super.key,
    this.subscription,
    this.onManageSubscription,
  });

  @override
  Widget build(BuildContext context) {
    final currentPlan = subscription ?? SubscriptionPlans.free;
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      margin: EdgeInsets.only(top: 2.h),
      decoration: BoxDecoration(
        color: _getPlanBackgroundColor(context, currentPlan.status),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPlanBorderColor(context, currentPlan.status),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Plan Header
          Row(
            children: [
              Icon(
                Icons.card_membership,
                color: _getPlanIconColor(context, currentPlan.status),
                size: 20,
              ),
              SizedBox(width: 2.w),
              Text(
                'Current Plan',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              const Spacer(),
              _buildPlanBadge(context, currentPlan),
            ],
          ),
          
          if (currentPlan.status != SubscriptionStatus.free) ...[ 
            SizedBox(height: 1.h),
            
            // Plan details for premium users
            Text(
              '\$${currentPlan.monthlyPrice.toStringAsFixed(2)}/month',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            
            if (currentPlan.renewalDate != null)
              Text(
                'Renews ${_formatDate(currentPlan.renewalDate!)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
          ] else ...[
            SizedBox(height: 1.h),
            
            // Free plan description
            Text(
              'Basic features included',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ],
          
          SizedBox(height: 2.h),
          
          // Manage Subscription Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: onManageSubscription,
              icon: CustomIconWidget(
                iconName: 'settings',
                color: Theme.of(context).colorScheme.primary,
                size: 16,
              ),
              label: Text('Manage Subscription'),
              style: OutlinedButton.styleFrom(
                foregroundColor: Theme.of(context).colorScheme.primary,
                side: BorderSide(color: Theme.of(context).colorScheme.primary),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.symmetric(vertical: 1.5.h),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build the plan status badge
  Widget _buildPlanBadge(BuildContext context, SubscriptionInfo subscription) {
    final theme = Theme.of(context);
    final status = _getStatusText(subscription.status);
    final color = _getPlanBadgeColor(context, subscription.status);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status,
        style: theme.textTheme.labelSmall?.copyWith(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Get status text for display
  String _getStatusText(SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return 'PREMIUM';
      case SubscriptionStatus.trial:
        return 'TRIAL';
      case SubscriptionStatus.expired:
        return 'EXPIRED';
      case SubscriptionStatus.cancelled:
        return 'CANCELLED';
      case SubscriptionStatus.free:
        return 'FREE';
      default:
        return 'FREE';
    }
  }

  /// Get background color based on plan status
  Color _getPlanBackgroundColor(BuildContext context, SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.1);
      case SubscriptionStatus.trial:
        return ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.1);
      case SubscriptionStatus.expired:
        return ThemeAwareColors.getErrorColor(context).withValues(alpha: 0.1);
      case SubscriptionStatus.cancelled:
        return ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.1);
      case SubscriptionStatus.free:
        return ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1);
      default:
        return ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1);
    }
  }

  /// Get border color based on plan status
  Color _getPlanBorderColor(BuildContext context, SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.3);
      case SubscriptionStatus.trial:
        return ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.3);
      case SubscriptionStatus.expired:
        return ThemeAwareColors.getErrorColor(context).withValues(alpha: 0.3);
      case SubscriptionStatus.cancelled:
        return ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3);
      case SubscriptionStatus.free:
        return ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.3);
      default:
        return ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.3);
    }
  }

  /// Get icon color based on plan status
  Color _getPlanIconColor(BuildContext context, SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return ThemeAwareColors.getSuccessColor(context);
      case SubscriptionStatus.trial:
        return ThemeAwareColors.getWarningColor(context);
      case SubscriptionStatus.expired:
        return ThemeAwareColors.getErrorColor(context);
      case SubscriptionStatus.cancelled:
        return ThemeAwareColors.getSecondaryTextColor(context);
      case SubscriptionStatus.free:
        return ThemeAwareColors.getPrimaryColor(context);
      default:
        return ThemeAwareColors.getPrimaryColor(context);
    }
  }

  /// Get badge color based on plan status
  Color _getPlanBadgeColor(BuildContext context, SubscriptionStatus status) {
    switch (status) {
      case SubscriptionStatus.active:
        return ThemeAwareColors.getSuccessColor(context);
      case SubscriptionStatus.trial:
        return ThemeAwareColors.getWarningColor(context);
      case SubscriptionStatus.expired:
        return ThemeAwareColors.getErrorColor(context);
      case SubscriptionStatus.cancelled:
        return ThemeAwareColors.getSecondaryTextColor(context);
      case SubscriptionStatus.free:
        return ThemeAwareColors.getPrimaryColor(context);
      default:
        return ThemeAwareColors.getPrimaryColor(context);
    }
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
