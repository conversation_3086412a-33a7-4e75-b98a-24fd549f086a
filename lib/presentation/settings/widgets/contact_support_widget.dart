import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../services/contact_support_service.dart';
import '../../../utils/error_handler.dart';

class ContactSupportWidget extends StatefulWidget {
  final UserProfile? userProfile;

  const ContactSupportWidget({
    super.key,
    this.userProfile,
  });

  @override
  State<ContactSupportWidget> createState() => _ContactSupportWidgetState();
}

class _ContactSupportWidgetState extends State<ContactSupportWidget> {
  final ContactSupportService _supportService = ContactSupportService.instance;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Report a Problem
        _buildSupportTile(
          title: 'Report a Problem',
          subtitle: 'Found a bug or issue?',
          icon: 'bug_report',
          onTap: () => _showSupportForm('problem'),
        ),
        
        // Provide Feedback
        _buildSupportTile(
          title: 'Provide Feedback',
          subtitle: 'Share your thoughts and suggestions',
          icon: 'feedback',
          onTap: () => _showSupportForm('feedback'),
        ),
        
        // General Support
        _buildSupportTile(
          title: 'General Support',
          subtitle: 'Get help with using the app',
          icon: 'help_outline',
          onTap: () => _showSupportForm('general'),
        ),
        
        // Contact Options
        _buildSupportTile(
          title: 'Contact Options',
          subtitle: 'Email or copy support address',
          icon: 'contact_support',
          onTap: () => _showContactOptions(),
        ),
      ],
    );
  }

  Widget _buildSupportTile({
    required String title,
    required String subtitle,
    required String icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: CustomIconWidget(
        iconName: icon,
        color: Theme.of(context).colorScheme.primary,
        size: 24,
      ),
      title: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
        ),
      ),
      trailing: Icon(
        Icons.chevron_right,
        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
      ),
      onTap: onTap,
    );
  }

  void _showSupportForm(String type) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _SupportFormBottomSheet(
        type: type,
        userProfile: widget.userProfile,
        supportService: _supportService,
      ),
    );
  }

  void _showContactOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _ContactOptionsBottomSheet(
        supportService: _supportService,
      ),
    );
  }
}

class _SupportFormBottomSheet extends StatefulWidget {
  final String type;
  final UserProfile? userProfile;
  final ContactSupportService supportService;

  const _SupportFormBottomSheet({
    required this.type,
    required this.userProfile,
    required this.supportService,
  });

  @override
  State<_SupportFormBottomSheet> createState() => _SupportFormBottomSheetState();
}

class _SupportFormBottomSheetState extends State<_SupportFormBottomSheet> {
  final _formKey = GlobalKey<FormState>();
  final _subjectController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _emailController = TextEditingController();
  final _nameController = TextEditingController();
  bool _isSubmitting = false;
  Map<String, String>? _deviceInfo;

  @override
  void initState() {
    super.initState();
    _initializeForm();
    _loadDeviceInfo();
  }

  void _initializeForm() {
    if (widget.userProfile != null) {
      _emailController.text = widget.userProfile!.email;
      _nameController.text = widget.userProfile!.fullName;
    }
    
    // Set default subject based on type
    switch (widget.type) {
      case 'problem':
        _subjectController.text = 'Bug Report: ';
        break;
      case 'feedback':
        _subjectController.text = 'Feedback: ';
        break;
      case 'general':
        _subjectController.text = 'Support Request: ';
        break;
    }
  }

  Future<void> _loadDeviceInfo() async {
    try {
      final deviceInfo = await widget.supportService.getDeviceAndAppInfo();
      setState(() {
        _deviceInfo = deviceInfo;
      });
    } catch (e) {
      debugPrint('Error loading device info: $e');
    }
  }

  @override
  void dispose() {
    _subjectController.dispose();
    _descriptionController.dispose();
    _emailController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.85,
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: Icon(Icons.close),
                ),
                Expanded(
                  child: Text(
                    _getFormTitle(),
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(width: 48), // Balance the close button
              ],
            ),
          ),
          
          Divider(height: 1),
          
          // Form
          Expanded(
            child: Form(
              key: _formKey,
              child: ListView(
                padding: EdgeInsets.all(4.w),
                children: [
                  // Name field
                  TextFormField(
                    controller: _nameController,
                    decoration: InputDecoration(
                      labelText: 'Your Name',
                      prefixIcon: Icon(Icons.person_outline),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter your name';
                      }
                      return null;
                    },
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Email field
                  TextFormField(
                    controller: _emailController,
                    decoration: InputDecoration(
                      labelText: 'Your Email',
                      prefixIcon: Icon(Icons.email_outlined),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    keyboardType: TextInputType.emailAddress,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter your email';
                      }
                      if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
                        return 'Please enter a valid email';
                      }
                      return null;
                    },
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Subject field
                  TextFormField(
                    controller: _subjectController,
                    decoration: InputDecoration(
                      labelText: 'Subject',
                      prefixIcon: Icon(Icons.subject),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter a subject';
                      }
                      return null;
                    },
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Description field
                  TextFormField(
                    controller: _descriptionController,
                    decoration: InputDecoration(
                      labelText: 'Description',
                      prefixIcon: Icon(Icons.description),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                      alignLabelWithHint: true,
                    ),
                    maxLines: 5,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please provide a description';
                      }
                      if (value.trim().length < 10) {
                        return 'Please provide more details (at least 10 characters)';
                      }
                      return null;
                    },
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Device info preview
                  if (_deviceInfo != null) ...[
                    ExpansionTile(
                      title: Text(
                        'Device Information',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      subtitle: Text(
                        'This information will be included to help us debug issues',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                      children: [
                        Container(
                          width: double.infinity,
                          padding: EdgeInsets.all(3.w),
                          margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surfaceContainerHighest,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            widget.supportService.formatDeviceInfoForEmail(_deviceInfo!),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontFamily: 'monospace',
                            ),
                          ),
                        ),
                      ],
                    ),
                    
                    SizedBox(height: 3.h),
                  ],
                  
                  // Submit buttons
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: _isSubmitting ? null : _submitViaForm,
                          icon: _isSubmitting 
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(strokeWidth: 2),
                                )
                              : Icon(Icons.send),
                          label: Text(_isSubmitting ? 'Submitting...' : 'Submit Form'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Theme.of(context).colorScheme.primary,
                            foregroundColor: Theme.of(context).colorScheme.onPrimary,
                            padding: EdgeInsets.symmetric(vertical: 2.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                      
                      SizedBox(width: 3.w),
                      
                      Expanded(
                        child: OutlinedButton.icon(
                          onPressed: _isSubmitting ? null : _submitViaEmail,
                          icon: Icon(Icons.email),
                          label: Text('Send Email'),
                          style: OutlinedButton.styleFrom(
                            foregroundColor: Theme.of(context).colorScheme.primary,
                            padding: EdgeInsets.symmetric(vertical: 2.h),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getFormTitle() {
    switch (widget.type) {
      case 'problem':
        return 'Report a Problem';
      case 'feedback':
        return 'Provide Feedback';
      case 'general':
        return 'General Support';
      default:
        return 'Contact Support';
    }
  }

  Future<void> _submitViaForm() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final success = await widget.supportService.submitSupportRequest(
        type: widget.type,
        subject: _subjectController.text.trim(),
        description: _descriptionController.text.trim(),
        userEmail: _emailController.text.trim(),
        userName: _nameController.text.trim(),
        deviceInfo: _deviceInfo,
      );

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          ErrorHandler.showSuccessSnackBar(
            context,
            'Support request submitted successfully! We\'ll get back to you soon.',
          );
        } else {
          ErrorHandler.showErrorSnackBar(
            context,
            'Failed to submit support request. Please try again or use email option.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(
          context,
          'An error occurred while submitting your request: $e',
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  Future<void> _submitViaEmail() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final body = '''
Hello Support Team,

Name: ${_nameController.text.trim()}
Email: ${_emailController.text.trim()}

Description:
${_descriptionController.text.trim()}

Please find the device information below for debugging purposes.
''';

      final success = await widget.supportService.launchEmailSupport(
        subject: _subjectController.text.trim(),
        body: body,
        deviceInfo: _deviceInfo,
      );

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          ErrorHandler.showSuccessSnackBar(
            context,
            'Email app opened successfully!',
          );
        } else {
          ErrorHandler.showErrorSnackBar(
            context,
            'Could not open email app. Please copy the support email address instead.',
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ErrorHandler.showErrorSnackBar(
          context,
          'An error occurred while opening email: $e',
        );
      }
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }
}

class _ContactOptionsBottomSheet extends StatelessWidget {
  final ContactSupportService supportService;

  const _ContactOptionsBottomSheet({
    required this.supportService,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
            child: Text(
              'Contact Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          
          Divider(height: 1),
          
          // Options
          ListTile(
            leading: Icon(
              Icons.email,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text('Open Email App'),
            subtitle: Text('Send email with device info'),
            onTap: () async {
              final navigator = Navigator.of(context);
              navigator.pop();
              final success = await supportService.launchEmailSupport(
                subject: 'Support Request',
                body: 'Hello Support Team,\n\nI need help with:\n\n',
              );
              
              if (!success) {
                ErrorHandler.showErrorSnackBar(
                  context,
                  'Could not open email app. Please copy the email address instead.',
                );
              }
            },
          ),
          
          ListTile(
            leading: Icon(
              Icons.copy,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text('Copy Email Address'),
            subtitle: Text(ContactSupportService.supportEmail),
            onTap: () async {
              final navigator = Navigator.of(context);
              navigator.pop();
              await supportService.copyEmailToClipboard();
              ErrorHandler.showSuccessSnackBar(
                context,
                'Email address copied to clipboard!',
              );
            },
          ),
          
          ListTile(
            leading: Icon(
              Icons.info_outline,
              color: Theme.of(context).colorScheme.primary,
            ),
            title: Text('View Device Info'),
            subtitle: Text('See what information we collect'),
            onTap: () => _showDeviceInfo(context),
          ),
          
          SizedBox(height: 2.h),
        ],
      ),
    );
  }

  void _showDeviceInfo(BuildContext context) async {
    Navigator.pop(context);
    
    showDialog(
      context: context,
      builder: (context) => FutureBuilder<String>(
        future: supportService.getFormattedDeviceInfo(),
        builder: (context, snapshot) {
          return AlertDialog(
            title: Text('Device Information'),
            content: SizedBox(
              width: double.maxFinite,
              height: 60.h,
              child: snapshot.hasData
                  ? SingleChildScrollView(
                      child: Text(
                        snapshot.data!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontFamily: 'monospace',
                        ),
                      ),
                    )
                  : Center(child: CircularProgressIndicator()),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('Close'),
              ),
              if (snapshot.hasData)
                TextButton(
                  onPressed: () async {
                    await Clipboard.setData(ClipboardData(text: snapshot.data!));
                    Navigator.pop(context);
                    ErrorHandler.showSuccessSnackBar(
                      context,
                      'Device information copied to clipboard!',
                    );
                  },
                  child: Text('Copy'),
                ),
            ],
          );
        },
      ),
    );
  }
}