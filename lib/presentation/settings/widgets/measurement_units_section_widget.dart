import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../../core/app_export.dart';
import '../../../services/measurement_units_service.dart';
import '../../../theme/theme_aware_colors.dart';
import 'settings_tile_widget.dart';

class MeasurementUnitsSectionWidget extends StatefulWidget {
  const MeasurementUnitsSectionWidget({super.key});

  @override
  State<MeasurementUnitsSectionWidget> createState() => _MeasurementUnitsSectionWidgetState();
}

class _MeasurementUnitsSectionWidgetState extends State<MeasurementUnitsSectionWidget> 
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  
  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _showMeasurementSystemDialog() {
    showDialog(
      context: context,
      builder: (context) => _MeasurementSystemDialog(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MeasurementUnitsService>(
      builder: (context, unitsService, child) {
        return SettingsTileWidget(
          title: 'Measurement Units',
          subtitle: unitsService.measurementSystemDescription,
          leading: CustomIconWidget(
            iconName: 'straighten',
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.8.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Text(
                  unitsService.measurementSystem,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              SizedBox(width: 2.w),
              CustomIconWidget(
                iconName: 'chevron_right',
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.4),
                size: 20,
              ),
            ],
          ),
          onTap: _showMeasurementSystemDialog,
        );
      },
    );
  }
}

class _MeasurementSystemDialog extends StatefulWidget {
  @override
  State<_MeasurementSystemDialog> createState() => _MeasurementSystemDialogState();
}

class _MeasurementSystemDialogState extends State<_MeasurementSystemDialog> 
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: Duration(milliseconds: 300),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));
    
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<MeasurementUnitsService>(
      builder: (context, unitsService, child) {
        return AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: ScaleTransition(
                scale: _scaleAnimation,
                child: AlertDialog(
                  title: Row(
                    children: [
                      CustomIconWidget(
                        iconName: 'straighten',
                        color: Theme.of(context).colorScheme.primary,
                        size: 24,
                      ),
                      SizedBox(width: 3.w),
                      Text(
                        'Measurement Units',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Choose your preferred measurement system. This will apply to all measurements throughout the app.',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                      ),
                      SizedBox(height: 3.h),
                      
                      // Metric Option
                      _buildMeasurementOption(
                        context: context,
                        title: 'Metric System',
                        subtitle: 'Kilograms, Centimeters, Celsius',
                        examples: 'Weight: kg, g\nHeight: cm, m\nTemp: °C',
                        isSelected: unitsService.isMetric,
                        onTap: () async {
                          await unitsService.setMeasurementSystem(true);
                          if (mounted) {
                            Navigator.of(context).pop();
                            _showSuccessMessage('Switched to Metric system');
                          }
                        },
                      ),
                      
                      SizedBox(height: 2.h),
                      
                      // Imperial Option
                      _buildMeasurementOption(
                        context: context,
                        title: 'Imperial System',
                        subtitle: 'Pounds, Inches, Fahrenheit',
                        examples: 'Weight: lbs, oz\nHeight: in, ft\nTemp: °F',
                        isSelected: !unitsService.isMetric,
                        onTap: () async {
                          await unitsService.setMeasurementSystem(false);
                          if (mounted) {
                            Navigator.of(context).pop();
                            _showSuccessMessage('Switched to Imperial system');
                          }
                        },
                      ),
                    ],
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(context).pop(),
                      child: Text(
                        'Cancel',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                        ),
                      ),
                    ),
                  ],
                  backgroundColor: Theme.of(context).dialogBackgroundColor,
                  surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildMeasurementOption({
    required BuildContext context,
    required String title,
    required String subtitle,
    required String examples,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: Duration(milliseconds: 200),
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          color: isSelected 
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected 
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).dividerColor.withValues(alpha: 0.3),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: isSelected 
                              ? Theme.of(context).colorScheme.primary
                              : null,
                        ),
                      ),
                      if (isSelected) ...[
                        SizedBox(width: 2.w),
                        CustomIconWidget(
                          iconName: 'check_circle',
                          color: Theme.of(context).colorScheme.primary,
                          size: 20,
                        ),
                      ],
                    ],
                  ),
                  SizedBox(height: 0.5.h),
                  Text(
                    subtitle,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    examples,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.5),
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSuccessMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            CustomIconWidget(
              iconName: 'check_circle',
              color: Colors.white,
              size: 20,
            ),
            SizedBox(width: 2.w),
            Text(message),
          ],
        ),
        backgroundColor: ThemeAwareColors.getSuccessColor(context),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}