import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import './settings_tile_widget.dart';

class DataPrivacySectionWidget extends StatelessWidget {
  const DataPrivacySectionWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SettingsTileWidget(
          title: 'Export Data',
          subtitle: 'Download your baby\'s data',
          leading: CustomIconWidget(
            iconName: 'download',
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          onTap: () => _showExportDialog(context),
        ),
        SettingsTileWidget(
          title: 'Privacy Policy',
          subtitle: 'View our privacy policy',
          leading: CustomIconWidget(
            iconName: 'privacy_tip',
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          onTap: () => _showPrivacyPolicy(context),
        ),
        SettingsTileWidget(
          title: 'Delete Account',
          subtitle: 'Permanently delete your account and data',
          leading: CustomIconWidget(
            iconName: 'delete_forever',
            color: Theme.of(context).colorScheme.error,
            size: 24,
          ),
          onTap: () => _showDeleteAccountDialog(context),
          showDivider: false,
        ),
      ],
    );
  }

  void _showExportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Export Data'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: CustomIconWidget(iconName: 'picture_as_pdf', size: 24),
              title: Text('PDF Report'),
              subtitle: Text('Comprehensive baby report'),
              onTap: () {
                Navigator.pop(context);
                _exportPDF(context);
              },
            ),
            ListTile(
              leading: CustomIconWidget(iconName: 'table_chart', size: 24),
              title: Text('CSV Data'),
              subtitle: Text('Raw data for analysis'),
              onTap: () {
                Navigator.pop(context);
                _exportCSV(context);
              },
            ),
            ListTile(
              leading: CustomIconWidget(iconName: 'code', size: 24),
              title: Text('JSON Data'),
              subtitle: Text('Complete data backup'),
              onTap: () {
                Navigator.pop(context);
                _exportJSON(context);
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
        ],
      ),
    );
  }


  void _showPrivacyPolicy(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Privacy Policy'),
        content: SingleChildScrollView(
          child: Text(
            'Privacy Policy\n\n'
            'Last updated: [Date]\n\n'
            'Your privacy is important to us. This Privacy Policy explains how we collect, use, and protect your information when you use our Baby Tracker Pro app.\n\n'
            '1. Information We Collect\n'
            '- Baby profile information (name, birth date, measurements)\n'
            '- Activity logs (feeding, sleep, diaper changes)\n'
            '- Usage analytics (anonymous)\n\n'
            '2. How We Use Your Information\n'
            '- To provide personalized insights\n'
            '- To improve app functionality\n'
            '- To generate reports for healthcare providers\n\n'
            '3. Data Security\n'
            '- All data is encrypted in transit and at rest\n'
            '- We use industry-standard security measures\n'
            '- No data is shared with third parties without consent\n\n'
            'For the complete privacy policy, visit our website.',
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showDeleteAccountDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Delete Account'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Are you sure you want to delete your account?',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 2.h),
            Text('This action will:'),
            Text('• Permanently delete all baby data'),
            Text('• Remove all activity logs'),
            Text('• Delete all photos and notes'),
            Text('• Cancel any active subscriptions'),
            SizedBox(height: 2.h),
            Text(
              'This action cannot be undone.',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _deleteAccount(context);
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: Text('Delete Account'),
          ),
        ],
      ),
    );
  }


  void _exportPDF(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('PDF export functionality coming soon')),
    );
  }

  void _exportCSV(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('CSV export functionality coming soon')),
    );
  }

  void _exportJSON(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('JSON export functionality coming soon')),
    );
  }


  void _deleteAccount(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Account deletion functionality coming soon'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
