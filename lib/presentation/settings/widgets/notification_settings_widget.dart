import 'package:flutter/material.dart';

import '../../../core/app_export.dart';
import '../../../widgets/modern_date_time_picker.dart';
import '../../../services/notification_service.dart';
import './settings_tile_widget.dart';

class NotificationSettingsWidget extends StatefulWidget {
  final Function(Map<String, dynamic>)? onSettingsChanged;

  const NotificationSettingsWidget({
    super.key,
    this.onSettingsChanged,
  });

  @override
  State<NotificationSettingsWidget> createState() =>
      _NotificationSettingsWidgetState();
}

class _NotificationSettingsWidgetState
    extends State<NotificationSettingsWidget> {
  final NotificationService _notificationService = NotificationService.instance;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeNotificationService();
  }
  
  Future<void> _initializeNotificationService() async {
    if (!_notificationService.isInitialized) {
      await _notificationService.init();
    }
    _notificationService.addListener(_onNotificationServiceChanged);
    setState(() {
      _isInitialized = true;
    });
  }
  
  @override
  void dispose() {
    _notificationService.removeListener(_onNotificationServiceChanged);
    super.dispose();
  }
  
  void _onNotificationServiceChanged() {
    if (mounted) {
      setState(() {
        // Rebuild when notification service changes
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Center(
        child: CircularProgressIndicator(),
      );
    }
    
    return Column(
      children: [
        SettingsTileWidget(
          title: 'All Notifications',
          subtitle: _notificationService.notificationsEnabled 
              ? 'All notifications are enabled' 
              : 'All notifications are disabled',
          leading: CustomIconWidget(
            iconName: 'notifications',
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          trailing: Switch(
            value: _notificationService.notificationsEnabled,
            onChanged: (value) async {
              await _notificationService.setNotificationsEnabled(value);
              _notifyChange();
            },
          ),
        ),
        SettingsTileWidget(
          title: 'View All Notifications',
          subtitle: 'See all your notifications in one place',
          leading: CustomIconWidget(
            iconName: 'list',
            color: Theme.of(context).colorScheme.primary,
            size: 24,
          ),
          trailing: CustomIconWidget(
            iconName: 'arrow_forward_ios',
            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
            size: 16,
          ),
          onTap: () => Navigator.pushNamed(context, '/notifications'),
          showDivider: false,
        ),
      ],
    );
  }

  void _notifyChange() {
    if (widget.onSettingsChanged != null) {
      widget.onSettingsChanged!({
        'notificationsEnabled': _notificationService.notificationsEnabled,
        'feedingReminders': _notificationService.feedingReminders,
        'sleepAlerts': _notificationService.sleepAlerts,
        'milestoneNotifications': _notificationService.milestoneNotifications,
        'aiInsightUpdates': _notificationService.aiInsightUpdates,
        'dailySummary': _notificationService.dailySummary,
        'weeklyReport': _notificationService.weeklyReport,
        'quietHoursStart': _notificationService.quietHoursStart,
        'quietHoursEnd': _notificationService.quietHoursEnd,
      });
    }
  }

  void _showQuietHoursDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Quiet Hours'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: Text('Start Time'),
              subtitle: Text(_notificationService.quietHoursStart.format(context)),
              trailing: IconButton(
                icon: CustomIconWidget(iconName: 'schedule', size: 20),
                onPressed: () async {
                  final time = await ModernDateTimePicker.showTimePicker(
                    context: context,
                    initialTime: _notificationService.quietHoursStart,
                  );
                  if (time != null) {
                    await _notificationService.setQuietHoursStart(time);
                    _notifyChange();
                  }
                },
              ),
            ),
            ListTile(
              title: Text('End Time'),
              subtitle: Text(_notificationService.quietHoursEnd.format(context)),
              trailing: IconButton(
                icon: CustomIconWidget(iconName: 'schedule', size: 20),
                onPressed: () async {
                  final time = await ModernDateTimePicker.showTimePicker(
                    context: context,
                    initialTime: _notificationService.quietHoursEnd,
                  );
                  if (time != null) {
                    await _notificationService.setQuietHoursEnd(time);
                    _notifyChange();
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Done'),
          ),
        ],
      ),
    );
  }
}
