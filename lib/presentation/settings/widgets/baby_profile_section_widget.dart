import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/baby_profile_photo_widget.dart';

class BabyProfileSectionWidget extends StatelessWidget {
  final VoidCallback? onEditProfile;
  final VoidCallback? onManageBabies;
  final BabyProfile? babyProfile;

  const BabyProfileSectionWidget({
    super.key,
    this.onEditProfile,
    this.onManageBabies,
    this.babyProfile,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Baby info
            Row(
              children: [
                // Baby photo
                BabyProfilePhotoVariants.medium(
                  photoUrl: babyProfile?.photo,
                  babyName: babyProfile?.name,
                  gender: babyProfile?.gender,
                  onTap: onEditProfile,
                ),

                SizedBox(width: 4.w),

                // Baby details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        babyProfile?.name ?? 'No Profile',
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        babyProfile != null 
                          ? 'Born: ${_formatDate(babyProfile!.birthDate)}'
                          : 'No birth date',
                        style:
                            Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface
                              .withValues(alpha: 0.6),
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        babyProfile != null 
                          ? 'Age: ${_calculateAge(babyProfile!.birthDate)}'
                          : 'No age calculated',
                        style:
                            Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface
                              .withValues(alpha: 0.6),
                        ),
                      ),
                    ],
                  ),
                ),

                // Edit button
                IconButton(
                  onPressed: onEditProfile,
                  icon: CustomIconWidget(
                    iconName: 'edit',
                    color: Theme.of(context).colorScheme.primary,
                    size: 20,
                  ),
                ),
              ],
            ),

            SizedBox(height: 2.h),

            // Action buttons
            Container(
              width: double.infinity,
              child: OutlinedButton.icon(
                onPressed: onManageBabies,
                icon: CustomIconWidget(
                  iconName: 'child_care',
                  color: Theme.of(context).colorScheme.primary,
                  size: 16,
                ),
                label: Text('Manage Babies'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.primary,
                  side: BorderSide(
                      color: Theme.of(context).colorScheme.primary),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final totalDays = difference.inDays;
    
    if (totalDays < 30) {
      return '$totalDays days';
    } else if (totalDays < 365) {
      final months = (totalDays / 30.44).floor();
      final remainingDays = (totalDays - (months * 30.44)).round();
      if (remainingDays < 7) {
        return '$months months';
      } else {
        final weeks = (remainingDays / 7).floor();
        return '$months months, $weeks weeks';
      }
    } else {
      final years = (totalDays / 365.25).floor();
      final remainingDays = totalDays - (years * 365.25).round();
      final months = (remainingDays / 30.44).floor();
      return '$years years, $months months';
    }
  }
}
