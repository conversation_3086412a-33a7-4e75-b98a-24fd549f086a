import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../../services/theme_service.dart';
import 'theme_option_tile.dart';

class ThemeSelectionDialog extends StatefulWidget {
  final String currentTheme;
  final ValueChanged<String> onThemeChanged;

  const ThemeSelectionDialog({
    super.key,
    required this.currentTheme,
    required this.onThemeChanged,
  });

  @override
  State<ThemeSelectionDialog> createState() => _ThemeSelectionDialogState();
}

class _ThemeSelectionDialogState extends State<ThemeSelectionDialog> {
  late String _selectedTheme;

  @override
  void initState() {
    super.initState();
    _selectedTheme = widget.currentTheme;
  }

  static const List<Map<String, String>> _themeOptions = [
    {'title': 'Light', 'value': 'Light', 'icon': 'light_mode'},
    {'title': 'Dark', 'value': 'Dark', 'icon': 'dark_mode'},
    {'title': 'System', 'value': 'System', 'icon': 'settings_brightness'},
  ];

  Future<void> _handleThemeChange(String? value) async {
    if (value == null) return;
    
    try {
      final themeService = Provider.of<ThemeService>(context, listen: false);
      await themeService.setThemeModeFromString(value);
      
      setState(() {
        _selectedTheme = value;
      });
      
      widget.onThemeChanged(value);
      Navigator.pop(context);
    } catch (e) {
      // Handle error gracefully
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to change theme: ${e.toString()}'),
          backgroundColor: Theme.of(context).colorScheme.error,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        'Select Theme',
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: _themeOptions.map((option) => ThemeOptionTile(
          title: option['title']!,
          value: option['value']!,
          iconName: option['icon']!,
          groupValue: _selectedTheme,
          onChanged: _handleThemeChange,
        )).toList(),
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      backgroundColor: Theme.of(context).dialogBackgroundColor,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ),
      ],
    );
  }
}