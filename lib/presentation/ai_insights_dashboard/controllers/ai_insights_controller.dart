import 'package:flutter/material.dart';
import '../../../models/activity_log.dart';
import '../../../models/baby_profile.dart';
import '../../../services/ai_insights_service.dart';
import '../../../services/ai_insights_state_manager.dart';
import '../../../services/baby_profile_state_manager.dart';
import '../../../services/supabase_service.dart';

/// Controller for managing AI insights dashboard state and business logic
class AIInsightsController extends ChangeNotifier {
  final AIInsightsStateManager _aiInsightsManager = AIInsightsStateManager();
  final AIInsightsService _aiInsightsService = AIInsightsService();
  final SupabaseService _supabaseService = SupabaseService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();

  Map<String, String> _aiGeneratedInsights = {};
  List<ActivityLog> _recentActivities = [];
  bool _isLoading = true;
  String? _errorMessage;
  BabyProfile? _babyProfile;

  // Getters
  Map<String, String> get aiGeneratedInsights => _aiGeneratedInsights;
  List<ActivityLog> get recentActivities => _recentActivities;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  BabyProfile? get babyProfile => _babyProfile;
  bool get isUpdating => _aiInsightsManager.isUpdating;

  AIInsightsController() {
    _aiInsightsManager.addListener(_onAIInsightsUpdated);
    _babyProfileManager.addListener(_onBabyProfileChanged);
  }

  @override
  void dispose() {
    _aiInsightsManager.removeListener(_onAIInsightsUpdated);
    _babyProfileManager.removeListener(_onBabyProfileChanged);
    super.dispose();
  }

  void _onAIInsightsUpdated() {
    notifyListeners();
  }

  void _onBabyProfileChanged() {
    debugPrint('🔄 Baby profile changed in AI Insights Dashboard, reloading');
    loadActiveBaby();
  }

  Future<void> loadActiveBaby() async {
    try {
      BabyProfile? activeBaby = _babyProfileManager.activeBaby;
      
      if (activeBaby != null) {
        debugPrint('✅ Using active baby from state manager: ${activeBaby.name} (${activeBaby.id})');
        _babyProfile = activeBaby;
      } else {
        debugPrint('⚠️ No active baby in state manager');
      }
      
      if (_babyProfile != null) {
        debugPrint('🎨 AI Insights Dashboard initialized for baby: ${_babyProfile!.name}');
        await loadInsights();
      } else {
        debugPrint('❌ No baby profile available for AI Insights Dashboard');
        _setError('No baby profile available');
      }
    } catch (e) {
      debugPrint('❌ Error loading active baby: $e');
      _setError('Error loading baby profile: $e');
    }
  }

  Future<void> loadInsights() async {
    if (_babyProfile == null) {
      _setError('No baby profile available');
      return;
    }
    
    try {
      _setLoading(true);

      final activities = await _supabaseService.getRecentActivities(_babyProfile!.id, limit: 200);
      _recentActivities = activities;

      _aiInsightsManager.trackUserActivity();
      
      debugPrint('📱 Dashboard: Using shared insights state for baby ${_babyProfile!.name}');
      debugPrint('📱 Dashboard: Has insights in state manager: ${_aiInsightsManager.hasInsightsForBaby(_babyProfile!.id)}');
      
      await _loadAITextInsights(fromCache: true);
      
      _setLoading(false);
      
    } catch (e) {
      _setError('Error loading insights: $e');
      _aiGeneratedInsights = _getDefaultAIInsights();
      debugPrint('❌ Error loading AI insights: $e');
    }
  }

  Future<void> manualRefreshAI({bool forceRefresh = false}) async {
    if (_aiInsightsManager.isUpdating && !forceRefresh) {
      throw Exception('AI analysis already in progress...');
    }

    if (!_aiInsightsManager.canManualRefresh() && !forceRefresh) {
      final timeRemaining = _aiInsightsManager.getTimeUntilNextRefresh();
      if (timeRemaining != null) {
        throw RateLimitException(timeRemaining);
      }
    }

    if (_babyProfile == null) {
      throw Exception('No baby profile available');
    }
    
    debugPrint('🔄 Manual AI refresh requested via shared state manager');
    
    final success = await _aiInsightsManager.manualRefresh(_babyProfile!, forceRefresh: forceRefresh);
    
    if (success) {
      await _loadAITextInsights(fromCache: true);
    }
    
    // Return void as per method signature
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _isLoading = false;
    _errorMessage = error;
    notifyListeners();
  }

  Future<void> _loadAITextInsights({required bool fromCache}) async {
    try {
      final sharedInsights = _aiInsightsManager.insights;
      
      if (sharedInsights.isNotEmpty) {
        _aiGeneratedInsights = _convertSharedInsightsToTextFormat(sharedInsights);
      } else {
        _aiGeneratedInsights = _getDefaultAIInsights();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error loading AI text insights: $e');
      _aiGeneratedInsights = _getDefaultAIInsights();
      notifyListeners();
    }
  }

  Map<String, String> _convertSharedInsightsToTextFormat(Map<String, dynamic> sharedInsights) {
    // Implementation moved from main class
    // ... (existing conversion logic)
    return {};
  }

  Map<String, String> _getDefaultAIInsights() {
    return {
      'sleep_insights': 'Continue tracking sleep patterns to get personalized insights about rest quality and schedule.',
      'sleep_recommendations': 'Maintain consistent bedtime routines and ensure a calm sleep environment.',
      'feeding_insights': 'Keep logging feeding sessions to understand nutrition patterns and preferences.',
      'feeding_recommendations': 'Follow your baby\'s hunger cues and maintain regular feeding schedules.',
      'diaper_insights': 'Regular diaper changes indicate healthy digestion and hydration patterns.',
      'diaper_recommendations': 'Continue monitoring diaper changes as they reflect your baby\'s health.',
      'growth_insights': 'Track growth measurements regularly to monitor healthy development.',
      'growth_recommendations': 'Consult your pediatrician for regular checkups and growth assessments.',
      'overall_insights': 'Your baby\'s development is progressing well. Continue with consistent care routines.',
      'overall_recommendations': 'Maintain regular feeding, sleeping, and play schedules for optimal development.',
    };
  }
}

class RateLimitException implements Exception {
  final Duration timeRemaining;
  RateLimitException(this.timeRemaining);
}