import 'package:flutter/foundation.dart';
import '../../../core/app_export.dart';
import '../../../services/ai_insights_service.dart';
import '../../../services/ai_insights_state_manager.dart';
import '../../../services/baby_profile_state_manager.dart';

/// Controller for AI Insights Dashboard following separation of concerns
class AIInsightsDashboardController extends ChangeNotifier {
  final AIInsightsStateManager _aiInsightsManager = AIInsightsStateManager();
  final AIAnalysisService _aiAnalysisService = AIAnalysisService();
  final AIInsightsService _aiInsightsService = AIInsightsService();
  final SupabaseService _supabaseService = SupabaseService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();

  // State variables
  Map<String, String> _aiGeneratedInsights = {};
  List<ActivityLog> _recentActivities = [];
  bool _isLoading = true;
  String? _errorMessage;
  BabyProfile? _babyProfile;

  // Getters
  Map<String, String> get aiGeneratedInsights => _aiGeneratedInsights;
  List<ActivityLog> get recentActivities => _recentActivities;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  BabyProfile? get babyProfile => _babyProfile;

  void initialize(BabyProfile? initialBabyProfile) {
    _aiInsightsManager.addListener(_onAIInsightsUpdated);
    _babyProfileManager.addListener(_onBabyProfileChanged);
    _loadActiveBaby(initialBabyProfile);
  }

  @override
  void dispose() {
    _aiInsightsManager.removeListener(_onAIInsightsUpdated);
    _babyProfileManager.removeListener(_onBabyProfileChanged);
    super.dispose();
  }

  void _onAIInsightsUpdated() {
    notifyListeners();
  }

  void _onBabyProfileChanged() {
    debugPrint('🔄 Baby profile changed in AI Insights Dashboard, reloading');
    _loadActiveBaby(null);
  }

  Future<void> _loadActiveBaby(BabyProfile? initialBabyProfile) async {
    try {
      BabyProfile? activeBaby = _babyProfileManager.activeBaby ?? initialBabyProfile;
      
      if (activeBaby != null) {
        debugPrint('✅ Using active baby: ${activeBaby.name} (${activeBaby.id})');
        _babyProfile = activeBaby;
        await loadInsights();
      } else {
        debugPrint('❌ No baby profile available for AI Insights Dashboard');
        _setError('No baby profile available');
      }
    } catch (e) {
      debugPrint('❌ Error loading active baby: $e');
      _setError('Error loading baby profile: $e');
    }
  }

  Future<void> loadInsights() async {
    if (_babyProfile == null) {
      _setError('No baby profile available');
      return;
    }
    
    try {
      _setLoading(true);


      await _loadRealInsights();
    } catch (e) {
      _setError('Error loading insights: $e');
      _aiGeneratedInsights = _getDefaultAIInsights();
    }
  }


  Future<void> _loadRealInsights() async {
    final activities = await _supabaseService.getRecentActivities(_babyProfile!.id, limit: 200);
    _recentActivities = activities;

    _aiInsightsManager.trackUserActivity();
    
    if (!_aiInsightsManager.hasInsightsForBaby(_babyProfile!.id)) {
      // FIXED: Don't duplicate loadInsights() call - Home screen already handles this
      // await _aiInsightsManager.loadInsights(_babyProfile!);
    }
    
    await _loadAITextInsights();
    _setLoading(false);
  }

  Future<void> _loadAITextInsights() async {
    try {
      final sharedInsights = _aiInsightsManager.insights;
      
      if (sharedInsights.isNotEmpty) {
        _aiGeneratedInsights = _convertSharedInsightsToTextFormat(sharedInsights);
      } else {
        _aiGeneratedInsights = _getDefaultAIInsights();
      }
      
      notifyListeners();
    } catch (e) {
      debugPrint('❌ Error loading AI text insights: $e');
      _aiGeneratedInsights = _getDefaultAIInsights();
      notifyListeners();
    }
  }


  void _setLoading(bool loading) {
    _isLoading = loading;
    _errorMessage = null;
    notifyListeners();
  }

  void _setError(String error) {
    _errorMessage = error;
    _isLoading = false;
    notifyListeners();
  }

  // Helper methods would be moved here from the main widget
  Map<String, String> _convertSharedInsightsToTextFormat(Map<String, dynamic> sharedInsights) {
    // Implementation moved from main widget
    return {};
  }

  Map<String, String> _getDefaultAIInsights() {
    // Implementation moved from main widget
    return {};
  }

}