import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'dart:ui' as ui;
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';

import '../../core/app_export.dart';
import '../../theme/theme_aware_colors.dart';
import '../ai_insights/widgets/milestone_predictions_widget.dart';
import '../../services/ai_insights_service.dart';
import '../../services/ai_insights_state_manager.dart';
import '../../services/baby_profile_state_manager.dart';
import '../subscription/controllers/subscription_controller.dart';
import '../subscription/widgets/premium_feature_card.dart';


class AIInsightsDashboard extends StatefulWidget {
  final BabyProfile? babyProfile;
  const AIInsightsDashboard({super.key, this.babyProfile});

  @override
  State<AIInsightsDashboard> createState() => _AIInsightsDashboardState();
}

class _AIInsightsDashboardState extends State<AIInsightsDashboard>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final AIInsightsStateManager _aiInsightsManager = AIInsightsStateManager();
  final AIAnalysisService _aiAnalysisService = AIAnalysisService();
  final AIInsightsService _aiInsightsService = AIInsightsService();
  final SupabaseService _supabaseService = SupabaseService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();

  Map<String, String> _aiGeneratedInsights = {};
  List<ActivityLog> _recentActivities = [];
  bool _isLoading = true;
  String? _errorMessage;
  BabyProfile? _babyProfile;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this); // Updated activity types without Tips and Bath
    _aiInsightsManager.addListener(_onAIInsightsUpdated);
    _babyProfileManager.addListener(_onBabyProfileChanged);
    _loadActiveBaby();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _aiInsightsManager.removeListener(_onAIInsightsUpdated);
    _babyProfileManager.removeListener(_onBabyProfileChanged);
    super.dispose();
  }
  
  void _onAIInsightsUpdated() {
    if (mounted) {
      setState(() {
        // Trigger rebuild when AI insights state changes
      });
    }
  }
  
  void _onBabyProfileChanged() {
    if (mounted) {
      debugPrint('🔄 Baby profile changed in AI Insights Dashboard, reloading');
      _loadActiveBaby();
    }
  }
  
  Future<void> _loadActiveBaby() async {
    try {
      // Try to get active baby from state manager first
      BabyProfile? activeBaby = _babyProfileManager.activeBaby;
      
      if (activeBaby != null) {
        debugPrint('✅ Using active baby from state manager: ${activeBaby.name} (${activeBaby.id})');
        _babyProfile = activeBaby;
      } else {
        // Fallback to widget parameter
        _babyProfile = widget.babyProfile;
        debugPrint('⚠️ No active baby in state manager, using widget parameter: ${_babyProfile?.name}');
      }
      
      if (_babyProfile != null) {
        debugPrint('🎨 AI Insights Dashboard initialized for baby: ${_babyProfile!.name}');
        await _loadInsights();
      } else {
        debugPrint('❌ No baby profile available for AI Insights Dashboard');
        setState(() {
          _errorMessage = 'No baby profile available';
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('❌ Error loading active baby: $e');
      setState(() {
        _errorMessage = 'Error loading baby profile: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadInsights() async {
    if (_babyProfile == null) {
      debugPrint('❌ Cannot load insights: no baby profile available');
      setState(() {
        _errorMessage = 'No baby profile available';
        _isLoading = false;
      });
      return;
    }
    
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });


      // Fetch real activity logs from Supabase for chart generation
      final activities = await _supabaseService.getRecentActivities(_babyProfile!.id, limit: 200);
      _recentActivities = activities;

      // ✅ FIXED: Only use shared state manager - no duplicate API calls
      _aiInsightsManager.trackUserActivity(); // Track that user opened the app
      
      // Always use existing insights from shared state manager
      // The Home screen is responsible for loading insights initially
      debugPrint('📱 Dashboard: Using shared insights state for baby ${_babyProfile!.name}');
      debugPrint('📱 Dashboard: Has insights in state manager: ${_aiInsightsManager.hasInsightsForBaby(_babyProfile!.id)}');
      
      // No need to load insights separately; rely on shared state
      if (!_aiInsightsManager.hasInsightsForBaby(_babyProfile!.id)) {
          debugPrint('📱 Dashboard: No insights loaded, using shared state');
      }
      
      // Try to load AI text insights from the shared state
      await _loadAITextInsights(fromCache: true);
      
      // Ensure loading state is set to false at the end
      setState(() {
        _isLoading = false;
      });
      
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error loading insights: $e';
        _aiGeneratedInsights = _getDefaultAIInsights(); // Use appropriate default insights instead of demo
      });
      debugPrint('❌ Error loading AI insights: $e');
    }
  }

  Future<void> _loadAITextInsights({required bool fromCache}) async {
    try {
      // Always use the shared state manager as the source of truth
      final sharedInsights = _aiInsightsManager.insights;
      
      if (sharedInsights.isNotEmpty) {
        setState(() {
          _aiGeneratedInsights = _convertSharedInsightsToTextFormat(sharedInsights);
        });
        return;
      }
      
      setState(() {
        _aiGeneratedInsights = _getDefaultAIInsights();
      });
      
    } catch (e) {
      debugPrint('❌ Error loading AI text insights: $e');
      setState(() {
        _aiGeneratedInsights = _getDefaultAIInsights();
      });
    }
  }

  Future<void> _manualRefreshAI() async {
    if (_aiInsightsManager.isUpdating) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'AI analysis already in progress...',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: ThemeAwareColors.getWarningColor(context),
        ),
      );
      return;
    }

    // Development mode check - allow bypassing rate limits with long press
    bool isDevMode = false;
    const bool kDebugMode = bool.fromEnvironment('dart.vm.product') == false;
    
    // Check rate limiting through state manager (skip in dev mode if forced)
    if (!_aiInsightsManager.canManualRefresh() && !isDevMode) {
      final timeRemaining = _aiInsightsManager.getTimeUntilNextRefresh();
      if (timeRemaining != null) {
        final hoursRemaining = timeRemaining.inHours;
        final minutesRemaining = timeRemaining.inMinutes % 60;
        
        String timeMessage;
        String waitTimeMessage;
        if (hoursRemaining > 0) {
          timeMessage = '$hoursRemaining hour${hoursRemaining > 1 ? 's' : ''}';
          if (minutesRemaining > 0) {
            timeMessage += ' $minutesRemaining minute${minutesRemaining > 1 ? 's' : ''}';
          }
          waitTimeMessage = 'wait $timeMessage';
        } else {
          timeMessage = '$minutesRemaining minute${minutesRemaining > 1 ? 's' : ''}';
          waitTimeMessage = 'wait $timeMessage';
        }
        
        // In debug mode, show option to force refresh
        if (kDebugMode) {
          final shouldForceUpdate = await showDialog<bool>(
            context: context,
            builder: (context) => AlertDialog(
              title: Text('Rate Limited (Dev Mode)'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Rate limit active: $waitTimeMessage remaining.'),
                  SizedBox(height: 8),
                  Text('Force refresh anyway? (Dev mode only)', 
                       style: TextStyle(color: ThemeAwareColors.getWarningColor(context), fontWeight: FontWeight.bold)),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context, false),
                  child: Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: () => Navigator.pop(context, true),
                  style: ElevatedButton.styleFrom(backgroundColor: ThemeAwareColors.getWarningColor(context)),
                  child: Text('Force Refresh'),
                ),
              ],
            ),
          );
          
          if (shouldForceUpdate == true) {
            isDevMode = true; // Allow bypass
          } else {
            return;
          }
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'No updates available. Last update was ${_aiInsightsManager.formattedLastUpdate ?? 'recently'}. Please log more activities or $waitTimeMessage to refresh insights.',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: ThemeAwareColors.getWarningColor(context),
              duration: Duration(seconds: 5),
            ),
          );
          return;
        }
      }
    }

    // Show confirmation dialog
    final shouldUpdate = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isDevMode ? 'Force Update AI Analysis (Dev Mode)' : 'Update AI Analysis'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('This will generate fresh AI insights using the latest data. This may take 10-30 seconds.'),
            if (isDevMode) ...[
              SizedBox(height: 8),
              Text('⚠️ Rate limit bypassed in development mode', 
                   style: TextStyle(color: ThemeAwareColors.getWarningColor(context), fontSize: 12)),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('Update'),
          ),
        ],
      ),
    );

    if (shouldUpdate != true) return;

    try {
      debugPrint('🔄 Manual AI refresh requested via shared state manager');
      
      // Use state manager for manual refresh
      if (_babyProfile == null) {
        throw Exception('No baby profile available');
      }
      
      final success = await _aiInsightsManager.manualRefresh(_babyProfile!, forceRefresh: isDevMode);
      
      if (success) {
        // Update AI text insights from the refreshed shared state
        await _loadAITextInsights(fromCache: true);
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isDevMode 
                ? '✅ AI insights refreshed successfully (Dev mode override)'
                : '✅ AI insights refreshed successfully',
              style: TextStyle(color: Colors.white),
            ),
            backgroundColor: isDevMode ? ThemeAwareColors.getWarningColor(context) : ThemeAwareColors.getSuccessColor(context),
            duration: Duration(seconds: 2),
          ),
        );
      } else {
        // Check if it's a "no new data" scenario vs rate limiting
        final timeRemaining = _aiInsightsManager.getTimeUntilNextRefresh();
        if (timeRemaining == null) {
          // Not rate limited, so it must be no new data
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'No new insights available. Continue logging activities to receive updated insights and recommendations.',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: Theme.of(context).colorScheme.primary,
              duration: Duration(seconds: 4),
            ),
          );
        } else {
          // Rate limited
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                '❌ Manual refresh failed or rate limited',
                style: TextStyle(color: Colors.white),
              ),
              backgroundColor: ThemeAwareColors.getWarningColor(context),
              duration: Duration(seconds: 4),
            ),
          );
        }
      }
      
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            '❌ AI insights updates not available. Please log more activities or wait for a while to get updated insights.',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: ThemeAwareColors.getWarningColor(context),
          duration: Duration(seconds: 4),
        ),
      );
    }
  }

  String _formatTimeDifference(Duration difference) {
    if (difference.inDays > 0) {
      final days = difference.inDays;
      final hours = difference.inHours % 24;
      if (hours > 0) {
        return '$days day${days > 1 ? 's' : ''} $hours hour${hours > 1 ? 's' : ''}';
      }
      return '$days day${days > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      if (minutes > 0) {
        return '$hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''}';
      }
      return '$hours hour${hours > 1 ? 's' : ''}';
    } else {
      final minutes = difference.inMinutes;
      return '$minutes minute${minutes > 1 ? 's' : ''}';
    }
  }


  /// Convert shared insights from state manager to text format expected by dashboard
  Map<String, String> _convertSharedInsightsToTextFormat(Map<String, dynamic> sharedInsights) {
    // Safe casting to handle potential type mismatches
    final sleepAnalysis = sharedInsights['sleepAnalysis'] is Map<String, dynamic> 
        ? sharedInsights['sleepAnalysis'] as Map<String, dynamic> : null;
    final feedingAnalysis = sharedInsights['feedingAnalysis'] is Map<String, dynamic> 
        ? sharedInsights['feedingAnalysis'] as Map<String, dynamic> : null;
    final growthAnalysis = sharedInsights['growthAnalysis'] is Map<String, dynamic> 
        ? sharedInsights['growthAnalysis'] as Map<String, dynamic> : null;
    final milestoneAnalysis = sharedInsights['milestoneAnalysis'] is Map<String, dynamic> 
        ? sharedInsights['milestoneAnalysis'] as Map<String, dynamic> : null;
    final overallSummary = sharedInsights['overallSummary'] is Map<String, dynamic> 
        ? sharedInsights['overallSummary'] as Map<String, dynamic> : null;
    
    return {
      'sleep_insights': _extractDetailedInsights(sleepAnalysis) ?? 'Continue tracking sleep patterns to get personalized insights.',
      'sleep_recommendations': _extractRecommendations(sleepAnalysis),
      'feeding_insights': _extractDetailedInsights(feedingAnalysis) ?? 'Keep logging feeding sessions to understand nutrition patterns.',
      'feeding_recommendations': _extractRecommendations(feedingAnalysis),
      'diaper_insights': 'Regular diaper changes indicate healthy digestion patterns.',
      'diaper_recommendations': 'Continue monitoring diaper changes for health indicators.',
      'growth_insights': _extractDetailedInsights(growthAnalysis) ?? 'Track growth measurements regularly to monitor development.',
      'growth_recommendations': _extractRecommendations(growthAnalysis),
      'overall_insights': overallSummary?['mainSummary'] ?? _generateOverallInsightsText(sharedInsights),
      'overall_recommendations': overallSummary?['topRecommendations']?.join('. ') ?? _generateOverallRecommendationsText(sharedInsights),
    };
  }
  
  /// Convert cached insights from analysis service to text format expected by dashboard (legacy support)
  Map<String, String> _convertCachedInsightsToTextFormat(Map<String, dynamic> cachedInsights) {
    return _convertSharedInsightsToTextFormat(cachedInsights);
  }
  
  /// Safely cast dynamic map to Map<String, dynamic>
  Map<String, dynamic>? _safeMapCast(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) {
      return value;
    } else if (value is Map) {
      return Map<String, dynamic>.from(value);
    }
    return null;
  }
  
  String _extractRecommendations(Map<String, dynamic>? analysis) {
    if (analysis == null) return 'Continue logging activities for personalized recommendations.';
    
    final dataField = analysis['data'];
    final Map<String, dynamic>? data;
    if (dataField is Map<String, dynamic>) {
      data = dataField;
    } else if (dataField is Map) {
      data = Map<String, dynamic>.from(dataField);
    } else {
      data = null;
    }
    
    final recommendations = data?['recommendations'] as List?;
    
    if (recommendations != null && recommendations.isNotEmpty) {
      return recommendations.take(3).join('. ') + '.';
    }
    
    return 'Continue logging activities for personalized recommendations.';
  }
  
  /// Extract detailed insights from data.insights array instead of generic description
  String? _extractDetailedInsights(Map<String, dynamic>? analysis) {
    if (analysis == null) return null;
    
    // First try to get detailed insights from data.insights array
    final dataField = analysis['data'];
    final Map<String, dynamic>? data;
    if (dataField is Map<String, dynamic>) {
      data = dataField;
    } else if (dataField is Map) {
      data = Map<String, dynamic>.from(dataField);
    } else {
      data = null;
    }
    
    final insights = data?['insights'] as List?;
    
    if (insights != null && insights.isNotEmpty) {
      // Join the insight sentences into a coherent paragraph
      final insightText = insights.join(' ');
      return insightText;
    }
    
    // Fallback to description if insights array is not available
    final description = analysis['description'] as String?;
    if (description != null && description.isNotEmpty && !description.toLowerCase().contains('not enough data')) {
      return description;
    }
    
    return null;
  }
  
  String _generateOverallInsightsText(Map<String, dynamic> insights) {
    final components = <String>[];
    
    if (insights['sleepAnalysis'] != null) {
      final confidence = (insights['sleepAnalysis']['confidence'] as num?) ?? 0;
      if (confidence > 0.2) components.add('sleep patterns analyzed');
    }
    
    if (insights['feedingAnalysis'] != null) {
      final confidence = (insights['feedingAnalysis']['confidence'] as num?) ?? 0;
      if (confidence > 0.2) components.add('feeding schedule reviewed');
    }
    
    if (insights['growthAnalysis'] != null) {
      final confidence = (insights['growthAnalysis']['confidence'] as num?) ?? 0;
      if (confidence > 0.2) components.add('growth trends tracked');
    }
    
    if (components.isEmpty) {
      return 'Continue logging activities to unlock personalized AI insights and recommendations.';
    }
    
    return '${_capitalize(components.join(', '))}. AI insights are being generated based on your data.';
  }
  
  String _generateOverallRecommendationsText(Map<String, dynamic> insights) {
    final recommendations = <String>[];
    
    // Extract top recommendations from each category
    for (final category in ['sleepAnalysis', 'feedingAnalysis', 'growthAnalysis']) {
      final analysisField = insights[category];
      final Map<String, dynamic>? analysis;
      if (analysisField is Map<String, dynamic>) {
        analysis = analysisField;
      } else if (analysisField is Map) {
        analysis = Map<String, dynamic>.from(analysisField);
      } else {
        analysis = null;
      }
      
      if (analysis != null) {
        final dataField = analysis['data'];
        final Map<String, dynamic>? data;
        if (dataField is Map<String, dynamic>) {
          data = dataField;
        } else if (dataField is Map) {
          data = Map<String, dynamic>.from(dataField);
        } else {
          data = null;
        }
        
        final recs = data?['recommendations'] as List?;
        if (recs != null && recs.isNotEmpty) {
          recommendations.add(recs.first.toString());
        }
      }
    }
    
    if (recommendations.isEmpty) {
      return 'Maintain consistent routines for feeding, sleeping, and growth tracking.';
    }
    
    return recommendations.take(3).join('. ') + '.';
  }
  
  String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
  
  /// Get default AI insights when no cached data is available
  Map<String, String> _getDefaultAIInsights() {
    return {
      'sleep_insights': 'Continue tracking sleep patterns to get personalized insights about rest quality and schedule.',
      'sleep_recommendations': 'Maintain consistent bedtime routines and ensure a calm sleep environment.',
      'feeding_insights': 'Keep logging feeding sessions to understand nutrition patterns and preferences.',
      'feeding_recommendations': 'Follow your baby\'s hunger cues and maintain regular feeding schedules.',
      'diaper_insights': 'Regular diaper changes indicate healthy digestion and hydration patterns.',
      'diaper_recommendations': 'Continue monitoring diaper changes as they reflect your baby\'s health.',
      'growth_insights': 'Track growth measurements regularly to monitor healthy development.',
      'growth_recommendations': 'Consult your pediatrician for regular checkups and growth assessments.',
      'overall_insights': 'Your baby\'s development is progressing well. Continue with consistent care routines.',
      'overall_recommendations': 'Maintain regular feeding, sleeping, and play schedules for optimal development.',
    };
  }

  

  Future<void> _generateInsights() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Use state manager instead of direct service call to prevent duplicate API calls
      final stateManager = AIInsightsStateManager();
      await stateManager.loadInsights(_babyProfile!);

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to generate AI insights: $e';
      });
      debugPrint('Error generating insights: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return Scaffold(
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text('AI Insights'),
          backgroundColor: Colors.transparent,
          elevation: 0,
          scrolledUnderElevation: 0,
        ),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(height: 2.h),
              Text(
                'Loading AI insights...',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return Scaffold(
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            if (_babyProfile != null)
              Text(
                'for ${_babyProfile!.name}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.normal,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
          ],
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            if (mounted && Navigator.canPop(context)) {
              Navigator.pop(context);
            } else {
              Navigator.pushReplacementNamed(context, '/main-navigation');
            }
          },
        ),
        actions: [
          Consumer<SubscriptionController>(
            builder: (context, subscriptionController, _) {
              // Hide refresh button for free users
              if (subscriptionController.isOnFreePlan) {
                return SizedBox.shrink();
              }
              
              return IconButton(
                icon: _aiInsightsManager.isUpdating 
                    ? SizedBox(
                        width: 18,
                        height: 18,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(ThemeAwareColors.getPrimaryColor(context)),
                        ),
                      )
                    : Icon(Icons.refresh, size: 22),
                onPressed: (_isLoading || _aiInsightsManager.isUpdating) ? null : _manualRefreshAI,
                tooltip: 'Refresh Analysis',
              );
            },
          ),
        ],
      ),
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return _buildLoadingState();
    }

    if (_errorMessage != null) {
      return _buildErrorState();
    }

    return _buildInsightsDashboard();
  }

  Widget _buildLoadingState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 20.w,
                height: 20.w,
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
              ),
              CircularProgressIndicator(
                strokeWidth: 3,
                valueColor: AlwaysStoppedAnimation<Color>(
                  ThemeAwareColors.getPrimaryColor(context),
                ),
              ),
              CustomIconWidget(
                iconName: 'psychology',
                color: ThemeAwareColors.getPrimaryColor(context),
                size: 8.w,
              ),
            ],
          ),
          SizedBox(height: 3.h),
          Text(
            'Analyzing Baby Care Data',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w700,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'AI is processing sleep, feeding, growth, and development patterns',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: 2.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'This may take 10-30 seconds',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getPrimaryColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'error_outline',
              color: ThemeAwareColors.getErrorColor(context),
              size: 64,
            ),
            SizedBox(height: 2.h),
            Text(
              'Oops! Something went wrong',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              _errorMessage ?? 'Unknown error occurred',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
            SizedBox(height: 3.h),
            ElevatedButton(
              onPressed: _loadInsights,
              child: Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInsightsDashboard() {
    return Column(
      children: [
        // Compact Tab Navigation
        Container(
          margin: EdgeInsets.fromLTRB(3.w, 1.h, 3.w, 0.5.h),
          decoration: BoxDecoration(
            color: ThemeAwareColors.getCardColor(context),
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: ThemeAwareColors.getShadowColor(context),
                blurRadius: 6,
                offset: Offset(0, 1),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              padding: EdgeInsets.all(0.3.w),
              tabAlignment: TabAlignment.start,
              dividerColor: Colors.transparent,
              labelColor: Theme.of(context).brightness == Brightness.dark 
                  ? Colors.black 
                  : Colors.white,
              unselectedLabelColor: ThemeAwareColors.getSecondaryTextColor(context),
              indicator: BoxDecoration(
                color: ThemeAwareColors.getPrimaryColor(context),
                borderRadius: BorderRadius.circular(8),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              indicatorPadding: EdgeInsets.all(0.2.w),
              labelPadding: EdgeInsets.symmetric(horizontal: 2.w),
              labelStyle: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 11.sp,
              ),
              unselectedLabelStyle: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 11.sp,
              ),
              tabs: _buildCompactTabs(),
            ),
          ),
        ),
        
        // Tab Content with Rich Charts
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildAdvancedTabContent('sleep'),
              _buildAdvancedTabContent('feeding'),
              _buildAdvancedTabContent('growth'),
              _buildAdvancedTabContent('diaper'),
              _buildAdvancedTabContent('health'),
              _buildAdvancedTabContent('medicine'),
              _buildAdvancedTabContent('development'),
              _buildAdvancedTabContent('milestones'),
            ],
          ),
        ),
      ],
    );
  }
  
  List<Widget> _buildCompactTabs() {
    final tabData = [
      {'icon': 'bedtime', 'label': 'Sleep'},
      {'icon': 'restaurant', 'label': 'Feeding'},
      {'icon': 'trending_up', 'label': 'Growth'},
      {'icon': 'child_care', 'label': 'Diaper'},
      {'icon': 'local_hospital', 'label': 'Health'},
      {'icon': 'medication', 'label': 'Medicine'},
      {'icon': 'fitness_center', 'label': 'Development'},
      {'icon': 'emoji_events', 'label': 'Milestones'},
    ];
    
    return tabData.map((data) => Tab(
      height: 7.h,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 1.5.w, vertical: 0.8.h),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: data['icon']!,
              size: 4.w,
            ),
            SizedBox(height: 0.3.h),
            Text(
              data['label']!,
              style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    )).toList();
  }


  Widget _buildStatCard(String title, String value, String icon, Color color) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSurfaceColor(context),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomIconWidget(
              iconName: icon,
              color: color,
              size: 4.w,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            value,
            style: TextStyle(
              color: ThemeAwareColors.getPrimaryTextColor(context),
              fontSize: 16.sp,
              fontWeight: FontWeight.w700,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontSize: 9.sp,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedTabContent(String type) {
    // Handle milestones tab specially
    if (type == 'milestones') {
      return _buildMilestonesTab();
    }
    
    // Generate real chart data from activity logs
    final chartData = _generateChartDataFromActivities(type);
    
    // Get AI analysis data from shared state manager with safe casting
    final analysisData = _aiInsightsManager.insights['${type}Analysis'];
    final Map<String, dynamic> safeAnalysisData;
    if (analysisData is Map<String, dynamic>) {
      safeAnalysisData = analysisData;
    } else if (analysisData is Map) {
      safeAnalysisData = Map<String, dynamic>.from(analysisData);
    } else {
      safeAnalysisData = {};
    }
    
    final dataField = safeAnalysisData['data'];
    final Map<String, dynamic> data;
    if (dataField is Map<String, dynamic>) {
      data = dataField;
    } else if (dataField is Map) {
      data = Map<String, dynamic>.from(dataField);
    } else {
      data = {};
    }
    
    final recommendations = data['recommendations'] ?? [];
    
    debugPrint('📈 Tab content for $type: ${chartData.length} chart data points');
    debugPrint('🤖 Analysis data from shared state: $analysisData');
    
    return Padding(
      padding: EdgeInsets.all(3.w),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Main Chart Card
            _buildMainChartCard(type, chartData),
            
            SizedBox(height: 3.h),
            
            // Key Metrics
            _buildKeyMetrics(type, chartData),
            
            SizedBox(height: 3.h),
            
            // Combined AI Insights and Recommendations with Premium Access Control
            Consumer<SubscriptionController>(
              builder: (context, subscriptionController, _) {
                if (subscriptionController.isOnFreePlan) {
                  return Container(
                    margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Section Title - Always show
                        Text(
                          'AI Insights',
                          style: TextStyle(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: _getTypeColor(type),
                          ),
                        ),
                        SizedBox(height: 1.h),
                        // Premium Card
                        PremiumFeatureCard(
                          title: 'Unlock AI Insights',
                          description: 'Get personalized insights and recommendations',
                          icon: Icons.analytics,
                          compact: false,
                        ),
                      ],
                    ),
                  );
                }
                
                // Show full AI insights for premium users
                return _buildAIInsightsAndRecommendations(type, data, recommendations);
              },
            ),
            
            SizedBox(height: 5.h), // Bottom padding for scroll
          ],
        ),
      ),
    );
  }

  Widget _buildMainChartCard(String type, List<dynamic> chartData) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: _getTypeColor(type).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: _getTypeIcon(type),
                  color: _getTypeColor(type),
                  size: 4.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${type[0].toUpperCase()}${type.substring(1)} Analytics',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                      ),
                    ),
                    Text(
                      _getAnalyticsSubtitle(type),
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          // Simplified chart visualization with dynamic height
          Container(
            height: _getChartHeight(type, chartData), // Dynamic height based on data
            child: chartData.isNotEmpty
                ? _buildCompactChart(type, chartData)
                : _buildEmptyChartState(type),
          ),
          SizedBox(height: 1.h),
        ],
      ),
    );
  }

  String _getAnalyticsSubtitle(String type) {
    switch (type) {
      case 'sleep':
        return 'Sleep patterns & quality analysis';
      case 'feeding':
        return 'Feeding schedule & intake tracking';
      case 'growth':
        return 'Growth milestones & trends';
      case 'diaper':
        return 'Diaper changes & patterns';
      default:
        return 'Past 7 days analysis';
    }
  }

  Widget _buildCompactChart(String type, List<dynamic> chartData) {
    if (chartData.isEmpty) return _buildEmptyChartState(type);
    
    // Check for unusual high values and show error message
    final values = chartData.map((data) => _extractChartValue(type, data)).toList();
    final actualMax = values.isNotEmpty ? values.reduce((a, b) => a > b ? a : b) : 0.0;
    
    // Define thresholds for unusual values
    int threshold = 15;
    if (type == 'diaper') threshold = 20;
    
    if (actualMax > threshold) {
      return _buildErrorMessage(type, actualMax.toInt(), threshold);
    }
    
    // Use the real chart data directly (already generated with last 7 days)
    final last7DaysData = chartData;
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getTypeColor(type).withValues(alpha: 0.05),
            _getTypeColor(type).withValues(alpha: 0.02),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(2.w),
      child: Column(
        mainAxisSize: MainAxisSize.min, // Use minimum space needed
        children: [
          // Chart area
          Flexible( // Use Flexible instead of fixed Container
            child: Container(
              height: _getChartHeight(type, last7DaysData), // Fixed height
              child: ClipRect( // Clip any overflow
                child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  // Y-axis labels (compact)
                  Container(
                    width: 8.w,
                    height: _getChartHeight(type, last7DaysData),
                    child: Flexible(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        mainAxisSize: MainAxisSize.max,
                        children: _getCompactYAxisLabels(type, last7DaysData),
                      ),
                    ),
                  ),
                  SizedBox(width: 2.w),
                  // Chart bars - clean and simple
                  Expanded(
                    child: Container(
                      height: _getChartHeight(type, last7DaysData),
                      child: Stack(
                        children: [
                          // Horizontal grid lines
                          _buildHorizontalGridLines(type, last7DaysData),
                          // Chart bars
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: last7DaysData.asMap().entries.map((entry) {
                          final data = entry.value;
                          final value = _extractChartValue(type, data);
                          final maxValue = _getMaxValue(type, last7DaysData);
                          // Chart height calculations are now handled in LayoutBuilder
                          
                          return Expanded(
                            child: Center( // Center the entire bar container
                              child: Container(
                                width: 8.w, // Fixed width for consistent alignment
                                child: LayoutBuilder(
                                  builder: (context, constraints) {
                                  final containerHeight = constraints.maxHeight;
                                  final minBarHeight = 0.2.h; // Minimum visible bar
                                  
                                  // Calculate proportional bar height based on data value
                                  double proportionalHeight = 0.0;
                                  if (maxValue > 0 && value > 0) {
                                    // Use the actual max value from the scale, not the capped value
                                    final actualMaxValue = _getMaxValue(type, last7DaysData);
                                    // Use full container height for calculation to reach bottom
                                    proportionalHeight = (value / actualMaxValue) * containerHeight;
                                  }
                                  
                                  // Ensure minimum height for non-zero values, but allow full height
                                  final actualBarHeight = value > 0 
                                      ? proportionalHeight.clamp(minBarHeight, containerHeight)
                                      : 0.0; // Zero height for zero values
                                  
                                  // Show tooltip only if there's space and value > 0
                                  final showTooltip = value > 0 && actualBarHeight > 1.h;
                                  
                                  return Stack(
                                    children: [
                                      // Bar anchored to bottom
                                      Positioned(
                                        bottom: 0,
                                        left: 0,
                                        right: 0,
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.end,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // Proportional Bar Height - anchored to bottom
                                            if (actualBarHeight > 0)
                                              Container(
                                                height: actualBarHeight,
                                                width: 8.w, // Much thicker bars
                                                decoration: BoxDecoration(
                                                  gradient: LinearGradient(
                                                    begin: Alignment.bottomCenter,
                                                    end: Alignment.topCenter,
                                                    colors: [
                                                      _getTypeColor(type),
                                                      _getTypeColor(type).withValues(alpha: 0.7),
                                                    ],
                                                  ),
                                                  borderRadius: BorderRadius.circular(2),
                                                  boxShadow: [
                                                    BoxShadow(
                                                      color: _getTypeColor(type).withValues(alpha: 0.2),
                                                      blurRadius: 2,
                                                      offset: Offset(0, 1),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                          ],
                                        ),
                                      ),
                                      // Tooltip positioned above bar
                                      if (showTooltip && actualBarHeight > 2.h)
                                        Positioned(
                                          bottom: actualBarHeight + 0.2.h,
                                          left: 0,
                                          right: 0,
                                          child: Center(
                                            child: Container(
                                              padding: EdgeInsets.symmetric(horizontal: 0.5.w, vertical: 0.05.h),
                                              decoration: BoxDecoration(
                                                color: _getTypeColor(type),
                                                borderRadius: BorderRadius.circular(2),
                                              ),
                                              child: Text(
                                                _formatValue(type, value),
                                                style: TextStyle(
                                                  color: Theme.of(context).brightness == Brightness.dark 
                                                      ? Colors.black 
                                                      : Colors.white,
                                                  fontSize: 4.sp, // Much smaller font
                                                  fontWeight: FontWeight.w600,
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                    ],
                                  );
                                  },
                                ),
                              ),
                            ),
                          );
                            }).toList(),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
                ),
              ),
            ),
          ),
          SizedBox(height: 0.5.h), // Reduced spacing
          // X-axis day labels
          Row(
            children: [
              SizedBox(width: 8.w), // Align with Y-axis
              SizedBox(width: 2.w),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: last7DaysData.asMap().entries.map((entry) {
                    final data = entry.value;
                    final date = DateTime.parse(data['date']);
                    final dayName = DateFormat('EEE').format(date);
                    
                    return Expanded(
                      child: Center( // Center the day labels exactly like bars
                        child: Container(
                          width: 8.w, // Same width as bars for perfect alignment
                          child: Text(
                            dayName,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 8.sp,
                              color: ThemeAwareColors.getSecondaryTextColor(context),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _getCompactYAxisLabels(String type, List<dynamic> chartData) {
    final maxValue = _getMaxValue(type, chartData);
    
    List<Widget> labels = [];
    List<int> labelValues = [];
    
    // Generate more labels for better granularity with taller chart - ALWAYS include 0
    if (maxValue <= 5) {
      labelValues = [5, 4, 3, 2, 1, 0]; // 6 labels for 0-5 scale
    } else if (maxValue <= 10) {
      labelValues = [10, 8, 6, 4, 2, 0]; // 6 labels for 0-10 scale
    } else if (maxValue <= 15) {
      labelValues = [15, 12, 9, 6, 3, 0]; // 6 labels for 0-15 scale
    } else if (maxValue <= 20) {
      labelValues = [20, 16, 12, 8, 4, 0]; // 6 labels for 0-20 scale
    } else {
      // For growth charts with decimal values
      final stepSize = maxValue / 5;
      labelValues = [];
      for (int i = 5; i >= 0; i--) {
        labelValues.add((stepSize * i).round());
      }
    }
    
    for (final value in labelValues) {
      String label;
      
      switch (type) {
        case 'growth':
          label = value == 0 ? '0kg' : '${value.toStringAsFixed(1)}kg';
          break;
        case 'sleep':
        case 'feeding':
        case 'diaper':
        default:
          label = value.toString();
          break;
      }
      
      labels.add(
        Container(
          alignment: Alignment.centerRight,
          child: Text(
            label,
            style: TextStyle(
              fontSize: 6.sp, // Slightly larger font with taller chart
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      );
    }
    
    return labels;
  }

  Widget _buildHorizontalGridLines(String type, List<dynamic> chartData) {
    final maxValue = _getMaxValue(type, chartData);
    List<int> labelValues = [];
    
    // Use same label values as Y-axis for grid lines
    if (maxValue <= 5) {
      labelValues = [5, 4, 3, 2, 1, 0];
    } else if (maxValue <= 10) {
      labelValues = [10, 8, 6, 4, 2, 0];
    } else if (maxValue <= 15) {
      labelValues = [15, 12, 9, 6, 3, 0];
    } else if (maxValue <= 20) {
      labelValues = [20, 16, 12, 8, 4, 0];
    }
    
    return Positioned.fill(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: labelValues.map((value) {
          return Container(
            height: 0.5,
            color: ThemeAwareColors.getDividerColor(context),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildErrorMessage(String type, int actualValue, int threshold) {
    String activityName = type.toLowerCase();
    
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              color: ThemeAwareColors.getWarningColor(context),
              size: 6.w,
            ),
            SizedBox(height: 1.h),
            Text(
              'Unusual Activity Detected',
              style: TextStyle(
                fontSize: 10.sp,
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getWarningColor(context),
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 0.5.h),
            Flexible(
              child: Text(
                'There are $actualValue $activityName logs in a single day. This is unusually high and may affect chart accuracy.',
                style: TextStyle(
                  fontSize: 8.sp,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(height: 0.5.h),
            Flexible(
              child: Text(
                'Please review and remove any duplicate or incorrect logs to ensure accurate visual charts.',
                style: TextStyle(
                  fontSize: 7.sp,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  fontStyle: FontStyle.italic,
                  height: 1.2,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _extractChartValue(String type, dynamic data) {
    if (data == null) return 0.0;
    
    switch (type) {
      case 'sleep':
        // Use logs count for chart display (number of sleep sessions)
        return (data['logs'] ?? 0.0).toDouble();
      case 'feeding':
        return (data['sessions'] ?? 0.0).toDouble();
      case 'growth':
        return (data['weight'] ?? 0.0).toDouble();
      case 'diaper':
        final wet = (data['wet'] ?? 0.0).toDouble();
        final soiled = (data['soiled'] ?? 0.0).toDouble();
        return wet + soiled;
      default:
        return (data['value'] ?? 0.0).toDouble();
    }
  }

  double _getMaxValue(String type, List<dynamic> chartData) {
    final values = chartData.map((data) => _extractChartValue(type, data)).toList();
    if (values.isEmpty) return 5.0;
    
    final actualMax = values.reduce((a, b) => a > b ? a : b);
    
    switch (type) {
      case 'sleep':
        if (actualMax <= 5) return 5.0;
        if (actualMax <= 10) return 10.0;
        if (actualMax <= 15) return 15.0;
        return 15.0; // Cap at 15, will show error for values > 15
      case 'feeding':
        if (actualMax <= 5) return 5.0;
        if (actualMax <= 10) return 10.0;
        if (actualMax <= 15) return 15.0;
        return 15.0; // Cap at 15, will show error for values > 15
      case 'diaper':
        if (actualMax <= 5) return 5.0;
        if (actualMax <= 10) return 10.0;
        if (actualMax <= 15) return 15.0;
        if (actualMax <= 20) return 20.0;
        return 20.0; // Cap at 20, will show error for values > 20
      case 'growth':
        return actualMax * 1.2; // 20% padding for growth
      default:
        if (actualMax <= 5) return 5.0;
        if (actualMax <= 10) return 10.0;
        if (actualMax <= 15) return 15.0;
        return 15.0;
    }
  }

  String _formatValue(String type, double value) {
    switch (type) {
      case 'sleep':
        return '${value.toInt()}'; // Show number of logs
      case 'feeding':
        return '${value.toInt()}';
      case 'growth':
        return '${value.toStringAsFixed(1)}kg';
      case 'diaper':
        return '${value.toInt()}';
      default:
        return value.toStringAsFixed(1);
    }
  }
  
  double _getChartHeight(String type, List<dynamic> chartData) {
    if (chartData.isEmpty) return 12.h; // Taller height for better visibility
    
    final maxValue = _getMaxValue(type, chartData);
    final baseHeight = 12.h; // Taller base height
    
    // Taller height scaling for better granularity
    switch (type) {
      case 'sleep':
      case 'feeding':
        if (maxValue <= 5) return baseHeight;
        if (maxValue <= 10) return baseHeight + 2.h;
        if (maxValue <= 15) return baseHeight + 3.h;
        return baseHeight + 3.h; // Max height for error cases
      case 'diaper':
        if (maxValue <= 5) return baseHeight;
        if (maxValue <= 10) return baseHeight + 2.h;
        if (maxValue <= 15) return baseHeight + 3.h;
        if (maxValue <= 20) return baseHeight + 4.h;
        return baseHeight + 4.h; // Max height for error cases
      case 'growth':
        return baseHeight;
      default:
        if (maxValue <= 5) return baseHeight;
        if (maxValue <= 10) return baseHeight + 2.h;
        if (maxValue <= 15) return baseHeight + 3.h;
        return baseHeight + 3.h;
    }
  }

  List<Map<String, dynamic>> _generateLast7DaysData(String type, List<dynamic> chartData) {
    final now = DateTime.now();
    final last7Days = <Map<String, dynamic>>[];
    
    // Generate the last 7 days from today backwards (including today)
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayName = _getDayName(date.weekday);
      
      // Try to find real data for this date first
      Map<String, dynamic> dayData = {
        'date': date.toIso8601String(),
        'day': dayName,
        'fullDate': date,
      };
      
      // Look for real data from chartData that matches this date
      bool foundRealData = false;
      
      if (chartData.isNotEmpty) {
        for (var data in chartData) {
          if (data is Map<String, dynamic>) {
            // Try to match by date if available
            if (data.containsKey('date') || data.containsKey('day')) {
              // Use real data if found
              dayData.addAll(data);
              foundRealData = true;
              break;
            }
          }
        }
      }
      
      // If no real data found and we're in demo mode, generate demo data
      if (!foundRealData) {
        switch (type) {
          case 'sleep':
            dayData['hours'] = 7.5 + (i * 0.2) + (dayName == 'Sat' || dayName == 'Sun' ? 0.5 : 0);
            dayData['quality'] = 4.0 + (i * 0.1);
            break;
          case 'feeding':
            dayData['sessions'] = 6 + (i % 3);
            dayData['volume'] = 150 + (i * 10);
            break;
          case 'growth':
            dayData['weight'] = 7.0 + (i * 0.05);
            dayData['height'] = 65 + (i * 0.2);
            break;
          case 'diaper':
            dayData['changes'] = 7 + (i % 4);
            dayData['wet'] = 5 + (i % 3);
            dayData['soiled'] = 2 + (i % 2);
            break;
          default:
            dayData['value'] = 5 + (i * 0.5);
        }
      } else if (!foundRealData) {
        // No real data and not in demo mode - set to 0
        switch (type) {
          case 'sleep':
            dayData['hours'] = 0.0;
            break;
          case 'feeding':
            dayData['sessions'] = 0.0;
            break;
          case 'growth':
            dayData['weight'] = 0.0;
            break;
          case 'diaper':
            dayData['changes'] = 0.0;
            dayData['wet'] = 0.0;
            dayData['soiled'] = 0.0;
            break;
          default:
            dayData['value'] = 0.0;
        }
      }
      
      last7Days.add(dayData);
    }
    
    debugPrint('📊 Generated last 7 days data for $type: ${last7Days.map((d) => '${d['day']}: ${_extractChartValue(type, d)}').join(', ')}');
    return last7Days;
  }
  
  String _getDayName(int weekday) {
    switch (weekday) {
      case 1: return 'Mon';
      case 2: return 'Tue';
      case 3: return 'Wed';
      case 4: return 'Thu';
      case 5: return 'Fri';
      case 6: return 'Sat';
      case 7: return 'Sun';
      default: return 'Day';
    }
  }
  
  List<Map<String, dynamic>> _generateChartDataFromActivities(String type) {
    if (false) {
      // Use demo data if in demo mode
      final analysisData = _aiInsightsManager.insights['${type}Analysis'] as Map<String, dynamic>? ?? {};
      final data = analysisData['data'] as Map<String, dynamic>? ?? {};
      return List<Map<String, dynamic>>.from(data['chartData'] ?? []);
    }
    
    // Filter activities by type
    final typeActivities = _recentActivities.where((activity) {
      switch (type) {
        case 'sleep':
          return activity.type == ActivityType.sleep;
        case 'feeding':
          return activity.type == ActivityType.feeding;
        case 'growth':
          return activity.type == ActivityType.growth;
        case 'diaper':
          return activity.type == ActivityType.diaper;
        case 'health':
          return activity.type == ActivityType.doctor || activity.type == ActivityType.temperature;
        case 'medicine':
          return activity.type == ActivityType.medicine;
        case 'development':
          return activity.type == ActivityType.milestone || activity.type == ActivityType.play;
        default:
          return false;
      }
    }).toList();
    
    debugPrint('📈 Found ${typeActivities.length} $type activities to convert to chart data');
    
    // Generate last 7 days chart data
    final now = DateTime.now();
    final chartData = <Map<String, dynamic>>[];
    
    for (int i = 6; i >= 0; i--) {
      final date = now.subtract(Duration(days: i));
      final dayName = _getDayName(date.weekday);
      
      // Find activities for this day
      final dayActivities = typeActivities.where((activity) {
        final activityDate = activity.timestamp;
        return activityDate.year == date.year &&
               activityDate.month == date.month &&
               activityDate.day == date.day;
      }).toList();
      
      // Create chart data point for this day
      final dayData = {
        'day': dayName,
        'date': date.toIso8601String(),
        'fullDate': date,
      };
      
      // Add type-specific data
      switch (type) {
        case 'sleep':
          double totalHours = 0;
          double totalQuality = 0;
          int sleepCount = dayActivities.length; // Count all sleep activities for the day
          
          for (final activity in dayActivities) {
            if (activity.duration != null) {
              totalHours += activity.duration!.inMinutes / 60.0;
            }
            if (activity.notes != null && activity.notes!.isNotEmpty) {
              // Try to extract quality from notes (simplified)
              totalQuality += 4.0; // Default quality
            }
          }
          
          dayData['logs'] = sleepCount; // Number of sleep logs
          dayData['hours'] = totalHours; // Total hours
          dayData['quality'] = sleepCount > 0 ? (totalQuality / sleepCount) : 0.0;
          
          debugPrint('🛌 ${dayData['day']}: $sleepCount sleep logs, ${totalHours.toStringAsFixed(1)} hours');
          break;
          
        case 'feeding':
          dayData['sessions'] = dayActivities.length;
          double totalVolume = 0;
          for (final activity in dayActivities) {
            if (activity.notes != null) {
              // Try to extract volume from notes if available
              final volumeMatch = RegExp(r'(\d+)\s*ml').firstMatch(activity.notes!);
              if (volumeMatch != null) {
                totalVolume += double.tryParse(volumeMatch.group(1)!) ?? 0;
              } else {
                totalVolume += 150; // Default volume
              }
            } else {
              totalVolume += 150; // Default volume
            }
          }
          dayData['volume'] = totalVolume;
          break;
          
        case 'growth':
          // For growth, we need weight/height measurements
          if (dayActivities.isNotEmpty) {
            dayData['weight'] = 7.0; // Would need to extract from notes or separate measurements
            dayData['height'] = 65.0;
          } else {
            dayData['weight'] = 0.0;
            dayData['height'] = 0.0;
          }
          break;
          
        case 'diaper':
          int wetCount = 0;
          int soiledCount = 0;
          
          for (final activity in dayActivities) {
            if (activity.notes != null) {
              final notes = activity.notes!.toLowerCase();
              if (notes.contains('wet') || notes.contains('urine')) {
                wetCount++;
              } else if (notes.contains('soiled') || notes.contains('poop') || notes.contains('stool')) {
                soiledCount++;
              } else {
                wetCount++; // Default to wet
              }
            } else {
              wetCount++; // Default
            }
          }
          
          dayData['changes'] = wetCount + soiledCount;
          dayData['wet'] = wetCount;
          dayData['soiled'] = soiledCount;
          break;
          
        default:
          dayData['value'] = dayActivities.length.toDouble();
      }
      
      chartData.add(dayData);
    }
    
    debugPrint('📊 Generated chart data for $type: ${chartData.map((d) => '${d['day']}: ${_extractChartValue(type, d)}').join(', ')}');
    return chartData;
  }
  
  String _getDefaultKey(String type) {
    switch (type) {
      case 'sleep': return 'hours';
      case 'feeding': return 'sessions';
      case 'growth': return 'weight';
      case 'diaper': return 'changes';
      default: return 'value';
    }
  }

  String _getDayLabel(dynamic data, int index) {
    if (data['day'] != null) {
      return data['day'].toString().length > 3 
        ? data['day'].toString().substring(0, 3)
        : data['day'].toString();
    }
    return _getDayName(DateTime.now().subtract(Duration(days: 6 - index)).weekday);
  }

  Map<String, String> _getDemoAIInsights() {
    return {
      'sleep_insights': 'Your baby shows excellent sleep patterns with consistent 8+ hour nights. Sleep quality has improved 15% over the past week.',
      'sleep_recommendations': 'Continue current bedtime routine. Consider adding white noise for deeper sleep during growth spurts.',
      'feeding_insights': 'Feeding schedule is well-established with healthy appetite. Average 7 sessions per day indicates good nutrition.',
      'feeding_recommendations': 'Maintain current feeding intervals. Introduce new foods gradually as baby shows readiness cues.',
      'diaper_insights': 'Healthy diaper patterns with 8-9 changes daily. Good hydration and digestion indicators observed.',
      'diaper_recommendations': 'Continue monitoring for consistency changes. Current pattern suggests excellent health.',
      'growth_insights': 'Steady growth trajectory in 65th percentile. Weight gain of 0.8kg this month is ideal for age.',
      'growth_recommendations': 'Schedule next pediatric checkup. Continue balanced nutrition for optimal development.',
      'health_insights': 'All health indicators show positive trends. Temperature regulation is excellent and no concerning symptoms.',
      'health_recommendations': 'Continue regular checkups. Monitor for any changes in appetite or energy levels.',
      'medicine_insights': 'Vitamin D supplement adherence is excellent at 98%. No side effects observed.',
      'medicine_recommendations': 'Continue current medication schedule. Consult pediatrician before any changes.',
      'development_insights': 'Motor skills are developing ahead of schedule. Shows strong social engagement and curiosity.',
      'development_recommendations': 'Provide varied sensory experiences. Continue tummy time and interactive play.',
      'overall_insights': 'Your baby is thriving! All health indicators show positive trends and age-appropriate development.',
      'overall_recommendations': 'Keep up the excellent care routine. Focus on tummy time and social interaction for cognitive development.',
    };
  }

  String _getCompactInsights(String type, List<dynamic> chartData) {
    // First, try to get insights from shared state manager
    final analysisData = _aiInsightsManager.insights['${type}Analysis'] as Map<String, dynamic>?;
    if (analysisData != null) {
      // Try to extract detailed insights from data.insights array first
      final detailedInsights = _extractDetailedInsights(analysisData);
      if (detailedInsights != null && detailedInsights.isNotEmpty) {
        debugPrint('📊 Using detailed insights for $type: $detailedInsights');
        return detailedInsights;
      }
      
      // Fallback to description if detailed insights not available
      final description = analysisData['description'] as String?;
      if (description != null && description.isNotEmpty && !description.toLowerCase().contains('not enough data')) {
        debugPrint('📊 Using description fallback for $type: $description');
        return description;
      }
    }
    
    // If in demo mode, use demo insights
    if (false) {
      switch (type) {
        case 'sleep':
          return _aiGeneratedInsights['sleep_insights'] ?? 'Continue tracking sleep patterns for personalized insights.';
        case 'feeding':
          return _aiGeneratedInsights['feeding_insights'] ?? 'Keep logging feeding sessions for nutrition analysis.';
        case 'growth':
          return _aiGeneratedInsights['growth_insights'] ?? 'Track growth measurements for development insights.';
        case 'diaper':
          return _aiGeneratedInsights['diaper_insights'] ?? 'Monitor diaper changes for health indicators.';
        case 'health':
          return _aiGeneratedInsights['health_insights'] ?? 'Track health indicators for wellness monitoring.';
        case 'medicine':
          return _aiGeneratedInsights['medicine_insights'] ?? 'Monitor medication adherence and effectiveness.';
        case 'development':
          return _aiGeneratedInsights['development_insights'] ?? 'Track developmental milestones and progress.';
        case 'milestones':
          return 'AI-powered milestone predictions show your baby is developing well. Continue documenting achievements.';
        default:
          return _aiGeneratedInsights['overall_insights'] ?? 'Continue logging activities for comprehensive analysis.';
      }
    }
    
    // For real data mode, analyze the actual chart data
    switch (type) {
      case 'sleep':
        return _getSleepInsights(chartData);
      case 'feeding':
        return _getFeedingInsights(chartData);
      case 'growth':
        return _getGrowthInsights(chartData);
      case 'diaper':
        return _getDiaperInsights(chartData);
      case 'health':
        return _getHealthInsights(chartData);
      case 'medicine':
        return _getMedicineInsights(chartData);
      case 'development':
        return _getDevelopmentInsights(chartData);
      case 'milestones':
        return 'AI-powered milestone predictions based on your baby\'s development patterns. Continue documenting achievements for better predictions.';
      default:
        // For other types, check if we have meaningful data
        if (chartData.isNotEmpty && chartData.any((data) => _extractChartValue(type, data) > 0)) {
          return 'Based on recent activity logs, patterns are being tracked. Continue logging for more detailed insights.';
        }
        return 'No ${type} data available yet. Start logging activities to see AI-powered insights and recommendations.';
    }
  }

  String _getCompactRecommendations(String type, List<dynamic> chartData) {
    // First, try to get recommendations from shared state manager
    final analysisData = _aiInsightsManager.insights['${type}Analysis'] as Map<String, dynamic>?;
    if (analysisData != null) {
      final data = (analysisData['data'] is Map<String, dynamic>) ? analysisData['data'] as Map<String, dynamic>? : <String, dynamic>{};
      final recommendations = data?['recommendations'] as List?;
      if (recommendations != null && recommendations.isNotEmpty) {
        final recommendationText = recommendations.take(3).join('. ') + '.';
        if (!recommendationText.toLowerCase().contains('not enough data') && !recommendationText.toLowerCase().contains('log your first')) {
          debugPrint('💡 Using shared state recommendations for $type: $recommendationText');
          return recommendationText;
        }
      }
    }
    
    // If in demo mode, use demo recommendations
    if (false) {
      switch (type) {
        case 'sleep':
          return _aiGeneratedInsights['sleep_recommendations'] ?? 'Continue tracking sleep patterns for personalized recommendations.';
        case 'feeding':
          return _aiGeneratedInsights['feeding_recommendations'] ?? 'Keep logging feeding sessions for nutrition recommendations.';
        case 'growth':
          return _aiGeneratedInsights['growth_recommendations'] ?? 'Track growth measurements for development recommendations.';
        case 'diaper':
          return _aiGeneratedInsights['diaper_recommendations'] ?? 'Monitor diaper changes for health recommendations.';
        case 'health':
          return _aiGeneratedInsights['health_recommendations'] ?? 'Continue health monitoring for wellness recommendations.';
        case 'medicine':
          return _aiGeneratedInsights['medicine_recommendations'] ?? 'Follow medication schedule for optimal health outcomes.';
        case 'development':
          return _aiGeneratedInsights['development_recommendations'] ?? 'Engage in developmental activities for growth.';
        case 'milestones':
          return 'Continue documenting milestones and provide age-appropriate stimulation for optimal development.';
        default:
          return _aiGeneratedInsights['overall_recommendations'] ?? 'Continue logging activities for comprehensive recommendations.';
      }
    }
    
    // For real data mode, generate recommendations based on actual chart data
    switch (type) {
      case 'sleep':
        return _getSleepRecommendations(chartData);
      case 'feeding':
        return _getFeedingRecommendations(chartData);
      case 'growth':
        return _getGrowthRecommendations(chartData);
      case 'diaper':
        return _getDiaperRecommendations(chartData);
      case 'health':
        return _getHealthRecommendations(chartData);
      case 'medicine':
        return _getMedicineRecommendations(chartData);
      case 'development':
        return _getDevelopmentRecommendations(chartData);
      case 'milestones':
        return 'Continue documenting milestones and provide age-appropriate stimulation for optimal development progress.';
      default:
        // For other types, check if we have meaningful data
        if (chartData.isNotEmpty && chartData.any((data) => _extractChartValue(type, data) > 0)) {
          return 'Continue tracking ${type} activities consistently to receive personalized AI recommendations.';
        }
        return 'Start logging ${type} activities to unlock personalized recommendations for better care.';
    }
  }

  Widget _buildRichChart(String type, List<dynamic> chartData) {
    // This method is now replaced by _buildCompactChart
    return _buildCompactChart(type, chartData);
  }

  // All complex chart methods removed to prevent overflow - using compact chart only

  Widget _buildGenericChart(List<dynamic> chartData) {
    return _buildCompactChart('sleep', chartData); // Use compact chart for default case
  }

  Widget _buildYAxisLabel(String label) {
    return Text(
      label,
      style: TextStyle(
        fontSize: 8.sp,
        color: Theme.of(context).colorScheme.onSurfaceVariant,
        fontWeight: FontWeight.w500,
      ),
    );
  }

  String _getSleepInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No sleep data available for analysis.';
    
    double avgHours = 0;
    int totalDays = 0;
    
    for (var data in chartData) {
      if (data['hours'] != null) {
        avgHours += data['hours'].toDouble();
        totalDays++;
      }
    }
    
    if (totalDays > 0) {
      avgHours /= totalDays;
      
      if (avgHours >= 10) {
        return 'Excellent sleep pattern! Your baby is getting optimal rest (${avgHours.toStringAsFixed(1)}h avg). Consider maintaining the current bedtime routine.';
      } else if (avgHours >= 8) {
        return 'Good sleep pattern with ${avgHours.toStringAsFixed(1)}h average. Consider extending sleep time by 30-60 minutes for optimal development.';
      } else {
        return 'Sleep pattern needs improvement (${avgHours.toStringAsFixed(1)}h avg). Try establishing a consistent bedtime routine and ensuring a calm sleep environment.';
      }
    }
    
    return 'Insufficient data for meaningful sleep analysis.';
  }

  String _getFeedingInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No feeding data available for analysis.';
    
    double avgSessions = 0;
    int totalDays = 0;
    
    for (var data in chartData) {
      if (data['sessions'] != null) {
        avgSessions += data['sessions'].toDouble();
        totalDays++;
      }
    }
    
    if (totalDays > 0) {
      avgSessions /= totalDays;
      
      if (avgSessions >= 8) {
        return 'Excellent feeding schedule! Your baby is getting frequent, regular meals (${avgSessions.toStringAsFixed(1)} sessions/day). This supports healthy growth.';
      } else if (avgSessions >= 6) {
        return 'Good feeding pattern with ${avgSessions.toStringAsFixed(1)} sessions per day. Consider adding 1-2 more sessions if baby seems hungry.';
      } else {
        return 'Feeding frequency may be low (${avgSessions.toStringAsFixed(1)} sessions/day). Consult your pediatrician about optimal feeding schedule.';
      }
    }
    
    return 'Insufficient data for meaningful feeding analysis.';
  }

  String _getGrowthInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No growth data available for analysis.';
    
    List<double> weights = [];
    for (var data in chartData) {
      if (data['weight'] != null) {
        weights.add(data['weight'].toDouble());
      }
    }
    
    if (weights.length >= 2) {
      double growth = weights.last - weights.first;
      
      if (growth > 0) {
        return 'Positive growth trend! Your baby has gained ${growth.toStringAsFixed(2)}kg over the tracking period. This indicates healthy development.';
      } else if (growth == 0) {
        return 'Stable weight maintained. If this continues, consult your pediatrician to ensure adequate nutrition and growth.';
      } else {
        return 'Weight loss detected (${growth.abs().toStringAsFixed(2)}kg). Please consult your pediatrician immediately for evaluation.';
      }
    }
    
    return 'Insufficient data for meaningful growth analysis.';
  }

  String _getDiaperInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No diaper data available for analysis.';
    
    double avgChanges = 0;
    int totalDays = 0;
    
    for (var data in chartData) {
      if (data['changes'] != null) {
        avgChanges += data['changes'].toDouble();
        totalDays++;
      }
    }
    
    if (totalDays > 0) {
      avgChanges /= totalDays;
      
      if (avgChanges >= 6) {
        return 'Healthy diaper change frequency! Your baby averages ${avgChanges.toStringAsFixed(1)} changes per day, indicating good hydration and digestion.';
      } else if (avgChanges >= 4) {
        return 'Moderate diaper change frequency (${avgChanges.toStringAsFixed(1)}/day). Monitor for signs of dehydration or constipation.';
      } else {
        return 'Low diaper change frequency (${avgChanges.toStringAsFixed(1)}/day). Please ensure adequate feeding and consult pediatrician if concerned.';
      }
    }
    
    return 'Insufficient data for meaningful diaper analysis.';
  }

  String _getSleepRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start logging sleep sessions to receive personalized sleep recommendations.';
    
    double avgHours = 0;
    int totalDays = 0;
    
    for (var data in chartData) {
      if (data['hours'] != null) {
        avgHours += data['hours'].toDouble();
        totalDays++;
      }
    }
    
    if (totalDays > 0) {
      avgHours /= totalDays;
      
      if (avgHours >= 10) {
        return 'Maintain your current bedtime routine as it\'s working well. Consider tracking room temperature and noise levels for optimal sleep environment.';
      } else if (avgHours >= 8) {
        return 'Try extending bedtime by 30 minutes earlier. Create a calming pre-sleep routine with dimmed lights and quiet activities.';
      } else {
        return 'Focus on establishing a consistent bedtime routine. Ensure a dark, quiet room and consider swaddling or white noise for better sleep.';
      }
    }
    
    return 'Log more sleep sessions to receive tailored sleep improvement recommendations.';
  }

  String _getFeedingRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start tracking feeding sessions to receive personalized nutrition recommendations.';
    
    double avgSessions = 0;
    int totalDays = 0;
    
    for (var data in chartData) {
      if (data['sessions'] != null) {
        avgSessions += data['sessions'].toDouble();
        totalDays++;
      }
    }
    
    if (totalDays > 0) {
      avgSessions /= totalDays;
      
      if (avgSessions >= 8) {
        return 'Excellent feeding routine! Continue monitoring baby\'s hunger cues and consider tracking feeding duration for optimization.';
      } else if (avgSessions >= 6) {
        return 'Consider offering additional feeding opportunities. Watch for early hunger cues like rooting or sucking motions.';
      } else {
        return 'Increase feeding frequency to 8-12 sessions per day for newborns. Consult your pediatrician for personalized feeding schedules.';
      }
    }
    
    return 'Track more feeding sessions to receive nutrition optimization recommendations.';
  }

  String _getGrowthRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start recording weight and height measurements to receive growth recommendations.';
    
    List<double> weights = [];
    for (var data in chartData) {
      if (data['weight'] != null) {
        weights.add(data['weight'].toDouble());
      }
    }
    
    if (weights.length >= 2) {
      double growth = weights.last - weights.first;
      
      if (growth > 0) {
        return 'Continue current nutrition and feeding patterns. Schedule regular pediatric checkups to monitor growth trajectory and developmental milestones.';
      } else if (growth == 0) {
        return 'Monitor feeding frequency and volume. Consider consulting your pediatrician about nutrition optimization and growth expectations.';
      } else {
        return 'Schedule immediate pediatric consultation. Monitor feeding closely and ensure adequate nutrition and hydration.';
      }
    }
    
    return 'Record regular weight measurements to track growth patterns and receive personalized development recommendations.';
  }

  String _getDiaperRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start tracking diaper changes to monitor hydration and digestive health.';
    
    double avgChanges = 0;
    int totalDays = 0;
    
    for (var data in chartData) {
      if (data['changes'] != null) {
        avgChanges += data['changes'].toDouble();
        totalDays++;
      }
    }
    
    if (totalDays > 0) {
      avgChanges /= totalDays;
      
      if (avgChanges >= 6) {
        return 'Great diaper change frequency! Continue monitoring for any changes in pattern or consistency that might indicate health changes.';
      } else if (avgChanges >= 4) {
        return 'Monitor feeding intake and watch for signs of dehydration. Ensure adequate fluid intake and consult pediatrician if concerned.';
      } else {
        return 'Increase feeding frequency and monitor for signs of dehydration or constipation. Consult your pediatrician for guidance.';
      }
    }
    
    return 'Track diaper changes consistently to monitor digestive health and receive hydration recommendations.';
  }

  String _getHealthRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start tracking health checkups and vital signs for wellness recommendations.';
    
    final recentHealthEvents = chartData.where((data) => _extractChartValue('health', data) > 0).length;
    
    if (recentHealthEvents >= 2) {
      return 'Maintain regular pediatric checkups and vaccination schedules. Continue monitoring growth and development milestones.';
    } else if (recentHealthEvents >= 1) {
      return 'Schedule regular health checkups every 3-6 months. Track any concerns and maintain vaccination records.';
    }
    
    return 'Schedule initial pediatric consultation and establish regular checkup routine for optimal health monitoring.';
  }

  String _getMedicineRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start tracking medication administration for adherence monitoring.';
    
    final medicationEvents = chartData.where((data) => _extractChartValue('medicine', data) > 0).length;
    
    if (medicationEvents >= 5) {
      return 'Excellent medication compliance! Continue current schedule and monitor for any side effects or concerns.';
    } else if (medicationEvents >= 2) {
      return 'Set medication reminders and maintain consistent timing. Consult pediatrician before making any changes.';
    }
    
    return 'Establish medication tracking routine and always follow pediatric guidance for dosing and timing.';
  }

  String _getDevelopmentRecommendations(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'Start documenting developmental milestones for growth tracking.';
    
    final developmentEvents = chartData.where((data) => _extractChartValue('development', data) > 0).length;
    
    if (developmentEvents >= 3) {
      return 'Continue providing varied developmental activities. Document new skills and provide age-appropriate challenges.';
    } else if (developmentEvents >= 1) {
      return 'Engage in regular tummy time, reading, and sensory play. Document milestone achievements for tracking.';
    }
    
    return 'Start with basic developmental activities like tummy time, reading, and sensory exploration for growth.';
  }

  String _getHealthInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No health data available for analysis.';
    
    // For health, we can analyze checkup frequency and health indicators
    final recentHealthEvents = chartData.where((data) => _extractChartValue('health', data) > 0).length;
    
    if (recentHealthEvents >= 2) {
      return 'Regular health monitoring shows positive trends. All vital signs and checkups indicate healthy development.';
    } else if (recentHealthEvents >= 1) {
      return 'Health monitoring is on track. Consider scheduling regular checkups to maintain optimal health tracking.';
    }
    
    return 'Limited health data available. Regular health checkups and monitoring help ensure optimal development.';
  }

  String _getMedicineInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No medication data available for analysis.';
    
    // For medicine, we can analyze adherence patterns
    final medicationEvents = chartData.where((data) => _extractChartValue('medicine', data) > 0).length;
    
    if (medicationEvents >= 5) {
      return 'Excellent medication adherence observed. Consistent tracking shows good compliance with prescribed schedules.';
    } else if (medicationEvents >= 2) {
      return 'Good medication tracking progress. Continue monitoring for optimal health outcomes.';
    }
    
    return 'Start tracking medication administration to monitor adherence and effectiveness.';
  }

  String _getDevelopmentInsights(List<dynamic> chartData) {
    if (chartData.isEmpty) return 'No development data available for analysis.';
    
    // For development, we can analyze milestone achievements and progress
    final developmentEvents = chartData.where((data) => _extractChartValue('development', data) > 0).length;
    
    if (developmentEvents >= 3) {
      return 'Strong developmental progress observed. Regular milestone tracking shows age-appropriate advancement.';
    } else if (developmentEvents >= 1) {
      return 'Development tracking is progressing well. Continue documenting milestones for better insights.';
    }
    
    return 'Start tracking developmental milestones to monitor cognitive and motor skill progress.';
  }

  Widget _buildSimpleChart(List<dynamic> chartData) {
    if (chartData.isEmpty) {
      return Center(
        child: Text(
          'No chart data available',
          style: TextStyle(
            fontSize: 12.sp,
            color: ThemeAwareColors.getSecondaryTextColor(context),
          ),
        ),
      );
    }

    // Simplified chart visualization without complex nesting
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getTypeColor(_getCurrentTabType()).withValues(alpha: 0.1),
            _getTypeColor(_getCurrentTabType()).withValues(alpha: 0.05),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: EdgeInsets.all(3.w),
        child: _buildSimpleBarChart(chartData),
      ),
    );
  }

  Widget _buildEmptyChartState(String type) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getTypeColor(type).withValues(alpha: 0.05),
            _getTypeColor(type).withValues(alpha: 0.02),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: _getTypeIcon(type),
              color: _getTypeColor(type).withValues(alpha: 0.5),
              size: 6.w,
            ),
            SizedBox(height: 1.h),
            Text(
              'No $type data yet',
              style: TextStyle(
                fontSize: 12.sp,
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
            Text(
              'Log activities to see trends',
              style: TextStyle(
                fontSize: 10.sp,
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKeyMetrics(String type, List<Map<String, dynamic>> chartData) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
                      Text(
                        'Key Metrics',
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                      ),
        SizedBox(height: 2.h),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                _getMetricTitle(type, 'primary'),
                _getMetricValueFromChart(type, chartData, 'primary'),
                _getTypeColor(type),
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: _buildMetricCard(
                _getMetricTitle(type, 'secondary'),
                _getMetricValueFromChart(type, chartData, 'secondary'),
                _getTypeColor(type),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, Color color) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            value,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w700,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            title,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
              fontSize: 10.sp,
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIInsightsAndRecommendations(String type, Map<String, dynamic> data, List<dynamic> recommendations) {
    // Get AI insights and recommendations text
    final insights = _getCompactInsights(type, data['chartData'] ?? []);
    final aiRecommendations = _getCompactRecommendations(type, data['chartData'] ?? []);
    
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getTypeColor(type).withValues(alpha: 0.1),
            _getTypeColor(type).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: _getTypeColor(type).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // AI Insights Section with Timestamp and Refresh Button
          Row(
            children: [
              CustomIconWidget(
                iconName: 'psychology',
                color: _getTypeColor(type),
                size: 4.w,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AI Insights',
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w600,
                        color: _getTypeColor(type),
                      ),
                    ),
                    if (_aiInsightsManager.mostRecentTimestamp != null)
                      Text(
                        'Last updated ${_aiInsightsManager.formattedLastUpdate}',
                        style: TextStyle(
                          fontSize: 9.sp,
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                          fontStyle: FontStyle.italic,
                        ),
                      ),
                  ],
                ),
              ),
              // Manual Refresh Button
              if (!_aiInsightsManager.isUpdating)
                InkWell(
                  onTap: _manualRefreshAI,
                  borderRadius: BorderRadius.circular(6),
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                    decoration: BoxDecoration(
                      color: _getTypeColor(type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: _getTypeColor(type).withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.refresh,
                          size: 3.w,
                          color: _getTypeColor(type),
                        ),
                        SizedBox(width: 1.w),
                        Text(
                          'Refresh',
                          style: TextStyle(
                            fontSize: 9.sp,
                            fontWeight: FontWeight.w600,
                            color: _getTypeColor(type),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 1.5.h),
          Text(
            insights,
            style: TextStyle(
              fontSize: 11.sp,
              color: Theme.of(context).colorScheme.onSurface,
              height: 1.4,
            ),
          ),
          
          SizedBox(height: 2.h),
          
          // AI Recommendations Section
          Row(
            children: [
              CustomIconWidget(
                iconName: 'lightbulb',
                color: _getTypeColor(type),
                size: 4.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'AI Recommendations',
                style: TextStyle(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: _getTypeColor(type),
                ),
              ),
            ],
          ),
          SizedBox(height: 1.5.h),
          Text(
            aiRecommendations,
            style: TextStyle(
              fontSize: 11.sp,
              color: Theme.of(context).colorScheme.onSurface,
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(String text) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 1.5.w,
            height: 1.5.w,
            margin: EdgeInsets.only(top: 1.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSuccessColor(context),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                fontSize: 11.sp,
                color: Theme.of(context).colorScheme.onSurface,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }


  Widget _buildMetricRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 11.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 11.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTipsContent() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTipCard(
            'Sleep Tips',
            'Establish a consistent bedtime routine to help your baby sleep better.',
            'bedtime',
            Colors.indigo,
            [
              'Create a calm environment',
              'Consistent bedtime schedule',
              'Monitor room temperature',
              'Use white noise if helpful',
            ],
          ),
          SizedBox(height: 3.h),
          _buildTipCard(
            'Feeding Tips',
            'Track feeding patterns to establish healthy eating habits.',
            'restaurant',
            Colors.orange,
            [
              'Feed on demand for newborns',
              'Watch for hunger cues',
              'Track feeding duration',
              'Ensure proper burping',
            ],
          ),
          SizedBox(height: 3.h),
          _buildTipCard(
            'Growth Tips',
            'Regular measurements help track healthy development.',
            'trending_up',
            ThemeAwareColors.getSuccessColor(context),
            [
              'Monthly weight checks',
              'Track height regularly',
              'Monitor head circumference',
              'Consult pediatrician regularly',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTipCard(String title, String description, String icon, Color color, List<String> tips) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: CustomIconWidget(
                  iconName: icon,
                  color: color,
                  size: 5.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 10.sp,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          ...tips.map((tip) => _buildTipItem(tip)),
        ],
      ),
    );
  }

  Widget _buildTipItem(String tip) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 1.5.w,
            height: 1.5.w,
            margin: EdgeInsets.only(top: 1.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getPrimaryColor(context),
              shape: BoxShape.circle,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Text(
              tip,
              style: TextStyle(
                fontSize: 11.sp,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return Colors.indigo;
      case 'feeding':
        return Colors.orange;
      case 'growth':
        return ThemeAwareColors.getSuccessColor(context);
      case 'diaper':
        return Colors.amber;
      case 'health':
        return Colors.red;
      case 'development':
        return Colors.purple;
      case 'mood':
        return Colors.teal;
      case 'playtime':
        return Colors.pink;
      case 'bathing':
        return Colors.blue;
      case 'medicine':
        return Colors.purple;
      case 'milestones':
        return Colors.blue;
      default:
        return ThemeAwareColors.getPrimaryColor(context);
    }
  }
  String _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return 'bedtime';
      case 'feeding':
        return 'restaurant';
      case 'growth':
        return 'trending_up';
      case 'diaper':
        return 'child_care';
      case 'health':
        return 'local_hospital';
      case 'development':
        return 'psychology';
      case 'mood':
        return 'mood';
      case 'playtime':
        return 'toys';
      case 'bathing':
        return 'bathtub';
      case 'medicine':
        return 'medication';
      case 'milestones':
        return 'emoji_events';
      default:
        return 'psychology';
    }
  }

  String _getMetricTitle(String type, String metric) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return metric == 'primary' ? 'Sleep Sessions' : 'Total Duration';
      case 'feeding':
        return metric == 'primary' ? 'Daily Feeds' : 'Avg Amount';
      case 'growth':
        return metric == 'primary' ? 'Weight Trend' : 'Height Trend';
      case 'diaper':
        return metric == 'primary' ? 'Daily Changes' : 'Wet/Dirty';
      case 'health':
        return metric == 'primary' ? 'Appointments' : 'Vaccinations';
      case 'development':
        return metric == 'primary' ? 'Milestones' : 'Progress Rate';
      case 'mood':
        return metric == 'primary' ? 'Mood Score' : 'Fussy Periods';
      case 'playtime':
        return metric == 'primary' ? 'Play Sessions' : 'Activity Level';
      case 'bathing':
        return metric == 'primary' ? 'Bath Frequency' : 'Duration';
      case 'medicine':
        return metric == 'primary' ? 'Medications' : 'Adherence Rate';
      case 'milestones':
        return metric == 'primary' ? 'Achieved' : 'Upcoming';
      default:
        return 'Metric';
    }
  }

  String _getMetricValueFromChart(String type, List<Map<String, dynamic>> chartData, String metric) {
    debugPrint('🔍 Calculating metric value for $type - $metric');
    debugPrint('📈 Chart data: $chartData');
    
    // If not in demo mode and no real data, return N/A
    if (!false && chartData.isEmpty) {
      debugPrint('❌ No chart data available and not in demo mode');
      return 'N/A';
    }
    
    return _calculateMetricFromChartData(type, chartData, metric);
  }
  
  String _calculateMetricFromChartData(String type, List<Map<String, dynamic>> chartData, String metric) {
    switch (type.toLowerCase()) {
      case 'sleep':
        if (metric == 'primary') {
          // Calculate total sleep logs in last 7 days
          if (chartData.isNotEmpty) {
            final totalLogs = chartData
                .map((item) => (item['logs'] ?? 0) as int)
                .fold(0, (sum, logs) => sum + logs);
            
            debugPrint('💤 Total sleep logs in 7 days: $totalLogs');
            return '$totalLogs';
          }
          
          if (false) {
            return '15'; // Demo data
          }
          
          return '0';
        } else {
          // Calculate total sleep hours and average duration
          if (chartData.isNotEmpty) {
            final totalHours = chartData
                .map((item) => (item['hours'] ?? 0.0) as double)
                .fold(0.0, (sum, hours) => sum + hours);
            
            final totalLogs = chartData
                .map((item) => (item['logs'] ?? 0) as int)
                .fold(0, (sum, logs) => sum + logs);
            
            if (totalLogs > 0) {
              final avgDuration = totalHours / totalLogs;
              final hours = totalHours.floor();
              final minutes = ((totalHours - hours) * 60).round();
              debugPrint('💤 Total: ${hours}h ${minutes}m, Avg: ${avgDuration.toStringAsFixed(1)}h');
              return '${hours}h ${minutes}m';
            }
          }
          
          if (false) {
            return '58h 30m'; // Demo data
          }
          
          return '0h 0m';
        }
        
      case 'feeding':
        if (metric == 'primary') {
          // Calculate average daily feeds
          if (chartData.isNotEmpty) {
            final validSessions = chartData
                .where((item) => item['sessions'] != null && item['sessions'] > 0)
                .map((item) => (item['sessions'] as num).toDouble())
                .toList();
            
            debugPrint('🍼 Valid feeding sessions: $validSessions');
            
            if (validSessions.isNotEmpty) {
              final avgSessions = validSessions.reduce((a, b) => a + b) / validSessions.length;
              debugPrint('🍼 Average feeding sessions: ${avgSessions.toStringAsFixed(1)}/day');
              return '${avgSessions.toStringAsFixed(1)}/day';
            }
          }
          
          if (false) {
            return '7/day'; // Demo data
          }
          
          debugPrint('🍼 No valid feeding data found');
          return 'N/A';
        } else {
          // Average feeding amount
          if (chartData.isNotEmpty) {
            final validVolumes = chartData
                .where((item) => item['volume'] != null && item['volume'] > 0)
                .map((item) => (item['volume'] as num).toDouble())
                .toList();
            
            if (validVolumes.isNotEmpty) {
              final avgVolume = validVolumes.reduce((a, b) => a + b) / validVolumes.length;
              return '${avgVolume.toStringAsFixed(0)}ml';
            }
          }
          
          if (false) {
            return '150ml'; // Demo data
          }
          
          return 'N/A';
        }
        
      case 'growth':
        if (metric == 'primary') {
          // Calculate growth trend
          if (chartData.length >= 2) {
            final firstWeight = (chartData.first['weight'] ?? 0).toDouble();
            final lastWeight = (chartData.last['weight'] ?? 0).toDouble();
            final trend = lastWeight - firstWeight;
            if (trend > 0.2) return 'Growing Well';
            if (trend > 0) return 'Steady Growth';
            return 'Monitor';
          }
          return false ? 'Steady' : 'N/A';
        }
        return false ? 'Normal' : 'N/A';
        
      case 'diaper':
        if (metric == 'primary') {
          // Calculate average daily changes
          if (chartData.isNotEmpty) {
            final totalChanges = chartData.fold(0.0, (sum, item) {
              final wet = (item['wet'] ?? 0).toDouble();
              final soiled = (item['soiled'] ?? 0).toDouble();
              return sum + wet + soiled;
            });
            final avgChanges = totalChanges / chartData.length;
            return '${avgChanges.toStringAsFixed(1)}/day';
          }
          return false ? '8/day' : 'N/A';
        }
        // Calculate wet/dry ratio
        if (chartData.isNotEmpty) {
          final totalWet = chartData.fold(0.0, (sum, item) => sum + (item['wet'] ?? 0).toDouble());
          final totalSoiled = chartData.fold(0.0, (sum, item) => sum + (item['soiled'] ?? 0).toDouble());
          final total = totalWet + totalSoiled;
          if (total > 0) {
            final wetPercent = (totalWet / total * 100).round();
            final soiledPercent = (totalSoiled / total * 100).round();
            return '$wetPercent/$soiledPercent';
          }
        }
        return false ? '70/30' : 'N/A';
        
      case 'health':
        if (metric == 'primary') return false ? '2' : 'N/A';
        return false ? 'Up-to-date' : 'N/A';
      case 'development':
        if (metric == 'primary') return false ? '5' : 'N/A';
        return false ? 'On Track' : 'N/A';
      case 'medicine':
        if (metric == 'primary') return false ? '2' : 'N/A';
        return false ? '95%' : 'N/A';
      case 'milestones':
        if (metric == 'primary') return false ? '5' : 'N/A';
        return false ? '3' : 'N/A';
      default:
        return 'N/A';
    }
  }
  
  String _getMetricValue(String type, Map<String, dynamic> data, String metric) {
    debugPrint('🔍 Calculating metric value for $type - $metric');
    debugPrint('📊 Data available: $data');
    
    // Calculate actual metrics from chart data if available
    final chartData = data['chartData'] as List<dynamic>? ?? [];
    debugPrint('📈 Chart data: $chartData');
    
    // If not in demo mode and no real data, return N/A
    if (!false && chartData.isEmpty) {
      debugPrint('❌ No chart data available and not in demo mode');
      return 'N/A';
    }
    
    switch (type.toLowerCase()) {
      case 'sleep':
        if (metric == 'primary') {
          // Calculate average sleep hours from chart data
          if (chartData.isNotEmpty) {
            final validHours = chartData
                .where((item) => item != null && item['hours'] != null && item['hours'] > 0)
                .map((item) => (item['hours'] as num).toDouble())
                .toList();
            
            debugPrint('💤 Valid sleep hours: $validHours');
            
            if (validHours.isNotEmpty) {
              final avgHours = validHours.reduce((a, b) => a + b) / validHours.length;
              debugPrint('💤 Average sleep hours: ${avgHours.toStringAsFixed(1)}h');
              return '${avgHours.toStringAsFixed(1)}h';
            }
          }
          
          // Check if we're in demo mode
          if (false) {
            return '8.2h'; // Demo data
          }
          
          debugPrint('💤 No valid sleep data found');
          return 'N/A';
        } else {
          // Sleep quality
          if (chartData.isNotEmpty) {
            final validQualities = chartData
                .where((item) => item != null && item['quality'] != null && item['quality'] > 0)
                .map((item) => (item['quality'] as num).toDouble())
                .toList();
            
            if (validQualities.isNotEmpty) {
              final avgQuality = validQualities.reduce((a, b) => a + b) / validQualities.length;
              if (avgQuality >= 4.0) return 'Excellent';
              if (avgQuality >= 3.0) return 'Good';
              if (avgQuality >= 2.0) return 'Fair';
              return 'Poor';
            }
          }
          
          if (false) {
            return 'Good'; // Demo data
          }
          
          return 'N/A';
        }
        
      case 'feeding':
        if (metric == 'primary') {
          // Calculate average daily feeds
          if (chartData.isNotEmpty) {
            final validSessions = chartData
                .where((item) => item != null && item['sessions'] != null && item['sessions'] > 0)
                .map((item) => (item['sessions'] as num).toDouble())
                .toList();
            
            debugPrint('🍼 Valid feeding sessions: $validSessions');
            
            if (validSessions.isNotEmpty) {
              final avgSessions = validSessions.reduce((a, b) => a + b) / validSessions.length;
              debugPrint('🍼 Average feeding sessions: ${avgSessions.toStringAsFixed(1)}/day');
              return '${avgSessions.toStringAsFixed(1)}/day';
            }
          }
          
          if (false) {
            return '7/day'; // Demo data
          }
          
          debugPrint('🍼 No valid feeding data found');
          return 'N/A';
        } else {
          // Average feeding amount
          if (chartData.isNotEmpty) {
            final validVolumes = chartData
                .where((item) => item != null && item['volume'] != null && item['volume'] > 0)
                .map((item) => (item['volume'] as num).toDouble())
                .toList();
            
            if (validVolumes.isNotEmpty) {
              final avgVolume = validVolumes.reduce((a, b) => a + b) / validVolumes.length;
              return '${avgVolume.toStringAsFixed(0)}ml';
            }
          }
          
          if (false) {
            return '150ml'; // Demo data
          }
          
          return 'N/A';
        }
        
      case 'growth':
        if (metric == 'primary') {
          // Calculate growth trend
          if (chartData.length >= 2) {
            final firstWeight = (chartData.first['weight'] ?? 0).toDouble();
            final lastWeight = (chartData.last['weight'] ?? 0).toDouble();
            final trend = lastWeight - firstWeight;
            if (trend > 0.2) return 'Growing Well';
            if (trend > 0) return 'Steady Growth';
            return 'Monitor';
          }
          return 'Steady';
        }
        return data['heightTrend']?.toString() ?? 'Normal';
        
      case 'diaper':
        if (metric == 'primary') {
          // Calculate average daily changes
          if (chartData.isNotEmpty) {
            final totalChanges = chartData.fold(0.0, (sum, item) {
              final wet = (item['wet'] ?? 0).toDouble();
              final soiled = (item['soiled'] ?? 0).toDouble();
              return sum + wet + soiled;
            });
            final avgChanges = totalChanges / chartData.length;
            return '${avgChanges.toStringAsFixed(1)}/day';
          }
          return '8/day';
        }
        // Calculate wet/dry ratio
        if (chartData.isNotEmpty) {
          final totalWet = chartData.fold(0.0, (sum, item) => sum + (item['wet'] ?? 0).toDouble());
          final totalSoiled = chartData.fold(0.0, (sum, item) => sum + (item['soiled'] ?? 0).toDouble());
          final total = totalWet + totalSoiled;
          if (total > 0) {
            final wetPercent = (totalWet / total * 100).round();
            final soiledPercent = (totalSoiled / total * 100).round();
            return '$wetPercent/$soiledPercent';
          }
        }
        return '70/30';
        
      case 'health':
        if (metric == 'primary') return data['appointmentCount']?.toString() ?? '2';
        return data['vaccinationStatus']?.toString() ?? 'Up-to-date';
      case 'development':
        if (metric == 'primary') return data['milestonesReached']?.toString() ?? '5';
        return data['progressRate']?.toString() ?? 'On Track';
      case 'medicine':
        if (metric == 'primary') return data['medicationCount']?.toString() ?? '2';
        return data['adherenceRate']?.toString() ?? '95%';
      case 'milestones':
        if (metric == 'primary') return data['achievedCount']?.toString() ?? '5';
        return data['upcomingCount']?.toString() ?? '3';
      default:
        return 'N/A';
    }
  }

  List<Map<String, dynamic>> _getAdditionalMetrics(String type, Map<String, dynamic> data) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return [
          {'label': 'Bedtime Consistency', 'value': data['consistency'] ?? 'Good'},
          {'label': 'Night Wakings', 'value': data['wakings'] ?? '2-3'},
          {'label': 'Longest Sleep', 'value': data['longestSleep'] ?? '6h'},
        ];
      case 'feeding':
        return [
          {'label': 'Feeding Window', 'value': data['window'] ?? '2-3h'},
          {'label': 'Bottle/Breast', 'value': data['method'] ?? 'Mixed'},
          {'label': 'Growth Rate', 'value': data['growthRate'] ?? 'Normal'},
        ];
      case 'growth':
        return [
          {'label': 'Percentile Range', 'value': data['percentile'] ?? '50-75th'},
          {'label': 'Growth Velocity', 'value': data['velocity'] ?? 'Normal'},
          {'label': 'Last Measurement', 'value': data['lastMeasured'] ?? '1 week ago'},
        ];
      case 'diaper':
        return [
          {'label': 'Avg Between Changes', 'value': data['avgInterval'] ?? '3.5h'},
          {'label': 'Night Changes', 'value': data['nightChanges'] ?? '2'},
          {'label': 'Most Common Type', 'value': data['commonType'] ?? 'Wet'},
        ];
      case 'health':
        return [
          {'label': 'Last Checkup', 'value': data['lastCheckup'] ?? '2 weeks ago'},
          {'label': 'Next Appointment', 'value': data['nextAppointment'] ?? '1 month'},
          {'label': 'Health Score', 'value': data['healthScore'] ?? 'Excellent'},
        ];
      case 'development':
        return [
          {'label': 'Motor Skills', 'value': data['motorSkills'] ?? 'Advanced'},
          {'label': 'Cognitive', 'value': data['cognitive'] ?? 'On Track'},
          {'label': 'Social Skills', 'value': data['social'] ?? 'Developing'},
        ];
      case 'mood':
        return [
          {'label': 'Happy Hours', 'value': data['happyHours'] ?? '16h/day'},
          {'label': 'Calm Periods', 'value': data['calmPeriods'] ?? '6h'},
          {'label': 'Most Active', 'value': data['activeTime'] ?? 'Morning'},
        ];
      case 'playtime':
        return [
          {'label': 'Favorite Activity', 'value': data['favoriteActivity'] ?? 'Tummy Time'},
          {'label': 'Attention Span', 'value': data['attentionSpan'] ?? '15 min'},
          {'label': 'Social Play', 'value': data['socialPlay'] ?? '3h/day'},
        ];
      case 'bathing':
        return [
          {'label': 'Water Temperature', 'value': data['temperature'] ?? '37°C'},
          {'label': 'Enjoys Baths', 'value': data['enjoyment'] ?? 'Yes'},
          {'label': 'Hair Washing', 'value': data['hairWashing'] ?? '2x/week'},
        ];
      case 'medicine':
        return [
          {'label': 'Active Medications', 'value': data['activeMedications'] ?? '2'},
          {'label': 'Last Dose', 'value': data['lastDose'] ?? '2 hours ago'},
          {'label': 'Side Effects', 'value': data['sideEffects'] ?? 'None'},
        ];
      case 'milestones':
        return [
          {'label': 'Development Score', 'value': data['developmentScore'] ?? '92%'},
          {'label': 'Next Milestone', 'value': data['nextMilestone'] ?? 'First Steps'},
          {'label': 'Last Achievement', 'value': data['lastAchievement'] ?? 'Sitting Up'},
        ];
      default:
        return [];
    }
  }

  String _getCurrentTabType() {
    final tabNames = ['sleep', 'feeding', 'growth', 'diaper', 'health', 'medicine', 'development', 'milestones'];
    if (_tabController.index < tabNames.length) {
      return tabNames[_tabController.index];
    }
    return 'sleep';
  }

  InsightType _getInsightType(String type) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return InsightType.sleep;
      case 'feeding':
        return InsightType.feeding;
      case 'growth':
        return InsightType.growth;
      default:
        return InsightType.sleep;
    }
  }

  Widget _buildChartSummary(List<dynamic> chartData) {
    if (chartData.isEmpty) return SizedBox.shrink();
    
    double average = 0;
    String unit = '';
    
    // Calculate statistics based on data type
    final sampleMap = chartData.first as Map<String, dynamic>;
    if (sampleMap.containsKey('hours')) {
      final total = chartData.fold(0.0, (sum, item) => sum + ((item as Map<String, dynamic>)['hours'] as num? ?? 0));
      average = total / chartData.length;
      unit = 'h';
    } else if (sampleMap.containsKey('feeds')) {
      final total = chartData.fold(0.0, (sum, item) => sum + ((item as Map<String, dynamic>)['feeds'] as num? ?? 0));
      average = total / chartData.length;
      unit = 'f';
    } else if (sampleMap.containsKey('weight')) {
      final weights = chartData.map((item) => (item as Map<String, dynamic>)['weight'] as num? ?? 0).toList();
      average = weights.last.toDouble();
      unit = 'kg';
    } else if (sampleMap.containsKey('wet')) {
      final total = chartData.fold(0.0, (sum, item) {
        final itemMap = item as Map<String, dynamic>;
        return sum + ((itemMap['wet'] as num? ?? 0) + (itemMap['soiled'] as num? ?? 0));
      });
      average = total / chartData.length;
      unit = 'ch';
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.8.h),
      decoration: BoxDecoration(
        color: _getTypeColor(_getCurrentTabType()).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            'Avg: ${average.toStringAsFixed(average % 1 == 0 ? 0 : 1)}$unit',
            style: TextStyle(
              fontSize: 9.sp,
              fontWeight: FontWeight.w600,
              color: _getTypeColor(_getCurrentTabType()),
            ),
          ),
          SizedBox(width: 4.w),
          Text(
            '${chartData.length} days',
            style: TextStyle(
              fontSize: 9.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSimpleBarChart(List<dynamic> chartData) {
    if (chartData.isEmpty) {
      return Center(
        child: Text(
          'No data points',
          style: TextStyle(
            fontSize: 10.sp,
            color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
          ),
        ),
      );
    }

    return Column(
      children: [
        // Chart title
        Padding(
          padding: EdgeInsets.only(bottom: 1.h),
          child: Row(
            children: [
              CustomIconWidget(
                iconName: 'trending_up',
                color: _getTypeColor(_getCurrentTabType()),
                size: 3.w,
              ),
              SizedBox(width: 1.w),
              Text(
                'Data Trend',
                style: TextStyle(
                  fontSize: 10.sp,
                  fontWeight: FontWeight.w600,
                  color: _getTypeColor(_getCurrentTabType()),
                ),
              ),
            ],
          ),
        ),
        // Simple bar visualization
        SizedBox(
          height: 6.h,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: chartData.take(7).map((item) {
              final itemMap = item as Map<String, dynamic>;
              double value = 0;
              String label = '';
              
              // Extract value based on data type
              if (itemMap.containsKey('hours')) {
                value = (itemMap['hours'] as num?)?.toDouble() ?? 0;
                label = itemMap['day']?.toString().substring(0, 1) ?? '';
              } else if (itemMap.containsKey('feeds')) {
                value = (itemMap['feeds'] as num?)?.toDouble() ?? 0;
                label = itemMap['day']?.toString().substring(0, 1) ?? '';
              } else if (itemMap.containsKey('weight')) {
                value = (itemMap['weight'] as num?)?.toDouble() ?? 0;
                label = 'M${chartData.indexOf(item) + 1}';
              } else if (itemMap.containsKey('wet')) {
                value = ((itemMap['wet'] as num?)?.toDouble() ?? 0) + ((itemMap['soiled'] as num?)?.toDouble() ?? 0);
                label = itemMap['day']?.toString().substring(0, 1) ?? '';
              }
              
              final maxValue = chartData.map((e) {
                final eMap = e as Map<String, dynamic>;
                if (eMap.containsKey('hours')) return (eMap['hours'] as num?)?.toDouble() ?? 0;
                if (eMap.containsKey('feeds')) return (eMap['feeds'] as num?)?.toDouble() ?? 0;
                if (eMap.containsKey('weight')) return (eMap['weight'] as num?)?.toDouble() ?? 0;
                if (eMap.containsKey('wet')) return ((eMap['wet'] as num?)?.toDouble() ?? 0) + ((eMap['soiled'] as num?)?.toDouble() ?? 0);
                return 0.0;
              }).fold(0.0, (a, b) => a > b ? a : b);
              
              final heightPercentage = maxValue > 0 ? (value / maxValue) : 0;
              final barHeight = (heightPercentage * 4.h).clamp(0.5.h, 4.h);
              
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 0.5.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        width: 3.w,
                        height: barHeight,
                        decoration: BoxDecoration(
                          color: _getTypeColor(_getCurrentTabType()),
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        label,
                        style: TextStyle(
                          fontSize: 7.sp,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
        // Summary stats
        SizedBox(height: 1.h),
        _buildChartSummary(chartData),
      ],
    );
  }

  Widget _buildMilestonesTab() {
    final milestonesData = _aiInsightsManager.insights['milestonesAnalysis'] as Map<String, dynamic>? ?? {};
    final data = milestonesData['data'] as Map<String, dynamic>? ?? {};
    final upcomingMilestones = data['upcomingMilestones'] as List<dynamic>? ?? [];
    final achievedMilestones = data['achievedMilestones'] as List<dynamic>? ?? [];
    
    // Convert the demo data to the format expected by MilestonePredictionsWidget
    final predictions = upcomingMilestones.map((milestone) {
      if (milestone is Map<String, dynamic>) {
        return {
          'name': milestone['milestone'] ?? 'Unknown Milestone',
          'eta': milestone['predictedAge'] ?? 'TBD',
          'confidence': milestone['confidence'] ?? 0.85,
          'description': 'AI-powered prediction based on your baby\'s development patterns',
          'icon': 'emoji_events',
          'color': 0xFF42A5F5,
          'tips': milestone['preparationTips'] ?? ['Continue current activities'],
        };
      }
      return {
        'name': milestone.toString(),
        'eta': 'TBD',
        'confidence': 0.8,
        'description': 'AI-powered prediction based on development patterns',
        'icon': 'emoji_events',
        'color': 0xFF42A5F5,
        'tips': ['Continue current activities'],
      };
    }).toList().cast<Map<String, dynamic>>();

    return SingleChildScrollView(
      child: Column(
        children: [
          // Achievement Overview Card
          Container(
            margin: EdgeInsets.all(4.w),
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
                  ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.05),
                ],
              ),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CustomIconWidget(
                      iconName: 'emoji_events',
                      color: ThemeAwareColors.getPrimaryColor(context),
                      size: 6.w,
                    ),
                    SizedBox(width: 3.w),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Milestone Progress',
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w600,
                              color: ThemeAwareColors.getPrimaryColor(context),
                            ),
                          ),
                          Text(
                            'Achieved: ${achievedMilestones.length} | Upcoming: ${upcomingMilestones.length}',
                            style: TextStyle(
                              fontSize: 11.sp,
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 2.h),
                // Progress indicators
                if (achievedMilestones.isNotEmpty)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Recent Achievements',
                        style: TextStyle(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w600,
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      SizedBox(height: 1.h),
                      ...achievedMilestones.take(3).map((milestone) {
                        final milestoneMap = milestone as Map<String, dynamic>;
                        return Container(
                          margin: EdgeInsets.only(bottom: 1.h),
                          padding: EdgeInsets.all(2.w),
                          decoration: BoxDecoration(
                            color: ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle,
                                color: ThemeAwareColors.getSuccessColor(context),
                                size: 4.w,
                              ),
                              SizedBox(width: 2.w),
                              Expanded(
                                child: Text(
                                  milestoneMap['milestone'] ?? 'Achievement',
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                              Text(
                                milestoneMap['achievedAt'] ?? '',
                                style: TextStyle(
                                  fontSize: 9.sp,
                                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ),
                        );
                      }),
                    ],
                  ),
              ],
            ),
          ),
          
          // Milestone Predictions Widget
          if (predictions.isNotEmpty)
            MilestonePredictionsWidget(predictions: predictions)
          else
            Container(
              margin: EdgeInsets.all(4.w),
              padding: EdgeInsets.all(6.w),
              decoration: BoxDecoration(
                color: ThemeAwareColors.getSurfaceColor(context),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                children: [
                  CustomIconWidget(
                    iconName: 'emoji_events',
                    color: ThemeAwareColors.getDividerColor(context),
                    size: 8.w,
                  ),
                  SizedBox(height: 2.h),
                  Text(
                    'No milestone predictions available',
                    style: TextStyle(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Log more activities to get AI-powered milestone predictions',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 11.sp,
                      color: Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
            ),
          
          SizedBox(height: 3.h), // Reduced bottom padding
        ],
      ),
    );
  }
  
  Map<String, dynamic> _getInsufficientDataInsights() {
    return {
      'sleepAnalysis': {
        'title': 'Sleep Tracking Started',
        'description': 'Log more sleep sessions to unlock AI-powered insights and patterns',
        'confidence': 0.1,
        'data': {
          'recommendations': ['Log at least 7 sleep sessions this week to see patterns'],
          'chartData': [],
        },
      },
      'feedingAnalysis': {
        'title': 'Feeding Tracking Started',
        'description': 'Log more feeding sessions to get nutrition insights',
        'confidence': 0.1,
        'data': {
          'recommendations': ['Track feeding times and amounts for better insights'],
          'chartData': [],
        },
      },
      'growthAnalysis': {
        'title': 'Growth Tracking Started',
        'description': 'Add weight and height measurements to track development',
        'confidence': 0.1,
        'data': {
          'recommendations': ['Record monthly measurements for growth tracking'],
          'chartData': [],
        },
      },
    };
  }
  
}

// Custom painter for chart line connections
class ChartLinePainter extends CustomPainter {
  final List<dynamic> dataPoints;
  final String type;
  final double maxValue;
  final Color color;
  final double chartHeight;
  
  ChartLinePainter({
    required this.dataPoints,
    required this.type,
    required this.maxValue,
    required this.color,
    required this.chartHeight,
  });
  
  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.length < 2) return;
    
    final paint = Paint()
      ..color = color.withValues(alpha: 0.8)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
      
    final shadowPaint = Paint()
      ..color = color.withValues(alpha: 0.3)
      ..strokeWidth = 4.0
      ..style = PaintingStyle.stroke
      ..maskFilter = ui.MaskFilter.blur(ui.BlurStyle.normal, 2.0);
    
    final path = Path();
    final shadowPath = Path();
    final points = <Offset>[];
    
    // Calculate points
    for (int i = 0; i < dataPoints.length; i++) {
      final data = dataPoints[i];
      final value = (data['value'] ?? 0.0).toDouble();
      
      final x = (i / (dataPoints.length - 1)) * size.width;
      final normalizedValue = maxValue > 0 ? value / maxValue : 0;
      final y = chartHeight - (normalizedValue * chartHeight);
      
      final point = Offset(x, y);
      points.add(point);
      
      if (i == 0) {
        path.moveTo(x, y);
        shadowPath.moveTo(x, y);
      } else {
        // Use curve for smoother line
        final prevPoint = points[i - 1];
        final cpX1 = prevPoint.dx + (point.dx - prevPoint.dx) * 0.5;
        final cpY1 = prevPoint.dy;
        final cpX2 = prevPoint.dx + (point.dx - prevPoint.dx) * 0.5;
        final cpY2 = point.dy;
        
        path.cubicTo(cpX1, cpY1, cpX2, cpY2, point.dx, point.dy);
        shadowPath.cubicTo(cpX1, cpY1, cpX2, cpY2, point.dx, point.dy);
      }
    }
    
    // Draw shadow first
    canvas.drawPath(shadowPath, shadowPaint);
    
    // Draw main line
    canvas.drawPath(path, paint);
    
    // Draw connection points
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
      
    final pointBorderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;
    
    for (final point in points) {
      canvas.drawCircle(point, 4.0, pointBorderPaint);
      canvas.drawCircle(point, 3.0, pointPaint);
    }
  }
  
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}

// Custom painter for growth chart line visualization
class GrowthLinePainter extends CustomPainter {
  final List<dynamic> chartData;
  final Color lineColor;
  final Color pointColor;
  
  GrowthLinePainter(this.chartData, {required this.lineColor, required this.pointColor});
  
  @override
  void paint(Canvas canvas, Size size) {
    if (chartData.isEmpty) return;
    
    final paint = Paint()
      ..color = lineColor
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;
    
    final pointPaint = Paint()
      ..color = pointColor
      ..style = PaintingStyle.fill;
    
    final path = Path();
    final points = <Offset>[];
    
    // Extract weight values
    final weights = chartData.map((data) {
      return (data['weight'] ?? 0).toDouble();
    }).toList();
    
    if (weights.isEmpty) return;
    
    final maxWeight = weights.reduce((a, b) => a > b ? a : b);
    final minWeight = weights.reduce((a, b) => a < b ? a : b);
    final weightRange = maxWeight - minWeight;
    
    // Generate points
    for (int i = 0; i < weights.length; i++) {
      final x = (i / (weights.length - 1)) * size.width;
      final normalizedWeight = weightRange > 0 ? (weights[i] - minWeight) / weightRange : 0.5;
      final y = size.height - (normalizedWeight * size.height * 0.8) - (size.height * 0.1);
      
      final point = Offset(x, y);
      points.add(point);
      
      if (i == 0) {
        path.moveTo(x, y);
      } else {
        path.lineTo(x, y);
      }
    }
    
    // Draw the line
    canvas.drawPath(path, paint);
    
    // Draw points
    for (final point in points) {
      canvas.drawCircle(point, 3.0, pointPaint);
    }
    
    // Draw weight labels
    final textPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
    );
    
    for (int i = 0; i < points.length; i++) {
      final point = points[i];
      final weight = weights[i];
      
      textPainter.text = TextSpan(
        text: '${weight.toStringAsFixed(1)}kg',
        style: TextStyle(
          color: pointColor,
          fontSize: 10,
          fontWeight: FontWeight.w600,
        ),
      );
      
      textPainter.layout();
      
      final textOffset = Offset(
        point.dx - textPainter.width / 2,
        point.dy - textPainter.height - 8,
      );
      
      // Draw background for text
      final textBg = Paint()..color = Colors.white.withValues(alpha: 0.8);
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            textOffset.dx - 2,
            textOffset.dy - 1,
            textPainter.width + 4,
            textPainter.height + 2,
          ),
          Radius.circular(4),
        ),
        textBg,
      );
      
      textPainter.paint(canvas, textOffset);
    }
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return true;
  }
}