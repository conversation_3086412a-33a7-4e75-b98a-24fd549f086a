import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MilestonePredictionWidget extends StatelessWidget {
  final List<Map<String, dynamic>> predictions;

  const MilestonePredictionWidget({
    super.key,
    required this.predictions,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'psychology',
                  color: ThemeAwareColors.getPrimaryColor(context),
                  size: 24,
                ),
                SizedBox(width: 2.w),
                Text(
                  'AI Milestone Predictions',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),

            Sized<PERSON>ox(height: 1.h),

            Text(
              predictions.isNotEmpty 
                  ? 'Based on your baby\'s development patterns'
                  : 'Log activities to get milestone predictions',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),

            SizedBox(height: 3.h),

            // Milestones list or empty state
            if (predictions.isEmpty)
              _buildEmptyState(context)
            else
              ...predictions.map((milestone) => _buildMilestoneCard(context, milestone)),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'child_care',
            color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.4),
            size: 48,
          ),
          SizedBox(height: 2.h),
          Text(
            'No Milestone Predictions Yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Log more activities and growth measurements to get AI-powered milestone predictions',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.4),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMilestoneCard(BuildContext context, Map<String, dynamic> milestone) {
    final color = _getMilestoneColor(context, milestone['category']?.toString() ?? 'general');
    final confidence = (milestone['confidence'] as num?)?.toInt() ?? 0;
    
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: milestone['icon']?.toString() ?? 'accessibility',
                  color: color,
                  size: 20,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      milestone['title']?.toString() ?? 'Milestone',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 2.w, vertical: 0.5.h),
                          decoration: BoxDecoration(
                            color: color.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'ETA: ${milestone['prediction']?.toString() ?? 'TBD'}',
                            style: Theme.of(context).textTheme.labelSmall
                                ?.copyWith(
                              color: color,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 2.w, vertical: 0.5.h),
                          decoration: BoxDecoration(
                            color: _getConfidenceColor(context, confidence)
                                .withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '$confidence% confidence',
                            style: Theme.of(context).textTheme.labelSmall
                                ?.copyWith(
                              color: _getConfidenceColor(context, confidence),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Description
          if (milestone['description']?.toString().isNotEmpty == true)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  milestone['description'].toString(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
                SizedBox(height: 2.h),
              ],
            ),

          // Tips
          if (milestone['tips'] != null && milestone['tips'] is List)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Preparation Tips',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: color,
                  ),
                ),
                SizedBox(height: 1.h),
                ...(milestone['tips'] as List).map((tip) => Padding(
                      padding: EdgeInsets.only(bottom: 0.5.h),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            margin: EdgeInsets.only(top: 0.5.h),
                            child: CustomIconWidget(
                              iconName: 'circle',
                              color: color.withValues(alpha: 0.6),
                              size: 6,
                            ),
                          ),
                          SizedBox(width: 2.w),
                          Expanded(
                            child: Text(
                              tip.toString(),
                              style: Theme.of(context).textTheme.bodySmall
                                  ?.copyWith(
                                color: ThemeAwareColors.getSecondaryTextColor(context),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
        ],
      ),
    );
  }

  Color _getMilestoneColor(BuildContext context, String category) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (category.toLowerCase()) {
      case 'motor':
        return isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069);
      case 'cognitive':
        return isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4);
      case 'social':
        return isDark ? const Color(0xFF9575CD) : const Color(0xFF6C63FF);
      case 'feeding':
        return isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261);
      case 'communication':
        return isDark ? const Color(0xFF4DB6AC) : const Color(0xFF2A9D8F);
      case 'physical':
        return isDark ? const Color(0xFF90A4AE) : const Color(0xFF264653);
      default:
        return ThemeAwareColors.getPrimaryColor(context);
    }
  }

  Color _getConfidenceColor(BuildContext context, int confidence) {
    if (confidence >= 90) {
      return ThemeAwareColors.getSuccessColor(context);
    } else if (confidence >= 70) {
      return ThemeAwareColors.getWarningColor(context);
    } else if (confidence >= 50) {
      return ThemeAwareColors.getWarningColor(context);
    } else {
      return ThemeAwareColors.getErrorColor(context);
    }
  }
}
