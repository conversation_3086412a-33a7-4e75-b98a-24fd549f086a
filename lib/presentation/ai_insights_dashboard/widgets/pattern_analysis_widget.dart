import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class PatternAnalysisWidget extends StatelessWidget {
  final Map<String, dynamic> analysisData;

  const PatternAnalysisWidget({
    super.key,
    required this.analysisData,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 180, // Fixed height to prevent overflow
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Important: Use minimum space needed
            children: [
              // Header Section
              _buildHeader(context),
              
              SizedBox(height: 12),
              
              // Chart Section
              Expanded( // Use remaining space for chart
                child: _buildChartSection(context),
              ),
              
              SizedBox(height: 12),
              
              // Key Insights Section
              _buildKeyInsightsSection(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: _getTypeColor(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: _getTypeIcon(),
            color: _getTypeColor(context),
            size: 20,
          ),
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                analysisData['title'] ?? 'Analysis',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 2),
              Text(
                analysisData['subtitle'] ?? 'No data available',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildChartSection(BuildContext context) {
    return Container(
      width: double.infinity,
      child: ClipRect( // Clip any overflow
        child: _buildChart(context),
      ),
    );
  }

  Widget _buildKeyInsightsSection(BuildContext context) {
    final insights = analysisData['insights'] as List<dynamic>? ?? [];
    
    return Container(
      height: 60, // Fixed height to prevent overflow
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Key Insights',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
          ),
          SizedBox(height: 4),
          Expanded(
            child: insights.isEmpty
                ? _buildEmptyInsights(context)
                : _buildInsightsList(context, insights),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyInsights(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: ThemeAwareColors.getDividerColor(context),
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: 'info',
            color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.4),
            size: 12,
          ),
          SizedBox(width: 6),
          Expanded(
            child: Text(
              'Log more activities to get AI-powered insights',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
                fontSize: 10,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsList(BuildContext context, List<dynamic> insights) {
    // Limit to 1 insight to prevent overflow
    final limitedInsights = insights.take(1).toList();
    
    return Container(
      padding: EdgeInsets.all(6),
      decoration: BoxDecoration(
        color: _getTypeColor(context).withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CustomIconWidget(
            iconName: 'lightbulb',
            color: _getTypeColor(context),
            size: 12,
          ),
          SizedBox(width: 6),
          Expanded(
            child: Text(
              limitedInsights.isNotEmpty ? limitedInsights.first.toString() : 'No insights available',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontSize: 10,
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildChart(BuildContext context) {
    final chartData = analysisData['chartData'] as List<dynamic>? ?? [];
    
    if (chartData.isEmpty) {
      return _buildEmptyState(context);
    }

    return Container(
      padding: EdgeInsets.all(8),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: _getHorizontalInterval(),
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: ThemeAwareColors.getDividerColor(context),
                strokeWidth: 0.5,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 20, // Reduced reserved size
                getTitlesWidget: (value, meta) {
                  return Container(
                    padding: EdgeInsets.only(top: 4),
                    child: Text(
                      _getBottomTitle(value, chartData),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 10,
                      ),
                    ),
                  );
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 25, // Reduced reserved size
                getTitlesWidget: (value, meta) {
                  return Container(
                    padding: EdgeInsets.only(right: 4),
                    child: Text(
                      _getLeftTitle(value),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 10,
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(
              color: ThemeAwareColors.getDividerColor(context),
              width: 1,
            ),
          ),
          backgroundColor: ThemeAwareColors.getCardColor(context),
          minX: 0,
          maxX: _getMaxX(chartData),
          minY: 0,
          maxY: _getMaxY(chartData),
          lineBarsData: [
            LineChartBarData(
              spots: _getChartSpots(chartData),
              isCurved: true,
              color: _getTypeColor(context),
              barWidth: 2,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) {
                  return FlDotCirclePainter(
                    radius: 3,
                    color: _getTypeColor(context),
                    strokeWidth: 1,
                    strokeColor: ThemeAwareColors.getCardColor(context),
                  );
                },
              ),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    _getTypeColor(context).withValues(alpha: 0.1),
                    _getTypeColor(context).withValues(alpha: 0.05),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getTypeColor(context).withValues(alpha: 0.05),
            _getTypeColor(context).withValues(alpha: 0.02),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getTypeColor(context).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getCardColor(context),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: _getTypeColor(context).withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: CustomIconWidget(
              iconName: _getTypeIcon(),
              color: _getTypeColor(context),
              size: 20,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Chart Preview',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: _getTypeColor(context),
              fontWeight: FontWeight.w600,
              fontSize: 11,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 4),
          Text(
            'Log activities to see trends',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
              fontSize: 9,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Color _getTypeColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final title = analysisData['title']?.toString().toLowerCase() ?? '';
    
    if (title.contains('sleep')) {
      return isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261);
    } else if (title.contains('feeding')) {
      return isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4);
    } else if (title.contains('growth')) {
      return isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069);
    } else if (title.contains('behavior')) {
      return isDark ? const Color(0xFF9575CD) : const Color(0xFF6C63FF);
    } else if (title.contains('health')) {
      return isDark ? const Color(0xFFFF8A65) : const Color(0xFFE76F51);
    } else if (title.contains('development')) {
      return isDark ? const Color(0xFF4DB6AC) : const Color(0xFF2A9D8F);
    }
    
    return isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4); // Fallback color
  }

  String _getTypeIcon() {
    final title = analysisData['title']?.toString().toLowerCase() ?? '';
    
    if (title.contains('sleep')) {
      return 'bedtime';
    } else if (title.contains('feeding')) {
      return 'restaurant';
    } else if (title.contains('growth')) {
      return 'trending_up';
    } else if (title.contains('behavior')) {
      return 'psychology';
    }
    
    return 'analytics';
  }

  List<FlSpot> _getChartSpots(List<dynamic> chartData) {
    if (chartData.isEmpty) return [];
    final spots = <FlSpot>[];
    for (int i = 0; i < chartData.length; i++) {
      final dataPoint = chartData[i] as Map<String, dynamic>;
      double? yValue;
      if (dataPoint.containsKey('hours')) {
        yValue = (dataPoint['hours'] as num?)?.toDouble();
      } else if (dataPoint.containsKey('feeds')) {
        yValue = (dataPoint['feeds'] as num?)?.toDouble();
      } else if (dataPoint.containsKey('value')) {
        yValue = (dataPoint['value'] as num?)?.toDouble();
      }
      spots.add(FlSpot(i.toDouble(), yValue ?? 0));
    }
    // If no valid spots, create default ones
    if (spots.isEmpty) {
      return [FlSpot(0, 0), FlSpot(1, 0)];
    }
    return spots;
  }

  String _getBottomTitle(double value, List<dynamic> chartData) {
    final index = value.toInt();
    if (index >= 0 && index < chartData.length) {
      final dataPoint = chartData[index] as Map<String, dynamic>;
      
      // Return label if available, truncate to avoid duplicates
      if (dataPoint.containsKey('label')) {
        final label = dataPoint['label'].toString();
        // For day labels, ensure we show only the first 3 characters to avoid duplicates
        if (label.length > 3) {
          return label.substring(0, 3);
        }
        return label;
      }
      
      // Return day if available
      if (dataPoint.containsKey('day')) {
        final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        final dayIndex = dataPoint['day'] as int;
        if (dayIndex >= 0 && dayIndex < days.length) {
          return days[dayIndex];
        }
      }
      
      // For date strings, extract day name
      if (dataPoint.containsKey('date')) {
        final dateStr = dataPoint['date'].toString();
        if (dateStr.length >= 3) {
          return dateStr.substring(0, 3);
        }
        return dateStr;
      }
    }
    
    // Fallback to sequential numbering to avoid duplicates
    final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    if (index >= 0 && index < days.length) {
      return days[index];
    }
    return (index + 1).toString();
  }

  String _getLeftTitle(double value) {
    final title = analysisData['title']?.toString().toLowerCase() ?? '';
    
    if (title.contains('sleep')) {
      return '${value.toInt()}h';
    } else if (title.contains('feeding')) {
      return '${value.toInt()}x';
    } else if (title.contains('growth')) {
      return '${value.toInt()}kg';
    }
    
    return value.toInt().toString();
  }

  double _getMaxX(List<dynamic> chartData) {
    if (chartData.isEmpty) return 6;
    return (chartData.length - 1).toDouble();
  }

  double _getMaxY(List<dynamic> chartData) {
    if (chartData.isEmpty) return 10;
    
    double maxValue = 0;
    for (final dataPoint in chartData) {
      final data = dataPoint as Map<String, dynamic>;
      
      double? value;
      if (data.containsKey('hours')) {
        value = (data['hours'] as num?)?.toDouble();
      } else if (data.containsKey('feeds')) {
        value = (data['feeds'] as num?)?.toDouble();
      } else if (data.containsKey('value')) {
        value = (data['value'] as num?)?.toDouble();
      }
      
      if (value != null && value > maxValue) {
        maxValue = value;
      }
    }
    
    // Add some padding to the top
    return maxValue > 0 ? maxValue * 1.2 : 10;
  }

  double _getHorizontalInterval() {
    final maxY = _getMaxY(analysisData['chartData'] as List<dynamic>? ?? []);
    
    if (maxY <= 5) return 1;
    if (maxY <= 10) return 2;
    if (maxY <= 20) return 5;
    return 10;
  }
}
