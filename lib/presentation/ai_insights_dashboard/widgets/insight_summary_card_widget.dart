import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class InsightSummaryCardWidget extends StatelessWidget {
  final String title;
  final String type;
  final String score;
  final String trend;
  final String subtitle;

  const InsightSummaryCardWidget({
    super.key,
    required this.title,
    required this.type,
    required this.score,
    required this.trend,
    required this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Container(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getTypeColor(context).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: _getTypeIcon(),
                    color: _getTypeColor(context),
                    size: 20,
                  ),
                ),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: 2),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: _getTypeColor(context).withValues(alpha: 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          type.toUpperCase(),
                          style: Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: _getTypeColor(context),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 12),
            
            // Score and Trend Row
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        'Score',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                      ),
                      Text(
                        score,
                        style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: _getTypeColor(context),
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getTrendColor(context).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomIconWidget(
                        iconName: _getTrendIcon(),
                        color: _getTrendColor(context),
                        size: 14,
                      ),
                      SizedBox(width: 4),
                      Text(
                        trend.toUpperCase(),
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                          color: _getTrendColor(context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 8),
            
            // Subtitle
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Color _getTypeColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (type.toLowerCase()) {
      case 'sleep':
        return isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261);
      case 'feeding':
        return isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4);
      case 'growth':
        return isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069);
      case 'behavior':
        return isDark ? const Color(0xFF9575CD) : const Color(0xFF6C63FF);
      case 'health':
        return isDark ? const Color(0xFFFF8A65) : const Color(0xFFE76F51);
      case 'development':
        return isDark ? const Color(0xFF4DB6AC) : const Color(0xFF2A9D8F);
      default:
        return isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4); // Fallback color
    }
  }

  String _getTypeIcon() {
    switch (type) {
      case 'sleep':
        return 'bedtime';
      case 'feeding':
        return 'restaurant';
      case 'growth':
        return 'trending_up';
      case 'behavior':
        return 'psychology';
      default:
        return 'lightbulb';
    }
  }

  Color _getTrendColor(BuildContext context) {
    switch (trend.toLowerCase()) {
      case 'improving':
        return ThemeAwareColors.getSuccessColor(context);
      case 'declining':
        return ThemeAwareColors.getErrorColor(context);
      case 'stable':
        return ThemeAwareColors.getPrimaryColor(context);
      default:
        return ThemeAwareColors.getSecondaryTextColor(context);
    }
  }

  String _getTrendIcon() {
    switch (trend) {
      case 'improving':
        return 'trending_up';
      case 'declining':
        return 'trending_down';
      case 'stable':
        return 'trending_flat';
      default:
        return 'timeline';
    }
  }

  Color _getConfidenceColor() {
    if (score.endsWith('%')) {
      int confidence = int.parse(score.replaceAll('%', ''));
      if (confidence >= 80) {
        return Colors.green;
      } else if (confidence >= 60) {
        return Colors.orange;
      } else {
        return Colors.red;
      }
    }
    return Colors.blue; // Fallback color
  }
}
