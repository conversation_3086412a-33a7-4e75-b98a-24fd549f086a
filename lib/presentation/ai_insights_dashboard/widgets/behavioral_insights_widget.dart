import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class BehavioralInsightsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> behaviorData;
  
  const BehavioralInsightsWidget({
    super.key, 
    this.behaviorData = const [],
  });

  @override
  Widget build(BuildContext context) {
    // Use real data instead of mock data - ✅ Fixed
    final hasRealData = behaviorData.isNotEmpty;
    
    return Container(
      padding: EdgeInsets.all(2.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(2.w),
                  decoration: BoxDecoration(
                    color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: 'psychology',
                    color: ThemeAwareColors.getPrimaryColor(context),
                    size: 24,
                  ),
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Behavior Pattern Analysis',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      SizedBox(height: 0.5.h),
                      Text(
                        'Analysis of activity patterns',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: 2.h),

          // ✅ Fixed: Show empty state if no real data, otherwise show real patterns
          if (!hasRealData)
            // Empty state
            Expanded(
              child: Container(
                padding: EdgeInsets.all(4.w),
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getSurfaceColor(context),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: ThemeAwareColors.getDividerColor(context).withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomIconWidget(
                      iconName: 'trending_up',
                      color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.4),
                      size: 48,
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      'No chart data available',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      'Log more activities to see patterns',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.4),
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            // Real behavioral patterns
            Expanded(
              child: ListView.builder(
                itemCount: behaviorData.length,
                itemBuilder: (context, index) {
                  final behavior = behaviorData[index];
                  return _buildBehaviorCard(context, behavior);
                },
              ),
            ),

          // Key Insights
          if (hasRealData) ...[
            SizedBox(height: 2.h),
            Text(
              'Key Insights',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            // Generate insights from real data
            ...behaviorData.take(3).map((behavior) => Padding(
              padding: EdgeInsets.symmetric(vertical: 0.5.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: EdgeInsets.only(top: 0.5.h),
                    child: CustomIconWidget(
                      iconName: 'circle',
                      color: ThemeAwareColors.getPrimaryColor(context),
                      size: 8,
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      'Developing predictable daily rhythm',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ],
      ),
    );
  }

  Widget _buildBehaviorCard(BuildContext context, Map<String, dynamic> behavior) {
    final color = behavior['color'] as Color? ?? Colors.purple;
    
    return Container(
      margin: EdgeInsets.only(bottom: 1.5.h), // ✅ Fixed: Reduced margin to prevent overflow
      padding: EdgeInsets.all(2.5.w), // ✅ Fixed: Reduced padding
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min, // ✅ Fixed: Prevent unnecessary expansion
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(1.5.w), // ✅ Fixed: Reduced padding
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: behavior['icon'] ?? 'psychology',
                  color: color,
                  size: 18, // ✅ Fixed: Reduced icon size
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min, // ✅ Fixed: Prevent expansion
                  children: [
                    // ✅ Fixed: Fixed the broken Row structure
                    Text(
                      behavior['pattern'] ?? 'Pattern Analysis',
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.3.h), // ✅ Fixed: Reduced spacing
                    Text(
                      'Analysis of recent activity patterns',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Color _getConfidenceColor(int confidence) {
    if (confidence >= 80) {
      return Colors.green;
    } else if (confidence >= 60) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
