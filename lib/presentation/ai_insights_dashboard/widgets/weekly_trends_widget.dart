import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class WeeklyTrendsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> weeklyData;
  final bool isLoading;
  
  const WeeklyTrendsWidget({
    super.key,
    this.weeklyData = const [],
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Weekly Trends',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                // Only show analyzing indicator when there's data
                if (weeklyData.isNotEmpty)
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                    decoration: BoxDecoration(
                      color: ThemeAwareColors.getSuccessColor(context).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        CustomIconWidget(
                          iconName: 'trending_up',
                          color: ThemeAwareColors.getSuccessColor(context),
                          size: 16,
                        ),
                        SizedBox(width: 1.w),
                        Text(
                          isLoading 
                            ? 'Loading...' 
                            : 'Not enough data',
                          style:
                              Theme.of(context).textTheme.labelSmall?.copyWith(
                            color: isLoading ? ThemeAwareColors.getSuccessColor(context) : ThemeAwareColors.getWarningColor(context),
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),

            SizedBox(height: 2.h),

            // Metrics cards
            weeklyData.isEmpty 
              ? _buildEmptyMetrics(context)
              : Row(
                  children: [
                    Expanded(
                      child: _buildMetricCard(
                        'Sleep Quality',
                        weeklyData.isNotEmpty ? '${_calculateSleepQuality()}/10' : 'N/A',
                        _getSleepTrend(),
                        Theme.of(context).brightness == Brightness.dark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261),
                        'bedtime',
                        context,
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: _buildMetricCard(
                        'Feeding Schedule',
                        weeklyData.isNotEmpty ? '${_calculateFeedingSchedule()}%' : 'N/A',
                        _getFeedingTrend(),
                        Theme.of(context).brightness == Brightness.dark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4),
                        'restaurant',
                        context,
                      ),
                    ),
                  ],
                ),

            SizedBox(height: 2.h),

            // Chart
            SizedBox(
              height: 20.h,
              child: LineChart(
                LineChartData(
                  gridData: FlGridData(
                    show: true,
                    drawVerticalLine: false,
                    horizontalInterval: 2,
                    getDrawingHorizontalLine: (value) {
                      return FlLine(
                        color: ThemeAwareColors.getDividerColor(context),
                        strokeWidth: 0.5,
                      );
                    },
                  ),
                  titlesData: FlTitlesData(
                    show: true,
                    rightTitles:
                        AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    topTitles:
                        AxisTitles(sideTitles: SideTitles(showTitles: false)),
                    bottomTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          const days = [
                            'Mon',
                            'Tue',
                            'Wed',
                            'Thu',
                            'Fri',
                            'Sat',
                            'Sun'
                          ];
                          if (value.toInt() >= 0 &&
                              value.toInt() < days.length) {
                            return Padding(
                              padding: EdgeInsets.only(top: 1.h),
                              child: Text(
                                days[value.toInt()],
                                style: Theme.of(context).textTheme.labelSmall,
                              ),
                            );
                          }
                          return Text('');
                        },
                      ),
                    ),
                    leftTitles: AxisTitles(
                      sideTitles: SideTitles(
                        showTitles: true,
                        reservedSize: 30,
                        getTitlesWidget: (value, meta) {
                          return Text(
                            value.toInt().toString(),
                            style: Theme.of(context).textTheme.labelSmall,
                          );
                        },
                      ),
                    ),
                  ),
                  borderData: FlBorderData(
                    show: true,
                    border: Border.all(
                      color: ThemeAwareColors.getDividerColor(context),
                      width: 1,
                    ),
                  ),
                  backgroundColor: ThemeAwareColors.getCardColor(context),
                  minX: 0,
                  maxX: 6,
                  minY: 0,
                  maxY: 10,
                  lineBarsData: weeklyData.isEmpty ? [] : [
                    // Sleep quality line
                    LineChartBarData(
                      spots: _generateChartData(),
                      isCurved: true,
                      gradient: LinearGradient(
                        colors: [
                          (Theme.of(context).brightness == Brightness.dark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261)).withValues(alpha: 0.8),
                          Theme.of(context).brightness == Brightness.dark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261)
                        ],
                      ),
                      barWidth: 3,
                      isStrokeCapRound: true,
                      dotData: FlDotData(
                        show: true,
                        getDotPainter: (spot, percent, barData, index) {
                          return FlDotCirclePainter(
                            radius: 3,
                            color: Theme.of(context).brightness == Brightness.dark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261),
                            strokeWidth: 2,
                            strokeColor: ThemeAwareColors.getCardColor(context),
                          );
                        },
                      ),
                      belowBarData: BarAreaData(
                        show: true,
                        gradient: LinearGradient(
                          colors: [
                            (Theme.of(context).brightness == Brightness.dark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261)).withValues(alpha: 0.1),
                            (Theme.of(context).brightness == Brightness.dark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261)).withValues(alpha: 0.05),
                          ],
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricCard(String title, String value, String subtitle,
      Color color, String iconName, BuildContext context) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: iconName,
                color: color,
                size: 20,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  title,
                  style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: color,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            subtitle,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyMetrics(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context).withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Center(
        child: Column(
          children: [
            CustomIconWidget(
              iconName: 'insights',
              color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3),
              size: 48,
            ),
            SizedBox(height: 1.h),
            Text(
              'No data available yet',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  double _calculateSleepQuality() {
    if (weeklyData.isEmpty) return 0.0;
    // Calculate average sleep quality from recent data
    // This would be implemented based on actual sleep data analysis
    return 0.0; // Placeholder - would calculate from real data
  }

  int _calculateFeedingSchedule() {
    if (weeklyData.isEmpty) return 0;
    // Calculate feeding schedule adherence percentage
    // This would be implemented based on actual feeding data analysis  
    return 0; // Placeholder - would calculate from real data
  }

  String _getSleepTrend() {
    return 'No data available';
  }

  String _getFeedingTrend() {
    return 'Not enough data';
  }

  List<FlSpot> _generateChartData() {
    if (weeklyData.isEmpty) return [];
    // Use 'hours' field for sleep demo data
    final spots = <FlSpot>[];
    for (int i = 0; i < weeklyData.length; i++) {
      final dataPoint = weeklyData[i];
      double? yValue;
      if (dataPoint.containsKey('hours')) {
        yValue = (dataPoint['hours'] as num?)?.toDouble();
      } else if (dataPoint.containsKey('feeds')) {
        yValue = (dataPoint['feeds'] as num?)?.toDouble();
      } else if (dataPoint.containsKey('value')) {
        yValue = (dataPoint['value'] as num?)?.toDouble();
      }
      spots.add(FlSpot(i.toDouble(), yValue ?? 0));
    }
    return spots;
  }
}
