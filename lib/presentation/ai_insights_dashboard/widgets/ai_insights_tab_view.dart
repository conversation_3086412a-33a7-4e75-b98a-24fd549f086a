import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';
import '../controllers/ai_insights_dashboard_controller.dart';

/// Optimized tab view widget that only rebuilds when necessary
class AIInsightsTabView extends StatelessWidget {
  final TabController tabController;
  
  const AIInsightsTabView({
    super.key,
    required this.tabController,
  });

  @override
  Widget build(BuildContext context) {
    return Consumer<AIInsightsDashboardController>(
      // Only rebuild when insights data changes
      builder: (context, controller, child) {
        if (controller.isLoading) {
          return _buildLoadingView();
        }
        
        if (controller.errorMessage != null) {
          return _buildErrorView(context, controller.errorMessage!);
        }
        
        return TabBarView(
          controller: tabController,
          children: [
            _buildSleepTab(context, controller.aiGeneratedInsights),
            _buildFeedingTab(context, controller.aiGeneratedInsights),
            _buildDiaperTab(context, controller.aiGeneratedInsights),
            _buildGrowthTab(context, controller.aiGeneratedInsights),
            _buildHealthTab(context, controller.aiGeneratedInsights),
            _buildMedicineTab(context, controller.aiGeneratedInsights),
            _buildDevelopmentTab(context, controller.aiGeneratedInsights),
            _buildOverallTab(context, controller.aiGeneratedInsights),
          ],
        );
      },
    );
  }

  Widget _buildLoadingView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 2.h),
          Text(
            'Generating AI insights...',
            style: TextStyle(
              fontSize: 14.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 12.w,
            color: ThemeAwareColors.getErrorColor(context),
          ),
          SizedBox(height: 2.h),
          Text(
            'Error loading insights',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 1.h),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.w),
            child: Text(
              error,
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ),
          SizedBox(height: 3.h),
          ElevatedButton(
            onPressed: () {
              context.read<AIInsightsDashboardController>().loadInsights();
            },
            child: Text('Retry'),
          ),
        ],
      ),
    );
  }

  // Individual tab builders - these would be extracted to separate widgets
  // for better performance and maintainability
  Widget _buildSleepTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Sleep Analysis',
      insights: insights['sleep_insights'] ?? 'No sleep insights available',
      recommendations: insights['sleep_recommendations'] ?? 'Continue tracking sleep patterns',
      icon: Icons.bedtime,
    );
  }

  Widget _buildFeedingTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Feeding Analysis',
      insights: insights['feeding_insights'] ?? 'No feeding insights available',
      recommendations: insights['feeding_recommendations'] ?? 'Continue tracking feeding sessions',
      icon: Icons.restaurant,
    );
  }

  Widget _buildDiaperTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Diaper Analysis',
      insights: insights['diaper_insights'] ?? 'No diaper insights available',
      recommendations: insights['diaper_recommendations'] ?? 'Continue monitoring diaper changes',
      icon: Icons.child_care,
    );
  }

  Widget _buildGrowthTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Growth Analysis',
      insights: insights['growth_insights'] ?? 'No growth insights available',
      recommendations: insights['growth_recommendations'] ?? 'Continue tracking growth measurements',
      icon: Icons.trending_up,
    );
  }

  Widget _buildHealthTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Health Analysis',
      insights: insights['health_insights'] ?? 'No health insights available',
      recommendations: insights['health_recommendations'] ?? 'Continue monitoring health indicators',
      icon: Icons.favorite,
    );
  }

  Widget _buildMedicineTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Medicine Analysis',
      insights: insights['medicine_insights'] ?? 'No medicine insights available',
      recommendations: insights['medicine_recommendations'] ?? 'Continue tracking medication',
      icon: Icons.medication,
    );
  }

  Widget _buildDevelopmentTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Development Analysis',
      insights: insights['development_insights'] ?? 'No development insights available',
      recommendations: insights['development_recommendations'] ?? 'Continue tracking milestones',
      icon: Icons.psychology,
    );
  }

  Widget _buildOverallTab(BuildContext context, Map<String, String> insights) {
    return _buildInsightTab(context,
      title: 'Overall Summary',
      insights: insights['overall_insights'] ?? 'No overall insights available',
      recommendations: insights['overall_recommendations'] ?? 'Continue consistent care routines',
      icon: Icons.analytics,
    );
  }

  Widget _buildInsightTab(BuildContext context, {
    required String title,
    required String insights,
    required String recommendations,
    required IconData icon,
  }) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                icon,
                size: 6.w,
                color: ThemeAwareColors.getPrimaryColor(context),
              ),
              SizedBox(width: 3.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 3.h),
          
          // Insights Section
          _buildSection(context,
            title: 'Insights',
            content: insights,
            icon: Icons.lightbulb_outline,
          ),
          
          SizedBox(height: 3.h),
          
          // Recommendations Section
          _buildSection(context,
            title: 'Recommendations',
            content: recommendations,
            icon: Icons.recommend,
          ),
        ],
      ),
    );
  }

  Widget _buildSection(BuildContext context, {
    required String title,
    required String content,
    required IconData icon,
  }) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                size: 5.w,
                color: ThemeAwareColors.getSecondaryColor(context),
              ),
              SizedBox(width: 2.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Text(
            content,
            style: TextStyle(
              fontSize: 14.sp,
              height: 1.5,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
        ],
      ),
    );
  }
}