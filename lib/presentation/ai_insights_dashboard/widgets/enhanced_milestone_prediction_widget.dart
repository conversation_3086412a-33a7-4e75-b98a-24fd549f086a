import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../models/baby_profile.dart';
import '../../../services/ai_insights_service.dart';
import '../../../theme/app_theme.dart';
import '../../../theme/theme_aware_colors.dart';
import '../../../widgets/custom_icon_widget.dart';

class EnhancedMilestonePredictionWidget extends StatefulWidget {
  final BabyProfile babyProfile;
  final String babyId;

  const EnhancedMilestonePredictionWidget({
    super.key,
    required this.babyProfile,
    required this.babyId,
  });

  @override
  State<EnhancedMilestonePredictionWidget> createState() => _EnhancedMilestonePredictionWidgetState();
}

class _EnhancedMilestonePredictionWidgetState extends State<EnhancedMilestonePredictionWidget> {
  final AIInsightsService _aiInsightsService = AIInsightsService();
  Map<String, dynamic>? _predictions;
  bool _isLoading = false;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadMilestonePredictions();
  }

  Future<void> _loadMilestonePredictions() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final predictions = await _aiInsightsService.generateMilestonePredictions(
        babyProfile: widget.babyProfile,
        babyId: widget.babyId,
      );

      if (mounted) {
        setState(() {
          _predictions = predictions;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            const Color(0xFFFFD700).withValues(alpha: 0.1),
            const Color(0xFFFFA726).withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFFFFD700).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(),
            SizedBox(height: 2.h),
            if (_isLoading)
              _buildLoadingState()
            else if (_error != null)
              _buildErrorState()
            else if (_predictions != null)
              _buildPredictionsContent()
            else
              _buildEmptyState(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: const Color(0xFFFFD700).withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: CustomIconWidget(
            iconName: 'emoji_events',
            color: const Color(0xFFFFD700),
            size: 6.w,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Milestone Predictions',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: const Color(0xFFFFD700),
                ),
              ),
              Text(
                'AI-powered developmental forecasts',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: _loadMilestonePredictions,
          icon: CustomIconWidget(
            iconName: 'refresh',
            color: const Color(0xFFFFD700),
            size: 5.w,
          ),
        ),
      ],
    );
  }

  Widget _buildLoadingState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 4.h),
        child: Column(
          children: [
            const CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Color(0xFFFFD700)),
            ),
            SizedBox(height: 2.h),
            Text(
              'Analyzing developmental patterns...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorState() {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: 'error',
            color: Colors.red,
            size: 5.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Text(
              'Unable to generate predictions. Tap refresh to try again.',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.red,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(4.w),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'timeline',
            color: ThemeAwareColors.getSecondaryTextColor(context),
            size: 12.w,
          ),
          SizedBox(height: 2.h),
          Text(
            'Start logging activities to get personalized milestone predictions',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionsContent() {
    final predictions = _predictions!['predictions'] as Map<String, dynamic>?;
    if (predictions == null) return _buildEmptyState();

    final nextMilestones = predictions['next_milestones'] as List<dynamic>? ?? [];
    final developmentSummary = predictions['development_summary'] as String? ?? '';
    final recommendations = predictions['recommendations'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (developmentSummary.isNotEmpty) ...[
          _buildDevelopmentSummary(developmentSummary),
          SizedBox(height: 3.h),
        ],
        if (nextMilestones.isNotEmpty) ...[
          _buildMilestonesSection(nextMilestones),
          SizedBox(height: 3.h),
        ],
        if (recommendations.isNotEmpty) ...[
          _buildRecommendationsSection(recommendations),
        ],
      ],
    );
  }

  Widget _buildDevelopmentSummary(String summary) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: ThemeAwareColors.getSecondaryTextColor(context).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CustomIconWidget(
                iconName: 'psychology',
                color: const Color(0xFF2196F3),
                size: 5.w,
              ),
              SizedBox(width: 2.w),
              Text(
                'Development Summary',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          SizedBox(height: 1.h),
          Text(
            summary,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
              height: 1.4,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMilestonesSection(List<dynamic> milestones) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Upcoming Milestones',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        ...milestones.take(3).map((milestone) => _buildMilestoneCard(milestone)).toList(),
      ],
    );
  }

  Widget _buildMilestoneCard(dynamic milestone) {
    final milestoneMap = milestone as Map<String, dynamic>;
    final title = milestoneMap['title'] as String? ?? 'Unknown Milestone';
    final description = milestoneMap['description'] as String? ?? '';
    final category = milestoneMap['category'] as String? ?? 'motor';
    final timeframe = milestoneMap['predicted_timeframe'] as String? ?? 'Soon';
    final confidence = milestoneMap['confidence'] as String? ?? 'medium';
    final activitiesToEncourage = milestoneMap['activities_to_encourage'] as List<dynamic>? ?? [];

    final categoryColor = _getCategoryColor(category);
    final categoryIcon = _getCategoryIcon(category);
    final confidenceColor = _getConfidenceColor(confidence);

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: categoryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: categoryColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(1.5.w),
                decoration: BoxDecoration(
                  color: categoryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: categoryIcon,
                  color: categoryColor,
                  size: 4.w,
                ),
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: categoryColor,
                      ),
                    ),
                    Row(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                          decoration: BoxDecoration(
                            color: confidenceColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            timeframe,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: confidenceColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                        SizedBox(width: 2.w),
                        Container(
                          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                          decoration: BoxDecoration(
                            color: confidenceColor.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '$confidence confidence',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: confidenceColor,
                              fontSize: 10.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (description.isNotEmpty) ...[
            SizedBox(height: 2.h),
            Text(
              description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
                height: 1.3,
              ),
            ),
          ],
          if (activitiesToEncourage.isNotEmpty) ...[
            SizedBox(height: 2.h),
            Text(
              'Activities to encourage:',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 0.5.h),
            ...activitiesToEncourage.take(2).map((activity) => Padding(
              padding: EdgeInsets.only(left: 3.w, bottom: 0.5.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '• ',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: categoryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      activity.toString(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ],
        ],
      ),
    );
  }

  Widget _buildRecommendationsSection(List<dynamic> recommendations) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recommendations',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        Container(
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: const Color(0xFF10B981).withValues(alpha: 0.05),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF10B981).withValues(alpha: 0.3),
            ),
          ),
          child: Column(
            children: recommendations.take(3).map((rec) => Padding(
              padding: EdgeInsets.only(bottom: 1.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomIconWidget(
                    iconName: 'check_circle',
                    color: const Color(0xFF10B981),
                    size: 4.w,
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Text(
                      rec.toString(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        height: 1.3,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ),
        ),
      ],
    );
  }

  Color _getCategoryColor(String category) {
    switch (category.toLowerCase()) {
      case 'motor':
        return const Color(0xFF4CAF50);
      case 'cognitive':
        return const Color(0xFF2196F3);
      case 'language':
        return const Color(0xFF9C27B0);
      case 'social':
        return const Color(0xFFFF9800);
      case 'emotional':
        return const Color(0xFFE91E63);
      case 'sensory':
        return const Color(0xFF00BCD4);
      case 'adaptive':
        return const Color(0xFF795548);
      case 'feeding':
        return const Color(0xFF6366F1);
      case 'sleep':
        return const Color(0xFFF59E0B);
      case 'play':
        return const Color(0xFF10B981);
      case 'health':
        return const Color(0xFFEF4444);
      default:
        return const Color(0xFF6B7280);
    }
  }

  String _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'motor':
        return 'directions_run';
      case 'cognitive':
        return 'psychology';
      case 'language':
        return 'record_voice_over';
      case 'social':
        return 'people';
      case 'emotional':
        return 'favorite';
      case 'sensory':
        return 'visibility';
      case 'adaptive':
        return 'settings';
      case 'feeding':
        return 'restaurant';
      case 'sleep':
        return 'bedtime';
      case 'play':
        return 'toys';
      case 'health':
        return 'health_and_safety';
      default:
        return 'emoji_events';
    }
  }

  Color _getConfidenceColor(String confidence) {
    switch (confidence.toLowerCase()) {
      case 'high':
        return const Color(0xFF10B981);
      case 'medium':
        return const Color(0xFFF59E0B);
      case 'low':
        return const Color(0xFFEF4444);
      default:
        return const Color(0xFF6B7280);
    }
  }
}
