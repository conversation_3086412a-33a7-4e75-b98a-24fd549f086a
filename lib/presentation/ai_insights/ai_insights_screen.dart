import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../core/app_export.dart';
import '../../models/feature_access.dart';
import '../subscription/controllers/feature_access_controller.dart';
import '../subscription/widgets/feature_gate.dart';
import './widgets/analysis_categories_widget.dart';
import './widgets/insight_card_widget.dart';
import './widgets/insights_filter_widget.dart';
import './widgets/chart_widget.dart';

class AIInsightsScreen extends StatefulWidget {
  final BabyProfile babyProfile;
  final List<ActivityLog> activities;

  const AIInsightsScreen({
    super.key,
    required this.babyProfile,
    required this.activities,
  });

  @override
  State<AIInsightsScreen> createState() => _AIInsightsScreenState();
}

class _AIInsightsScreenState extends State<AIInsightsScreen> {
  final AIAnalysisService _analysisService = AIAnalysisService();

  List<AIInsight> _insights = [];
  bool _isLoading = false;
  InsightType? _selectedFilter;
  bool _showArchived = false;

  @override
  void initState() {
    super.initState();
    _loadInsights();
  }

  Future<void> _loadInsights() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Load real data
      final insights = await _analysisService.analyzeActivityPatterns(
        babyProfile: widget.babyProfile,
        activities: widget.activities,
      );
      _insights = insights;
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to load insights: $e',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: ThemeAwareColors.getErrorColor(context),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateSpecificAnalysis(InsightType type) async {
    setState(() {
      _isLoading = true;
    });

    try {
      AIInsight? newInsight;

      switch (type) {
        case InsightType.sleep:
          final sleepLogs = widget.activities
              .where((log) => log.type == ActivityType.sleep)
              .toList();
          newInsight = await _analysisService.analyzeSleepPatterns(
            babyProfile: widget.babyProfile,
            sleepLogs: sleepLogs,
          );
          break;
        case InsightType.feeding:
          final feedingLogs = widget.activities
              .where((log) => log.type == ActivityType.feeding)
              .toList();
          newInsight = await _analysisService.analyzeFeedingPatterns(
            babyProfile: widget.babyProfile,
            feedingLogs: feedingLogs,
          );
          break;
        case InsightType.growth:
          final growthLogs = widget.activities
              .where((log) => log.type == ActivityType.growth)
              .toList();
          newInsight = await _analysisService.analyzeGrowthTrends(
            babyProfile: widget.babyProfile,
            growthLogs: growthLogs,
          );
          break;
        default:
          break;
      }

      if (newInsight != null) {
        setState(() {
          _insights.insert(0, newInsight!);
        });
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Failed to generate analysis: $e',
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: ThemeAwareColors.getErrorColor(context),
        ),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  List<AIInsight> get _filteredInsights {
    List<AIInsight> filtered = _insights;

    if (!_showArchived) {
      filtered = filtered.where((insight) => !insight.isArchived).toList();
    }

    if (_selectedFilter != null) {
      filtered =
          filtered.where((insight) => insight.type == _selectedFilter).toList();
    }

    // Sort by priority and date
    filtered.sort((a, b) {
      final priorityOrder = {
        InsightPriority.high: 0,
        InsightPriority.medium: 1,
        InsightPriority.low: 2,
      };

      final aPriority = priorityOrder[a.priority] ?? 2;
      final bPriority = priorityOrder[b.priority] ?? 2;

      if (aPriority != bPriority) {
        return aPriority.compareTo(bPriority);
      }

      return b.generatedAt.compareTo(a.generatedAt);
    });

    return filtered;
  }

  void _toggleInsightRead(AIInsight insight) {
    setState(() {
      final index = _insights.indexWhere((i) => i.id == insight.id);
      if (index != -1) {
        _insights[index] = insight.copyWith(isRead: !insight.isRead);
      }
    });
  }

  void _archiveInsight(AIInsight insight) {
    setState(() {
      final index = _insights.indexWhere((i) => i.id == insight.id);
      if (index != -1) {
        _insights[index] = insight.copyWith(isArchived: true);
      }
    });
  }




  @override
  Widget build(BuildContext context) {
    return FeatureGate(
      feature: AppFeature.aiInsights,
      showUpgradePrompt: true,
      child: _buildAIInsightsContent(context),
      fallback: _buildRestrictedContent(context),
    );
  }

  Widget _buildAIInsightsContent(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            Text(
              'Analysis for ${widget.babyProfile.name}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: CustomIconWidget(
            iconName: 'arrow_back',
            size: 6.w,
          ),
          onPressed: () {
            if (mounted && Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        ),
        actions: [
          IconButton(
            icon: CustomIconWidget(
              iconName: 'refresh',
              size: 6.w,
            ),
            onPressed: _isLoading ? null : _loadInsights,
          ),
          IconButton(
            icon: CustomIconWidget(
              iconName: 'filter_list',
              size: 6.w,
            ),
            onPressed: () => _showFilterDialog(),
          ),
        ],
      ),
      body: _isLoading && _insights.isEmpty
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadInsights,
              child: CustomScrollView(
                slivers: [
                  // Analysis Categories
                  SliverToBoxAdapter(
                    child: AnalysisCategoriesWidget(
                      onCategorySelected: _generateSpecificAnalysis,
                      isLoading: _isLoading,
                    ),
                  ),

                  // Insights Filter
                  SliverToBoxAdapter(
                    child: InsightsFilterWidget(
                      selectedFilter: _selectedFilter,
                      showArchived: _showArchived,
                      onFilterChanged: (filter) {
                        setState(() {
                          _selectedFilter = filter;
                        });
                      },
                      onArchivedToggled: (show) {
                        setState(() {
                          _showArchived = show;
                        });
                      },
                    ),
                  ),

                  // Insights List
                  if (_filteredInsights.isEmpty)
                    SliverToBoxAdapter(
                      child: _buildEmptyState(),
                    )
                  else
                    SliverList(
                      delegate: SliverChildBuilderDelegate(
                        (context, index) {
                          final insight = _filteredInsights[index];
                          return Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 4.w,
                              vertical: 1.h,
                            ),
                            child: InsightCardWidget(
                              insight: insight,
                              onRead: () => _toggleInsightRead(insight),
                              onArchive: () => _archiveInsight(insight),
                            ),
                          );
                        },
                        childCount: _filteredInsights.length,
                      ),
                    ),

                  // Bottom padding
                  SliverToBoxAdapter(
                    child: SizedBox(height: 10.h),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildRestrictedContent(BuildContext context) {
    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'AI Insights',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
            ),
            Text(
              'Analysis for ${widget.babyProfile.name}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
            ),
          ],
        ),
        backgroundColor: Theme.of(context).scaffoldBackgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            if (mounted && Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        ),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.psychology,
              size: 64,
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
            ),
            SizedBox(height: 24),
            Text(
              'AI Insights',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 16),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'Get AI-powered insights about your baby\'s development patterns. Upgrade to Premium to unlock this feature.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ),
            SizedBox(height: 32),
            ElevatedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/subscription');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: Text(
                'Upgrade to Premium',
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: EdgeInsets.all(8.w),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'psychology',
            size: 20.w,
            color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          ),
          SizedBox(height: 3.h),
          Text(
            'No insights available',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Generate insights by logging more activities or tap the categories above for specific analysis.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Insights'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ...InsightType.values.map((type) => RadioListTile<InsightType?>(
                  title: Text(type.name.toUpperCase()),
                  value: type,
                  groupValue: _selectedFilter,
                  onChanged: (value) {
                    setState(() {
                      _selectedFilter = value;
                    });
                    Navigator.pop(context);
                  },
                )),
            RadioListTile<InsightType?>(
              title: const Text('ALL'),
              value: null,
              groupValue: _selectedFilter,
              onChanged: (value) {
                setState(() {
                  _selectedFilter = null;
                });
                Navigator.pop(context);
              },
            ),
            const Divider(),
            SwitchListTile(
              title: const Text('Show Archived'),
              value: _showArchived,
              onChanged: (value) {
                setState(() {
                  _showArchived = value;
                });
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
