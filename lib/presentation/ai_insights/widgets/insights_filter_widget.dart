import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class InsightsFilterWidget extends StatelessWidget {
  final InsightType? selectedFilter;
  final bool showArchived;
  final Function(InsightType?) onFilterChanged;
  final Function(bool) onArchivedToggled;

  const InsightsFilterWidget({
    super.key,
    this.selectedFilter,
    this.showArchived = false,
    required this.onFilterChanged,
    required this.onArchivedToggled,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    final filters = [
      {'type': null, 'label': 'All', 'color': ThemeAwareColors.getSecondaryTextColor(context)},
      {
        'type': InsightType.sleep,
        'label': 'Sleep',
        'color': isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261)
      },
      {
        'type': InsightType.feeding,
        'label': 'Feeding',
        'color': isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4)
      },
      {
        'type': InsightType.growth,
        'label': 'Growth',
        'color': isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069)
      },
      {
        'type': InsightType.development,
        'label': 'Development',
        'color': isDark ? const Color(0xFF4DB6AC) : const Color(0xFF2A9D8F)
      },
      {
        'type': InsightType.health,
        'label': 'Health',
        'color': isDark ? const Color(0xFFFF8A65) : const Color(0xFFE76F51)
      },
      {
        'type': InsightType.behavior,
        'label': 'Behavior',
        'color': isDark ? const Color(0xFF90A4AE) : const Color(0xFF264653)
      },
      {
        'type': InsightType.recommendation,
        'label': 'Tips',
        'color': isDark ? const Color(0xFF9575CD) : const Color(0xFF6C63FF)
      },
    ];

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Filter Insights',
                style: GoogleFonts.inter(
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              Row(
                children: [
                  Text(
                    'Show Archived',
                    style: GoogleFonts.inter(
                      fontSize: 11.sp,
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Switch(
                    value: showArchived,
                    onChanged: onArchivedToggled,
                    activeColor: ThemeAwareColors.getPrimaryColor(context),
                    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 2.h),
          SizedBox(
            height: 8.h,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: filters.length,
              itemBuilder: (context, index) {
                final filter = filters[index];
                final type = filter['type'] as InsightType?;
                final label = filter['label'] as String;
                final color = filter['color'] as Color;
                final isSelected = selectedFilter == type;

                return Padding(
                  padding: EdgeInsets.only(right: 2.w),
                  child: GestureDetector(
                    onTap: () => onFilterChanged(type),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                          horizontal: 4.w, vertical: 1.5.h),
                      decoration: BoxDecoration(
                        color:
                            isSelected ? color : color.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(6.w),
                        border: Border.all(
                          color:
                              color.withValues(alpha: isSelected ? 1.0 : 0.3),
                          width: isSelected ? 2 : 1,
                        ),
                      ),
                      child: Center(
                        child: Text(
                          label,
                          style: GoogleFonts.inter(
                            fontSize: 11.sp,
                            fontWeight: FontWeight.w600,
                            color: isSelected 
                                ? Colors.white
                                : color,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          SizedBox(height: 2.h),
        ],
      ),
    );
  }
}
