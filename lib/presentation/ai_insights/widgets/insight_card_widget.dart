import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';
import './chart_widget.dart';

class InsightCardWidget extends StatelessWidget {
  final AIInsight insight;
  final VoidCallback onRead;
  final VoidCallback onArchive;

  const InsightCardWidget({
    super.key,
    required this.insight,
    required this.onRead,
    required this.onArchive,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.zero,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(3.w),
        color: ThemeAwareColors.getCardColor(context),
        border: Border.all(
          color: _getPriorityColor(context).withValues(alpha: 0.3),
          width: insight.priority == InsightPriority.high ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: insight.isRead ? 2 : 6,
            offset: Offset(0, insight.isRead ? 1 : 3),
          ),
        ],
      ),
      child: Container(
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(3.w),
          color: insight.isRead
              ? Colors.transparent
              : _getTypeColor(context).withValues(alpha: 0.02),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with type, priority, and actions
            Row(
              children: [
                _buildTypeChip(context),
                SizedBox(width: 2.w),
                _buildPriorityChip(context),
                const Spacer(),
                if (!insight.isRead)
                  Container(
                    width: 2.w,
                    height: 2.w,
                    decoration: BoxDecoration(
                      color: ThemeAwareColors.getPrimaryColor(context),
                      shape: BoxShape.circle,
                    ),
                  ),
                SizedBox(width: 2.w),
                PopupMenuButton<String>(
                  icon: CustomIconWidget(
                    iconName: 'more_vert',
                    size: 5.w,
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                  onSelected: (value) {
                    switch (value) {
                      case 'read':
                        onRead();
                        break;
                      case 'archive':
                        onArchive();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    PopupMenuItem(
                      value: 'read',
                      child: Row(
                        children: [
                          CustomIconWidget(
                            iconName: insight.isRead
                                ? 'mark_as_unread'
                                : 'mark_as_read',
                            size: 4.w,
                          ),
                          SizedBox(width: 2.w),
                          Text(insight.isRead
                              ? 'Mark as unread'
                              : 'Mark as read'),
                        ],
                      ),
                    ),
                    PopupMenuItem(
                      value: 'archive',
                      child: Row(
                        children: [
                          CustomIconWidget(
                            iconName: 'archive',
                            size: 4.w,
                          ),
                          SizedBox(width: 2.w),
                          const Text('Archive'),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),

            SizedBox(height: 2.h),

            // Title
            Text(
              insight.title,
              style: GoogleFonts.inter(
                fontSize: 14.sp,
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),

            SizedBox(height: 1.h),

            // Description
            Text(
              insight.description,
              style: GoogleFonts.inter(
                fontSize: 12.sp,
                color: ThemeAwareColors.getSecondaryTextColor(context),
                height: 1.4,
              ),
            ),

            // Chart
            if (insight.data != null && insight.data!['chartData'] != null) ...[
              SizedBox(height: 2.h),
              ChartWidget(
                data: insight.data!,
                type: insight.type,
                color: _getTypeColor(context),
              ),
            ],

            // Recommendations
            if (insight.recommendations.isNotEmpty) ...[
              SizedBox(height: 2.h),
              Text(
                'Recommendations:',
                style: GoogleFonts.inter(
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              SizedBox(height: 1.h),
              ...insight.recommendations.map((recommendation) => Padding(
                    padding: EdgeInsets.only(bottom: 0.5.h),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: 0.5.h, right: 2.w),
                          width: 1.w,
                          height: 1.w,
                          decoration: BoxDecoration(
                            color: _getTypeColor(context),
                            shape: BoxShape.circle,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            recommendation,
                            style: GoogleFonts.inter(
                              fontSize: 11.sp,
                              color: ThemeAwareColors.getSecondaryTextColor(context),
                              height: 1.3,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
            ],

            SizedBox(height: 2.h),

            // Footer with confidence and timestamp
            Row(
              children: [
                CustomIconWidget(
                  iconName: 'psychology',
                  size: 4.w,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
                SizedBox(width: 1.w),
                Text(
                  '${(insight.confidence * 100).toInt()}% confidence',
                  style: GoogleFonts.inter(
                    fontSize: 10.sp,
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const Spacer(),
                Text(
                  _formatDate(insight.generatedAt),
                  style: GoogleFonts.inter(
                    fontSize: 10.sp,
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTypeChip(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: _getTypeColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(2.w),
        border: Border.all(
          color: _getTypeColor(context).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        insight.type.name.toUpperCase(),
        style: GoogleFonts.inter(
          fontSize: 9.sp,
          fontWeight: FontWeight.w600,
          color: _getTypeColor(context),
        ),
      ),
    );
  }

  Widget _buildPriorityChip(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: _getPriorityColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(2.w),
      ),
      child: Text(
        insight.priority.name.toUpperCase(),
        style: GoogleFonts.inter(
          fontSize: 9.sp,
          fontWeight: FontWeight.w600,
          color: _getPriorityColor(context),
        ),
      ),
    );
  }

  Color _getTypeColor(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (insight.type) {
      case InsightType.sleep:
        return isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261);
      case InsightType.feeding:
        return isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4);
      case InsightType.growth:
        return isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069);
      case InsightType.development:
        return isDark ? const Color(0xFF4DB6AC) : const Color(0xFF2A9D8F);
      case InsightType.health:
        return isDark ? const Color(0xFFFF8A65) : const Color(0xFFE76F51);
      case InsightType.behavior:
        return isDark ? const Color(0xFF90A4AE) : const Color(0xFF264653);
      case InsightType.recommendation:
        return isDark ? const Color(0xFF9575CD) : const Color(0xFF6C63FF);
    }
  }

  Color _getPriorityColor(BuildContext context) {
    switch (insight.priority) {
      case InsightPriority.high:
        return ThemeAwareColors.getErrorColor(context);
      case InsightPriority.medium:
        return ThemeAwareColors.getWarningColor(context);
      case InsightPriority.low:
        return ThemeAwareColors.getSecondaryTextColor(context);
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
