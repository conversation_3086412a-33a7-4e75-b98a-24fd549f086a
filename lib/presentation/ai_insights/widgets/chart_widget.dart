import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class ChartWidget extends StatelessWidget {
  final Map<String, dynamic> data;
  final InsightType type;
  final Color color;

  const ChartWidget({
    super.key,
    required this.data,
    required this.type,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    if (data['chartData'] == null || (data['chartData'] as List).isEmpty) {
      return const SizedBox.shrink();
    }

    switch (type) {
      case InsightType.sleep:
        return _buildSleepChart(context);
      case InsightType.feeding:
        return _buildFeedingChart(context);
      case InsightType.growth:
        return _buildGrowthChart(context);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildSleepChart(BuildContext context) {
    final chartData = (data['chartData'] as List).cast<Map<String, dynamic>>();
    final spots = chartData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value['hours'].toDouble());
    }).toList();

    return Container(
      height: 20.h,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(2.w),
      ),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: ThemeAwareColors.getDividerColor(context),
                strokeWidth: 0.5,
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  return Text(
                    '${value.toInt()}h',
                    style: GoogleFonts.inter(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 10.sp,
                    ),
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() >= chartData.length) return const SizedBox();
                  return Text(
                    chartData[value.toInt()]['date'] as String,
                    style: GoogleFonts.inter(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 10.sp,
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(show: false),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: color,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                color: color.withValues(alpha: 0.1),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeedingChart(BuildContext context) {
    final chartData = (data['chartData'] as List).cast<Map<String, dynamic>>();
    final spots = chartData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value['feeds'].toDouble());
    }).toList();

    return Container(
      height: 20.h,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(2.w),
      ),
      child: BarChart(
        BarChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: ThemeAwareColors.getDividerColor(context),
                strokeWidth: 0.5,
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: GoogleFonts.inter(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 10.sp,
                    ),
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() >= chartData.length) return const SizedBox();
                  return Text(
                    chartData[value.toInt()]['date'] as String,
                    style: GoogleFonts.inter(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 10.sp,
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(
              color: ThemeAwareColors.getDividerColor(context),
              width: 1,
            ),
          ),
          backgroundColor: ThemeAwareColors.getCardColor(context),
          barGroups: chartData.asMap().entries.map((entry) {
            return BarChartGroupData(
              x: entry.key,
              barRods: [
                BarChartRodData(
                  toY: entry.value['feeds'].toDouble(),
                  color: color,
                  width: 16,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildGrowthChart(BuildContext context) {
    final chartData = (data['chartData'] as List).cast<Map<String, dynamic>>();
    final spots = chartData.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value['value'].toDouble());
    }).toList();

    return Container(
      height: 20.h,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getSurfaceColor(context),
        borderRadius: BorderRadius.circular(2.w),
      ),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: false,
            horizontalInterval: 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: ThemeAwareColors.getDividerColor(context),
                strokeWidth: 0.5,
              );
            },
          ),
          titlesData: FlTitlesData(
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toStringAsFixed(1),
                    style: GoogleFonts.inter(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 10.sp,
                    ),
                  );
                },
              ),
            ),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                getTitlesWidget: (value, meta) {
                  if (value.toInt() >= chartData.length) return const SizedBox();
                  return Text(
                    chartData[value.toInt()]['date'] as String,
                    style: GoogleFonts.inter(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 10.sp,
                    ),
                  );
                },
              ),
            ),
            rightTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
            topTitles: AxisTitles(
              sideTitles: SideTitles(showTitles: false),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(
              color: ThemeAwareColors.getDividerColor(context),
              width: 1,
            ),
          ),
          backgroundColor: ThemeAwareColors.getCardColor(context),
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: color,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                color: color.withValues(alpha: 0.1),
              ),
            ),
          ],
        ),
      ),
    );
  }
} 