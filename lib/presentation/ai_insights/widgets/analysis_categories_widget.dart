import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class AnalysisCategoriesWidget extends StatelessWidget {
  final Function(InsightType) onCategorySelected;
  final bool isLoading;

  const AnalysisCategoriesWidget({
    super.key,
    required this.onCategorySelected,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    final categories = [
      {
        'type': InsightType.sleep,
        'title': 'Sleep Analysis',
        'subtitle': 'Patterns & Quality',
        'icon': 'bedtime',
        'color': isDark ? const Color(0xFFFFB74D) : const Color(0xFFF4A261),
      },
      {
        'type': InsightType.feeding,
        'title': 'Feeding Analysis',
        'subtitle': 'Schedule & Amount',
        'icon': 'restaurant',
        'color': isDark ? const Color(0xFF64B5F6) : const Color(0xFF4A90A4),
      },
      {
        'type': InsightType.growth,
        'title': 'Growth Analysis',
        'subtitle': 'Development Trends',
        'icon': 'trending_up',
        'color': isDark ? const Color(0xFF81C784) : const Color(0xFF7FB069),
      },
    ];

    return Container(
      padding: EdgeInsets.all(4.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Generate Specific Analysis',
            style: GoogleFonts.inter(
              fontSize: 16.sp,
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Tap on a category to get detailed AI-powered insights',
            style: GoogleFonts.inter(
              fontSize: 12.sp,
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: 3.h),
          Row(
            children: categories.map((category) {
              return Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 1.w),
                  child: _buildCategoryCard(
                    context,
                    type: category['type'] as InsightType,
                    title: category['title'] as String,
                    subtitle: category['subtitle'] as String,
                    icon: category['icon'] as String,
                    color: category['color'] as Color,
                  ),
                ),
              );
            }).toList(),
          ),
          SizedBox(height: 3.h),
        ],
      ),
    );
  }

  Widget _buildCategoryCard(
    BuildContext context, {
    required InsightType type,
    required String title,
    required String subtitle,
    required String icon,
    required Color color,
  }) {
    return GestureDetector(
      onTap: isLoading ? null : () => onCategorySelected(type),
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.08),
          borderRadius: BorderRadius.circular(3.w),
          border: Border.all(
            color: color.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Column(
          children: [
            Container(
              width: 12.w,
              height: 12.w,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.15),
                borderRadius: BorderRadius.circular(6.w),
              ),
              child: isLoading
                  ? Center(
                      child: SizedBox(
                        width: 4.w,
                        height: 4.w,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(color),
                        ),
                      ),
                    )
                  : CustomIconWidget(
                      iconName: icon,
                      color: color,
                      size: 6.w,
                    ),
            ),
            SizedBox(height: 2.h),
            Text(
              title,
              style: GoogleFonts.inter(
                fontSize: 11.sp,
                fontWeight: FontWeight.w600,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 0.5.h),
            Text(
              subtitle,
              style: GoogleFonts.inter(
                fontSize: 9.sp,
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
