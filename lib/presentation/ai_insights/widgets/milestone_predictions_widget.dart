import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import '../../../core/app_export.dart';
import '../../../theme/theme_aware_colors.dart';

class MilestonePredictionsWidget extends StatelessWidget {
  final List<Map<String, dynamic>> predictions;
  const MilestonePredictionsWidget({super.key, required this.predictions});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Upcoming Milestones',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w700),
          ),
          SizedBox(height: 0.5.h),
          Text(
            'AI-powered predictions and preparation tips',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: ThemeAwareColors.getSecondaryTextColor(context)),
          ),
          Si<PERSON><PERSON><PERSON>(height: 2.h),
          Container(
            decoration: BoxDecoration(
              color: ThemeAwareColors.getSurfaceColor(context),
              borderRadius: BorderRadius.circular(16),
            ),
            padding: EdgeInsets.all(4.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(Icons.emoji_events, color: ThemeAwareColors.getPrimaryColor(context)),
                    SizedBox(width: 2.w),
                    Text(
                      'AI Milestone Predictions',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.w600),
                    ),
                  ],
                ),
                SizedBox(height: 0.5.h),
                Text(
                  'Based on your baby\'s development patterns',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(color: ThemeAwareColors.getSecondaryTextColor(context)),
                ),
                SizedBox(height: 2.h),
                ...predictions.map((m) => _buildMilestoneCard(context, m)).toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMilestoneCard(BuildContext context, Map<String, dynamic> m) {
    final Color cardColor = Color(m['color'] ?? 0xFF7FB069);
    final double confidence = (m['confidence'] as num?)?.toDouble() ?? 0.0;
    bool expanded = false;
    return StatefulBuilder(
      builder: (context, setState) => Container(
        margin: EdgeInsets.only(bottom: 2.h),
        decoration: BoxDecoration(
          color: cardColor.withOpacity(0.08),
          borderRadius: BorderRadius.circular(14),
          border: Border.all(color: cardColor.withOpacity(0.2)),
        ),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CustomIconWidget(
                    iconName: m['icon'] ?? 'psychology',
                    color: cardColor,
                    size: 7.w,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Text(
                      m['name'] ?? '',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: cardColor,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 1.h),
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'ETA: ${m['eta'] ?? 'N/A'}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: cardColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                    decoration: BoxDecoration(
                      color: cardColor.withOpacity(0.15),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${(confidence * 100).toStringAsFixed(0)}% confidence',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: cardColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 1.h),
              Text(
                m['description'] ?? '',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              SizedBox(height: 1.h),
              Row(
                children: [
                  Text('Confidence:', style: Theme.of(context).textTheme.bodySmall),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: LinearProgressIndicator(
                      value: confidence,
                      color: cardColor,
                      backgroundColor: cardColor.withOpacity(0.15),
                      minHeight: 6,
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Text('${(confidence * 100).toStringAsFixed(0)}%', style: Theme.of(context).textTheme.bodySmall?.copyWith(color: cardColor, fontWeight: FontWeight.w600)),
                ],
              ),
              SizedBox(height: 1.h),
              GestureDetector(
                onTap: () => setState(() => expanded = !expanded),
                child: Container(
                  decoration: BoxDecoration(
                    color: cardColor.withOpacity(0.10),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.2.h),
                  child: Row(
                    children: [
                      Icon(Icons.tips_and_updates, color: cardColor, size: 20),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          'Preparation Tips',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: cardColor,
                          ),
                        ),
                      ),
                      Icon(expanded ? Icons.expand_less : Icons.expand_more, color: cardColor),
                    ],
                  ),
                ),
              ),
              if (expanded && m['tips'] != null && (m['tips'] as List).isNotEmpty)
                Padding(
                  padding: EdgeInsets.only(top: 1.h, left: 2.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...List<String>.from(m['tips']).map((tip) => Padding(
                        padding: EdgeInsets.only(bottom: 0.5.h),
                        child: Row(
                          children: [
                            Icon(Icons.circle, size: 8, color: cardColor),
                            SizedBox(width: 2.w),
                            Expanded(child: Text(tip, style: Theme.of(context).textTheme.bodySmall)),
                          ],
                        ),
                      )),
                    ],
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
} 