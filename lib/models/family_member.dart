import 'enums.dart';

/// Model representing a family member in the family sharing system
class FamilyMember {
  /// Unique identifier for the family member
  final String id;
  
  /// Full name of the family member
  final String fullName;
  
  /// Email address of the family member
  final String email;
  
  /// Role of the family member (parent, caregiver, grandparent, etc.)
  final String role;
  
  /// URL to the family member's avatar image
  final String? avatarUrl;
  
  /// Date when the family member joined
  final DateTime joinedAt;
  
  /// Last time the family member was active
  final DateTime? lastActiveAt;
  
  /// Current status of the family member
  final FamilyMemberStatus status;
  
  /// Permissions granted to this family member
  final Map<String, bool> permissions;
  
  /// ID of the user who invited this family member
  final String? invitedBy;
  
  /// Phone number of the family member (optional)
  final String? phoneNumber;
  
  /// Timezone of the family member
  final String? timezone;
  
  /// Date when the invitation was sent (for pending members)
  final DateTime? invitationSentAt;
  
  /// Date when the invitation expires (for pending members)
  final DateTime? invitationExpiresAt;

  const FamilyMember({
    required this.id,
    required this.fullName,
    required this.email,
    required this.role,
    this.avatarUrl,
    required this.joinedAt,
    this.lastActiveAt,
    required this.status,
    required this.permissions,
    this.invitedBy,
    this.phoneNumber,
    this.timezone,
    this.invitationSentAt,
    this.invitationExpiresAt,
  });

  /// Get initials for avatar fallback
  String get initials {
    final names = fullName.trim().split(' ');
    if (names.isEmpty) return '?';
    if (names.length == 1) return names[0][0].toUpperCase();
    return '${names[0][0]}${names[names.length - 1][0]}'.toUpperCase();
  }
  
  /// Check if the family member is currently active
  bool get isActive => status == FamilyMemberStatus.active;
  
  /// Check if the family member has a pending invitation
  bool get isPending => status == FamilyMemberStatus.pending;
  
  /// Check if the invitation has expired (for pending members)
  bool get isInvitationExpired {
    if (!isPending || invitationExpiresAt == null) return false;
    return DateTime.now().isAfter(invitationExpiresAt!);
  }
  
  /// Get days since last activity
  int? get daysSinceLastActivity {
    if (lastActiveAt == null) return null;
    return DateTime.now().difference(lastActiveAt!).inDays;
  }
  
  /// Check if the member has a specific permission
  bool hasPermission(String permission) {
    return permissions[permission] == true;
  }
  
  /// Get role display name with proper capitalization
  String get roleDisplayName {
    return role.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1).toLowerCase()
    ).join(' ');
  }
  
  /// Get activity status text
  String get activityStatusText {
    if (!isActive) return status.displayName;
    
    if (lastActiveAt == null) return 'Never active';
    
    final days = daysSinceLastActivity!;
    if (days == 0) return 'Active today';
    if (days == 1) return 'Active yesterday';
    if (days < 7) return 'Active $days days ago';
    if (days < 30) return 'Active ${(days / 7).round()} weeks ago';
    return 'Active ${(days / 30).round()} months ago';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() => {
        'id': id,
        'full_name': fullName,
        'email': email,
        'role': role,
        'avatar_url': avatarUrl,
        'joined_at': joinedAt.toIso8601String(),
        'last_active_at': lastActiveAt?.toIso8601String(),
        'status': status.toString().split('.').last,
        'permissions': permissions,
        'invited_by': invitedBy,
        'phone_number': phoneNumber,
        'timezone': timezone,
        'invitation_sent_at': invitationSentAt?.toIso8601String(),
        'invitation_expires_at': invitationExpiresAt?.toIso8601String(),
      };

  /// Create from JSON
  factory FamilyMember.fromJson(Map<String, dynamic> json) => FamilyMember(
        id: json['id'],
        fullName: json['full_name'],
        email: json['email'],
        role: json['role'],
        avatarUrl: json['avatar_url'],
        joinedAt: DateTime.parse(json['joined_at']),
        lastActiveAt: json['last_active_at'] != null 
            ? DateTime.parse(json['last_active_at']) 
            : null,
        status: FamilyMemberStatusExtension.fromStringWithFallback(json['status']),
        permissions: Map<String, bool>.from(json['permissions'] ?? {}),
        invitedBy: json['invited_by'],
        phoneNumber: json['phone_number'],
        timezone: json['timezone'],
        invitationSentAt: json['invitation_sent_at'] != null 
            ? DateTime.parse(json['invitation_sent_at']) 
            : null,
        invitationExpiresAt: json['invitation_expires_at'] != null 
            ? DateTime.parse(json['invitation_expires_at']) 
            : null,
      );

  /// Create a copy with updated values
  FamilyMember copyWith({
    String? id,
    String? fullName,
    String? email,
    String? role,
    String? avatarUrl,
    DateTime? joinedAt,
    DateTime? lastActiveAt,
    FamilyMemberStatus? status,
    Map<String, bool>? permissions,
    String? invitedBy,
    String? phoneNumber,
    String? timezone,
    DateTime? invitationSentAt,
    DateTime? invitationExpiresAt,
  }) =>
      FamilyMember(
        id: id ?? this.id,
        fullName: fullName ?? this.fullName,
        email: email ?? this.email,
        role: role ?? this.role,
        avatarUrl: avatarUrl ?? this.avatarUrl,
        joinedAt: joinedAt ?? this.joinedAt,
        lastActiveAt: lastActiveAt ?? this.lastActiveAt,
        status: status ?? this.status,
        permissions: permissions ?? this.permissions,
        invitedBy: invitedBy ?? this.invitedBy,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        timezone: timezone ?? this.timezone,
        invitationSentAt: invitationSentAt ?? this.invitationSentAt,
        invitationExpiresAt: invitationExpiresAt ?? this.invitationExpiresAt,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FamilyMember &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          email == other.email;

  @override
  int get hashCode => id.hashCode ^ email.hashCode;

  @override
  String toString() => 'FamilyMember(id: $id, name: $fullName, role: $role, status: ${status.toString().split('.').last})';
}

/// Default permissions for different family member roles
class FamilyMemberPermissions {
  /// Default permissions for parent role
  static const Map<String, bool> parent = {
    'view_activities': true,
    'add_activities': true,
    'edit_activities': true,
    'delete_activities': true,
    'manage_babies': true,
    'manage_family': true,
    'view_insights': true,
    'export_data': true,
    'manage_settings': true,
  };
  
  /// Default permissions for caregiver role
  static const Map<String, bool> caregiver = {
    'view_activities': true,
    'add_activities': true,
    'edit_activities': true,
    'delete_activities': false,
    'manage_babies': false,
    'manage_family': false,
    'view_insights': true,
    'export_data': false,
    'manage_settings': false,
  };
  
  /// Default permissions for grandparent role
  static const Map<String, bool> grandparent = {
    'view_activities': true,
    'add_activities': true,
    'edit_activities': false,
    'delete_activities': false,
    'manage_babies': false,
    'manage_family': false,
    'view_insights': true,
    'export_data': false,
    'manage_settings': false,
  };
  
  /// Default permissions for babysitter role
  static const Map<String, bool> babysitter = {
    'view_activities': true,
    'add_activities': true,
    'edit_activities': false,
    'delete_activities': false,
    'manage_babies': false,
    'manage_family': false,
    'view_insights': false,
    'export_data': false,
    'manage_settings': false,
  };
  
  /// Get default permissions for a role
  static Map<String, bool> getDefaultPermissions(String role) {
    switch (role.toLowerCase()) {
      case 'parent':
        return Map<String, bool>.from(parent);
      case 'caregiver':
        return Map<String, bool>.from(caregiver);
      case 'grandparent':
        return Map<String, bool>.from(grandparent);
      case 'babysitter':
        return Map<String, bool>.from(babysitter);
      default:
        return Map<String, bool>.from(caregiver); // Default to caregiver permissions
    }
  }
}