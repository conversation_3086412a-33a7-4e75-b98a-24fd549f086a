import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

enum MilestoneCategory {
  motor,
  cognitive,
  language,
  social,
  emotional,
  sensory,
  adaptive,
  feeding,
  sleep,
  play,
  health,
  custom
}

enum MilestoneType {
  gross_motor,
  fine_motor,
  communication,
  problem_solving,
  personal_social,
  cognitive_development,
  sensory_development,
  adaptive_behavior
}

extension MilestoneCategoryExtension on MilestoneCategory {
  String get displayName {
    switch (this) {
      case MilestoneCategory.motor:
        return 'Motor Skills';
      case MilestoneCategory.cognitive:
        return 'Cognitive';
      case MilestoneCategory.language:
        return 'Language';
      case MilestoneCategory.social:
        return 'Social';
      case MilestoneCategory.emotional:
        return 'Emotional';
      case MilestoneCategory.sensory:
        return 'Sensory';
      case MilestoneCategory.adaptive:
        return 'Adaptive';
      case MilestoneCategory.feeding:
        return 'Feeding';
      case MilestoneCategory.sleep:
        return 'Sleep';
      case MilestoneCategory.play:
        return 'Play';
      case MilestoneCategory.health:
        return 'Health';
      case MilestoneCategory.custom:
        return 'Custom';
    }
  }

  Color get color {
    switch (this) {
      case MilestoneCategory.motor:
        return const Color(0xFF4CAF50);
      case MilestoneCategory.cognitive:
        return const Color(0xFF2196F3);
      case MilestoneCategory.language:
        return const Color(0xFF9C27B0);
      case MilestoneCategory.social:
        return const Color(0xFFFF9800);
      case MilestoneCategory.emotional:
        return const Color(0xFFE91E63);
      case MilestoneCategory.sensory:
        return const Color(0xFF00BCD4);
      case MilestoneCategory.adaptive:
        return const Color(0xFF795548);
      case MilestoneCategory.feeding:
        return const Color(0xFF6366F1);
      case MilestoneCategory.sleep:
        return const Color(0xFFF59E0B);
      case MilestoneCategory.play:
        return const Color(0xFF10B981);
      case MilestoneCategory.health:
        return const Color(0xFFEF4444);
      case MilestoneCategory.custom:
        return const Color(0xFF6B7280);
    }
  }

  IconData get icon {
    switch (this) {
      case MilestoneCategory.motor:
        return Icons.directions_run;
      case MilestoneCategory.cognitive:
        return Icons.psychology;
      case MilestoneCategory.language:
        return Icons.record_voice_over;
      case MilestoneCategory.social:
        return Icons.people;
      case MilestoneCategory.emotional:
        return Icons.favorite;
      case MilestoneCategory.sensory:
        return Icons.visibility;
      case MilestoneCategory.adaptive:
        return Icons.settings;
      case MilestoneCategory.feeding:
        return Icons.restaurant;
      case MilestoneCategory.sleep:
        return Icons.bedtime;
      case MilestoneCategory.play:
        return Icons.toys;
      case MilestoneCategory.health:
        return Icons.health_and_safety;
      case MilestoneCategory.custom:
        return Icons.add_circle_outline;
    }
  }
}

extension MilestoneTypeExtension on MilestoneType {
  String get displayName {
    switch (this) {
      case MilestoneType.gross_motor:
        return 'Gross Motor';
      case MilestoneType.fine_motor:
        return 'Fine Motor';
      case MilestoneType.communication:
        return 'Communication';
      case MilestoneType.problem_solving:
        return 'Problem Solving';
      case MilestoneType.personal_social:
        return 'Personal-Social';
      case MilestoneType.cognitive_development:
        return 'Cognitive Development';
      case MilestoneType.sensory_development:
        return 'Sensory Development';
      case MilestoneType.adaptive_behavior:
        return 'Adaptive Behavior';
    }
  }
}

class Milestone {
  final String id;
  final String babyId;
  final String title;
  final String description;
  final MilestoneCategory category;
  final MilestoneType type;
  final DateTime achievedDate;
  final int ageInMonths;
  final int ageInDays;
  final String? notes;
  final List<String>? photoUrls;
  final bool isCustom;
  final DateTime createdAt;
  final DateTime updatedAt;

  Milestone({
    String? id,
    required this.babyId,
    required this.title,
    required this.description,
    required this.category,
    required this.type,
    required this.achievedDate,
    required this.ageInMonths,
    required this.ageInDays,
    this.notes,
    this.photoUrls,
    this.isCustom = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  Map<String, dynamic> toJson() {
    final json = {
      'baby_id': babyId,
      'title': title,
      'description': description,
      'milestone_date': achievedDate.toIso8601String().split('T')[0], // Use milestone_date as DATE only
      'age_in_days': ageInDays,
      'category': category.name,
    };
    
    // Only include photo_url if we have one
    if (photoUrls?.isNotEmpty == true) {
      json['photo_url'] = photoUrls!.first;
    }
    
    return json;
  }
  
  // Separate method for database insertion - excludes fields that should be auto-generated
  Map<String, dynamic> toInsertJson() {
    final json = {
      'baby_id': babyId,
      'title': title,
      'description': description,
      'achieved_date': achievedDate.toString(), // Use same format as sleep logs (toString() instead of toIso8601String())
      'milestone_date': achievedDate.toIso8601String().split('T')[0], // Use milestone_date as date string
      'age_in_days': ageInDays,
      'category': category.name,
    };
    
    // Only include photo_url if we have one
    if (photoUrls?.isNotEmpty == true) {
      json['photo_url'] = photoUrls!.first;
    }
    
    return json;
  }

  factory Milestone.fromJson(Map<String, dynamic> json) {
    // Handle both achieved_date and milestone_date columns for backward compatibility
    String? dateString = json['achieved_date'] ?? json['milestone_date'];
    if (dateString == null) {
      throw ArgumentError('Neither achieved_date nor milestone_date found in JSON');
    }
    
    // Parse the date string and handle time component like ActivityLog does
    DateTime achievedDate;
    try {
      final parsed = DateTime.parse(dateString);
      
      // If the parsed date has only date component (time is 00:00:00), 
      // it means we got a date-only string, so we should use current time
      if (parsed.hour == 0 && parsed.minute == 0 && parsed.second == 0 && parsed.millisecond == 0) {
        // This is likely a date-only string, use current time for the time component
        final now = DateTime.now();
        achievedDate = DateTime(parsed.year, parsed.month, parsed.day, now.hour, now.minute, now.second, now.millisecond);
      } else {
        // This already has time component, use as-is
        achievedDate = parsed;
      }
    } catch (e) {
      debugPrint('⚠️ Error parsing milestone date $dateString: $e');
      achievedDate = DateTime.now();
    }
    
    return Milestone(
      id: json['id'],
      babyId: json['baby_id'],
      title: json['title'],
      description: json['description'] ?? '',
      category: MilestoneCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => MilestoneCategory.motor,
      ),
      type: MilestoneType.gross_motor, // Default type since it's not in the DB schema
      achievedDate: achievedDate,
      ageInMonths: json['age_in_days'] != null ? (json['age_in_days'] as int) ~/ 30 : 0,
      ageInDays: json['age_in_days'] ?? 0,
      notes: null, // Notes column doesn't exist in the database
      photoUrls: json['photo_url'] != null 
          ? [json['photo_url']] 
          : null,
      isCustom: false, // Not in the DB schema
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Milestone copyWith({
    String? id,
    String? babyId,
    String? title,
    String? description,
    MilestoneCategory? category,
    MilestoneType? type,
    DateTime? achievedDate,
    int? ageInMonths,
    int? ageInDays,
    String? notes,
    List<String>? photoUrls,
    bool? isCustom,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Milestone(
      id: id ?? this.id,
      babyId: babyId ?? this.babyId,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      type: type ?? this.type,
      achievedDate: achievedDate ?? this.achievedDate,
      ageInMonths: ageInMonths ?? this.ageInMonths,
      ageInDays: ageInDays ?? this.ageInDays,
      notes: notes ?? this.notes,
      photoUrls: photoUrls ?? this.photoUrls,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Convert milestone to the format expected by Recent Activities widget
  Map<String, dynamic> toRecentActivityMap() {
    return {
      'id': id,
      'type': 'milestone',
      'title': title,
      'timestamp': achievedDate,
      'duration': null, // Milestones don't have duration
      'amount': null, // Milestones don't have amount
      'type_detail': category.displayName,
      'icon': category.icon.codePoint.toString(),
      'color': category.color,
      'notes': notes ?? '',
      'milestone_title': title,
      'milestone_description': description,
      'milestone_category': category.displayName,
      'milestone_type': type.displayName,
      'age_in_months': ageInMonths,
      'age_in_days': ageInDays,
      'is_custom': isCustom,
    };
  }
}

// Updated milestone templates by category
class UpdatedMilestoneTemplates {
  static final Map<String, List<String>> templatesByCategory = {
    'Gross Motor': [
      'Lifts head',
      'Rolls over',
      'Sits without support',
      'Crawls',
      'Stands with support',
      'Walks independently',
      'Runs',
      'Climbs stairs'
    ],
    'Fine Motor': [
      'Opens hands',
      'Grasps objects',
      'Transfers hand to hand',
      'Uses pincer grasp',
      'Scribbles',
      'Stacks blocks',
      'Turns book pages',
      'Uses utensils'
    ],
    'Cognitive': [
      'Tracks moving objects',
      'Recognizes faces',
      'Explores with mouth',
      'Finds hidden items',
      'Imitates actions',
      'Matches shapes',
      'Follows simple instructions',
      'Understands cause and effect'
    ],
    'Language': [
      'Coos',
      'Babbles',
      'Says "mama" or "dada"',
      'Points to communicate',
      'Uses simple words',
      'Combines 2–3 words',
      'Names objects',
      'Follows verbal commands'
    ],
    'Social': [
      'Smiles socially',
      'Enjoys play with others',
      'Shows stranger anxiety',
      'Waves or claps',
      'Shows affection',
      'Parallel play',
      'Takes turns',
      'Initiates interaction'
    ],
    'Emotional': [
      'Calms with caregiver',
      'Laughs',
      'Shows frustration',
      'Seeks comfort',
      'Displays pride',
      'Recognizes emotions',
      'Shows empathy',
      'Manages brief separation'
    ],
    'Sensory': [
      'Responds to sound',
      'Enjoys textures',
      'Turns to familiar voice',
      'Dislikes loud noise',
      'Explores new tastes',
      'Engages in messy play',
      'Adjusts to light/noise',
      'Prefers certain stimuli'
    ],
    'Adaptive / Self-Help': [
      'Holds bottle',
      'Feeds self with fingers',
      'Drinks from cup',
      'Uses spoon',
      'Removes clothing',
      'Tries to dress',
      'Washes hands with help',
      'Begins toilet training'
    ],
    'Feeding': [
      'Breastfeeds/formula',
      'Starts solids',
      'Eats mashed food',
      'Eats finger food',
      'Drinks from sippy cup',
      'Feeds self',
      'Chews all textures',
      'Uses straw'
    ],
    'Sleep': [
      'Sleeps 14–17 hours/day',
      'Sleeps longer at night',
      'Naps 2–3 times/day',
      'Sleeps through night',
      'Transitions to 1 nap',
      'Self-soothes',
      'Moves to toddler bed',
      'Consistent bedtime routine'
    ],
    'Play': [
      'Watches faces',
      'Bats at toys',
      'Bangs objects',
      'Pushes/pulls toys',
      'Engages in pretend play',
      'Builds block tower',
      'Plays with dolls',
      'Enjoys interactive games'
    ],
    'Health': [
      'Steady weight gain',
      'First tooth erupts',
      'Up-to-date immunizations',
      'Regular bowel movements',
      'Few colds',
      'Tracks growth percentile',
      'Doctor wellness checks',
      'Begins brushing teeth'
    ]
  };
}
// Updated milestone templates by age
// This class holds predefined milestone examples organized by category.
class MilestoneTemplates {
  static List<Map<String, dynamic>> getTemplatesForAge(int ageInMonths) {
    final templates = <Map<String, dynamic>>[];
    
    // 0-3 months (Newborn)
    if (ageInMonths <= 3) {
      templates.addAll([
// Gross Motor
        {
          'title': 'Lifts head',
          'description': 'Lifts head during tummy time',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Rolls over',
          'description': 'Rolls from tummy to back or back to tummy',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Sits without support',
          'description': 'Sits up without needing support',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Crawls',
          'description': 'Crawls on hands and knees',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Stands with support',
          'description': 'Stands while holding onto furniture',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Follows Objects',
          'description': 'Tracks moving objects with eyes',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Makes Cooing Sounds',
          'description': 'Makes soft vowel sounds',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Lifts Head 45 Degrees',
          'description': 'Lifts head and chest when on tummy',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Responds to Voices',
          'description': 'Turns head toward familiar voices',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Recognizes Parents',
          'description': 'Shows recognition of parents faces',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        // Feeding Milestones
        {
          'title': 'Latches for Feeding',
          'description': 'Successfully latches for breastfeeding',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Recognizes Bottle',
          'description': 'Shows excitement when bottle approaches',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        // Sleep Milestones
        {
          'title': 'Longer Sleep Periods',
          'description': 'Sleeps for 3-4 hours at a time',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Day/Night Awareness',
          'description': 'Begins to distinguish day from night',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        // Health Milestones
        {
          'title': 'Umbilical Cord Heals',
          'description': 'Umbilical cord falls off and heals',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // 4-6 months (Infant)
    if (ageInMonths >= 4 && ageInMonths <= 6) {
      templates.addAll([
        // Motor Skills
        {
          'title': 'Rolls Over',
          'description': 'Rolls from tummy to back or back to tummy',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Sits with Support',
          'description': 'Sits up when supported',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Grasps Objects',
          'description': 'Reaches for and grasps toys',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Transfers Objects',
          'description': 'Moves toys from one hand to another',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        // Social & Emotional
        {
          'title': 'Climbs stairs',
          'description': 'Climbs stairs with support',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        // Fine Motor
        {
          'title': 'Opens hands',
          'description': 'Opens and closes hands',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Grasps objects',
          'description': 'Holds toys using whole hand',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Transfers hand to hand',
          'description': 'Moves objects from one hand to another',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Uses pincer grasp',
          'description': 'Picks small items with thumb and index finger',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Scribbles',
          'description': 'Makes marks on paper with crayons',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Stacks blocks',
          'description': 'Builds towers with blocks',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Turns book pages',
          'description': 'Turns pages of a book singly',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Uses utensils',
          'description': 'Uses fork or spoon',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Laughs',
          'description': 'Laughs out loud',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Tracks moving objects',
          'description': 'Follows objects with eyes',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Recognizes faces',
          'description': 'Shows recognition of familiar faces',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Explores with mouth',
          'description': 'Puts objects in mouth to explore',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Finds hidden items',
          'description': 'Looks for items hidden from view',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Imitates actions',
          'description': 'Copies physical actions or gestures',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Matches shapes',
          'description': 'Places shapes in correct holes',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Follows simple instructions',
          'description': 'Performs tasks with simple commands',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Understands cause and effect',
          'description': 'Shows understanding of actions leading to outcomes',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Recognizes Faces',
          'description': 'Shows preference for familiar faces',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        // Feeding Milestones
        {
          'title': 'Ready for Solids',
          'description': 'Shows interest in food, sits with support',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'First Taste of Food',
          'description': 'Tries first solid foods (purees)',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        // Play Milestones
        {
          'title': 'Enjoys Peek-a-Boo',
          'description': 'Responds with joy to peek-a-boo games',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Plays with Toys',
          'description': 'Shows interest in rattles and soft toys',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Musical Toys',
          'description': 'Shows interest in music boxes and musical toys',
          'category': MilestoneCategory.play,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Reaches for Dangling Toys',
          'description': 'Attempts to grasp toys hanging above',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Tummy Time Play',
          'description': 'Shows interest in toys during tummy time',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        // Cognitive Milestones
        {
          'title': 'Recognizes Familiar Faces',
          'description': 'Shows different reactions to familiar vs unfamiliar faces',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Begins to Focus',
          'description': 'Can focus on objects 8-12 inches away',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Shows Memory',
          'description': 'Recognizes routine activities like feeding time',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        // Emotional Milestones
        {
          'title': 'Calms with Comfort',
          'description': 'Responds to soothing voice and touch',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows Contentment',
          'description': 'Displays relaxed body language when comfortable',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Responds to Emotions',
          'description': 'Reacts to happy or sad voices differently',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Adaptive Milestones
        {
          'title': 'Adapts to Schedule',
          'description': 'Begins to follow feeding and sleeping routines',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Self-Soothing Attempts',
          'description': 'Tries to calm self by sucking thumb or fingers',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // 7-9 months (Mobile Infant)
    if (ageInMonths >= 7 && ageInMonths <= 9) {
      templates.addAll([
        // Motor Skills
        {
          'title': 'Sits Without Support',
          'description': 'Sits up without help',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Crawls',
          'description': 'Moves forward on hands and knees',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Pulls to Stand',
          'description': 'Pulls self up to standing position',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Bangs Objects',
          'description': 'Bangs two objects together',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        // Language & Communication
        {
          'title': 'Says First Words',
          'description': 'Says "mama" or "dada"',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Coos',
          'description': 'Makes cooing sounds',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Babbles',
          'description': 'Makes babbling sounds with consonants',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        // Social & Play
        {
          'title': 'Plays Peek-a-Boo',
          'description': 'Enjoys peek-a-boo games',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Stranger Anxiety',
          'description': 'Shows wariness around strangers',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Feeding Milestones
        {
          'title': "Says 'mama' or 'dada'",
          'description': "Uses 'mama' or 'dada' specifically",
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Points to communicate',
          'description': 'Points to express needs or show objects',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Uses simple words',
          'description': 'Speaks basic words like dog, ball',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Combines 2–3 words',
          'description': 'Forms basic sentences with a few words',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Names objects',
          'description': 'Identifies objects by name',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Follows verbal commands',
          'description': 'Understands and acts on verbal instructions',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Finger Foods',
          'description': 'Picks up and eats finger foods',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Drinks from Cup',
          'description': 'Attempts to drink from a sippy cup',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // 10-12 months (Toddler)
    if (ageInMonths >= 10 && ageInMonths <= 12) {
      templates.addAll([
        // Motor Skills
        {
          'title': 'First Steps',
          'description': 'Takes first independent steps',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Cruises Furniture',
          'description': 'Walks while holding onto furniture',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Pincer Grasp',
          'description': 'Picks up small objects with thumb and finger',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Claps Hands',
          'description': 'Claps hands together',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        // Language & Communication
        {
          'title': 'First Real Words',
          'description': 'Says first meaningful words',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Understands "No"',
          'description': 'Responds to simple commands',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Points to Objects',
          'description': 'Points to desired objects',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        // Social & Emotional
        {
          'title': 'Smiles socially',
          'description': 'Smiles in response to other people smiling',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys play with others',
          'description': 'Shows interest in games with others',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows stranger anxiety',
          'description': 'Wary of unfamiliar people',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Waves or claps',
          'description': 'Waves goodbye or claps in excitement',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows affection',
          'description': 'Offers hugs or kisses to familiar people',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Parallel play',
          'description': 'Plays alongside other children without interaction',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Takes turns',
          'description': 'Engages in simple turn-taking games',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Initiates interaction',
          'description': 'Starts play with adults or peers',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Waves Bye-Bye',
          'description': 'Waves goodbye',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Separation Anxiety',
          'description': 'Shows distress when separated from parents',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Feeding Milestones
        {
          'title': 'Self-Feeding',
          'description': 'Attempts to feed self with spoon',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Variety of Foods',
          'description': 'Eats a variety of finger foods',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        // Play Milestones
        {
          'title': 'Calms with caregiver',
          'description': 'Calms down when soothed by a caregiver',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Laughs',
          'description': 'Copies simple actions and gestures',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Cause-Effect Toys',
          'description': 'Plays with toys that make sounds or move when touched',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Stacks and Knocks Down',
          'description': 'Enjoys building and destroying block towers',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Plays Pat-a-Cake',
          'description': 'Participates in clapping games',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Ball Play',
          'description': 'Rolls ball back and forth with others',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Uses Objects as Tools',
          'description': 'Uses one object to get another (like using spoon to get toy)',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
        // Cognitive Milestones
        {
          'title': 'Finds Hidden Objects',
          'description': 'Looks for toys hidden under blankets',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Imitates Sounds',
          'description': 'Copies sounds and gestures made by others',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Explores with Mouth',
          'description': 'Puts objects in mouth to explore them',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Shows Interest in Mirror',
          'description': 'Reaches for and interacts with reflection',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        // Emotional Milestones
        {
          'title': 'Shows Excitement',
          'description': 'Displays joy and excitement appropriately',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Comforts Self',
          'description': 'Uses self-soothing techniques when upset',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Sensory Milestones
        {
          'title': 'Explores Textures',
          'description': 'Shows interest in different textures and materials',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Responds to Music',
          'description': 'Moves body or shows interest in musical sounds',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Visual Tracking',
          'description': 'Follows moving objects with eyes smoothly',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
      ]);
    }
    
    // 13-18 months (Early Toddler)
    if (ageInMonths >= 13 && ageInMonths <= 18) {
      templates.addAll([
        // Motor Skills
        {
          'title': 'Walks Independently',
          'description': 'Walks without support',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Climbs Stairs',
          'description': 'Crawls up stairs with help',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Stacks Blocks',
          'description': 'Stacks 2-3 blocks',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Scribbles',
          'description': 'Makes marks with crayons',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        // Language
        {
          'title': 'Says 10+ Words',
          'description': 'Has vocabulary of 10 or more words',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Follows Simple Commands',
          'description': 'Follows one-step instructions',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        // Social & Play
        {
          'title': 'Plays Alongside Others',
          'description': 'Engages in parallel play',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows Affection',
          'description': 'Hugs and kisses familiar people',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Simple Puzzles',
          'description': 'Attempts to fit shapes into matching holes',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Plays with Push Toys',
          'description': 'Enjoys toys that can be pushed while walking',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Nests Objects',
          'description': 'Puts smaller objects inside larger ones',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Enjoys Water Play',
          'description': 'Shows interest in splashing and water activities',
          'category': MilestoneCategory.play,
          'type': MilestoneType.sensory_development,
        },
        // Cognitive Development
        {
          'title': 'Matches Similar Objects',
          'description': 'Groups similar toys or objects together',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Shows Symbolic Play',
          'description': 'Pretends to feed doll or talk on toy phone',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Remembers Routines',
          'description': 'Anticipates steps in familiar activities',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        // Emotional Development
        {
          'title': 'Shows Empathy',
          'description': 'Responds to others\'s emotions with concern',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Expresses Preferences',
          'description': 'Shows clear likes and dislikes',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Sensory Development
        {
          'title': 'Discriminates Sounds',
          'description': 'Reacts differently to various sounds and voices',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Enjoys Sensory Bins',
          'description': 'Explores rice, beans, or other sensory materials',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        // Adaptive Skills
        {
          'title': 'Removes Clothing',
          'description': 'Can take off hat, socks, or shoes',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Washes Hands',
          'description': 'Attempts to wash hands with help',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        // Feeding
        {
          'title': 'Uses Spoon/Fork',
          'description': 'Attempts to use utensils',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Drinks from Cup',
          'description': 'Drinks from regular cup with help',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // 19-24 months (Late Toddler)
    if (ageInMonths >= 19 && ageInMonths <= 24) {
      templates.addAll([
        // Motor Skills
        {
          'title': 'Runs',
          'description': 'Runs with coordination',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Kicks Ball',
          'description': 'Kicks a ball forward',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Builds Tower',
          'description': 'Stacks 4-6 blocks',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Turns Pages',
          'description': 'Turns pages in a book',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        // Language
        {
          'title': 'Says 50+ Words',
          'description': 'Has vocabulary of 50 or more words',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Two-Word Phrases',
          'description': 'Combines two words together',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        // Social & Emotional
        {
          'title': 'Temper Tantrums',
          'description': 'Shows frustration through tantrums',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Pretend Play',
          'description': 'Engages in simple pretend play',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Art Activities',
          'description': 'Shows interest in crayons, finger painting, and drawing',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Plays with Dolls/Stuffed Animals',
          'description': 'Engages in nurturing play with toys',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Sandbox Play',
          'description': 'Shows interest in digging and building in sand',
          'category': MilestoneCategory.play,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Imitates Household Activities',
          'description': 'Pretends to cook, clean, or do adult activities',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Plays Simple Games',
          'description': 'Enjoys games like hide and seek or chase',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        // Cognitive Development
        {
          'title': 'Sorts by Color/Shape',
          'description': 'Groups objects by basic attributes',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Completes Simple Puzzles',
          'description': 'Finishes 3-4 piece puzzles',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Understands Big/Small',
          'description': 'Recognizes size differences in objects',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Shows Imagination',
          'description': 'Creates stories or scenarios during play',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        // Emotional Development
        {
          'title': 'Shows Pride',
          'description': 'Demonstrates pride in accomplishments',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Seeks Comfort',
          'description': 'Comes to caregivers when hurt or scared',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows Jealousy',
          'description': 'Displays jealousy when attention goes to others',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Sensory Development
        {
          'title': 'Enjoys Textured Foods',
          'description': 'Accepts variety of food textures',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Dances to Music',
          'description': 'Moves body rhythmically to music',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Notices Details',
          'description': 'Points out small details in books or environment',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        // Adaptive Skills
        {
          'title': 'Shows frustration',
          'description': 'Expresses frustration through sounds or gestures',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Seeks comfort',
          'description': 'Seeks comfort from familiar people when upset',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Displays pride',
          'description': 'Shows pride in achievements',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Recognizes emotions',
          'description': 'Shows understanding of others\' emotions',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows empathy',
          'description': 'Displays concern for others\' distress',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Manages brief separation',
          'description': 'Handles short periods away from caregivers',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Helps with Chores',
          'description': 'Assists with simple household tasks',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Follows Multi-Step Directions',
          'description': 'Completes 2-3 step instructions',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Washes Face',
          'description': 'Attempts to wash face with washcloth',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        // Health & Adaptive
        {
          'title': 'Toilet Training Interest',
          'description': 'Shows interest in potty training',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Brushes Teeth',
          'description': 'Attempts to brush teeth with help',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // 25-36 months (Preschooler)
    if (ageInMonths >= 25 && ageInMonths <= 36) {
      templates.addAll([
        // Motor Skills
        {
          'title': 'Jumps',
          'description': 'Jumps with both feet off ground',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Pedals Tricycle',
          'description': 'Pedals a tricycle',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.gross_motor,
        },
        {
          'title': 'Draws Circles',
          'description': 'Draws recognizable circles',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        {
          'title': 'Uses Scissors',
          'description': 'Cuts paper with scissors',
          'category': MilestoneCategory.motor,
          'type': MilestoneType.fine_motor,
        },
        // Language
        {
          'title': 'Speaks in Sentences',
          'description': 'Uses 3-4 word sentences',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        {
          'title': 'Asks Questions',
          'description': 'Asks "why" and "what" questions',
          'category': MilestoneCategory.language,
          'type': MilestoneType.communication,
        },
        // Social & Play
        {
          'title': 'Sleeps 14–17 hours/day',
          'description': 'Sleeps between 14 and 17 hours a day, including naps',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Sleeps longer at night',
          'description': 'Stretches of sleep lasting longer during the night',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Naps 2–3 times/day',
          'description': 'Takes 2 to 3 naps during daytime',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Sleeps through night',
          'description': 'Sleeps through the whole night without waking',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Transitions to 1 nap',
          'description': 'Reduces daytime sleep to 1 nap',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Self-soothes',
          'description': 'Calms self back to sleep if awake',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Moves to toddler bed',
          'description': 'Transitions from crib to toddle bed',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Consistent bedtime routine',
          'description': 'Follows a consistent sleep routine',
          'category': MilestoneCategory.sleep,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Cooperative Play',
          'description': 'Plays cooperatively with others',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shares Toys',
          'description': 'Shares toys with others (with encouragement)',
          'category': MilestoneCategory.social,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Watches faces',
          'description': 'Observes faces intently to learn expressions',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Bats at toys',
          'description': 'Swats at toys to create movement',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Bangs objects',
          'description': 'Bangs two objects together in play',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Pushes/pulls toys',
          'description': 'Pushes or pulls toys during play',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Engages in pretend play',
          'description': 'Engages in imaginative play and storytelling',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Builds block tower',
          'description': 'Builds towers using blocks',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Plays with dolls',
          'description': 'Plays with dolls/soft toys in nurturing play',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys interactive games',
          'description': 'Participates in games like peek-a-boo or hide-and-seek',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Playground Activities',
          'description': 'Shows interest in swings, slides, and climbing',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Role Playing',
          'description': 'Pretends to be different characters or animals',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Steady weight gain',
          'description': 'Maintains steady growth on growth chart',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'First tooth erupts',
          'description': 'First tooth begins to emerge',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Up-to-date immunizations',
          'description': 'Receives immunizations on time',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Regular bowel movements',
          'description': 'Has regular and healthy bowel movements',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Few colds',
          'description': 'Experiences few illnesses',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Tracks growth percentile',
          'description': 'Growth follows expected percentiles',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Doctor wellness checks',
          'description': 'Attends regular check-ups with pediatrician',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Begins brushing teeth',
          'description': 'Starts brushing teeth with assistance',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Builds Complex Structures',
          'description': 'Creates elaborate buildings with blocks or toys',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Engages in Fantasy Play',
          'description': 'Creates imaginary scenarios and stories',
          'category': MilestoneCategory.play,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Plays Board Games',
          'description': 'Participates in simple board games with rules',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Enjoys Nature Play',
          'description': 'Shows interest in outdoor exploration and nature',
          'category': MilestoneCategory.play,
          'type': MilestoneType.sensory_development,
        },
        // Cognitive Development
        {
          'title': 'Counts Objects',
          'description': 'Counts to 3 or higher with objects',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Recognizes Colors',
          'description': 'Identifies basic colors correctly',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Understands Time Concepts',
          'description': 'Grasps "before", "after", "now", "later"',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Remembers Stories',
          'description': 'Recalls details from favorite books or stories',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Problem Solves',
          'description': 'Figures out how to overcome simple obstacles',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        // Emotional Development
        {
          'title': 'Shows Self-Control',
          'description': 'Can wait turn and control impulses better',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Expresses Emotions',
          'description': 'Uses words to express feelings',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Shows Independence',
          'description': 'Attempts tasks without help',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Comforts Others',
          'description': 'Shows concern and tries to help when others are upset',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        // Sensory Development
        {
          'title': 'Responds to sound',
          'description': 'Turns head toward sounds',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Enjoys textures',
          'description': 'Shows interest in different textures',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Turns to familiar voice',
          'description': 'Recognizes and turns towards familiar voices',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Dislikes loud noise',
          'description': 'Shows discomfort with loud sounds',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Explores new tastes',
          'description': 'Shows interest in a variety of food tastes',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Engages in messy play',
          'description': 'Enjoys play with sand, water, or mud',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Adjusts to light/noise',
          'description': 'Adapts to different lighting and noise levels',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Prefers certain stimuli',
          'description': 'Shows preference for certain images or sounds',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Enjoys Sensory Activities',
          'description': 'Engages with play dough, finger paints, textures',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Discriminates Tastes',
          'description': 'Recognizes sweet, sour, salty flavors',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Balances Activities',
          'description': 'Walks on balance beam or line',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        // Self-Care
        {
          'title': 'Holds bottle',
          'description': 'Holds and drinks from a bottle independently',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Feeds self with fingers',
          'description': 'Eats finger foods without assistance',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Drinks from cup',
          'description': 'Uses a cup to drink with some guidance',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Uses spoon',
          'description': 'Uses a spoon with some spilling',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Removes clothing',
          'description': 'Takes off simple clothing independently',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Tries to dress',
          'description': 'Attempts to dress self with assistance',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Washes hands with help',
          'description': 'Washes hands with caregiver assistance',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Begins toilet training',
          'description': 'Shows interest in using a potty',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Dresses Self',
          'description': 'Puts on simple clothing',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Breastfeeds/formula',
          'description': 'Feeds using breast or formula milk',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Starts solids',
          'description': 'Begins eating solid foods',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Eats mashed food',
          'description': 'Eats mashed or pureed foods',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Eats finger food',
          'description': 'Picks up and eats small pieces of food',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Drinks from sippy cup',
          'description': 'Uses a sippy cup to drink liquids',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Feeds self',
          'description': 'Feeds self with minimal spillage',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Chews all textures',
          'description': 'Eats food of various textures',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Uses straw',
          'description': 'Drinks liquids using a straw',
          'category': MilestoneCategory.feeding,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Potty Trained',
          'description': 'Uses potty independently (daytime)',
          'category': MilestoneCategory.health,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // Add additional templates for categories that might not be well-covered
    // More Play milestones for various ages
    if (ageInMonths >= 1 && ageInMonths <= 6) {
      templates.addAll([
        {
          'title': 'Enjoys Being Sung To',
          'description': 'Shows pleasure when caregivers sing lullabies',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Responds to Silly Faces',
          'description': 'Shows interest or amusement at funny expressions',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
      ]);
    }
    
    if (ageInMonths >= 7 && ageInMonths <= 12) {
      templates.addAll([
        {
          'title': 'Enjoys Drop and Fetch',
          'description': 'Drops objects repeatedly for others to pick up',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Explores Containers',
          'description': 'Puts objects in and out of containers',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
      ]);
    }
    
    if (ageInMonths >= 13 && ageInMonths <= 24) {
      templates.addAll([
        {
          'title': 'Enjoys Movement Songs',
          'description': 'Participates in songs with actions',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Plays with Vehicles',
          'description': 'Pushes cars and trucks around making sounds',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
      ]);
    }
    
    if (ageInMonths >= 25 && ageInMonths <= 36) {
      templates.addAll([
        {
          'title': 'Enjoys Dress-Up',
          'description': 'Likes to wear costumes and pretend to be different characters',
          'category': MilestoneCategory.play,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Plays Memory Games',
          'description': 'Enjoys simple memory and matching games',
          'category': MilestoneCategory.play,
          'type': MilestoneType.problem_solving,
        },
      ]);
    }
    
    // More Sensory milestones for various ages
    if (ageInMonths >= 1 && ageInMonths <= 12) {
      templates.addAll([
        {
          'title': 'Startles at Loud Sounds',
          'description': 'Responds to sudden loud noises',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Enjoys Different Textures',
          'description': 'Explores toys with different textures',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Prefers Certain Sounds',
          'description': 'Shows preference for specific music or sounds',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Enjoys Gentle Touch',
          'description': 'Responds positively to gentle massage or stroking',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
      ]);
    }
    
    if (ageInMonths >= 13 && ageInMonths <= 36) {
      templates.addAll([
        {
          'title': 'Enjoys Swinging',
          'description': 'Shows pleasure in swinging motion',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
        {
          'title': 'Tolerates Hair Brushing',
          'description': 'Accepts hair brushing and grooming activities',
          'category': MilestoneCategory.sensory,
          'type': MilestoneType.sensory_development,
        },
      ]);
    }
    
    // More Cognitive milestones for various ages
    if (ageInMonths >= 4 && ageInMonths <= 24) {
      templates.addAll([
        {
          'title': 'Object Permanence',
          'description': 'Looks for hidden objects',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Cause and Effect',
          'description': 'Understands cause and effect relationships',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.problem_solving,
        },
        {
          'title': 'Anticipates Events',
          'description': 'Shows excitement before familiar activities',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
      ]);
    }
    
    if (ageInMonths >= 25 && ageInMonths <= 36) {
      templates.addAll([
        {
          'title': 'Understands Opposites',
          'description': 'Recognizes concepts like hot/cold, big/small',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
        {
          'title': 'Follows Story Sequence',
          'description': 'Understands beginning, middle, and end of stories',
          'category': MilestoneCategory.cognitive,
          'type': MilestoneType.cognitive_development,
        },
      ]);
    }
    
    // More Adaptive milestones for various ages
    if (ageInMonths >= 6 && ageInMonths <= 36) {
      templates.addAll([
        {
          'title': 'Imitates Actions',
          'description': 'Copies simple actions of others',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Helps with Dressing',
          'description': 'Assists when being dressed',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    if (ageInMonths >= 13 && ageInMonths <= 36) {
      templates.addAll([
        {
          'title': 'Indicates Wet Diaper',
          'description': 'Shows awareness of wet or soiled diaper',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
        {
          'title': 'Participates in Cleanup',
          'description': 'Helps put toys away when asked',
          'category': MilestoneCategory.adaptive,
          'type': MilestoneType.adaptive_behavior,
        },
      ]);
    }
    
    // More Emotional milestones for various ages
    if (ageInMonths >= 1 && ageInMonths <= 12) {
      templates.addAll([
        {
          'title': 'Shows Trust',
          'description': 'Relaxes with familiar caregivers',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Expresses Discomfort',
          'description': 'Cries or fusses when hungry, tired, or uncomfortable',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
      ]);
    }
    
    if (ageInMonths >= 13 && ageInMonths <= 36) {
      templates.addAll([
        {
          'title': 'Shows Defiance',
          'description': 'Says "no" and shows independent streak',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
        {
          'title': 'Seeks Approval',
          'description': 'Looks for praise after accomplishing tasks',
          'category': MilestoneCategory.emotional,
          'type': MilestoneType.personal_social,
        },
      ]);
    }
    
    // Always add Custom Milestone option
    templates.add({
      'title': 'Custom Milestone',
      'description': 'Create your own milestone',
      'category': MilestoneCategory.custom,
      'type': MilestoneType.adaptive_behavior,
    });
    
    // Remove duplicates by title to avoid dropdown errors
    final uniqueTitles = <String>{};
    final uniqueTemplates = <Map<String, dynamic>>[];
    
    for (final template in templates) {
      final title = template['title'] as String;
      if (!uniqueTitles.contains(title)) {
        uniqueTitles.add(title);
        uniqueTemplates.add(template);
      }
    }
    
    return uniqueTemplates;
  }
}
