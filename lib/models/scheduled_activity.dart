import 'package:flutter/foundation.dart';

/// Enum for scheduled activity types
enum ScheduledActivityType {
  sleepReminder('sleep_reminder', 'Sleep Reminder'),
  feedingReminder('feeding_reminder', 'Feeding Reminder'),
  medicationReminder('medication_reminder', 'Medication Reminder'),
  doctorAppointment('doctor_appointment', 'Doctor Appointment'),
  vaccinationAppointment('vaccination_appointment', 'Vaccination Appointment'),
  diaperChangeReminder('diaper_change_reminder', 'Diaper Change Reminder'),
  bathTime('bath_time', 'Bath Time'),
  tummyTime('tummy_time', 'Tummy Time'),
  playTime('play_time', 'Play Time'),
  shoppingTrip('shopping_trip', 'Shopping Trip'),
  napTime('nap_time', 'Nap Time'),
  mealTime('meal_time', 'Meal Time'),
  walkTime('walk_time', 'Walk Time'),
  nursingSession('nursing_session', 'Nursing Session'),
  bottleFeeding('bottle_feeding', 'Bottle Feeding'),
  customReminder('custom_reminder', 'Custom Reminder');

  const ScheduledActivityType(this.value, this.displayName);
  final String value;
  final String displayName;

  static ScheduledActivityType fromString(String value) {
    return ScheduledActivityType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => ScheduledActivityType.customReminder,
    );
  }
}

/// Enum for recurrence patterns
enum RecurrencePattern {
  none('none', 'No Repeat'),
  daily('daily', 'Daily'),
  weekly('weekly', 'Weekly'),
  monthly('monthly', 'Monthly'),
  custom('custom', 'Custom');

  const RecurrencePattern(this.value, this.displayName);
  final String value;
  final String displayName;

  static RecurrencePattern fromString(String value) {
    return RecurrencePattern.values.firstWhere(
      (pattern) => pattern.value == value,
      orElse: () => RecurrencePattern.none,
    );
  }
}

/// Model class for scheduled activities
class ScheduledActivity {
  final String id;
  final String babyId;
  final ScheduledActivityType type;
  final String title;
  final String? description;
  final DateTime scheduledTime;
  final int notifyBeforeMinutes;
  final bool isRecurring;
  final RecurrencePattern recurrencePattern;
  final int recurrenceInterval;
  final DateTime? recurrenceEndDate;
  final bool isActive;
  final bool isCompleted;
  final DateTime? completedAt;
  final DateTime createdAt;
  final DateTime updatedAt;

  ScheduledActivity({
    required this.id,
    required this.babyId,
    required this.type,
    required this.title,
    this.description,
    required this.scheduledTime,
    this.notifyBeforeMinutes = 10,
    this.isRecurring = false,
    this.recurrencePattern = RecurrencePattern.none,
    this.recurrenceInterval = 1,
    this.recurrenceEndDate,
    this.isActive = true,
    this.isCompleted = false,
    this.completedAt,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create a ScheduledActivity from JSON
  factory ScheduledActivity.fromJson(Map<String, dynamic> json) {
    // Add null safety checks for required fields
    final id = json['id'];
    final babyId = json['baby_id'];
    // Handle both 'type' and 'activity_type' field names
    final type = json['type'] ?? json['activity_type'];
    final title = json['title'];
    final scheduledTime = json['scheduled_time'];
    final createdAt = json['created_at'];
    final updatedAt = json['updated_at'];
    
    if (id == null || babyId == null || type == null || title == null || 
        scheduledTime == null || createdAt == null || updatedAt == null) {
      throw Exception('Missing required fields in ScheduledActivity JSON: $json');
    }
    
    // Handle both 'notify_before_minutes' and 'reminder_minutes' field names
    final notifyBeforeMinutes = json['notify_before_minutes'] ?? json['reminder_minutes'] ?? 10;
    
    return ScheduledActivity(
      id: id as String,
      babyId: babyId as String,
      type: ScheduledActivityType.fromString(type as String),
      title: title as String,
      description: json['description'] as String?,
      scheduledTime: _parseTimestampSafely(scheduledTime as String),
      notifyBeforeMinutes: notifyBeforeMinutes as int,
      isRecurring: json['is_recurring'] as bool? ?? false,
      recurrencePattern: RecurrencePattern.fromString(json['recurrence_pattern'] as String? ?? 'none'),
      recurrenceInterval: json['recurrence_interval'] as int? ?? 1,
      recurrenceEndDate: json['recurrence_end_date'] != null
          ? _parseTimestampSafely(json['recurrence_end_date'] as String)
          : null,
      isActive: json['is_active'] as bool? ?? true,
      isCompleted: json['is_completed'] as bool? ?? false,
      completedAt: json['completed_at'] != null
          ? _parseTimestampSafely(json['completed_at'] as String)
          : null,
      createdAt: _parseTimestampSafely(createdAt as String),
      updatedAt: _parseTimestampSafely(updatedAt as String),
    );
  }

  /// Convert ScheduledActivity to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'baby_id': babyId,
      'type': type.value,
      'title': title,
      'description': description,
      'scheduled_time': _formatTimestampForStorage(scheduledTime),
      'notify_before_minutes': notifyBeforeMinutes,
      'is_recurring': isRecurring,
      'recurrence_pattern': recurrencePattern.value,
      'recurrence_interval': recurrenceInterval,
      'recurrence_end_date': recurrenceEndDate != null ? _formatTimestampForStorage(recurrenceEndDate!) : null,
      'is_active': isActive,
      'is_completed': isCompleted,
      'completed_at': completedAt != null ? _formatTimestampForStorage(completedAt!) : null,
      'created_at': _formatTimestampForStorage(createdAt),
      'updated_at': _formatTimestampForStorage(updatedAt),
    };
  }

  /// Create a copy of this ScheduledActivity with updated values
  ScheduledActivity copyWith({
    String? id,
    String? babyId,
    ScheduledActivityType? type,
    String? title,
    String? description,
    DateTime? scheduledTime,
    int? notifyBeforeMinutes,
    bool? isRecurring,
    RecurrencePattern? recurrencePattern,
    int? recurrenceInterval,
    DateTime? recurrenceEndDate,
    bool? isActive,
    bool? isCompleted,
    DateTime? completedAt,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ScheduledActivity(
      id: id ?? this.id,
      babyId: babyId ?? this.babyId,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      notifyBeforeMinutes: notifyBeforeMinutes ?? this.notifyBeforeMinutes,
      isRecurring: isRecurring ?? this.isRecurring,
      recurrencePattern: recurrencePattern ?? this.recurrencePattern,
      recurrenceInterval: recurrenceInterval ?? this.recurrenceInterval,
      recurrenceEndDate: recurrenceEndDate ?? this.recurrenceEndDate,
      isActive: isActive ?? this.isActive,
      isCompleted: isCompleted ?? this.isCompleted,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get the next occurrence of this scheduled activity (for recurring activities)
  DateTime? getNextOccurrence() {
    if (!isRecurring || isCompleted) return null;

    final now = DateTime.now();
    var nextTime = scheduledTime;

    // If the scheduled time is in the past, calculate the next occurrence
    if (nextTime.isBefore(now)) {
      switch (recurrencePattern) {
        case RecurrencePattern.daily:
          final daysDifference = now.difference(nextTime).inDays;
          final occurrences = (daysDifference / recurrenceInterval).ceil();
          nextTime = nextTime.add(Duration(days: occurrences * recurrenceInterval));
          break;
        case RecurrencePattern.weekly:
          final daysDifference = now.difference(nextTime).inDays;
          final weeksDifference = (daysDifference / 7).ceil();
          final occurrences = (weeksDifference / recurrenceInterval).ceil();
          nextTime = nextTime.add(Duration(days: occurrences * recurrenceInterval * 7));
          break;
        case RecurrencePattern.monthly:
          while (nextTime.isBefore(now)) {
            nextTime = DateTime(
              nextTime.year,
              nextTime.month + recurrenceInterval,
              nextTime.day,
              nextTime.hour,
              nextTime.minute,
            );
          }
          break;
        case RecurrencePattern.none:
        case RecurrencePattern.custom:
          return null;
      }
    }

    // Check if the next occurrence is past the end date
    if (recurrenceEndDate != null && nextTime.isAfter(recurrenceEndDate!)) {
      return null;
    }

    return nextTime;
  }

  /// Check if this scheduled activity is due for notification
  bool shouldNotify() {
    if (isCompleted || !isActive) return false;
    
    final now = DateTime.now();
    final nextOccurrence = getNextOccurrence() ?? scheduledTime;
    final notificationTime = nextOccurrence.subtract(Duration(minutes: notifyBeforeMinutes));
    
    return now.isAfter(notificationTime) && now.isBefore(nextOccurrence);
  }

  /// Get a formatted string for the recurrence pattern
  String get recurrenceDescription {
    if (!isRecurring) return 'No repeat';
    
    switch (recurrencePattern) {
      case RecurrencePattern.daily:
        return recurrenceInterval == 1 ? 'Daily' : 'Every $recurrenceInterval days';
      case RecurrencePattern.weekly:
        return recurrenceInterval == 1 ? 'Weekly' : 'Every $recurrenceInterval weeks';
      case RecurrencePattern.monthly:
        return recurrenceInterval == 1 ? 'Monthly' : 'Every $recurrenceInterval months';
      case RecurrencePattern.custom:
        return 'Custom';
      case RecurrencePattern.none:
        return 'No repeat';
    }
  }

  /// Get the icon name for this scheduled activity type
  /// Uses the same icons as Quick Log activities for consistency
  String get iconName {
    switch (type) {
      case ScheduledActivityType.sleepReminder:
        return 'bedtime'; // Same as 'sleep' activity
      case ScheduledActivityType.feedingReminder:
        return 'restaurant'; // Same as 'feeding' activity
      case ScheduledActivityType.medicationReminder:
        return 'medication'; // Same as 'medicine' activity
      case ScheduledActivityType.doctorAppointment:
        return 'health_and_safety'; // Use health icon for consistency
      case ScheduledActivityType.vaccinationAppointment:
        return 'vaccines'; // Same as 'vaccination' activity
      case ScheduledActivityType.diaperChangeReminder:
        return 'child_care'; // Same as 'diaper' activity
      case ScheduledActivityType.bathTime:
        return 'child_care'; // Map to general care icon for consistency
      case ScheduledActivityType.tummyTime:
        return 'fitness_center'; // Same as 'tummy_time' activity
      case ScheduledActivityType.playTime:
        return 'toys'; // Same as 'indoor_play' activity
      case ScheduledActivityType.shoppingTrip:
        return 'shopping_cart'; // Shopping icon
      case ScheduledActivityType.napTime:
        return 'bedtime'; // Same as sleep
      case ScheduledActivityType.mealTime:
        return 'restaurant'; // Same as feeding
      case ScheduledActivityType.walkTime:
        return 'directions_walk'; // Walking icon
      case ScheduledActivityType.nursingSession:
        return 'child_care'; // Nursing icon
      case ScheduledActivityType.bottleFeeding:
        return 'baby_changing_station'; // Bottle icon
      case ScheduledActivityType.customReminder:
        return 'add_circle'; // Same as 'custom' activity
    }
  }

  /// Get the corresponding Quick Log activity type for this scheduled activity
  /// This helps maintain consistency between scheduled activities and quick log activities
  String get correspondingQuickLogActivityType {
    switch (type) {
      case ScheduledActivityType.sleepReminder:
        return 'sleep';
      case ScheduledActivityType.feedingReminder:
        return 'feeding';
      case ScheduledActivityType.medicationReminder:
        return 'medicine';
      case ScheduledActivityType.doctorAppointment:
        return 'doctor_appointment'; // Use doctor_appointment for consistency
      case ScheduledActivityType.vaccinationAppointment:
        return 'vaccination';
      case ScheduledActivityType.diaperChangeReminder:
        return 'diaper';
      case ScheduledActivityType.bathTime:
        return 'bath'; // Use bath activity if available, otherwise custom
      case ScheduledActivityType.tummyTime:
        return 'tummy_time';
      case ScheduledActivityType.playTime:
        return 'indoor_play'; // Default to indoor play
      case ScheduledActivityType.shoppingTrip:
        return 'shopping_trip'; // Use shopping_trip for consistency
      case ScheduledActivityType.napTime:
        return 'sleep'; // Same as sleep
      case ScheduledActivityType.mealTime:
        return 'feeding'; // Same as feeding
      case ScheduledActivityType.walkTime:
        return 'outdoor_play'; // Similar to outdoor activity
      case ScheduledActivityType.nursingSession:
        return 'feeding'; // Similar to feeding
      case ScheduledActivityType.bottleFeeding:
        return 'feeding'; // Same as feeding
      case ScheduledActivityType.customReminder:
        return 'custom';
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ScheduledActivity &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  /// Safely parse timestamp string, converting to local time
  /// This app uses local time only - all timestamps are converted to device local time
  static DateTime _parseTimestampSafely(String timestampStr) {
    try {
      debugPrint('Parsing timestamp as local time: $timestampStr');
      
      // Remove timezone markers and parse as local time
      String localTimeStr = timestampStr
          .replaceAll('Z', '')
          .replaceAll('+00:00', '')
          .replaceAll('+00', '')
          .replaceAll('T', ' ');
      
      final localTime = DateTime.parse(localTimeStr);
      debugPrint('Parsed as local time: $localTime');
      return localTime;
    } catch (e) {
      debugPrint('Error parsing timestamp $timestampStr: $e');
      return DateTime.now();
    }
  }

  /// Format timestamp for storage - store as local time without UTC markers
  static String _formatTimestampForStorage(DateTime dateTime) {
    // Store as local time string without timezone markers
    final localTimeStr = dateTime.toString().split('.')[0];
    debugPrint('Formatting timestamp for storage: $localTimeStr');
    return localTimeStr;
  }

  @override
  String toString() {
    return 'ScheduledActivity{id: $id, title: $title, type: ${type.displayName}, scheduledTime: $scheduledTime, isRecurring: $isRecurring}';
  }
}
