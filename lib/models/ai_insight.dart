enum InsightType {
  sleep,
  feeding,
  growth,
  development,
  health,
  behavior,
  recommendation,
}

enum InsightPriority {
  low,
  medium,
  high,
}

class AIInsight {
  final String id;
  final String babyId;
  final String title;
  final String description;
  final List<String> recommendations;
  final double confidence;
  final InsightType type;
  final InsightPriority priority;
  final DateTime generatedAt;
  final bool isRead;
  final bool isArchived;
  final Map<String, dynamic>? data;

  AIInsight({
    required this.id,
    required this.babyId,
    required this.title,
    required this.description,
    required this.recommendations,
    required this.confidence,
    required this.type,
    required this.priority,
    required this.generatedAt,
    this.isRead = false,
    this.isArchived = false,
    this.data,
  });

  factory AIInsight.fromJson(Map<String, dynamic> json) {
    return AIInsight(
      id: json['id'] as String,
      babyId: json['baby_id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      recommendations: List<String>.from(json['recommendations'] ?? []),
      confidence: (json['confidence'] as num).toDouble(),
      type: InsightType.values.firstWhere(
        (t) => t.name == (json['type'] as String).toLowerCase(),
        orElse: () => InsightType.recommendation,
      ),
      priority: InsightPriority.values.firstWhere(
        (p) => p.name == (json['priority'] as String).toLowerCase(),
        orElse: () => InsightPriority.medium,
      ),
      generatedAt: DateTime.parse(json['generated_at'] as String),
      isRead: json['is_read'] as bool? ?? false,
      isArchived: json['is_archived'] as bool? ?? false,
      data: json['data'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'baby_id': babyId,
      'title': title,
      'description': description,
      'recommendations': recommendations,
      'confidence': confidence,
      'type': type.name,
      'priority': priority.name,
      'generated_at': generatedAt.toIso8601String(),
      'is_read': isRead,
      'is_archived': isArchived,
      if (data != null) 'data': data,
    };
  }

  AIInsight copyWith({
    String? id,
    String? babyId,
    String? title,
    String? description,
    List<String>? recommendations,
    double? confidence,
    InsightType? type,
    InsightPriority? priority,
    DateTime? generatedAt,
    bool? isRead,
    bool? isArchived,
    Map<String, dynamic>? data,
  }) {
    return AIInsight(
      id: id ?? this.id,
      babyId: babyId ?? this.babyId,
      title: title ?? this.title,
      description: description ?? this.description,
      recommendations: recommendations ?? this.recommendations,
      confidence: confidence ?? this.confidence,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      generatedAt: generatedAt ?? this.generatedAt,
      isRead: isRead ?? this.isRead,
      isArchived: isArchived ?? this.isArchived,
      data: data ?? this.data,
    );
  }

  static AIInsight fromOpenAIResponse({
    required String babyId,
    required Map<String, dynamic> analysisData,
    required InsightType type,
  }) {
    final data = analysisData['data'] as Map<String, dynamic>;
    final recommendations = List<String>.from(data['recommendations'] ?? []);
    
    return AIInsight(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      babyId: babyId,
      title: analysisData['title'] as String,
      description: analysisData['description'] as String,
      recommendations: recommendations,
      confidence: (analysisData['confidence'] as num).toDouble(),
      type: type,
      priority: InsightPriority.medium,
      generatedAt: DateTime.now(),
      data: data,
    );
  }
}
