class UserProfile {
  final String id;
  final String email;
  final String fullName;
  final String? avatarUrl;
  final String role;
  final String? provider;
  final String? providerId;
  final DateTime? lastSignInAt;
  final int signInCount;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Enhanced fields for profile completion
  final String? phoneNumber;
  final String? timezone;
  final Map<String, dynamic> preferences;
  final bool isEmailVerified;
  final bool isTwoFactorEnabled;
  final Map<String, bool> permissions;

  UserProfile({
    required this.id,
    required this.email,
    required this.fullName,
    this.avatarUrl,
    required this.role,
    this.provider,
    this.providerId,
    this.lastSignInAt,
    required this.signInCount,
    required this.createdAt,
    required this.updatedAt,
    this.phoneNumber,
    this.timezone,
    this.preferences = const {},
    this.isEmailVerified = false,
    this.isTwoFactorEnabled = false,
    this.permissions = const {},
  });

  Map<String, dynamic> toJson() => {
        'id': id,
        'email': email,
        'full_name': fullName,
        'avatar_url': avatarUrl,
        'role': role,
        'provider': provider,
        'provider_id': providerId,
        'last_sign_in_at': lastSignInAt?.toString(),
        'sign_in_count': signInCount,
        'created_at': createdAt.toString(),
        'updated_at': updatedAt.toString(),
        'phone_number': phoneNumber,
        'timezone': timezone,
        'preferences': preferences,
        'is_email_verified': isEmailVerified,
        'is_two_factor_enabled': isTwoFactorEnabled,
        'permissions': permissions,
      };

  factory UserProfile.fromJson(Map<String, dynamic> json) => UserProfile(
        id: json['id'],
        email: json['email'],
        fullName: json['full_name'],
        avatarUrl: json['avatar_url'],
        role: json['role'] ?? 'parent',
        provider: json['provider'],
        providerId: json['provider_id'],
        lastSignInAt: json['last_sign_in_at'] != null
            ? DateTime.parse(json['last_sign_in_at'])
            : null,
        signInCount: json['sign_in_count'] ?? 0,
        createdAt: DateTime.parse(json['created_at']),
        updatedAt: DateTime.parse(json['updated_at']),
        phoneNumber: json['phone_number'],
        timezone: json['timezone'],
        preferences: Map<String, dynamic>.from(json['preferences'] ?? {}),
        isEmailVerified: json['is_email_verified'] ?? false,
        isTwoFactorEnabled: json['is_two_factor_enabled'] ?? false,
        permissions: Map<String, bool>.from(json['permissions'] ?? {}),
      );

  UserProfile copyWith({
    String? id,
    String? email,
    String? fullName,
    String? avatarUrl,
    String? role,
    String? provider,
    String? providerId,
    DateTime? lastSignInAt,
    int? signInCount,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? phoneNumber,
    String? timezone,
    Map<String, dynamic>? preferences,
    bool? isEmailVerified,
    bool? isTwoFactorEnabled,
    Map<String, bool>? permissions,
  }) =>
      UserProfile(
        id: id ?? this.id,
        email: email ?? this.email,
        fullName: fullName ?? this.fullName,
        avatarUrl: avatarUrl ?? this.avatarUrl,
        role: role ?? this.role,
        provider: provider ?? this.provider,
        providerId: providerId ?? this.providerId,
        lastSignInAt: lastSignInAt ?? this.lastSignInAt,
        signInCount: signInCount ?? this.signInCount,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        phoneNumber: phoneNumber ?? this.phoneNumber,
        timezone: timezone ?? this.timezone,
        preferences: preferences ?? this.preferences,
        isEmailVerified: isEmailVerified ?? this.isEmailVerified,
        isTwoFactorEnabled: isTwoFactorEnabled ?? this.isTwoFactorEnabled,
        permissions: permissions ?? this.permissions,
      );
}
