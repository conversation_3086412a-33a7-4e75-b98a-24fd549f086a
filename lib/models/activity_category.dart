import 'package:flutter/material.dart';

/// Represents an individual activity within a category
class Activity {
  final String type;
  final String label;
  final String icon;
  final String description;

  const Activity({
    required this.type,
    required this.label,
    required this.icon,
    required this.description,
  });

  /// Create from JSON/Map
  factory Activity.fromJson(Map<String, dynamic> json) {
    return Activity(
      type: json['type'] as String,
      label: json['label'] as String,
      icon: json['icon'] as String,
      description: json['description'] as String,
    );
  }

  /// Convert to JSON/Map
  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'label': label,
      'icon': icon,
      'description': description,
    };
  }
}

/// Represents a category of activities
class ActivityCategory {
  final String title;
  final String description;
  final Color color;
  final List<Activity> activities;

  const ActivityCategory({
    required this.title,
    required this.description,
    required this.color,
    required this.activities,
  });

  /// Create from JSON/Map
  factory ActivityCategory.fromJson(Map<String, dynamic> json) {
    final activitiesJson = json['activities'] as List<dynamic>;
    final activities = activitiesJson
        .map((activityJson) => Activity.fromJson(activityJson as Map<String, dynamic>))
        .toList();

    return ActivityCategory(
      title: json['title'] as String,
      description: json['description'] as String,
      color: Color(json['color'] as int),
      activities: activities,
    );
  }

  /// Convert to JSON/Map
  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'description': description,
      'color': color.value,
      'activities': activities.map((activity) => activity.toJson()).toList(),
    };
  }
}