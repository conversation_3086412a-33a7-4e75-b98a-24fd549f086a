import 'package:flutter/foundation.dart';

/// Enum for notification types
enum NotificationType {
  feeding('feeding', 'Feeding Reminder'),
  sleep('sleep', 'Sleep Alert'),
  milestone('milestone', 'Milestone Notification'),
  aiInsight('ai_insight', 'AI Insight Update'),
  dailySummary('daily_summary', 'Daily Summary'),
  weeklyReport('weekly_report', 'Weekly Report'),
  medicine('medicine', 'Medicine Reminder'),
  vaccination('vaccination', 'Vaccination Reminder'),
  appointment('appointment', 'Appointment Reminder'),
  custom('custom', 'Custom Notification');

  const NotificationType(this.value, this.displayName);
  final String value;
  final String displayName;

  static NotificationType fromString(String value) {
    return NotificationType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => NotificationType.custom,
    );
  }
}

/// Enum for notification priority
enum NotificationPriority {
  low('low', 'Low'),
  normal('normal', 'Normal'),
  high('high', 'High'),
  urgent('urgent', 'Urgent');

  const NotificationPriority(this.value, this.displayName);
  final String value;
  final String displayName;

  static NotificationPriority fromString(String value) {
    return NotificationPriority.values.firstWhere(
      (priority) => priority.value == value,
      orElse: () => NotificationPriority.normal,
    );
  }
}

/// Model for individual notification items
class NotificationItem {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final NotificationPriority priority;
  final DateTime createdAt;
  final DateTime? scheduledFor;
  final bool isRead;
  final bool isDelivered;
  final String? babyId;
  final Map<String, dynamic>? metadata;

  const NotificationItem({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.priority = NotificationPriority.normal,
    required this.createdAt,
    this.scheduledFor,
    this.isRead = false,
    this.isDelivered = false,
    this.babyId,
    this.metadata,
  });

  /// Create a copy with updated fields
  NotificationItem copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    NotificationPriority? priority,
    DateTime? createdAt,
    DateTime? scheduledFor,
    bool? isRead,
    bool? isDelivered,
    String? babyId,
    Map<String, dynamic>? metadata,
  }) {
    return NotificationItem(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      createdAt: createdAt ?? this.createdAt,
      scheduledFor: scheduledFor ?? this.scheduledFor,
      isRead: isRead ?? this.isRead,
      isDelivered: isDelivered ?? this.isDelivered,
      babyId: babyId ?? this.babyId,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.value,
      'priority': priority.value,
      'created_at': createdAt.toIso8601String(),
      'scheduled_for': scheduledFor?.toIso8601String(),
      'is_read': isRead,
      'is_delivered': isDelivered,
      'baby_id': babyId,
      'metadata': metadata,
    };
  }

  /// Create from JSON
  factory NotificationItem.fromJson(Map<String, dynamic> json) {
    return NotificationItem(
      id: json['id'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      type: NotificationType.fromString(json['type'] as String),
      priority: NotificationPriority.fromString(json['priority'] as String? ?? 'normal'),
      createdAt: DateTime.parse(json['created_at'] as String),
      scheduledFor: json['scheduled_for'] != null 
          ? DateTime.parse(json['scheduled_for'] as String)
          : null,
      isRead: json['is_read'] as bool? ?? false,
      isDelivered: json['is_delivered'] as bool? ?? false,
      babyId: json['baby_id'] as String?,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  /// Check if notification is overdue
  bool get isOverdue {
    if (scheduledFor == null) return false;
    return DateTime.now().isAfter(scheduledFor!) && !isDelivered;
  }

  /// Check if notification is upcoming (within next hour)
  bool get isUpcoming {
    if (scheduledFor == null) return false;
    final now = DateTime.now();
    final oneHourFromNow = now.add(Duration(hours: 1));
    return scheduledFor!.isAfter(now) && scheduledFor!.isBefore(oneHourFromNow);
  }

  /// Get relative time string
  String get relativeTime {
    final now = DateTime.now();
    final targetTime = scheduledFor ?? createdAt;
    final difference = now.difference(targetTime);

    if (difference.isNegative) {
      // Future time
      final futureDiff = targetTime.difference(now);
      if (futureDiff.inMinutes < 60) {
        return 'in ${futureDiff.inMinutes} minutes';
      } else if (futureDiff.inHours < 24) {
        return 'in ${futureDiff.inHours} hours';
      } else {
        return 'in ${futureDiff.inDays} days';
      }
    } else {
      // Past time
      if (difference.inMinutes < 60) {
        return '${difference.inMinutes} minutes ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours} hours ago';
      } else {
        return '${difference.inDays} days ago';
      }
    }
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is NotificationItem &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'NotificationItem{id: $id, title: $title, type: ${type.value}, priority: ${priority.value}}';
  }
}