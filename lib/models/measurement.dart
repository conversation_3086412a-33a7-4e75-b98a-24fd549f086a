import 'package:flutter/foundation.dart';
import '../services/enhanced_percentile_calculator.dart';
import '../services/who_data_service.dart';

/// Enhanced measurement model with percentile, z-score, and velocity fields
class Measurement {
  final String id;
  final String babyId;
  final String measurementType; // 'weight', 'height', 'head_circumference'
  final double value;
  final String unit;
  final DateTime measuredAt;
  final double ageInMonths;
  final String? notes;
  
  // Enhanced fields for WHO analysis
  final double? percentile;
  final double? zScore;
  final GrowthVelocity? velocityFromPrevious;
  final PercentileResult? percentileAnalysis;
  final bool flaggedForReview;
  final Map<String, dynamic>? validationResults;
  
  // Metadata
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? metadata;

  const Measurement({
    required this.id,
    required this.babyId,
    required this.measurementType,
    required this.value,
    required this.unit,
    required this.measuredAt,
    required this.ageInMonths,
    this.notes,
    this.percentile,
    this.zScore,
    this.velocityFromPrevious,
    this.percentileAnalysis,
    this.flaggedForReview = false,
    this.validationResults,
    required this.createdAt,
    required this.updatedAt,
    this.metadata,
  });

  /// Create measurement from database JSON
  factory Measurement.fromJson(Map<String, dynamic> json) {
    return Measurement(
      id: json['id'] ?? '',
      babyId: json['baby_id'] ?? '',
      measurementType: json['measurement_type'] ?? '',
      value: (json['value'] as num?)?.toDouble() ?? 0.0,
      unit: json['unit'] ?? '',
      measuredAt: DateTime.parse(json['measured_at'] ?? json['measurement_date'] ?? DateTime.now().toIso8601String()),
      ageInMonths: (json['age_in_months'] as num?)?.toDouble() ?? 0.0,
      notes: json['notes'],
      percentile: (json['percentile'] as num?)?.toDouble(),
      zScore: (json['z_score'] as num?)?.toDouble(),
      velocityFromPrevious: MeasurementJsonHelper.growthVelocityFromJson(json['velocity_data']),
      percentileAnalysis: MeasurementJsonHelper.percentileResultFromJson(json['percentile_analysis']),
      flaggedForReview: json['flagged_for_review'] ?? false,
      validationResults: json['validation_results'] != null
          ? Map<String, dynamic>.from(json['validation_results'])
          : null,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      metadata: json['metadata'] != null
          ? Map<String, dynamic>.from(json['metadata'])
          : null,
    );
  }

  /// Convert measurement to database JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'baby_id': babyId,
      'measurement_type': measurementType,
      'value': value,
      'unit': unit,
      'measured_at': measuredAt.toIso8601String(),
      'measurement_date': measuredAt.toIso8601String(), // Backward compatibility
      'age_in_months': ageInMonths,
      'notes': notes,
      'percentile': percentile,
      'z_score': zScore,
      'velocity_data': velocityFromPrevious?.toJson(),
      'percentile_analysis': percentileAnalysis?.toJson(),
      'flagged_for_review': flaggedForReview,
      'validation_results': validationResults,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'metadata': metadata,
      // Legacy column support
      if (measurementType == 'weight') 'weight_kg': value,
      if (measurementType == 'height' || measurementType == 'length') 'height_cm': value,
      if (measurementType == 'head_circumference') 'head_circumference_cm': value,
    };
  }

  /// Create a copy with updated fields
  Measurement copyWith({
    String? id,
    String? babyId,
    String? measurementType,
    double? value,
    String? unit,
    DateTime? measuredAt,
    double? ageInMonths,
    String? notes,
    double? percentile,
    double? zScore,
    GrowthVelocity? velocityFromPrevious,
    PercentileResult? percentileAnalysis,
    bool? flaggedForReview,
    Map<String, dynamic>? validationResults,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? metadata,
  }) {
    return Measurement(
      id: id ?? this.id,
      babyId: babyId ?? this.babyId,
      measurementType: measurementType ?? this.measurementType,
      value: value ?? this.value,
      unit: unit ?? this.unit,
      measuredAt: measuredAt ?? this.measuredAt,
      ageInMonths: ageInMonths ?? this.ageInMonths,
      notes: notes ?? this.notes,
      percentile: percentile ?? this.percentile,
      zScore: zScore ?? this.zScore,
      velocityFromPrevious: velocityFromPrevious ?? this.velocityFromPrevious,
      percentileAnalysis: percentileAnalysis ?? this.percentileAnalysis,
      flaggedForReview: flaggedForReview ?? this.flaggedForReview,
      validationResults: validationResults ?? this.validationResults,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Check if measurement requires attention based on percentile and validation
  bool get requiresAttention {
    if (flaggedForReview) return true;
    if (percentileAnalysis?.requiresAttention == true) return true;
    if (percentile != null && (percentile! < 3.0 || percentile! > 97.0)) return true;
    if (validationResults?['hasWarnings'] == true) return true;
    return false;
  }

  /// Get display value with appropriate formatting
  String get displayValue {
    if (measurementType == 'weight') {
      return '${value.toStringAsFixed(2)} $unit';
    } else {
      return '${value.toStringAsFixed(1)} $unit';
    }
  }

  /// Get percentile display text
  String get percentileDisplay {
    if (percentile == null) return 'Not calculated';
    return '${percentile!.toStringAsFixed(1)}th percentile';
  }

  /// Get z-score display text
  String get zScoreDisplay {
    if (zScore == null) return 'Not calculated';
    return 'Z-score: ${zScore!.toStringAsFixed(2)}';
  }

  /// Convert to MeasurementData for analysis
  MeasurementData toMeasurementData(String gender) {
    return MeasurementData(
      value: value,
      ageInMonths: ageInMonths,
      date: measuredAt,
      measurementType: measurementType,
      gender: gender,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Measurement &&
        other.id == id &&
        other.babyId == babyId &&
        other.measurementType == measurementType &&
        other.value == value &&
        other.measuredAt == measuredAt;
  }

  @override
  int get hashCode {
    return Object.hash(id, babyId, measurementType, value, measuredAt);
  }

  @override
  String toString() {
    return 'Measurement(id: $id, type: $measurementType, value: $displayValue, percentile: $percentileDisplay, date: $measuredAt)';
  }
}

/// Extension methods for GrowthVelocity to support JSON serialization
extension GrowthVelocityJson on GrowthVelocity {
  Map<String, dynamic> toJson() {
    return {
      'velocity_per_month': velocityPerMonth,
      'velocity_percentile': velocityPercentile,
      'interpretation': interpretation,
      'is_normal': isNormal,
      'time_period_days': timePeriod.inDays,
    };
  }
}

/// Extension methods for PercentileResult to support JSON serialization
extension PercentileResultJson on PercentileResult {
  Map<String, dynamic> toJson() {
    return {
      'percentile': percentile,
      'z_score': zScore,
      'interpretation': interpretation,
      'requires_attention': requiresAttention,
      'category': category,
    };
  }
}

/// Helper functions for JSON deserialization
class MeasurementJsonHelper {
  static GrowthVelocity? growthVelocityFromJson(Map<String, dynamic>? json) {
    if (json == null) return null;
    return GrowthVelocity(
      velocityPerMonth: (json['velocity_per_month'] as num).toDouble(),
      velocityPercentile: (json['velocity_percentile'] as num).toDouble(),
      interpretation: json['interpretation'] ?? '',
      isNormal: json['is_normal'] ?? false,
      timePeriod: Duration(days: json['time_period_days'] ?? 0),
    );
  }

  static PercentileResult? percentileResultFromJson(Map<String, dynamic>? json) {
    if (json == null) return null;
    return PercentileResult(
      percentile: (json['percentile'] as num).toDouble(),
      zScore: (json['z_score'] as num).toDouble(),
      interpretation: json['interpretation'] ?? '',
      requiresAttention: json['requires_attention'] ?? false,
      category: json['category'] ?? '',
    );
  }
}