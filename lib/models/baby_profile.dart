class BabyProfile {
  final String id;
  final String name;
  final DateTime birthDate;
  final String? photo;
  final String gender;
  final double? birthWeight;
  final double? birthHeight;
  final List<String> allergies;
  final List<String> medications;
  final String? note;
  final String? healthNotes;
  final String? allergyNotes;
  final DateTime createdAt;
  final DateTime updatedAt;

  BabyProfile({
    required this.id,
    required this.name,
    required this.birthDate,
    this.photo,
    required this.gender,
    this.birthWeight,
    this.birthHeight,
    this.allergies = const [],
    this.medications = const [],
    this.note,
    this.healthNotes,
    this.allergyNotes,
    required this.createdAt,
    required this.updatedAt,
  });

  int get ageInDays => DateTime.now().difference(birthDate).inDays;
  int get ageInWeeks => (ageInDays / 7).floor();
  int get ageInMonths =>
      ((DateTime.now().year - birthDate.year) * 12) +
      (DateTime.now().month - birthDate.month);

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'birthDate': birthDate.toString(),
        'photo': photo,
        'gender': gender,
        'birthWeight': birthWeight,
        'birthHeight': birthHeight,
        'allergies': allergies,
        'medications': medications,
        'note': note,
        'healthNotes': healthNotes,
        'allergyNotes': allergyNotes,
        'createdAt': createdAt.toString(),
        'updatedAt': updatedAt.toString(),
      };

  factory BabyProfile.fromJson(Map<String, dynamic> json) => BabyProfile(
        id: json['id'],
        name: json['name'],
        birthDate: DateTime.parse(json['birth_date'] ?? json['birthDate']),
        photo: json['photo_url'] ?? json['photo'],
        gender: json['gender'],
        birthWeight: (json['birth_weight'] ?? json['birthWeight'])?.toDouble(),
        birthHeight: (json['birth_height'] ?? json['birthHeight'])?.toDouble(),
        allergies: List<String>.from(json['allergies'] ?? []),
        medications: List<String>.from(json['medications'] ?? []),
        note: json['note'] as String?,
        healthNotes: json['health_notes'] ?? json['healthNotes'],
        allergyNotes: json['allergy_notes'] ?? json['allergyNotes'],
        createdAt: DateTime.parse(json['created_at'] ?? json['createdAt']),
        updatedAt: DateTime.parse(json['updated_at'] ?? json['updatedAt']),
      );

  BabyProfile copyWith({
    String? id,
    String? name,
    DateTime? birthDate,
    String? photo,
    String? gender,
    double? birthWeight,
    double? birthHeight,
    List<String>? allergies,
    List<String>? medications,
    String? note,
    String? healthNotes,
    String? allergyNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) =>
      BabyProfile(
        id: id ?? this.id,
        name: name ?? this.name,
        birthDate: birthDate ?? this.birthDate,
        photo: photo ?? this.photo,
        gender: gender ?? this.gender,
        birthWeight: birthWeight ?? this.birthWeight,
        birthHeight: birthHeight ?? this.birthHeight,
        allergies: allergies ?? this.allergies,
        medications: medications ?? this.medications,
        note: note ?? this.note,
        healthNotes: healthNotes ?? this.healthNotes,
        allergyNotes: allergyNotes ?? this.allergyNotes,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
      );
}
