/// Feature access models and enums for subscription-based restrictions
/// 
/// This file defines the core models for managing feature access based on
/// subscription status, providing a clean interface for checking permissions
/// and displaying upgrade prompts throughout the application.

import 'package:flutter/material.dart';
import 'enums.dart';

/// Enum defining all features that can be restricted by subscription
enum AppFeature {
  /// AI-powered insights and analytics
  aiInsights,
  
  /// AI chat assistant
  aiChat,
  
  /// WHO growth charts and percentile tracking
  whoGrowthCharts,
  
  /// Multiple baby profiles (free users limited to 1)
  multipleBabyProfiles,
  
  /// Family sharing and member management
  familySharing,
  
  /// Data export functionality
  dataExport,
  
  /// Advanced analytics dashboard
  advancedAnalytics,
  
  /// Custom reminders and notifications
  customReminders,
  
  /// Priority customer support
  prioritySupport,
}

/// Extension methods for AppFeature enum
extension AppFeatureExtension on AppFeature {
  /// Get display name for the feature
  String get displayName {
    switch (this) {
      case AppFeature.aiInsights:
        return 'AI Insights';
      case AppFeature.aiChat:
        return 'AI Chat Assistant';
      case AppFeature.whoGrowthCharts:
        return 'WHO Growth Charts';
      case AppFeature.multipleBabyProfiles:
        return 'Multiple Baby Profiles';
      case AppFeature.familySharing:
        return 'Family Sharing';
      case AppFeature.dataExport:
        return 'Data Export';
      case AppFeature.advancedAnalytics:
        return 'Advanced Analytics';
      case AppFeature.customReminders:
        return 'Custom Reminders';
      case AppFeature.prioritySupport:
        return 'Priority Support';
    }
  }
  
  /// Get description for the feature
  String get description {
    switch (this) {
      case AppFeature.aiInsights:
        return 'Get AI-powered insights about your baby\'s development patterns';
      case AppFeature.aiChat:
        return '24/7 AI assistant for parenting questions and guidance';
      case AppFeature.whoGrowthCharts:
        return 'WHO standard growth charts with percentile tracking';
      case AppFeature.multipleBabyProfiles:
        return 'Track multiple children in one account';
      case AppFeature.familySharing:
        return 'Share access with family members and caregivers';
      case AppFeature.dataExport:
        return 'Export your data for healthcare providers';
      case AppFeature.advancedAnalytics:
        return 'Detailed analytics and trend analysis';
      case AppFeature.customReminders:
        return 'Set custom reminders and notifications';
      case AppFeature.prioritySupport:
        return 'Get priority customer support and assistance';
    }
  }
  
  /// Get icon for the feature
  IconData get icon {
    switch (this) {
      case AppFeature.aiInsights:
        return Icons.insights;
      case AppFeature.aiChat:
        return Icons.chat;
      case AppFeature.whoGrowthCharts:
        return Icons.trending_up;
      case AppFeature.multipleBabyProfiles:
        return Icons.people;
      case AppFeature.familySharing:
        return Icons.family_restroom;
      case AppFeature.dataExport:
        return Icons.download;
      case AppFeature.advancedAnalytics:
        return Icons.analytics;
      case AppFeature.customReminders:
        return Icons.notifications;
      case AppFeature.prioritySupport:
        return Icons.support_agent;
    }
  }
}

/// Style options for upgrade prompts
enum PromptStyle {
  /// Full-screen dialog
  dialog,
  
  /// Bottom sheet
  bottomSheet,
  
  /// Inline banner
  banner,
  
  /// Card-style prompt
  card,
  
  /// Standard modal
  standard,
}

/// Configuration for upgrade prompts
class UpgradePromptConfig {
  final String title;
  final String description;
  final List<String> benefits;
  final String? ctaText;
  final PromptStyle? style;
  final Color? accentColor;
  
  const UpgradePromptConfig({
    required this.title,
    required this.description,
    required this.benefits,
    this.ctaText,
    this.style,
    this.accentColor,
  });
}

/// Result of a feature access check
class FeatureAccessResult {
  final bool hasAccess;
  final String? restrictionReason;
  final UpgradePromptConfig? upgradePrompt;
  final int? currentUsage;
  final int? usageLimit;
  
  const FeatureAccessResult({
    required this.hasAccess,
    this.restrictionReason,
    this.upgradePrompt,
    this.currentUsage,
    this.usageLimit,
  });
  
  /// Factory for granted access
  factory FeatureAccessResult.granted({
    int? currentUsage,
    int? usageLimit,
  }) {
    return FeatureAccessResult(
      hasAccess: true,
      currentUsage: currentUsage,
      usageLimit: usageLimit,
    );
  }
  
  /// Factory for denied access
  factory FeatureAccessResult.denied({
    required String reason,
    required UpgradePromptConfig upgradePrompt,
    int? currentUsage,
    int? usageLimit,
  }) {
    return FeatureAccessResult(
      hasAccess: false,
      restrictionReason: reason,
      upgradePrompt: upgradePrompt,
      currentUsage: currentUsage,
      usageLimit: usageLimit,
    );
  }
  
  /// Check if user is near their usage limit
  bool get isNearLimit {
    if (currentUsage == null || usageLimit == null) return false;
    return currentUsage! >= (usageLimit! * 0.8); // 80% threshold
  }
  
  /// Get remaining usage count
  int? get remainingUsage {
    if (currentUsage == null || usageLimit == null) return null;
    return usageLimit! - currentUsage!;
  }
}

/// Feature restriction definition
class FeatureRestriction {
  final AppFeature feature;
  final bool isBlocked;
  final int? usageLimit;
  final String reason;
  
  const FeatureRestriction({
    required this.feature,
    required this.isBlocked,
    this.usageLimit,
    required this.reason,
  });
}

/// Feature benefit definition
class FeatureBenefit {
  final AppFeature feature;
  final String description;
  final bool isUnlimited;
  final int? usageLimit;
  
  const FeatureBenefit({
    required this.feature,
    required this.description,
    this.isUnlimited = false,
    this.usageLimit,
  });
}

/// Action taken on upgrade prompt
enum PromptAction {
  /// User clicked upgrade button
  upgrade,
  
  /// User dismissed the prompt
  dismiss,
  
  /// User clicked learn more
  learnMore,
  
  /// User closed the prompt
  close,
}