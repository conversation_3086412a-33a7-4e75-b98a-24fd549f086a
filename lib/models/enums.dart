/// Core enums for family member and subscription status management
/// 
/// This file contains enums and extensions for managing family member
/// statuses and subscription states throughout the application.
/// 
/// Key enums:
/// - [FamilyMemberStatus]: Tracks family member invitation and access states
/// - [SubscriptionStatus]: Manages subscription lifecycle and billing states

/// Status of a family member in the family sharing system
enum FamilyMemberStatus {
  /// Member is active and can access the app
  active,
  
  /// Member has been invited but hasn't accepted yet
  pending,
  
  /// Member is temporarily inactive
  inactive,
  
  /// Member has been removed from the family
  removed,
}

/// Status of a user's subscription
enum SubscriptionStatus {
  /// User has an active paid subscription
  active,
  
  /// User is in a trial period
  trial,
  
  /// Subscription has expired
  expired,
  
  /// Subscription was cancelled but still active until end of period
  cancelled,
  
  /// User is on the free plan
  free,
  
  /// Subscription is past due (payment failed)
  pastDue,
}

/// Extension methods for FamilyMemberStatus
extension FamilyMemberStatusExtension on FamilyMemberStatus {
  /// Get display name for the status
  String get displayName {
    switch (this) {
      case FamilyMemberStatus.active:
        return 'Active';
      case FamilyMemberStatus.pending:
        return 'Pending';
      case FamilyMemberStatus.inactive:
        return 'Inactive';
      case FamilyMemberStatus.removed:
        return 'Removed';
    }
  }
  
  /// Check if the member can access the app
  bool get canAccess {
    return this == FamilyMemberStatus.active;
  }
  
  /// Check if the status indicates a pending invitation
  bool get isPending {
    return this == FamilyMemberStatus.pending;
  }
  
  /// Safely parse FamilyMemberStatus from string
  static FamilyMemberStatus? fromString(String? value) {
    if (value == null) return null;
    try {
      return FamilyMemberStatus.values.firstWhere(
        (status) => status.name.toLowerCase() == value.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }
  
  /// Parse FamilyMemberStatus from string with fallback
  static FamilyMemberStatus fromStringWithFallback(String? value, {FamilyMemberStatus fallback = FamilyMemberStatus.pending}) {
    return fromString(value) ?? fallback;
  }
}

/// Extension methods for SubscriptionStatus
extension SubscriptionStatusExtension on SubscriptionStatus {
  /// Get display name for the status
  String get displayName {
    switch (this) {
      case SubscriptionStatus.active:
        return 'Active';
      case SubscriptionStatus.trial:
        return 'Trial';
      case SubscriptionStatus.expired:
        return 'Expired';
      case SubscriptionStatus.cancelled:
        return 'Cancelled';
      case SubscriptionStatus.free:
        return 'Free';
      case SubscriptionStatus.pastDue:
        return 'Past Due';
    }
  }
  
  /// Check if the subscription provides premium features
  bool get isPremium {
    return this == SubscriptionStatus.active || this == SubscriptionStatus.trial;
  }
  
  /// Check if the subscription needs attention (expired, cancelled, past due)
  bool get needsAttention {
    return this == SubscriptionStatus.expired || 
           this == SubscriptionStatus.cancelled || 
           this == SubscriptionStatus.pastDue;
  }
  
  /// Check if the subscription is in a trial period
  bool get isTrial {
    return this == SubscriptionStatus.trial;
  }
  
  /// Safely parse SubscriptionStatus from string
  static SubscriptionStatus? fromString(String? value) {
    if (value == null) return null;
    try {
      return SubscriptionStatus.values.firstWhere(
        (status) => status.name.toLowerCase() == value.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }
  
  /// Parse SubscriptionStatus from string with fallback
  static SubscriptionStatus fromStringWithFallback(String? value, {SubscriptionStatus fallback = SubscriptionStatus.free}) {
    return fromString(value) ?? fallback;
  }
}