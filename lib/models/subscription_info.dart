import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'enums.dart';

/// Model representing subscription information and plan details
class SubscriptionInfo {
  /// Unique identifier for the subscription plan
  final String planId;
  
  /// Display name of the subscription plan
  final String planName;
  
  /// Current status of the subscription
  final SubscriptionStatus status;
  
  /// Date when the subscription renews (for active subscriptions)
  final DateTime? renewalDate;
  
  /// Monthly price of the subscription
  final double monthlyPrice;
  
  /// List of features included in this plan
  final List<String> features;
  
  /// Whether the user is currently in a trial period
  final bool isTrialActive;
  
  /// Date when the trial period ends
  final DateTime? trialEndsAt;
  
  /// Payment method information
  final PaymentMethod? paymentMethod;
  
  /// Date when the subscription was created
  final DateTime createdAt;
  
  /// Date when the subscription was last updated
  final DateTime updatedAt;
  
  /// Number of family members allowed in this plan
  final int maxFamilyMembers;
  
  /// Whether AI insights are included
  final bool includesAiInsights;
  
  /// Whether data export is included
  final bool includesDataExport;
  
  /// Whether premium support is included
  final bool includesPremiumSupport;
  
  /// Storage limit in GB (null for unlimited)
  final int? storageLimit;

  const SubscriptionInfo({
    required this.planId,
    required this.planName,
    required this.status,
    this.renewalDate,
    required this.monthlyPrice,
    required this.features,
    required this.isTrialActive,
    this.trialEndsAt,
    this.paymentMethod,
    required this.createdAt,
    required this.updatedAt,
    required this.maxFamilyMembers,
    required this.includesAiInsights,
    required this.includesDataExport,
    required this.includesPremiumSupport,
    this.storageLimit,
  });

  /// Check if the subscription is currently active and provides premium features
  bool get isPremium => _isPremiumStatus(status);
  
  /// Check if the subscription needs user attention
  bool get needsAttention => _needsAttentionStatus(status);
  
  /// Check if the subscription is in trial period
  bool get isTrial => _isTrialStatus(status);
  
  /// Get days remaining in trial (null if not in trial)
  int? get trialDaysRemaining {
    if (!isTrialActive || trialEndsAt == null) return null;
    final remaining = trialEndsAt!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }
  
  /// Get days until renewal (null if not active)
  int? get daysUntilRenewal {
    if (renewalDate == null) return null;
    final remaining = renewalDate!.difference(DateTime.now()).inDays;
    return remaining > 0 ? remaining : 0;
  }
  
  /// Get formatted price string
  String get formattedPrice {
    if (monthlyPrice == 0) return 'Free';
    return '\$${monthlyPrice.toStringAsFixed(2)}/month';
  }
  
  /// Get subscription status message for display
  String get statusMessage {
    switch (status) {
      case SubscriptionStatus.active:
        if (renewalDate != null) {
          final days = daysUntilRenewal;
          if (days != null && days <= 7) {
            return 'Renews in $days days';
          }
          return 'Active until ${_formatDate(renewalDate!)}';
        }
        return 'Active';
      case SubscriptionStatus.trial:
        final days = trialDaysRemaining;
        if (days != null) {
          if (days == 0) return 'Trial ends today';
          if (days == 1) return 'Trial ends tomorrow';
          return 'Trial ends in $days days';
        }
        return 'Trial active';
      case SubscriptionStatus.expired:
        return 'Subscription expired';
      case SubscriptionStatus.cancelled:
        if (renewalDate != null) {
          return 'Cancelled - ends ${_formatDate(renewalDate!)}';
        }
        return 'Cancelled';
      case SubscriptionStatus.free:
        return 'Free plan';
      case SubscriptionStatus.pastDue:
        return 'Payment required';
    }
  }
  
  /// Get upgrade message for free/trial users
  String? get upgradeMessage {
    if (status == SubscriptionStatus.free) {
      return 'Upgrade to unlock premium features';
    }
    if (status == SubscriptionStatus.trial) {
      final days = trialDaysRemaining;
      if (days != null && days <= 3) {
        return 'Trial ending soon - upgrade to continue';
      }
    }
    return null;
  }
  
  /// Format date for display
  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() => {
        'plan_id': planId,
        'plan_name': planName,
        'status': status.toString().split('.').last,
        'renewal_date': renewalDate?.toIso8601String(),
        'monthly_price': monthlyPrice,
        'features': features,
        'is_trial_active': isTrialActive,
        'trial_ends_at': trialEndsAt?.toIso8601String(),
        'payment_method': paymentMethod?.toJson(),
        'created_at': createdAt.toIso8601String(),
        'updated_at': updatedAt.toIso8601String(),
        'max_family_members': maxFamilyMembers,
        'includes_ai_insights': includesAiInsights,
        'includes_data_export': includesDataExport,
        'includes_premium_support': includesPremiumSupport,
        'storage_limit': storageLimit,
      };

  /// Create from JSON
  factory SubscriptionInfo.fromJson(Map<String, dynamic> json) {
    // Handle features - could be JSON string or array
    List<String> featuresList = [];
    if (json['features'] != null) {
      if (json['features'] is String) {
        // Parse JSON string
        try {
          final decoded = jsonDecode(json['features']);
          featuresList = List<String>.from(decoded);
        } catch (e) {
          debugPrint('Error parsing features JSON: $e');
          featuresList = [];
        }
      } else if (json['features'] is List) {
        featuresList = List<String>.from(json['features']);
      }
    }

    return SubscriptionInfo(
      planId: json['plan_id'] ?? '',
      planName: json['plan_name'] ?? '',
      status: SubscriptionStatusExtension.fromStringWithFallback(json['status']),
      renewalDate: json['renewal_date'] != null 
          ? DateTime.parse(json['renewal_date']) 
          : null,
      monthlyPrice: (json['monthly_price'] as num?)?.toDouble() ?? 0.0,
      features: featuresList,
      isTrialActive: json['is_trial_active'] ?? false,
      trialEndsAt: json['trial_ends_at'] != null 
          ? DateTime.parse(json['trial_ends_at']) 
          : null,
      paymentMethod: json['payment_method'] != null 
          ? PaymentMethod.fromJson(json['payment_method']) 
          : null,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      maxFamilyMembers: json['max_family_members'] ?? 1,
      includesAiInsights: json['includes_ai_insights'] ?? false,
      includesDataExport: json['includes_data_export'] ?? false,
      includesPremiumSupport: json['includes_premium_support'] ?? false,
      storageLimit: json['storage_limit'],
    );
  }

  /// Create a copy with updated values
  SubscriptionInfo copyWith({
    String? planId,
    String? planName,
    SubscriptionStatus? status,
    DateTime? renewalDate,
    double? monthlyPrice,
    List<String>? features,
    bool? isTrialActive,
    DateTime? trialEndsAt,
    PaymentMethod? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
    int? maxFamilyMembers,
    bool? includesAiInsights,
    bool? includesDataExport,
    bool? includesPremiumSupport,
    int? storageLimit,
  }) =>
      SubscriptionInfo(
        planId: planId ?? this.planId,
        planName: planName ?? this.planName,
        status: status ?? this.status,
        renewalDate: renewalDate ?? this.renewalDate,
        monthlyPrice: monthlyPrice ?? this.monthlyPrice,
        features: features ?? this.features,
        isTrialActive: isTrialActive ?? this.isTrialActive,
        trialEndsAt: trialEndsAt ?? this.trialEndsAt,
        paymentMethod: paymentMethod ?? this.paymentMethod,
        createdAt: createdAt ?? this.createdAt,
        updatedAt: updatedAt ?? this.updatedAt,
        maxFamilyMembers: maxFamilyMembers ?? this.maxFamilyMembers,
        includesAiInsights: includesAiInsights ?? this.includesAiInsights,
        includesDataExport: includesDataExport ?? this.includesDataExport,
        includesPremiumSupport: includesPremiumSupport ?? this.includesPremiumSupport,
        storageLimit: storageLimit ?? this.storageLimit,
      );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is SubscriptionInfo &&
          runtimeType == other.runtimeType &&
          planId == other.planId &&
          status == other.status;

  @override
  int get hashCode => planId.hashCode ^ status.hashCode;

  @override
  String toString() => 'SubscriptionInfo(plan: $planName, status: ${status.toString().split('.').last})';
  
  /// Helper methods for subscription status checks
  static bool _isPremiumStatus(SubscriptionStatus status) {
    return status == SubscriptionStatus.active || status == SubscriptionStatus.trial;
  }
  
  static bool _needsAttentionStatus(SubscriptionStatus status) {
    return status == SubscriptionStatus.expired || 
           status == SubscriptionStatus.cancelled || 
           status == SubscriptionStatus.pastDue;
  }
  
  static bool _isTrialStatus(SubscriptionStatus status) {
    return status == SubscriptionStatus.trial;
  }
}

/// Model representing payment method information
class PaymentMethod {
  /// Type of payment method (card, paypal, etc.)
  final String type;
  
  /// Last 4 digits of card (for card payments)
  final String? last4;
  
  /// Card brand (visa, mastercard, etc.)
  final String? brand;
  
  /// Expiration month (for cards)
  final int? expiryMonth;
  
  /// Expiration year (for cards)
  final int? expiryYear;
  
  /// Whether this is the default payment method
  final bool isDefault;

  const PaymentMethod({
    required this.type,
    this.last4,
    this.brand,
    this.expiryMonth,
    this.expiryYear,
    required this.isDefault,
  });

  /// Get display name for the payment method
  String get displayName {
    if (type == 'card' && brand != null && last4 != null) {
      final brandName = brand!.toUpperCase();
      return '$brandName •••• $last4';
    }
    return type.toUpperCase();
  }
  
  /// Check if the card is expired (for card payments)
  bool get isExpired {
    if (type != 'card' || expiryMonth == null || expiryYear == null) {
      return false;
    }
    final now = DateTime.now();
    final expiry = DateTime(expiryYear!, expiryMonth!);
    return now.isAfter(expiry);
  }

  /// Convert to JSON for serialization
  Map<String, dynamic> toJson() => {
        'type': type,
        'last4': last4,
        'brand': brand,
        'expiry_month': expiryMonth,
        'expiry_year': expiryYear,
        'is_default': isDefault,
      };

  /// Create from JSON
  factory PaymentMethod.fromJson(Map<String, dynamic> json) => PaymentMethod(
        type: json['type'],
        last4: json['last4'],
        brand: json['brand'],
        expiryMonth: json['expiry_month'],
        expiryYear: json['expiry_year'],
        isDefault: json['is_default'] ?? false,
      );

  @override
  String toString() => 'PaymentMethod(type: $type, display: $displayName)';
}

/// Predefined subscription plans
class SubscriptionPlans {
  /// Free plan configuration
  static SubscriptionInfo get free => SubscriptionInfo(
        planId: 'free',
        planName: 'Free',
        status: SubscriptionStatus.free,
        monthlyPrice: 0.0,
        features: [
          'Basic activity tracking',
          'Up to 1 baby profile',
          '1 family member',
          'Basic charts and insights',
        ],
        isTrialActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        maxFamilyMembers: 1,
        includesAiInsights: false,
        includesDataExport: false,
        includesPremiumSupport: false,
        storageLimit: 1, // 1GB
      );
  
  /// Premium plan configuration
  static SubscriptionInfo get premium => SubscriptionInfo(
        planId: 'premium',
        planName: 'Premium',
        status: SubscriptionStatus.active,
        monthlyPrice: 9.99,
        features: [
          'Unlimited activity tracking',
          'Unlimited baby profiles',
          'Up to 6 family members',
          'AI-powered insights',
          'Advanced charts and analytics',
          'Data export',
          'Premium support',
          'Unlimited storage',
        ],
        isTrialActive: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        maxFamilyMembers: 6,
        includesAiInsights: true,
        includesDataExport: true,
        includesPremiumSupport: true,
        storageLimit: null, // Unlimited
      );
}