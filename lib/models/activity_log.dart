import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';
import '../utils/activity_type_config.dart';

enum ActivityType {
  feeding,
  sleep,
  diaper,
  medicine,
  vaccination,
  growth,
  mood,
  milestone,
  play,
  bath,
  doctor,
  photo,
  note,
  temperature,
  potty,
  pumping,
  tummy_time,
  story_time,
  screen_time,
  skin_to_skin,
  outdoor_play,
  indoor_play,
  brush_teeth,
  custom
}

extension ActivityTypeExtension on ActivityType {
  String toDisplayString() {
    return name.replaceAll('_', ' ').split(' ').map((str) => str[0].toUpperCase() + str.substring(1)).join(' ');
  }
}

enum FeedingType { breastfeeding, bottle, solid }

enum DiaperType { wet, dirty, both, dry }

enum SleepQuality { poor, fair, good, excellent }

enum MoodType { crying, fussy, calm, happy, playful, sleepy }

enum TemperatureType { oral, rectal, armpit, forehead }

class ActivityLog {
  final String id;
  final String babyId;
  final ActivityType type;
  final DateTime timestamp;
  final DateTime? endTime;
  final Map<String, dynamic> data;
  final String? notes;
  final List<String> photos;
  final DateTime createdAt;
  final Map<String, dynamic>? details;
  final Map<String, dynamic>? metadata;

  ActivityLog({
    required this.id,
    required this.babyId,
    required this.type,
    required this.timestamp,
    this.endTime,
    required this.data,
    this.notes,
    this.photos = const [],
    required this.createdAt,
    this.details,
    this.metadata,
  });

  Duration? get duration => endTime?.difference(timestamp);
  
  /// Safely parse timestamp string, handling both UTC and local formats
  static DateTime _parseTimestampSafely(String timestampStr) {
    try {
      final parsed = DateTime.parse(timestampStr);
      
      // Handle the transition period where we have mixed timestamp formats:
      // 1. Legacy UTC timestamps from database (need conversion)
      // 2. New local timestamps stored in our app (no conversion needed)
      
      if (timestampStr.contains('Z') || timestampStr.contains('+00:00')) {
        // This timestamp has UTC format markers
        // But we need to determine if it's actually UTC or local time stored with UTC format
        
        // Strategy: Check if this would result in a "future" timestamp when converted
        final convertedToLocal = parsed.toLocal();
        final now = DateTime.now();
        
        // If converting to local puts us more than 6 hours in the future, 
        // it's likely local time stored with UTC format - don't convert
        if (convertedToLocal.isAfter(now.add(Duration(hours: 6)))) {
          debugPrint('🕰️ Detected local time with UTC format: $timestampStr');
          // Treat as local time - parse without timezone conversion
          final localStr = timestampStr.replaceAll('+00:00', '').replaceAll('Z', '');
          return DateTime.parse(localStr);
        } else {
          // Likely genuine UTC timestamp - convert to local
          debugPrint('🕰️ Detected genuine UTC timestamp: $timestampStr');
          return convertedToLocal;
        }
      }
      
      // No UTC markers - treat as local time
      return parsed;
    } catch (e) {
      debugPrint('⚠️ Error parsing timestamp $timestampStr: $e');
      return DateTime.now();
    }
  }

  Map<String, dynamic> toJson() {
    final map = <String, dynamic>{
      'baby_id': babyId,
      'activity_type': type.name,
      'recorded_at': timestamp.toIso8601String(),
      'duration_minutes': duration?.inMinutes,
      'quantity': data['quantity'],
      'unit': data['unit'],
      'quality': data['quality'],
      'notes': notes,
      'created_at': createdAt.toIso8601String(),
    };
    
    // Only include id if it's not empty (let database auto-generate if empty)
    if (id.isNotEmpty) {
      map['id'] = id;
    }
    if (details != null && details!.isNotEmpty) {
      map['details'] = _convertDetailsToSerializable(details!);
    }
    if (metadata != null && metadata!.isNotEmpty) {
      map['metadata'] = _convertDetailsToSerializable(metadata!);
    }
    return map;
  }

  Map<String, dynamic> _convertDetailsToSerializable(Map<String, dynamic> details) {
    final Map<String, dynamic> serializable = {};
    
    details.forEach((key, value) {
      if (value is DateTime) {
        serializable[key] = value.toIso8601String();
      } else if (value is Map<String, dynamic>) {
        serializable[key] = _convertDetailsToSerializable(value);
      } else if (value is List) {
        serializable[key] = value.map((item) {
          if (item is DateTime) {
            return item.toIso8601String();
          } else if (item is Map<String, dynamic>) {
            return _convertDetailsToSerializable(item);
          }
          return item;
        }).toList();
      } else {
        serializable[key] = value;
      }
    });
    
    return serializable;
  }

  factory ActivityLog.fromJson(Map<String, dynamic> json) {
    debugPrint('🔄 Creating ActivityLog from JSON: ${json['recorded_at']}');
    // Parse timestamp as local time, removing UTC markers like we do for milestones
    final timestamp = json['recorded_at'] != null 
        ? DateTime.parse(json['recorded_at'].toString().replaceAll('+00:00', '').replaceAll('Z', ''))
        : DateTime.now();
    debugPrint('📅 Parsed timestamp: $timestamp');
    
    return ActivityLog(
      id: json['id'],
      babyId: json['baby_id'],
      type: ActivityType.values.firstWhere((e) => e.name == json['activity_type']),
      timestamp: timestamp,
      endTime: json['duration_minutes'] != null
          ? timestamp.add(Duration(minutes: json['duration_minutes']))
          : null,
      data: {
        'quantity': json['quantity'],
        'unit': json['unit'],
        'quality': json['quality'],
      },
      notes: json['notes'],
      details: json['details'] != null ? Map<String, dynamic>.from(json['details']) : null,
      metadata: json['metadata'] != null ? Map<String, dynamic>.from(json['metadata']) : null,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at'].toString().replaceAll('+00:00', '').replaceAll('Z', ''))
          : DateTime.now(),
    );
  }

  factory ActivityLog.fromRawData({
    required String babyId,
    required String type,
    required Map<String, dynamic> data,
  }) {
    final now = DateTime.now();
    // Build details for feeding logs
    Map<String, dynamic> details = {};
    if (type == 'feeding') {
      if (data['side'] != null) details['side'] = data['side'];
      if (data['formula_type'] != null) details['formula_type'] = data['formula_type'];
      if (data['meal_type'] != null) details['meal_type'] = data['meal_type'];
      if (data['food_items'] != null) details['food_items'] = data['food_items'];
      if (data['mood'] != null) details['mood'] = data['mood'];
      if (data['feeding_type'] != null) details['feeding_type'] = data['feeding_type'];
      if (data['duration'] != null) details['duration'] = data['duration'];
    }
    // Build details for diaper logs
    if (type == 'diaper') {
      if (data['type'] != null) {
        // Handle both single string and list of strings
        if (data['type'] is List) {
          final types = data['type'] as List;
          if (types.isNotEmpty) {
            details['diaper_type'] = types.join(', ');
          }
        } else {
          // Transform 'both' to 'Wet & Dirty' for better display
          final diaperType = data['type'].toString();
          details['diaper_type'] = diaperType == 'both' ? 'Wet & Dirty' : diaperType;
        }
      }
    }
    // Build details for sleep logs
    if (type == 'sleep') {
      if (data['quality'] != null) details['sleep_quality'] = data['quality'];
      if (data['sleepLocation'] != null) details['sleep_location'] = data['sleepLocation'];
      if (data['roomTemperature'] != null) details['room_temperature'] = data['roomTemperature'];
      if (data['noiseLevel'] != null) details['noise_level'] = data['noiseLevel'];
      if (data['fedBeforeSleep'] != null) details['fed_before_sleep'] = data['fedBeforeSleep'];
    }
    // Build details for milestone logs
    if (type == 'milestone') {
      if (data['title'] != null) details['milestone_title'] = data['title'];
      if (data['description'] != null) details['milestone_description'] = data['description'];
      if (data['category'] != null) details['milestone_category'] = data['category'];
      if (data['type'] != null) details['milestone_type'] = data['type'];
      if (data['age_in_months'] != null) details['age_in_months'] = data['age_in_months'];
      if (data['age_in_days'] != null) details['age_in_days'] = data['age_in_days'];
      if (data['is_custom'] != null) details['is_custom'] = data['is_custom'];
    }
    // Build details for medicine logs
    if (type == 'medicine') {
      if (data['medication'] != null) details['medication'] = data['medication'];
      if (data['quantity'] != null && data['unit'] != null) {
        details['dosage'] = '${data['quantity']} ${data['unit']}';
      }
    }
    // Build details for vaccination logs
    if (type == 'vaccination') {
      if (data['vaccine'] != null) details['vaccine'] = data['vaccine'];
      if (data['batch_number'] != null) details['batch_number'] = data['batch_number'];
      if (data['provider'] != null) details['provider'] = data['provider'];
      if (data['location'] != null) details['location'] = data['location'];
    }
    // Build details for temperature logs
    if (type == 'temperature') {
      if (data['temperature'] != null && data['temperature_unit'] != null) {
        final unit = data['temperature_unit'].toString().contains('Celsius') ? 'C' : 'F';
        details['temperature_reading'] = '${data['temperature']}$unit';
        
        // Add temperature status
        double tempInCelsius = data['temperature'];
        if (unit == 'F') {
          tempInCelsius = (data['temperature'] - 32) * 5 / 9;
        }
        
        String status;
        if (tempInCelsius < 36.0) {
          status = 'Low Temperature';
        } else if (tempInCelsius >= 36.0 && tempInCelsius <= 37.5) {
          status = 'Normal';
        } else if (tempInCelsius > 37.5 && tempInCelsius <= 38.5) {
          status = 'Mild Fever';
        } else if (tempInCelsius > 38.5 && tempInCelsius <= 40.0) {
          status = 'High Fever';
        } else {
          status = 'Very High Fever';
        }
        details['temperature_status'] = status;
      }
      if (data['measurement_method'] != null) details['measurement_method'] = data['measurement_method'];
      if (data['temperature_unit'] != null) details['temperature_unit'] = data['temperature_unit'];
    }
    // Build details for potty logs
    if (type == 'potty') {
      if (data['potty_type'] != null) details['potty_type'] = data['potty_type'];
      if (data['success_level'] != null) details['success_level'] = data['success_level'];
      if (data['location'] != null) details['location'] = data['location'];
      if (data['assistance_needed'] != null) details['assistance_needed'] = data['assistance_needed'];
    }
    // Build details for tummy time logs
    if (type == 'tummy_time') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['position'] != null) details['position'] = data['position'];
      if (data['mood'] != null) details['mood'] = data['mood'];
      if (data['activity'] != null) details['activity'] = data['activity'];
    }
    // Build details for story time logs
    if (type == 'story_time') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['book_type'] != null) details['book_type'] = data['book_type'];
      if (data['engagement'] != null) details['engagement'] = data['engagement'];
      if (data['activity'] != null) details['activity'] = data['activity'];
    }
    // Build details for skin to skin logs
    if (type == 'skin_to_skin') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['position'] != null) details['position'] = data['position'];
      if (data['location'] != null) details['location'] = data['location'];
      if (data['benefit'] != null) details['benefit'] = data['benefit'];
    }
    // Build details for screen time logs
    if (type == 'screen_time') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['content'] != null) details['content'] = data['content'];
      if (data['device'] != null) details['device'] = data['device'];
      if (data['purpose'] != null) details['purpose'] = data['purpose'];
    }
    // Build details for outdoor play logs
    if (type == 'outdoor_play') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['activity'] != null) details['activity'] = data['activity'];
      if (data['location'] != null) details['location'] = data['location'];
      if (data['weather'] != null) details['weather'] = data['weather'];
    }
    // Build details for indoor play logs
    if (type == 'indoor_play') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['activity'] != null) details['activity'] = data['activity'];
      if (data['location'] != null) details['location'] = data['location'];
      if (data['toys'] != null) details['toys'] = data['toys'];
    }
    // Build details for brush teeth logs
    if (type == 'brush_teeth') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['toothbrush'] != null) details['toothbrush'] = data['toothbrush'];
      if (data['toothpaste'] != null) details['toothpaste'] = data['toothpaste'];
      if (data['location'] != null) details['location'] = data['location'];
      if (data['cooperation'] != null) details['cooperation'] = data['cooperation'];
      if (data['quality'] != null) details['quality'] = data['quality'];
    }
    // Build details for pumping logs
    if (type == 'pumping') {
      if (data['duration'] != null) details['duration'] = data['duration'];
      if (data['side'] != null) details['side'] = data['side'];
    }
    // Build details for custom logs
    if (type == 'custom') {
      if (data['activity_name'] != null) details['activity_name'] = data['activity_name'];
      if (data['category'] != null && data['category'].toString().isNotEmpty) details['category'] = data['category'];
      details['is_custom'] = true;
    }
    // Add other details for other types as needed
    // Calculate duration for sleep and feeding activities
    Duration? activityDuration;
    if (type == 'sleep' || (type == 'feeding' && data['feeding_type'] == 'breast')) {
      final startTime = data['startTime'] as DateTime? ?? now;
      final endTime = data['endTime'] as DateTime?;
      final durationMinutes = data['duration'] as double?;
      
      if (endTime != null && startTime != null) {
        activityDuration = endTime.difference(startTime);
      } else if (durationMinutes != null && durationMinutes > 0) {
        activityDuration = Duration(minutes: durationMinutes.round());
      }
    }

    // Build data map with conditional quantity for feeding types
    final activityData = <String, dynamic>{
      'quality': data['quality'] ?? 'good',
    };
    
    // Only include quantity for bottle feeding, not for breast or solid feeding
    if (type != 'feeding' || (data['feeding_type'] == 'bottle' && data['amount'] != null)) {
      // For medicine, use the parsed numeric quantity, not the string dosage
      if (type == 'medicine') {
        activityData['quantity'] = data['quantity']; // Use numeric quantity
        activityData['unit'] = data['unit'];
      } else {
        activityData['quantity'] = data['amount'] ?? data['volume'] ?? data['quantity'];
        activityData['unit'] = data['unit'] ?? 'ml';
      }
    }

    return ActivityLog(
      id: Uuid().v4(),
      babyId: babyId,
      type: ActivityType.values.firstWhere((e) => e.name == type),
      timestamp: data['startTime'] as DateTime? ?? now,
      endTime: data['endTime'] as DateTime? ?? (activityDuration != null ? (data['startTime'] as DateTime? ?? now).add(activityDuration) : null),
      data: activityData,
      notes: data['notes'],
      details: details.isNotEmpty ? details : null,
      metadata: {'source': 'quick_log'},
      createdAt: now,
    );
  }

  Map<String, dynamic> toRecentActivityMap() {
    // Base activity data
    final Map<String, dynamic> activityMap = {
      'id': id,
      'timestamp': timestamp,
      'type': type.name,
      'notes': notes,
    };

    // Activity-specific details
    switch (type) {
      case ActivityType.feeding:
        activityMap['title'] = 'Feeding';
        activityMap['icon'] = 'restaurant';
        // Color will be set at the end using centralized config
        
        // Build comprehensive feeding details
        final List<String> feedingDetails = [];
        
        // Add feeding type (bottle, breast, solid)
        if (details != null && details!['feeding_type'] != null && details!['feeding_type'] != '') {
          feedingDetails.add(details!['feeding_type']);
        }
        
        // Add quantity/amount information (only for bottle feeding)
        if (data['quantity'] != null && data['quantity'] > 0) {
          final unit = data['unit'] ?? 'ml';
          // Only show amount for bottle feeding, not for solid or breast feeding
          if (details != null && details!['feeding_type'] == 'bottle') {
            feedingDetails.add('${data['quantity']}$unit');
          }
        }
        
        // Add formula type or food items
        if (details != null) {
          if (details!['formula_type'] != null && details!['formula_type'] != '') {
            feedingDetails.add(details!['formula_type']);
          }
          if (details!['meal_type'] != null && details!['meal_type'] != '') {
            feedingDetails.add(details!['meal_type']);
          }
          if (details!['food_items'] != null && details!['food_items'] != '') {
            feedingDetails.add(details!['food_items']);
          }
        }
        
        // Add duration for breast feeding if available (from endTime calculation or details)
        if (details != null && details!['feeding_type'] == 'breast') {
          if (duration != null) {
            final minutes = duration!.inMinutes;
            feedingDetails.add('${minutes}min');
          }
          
          // Add side with proper display text for breast feeding
          if (details!['side'] != null && details!['side'] != '') {
            String sideDisplay = details!['side'];
            if (sideDisplay == 'both') {
              sideDisplay = 'Left & Right';
            } else if (sideDisplay == 'left') {
              sideDisplay = 'Left';
            } else if (sideDisplay == 'right') {
              sideDisplay = 'Right';
            }
            feedingDetails.add('Side: $sideDisplay');
          }
        }
        
        // Set the formatted details
        if (feedingDetails.isNotEmpty) {
          activityMap['type_detail'] = feedingDetails.join(', ');
        }
        break;
      case ActivityType.sleep:
        activityMap['title'] = 'Sleep';
        activityMap['icon'] = 'bedtime';
        // Color will be set at the end using centralized config
        
        // Build comprehensive sleep details (without duplicating duration)
        final List<String> sleepDetails = [];
        
        // Add sleep quality
        if (details != null && details!['sleep_quality'] != null && details!['sleep_quality'] != '') {
          sleepDetails.add('Quality: ${details!['sleep_quality']}');
        }
        
        // Add sleep location
        if (details != null && details!['sleep_location'] != null && details!['sleep_location'] != '') {
          sleepDetails.add('Location: ${details!['sleep_location']}');
        }
        
        // Add room temperature
        if (details != null && details!['room_temperature'] != null && details!['room_temperature'] != '') {
          sleepDetails.add('Temp: ${details!['room_temperature']}°C');
        }
        
        // Add duration (only once, not duplicated)
        if (duration != null) {
          final hours = duration!.inHours;
          final minutes = duration!.inMinutes.remainder(60);
          if (hours > 0) {
            sleepDetails.add('${hours}h ${minutes}m');
          } else {
            sleepDetails.add('${minutes}m');
          }
        }
        
        // Set the formatted details
        if (sleepDetails.isNotEmpty) {
          activityMap['type_detail'] = sleepDetails.join(', ');
        }
        break;
      case ActivityType.diaper:
        activityMap['title'] = 'Diaper';
        activityMap['icon'] = 'child_care';
        // Color will be set at the end using centralized config
        if (details != null) {
          // Transform 'both' to 'Wet & Dirty' for display
          final diaperType = details!['diaper_type'];
          final displayType = diaperType == 'both' ? 'Wet & Dirty' : diaperType;
          activityMap['diaper_type'] = displayType;
          // Set diaper type as the detail for display
          if (displayType != null && displayType != '') {
            activityMap['type_detail'] = displayType;
          }
        }
        break;
      case ActivityType.medicine:
        activityMap['title'] = 'Medicine';
        activityMap['icon'] = 'medication';
        // Color will be set at the end using centralized config
        if (details != null) {
          activityMap['medication'] = details!['medication'];
          activityMap['dosage'] = details!['dosage'];
          // Build medicine type_detail from medication name and dosage
          final List<String> medicineDetails = [];
          if (details!['medication'] != null && details!['medication'] != '') {
            medicineDetails.add(details!['medication']);
          }
          if (details!['dosage'] != null && details!['dosage'] != '') {
            medicineDetails.add(details!['dosage']);
          }
          if (medicineDetails.isNotEmpty) {
            activityMap['type_detail'] = medicineDetails.join(', ');
          }
        }
        break;
      case ActivityType.vaccination:
        activityMap['title'] = 'Vaccination';
        activityMap['icon'] = 'vaccines';
        // Color will be set at the end using centralized config
        if (details != null) {
          activityMap['vaccine'] = details!['vaccine'];
          activityMap['batch_number'] = details!['batch_number'];
          activityMap['provider'] = details!['provider'];
          activityMap['location'] = details!['location'];
          // Set vaccine name as the detail for display
          if (details!['vaccine'] != null) {
            activityMap['type_detail'] = details!['vaccine'];
          }
        }
        break;
      case ActivityType.milestone:
        activityMap['title'] = 'Milestone';
        activityMap['icon'] = 'emoji_events';
        // Color will be set at the end using centralized config
        if (details != null) {
          activityMap['milestone_title'] = details!['milestone_title'];
          activityMap['milestone_description'] = details!['milestone_description'];
          activityMap['milestone_category'] = details!['milestone_category'];
          activityMap['milestone_type'] = details!['milestone_type'];
          activityMap['age_in_months'] = details!['age_in_months'];
          activityMap['age_in_days'] = details!['age_in_days'];
          activityMap['is_custom'] = details!['is_custom'];
          
          // Build comprehensive milestone type_detail
          final List<String> milestoneDetails = [];
          
          // Add milestone title first
          if (details!['milestone_title'] != null && details!['milestone_title'] != '') {
            milestoneDetails.add(details!['milestone_title']);
          }
          
          // Add milestone description
          if (details!['milestone_description'] != null && details!['milestone_description'].toString().isNotEmpty) {
            milestoneDetails.add(details!['milestone_description']);
          }
          
          // Add category
          if (details!['milestone_category'] != null && details!['milestone_category'] != '') {
            milestoneDetails.add('Category: ${details!['milestone_category']}');
          }
          
          // Add age information
          if (details!['age_in_months'] != null && details!['age_in_days'] != null) {
            final months = details!['age_in_months'];
            final days = details!['age_in_days'] % 30; // Remaining days after months
            milestoneDetails.add('Age: ${months}m ${days}d');
          }
          
          if (milestoneDetails.isNotEmpty) {
            activityMap['type_detail'] = milestoneDetails.join(', ');
          }
        }
        break;
      case ActivityType.tummy_time:
        activityMap['title'] = 'Tummy Time';
        activityMap['icon'] = 'fitness_center';
        // Color will be set at the end using centralized config
        
        // Build comprehensive tummy time details
        final List<String> tummyTimeDetails = [];
        
        // Add activity
        if (details != null && details!['activity'] != null && details!['activity'] != '') {
          tummyTimeDetails.add('Activity: ${details!['activity']}');
        }
        
        // Add position
        if (details != null && details!['position'] != null && details!['position'] != '') {
          tummyTimeDetails.add('Position: ${details!['position']}');
        }
        
        // Add mood
        if (details != null && details!['mood'] != null && details!['mood'] != '') {
          tummyTimeDetails.add('Mood: ${details!['mood']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          tummyTimeDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (tummyTimeDetails.isNotEmpty) {
          activityMap['type_detail'] = tummyTimeDetails.join(', ');
        }
        break;
      case ActivityType.bath:
        activityMap['title'] = 'Bath';
        activityMap['icon'] = 'bathtub';
        // Color will be set at the end using centralized config
        break;
      case ActivityType.story_time:
        activityMap['title'] = 'Story Time';
        activityMap['icon'] = 'menu_book';
        // Color will be set at the end using centralized config
        
        // Build comprehensive story time details
        final List<String> storyTimeDetails = [];
        
        // Add book type
        if (details != null && details!['book_type'] != null && details!['book_type'] != '') {
          storyTimeDetails.add('Book: ${details!['book_type']}');
        }
        
        // Add activity
        if (details != null && details!['activity'] != null && details!['activity'] != '') {
          storyTimeDetails.add('Activity: ${details!['activity']}');
        }
        
        // Add engagement
        if (details != null && details!['engagement'] != null && details!['engagement'] != '') {
          storyTimeDetails.add('Engagement: ${details!['engagement']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          storyTimeDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (storyTimeDetails.isNotEmpty) {
          activityMap['type_detail'] = storyTimeDetails.join(', ');
        }
        break;
      case ActivityType.pumping:
        activityMap['title'] = 'Pumping';
        activityMap['icon'] = 'local_drink';
        // Color will be set at the end using centralized config
        
        // Build comprehensive pumping details
        final List<String> pumpingDetails = [];
        
        // Add amount
        if (data['quantity'] != null && data['quantity'] > 0) {
          final unit = data['unit'] ?? 'ml';
          pumpingDetails.add('${data['quantity']}$unit');
        }
        
        // Add duration if available
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          pumpingDetails.add('${details!['duration']} min');
        }
        
        // Add side with proper display text
        if (details != null && details!['side'] != null && details!['side'] != '') {
          String sideDisplay = details!['side'];
          if (sideDisplay == 'both') {
            sideDisplay = 'Left & Right';
          } else if (sideDisplay == 'left') {
            sideDisplay = 'Left';
          } else if (sideDisplay == 'right') {
            sideDisplay = 'Right';
          }
          pumpingDetails.add('Side: $sideDisplay');
        }
        
        // Set the formatted details
        if (pumpingDetails.isNotEmpty) {
          activityMap['type_detail'] = pumpingDetails.join(', ');
        }
        break;
      case ActivityType.growth:
        activityMap['title'] = 'Growth';
        activityMap['icon'] = 'trending_up';
        // Color will be set at the end using centralized config
        break;
      case ActivityType.outdoor_play:
        activityMap['title'] = 'Outdoor Play';
        activityMap['icon'] = 'park';
        // Color will be set at the end using centralized config
        
        // Build comprehensive outdoor play details
        final List<String> outdoorPlayDetails = [];
        
        // Add activity type
        if (details != null && details!['activity'] != null && details!['activity'] != '') {
          outdoorPlayDetails.add('Activity: ${details!['activity']}');
        }
        
        // Add location
        if (details != null && details!['location'] != null && details!['location'] != '') {
          outdoorPlayDetails.add('Location: ${details!['location']}');
        }
        
        // Add weather
        if (details != null && details!['weather'] != null && details!['weather'] != '') {
          outdoorPlayDetails.add('Weather: ${details!['weather']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          outdoorPlayDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (outdoorPlayDetails.isNotEmpty) {
          activityMap['type_detail'] = outdoorPlayDetails.join(', ');
        }
        break;
      case ActivityType.indoor_play:
        activityMap['title'] = 'Indoor Play';
        activityMap['icon'] = 'toys';
        // Color will be set at the end using centralized config
        
        // Build comprehensive indoor play details
        final List<String> indoorPlayDetails = [];
        
        // Add activity type
        if (details != null && details!['activity'] != null && details!['activity'] != '') {
          indoorPlayDetails.add('Activity: ${details!['activity']}');
        }
        
        // Add location
        if (details != null && details!['location'] != null && details!['location'] != '') {
          indoorPlayDetails.add('Location: ${details!['location']}');
        }
        
        // Add toys/materials
        if (details != null && details!['toys'] != null && details!['toys'] != '') {
          indoorPlayDetails.add('Toys: ${details!['toys']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          indoorPlayDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (indoorPlayDetails.isNotEmpty) {
          activityMap['type_detail'] = indoorPlayDetails.join(', ');
        }
        break;
      case ActivityType.brush_teeth:
        activityMap['title'] = 'Brush Teeth';
        activityMap['icon'] = 'tooth';
        // Color will be set at the end using centralized config
        
        // Build comprehensive brush teeth details
        final List<String> brushTeethDetails = [];
        
        // Add toothbrush type
        if (details != null && details!['toothbrush'] != null && details!['toothbrush'] != '') {
          brushTeethDetails.add('Toothbrush: ${details!['toothbrush']}');
        }
        
        // Add toothpaste
        if (details != null && details!['toothpaste'] != null && details!['toothpaste'] != '') {
          brushTeethDetails.add('Toothpaste: ${details!['toothpaste']}');
        }
        
        // Add cooperation level
        if (details != null && details!['cooperation'] != null && details!['cooperation'] != '') {
          brushTeethDetails.add('Cooperation: ${details!['cooperation']}');
        }
        
        // Add quality
        if (details != null && details!['quality'] != null && details!['quality'] != '') {
          brushTeethDetails.add('Quality: ${details!['quality']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          brushTeethDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (brushTeethDetails.isNotEmpty) {
          activityMap['type_detail'] = brushTeethDetails.join(', ');
        }
        break;
      case ActivityType.photo:
        activityMap['title'] = 'Photo';
        activityMap['icon'] = 'camera_alt';
        // Color will be set at the end using centralized config
        break;
      case ActivityType.note:
        activityMap['title'] = 'Note';
        activityMap['icon'] = 'note';
        // Color will be set at the end using centralized config
        break;
      case ActivityType.temperature:
        activityMap['title'] = 'Temperature';
        activityMap['icon'] = 'thermostat';
        // Color will be set at the end using centralized config
        if (details != null) {
          activityMap['temperature_reading'] = details!['temperature_reading'];
          activityMap['measurement_method'] = details!['measurement_method'];
          activityMap['temperature_unit'] = details!['temperature_unit'];
          activityMap['temperature_status'] = details!['temperature_status'];
          
          // Build comprehensive temperature type_detail
          final List<String> tempDetails = [];
          if (details!['temperature_reading'] != null && details!['temperature_reading'] != '') {
            tempDetails.add(details!['temperature_reading']);
          }
          if (details!['temperature_status'] != null && details!['temperature_status'] != '') {
            tempDetails.add('(${details!['temperature_status']})');
          }
          if (details!['measurement_method'] != null && details!['measurement_method'] != '') {
            tempDetails.add(details!['measurement_method']);
          }
          if (tempDetails.isNotEmpty) {
            activityMap['type_detail'] = tempDetails.join(' - ');
          }
        }
        break;
      case ActivityType.potty:
        activityMap['title'] = 'Potty';
        activityMap['icon'] = 'wc';
        // Color will be set at the end using centralized config
        if (details != null) {
          activityMap['potty_type'] = details!['potty_type'];
          activityMap['success_level'] = details!['success_level'];
          activityMap['location'] = details!['location'];
          activityMap['assistance_needed'] = details!['assistance_needed'];
          
          // Build comprehensive potty type_detail
          final List<String> pottyDetails = [];
          if (details!['potty_type'] != null && details!['potty_type'] != '') {
            pottyDetails.add(details!['potty_type']);
          }
          if (details!['success_level'] != null && details!['success_level'] != '') {
            pottyDetails.add('(${details!['success_level']})');
          }
          if (details!['location'] != null && details!['location'] != '') {
            pottyDetails.add(details!['location']);
          }
          if (details!['assistance_needed'] == true) {
            pottyDetails.add('With Help');
          }
          if (pottyDetails.isNotEmpty) {
            activityMap['type_detail'] = pottyDetails.join(' - ');
          }
        }
        break;
      case ActivityType.mood:
        activityMap['title'] = 'Mood';
        activityMap['icon'] = 'mood';
        // Color will be set at the end using centralized config
        if (details != null) {
          activityMap['mood_type'] = details!['mood_type'];
          activityMap['mood_description'] = details!['mood_description'];
        }
        break;
      case ActivityType.custom:
        activityMap['title'] = 'Custom';
        activityMap['icon'] = 'add_circle';
        // Color will be set at the end using centralized config
        
        // Build comprehensive custom activity details
        final List<String> customDetails = [];
        
        // Add activity name
        if (details != null && details!['activity_name'] != null && details!['activity_name'] != '') {
          customDetails.add('${details!['activity_name']}');
        }
        
        // Add category if provided
        if (details != null && details!['category'] != null && details!['category'] != '') {
          customDetails.add('Category: ${details!['category']}');
        }
        
        // Set the formatted details
        if (customDetails.isNotEmpty) {
          activityMap['type_detail'] = customDetails.join(' - ');
        }
        
        // Mark as custom activity
        if (details != null && details!['is_custom'] == true) {
          activityMap['is_custom'] = details!['is_custom'];
        }
        break;
      case ActivityType.play:
        activityMap['title'] = 'Play';
        activityMap['icon'] = 'toys';
        // Color will be set at the end using centralized config
        break;
      case ActivityType.doctor:
        activityMap['title'] = 'Doctor Visit';
        activityMap['icon'] = 'local_hospital';
        // Color will be set at the end using centralized config
        break;
      case ActivityType.screen_time:
        activityMap['title'] = 'Screen Time';
        activityMap['icon'] = 'tv';
        // Color will be set at the end using centralized config
        
        // Build comprehensive screen time details
        final List<String> screenTimeDetails = [];
        
        // Add content
        if (details != null && details!['content'] != null && details!['content'] != '') {
          screenTimeDetails.add('Content: ${details!['content']}');
        }
        
        // Add device
        if (details != null && details!['device'] != null && details!['device'] != '') {
          screenTimeDetails.add('Device: ${details!['device']}');
        }
        
        // Add purpose
        if (details != null && details!['purpose'] != null && details!['purpose'] != '') {
          screenTimeDetails.add('Purpose: ${details!['purpose']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          screenTimeDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (screenTimeDetails.isNotEmpty) {
          activityMap['type_detail'] = screenTimeDetails.join(', ');
        }
        break;
      case ActivityType.skin_to_skin:
        activityMap['title'] = 'Skin to Skin';
        activityMap['icon'] = 'favorite';
        // Color will be set at the end using centralized config
        
        // Build comprehensive skin to skin details
        final List<String> skinToSkinDetails = [];
        
        // Add position
        if (details != null && details!['position'] != null && details!['position'] != '') {
          skinToSkinDetails.add('Position: ${details!['position']}');
        }
        
        // Add location
        if (details != null && details!['location'] != null && details!['location'] != '') {
          skinToSkinDetails.add('Location: ${details!['location']}');
        }
        
        // Add benefit
        if (details != null && details!['benefit'] != null && details!['benefit'] != '') {
          skinToSkinDetails.add('Benefit: ${details!['benefit']}');
        }
        
        // Add duration
        if (details != null && details!['duration'] != null && details!['duration'] != '') {
          skinToSkinDetails.add('Duration: ${details!['duration']} min');
        }
        
        // Set the formatted details
        if (skinToSkinDetails.isNotEmpty) {
          activityMap['type_detail'] = skinToSkinDetails.join(', ');
        }
        break;
    }

    // Add common details
    activityMap['amount'] = data['quantity'] != null ? '${data['quantity']} ${data['unit'] ?? ''}' : null;
    
    // Always use centralized configuration for consistency
    activityMap['icon'] = ActivityTypeConfig.getIcon(type.name);
    activityMap['color'] = ActivityTypeConfig.getColor(type.name);
    if (!activityMap.containsKey('type_detail')) {
      activityMap['type_detail'] = type.toDisplayString();
    }

    // Build specific type details if available
    if (type == ActivityType.feeding) {
      if (details != null) {
        activityMap['feeding_type'] = details!['feeding_type'];
        activityMap['formula_type'] = details!['formula_type'];
        activityMap['meal_type'] = details!['meal_type'];
        activityMap['food_items'] = details!['food_items'];
        activityMap['mood'] = details!['mood'];
      }
    } else if (type == ActivityType.diaper) {
      if (details != null) {
        // Transform 'both' to 'Wet & Dirty' for display
        final diaperType = details!['diaper_type'];
        activityMap['diaper_type'] = diaperType == 'both' ? 'Wet & Dirty' : diaperType;
      }
    } else if (type == ActivityType.sleep) {
      if (details != null) {
        activityMap['sleep_quality'] = details!['sleep_quality'];
        activityMap['sleep_location'] = details!['sleep_location'];
        activityMap['room_temperature'] = details!['room_temperature'];
        activityMap['noise_level'] = details!['noise_level'];
        activityMap['fed_before_sleep'] = details!['fed_before_sleep'];
      }
    } else if (type == ActivityType.milestone) {
      if (details != null) {
        activityMap['milestone_title'] = details!['milestone_title'];
        activityMap['milestone_description'] = details!['milestone_description'];
        activityMap['milestone_category'] = details!['milestone_category'];
        activityMap['milestone_type'] = details!['milestone_type'];
        activityMap['age_in_months'] = details!['age_in_months'];
        activityMap['age_in_days'] = details!['age_in_days'];
        activityMap['is_custom'] = details!['is_custom'];
      }
    } else if (type == ActivityType.medicine) {
      if (details != null) {
        activityMap['medication'] = details!['medication'];
        activityMap['dosage'] = details!['dosage'];
      }
    }

    return activityMap;
  }

  static IconData _getActivityIcon(ActivityType type) {
    switch (type) {
      case ActivityType.feeding:
        return Icons.restaurant;
      case ActivityType.sleep:
        return Icons.bedtime;
      case ActivityType.diaper:
        return Icons.child_care;
      case ActivityType.medicine:
        return Icons.medication;
      case ActivityType.vaccination:
        return Icons.vaccines;
      case ActivityType.temperature:
        return Icons.thermostat;
      case ActivityType.potty:
        return Icons.wc;
      case ActivityType.tummy_time:
        return Icons.fitness_center;
      case ActivityType.story_time:
        return Icons.menu_book;
      case ActivityType.screen_time:
        return Icons.tv;
      case ActivityType.skin_to_skin:
        return Icons.favorite;
      case ActivityType.bath:
        return Icons.bathtub;
      case ActivityType.outdoor_play:
        return Icons.park;
      case ActivityType.indoor_play:
        return Icons.toys;
      case ActivityType.brush_teeth:
        return Icons.cleaning_services; // Note: Icons.tooth doesn't exist, using cleaning_services
      case ActivityType.pumping:
        return Icons.local_drink;
      case ActivityType.growth:
        return Icons.trending_up;
      case ActivityType.milestone:
        return Icons.emoji_events;
      case ActivityType.custom:
        return Icons.add_circle;
      case ActivityType.play:
        return Icons.toys;
      case ActivityType.doctor:
        return Icons.local_hospital;
      case ActivityType.photo:
        return Icons.camera_alt;
      case ActivityType.note:
        return Icons.note;
      case ActivityType.mood:
        return Icons.mood;
      default:
        return Icons.add_circle;
    }
  }
  
  static String _getActivityIconString(ActivityType type) {
    switch (type) {
      case ActivityType.feeding:
        return 'restaurant';
      case ActivityType.sleep:
        return 'bedtime';
      case ActivityType.diaper:
        return 'child_care';
      case ActivityType.medicine:
        return 'medication';
      case ActivityType.vaccination:
        return 'vaccines';
      case ActivityType.temperature:
        return 'thermostat';
      case ActivityType.potty:
        return 'wc';
      case ActivityType.tummy_time:
        return 'fitness_center';
      case ActivityType.story_time:
        return 'menu_book';
      case ActivityType.screen_time:
        return 'tv';
      case ActivityType.skin_to_skin:
        return 'favorite';
      case ActivityType.bath:
        return 'bathtub';
      case ActivityType.outdoor_play:
        return 'park';
      case ActivityType.indoor_play:
        return 'toys';
      case ActivityType.brush_teeth:
        return 'tooth';
      case ActivityType.pumping:
        return 'local_drink';
      case ActivityType.growth:
        return 'trending_up';
      case ActivityType.milestone:
        return 'emoji_events';
      case ActivityType.custom:
        return 'add_circle';
      case ActivityType.play:
        return 'toys';
      case ActivityType.doctor:
        return 'local_hospital';
      case ActivityType.photo:
        return 'camera_alt';
      case ActivityType.note:
        return 'note';
      case ActivityType.mood:
        return 'mood';
      default:
        return 'add_circle';
    }
  }

  static Color _getActivityColor(ActivityType type) {
    // Use centralized configuration for consistent colors
    return ActivityTypeConfig.getColor(type.name);
  }
}
