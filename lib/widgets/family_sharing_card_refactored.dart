import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/family_member.dart';
import 'custom_elevated_button.dart';
import 'custom_icon_widget.dart';
import 'family_sharing_card/family_stats_widget.dart';
import 'family_sharing_card/family_member_avatar.dart';
import 'family_sharing_card/expandable_member_list.dart';
import 'family_sharing_card/family_sharing_card_states.dart';

/// Family sharing management card component
/// 
/// Features:
/// - Family member overview with count and status
/// - Expandable family member list with roles and activity
/// - Invite member functionality with permission checks
/// - Pending invitations display
/// - Family stats widget with visual indicators
/// - Loading and error states
/// - Responsive design with accessibility support
class FamilySharingCard extends StatelessWidget {
  /// List of current family members
  final List<FamilyMember> familyMembers;
  
  /// Number of pending invitations
  final int pendingInvitations;
  
  /// Callback when invite member button is tapped
  final VoidCallback? onInviteMember;
  
  /// Callback when a family member is tapped
  final Function(FamilyMember)? onMemberTap;
  
  /// Whether the current user can manage family members
  final bool canManageFamily;
  
  /// Whether the card is currently loading
  final bool isLoading;
  
  /// Callback when manage family is tapped
  final VoidCallback? onManageFamily;
  
  /// Maximum number of family members allowed
  final int maxFamilyMembers;
  
  /// Error message to display (if any)
  final String? errorMessage;
  
  /// Callback when retry is tapped in error state
  final VoidCallback? onRetry;

  const FamilySharingCard({
    super.key,
    required this.familyMembers,
    this.pendingInvitations = 0,
    this.onInviteMember,
    this.onMemberTap,
    this.canManageFamily = false,
    this.isLoading = false,
    this.onManageFamily,
    this.maxFamilyMembers = 10,
    this.errorMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    // Handle different states
    if (isLoading) {
      return const FamilySharingLoadingState();
    }
    
    if (errorMessage != null) {
      return FamilySharingErrorState(
        errorMessage: errorMessage,
        onRetry: onRetry,
      );
    }

    return _FamilySharingContent(
      familyMembers: familyMembers,
      pendingInvitations: pendingInvitations,
      onInviteMember: onInviteMember,
      onMemberTap: onMemberTap,
      canManageFamily: canManageFamily,
      onManageFamily: onManageFamily,
      maxFamilyMembers: maxFamilyMembers,
    );
  }
}

/// Main content widget for family sharing card
class _FamilySharingContent extends StatelessWidget {
  final List<FamilyMember> familyMembers;
  final int pendingInvitations;
  final VoidCallback? onInviteMember;
  final Function(FamilyMember)? onMemberTap;
  final bool canManageFamily;
  final VoidCallback? onManageFamily;
  final int maxFamilyMembers;

  const _FamilySharingContent({
    required this.familyMembers,
    required this.pendingInvitations,
    this.onInviteMember,
    this.onMemberTap,
    required this.canManageFamily,
    this.onManageFamily,
    required this.maxFamilyMembers,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _FamilySharingHeader(),
            SizedBox(height: 3.h),
            FamilyStatsWidget(familyMembers: familyMembers),
            SizedBox(height: 3.h),
            _MemberOverview(
              familyMembers: familyMembers,
              onMemberTap: onMemberTap,
              onInviteMember: onInviteMember,
            ),
            if (familyMembers.isNotEmpty) ...[
              SizedBox(height: 2.h),
              ExpandableMemberList(
                familyMembers: familyMembers,
                canManageFamily: canManageFamily,
                onMemberTap: onMemberTap,
              ),
            ],
            if (canManageFamily) ...[
              SizedBox(height: 3.h),
              _ActionButtons(
                familyMembers: familyMembers,
                maxFamilyMembers: maxFamilyMembers,
                onInviteMember: onInviteMember,
                onManageFamily: onManageFamily,
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Header section of the family sharing card
class _FamilySharingHeader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: CustomIconWidget(
            iconName: 'family_restroom',
            color: theme.colorScheme.primary,
            size: 6.w,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Family Sharing',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                'Manage family members and sharing',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Member overview section with avatars
class _MemberOverview extends StatelessWidget {
  final List<FamilyMember> familyMembers;
  final Function(FamilyMember)? onMemberTap;
  final VoidCallback? onInviteMember;

  const _MemberOverview({
    required this.familyMembers,
    this.onMemberTap,
    this.onInviteMember,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    if (familyMembers.isEmpty) {
      return FamilySharingEmptyState(onInviteMember: onInviteMember);
    }

    final displayMembers = familyMembers.take(5).toList();
    final remainingCount = familyMembers.length - displayMembers.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Family Members',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        Row(
          children: [
            ...displayMembers.map((member) => Padding(
              padding: EdgeInsets.only(right: 2.w),
              child: FamilyMemberAvatar(
                member: member,
                onTap: () => onMemberTap?.call(member),
              ),
            )),
            if (remainingCount > 0)
              _RemainingMembersIndicator(count: remainingCount),
          ],
        ),
      ],
    );
  }
}

/// Indicator showing remaining family members count
class _RemainingMembersIndicator extends StatelessWidget {
  final int count;

  const _RemainingMembersIndicator({required this.count});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Container(
          width: 15.w,
          height: 15.w,
          decoration: BoxDecoration(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
            shape: BoxShape.circle,
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              '+$count',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ),
        SizedBox(height: 1.h),
        SizedBox(
          width: 15.w,
          child: Text(
            'more',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }
}

/// Action buttons section
class _ActionButtons extends StatelessWidget {
  final List<FamilyMember> familyMembers;
  final int maxFamilyMembers;
  final VoidCallback? onInviteMember;
  final VoidCallback? onManageFamily;

  const _ActionButtons({
    required this.familyMembers,
    required this.maxFamilyMembers,
    this.onInviteMember,
    this.onManageFamily,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final canInviteMore = familyMembers.length < maxFamilyMembers;
    
    return Row(
      children: [
        if (canInviteMore)
          Expanded(
            child: CustomElevatedButton(
              text: 'Invite Member',
              onPressed: onInviteMember,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 2.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: CustomIconWidget(
                iconName: 'person_add',
                color: theme.colorScheme.onPrimary,
                size: 5.w,
              ),
            ),
          ),
        if (canInviteMore && onManageFamily != null)
          SizedBox(width: 3.w),
        if (onManageFamily != null)
          Expanded(
            child: CustomElevatedButton(
              text: 'Manage Family',
              onPressed: onManageFamily,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.surface,
                foregroundColor: theme.colorScheme.primary,
                padding: EdgeInsets.symmetric(vertical: 2.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: theme.colorScheme.primary,
                  ),
                ),
              ),
              icon: CustomIconWidget(
                iconName: 'settings',
                color: theme.colorScheme.primary,
                size: 5.w,
              ),
            ),
          ),
      ],
    );
  }
}