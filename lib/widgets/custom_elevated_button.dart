import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

/// Custom elevated button widget with consistent styling
class CustomElevatedButton extends StatelessWidget {
  final VoidCallback? onPressed;
  final Widget? child;
  final String? text;
  final Widget? icon;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final EdgeInsetsGeometry? padding;
  final double? borderRadius;
  final bool isLoading;
  final bool isEnabled;
  final ButtonStyle? style;

  const CustomElevatedButton({
    super.key,
    this.onPressed,
    this.child,
    this.text,
    this.icon,
    this.backgroundColor,
    this.foregroundColor,
    this.padding,
    this.borderRadius,
    this.isLoading = false,
    this.isEnabled = true,
    this.style,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    final Widget buttonChild = isLoading
        ? Row(
            mainAxisAlignment: MainAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 3.5.w,
                height: 3.5.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  color: foregroundColor ?? theme.colorScheme.onPrimary,
                ),
              ),
              SizedBox(width: 1.5.w),
              Flexible(
                child: Text(
                  'Loading...',
                  style: TextStyle(
                    color: foregroundColor ?? theme.colorScheme.onPrimary,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          )
        : child ??
            (icon != null
                ? Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      icon!,
                      if (text != null) SizedBox(width: 1.5.w),
                      if (text != null)
                        Flexible(
                          child: Text(
                            text!,
                            style: TextStyle(
                              color: foregroundColor ?? theme.colorScheme.onPrimary,
                              fontWeight: FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                    ],
                  )
                : Text(
                    text ?? '',
                    style: TextStyle(
                      color: foregroundColor ?? theme.colorScheme.onPrimary,
                      fontWeight: FontWeight.w600,
                    ),
                  ));

    return ElevatedButton(
      onPressed: isEnabled && !isLoading ? onPressed : null,
      style: style ??
          ElevatedButton.styleFrom(
            backgroundColor: backgroundColor ?? theme.colorScheme.primary,
            foregroundColor: foregroundColor ?? theme.colorScheme.onPrimary,
            padding: padding ?? EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(borderRadius ?? 12),
            ),
            elevation: 2,
            shadowColor: (backgroundColor ?? theme.colorScheme.primary).withOpacity(0.3),
          ),
      child: buttonChild,
    );
  }
}
