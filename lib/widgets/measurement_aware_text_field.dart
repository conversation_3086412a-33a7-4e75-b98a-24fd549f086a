import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../services/measurement_units_service.dart';
import '../services/unit_conversion_service.dart';

/// A text field that automatically converts between metric and imperial units
/// while maintaining the underlying metric value for storage
class MeasurementAwareTextField extends StatefulWidget {
  final String measurementType; // 'weight', 'height', 'head_circumference'
  final double? initialMetricValue; // Always in metric (kg, cm)
  final Function(double?) onMetricValueChanged; // Callback with metric value
  final String? label;
  final String? hintText;
  final bool isRequired;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;

  const MeasurementAwareTextField({
    super.key,
    required this.measurementType,
    required this.onMetricValueChanged,
    this.initialMetricValue,
    this.label,
    this.hintText,
    this.isRequired = false,
    this.keyboardType = TextInputType.number,
    this.inputFormatters,
  });

  @override
  State<MeasurementAwareTextField> createState() => _MeasurementAwareTextFieldState();
}

class _MeasurementAwareTextFieldState extends State<MeasurementAwareTextField> {
  late TextEditingController _controller;
  late MeasurementUnitsService _unitsService;
  bool _isInitialized = false;
  double? _currentMetricValue;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _currentMetricValue = widget.initialMetricValue;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _unitsService = context.watch<MeasurementUnitsService>();
    
    if (!_isInitialized) {
      _updateDisplayValue();
      _isInitialized = true;
    } else {
      // Units changed, update display value
      _updateDisplayValue();
    }
  }

  void _updateDisplayValue() {
    if (_currentMetricValue != null) {
      final displayValue = _unitsService.convertFromMetricForDisplay(
        _currentMetricValue!,
        widget.measurementType,
      );
      _controller.text = displayValue.toStringAsFixed(1);
    }
  }

  void _onTextChanged(String value) {
    if (value.isEmpty) {
      _currentMetricValue = null;
      widget.onMetricValueChanged(null);
      return;
    }

    final displayValue = double.tryParse(value);
    if (displayValue != null) {
      // Convert display value to metric for storage
      final unit = _unitsService.getUnitForMeasurementType(widget.measurementType);
      _currentMetricValue = _unitsService.convertToMetricForStorage(
        displayValue,
        unit,
        widget.measurementType,
      );
      widget.onMetricValueChanged(_currentMetricValue);
    }
  }

  String get _currentUnit => _unitsService.getUnitForMeasurementType(widget.measurementType);
  
  String get _hintText {
    if (widget.hintText != null) return widget.hintText!;
    return 'Enter ${widget.measurementType.replaceAll('_', ' ')} in $_currentUnit';
  }

  String? _validator(String? value) {
    if (widget.isRequired && (value == null || value.isEmpty)) {
      return '${widget.label ?? widget.measurementType} is required';
    }
    
    if (value != null && value.isNotEmpty) {
      final numValue = double.tryParse(value);
      if (numValue == null) {
        return 'Please enter a valid number';
      }
      
      final range = _getValidationRange();
      if (numValue < range['min']! || numValue > range['max']!) {
        return _unitsService.getValidationMessage(widget.measurementType);
      }
    }
    
    return null;
  }

  Map<String, double> _getValidationRange() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return _unitsService.getWeightValidationRange();
      case 'head_circumference':
        return _unitsService.getHeadCircumferenceValidationRange();
      default:
        return _unitsService.getLengthValidationRange();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: _controller,
      onChanged: _onTextChanged,
      validator: _validator,
      keyboardType: widget.keyboardType,
      inputFormatters: widget.inputFormatters ?? [
        FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
      ],
      decoration: InputDecoration(
        labelText: widget.label,
        hintText: _hintText,
        suffixText: _currentUnit,
        filled: true,
        fillColor: Theme.of(context).colorScheme.surface,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.error,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Theme.of(context).colorScheme.error,
            width: 2,
          ),
        ),
        suffixStyle: TextStyle(
          color: Theme.of(context).colorScheme.primary,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}