import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/family_member.dart';
import 'custom_elevated_button.dart';
import 'custom_text_form_field.dart';
import 'custom_icon_widget.dart';

/// Dialog for inviting new family members
/// 
/// Features:
/// - Email validation and invitation sending
/// - Role selection with permission preview
/// - Custom message option
/// - Invitation status feedback
class FamilyInvitationDialog extends StatefulWidget {
  /// Callback when invitation is sent successfully
  final Function(String email, String role, Map<String, bool> permissions, String? message) onInvite;
  
  /// Whether the invitation is currently being sent
  final bool isLoading;
  
  /// Current number of family members (for limit checking)
  final int currentMemberCount;
  
  /// Maximum allowed family members
  final int maxMembers;

  const FamilyInvitationDialog({
    super.key,
    required this.onInvite,
    this.isLoading = false,
    this.currentMemberCount = 0,
    this.maxMembers = 10,
  });

  @override
  State<FamilyInvitationDialog> createState() => _FamilyInvitationDialogState();
}

class _FamilyInvitationDialogState extends State<FamilyInvitationDialog> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _messageController = TextEditingController();
  
  String _selectedRole = 'caregiver';
  Map<String, bool> _selectedPermissions = {};
  bool _showAdvancedOptions = false;
  
  // Available roles for invitation
  final List<Map<String, dynamic>> _availableRoles = [
    {
      'value': 'parent',
      'label': 'Parent',
      'description': 'Full access to manage babies and family',
      'icon': 'person',
    },
    {
      'value': 'caregiver',
      'label': 'Caregiver',
      'description': 'Can track activities and view insights',
      'icon': 'favorite',
    },
    {
      'value': 'grandparent',
      'label': 'Grandparent',
      'description': 'Can view activities and add basic entries',
      'icon': 'elderly',
    },
    {
      'value': 'babysitter',
      'label': 'Babysitter',
      'description': 'Limited access for temporary care',
      'icon': 'child_care',
    },
  ];

  @override
  void initState() {
    super.initState();
    _selectedPermissions = Map<String, bool>.from(
      FamilyMemberPermissions.getDefaultPermissions(_selectedRole)
    );
  }

  @override
  void dispose() {
    _emailController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 90.w,
          maxHeight: 85.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildHeader(context, theme),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(6.w),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMemberLimitInfo(context, theme),
                      SizedBox(height: 4.h),
                      _buildEmailField(context, theme),
                      SizedBox(height: 4.h),
                      _buildRoleSelection(context, theme),
                      SizedBox(height: 4.h),
                      _buildAdvancedOptionsToggle(context, theme),
                      if (_showAdvancedOptions) ...[
                        SizedBox(height: 3.h),
                        _buildPermissionsSection(context, theme),
                        SizedBox(height: 3.h),
                        _buildMessageField(context, theme),
                      ],
                    ],
                  ),
                ),
              ),
            ),
            _buildActionButtons(context, theme),
          ],
        ),
      ),
    );
  }

  /// Build dialog header
  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: CustomIconWidget(
              iconName: 'person_add',
              color: theme.colorScheme.primary,
              size: 6.w,
            ),
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Invite Family Member',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  'Add a new member to your family sharing',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: CustomIconWidget(
              iconName: 'close',
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              size: 6.w,
            ),
          ),
        ],
      ),
    );
  }

  /// Build member limit information
  Widget _buildMemberLimitInfo(BuildContext context, ThemeData theme) {
    final remainingSlots = widget.maxMembers - widget.currentMemberCount;
    final isNearLimit = remainingSlots <= 2;
    
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isNearLimit 
            ? theme.colorScheme.error.withValues(alpha: 0.05)
            : theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isNearLimit 
              ? theme.colorScheme.error.withValues(alpha: 0.2)
              : theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: isNearLimit ? 'warning' : 'info',
            color: isNearLimit ? theme.colorScheme.error : theme.colorScheme.primary,
            size: 5.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Family Members: ${widget.currentMemberCount}/${widget.maxMembers}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isNearLimit ? theme.colorScheme.error : theme.colorScheme.primary,
                  ),
                ),
                if (remainingSlots > 0) ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    '$remainingSlots ${remainingSlots == 1 ? 'slot' : 'slots'} remaining',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ] else ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    'Family is at maximum capacity',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build email input field
  Widget _buildEmailField(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Email Address',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        CustomTextFormField(
          controller: _emailController,
          hintText: 'Enter email address',
          keyboardType: TextInputType.emailAddress,
          prefixIcon: CustomIconWidget(
            iconName: 'email',
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            size: 5.w,
          ),
          validator: _validateEmail,
          enabled: !widget.isLoading,
        ),
      ],
    );
  }

  /// Build role selection
  Widget _buildRoleSelection(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Role & Permissions',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        ..._availableRoles.map((role) => _buildRoleOption(context, theme, role)),
      ],
    );
  }

  /// Build individual role option
  Widget _buildRoleOption(BuildContext context, ThemeData theme, Map<String, dynamic> role) {
    final isSelected = _selectedRole == role['value'];
    
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: InkWell(
        onTap: widget.isLoading ? null : () => _selectRole(role['value']),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withValues(alpha: 0.2),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: isSelected 
                      ? theme.colorScheme.primary.withValues(alpha: 0.1)
                      : theme.colorScheme.outline.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: role['icon'],
                  color: isSelected 
                      ? theme.colorScheme.primary
                      : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  size: 5.w,
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      role['label'],
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isSelected 
                            ? theme.colorScheme.primary
                            : theme.colorScheme.onSurface,
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      role['description'],
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              if (isSelected)
                CustomIconWidget(
                  iconName: 'check_circle',
                  color: theme.colorScheme.primary,
                  size: 5.w,
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build advanced options toggle
  Widget _buildAdvancedOptionsToggle(BuildContext context, ThemeData theme) {
    return InkWell(
      onTap: widget.isLoading ? null : () {
        setState(() {
          _showAdvancedOptions = !_showAdvancedOptions;
        });
      },
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 3.w),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: 'tune',
              color: theme.colorScheme.primary,
              size: 5.w,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                'Advanced Options',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            AnimatedRotation(
              turns: _showAdvancedOptions ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: CustomIconWidget(
                iconName: 'expand_more',
                color: theme.colorScheme.primary,
                size: 5.w,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build permissions section
  Widget _buildPermissionsSection(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Permissions',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        Container(
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: _selectedPermissions.entries.map((entry) {
              return _buildPermissionToggle(context, theme, entry.key, entry.value);
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// Build individual permission toggle
  Widget _buildPermissionToggle(BuildContext context, ThemeData theme, String permission, bool value) {
    final permissionLabels = {
      'view_activities': 'View Activities',
      'add_activities': 'Add Activities',
      'edit_activities': 'Edit Activities',
      'delete_activities': 'Delete Activities',
      'manage_babies': 'Manage Babies',
      'manage_family': 'Manage Family',
      'view_insights': 'View AI Insights',
      'export_data': 'Export Data',
      'manage_settings': 'Manage Settings',
    };
    
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              permissionLabels[permission] ?? permission,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: widget.isLoading ? null : (newValue) {
              setState(() {
                _selectedPermissions[permission] = newValue;
              });
            },
            activeColor: theme.colorScheme.primary,
          ),
        ],
      ),
    );
  }

  /// Build custom message field
  Widget _buildMessageField(BuildContext context, ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Message (Optional)',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        CustomTextFormField(
          controller: _messageController,
          hintText: 'Add a personal message to the invitation...',
          maxLines: 3,
          enabled: !widget.isLoading,
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context, ThemeData theme) {
    final canInvite = widget.currentMemberCount < widget.maxMembers;
    
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: CustomElevatedButton(
              text: 'Cancel',
              onPressed: widget.isLoading ? null : () => Navigator.of(context).pop(),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.surface,
                foregroundColor: theme.colorScheme.onSurface,
                padding: EdgeInsets.symmetric(vertical: 2.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: CustomElevatedButton(
              text: widget.isLoading ? 'Sending...' : 'Send Invitation',
              onPressed: canInvite && !widget.isLoading ? _sendInvitation : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 2.h),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              icon: widget.isLoading 
                  ? SizedBox(
                      width: 5.w,
                      height: 5.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          theme.colorScheme.onPrimary,
                        ),
                      ),
                    )
                  : CustomIconWidget(
                      iconName: 'send',
                      color: theme.colorScheme.onPrimary,
                      size: 5.w,
                    ),
            ),
          ),
        ],
      ),
    );
  }

  /// Validate email address
  String? _validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email address is required';
    }
    
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(value)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Select a role and update permissions
  /// 
  /// Updates the selected role and resets permissions to the default
  /// permissions for that role. This ensures consistency between
  /// role selection and permission settings.
  /// 
  /// [role] The role identifier to select
  void _selectRole(String role) {
    setState(() {
      _selectedRole = role;
      _selectedPermissions = Map<String, bool>.from(
        FamilyMemberPermissions.getDefaultPermissions(role)
      );
    });
  }

  /// Send the invitation
  void _sendInvitation() {
    if (!_formKey.currentState!.validate()) return;
    
    try {
      final email = _emailController.text.trim();
      final message = _messageController.text.trim();
      
      widget.onInvite(
        email,
        _selectedRole,
        _selectedPermissions,
        message.isEmpty ? null : message,
      );
    } catch (e) {
      // Handle invitation sending errors
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send invitation: ${e.toString()}'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }
}