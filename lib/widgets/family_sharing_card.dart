import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/family_member.dart';
import '../models/enums.dart';
import '../theme/theme_aware_colors.dart';
import '../theme/ui_constants.dart';
import '../utils/accessibility_helper.dart';
import 'user_avatar_widget.dart';
import 'custom_elevated_button.dart';
import 'custom_icon_widget.dart';
import 'account_profile_theme_helper.dart';
import 'shared/animated_profile_widgets.dart';
import 'family_sharing_card/family_stats_widget.dart';
import 'family_sharing_card/family_member_avatar.dart';
import 'family_sharing_card/expandable_member_list.dart';
import 'family_sharing_card/family_sharing_card_states.dart';
import 'family_invitation_dialog.dart';
import 'invitation_status_widget.dart';

/// Family sharing management card component
/// 
/// Features:
/// - Family member overview with count and status
/// - Expandable family member list with roles and activity
/// - Invite member functionality with permission checks
/// - Pending invitations display
/// - Family stats widget with visual indicators
class FamilySharingCard extends StatefulWidget {
  /// List of current family members
  final List<FamilyMember> familyMembers;
  
  /// Number of pending invitations
  final int pendingInvitations;
  
  /// Callback when invite member button is tapped
  final VoidCallback? onInviteMember;
  
  /// Callback when invitation is sent
  final Function(String email, String role, Map<String, bool> permissions, String? message)? onSendInvitation;
  
  /// Callback when invitation is resent
  final Function(FamilyMember)? onResendInvitation;
  
  /// Callback when invitation is cancelled
  final Function(FamilyMember)? onCancelInvitation;
  
  /// Callback when a family member is tapped
  final Function(FamilyMember)? onMemberTap;
  
  /// Whether the current user can manage family members
  final bool canManageFamily;
  
  /// Whether the card is currently loading
  final bool isLoading;
  
  /// Callback when manage family is tapped
  final VoidCallback? onManageFamily;
  
  /// Maximum number of family members allowed
  final int maxFamilyMembers;

  const FamilySharingCard({
    super.key,
    required this.familyMembers,
    this.pendingInvitations = 0,
    this.onInviteMember,
    this.onSendInvitation,
    this.onResendInvitation,
    this.onCancelInvitation,
    this.onMemberTap,
    this.canManageFamily = false,
    this.isLoading = false,
    this.onManageFamily,
    this.maxFamilyMembers = 10,
  });

  @override
  State<FamilySharingCard> createState() => _FamilySharingCardState();
}

class _FamilySharingCardState extends State<FamilySharingCard>
    with TickerProviderStateMixin {
  bool _isInviting = false;
  String? _processingInvitationId;
  bool _isExpanded = false;
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return const FamilySharingLoadingState();
    }

    final theme = Theme.of(context);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    final pendingInvitations = widget.familyMembers.where((m) => m.isPending).toList();
    
    return Column(
      children: [
        AccessibilityHelper.createAccessibleCard(
          title: 'Family sharing management',
          subtitle: '${widget.familyMembers.length} family members',
          headingLevel: 2,
          child: AnimatedProfileWidgets.animatedCard(
            context: context,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildHeader(context, theme),
                SizedBox(height: spacing.sectionSpacing),
                FamilyStatsWidget(familyMembers: widget.familyMembers),
                SizedBox(height: spacing.sectionSpacing),
                _buildMemberOverview(context, theme),
                if (widget.familyMembers.isNotEmpty) ...[
                  SizedBox(height: spacing.elementSpacing),
                  ExpandableMemberList(
                    familyMembers: widget.familyMembers,
                    canManageFamily: widget.canManageFamily,
                    onMemberTap: widget.onMemberTap,
                  ),
                ],
                if (widget.canManageFamily) ...[
                  SizedBox(height: spacing.sectionSpacing),
                  _buildActionButtons(context, theme),
                ],
              ],
            ),
          ),
        ),
        // Show pending invitations if any
        if (pendingInvitations.isNotEmpty && widget.canManageFamily)
          AnimatedProfileWidgets.animatedListItem(
            context: context,
            index: 0,
            child: InvitationStatusWidget(
              pendingInvitations: pendingInvitations,
              onResendInvitation: _handleResendInvitation,
              onCancelInvitation: _handleCancelInvitation,
              isLoading: widget.isLoading,
              processingInvitationId: _processingInvitationId,
            ),
          ),
      ],
    );
  }

  /// Build card header with enhanced styling
  Widget _buildHeader(BuildContext context, ThemeData theme) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return Row(
      children: [
        AnimatedContainer(
          duration: UIConstants.shortAnimation,
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: CustomIconWidget(
            iconName: 'family_restroom',
            color: ThemeAwareColors.getPrimaryColor(context),
            size: 6.w,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Semantics(
                header: true,
                child: Text(
                  'Family Sharing',
                  style: AccountProfileThemeHelper.getResponsiveTextStyle(
                    context,
                    theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: ThemeAwareColors.getPrimaryTextColor(context),
                    ) ?? const TextStyle(),
                  ),
                ),
              ),
              SizedBox(height: spacing.elementSpacing * 0.5),
              Text(
                'Manage family members and sharing',
                style: AccountProfileThemeHelper.getResponsiveTextStyle(
                  context,
                  theme.textTheme.bodyMedium?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ) ?? const TextStyle(),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }



  /// Build member overview with avatars
  Widget _buildMemberOverview(BuildContext context, ThemeData theme) {
    if (widget.familyMembers.isEmpty) {
      return FamilySharingEmptyState(onInviteMember: widget.onInviteMember);
    }

    final displayMembers = widget.familyMembers.take(5).toList();
    final remainingCount = widget.familyMembers.length - displayMembers.length;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Semantics(
          header: true,
          child: Text(
            'Family Members',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
        ),
        SizedBox(height: 2.h),
        Semantics(
          label: 'Family members list, ${widget.familyMembers.length} total members',
          child: Row(
            children: [
              ...displayMembers.asMap().entries.map((entry) {
                final index = entry.key;
                final member = entry.value;
                return Padding(
                  padding: EdgeInsets.only(right: 2.w),
                  child: Semantics(
                    label: AccessibilityHelper.generateFamilyMemberSemanticLabel(
                      memberName: member.fullName,
                      role: AccessibilityHelper.formatRoleForAccessibility(member.role),
                      status: member.status.name,
                      lastActivity: member.lastActiveAt != null 
                          ? AccessibilityHelper.formatDateForAccessibility(member.lastActiveAt!)
                          : null,
                    ),
                    button: true,
                    child: FamilyMemberAvatar(
                      member: member,
                      onTap: () => widget.onMemberTap?.call(member),
                    ),
                  ),
                );
              }),
              if (remainingCount > 0)
                _buildRemainingCount(context, theme, remainingCount),
            ],
          ),
        ),
      ],
    );
  }

  /// Build member avatar
  Widget _buildMemberAvatar(BuildContext context, ThemeData theme, FamilyMember member) {
    return GestureDetector(
      onTap: () => widget.onMemberTap?.call(member),
      child: Column(
        children: [
          Stack(
            children: [
              UserAvatarWidget(
                imageUrl: member.avatarUrl,
                initials: member.initials,
                role: member.role,
                size: UserAvatarSizes.medium,
                onTap: () => widget.onMemberTap?.call(member),
              ),
              if (member.isPending)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    padding: EdgeInsets.all(1.w),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.secondary,
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: theme.scaffoldBackgroundColor,
                        width: 2,
                      ),
                    ),
                    child: CustomIconWidget(
                      iconName: 'schedule',
                      color: theme.colorScheme.onSecondary,
                      size: 3.w,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 1.h),
          SizedBox(
            width: 15.w,
            child: Text(
              member.fullName.split(' ').first,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  /// Build remaining count indicator
  Widget _buildRemainingCount(BuildContext context, ThemeData theme, int count) {
    return Column(
      children: [
        Container(
          width: 15.w,
          height: 15.w,
          decoration: BoxDecoration(
            color: theme.colorScheme.outline.withValues(alpha: 0.1),
            shape: BoxShape.circle,
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              '+$count',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ),
        ),
        SizedBox(height: 1.h),
        SizedBox(
          width: 15.w,
          child: Text(
            'more',
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState(BuildContext context, ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'group_add',
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            size: 12.w,
          ),
          SizedBox(height: 2.h),
          Text(
            'No Family Members Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Invite family members to share baby care responsibilities',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// Build expand/collapse toggle
  Widget _buildExpandToggle(BuildContext context, ThemeData theme) {
    return GestureDetector(
      onTap: _toggleExpanded,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              _isExpanded ? 'Show Less' : 'View All Members',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.primary,
              ),
            ),
            SizedBox(width: 2.w),
            AnimatedRotation(
              turns: _isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: CustomIconWidget(
                iconName: 'expand_more',
                color: theme.colorScheme.primary,
                size: 5.w,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build expandable content with detailed member list
  Widget _buildExpandableContent(BuildContext context, ThemeData theme) {
    return SizeTransition(
      sizeFactor: _expandAnimation,
      child: Column(
        children: [
          SizedBox(height: 2.h),
          ...widget.familyMembers.map((member) => _buildMemberListItem(context, theme, member)),
        ],
      ),
    );
  }

  /// Build detailed member list item
  Widget _buildMemberListItem(BuildContext context, ThemeData theme, FamilyMember member) {
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          UserAvatarWidget(
            imageUrl: member.avatarUrl,
            initials: member.initials,
            role: member.role,
            size: UserAvatarSizes.medium,
            onTap: () => widget.onMemberTap?.call(member),
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        member.fullName,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    _buildStatusBadge(context, theme, member),
                  ],
                ),
                SizedBox(height: 0.5.h),
                Text(
                  member.roleDisplayName,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  member.activityStatusText,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  ),
                ),
              ],
            ),
          ),
          if (widget.canManageFamily)
            CustomIconWidget(
              iconName: 'more_vert',
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              size: 5.w,
            ),
        ],
      ),
    );
  }

  /// Build status badge for member
  Widget _buildStatusBadge(BuildContext context, ThemeData theme, FamilyMember member) {
    Color badgeColor;
    String badgeText;
    
    switch (member.status) {
      case FamilyMemberStatus.active:
        badgeColor = theme.colorScheme.tertiary;
        badgeText = 'Active';
        break;
      case FamilyMemberStatus.pending:
        badgeColor = theme.colorScheme.secondary;
        badgeText = 'Pending';
        break;
      case FamilyMemberStatus.inactive:
        badgeColor = theme.colorScheme.outline;
        badgeText = 'Inactive';
        break;
      case FamilyMemberStatus.removed:
        badgeColor = theme.colorScheme.error;
        badgeText = 'Removed';
        break;
    }
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: badgeColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        badgeText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  /// Build action buttons with enhanced styling and animations
  Widget _buildActionButtons(BuildContext context, ThemeData theme) {
    final canInviteMore = widget.familyMembers.length < widget.maxFamilyMembers;
    
    return Row(
      children: [
        if (canInviteMore)
          Expanded(
            child: AnimatedProfileWidgets.animatedButton(
              context: context,
              onPressed: _isInviting ? null : _showInvitationDialog,
              isPrimary: true,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_isInviting)
                    SizedBox(
                      width: 4.w,
                      height: 4.w,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.7),
                        ),
                      ),
                    )
                  else
                    CustomIconWidget(
                      iconName: 'person_add',
                      color: Colors.white,
                      size: 4.w,
                    ),
                  SizedBox(width: 1.5.w),
                  Flexible(
                    child: Text(
                      _isInviting ? 'Inviting...' : 'Invite Member',
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
        if (canInviteMore && widget.onManageFamily != null)
          SizedBox(width: 3.w),
        if (widget.onManageFamily != null)
          Expanded(
            child: AnimatedProfileWidgets.animatedButton(
              context: context,
              onPressed: widget.onManageFamily,
              isPrimary: false,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  CustomIconWidget(
                    iconName: 'settings',
                    color: ThemeAwareColors.getPrimaryColor(context),
                    size: 4.w,
                  ),
                  SizedBox(width: 1.5.w),
                  Flexible(
                    child: Text(
                      'Manage Family',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        color: ThemeAwareColors.getPrimaryColor(context),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  /// Show invitation dialog
  void _showInvitationDialog() {
    if (widget.onSendInvitation == null) {
      // Fallback to old callback
      widget.onInviteMember?.call();
      return;
    }

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => FamilyInvitationDialog(
        onInvite: _handleSendInvitation,
        isLoading: _isInviting,
        currentMemberCount: widget.familyMembers.length,
        maxMembers: widget.maxFamilyMembers,
      ),
    );
  }

  /// Handle sending invitation
  void _handleSendInvitation(
    String email,
    String role,
    Map<String, bool> permissions,
    String? message,
  ) async {
    setState(() {
      _isInviting = true;
    });

    try {
      widget.onSendInvitation?.call(email, role, permissions, message);
      
      // Close dialog on success
      if (mounted) {
        Navigator.of(context).pop();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isInviting = false;
        });
      }
    }
  }

  /// Handle resending invitation
  void _handleResendInvitation(FamilyMember member) async {
    setState(() {
      _processingInvitationId = member.id;
    });

    try {
      widget.onResendInvitation?.call(member);
    } finally {
      if (mounted) {
        setState(() {
          _processingInvitationId = null;
        });
      }
    }
  }

  /// Handle cancelling invitation
  void _handleCancelInvitation(FamilyMember member) async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Invitation'),
        content: Text(
          'Are you sure you want to cancel the invitation for ${member.fullName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Keep'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('Cancel Invitation'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      setState(() {
        _processingInvitationId = member.id;
      });

      try {
        widget.onCancelInvitation?.call(member);
      } finally {
        if (mounted) {
          setState(() {
            _processingInvitationId = null;
          });
        }
      }
    }
  }
}