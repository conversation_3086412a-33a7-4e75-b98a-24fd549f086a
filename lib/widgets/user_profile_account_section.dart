import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/family_member.dart';
import '../models/subscription_info.dart';
import '../services/account_profile_controller.dart';
import '../services/error_handling_service.dart';
import '../theme/ui_constants.dart';
import '../presentation/settings/widgets/current_plan_widget.dart';
import 'profile_header_card.dart';
import 'family_sharing_card.dart';
import 'account_profile_theme_helper.dart';
import 'shared/animated_profile_widgets.dart';
import 'shared/skeleton_loading.dart';
import 'shared/error_display_widget.dart';

/// Enhanced UserProfileAccountSection widget that integrates all new components
/// into a cohesive, professional, and accessible account management interface.
/// 
/// Features:
/// - Responsive layout for different screen sizes
/// - Professional visual hierarchy with proper spacing
/// - Handles different user states (incomplete profile, admin, etc.)
/// - Accessibility compliance with screen reader support
/// - Loading states and error handling
/// - Real-time updates through AccountProfileController
class UserProfileAccountSection extends StatefulWidget {
  /// User profile data (optional for backward compatibility)
  final UserProfile? userProfile;
  
  /// Subscription information for displaying current plan
  final SubscriptionInfo? subscription;
  
  /// Callback when edit profile is requested
  final VoidCallback? onEditProfile;
  
  /// Callback when user management is requested
  final VoidCallback? onNavigateToUserManagement;
  
  /// Callback when promote to admin is requested
  final VoidCallback? onPromoteToAdmin;
  
  /// Callback when subscription management is requested
  final VoidCallback? onSubscriptionTap;
  
  /// Callback when family sharing is requested
  final VoidCallback? onFamilySharingTap;
  
  /// Callback when avatar upload is requested
  final VoidCallback? onAvatarTap;
  
  /// Whether to show the section header
  final bool showHeader;
  
  /// Custom section title (defaults to "Account & Profile")
  final String? sectionTitle;
  
  /// Whether to use compact layout
  final bool isCompact;

  const UserProfileAccountSection({
    super.key,
    this.userProfile,
    this.subscription,
    this.onEditProfile,
    this.onNavigateToUserManagement,
    this.onPromoteToAdmin,
    this.onSubscriptionTap,
    this.onFamilySharingTap,
    this.onAvatarTap,
    this.showHeader = true,
    this.sectionTitle,
    this.isCompact = false,
  });

  @override
  State<UserProfileAccountSection> createState() => _UserProfileAccountSectionState();
}

class _UserProfileAccountSectionState extends State<UserProfileAccountSection> {
  late AccountProfileController _controller;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isInitialized) {
      _initializeController();
    }
  }

  /// Initialize the controller and load data
  void _initializeController() {
    try {
      _controller = Provider.of<AccountProfileController>(context, listen: false);
      _isInitialized = true;
      
      // Load data if not already loaded
      if (_controller.userProfile == null && !_controller.isLoading) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _controller.loadAllData();
        });
      }
    } catch (e) {
      debugPrint('Error initializing AccountProfileController: $e');
      // Fallback to basic functionality without controller
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AccountProfileController>(
      builder: (context, controller, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Network status banner
            NetworkStatusBanner(
              isOnline: controller.isOnline,
              onRetry: () => controller.loadAllData(),
            ),
            
            // Section Header
            if (widget.showHeader) _buildSectionHeader(context),
            
            // Main Content
            _buildMainContent(context, controller),
            
            // Quick Actions (for compact layout)
            if (widget.isCompact) ...[
              SizedBox(height: 2.h),
              _buildQuickActions(context, controller),
            ],
          ],
        );
      },
    );
  }

  /// Build section header with enhanced styling
  Widget _buildSectionHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return AnimatedContainer(
      duration: UIConstants.shortAnimation,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Row(
        children: [
          AnimatedContainer(
            duration: UIConstants.shortAnimation,
            padding: EdgeInsets.all(1.5.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.account_circle_outlined,
              color: ThemeAwareColors.getPrimaryColor(context),
              size: 6.w,
              semanticLabel: 'Account section',
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Text(
              widget.sectionTitle ?? 'Account & Profile',
              style: AccountProfileThemeHelper.getResponsiveTextStyle(
                context,
                theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryColor(context),
                ) ?? const TextStyle(),
                isCompact: widget.isCompact,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build main content based on state
  Widget _buildMainContent(BuildContext context, AccountProfileController controller) {
    // Handle critical errors that prevent showing any content
    if (controller.profileError != null && 
        controller.profileError!.type == AccountProfileErrorType.authError) {
      return _buildCriticalErrorState(context, controller);
    }
    
    // Handle loading state for initial load
    if (controller.isLoading && controller.userProfile == null) {
      return _buildLoadingState(context);
    }
    
    // Handle empty/incomplete profile state
    if (controller.userProfile == null && !controller.isLoading) {
      return _buildEmptyProfileState(context);
    }
    
    // Build main content with all components (including individual error states)
    return _buildProfileContent(context, controller);
  }

  /// Build profile content with all components and individual error handling
  Widget _buildProfileContent(BuildContext context, AccountProfileController controller) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context, isCompact: widget.isCompact);
    
    return Column(
      children: [
        // Profile Header Card with error handling and animation
        AnimatedProfileWidgets.animatedListItem(
          context: context,
          index: 0,
          child: _buildProfileHeaderSection(context, controller),
        ),
        
        SizedBox(height: spacing.cardSpacing),
        
        // Family Sharing Card with error handling and animation
        AnimatedProfileWidgets.animatedListItem(
          context: context,
          index: 1,
          child: _buildFamilySharingSection(context, controller),
        ),
        
      ],
    );
  }

  /// Build profile header section with error handling
  Widget _buildProfileHeaderSection(BuildContext context, AccountProfileController controller) {
    if (controller.profileError != null) {
      return ErrorDisplayWidget(
        error: controller.profileError!,
        onRetry: () => controller.retryOperation('loadUserProfile'),
        onDismiss: () => controller.clearError('profile'),
        isCompact: widget.isCompact,
      );
    }

    if (controller.isLoadingProfile && controller.userProfile == null) {
      return ProfileHeaderSkeleton(
        showAccountDates: !widget.isCompact,
        avatarSize: widget.isCompact ? 60.0 : null,
      );
    }

    return Column(
      children: [
        ProfileHeaderCard(
          userProfile: controller.userProfile,
          onEditProfile: widget.onEditProfile,
          onAvatarTap: widget.onAvatarTap,
          isLoading: controller.isLoadingProfile,
          isEditable: true,
          showAccountDates: !widget.isCompact,
          avatarSize: widget.isCompact ? 60.0 : null,
        ),
        // Current Plan Widget - shown after Edit Profile button
        if (widget.subscription != null)
          _buildCurrentPlanWidget(context),
      ],
    );
  }

  /// Build family sharing section with error handling
  Widget _buildFamilySharingSection(BuildContext context, AccountProfileController controller) {
    if (controller.familyError != null) {
      return ErrorDisplayWidget(
        error: controller.familyError!,
        onRetry: () => controller.retryOperation('loadFamilyMembers'),
        onDismiss: () => controller.clearError('family'),
        isCompact: widget.isCompact,
      );
    }

    if (controller.isLoadingFamilyMembers && controller.familyMembers.isEmpty) {
      return const FamilySharingSkeleton();
    }

    return FamilySharingCard(
      familyMembers: controller.familyMembers,
      pendingInvitations: controller.pendingInvitationsCount,
      onInviteMember: widget.onFamilySharingTap,
      onSendInvitation: _handleSendInvitation,
      onResendInvitation: _handleResendInvitation,
      onCancelInvitation: _handleCancelInvitation,
      onMemberTap: _handleMemberTap,
      canManageFamily: controller.canManageFamily,
      isLoading: controller.isLoadingFamilyMembers,
      onManageFamily: widget.onNavigateToUserManagement,
    );
  }


  /// Build critical error state (e.g., authentication errors)
  Widget _buildCriticalErrorState(BuildContext context, AccountProfileController controller) {
    return ErrorDisplayWidget(
      error: controller.profileError!,
      onRetry: () => controller.refresh(),
      onDismiss: controller.clearErrors,
      showDismiss: false, // Don't allow dismissing critical errors
    );
  }

  /// Build loading state with enhanced skeletons and animations
  Widget _buildLoadingState(BuildContext context) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context, isCompact: widget.isCompact);
    
    return Column(
      children: [
        AnimatedProfileWidgets.animatedListItem(
          context: context,
          index: 0,
          child: _buildProfileHeaderSkeleton(context),
        ),
        SizedBox(height: spacing.cardSpacing),
        AnimatedProfileWidgets.animatedListItem(
          context: context,
          index: 1,
          child: _buildFamilySharingSkeleton(context),
        ),
      ],
    );
  }

  /// Build profile header skeleton with shimmer effects
  Widget _buildProfileHeaderSkeleton(BuildContext context) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context, isCompact: widget.isCompact);
    
    return AnimatedProfileWidgets.animatedCard(
      context: context,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AccountProfileThemeHelper.createShimmerEffect(
                context: context,
                width: widget.isCompact ? 60.0 : 80.0,
                height: widget.isCompact ? 60.0 : 80.0,
                borderRadius: (widget.isCompact ? 60.0 : 80.0) / 2,
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 60.w,
                      height: 20,
                    ),
                    SizedBox(height: spacing.elementSpacing),
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 40.w,
                      height: 16,
                    ),
                    SizedBox(height: spacing.elementSpacing),
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 20.w,
                      height: 24,
                      borderRadius: 12,
                    ),
                  ],
                ),
              ),
            ],
          ),
          if (!widget.isCompact) ...[
            SizedBox(height: spacing.sectionSpacing),
            AccountProfileThemeHelper.createShimmerEffect(
              context: context,
              width: 30.w,
              height: 16,
            ),
            SizedBox(height: spacing.elementSpacing),
            AccountProfileThemeHelper.createShimmerEffect(
              context: context,
              width: double.infinity,
              height: 8,
            ),
          ],
        ],
      ),
    );
  }

  /// Build family sharing skeleton with shimmer effects
  Widget _buildFamilySharingSkeleton(BuildContext context) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context, isCompact: widget.isCompact);
    
    return AnimatedProfileWidgets.animatedCard(
      context: context,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              AccountProfileThemeHelper.createShimmerEffect(
                context: context,
                width: 10.w,
                height: 10.w,
                borderRadius: 12,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 40.w,
                      height: 20,
                    ),
                    SizedBox(height: spacing.elementSpacing * 0.5),
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 60.w,
                      height: 16,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: spacing.sectionSpacing),
          Row(
            children: List.generate(3, (index) => Padding(
              padding: EdgeInsets.only(right: 2.w),
              child: AccountProfileThemeHelper.createShimmerEffect(
                context: context,
                width: 15.w,
                height: 15.w,
                borderRadius: 7.5.w,
              ),
            )),
          ),
        ],
      ),
    );
  }


  /// Build empty profile state with enhanced styling and animation
  Widget _buildEmptyProfileState(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context, isCompact: widget.isCompact);
    
    return AnimatedProfileWidgets.animatedCard(
      context: context,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          TweenAnimationBuilder<double>(
            tween: Tween(begin: 0.0, end: 1.0),
            duration: UIConstants.longAnimation,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Icon(
                  Icons.person_add_outlined,
                  size: 16.w,
                  color: ThemeAwareColors.getPrimaryColor(context),
                  semanticLabel: 'Create profile',
                ),
              );
            },
          ),
          SizedBox(height: spacing.sectionSpacing),
          Text(
            'Complete Your Profile',
            style: AccountProfileThemeHelper.getResponsiveTextStyle(
              context,
              theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ) ?? const TextStyle(),
              isCompact: widget.isCompact,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: spacing.elementSpacing),
          Text(
            'Set up your profile to access personalized insights, family sharing, and premium features.',
            style: AccountProfileThemeHelper.getResponsiveTextStyle(
              context,
              theme.textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ) ?? const TextStyle(),
              isCompact: widget.isCompact,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: spacing.sectionSpacing * 1.5),
          AnimatedProfileWidgets.animatedButton(
            context: context,
            onPressed: widget.onEditProfile,
            isPrimary: true,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.person_add,
                  size: 5.w,
                  color: Colors.white,
                ),
                SizedBox(width: 2.w),
                const Text(
                  'Create Profile',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build quick actions for compact layout with enhanced styling
  Widget _buildQuickActions(BuildContext context, AccountProfileController controller) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Row(
        children: [
          Expanded(
            child: AnimatedProfileWidgets.animatedButton(
              context: context,
              onPressed: widget.onEditProfile,
              isPrimary: false,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.edit_outlined, size: 18),
                  SizedBox(width: 2.w),
                  const Text('Edit Profile'),
                ],
              ),
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: AnimatedProfileWidgets.animatedButton(
              context: context,
              onPressed: widget.onNavigateToUserManagement,
              isPrimary: false,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.group_outlined, size: 18),
                  SizedBox(width: 2.w),
                  const Text('Manage Users'),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Handle family invitation sending with enhanced error feedback
  void _handleSendInvitation(
    String email,
    String role,
    Map<String, bool> permissions,
    String? message,
  ) async {
    final success = await _controller.inviteFamilyMember(
      email: email,
      role: role,
      permissions: permissions,
      customMessage: message,
    );
    
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Theme.of(context).colorScheme.onPrimary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(child: Text('Invitation sent to $email')),
            ],
          ),
          backgroundColor: Theme.of(context).colorScheme.primary,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 4),
        ),
      );
    } else if (mounted && _controller.familyError != null) {
      final error = _controller.familyError!;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.error_outline,
                color: Theme.of(context).colorScheme.onError,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(child: Text(error.message)),
            ],
          ),
          backgroundColor: error.isWarning 
              ? Theme.of(context).colorScheme.secondary
              : Theme.of(context).colorScheme.error,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 6),
          action: error.isRetryable ? SnackBarAction(
            label: 'Retry',
            textColor: Theme.of(context).colorScheme.onError,
            onPressed: () => _handleSendInvitation(email, role, permissions, message),
          ) : null,
        ),
      );
    }
  }

  /// Handle resending invitation
  void _handleResendInvitation(FamilyMember member) async {
    final success = await _controller.resendInvitation(member.id);
    
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invitation resent to ${member.fullName}'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }

  /// Handle cancelling invitation
  void _handleCancelInvitation(FamilyMember member) async {
    final success = await _controller.cancelInvitation(member.id);
    
    if (success && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Invitation cancelled for ${member.fullName}'),
          backgroundColor: Theme.of(context).colorScheme.primary,
        ),
      );
    }
  }

  /// Handle member tap
  void _handleMemberTap(FamilyMember member) {
    // Show member details or navigate to member management
    widget.onNavigateToUserManagement?.call();
  }


  /// Build current plan widget
  Widget _buildCurrentPlanWidget(BuildContext context) {
    if (widget.subscription == null) return const SizedBox.shrink();
    
    return Padding(
      padding: EdgeInsets.only(top: 2.h),
      child: CurrentPlanWidget(
        subscription: widget.subscription!,
        onManageSubscription: widget.onSubscriptionTap,
      ),
    );
  }

}

/// Convenient factory constructors for common UserProfileAccountSection configurations
extension UserProfileAccountSectionVariants on UserProfileAccountSection {
  /// Compact version for smaller screens or embedded contexts
  static Widget compact({
    UserProfile? userProfile,
    VoidCallback? onEditProfile,
    VoidCallback? onNavigateToUserManagement,
    VoidCallback? onSubscriptionTap,
    VoidCallback? onFamilySharingTap,
    VoidCallback? onAvatarTap,
  }) {
    return UserProfileAccountSection(
      userProfile: userProfile,
      onEditProfile: onEditProfile,
      onNavigateToUserManagement: onNavigateToUserManagement,
      onSubscriptionTap: onSubscriptionTap,
      onFamilySharingTap: onFamilySharingTap,
      onAvatarTap: onAvatarTap,
      isCompact: true,
      showHeader: false,
    );
  }

  /// Full version with all features and header
  static Widget full({
    UserProfile? userProfile,
    VoidCallback? onEditProfile,
    VoidCallback? onNavigateToUserManagement,
    VoidCallback? onPromoteToAdmin,
    VoidCallback? onSubscriptionTap,
    VoidCallback? onFamilySharingTap,
    VoidCallback? onAvatarTap,
    String? sectionTitle,
  }) {
    return UserProfileAccountSection(
      userProfile: userProfile,
      onEditProfile: onEditProfile,
      onNavigateToUserManagement: onNavigateToUserManagement,
      onPromoteToAdmin: onPromoteToAdmin,
      onSubscriptionTap: onSubscriptionTap,
      onFamilySharingTap: onFamilySharingTap,
      onAvatarTap: onAvatarTap,
      showHeader: true,
      sectionTitle: sectionTitle,
      isCompact: false,
    );
  }

  /// Settings version optimized for settings screen integration
  static Widget forSettings({
    UserProfile? userProfile,
    SubscriptionInfo? subscription,
    VoidCallback? onEditProfile,
    VoidCallback? onNavigateToUserManagement,
    VoidCallback? onSubscriptionTap,
    VoidCallback? onFamilySharingTap,
  }) {
    return UserProfileAccountSection(
      userProfile: userProfile,
      subscription: subscription,
      onEditProfile: onEditProfile,
      onNavigateToUserManagement: onNavigateToUserManagement,
      onSubscriptionTap: onSubscriptionTap,
      onFamilySharingTap: onFamilySharingTap,
      showHeader: true,
      isCompact: false,
    );
  }
}