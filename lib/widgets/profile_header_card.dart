import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../utils/user_avatar_theme_helper.dart';
import '../utils/accessibility_helper.dart';
import 'user_avatar_widget.dart';
import 'account_profile_theme_helper.dart';
import 'shared/animated_profile_widgets.dart';

/// Professional profile header card widget that displays user information with clear visual hierarchy
/// 
/// Features:
/// - Large, prominent user avatar with role-based styling
/// - Clear display of full name, email, and role badge
/// - Account creation date and last activity display
/// - Professional card layout with proper spacing and elevation
/// - Loading states and error handling
/// - Accessibility support with semantic labels
class ProfileHeaderCard extends StatelessWidget {
  /// User profile data to display
  final UserProfile? userProfile;
  
  
  /// Callback when edit profile button is tapped
  final VoidCallback? onEditProfile;
  
  /// Callback when avatar is tapped for photo upload
  final VoidCallback? onAvatarTap;
  
  /// Whether the card is currently loading
  final bool isLoading;
  
  /// Whether the profile can be edited
  final bool isEditable;
  
  /// Custom avatar size (defaults to large)
  final double? avatarSize;
  
  
  /// Whether to show account creation and activity dates
  final bool showAccountDates;

  const ProfileHeaderCard({
    super.key,
    this.userProfile,
    this.onEditProfile,
    this.onAvatarTap,
    this.isLoading = false,
    this.isEditable = true,
    this.avatarSize,
    this.showAccountDates = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return _buildLoadingCard(context);
    }

    if (userProfile == null) {
      return _buildErrorCard(context);
    }

    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);

    return AccessibilityHelper.createAccessibleCard(
      title: 'Profile information for ${userProfile!.fullName}',
      subtitle: 'Role: ${AccessibilityHelper.formatRoleForAccessibility(userProfile!.role)}',
      headingLevel: 2,
      child: AnimatedProfileWidgets.animatedCard(
        context: context,
        onTap: onEditProfile,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildProfileHeader(context),
            if (showAccountDates) ...[
              SizedBox(height: spacing.sectionSpacing),
              _buildAccountDates(context),
            ],
            if (isEditable) ...[
              SizedBox(height: spacing.sectionSpacing),
              _buildEditButton(context),
            ],
          ],
        ),
      ),
    );
  }

  /// Build the main profile header with avatar and user info
  Widget _buildProfileHeader(BuildContext context) {
    final theme = Theme.of(context);
    final initials = _getInitials(userProfile!.fullName);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return Row(
      children: [
        // User Avatar with animation
        AnimatedProfileWidgets.animatedAvatar(
          context: context,
          onTap: onAvatarTap,
          child: UserAvatarWidget(
            imageUrl: userProfile!.avatarUrl,
            initials: initials,
            role: userProfile!.role,
            size: avatarSize ?? 80.0,
            isEditable: isEditable,
            onTap: onAvatarTap,
            showBorder: true,
          ),
        ),
        SizedBox(width: 4.w),
        // User Information
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Full Name - Heading level 3
              Semantics(
                header: true,
                child: Text(
                  userProfile!.fullName,
                  style: AccountProfileThemeHelper.getResponsiveTextStyle(
                    context,
                    theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: ThemeAwareColors.getPrimaryTextColor(context),
                    ) ?? const TextStyle(),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: spacing.elementSpacing * 0.5),
              // Email
              Semantics(
                label: 'Email address: ${userProfile!.email}',
                child: Text(
                  userProfile!.email,
                  style: AccountProfileThemeHelper.getResponsiveTextStyle(
                    context,
                    theme.textTheme.bodyMedium?.copyWith(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                    ) ?? const TextStyle(),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: spacing.elementSpacing),
              // Role Badge with animation
              _buildRoleBadge(context),
            ],
          ),
        ),
      ],
    );
  }

  /// Build role badge with consistent color coding and animation
  Widget _buildRoleBadge(BuildContext context) {
    final roleConfig = UserAvatarThemeHelper.getRoleConfig(context, userProfile!.role);
    final theme = Theme.of(context);
    final roleText = _formatRoleText(userProfile!.role);
    
    return Semantics(
      label: 'User role: $roleText',
      child: AnimatedProfileWidgets.animatedBadge(
        context: context,
        status: userProfile!.role,
        child: Text(
          roleText,
          style: AccountProfileThemeHelper.getResponsiveTextStyle(
            context,
            theme.textTheme.labelMedium?.copyWith(
              color: roleConfig.borderColor,
              fontWeight: FontWeight.w500,
            ) ?? const TextStyle(),
            isCompact: avatarSize != null && avatarSize! < 80,
          ),
        ),
      ),
    );
  }


  /// Build account creation and activity dates
  Widget _buildAccountDates(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Semantics(
          header: true,
          child: Text(
            'Account Information',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w500,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
        ),
        SizedBox(height: 1.h),
        // Account Creation Date
        _buildInfoRow(
          context,
          icon: Icons.calendar_today_outlined,
          label: 'Member since',
          value: _formatDate(userProfile!.createdAt),
          semanticLabel: 'Member since ${AccessibilityHelper.formatDateForAccessibility(userProfile!.createdAt)}',
        ),
        SizedBox(height: 0.5.h),
        // Last Activity
        if (userProfile!.lastSignInAt != null)
          _buildInfoRow(
            context,
            icon: Icons.access_time_outlined,
            label: 'Last active',
            value: _formatLastActivity(userProfile!.lastSignInAt!),
            semanticLabel: 'Last active ${AccessibilityHelper.formatDateForAccessibility(userProfile!.lastSignInAt!)}',
          ),
        SizedBox(height: 0.5.h),
        // Sign In Count
        _buildInfoRow(
          context,
          icon: Icons.login_outlined,
          label: 'Total logins',
          value: '${userProfile!.signInCount}',
          semanticLabel: 'Total logins: ${userProfile!.signInCount}',
        ),
      ],
    );
  }

  /// Build info row with icon, label, and value
  Widget _buildInfoRow(
    BuildContext context, {
    required IconData icon,
    required String label,
    required String value,
    String? semanticLabel,
  }) {
    final theme = Theme.of(context);
    
    return Semantics(
      label: semanticLabel ?? '$label: $value',
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: ThemeAwareColors.getSecondaryTextColor(context),
          ),
          SizedBox(width: 2.w),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(width: 2.w),
          Expanded(
            child: Text(
              value,
              style: theme.textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.end,
            ),
          ),
        ],
      ),
    );
  }

  /// Build edit profile button with enhanced styling
  Widget _buildEditButton(BuildContext context) {
    return AccessibilityHelper.ensureMinTouchTarget(
      child: SizedBox(
        width: double.infinity,
        child: Semantics(
          button: true,
          enabled: onEditProfile != null,
          label: 'Edit profile information',
          hint: 'Opens profile editing screen',
          child: AnimatedProfileWidgets.animatedButton(
            context: context,
            onPressed: onEditProfile,
            isPrimary: false,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.edit_outlined, size: 18),
                SizedBox(width: 2.w),
                const Text('Edit Profile'),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Build loading state card with enhanced shimmer effects
  Widget _buildLoadingCard(BuildContext context) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return AnimatedProfileWidgets.animatedCard(
      context: context,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Avatar skeleton with shimmer
              AccountProfileThemeHelper.createShimmerEffect(
                context: context,
                width: avatarSize ?? 80.0,
                height: avatarSize ?? 80.0,
                borderRadius: (avatarSize ?? 80.0) / 2,
              ),
              SizedBox(width: 4.w),
              // Text skeletons with shimmer
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 60.w,
                      height: 20,
                    ),
                    SizedBox(height: spacing.elementSpacing),
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 40.w,
                      height: 16,
                    ),
                    SizedBox(height: spacing.elementSpacing),
                    AccountProfileThemeHelper.createShimmerEffect(
                      context: context,
                      width: 20.w,
                      height: 24,
                      borderRadius: 12,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build error state card
  Widget _buildErrorCard(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: ThemeAwareColors.getErrorColor(context),
            ),
            SizedBox(height: 2.h),
            Text(
              'Unable to load profile',
              style: theme.textTheme.titleMedium?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              'Please check your connection and try again',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
            if (onEditProfile != null) ...[
              SizedBox(height: 2.h),
              TextButton(
                onPressed: onEditProfile,
                child: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// Get user initials from full name
  String _getInitials(String fullName) {
    final names = fullName.trim().split(' ');
    if (names.isEmpty) return 'U';
    if (names.length == 1) return names[0][0].toUpperCase();
    return '${names.first[0]}${names.last[0]}'.toUpperCase();
  }

  /// Format role text for display
  String _formatRoleText(String role) {
    return role.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1).toLowerCase()
    ).join(' ');
  }


  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays < 1) {
      return 'Today';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else if (difference.inDays < 30) {
      final weeks = (difference.inDays / 7).floor();
      return '$weeks week${weeks > 1 ? 's' : ''} ago';
    } else if (difference.inDays < 365) {
      final months = (difference.inDays / 30).floor();
      return '$months month${months > 1 ? 's' : ''} ago';
    } else {
      final years = (difference.inDays / 365).floor();
      return '$years year${years > 1 ? 's' : ''} ago';
    }
  }

  /// Format last activity for display
  String _formatLastActivity(DateTime lastActivity) {
    final now = DateTime.now();
    final difference = now.difference(lastActivity);
    
    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hr ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return _formatDate(lastActivity);
    }
  }
}

/// Convenient factory constructors for common ProfileHeaderCard configurations
extension ProfileHeaderCardVariants on ProfileHeaderCard {
  /// Compact profile header without dates
  static Widget compact({
    UserProfile? userProfile,
    VoidCallback? onEditProfile,
    VoidCallback? onAvatarTap,
    bool isLoading = false,
    bool isEditable = true,
  }) {
    return ProfileHeaderCard(
      userProfile: userProfile,
      onEditProfile: onEditProfile,
      onAvatarTap: onAvatarTap,
      isLoading: isLoading,
      isEditable: isEditable,
      showAccountDates: false,
      avatarSize: 60.0,
    );
  }


  /// Read-only profile header for viewing other users
  static Widget readOnly({
    UserProfile? userProfile,
    bool isLoading = false,
  }) {
    return ProfileHeaderCard(
      userProfile: userProfile,
      isLoading: isLoading,
      isEditable: false,
      showAccountDates: true,
    );
  }
}