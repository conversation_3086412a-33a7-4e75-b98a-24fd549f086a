import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../../models/family_member.dart';
import '../custom_elevated_button.dart';
import '../custom_text_form_field.dart';
import '../custom_icon_widget.dart';
import 'invitation_dialog_controller.dart';
import 'role_selection_widget.dart';

/// Optimized family invitation dialog with better performance
class OptimizedFamilyInvitationDialog extends StatelessWidget {
  final Function(String email, String role, Map<String, bool> permissions, String? message) onInvite;
  final int currentMemberCount;
  final int maxMembers;

  const OptimizedFamilyInvitationDialog({
    super.key,
    required this.onInvite,
    this.currentMemberCount = 0,
    this.maxMembers = 10,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => InvitationDialogController(),
      child: _DialogContent(
        onInvite: onInvite,
        currentMemberCount: currentMemberCount,
        maxMembers: maxMembers,
      ),
    );
  }
}

class _DialogContent extends StatelessWidget {
  final Function(String email, String role, Map<String, bool> permissions, String? message) onInvite;
  final int currentMemberCount;
  final int maxMembers;

  const _DialogContent({
    required this.onInvite,
    required this.currentMemberCount,
    required this.maxMembers,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: 90.w,
          maxHeight: 85.h,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const _DialogHeader(),
            Flexible(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(6.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _MemberLimitInfo(
                      currentCount: currentMemberCount,
                      maxMembers: maxMembers,
                    ),
                    SizedBox(height: 4.h),
                    const _EmailField(),
                    SizedBox(height: 4.h),
                    const _RoleSelectionSection(),
                    SizedBox(height: 4.h),
                    const _AdvancedOptionsSection(),
                  ],
                ),
              ),
            ),
            _ActionButtons(
              onInvite: onInvite,
              canInvite: currentMemberCount < maxMembers,
            ),
          ],
        ),
      ),
    );
  }
}

class _DialogHeader extends StatelessWidget {
  const _DialogHeader();

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: CustomIconWidget(
              iconName: 'person_add',
              color: theme.colorScheme.primary,
              size: 6.w,
            ),
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Invite Family Member',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  'Add a new member to your family sharing',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: CustomIconWidget(
              iconName: 'close',
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
              size: 6.w,
            ),
          ),
        ],
      ),
    );
  }
}

class _MemberLimitInfo extends StatelessWidget {
  final int currentCount;
  final int maxMembers;

  const _MemberLimitInfo({
    required this.currentCount,
    required this.maxMembers,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final remainingSlots = maxMembers - currentCount;
    final isNearLimit = remainingSlots <= 2;
    
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isNearLimit 
            ? theme.colorScheme.error.withValues(alpha: 0.05)
            : theme.colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isNearLimit 
              ? theme.colorScheme.error.withValues(alpha: 0.2)
              : theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: isNearLimit ? 'warning' : 'info',
            color: isNearLimit ? theme.colorScheme.error : theme.colorScheme.primary,
            size: 5.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Family Members: $currentCount/$maxMembers',
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isNearLimit ? theme.colorScheme.error : theme.colorScheme.primary,
                  ),
                ),
                if (remainingSlots > 0) ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    '$remainingSlots ${remainingSlots == 1 ? 'slot' : 'slots'} remaining',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                ] else ...[
                  SizedBox(height: 0.5.h),
                  Text(
                    'Family is at maximum capacity',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.error,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _EmailField extends StatefulWidget {
  const _EmailField();

  @override
  State<_EmailField> createState() => _EmailFieldState();
}

class _EmailFieldState extends State<_EmailField> {
  final _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<InvitationDialogController>(
      builder: (context, controller, child) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Email Address',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 2.h),
            CustomTextFormField(
              controller: _controller,
              hintText: 'Enter email address',
              keyboardType: TextInputType.emailAddress,
              prefixIcon: CustomIconWidget(
                iconName: 'email',
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 5.w,
              ),
              validator: (_) => controller.validateEmail(),
              enabled: !controller.isLoading,
              onChanged: controller.updateEmail,
            ),
          ],
        );
      },
    );
  }
}

class _RoleSelectionSection extends StatelessWidget {
  const _RoleSelectionSection();

  @override
  Widget build(BuildContext context) {
    return Consumer<InvitationDialogController>(
      builder: (context, controller, child) {
        return RoleSelectionWidget(
          selectedRole: controller.formData.role,
          onRoleSelected: controller.updateRole,
          isEnabled: !controller.isLoading,
        );
      },
    );
  }
}

class _AdvancedOptionsSection extends StatelessWidget {
  const _AdvancedOptionsSection();

  @override
  Widget build(BuildContext context) {
    return Consumer<InvitationDialogController>(
      builder: (context, controller, child) {
        return Column(
          children: [
            _AdvancedOptionsToggle(
              isExpanded: controller.formData.showAdvancedOptions,
              onToggle: controller.toggleAdvancedOptions,
              isEnabled: !controller.isLoading,
            ),
            if (controller.formData.showAdvancedOptions) ...[
              SizedBox(height: 3.h),
              _PermissionsSection(
                permissions: controller.formData.permissions,
                onPermissionChanged: controller.updatePermission,
                isEnabled: !controller.isLoading,
              ),
              SizedBox(height: 3.h),
              _MessageField(
                onMessageChanged: controller.updateMessage,
                isEnabled: !controller.isLoading,
              ),
            ],
          ],
        );
      },
    );
  }
}

class _AdvancedOptionsToggle extends StatelessWidget {
  final bool isExpanded;
  final VoidCallback onToggle;
  final bool isEnabled;

  const _AdvancedOptionsToggle({
    required this.isExpanded,
    required this.onToggle,
    required this.isEnabled,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return InkWell(
      onTap: isEnabled ? onToggle : null,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 3.w),
        child: Row(
          children: [
            CustomIconWidget(
              iconName: 'tune',
              color: theme.colorScheme.primary,
              size: 5.w,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Text(
                'Advanced Options',
                style: theme.textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: theme.colorScheme.primary,
                ),
              ),
            ),
            AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: CustomIconWidget(
                iconName: 'expand_more',
                color: theme.colorScheme.primary,
                size: 5.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _PermissionsSection extends StatelessWidget {
  final Map<String, bool> permissions;
  final Function(String, bool) onPermissionChanged;
  final bool isEnabled;

  const _PermissionsSection({
    required this.permissions,
    required this.onPermissionChanged,
    required this.isEnabled,
  });

  static const Map<String, String> _permissionLabels = {
    'view_activities': 'View Activities',
    'add_activities': 'Add Activities',
    'edit_activities': 'Edit Activities',
    'delete_activities': 'Delete Activities',
    'manage_babies': 'Manage Babies',
    'manage_family': 'Manage Family',
    'view_insights': 'View AI Insights',
    'export_data': 'Export Data',
    'manage_settings': 'Manage Settings',
  };

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Permissions',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        Container(
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: theme.colorScheme.outline.withValues(alpha: 0.2),
            ),
          ),
          child: Column(
            children: permissions.entries.map((entry) {
              return _PermissionToggle(
                permission: entry.key,
                label: _permissionLabels[entry.key] ?? entry.key,
                value: entry.value,
                onChanged: isEnabled ? (value) => onPermissionChanged(entry.key, value) : null,
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}

class _PermissionToggle extends StatelessWidget {
  final String permission;
  final String label;
  final bool value;
  final Function(bool)? onChanged;

  const _PermissionToggle({
    required this.permission,
    required this.label,
    required this.value,
    this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: theme.colorScheme.primary,
          ),
        ],
      ),
    );
  }
}

class _MessageField extends StatefulWidget {
  final Function(String?) onMessageChanged;
  final bool isEnabled;

  const _MessageField({
    required this.onMessageChanged,
    required this.isEnabled,
  });

  @override
  State<_MessageField> createState() => _MessageFieldState();
}

class _MessageFieldState extends State<_MessageField> {
  final _controller = TextEditingController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Custom Message (Optional)',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        CustomTextFormField(
          controller: _controller,
          hintText: 'Add a personal message to the invitation...',
          maxLines: 3,
          enabled: widget.isEnabled,
          onChanged: widget.onMessageChanged,
        ),
      ],
    );
  }
}

class _ActionButtons extends StatelessWidget {
  final Function(String email, String role, Map<String, bool> permissions, String? message) onInvite;
  final bool canInvite;

  const _ActionButtons({
    required this.onInvite,
    required this.canInvite,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Consumer<InvitationDialogController>(
      builder: (context, controller, child) {
        return Container(
          padding: EdgeInsets.all(6.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.surface,
            borderRadius: const BorderRadius.only(
              bottomLeft: Radius.circular(20),
              bottomRight: Radius.circular(20),
            ),
            border: Border(
              top: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.2),
              ),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                child: CustomElevatedButton(
                  text: 'Cancel',
                  onPressed: controller.isLoading ? null : () => Navigator.of(context).pop(),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.surface,
                    foregroundColor: theme.colorScheme.onSurface,
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: BorderSide(
                        color: theme.colorScheme.outline.withValues(alpha: 0.3),
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: CustomElevatedButton(
                  text: controller.isLoading ? 'Sending...' : 'Send Invitation',
                  onPressed: canInvite && controller.canSubmit 
                      ? () => _handleInvite(context, controller)
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                    padding: EdgeInsets.symmetric(vertical: 2.h),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  icon: controller.isLoading 
                      ? SizedBox(
                          width: 5.w,
                          height: 5.w,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              theme.colorScheme.onPrimary,
                            ),
                          ),
                        )
                      : CustomIconWidget(
                          iconName: 'send',
                          color: theme.colorScheme.onPrimary,
                          size: 5.w,
                        ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _handleInvite(BuildContext context, InvitationDialogController controller) {
    final formData = controller.formData;
    onInvite(
      formData.email,
      formData.role,
      formData.permissions,
      formData.customMessage,
    );
  }
}