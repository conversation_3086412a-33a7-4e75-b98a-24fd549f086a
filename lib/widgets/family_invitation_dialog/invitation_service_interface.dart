/// Interface for invitation services
abstract class InvitationServiceInterface {
  /// Send an invitation to a family member
  Future<bool> sendInvitation({
    required String email,
    required String role,
    required Map<String, bool> permissions,
    String? customMessage,
  });
  
  /// Validate if an email can be invited
  Future<bool> canInviteEmail(String email);
  
  /// Get available roles for invitation
  List<String> getAvailableRoles();
  
  /// Get default permissions for a role
  Map<String, bool> getDefaultPermissions(String role);
}

/// Mock implementation for testing
class MockInvitationService implements InvitationServiceInterface {
  @override
  Future<bool> sendInvitation({
    required String email,
    required String role,
    required Map<String, bool> permissions,
    String? customMessage,
  }) async {
    // Simulate network delay
    await Future.delayed(const Duration(seconds: 1));
    
    // Simulate occasional failures for testing
    if (email.contains('fail')) {
      throw Exception('Failed to send invitation');
    }
    
    return true;
  }
  
  @override
  Future<bool> canInviteEmail(String email) async {
    // Simulate validation
    return !email.contains('invalid');
  }
  
  @override
  List<String> getAvailableRoles() {
    return ['parent', 'caregiver', 'grandparent', 'babysitter'];
  }
  
  @override
  Map<String, bool> getDefaultPermissions(String role) {
    return {
      'view_activities': true,
      'add_activities': role != 'babysitter',
      'edit_activities': role == 'parent',
      'delete_activities': role == 'parent',
      'manage_babies': role == 'parent',
      'manage_family': role == 'parent',
      'view_insights': role != 'babysitter',
      'export_data': role == 'parent',
      'manage_settings': role == 'parent',
    };
  }
}