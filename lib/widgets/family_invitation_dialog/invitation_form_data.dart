/// Data class to encapsulate invitation form state
class InvitationFormData {
  final String email;
  final String role;
  final Map<String, bool> permissions;
  final String? customMessage;
  final bool showAdvancedOptions;

  const InvitationFormData({
    required this.email,
    required this.role,
    required this.permissions,
    this.customMessage,
    this.showAdvancedOptions = false,
  });

  InvitationFormData copyWith({
    String? email,
    String? role,
    Map<String, bool>? permissions,
    String? customMessage,
    bool? showAdvancedOptions,
  }) {
    return InvitationFormData(
      email: email ?? this.email,
      role: role ?? this.role,
      permissions: permissions ?? this.permissions,
      customMessage: customMessage ?? this.customMessage,
      showAdvancedOptions: showAdvancedOptions ?? this.showAdvancedOptions,
    );
  }

  bool get isValid => email.isNotEmpty && _isValidEmail(email);

  bool _isValidEmail(String email) {
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    return emailRegex.hasMatch(email);
  }
}