import 'package:flutter/foundation.dart';

import '../../models/family_member.dart';
import '../../utils/validation_strategies.dart';
import 'invitation_form_data.dart';

/// Controller for managing family invitation dialog state
class InvitationDialogController extends ChangeNotifier {
  InvitationFormData _formData = const InvitationFormData(
    email: '',
    role: 'caregiver',
    permissions: {},
  );
  
  bool _isLoading = false;
  String? _errorMessage;
  
  // Getters
  InvitationFormData get formData => _formData;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  bool get canSubmit => _formData.isValid && !_isLoading;
  
  // Validation strategies
  final _emailValidator = const EmailValidationStrategy();
  
  /// Update email
  void updateEmail(String email) {
    _formData = _formData.copyWith(email: email.trim());
    _clearError();
    notifyListeners();
  }
  
  /// Update selected role
  void updateRole(String role) {
    final permissions = FamilyMemberPermissions.getDefaultPermissions(role);
    _formData = _formData.copyWith(
      role: role,
      permissions: Map<String, bool>.from(permissions),
    );
    notifyListeners();
  }
  
  /// Update custom message
  void updateMessage(String? message) {
    _formData = _formData.copyWith(customMessage: message?.trim());
    notifyListeners();
  }
  
  /// Toggle advanced options
  void toggleAdvancedOptions() {
    _formData = _formData.copyWith(
      showAdvancedOptions: !_formData.showAdvancedOptions,
    );
    notifyListeners();
  }
  
  /// Update permission
  void updatePermission(String permission, bool value) {
    final updatedPermissions = Map<String, bool>.from(_formData.permissions);
    updatedPermissions[permission] = value;
    _formData = _formData.copyWith(permissions: updatedPermissions);
    notifyListeners();
  }
  
  /// Validate form
  String? validateEmail() {
    return _emailValidator.validate(_formData.email);
  }
  
  /// Set loading state
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  /// Set error message
  void setError(String? error) {
    _errorMessage = error;
    notifyListeners();
  }
  
  /// Clear error
  void _clearError() {
    if (_errorMessage != null) {
      _errorMessage = null;
      notifyListeners();
    }
  }
  
  /// Reset form
  void reset() {
    _formData = const InvitationFormData(
      email: '',
      role: 'caregiver',
      permissions: {},
    );
    _isLoading = false;
    _errorMessage = null;
    notifyListeners();
  }
}