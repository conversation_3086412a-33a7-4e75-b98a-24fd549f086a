import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../models/family_member.dart';
import '../custom_icon_widget.dart';

/// Widget for role selection in family invitation dialog
class RoleSelectionWidget extends StatelessWidget {
  final String selectedRole;
  final Function(String) onRoleSelected;
  final bool isEnabled;

  const RoleSelectionWidget({
    super.key,
    required this.selectedRole,
    required this.onRoleSelected,
    this.isEnabled = true,
  });

  static const List<Map<String, dynamic>> _availableRoles = [
    {
      'value': 'parent',
      'label': 'Parent',
      'description': 'Full access to manage babies and family',
      'icon': 'person',
    },
    {
      'value': 'caregiver',
      'label': 'Caregiver',
      'description': 'Can track activities and view insights',
      'icon': 'favorite',
    },
    {
      'value': 'grandparent',
      'label': 'Grandparent',
      'description': 'Can view activities and add basic entries',
      'icon': 'elderly',
    },
    {
      'value': 'babysitter',
      'label': 'Babysitter',
      'description': 'Limited access for temporary care',
      'icon': 'child_care',
    },
  ];

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Role & Permissions',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 2.h),
        ..._availableRoles.map((role) => _RoleOption(
          role: role,
          isSelected: selectedRole == role['value'],
          onTap: isEnabled ? () => onRoleSelected(role['value']) : null,
        )),
      ],
    );
  }
}

class _RoleOption extends StatelessWidget {
  final Map<String, dynamic> role;
  final bool isSelected;
  final VoidCallback? onTap;

  const _RoleOption({
    required this.role,
    required this.isSelected,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: EdgeInsets.all(4.w),
          decoration: BoxDecoration(
            color: isSelected 
                ? theme.colorScheme.primary.withValues(alpha: 0.1)
                : theme.colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected 
                  ? theme.colorScheme.primary
                  : theme.colorScheme.outline.withValues(alpha: 0.2),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              _buildRoleIcon(theme),
              SizedBox(width: 4.w),
              Expanded(child: _buildRoleInfo(theme)),
              if (isSelected) _buildSelectedIndicator(theme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildRoleIcon(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: isSelected 
            ? theme.colorScheme.primary.withValues(alpha: 0.1)
            : theme.colorScheme.outline.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomIconWidget(
        iconName: role['icon'],
        color: isSelected 
            ? theme.colorScheme.primary
            : theme.colorScheme.onSurface.withValues(alpha: 0.6),
        size: 5.w,
      ),
    );
  }

  Widget _buildRoleInfo(ThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          role['label'],
          style: theme.textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: isSelected 
                ? theme.colorScheme.primary
                : theme.colorScheme.onSurface,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          role['description'],
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
          ),
        ),
      ],
    );
  }

  Widget _buildSelectedIndicator(ThemeData theme) {
    return CustomIconWidget(
      iconName: 'check_circle',
      color: theme.colorScheme.primary,
      size: 5.w,
    );
  }
}