/// Constants for family invitation dialog
class DialogConstants {
  const DialogConstants._();
  
  // Dialog dimensions
  static const double maxWidthPercentage = 90.0;
  static const double maxHeightPercentage = 85.0;
  static const double borderRadius = 20.0;
  
  // Spacing
  static const double headerPadding = 6.0;
  static const double contentPadding = 6.0;
  static const double sectionSpacing = 4.0;
  static const double itemSpacing = 2.0;
  
  // Icon sizes
  static const double headerIconSize = 6.0;
  static const double roleIconSize = 5.0;
  static const double closeIconSize = 6.0;
  
  // Animation duration
  static const Duration expandAnimationDuration = Duration(milliseconds: 300);
  
  // Validation
  static const int nearLimitThreshold = 2;
  
  // Default values
  static const String defaultRole = 'caregiver';
  static const int defaultMaxMembers = 10;
}

/// Permission labels for display
class PermissionLabels {
  const PermissionLabels._();
  
  static const Map<String, String> labels = {
    'view_activities': 'View Activities',
    'add_activities': 'Add Activities',
    'edit_activities': 'Edit Activities',
    'delete_activities': 'Delete Activities',
    'manage_babies': 'Manage Babies',
    'manage_family': 'Manage Family',
    'view_insights': 'View AI Insights',
    'export_data': 'Export Data',
    'manage_settings': 'Manage Settings',
  };
  
  static String getLabel(String permission) {
    return labels[permission] ?? permission;
  }
}

/// Available roles for invitation
class InvitationRoles {
  const InvitationRoles._();
  
  static const List<Map<String, dynamic>> roles = [
    {
      'value': 'parent',
      'label': 'Parent',
      'description': 'Full access to manage babies and family',
      'icon': 'person',
    },
    {
      'value': 'caregiver',
      'label': 'Caregiver',
      'description': 'Can track activities and view insights',
      'icon': 'favorite',
    },
    {
      'value': 'grandparent',
      'label': 'Grandparent',
      'description': 'Can view activities and add basic entries',
      'icon': 'elderly',
    },
    {
      'value': 'babysitter',
      'label': 'Babysitter',
      'description': 'Limited access for temporary care',
      'icon': 'child_care',
    },
  ];
}