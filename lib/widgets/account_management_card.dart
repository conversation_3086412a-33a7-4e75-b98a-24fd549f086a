import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';
import 'shared/skeleton_loading.dart';
import 'account_profile_theme_helper.dart';
import 'shared/animated_profile_widgets.dart';
import '../utils/accessibility_helper.dart';

/// Professional account management card widget that displays subscription status and account settings
/// 
/// Features:
/// - Subscription plan display with features and pricing
/// - Security settings overview (2FA, email verification, active sessions)
/// - Account preferences and settings organization
/// - Upgrade/downgrade call-to-action buttons
/// - Different subscription states handling (free, premium, trial, expired, cancelled)
/// - Loading states and error handling
/// - Accessibility support with semantic labels
class AccountManagementCard extends StatelessWidget {
  /// Subscription information to display
  final SubscriptionInfo? subscription;
  
  /// Whether the user has admin privileges
  final bool? hasAdminPrivileges;
  
  /// Whether the user's email is verified
  final bool? isEmailVerified;
  
  /// Whether two-factor authentication is enabled
  final bool? isTwoFactorEnabled;
  
  /// Number of active sessions
  final int? activeSessions;
  
  /// Callback when manage subscription is tapped
  final VoidCallback? onManageSubscription;
  
  /// Callback when security settings is tapped
  final VoidCallback? onSecuritySettings;
  
  /// Callback when account preferences is tapped
  final VoidCallback? onAccountPreferences;
  
  /// Callback when upgrade is tapped
  final VoidCallback? onUpgrade;
  
  /// Callback when downgrade is tapped
  final VoidCallback? onDowngrade;
  
  /// Whether the card is currently loading
  final bool isLoading;

  const AccountManagementCard({
    super.key,
    this.subscription,
    this.hasAdminPrivileges,
    this.isEmailVerified,
    this.isTwoFactorEnabled,
    this.activeSessions,
    this.onManageSubscription,
    this.onSecuritySettings,
    this.onAccountPreferences,
    this.onUpgrade,
    this.onDowngrade,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return const AccountManagementSkeleton();
    }

    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);

    return AccessibilityHelper.createAccessibleCard(
      title: 'Account Management',
      subtitle: 'Subscription and account settings',
      headingLevel: 2,
      child: AnimatedProfileWidgets.animatedCard(
        context: context,
        onTap: onManageSubscription,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            SizedBox(height: spacing.sectionSpacing),
            _buildSubscriptionSection(context),
            SizedBox(height: spacing.sectionSpacing),
            _buildSecuritySection(context),
            SizedBox(height: spacing.sectionSpacing),
            _buildPreferencesSection(context),
            if (_shouldShowUpgradeSection()) ...[
              SizedBox(height: spacing.sectionSpacing),
              _buildUpgradeSection(context),
            ],
          ],
        ),
      ),
    );
  }

  /// Build the card header with icon and title
  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.manage_accounts,
            color: ThemeAwareColors.getPrimaryColor(context),
            size: 6.w,
            semanticLabel: 'Account management',
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Text(
            'Account Management',
            style: AccountProfileThemeHelper.getResponsiveTextStyle(
              context,
              theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ) ?? const TextStyle(),
            ),
          ),
        ),
      ],
    );
  }

  /// Build the subscription information section
  Widget _buildSubscriptionSection(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section header
        _buildSectionHeader(
          context,
          'Subscription',
          Icons.card_membership,
          onManageSubscription,
        ),
        SizedBox(height: spacing.elementSpacing),
        
        // Subscription content
        if (subscription == null)
          _buildSubscriptionUnavailable(context)
        else
          _buildSubscriptionInfo(context),
      ],
    );
  }

  /// Build subscription unavailable state
  Widget _buildSubscriptionUnavailable(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.errorContainer.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: theme.colorScheme.error,
                size: 5.w,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  'Subscription information unavailable',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.error,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build detailed subscription information
  Widget _buildSubscriptionInfo(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: _getSubscriptionBadgeColor(context).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getSubscriptionBadgeColor(context).withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Plan name and status
          Row(
            children: [
              Expanded(
                child: Text(
                  subscription!.planName,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                  ),
                ),
              ),
              _buildSubscriptionStatusBadge(context),
            ],
          ),
          
          if (subscription!.monthlyPrice > 0) ...[
            SizedBox(height: spacing.elementSpacing * 0.5),
            Text(
              '\$${subscription!.monthlyPrice.toStringAsFixed(2)}/month',
              style: theme.textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w500,
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
          ],
          
          // Renewal or trial information
          if (_getSubscriptionStatusText() != null) ...[
            SizedBox(height: spacing.elementSpacing * 0.5),
            Text(
              _getSubscriptionStatusText()!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
            ),
          ],
          
          // Payment method
          if (subscription!.paymentMethod != null) ...[
            SizedBox(height: spacing.elementSpacing),
            _buildPaymentMethod(context),
          ],
          
          // Features (show first 4, then "+X more features")
          if (subscription!.features.isNotEmpty) ...[
            SizedBox(height: spacing.elementSpacing),
            _buildFeaturesList(context),
          ],
        ],
      ),
    );
  }

  /// Build subscription status badge
  Widget _buildSubscriptionStatusBadge(BuildContext context) {
    final theme = Theme.of(context);
    final status = _getStatusText();
    final color = _getSubscriptionBadgeColor(context);
    
    return AnimatedProfileWidgets.animatedBadge(
      context: context,
      status: subscription!.status.toString(),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Text(
          status,
          style: theme.textTheme.labelSmall?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  /// Build payment method display
  Widget _buildPaymentMethod(BuildContext context) {
    final theme = Theme.of(context);
    final paymentMethod = subscription!.paymentMethod!;
    
    String methodText;
    IconData methodIcon;
    bool isExpired = false;
    
    switch (paymentMethod.type) {
      case 'card':
        methodText = '${paymentMethod.brand?.toUpperCase() ?? 'CARD'} •••• ${paymentMethod.last4 ?? '****'}';
        methodIcon = Icons.credit_card;
        
        // Check if card is expired
        if (paymentMethod.expiryYear != null && paymentMethod.expiryMonth != null) {
          final now = DateTime.now();
          final expiry = DateTime(paymentMethod.expiryYear!, paymentMethod.expiryMonth!);
          isExpired = expiry.isBefore(now);
        }
        break;
      case 'paypal':
        methodText = 'PayPal';
        methodIcon = Icons.account_balance_wallet;
        break;
      default:
        methodText = paymentMethod.type.toUpperCase();
        methodIcon = Icons.payment;
    }
    
    return Row(
      children: [
        Icon(
          methodIcon,
          color: isExpired ? theme.colorScheme.error : ThemeAwareColors.getSecondaryTextColor(context),
          size: 4.w,
        ),
        SizedBox(width: 2.w),
        Expanded(
          child: Text(
            methodText,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isExpired ? theme.colorScheme.error : ThemeAwareColors.getSecondaryTextColor(context),
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        if (isExpired)
          Container(
            padding: EdgeInsets.symmetric(horizontal: 1.5.w, vertical: 0.3.h),
            decoration: BoxDecoration(
              color: theme.colorScheme.error.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Expired',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.error,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  /// Build features list (show first 4, then "+X more")
  Widget _buildFeaturesList(BuildContext context) {
    final theme = Theme.of(context);
    final features = subscription!.features;
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    final featuresToShow = features.take(4).toList();
    final remainingCount = features.length - featuresToShow.length;
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ...featuresToShow.map((feature) => Padding(
          padding: EdgeInsets.only(bottom: spacing.elementSpacing * 0.3),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(
                Icons.check_circle,
                color: ThemeAwareColors.getSuccessColor(context),
                size: 4.w,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  feature,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
                ),
              ),
            ],
          ),
        )),
        if (remainingCount > 0)
          Padding(
            padding: EdgeInsets.only(top: spacing.elementSpacing * 0.3),
            child: Text(
              '+$remainingCount more features',
              style: theme.textTheme.bodySmall?.copyWith(
                color: ThemeAwareColors.getPrimaryColor(context),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
      ],
    );
  }

  /// Build security settings section
  Widget _buildSecuritySection(BuildContext context) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          context,
          'Security',
          Icons.security,
          onSecuritySettings,
        ),
        SizedBox(height: spacing.elementSpacing),
        _buildSecurityItem(
          context,
          'Email Verification',
          isEmailVerified == true ? 'Verified' : 'Not Verified',
          isEmailVerified == true ? Icons.verified : Icons.warning,
          isEmailVerified == true ? ThemeAwareColors.getSuccessColor(context) : ThemeAwareColors.getErrorColor(context),
        ),
        SizedBox(height: spacing.elementSpacing * 0.7),
        _buildSecurityItem(
          context,
          'Two-Factor Authentication',
          isTwoFactorEnabled == true ? 'Enabled' : 'Disabled',
          isTwoFactorEnabled == true ? Icons.security : Icons.security_outlined,
          isTwoFactorEnabled == true ? ThemeAwareColors.getSuccessColor(context) : ThemeAwareColors.getSecondaryTextColor(context),
        ),
        SizedBox(height: spacing.elementSpacing * 0.7),
        _buildSecurityItem(
          context,
          'Active Sessions',
          '${activeSessions ?? 0} sessions',
          Icons.devices,
          (activeSessions ?? 0) > 1 ? ThemeAwareColors.getWarningColor(context) : ThemeAwareColors.getSuccessColor(context),
        ),
      ],
    );
  }

  /// Build individual security item
  Widget _buildSecurityItem(
    BuildContext context,
    String title,
    String status,
    IconData icon,
    Color color,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          color: color,
          size: 4.5.w,
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              Text(
                status,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.chevron_right,
          color: ThemeAwareColors.getSecondaryTextColor(context),
          size: 4.w,
        ),
      ],
    );
  }

  /// Build preferences section
  Widget _buildPreferencesSection(BuildContext context) {
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionHeader(
          context,
          'Preferences',
          Icons.tune,
          onAccountPreferences,
        ),
        SizedBox(height: spacing.elementSpacing),
        _buildPreferenceItem(
          context,
          'Notifications',
          'Push, email, and in-app settings',
          Icons.notifications,
        ),
        SizedBox(height: spacing.elementSpacing * 0.7),
        _buildPreferenceItem(
          context,
          'Privacy',
          'Data sharing and visibility',
          Icons.privacy_tip,
        ),
        SizedBox(height: spacing.elementSpacing * 0.7),
        _buildPreferenceItem(
          context,
          'Data Export',
          'Download your data',
          Icons.download,
        ),
      ],
    );
  }

  /// Build individual preference item
  Widget _buildPreferenceItem(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
  ) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          color: ThemeAwareColors.getPrimaryColor(context),
          size: 4.5.w,
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w500,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
              ),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.chevron_right,
          color: ThemeAwareColors.getSecondaryTextColor(context),
          size: 4.w,
        ),
      ],
    );
  }

  /// Build upgrade section (shown for free, trial, or expired subscriptions)
  Widget _buildUpgradeSection(BuildContext context) {
    final theme = Theme.of(context);
    final spacing = AccountProfileThemeHelper.getSectionSpacing(context);
    
    String title;
    String subtitle;
    Color backgroundColor;
    Color textColor;
    
    if (subscription?.status == SubscriptionStatus.trial) {
      title = 'Upgrade Available';
      subtitle = 'Trial ending soon - upgrade to continue';
      backgroundColor = ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.1);
      textColor = ThemeAwareColors.getWarningColor(context);
    } else if (subscription?.status == SubscriptionStatus.expired) {
      title = 'Reactivate Subscription';
      subtitle = 'Subscription expired';
      backgroundColor = ThemeAwareColors.getErrorColor(context).withValues(alpha: 0.1);
      textColor = ThemeAwareColors.getErrorColor(context);
    } else {
      title = 'Upgrade Available';
      subtitle = 'Upgrade to unlock premium features';
      backgroundColor = ThemeAwareColors.getPrimaryColor(context).withValues(alpha: 0.1);
      textColor = ThemeAwareColors.getPrimaryColor(context);
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: textColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.star,
                color: textColor,
                size: 5.w,
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: spacing.elementSpacing * 0.5),
          Text(
            subtitle,
            style: theme.textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getSecondaryTextColor(context),
            ),
          ),
          SizedBox(height: spacing.elementSpacing),
          Row(
            children: [
              if (onUpgrade != null)
                Expanded(
                  child: AnimatedProfileWidgets.animatedButton(
                    context: context,
                    onPressed: onUpgrade!,
                    isPrimary: true,
                    child: Text(
                      subscription?.status == SubscriptionStatus.trial ? 'Upgrade Now' : 
                      subscription?.status == SubscriptionStatus.expired ? 'Reactivate' : 'Upgrade',
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              if (onUpgrade != null && onDowngrade != null)
                SizedBox(width: 3.w),
              if (onDowngrade != null && subscription?.status == SubscriptionStatus.trial)
                Expanded(
                  child: AnimatedProfileWidgets.animatedButton(
                    context: context,
                    onPressed: onDowngrade!,
                    isPrimary: false,
                    child: const Text(
                      'Continue Free',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build section header with icon, title and optional tap handler
  Widget _buildSectionHeader(
    BuildContext context,
    String title,
    IconData icon,
    VoidCallback? onTap,
  ) {
    final theme = Theme.of(context);
    
    Widget content = Row(
      children: [
        Icon(
          icon,
          color: ThemeAwareColors.getPrimaryColor(context),
          size: 5.w,
        ),
        SizedBox(width: 2.w),
        Expanded(
          child: Text(
            title,
            style: theme.textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.w600,
              color: ThemeAwareColors.getPrimaryTextColor(context),
            ),
          ),
        ),
        if (onTap != null)
          Icon(
            Icons.chevron_right,
            color: ThemeAwareColors.getSecondaryTextColor(context),
            size: 4.w,
          ),
      ],
    );
    
    if (onTap != null) {
      return InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 1.h),
          child: content,
        ),
      );
    }
    
    return content;
  }

  /// Get subscription status text for display
  String _getStatusText() {
    switch (subscription?.status) {
      case SubscriptionStatus.active:
        return 'ACTIVE';
      case SubscriptionStatus.trial:
        return 'TRIAL';
      case SubscriptionStatus.expired:
        return 'EXPIRED';
      case SubscriptionStatus.cancelled:
        return 'CANCELLED';
      case SubscriptionStatus.free:
        return 'FREE';
      default:
        return 'FREE';
    }
  }

  /// Get subscription status text with additional information
  String? _getSubscriptionStatusText() {
    if (subscription == null) return null;
    
    switch (subscription!.status) {
      case SubscriptionStatus.active:
        if (subscription!.renewalDate != null) {
          return 'Renews ${_formatDate(subscription!.renewalDate!)}';
        }
        break;
      case SubscriptionStatus.trial:
        if (subscription!.trialEndsAt != null) {
          final daysLeft = subscription!.trialEndsAt!.difference(DateTime.now()).inDays;
          return 'Trial ends in $daysLeft days';
        }
        break;
      case SubscriptionStatus.cancelled:
        if (subscription!.renewalDate != null) {
          return 'Active until ${_formatDate(subscription!.renewalDate!)}';
        }
        break;
      case SubscriptionStatus.expired:
        return 'Subscription expired';
      default:
        break;
    }
    
    return null;
  }

  /// Get subscription badge color based on status
  Color _getSubscriptionBadgeColor(BuildContext context) {
    switch (subscription?.status) {
      case SubscriptionStatus.active:
        return ThemeAwareColors.getSuccessColor(context);
      case SubscriptionStatus.trial:
        return ThemeAwareColors.getWarningColor(context);
      case SubscriptionStatus.expired:
        return ThemeAwareColors.getErrorColor(context);
      case SubscriptionStatus.cancelled:
        return ThemeAwareColors.getSecondaryTextColor(context);
      case SubscriptionStatus.free:
        return ThemeAwareColors.getPrimaryColor(context);
      default:
        return ThemeAwareColors.getPrimaryColor(context);
    }
  }

  /// Check if upgrade section should be shown
  bool _shouldShowUpgradeSection() {
    if (subscription == null) return true;
    
    return subscription!.status == SubscriptionStatus.free ||
           subscription!.status == SubscriptionStatus.trial ||
           subscription!.status == SubscriptionStatus.expired;
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}

/// Factory constructors for common AccountManagementCard variants
class AccountManagementCardVariants {
  /// Create a card for free plan users
  static AccountManagementCard free({
    bool? isEmailVerified,
    bool? isTwoFactorEnabled,
    int? activeSessions,
    VoidCallback? onManageSubscription,
    VoidCallback? onSecuritySettings,
    VoidCallback? onAccountPreferences,
    VoidCallback? onUpgrade,
  }) {
    return AccountManagementCard(
      subscription: SubscriptionPlans.free,
      hasAdminPrivileges: false,
      isEmailVerified: isEmailVerified,
      isTwoFactorEnabled: isTwoFactorEnabled,
      activeSessions: activeSessions,
      onManageSubscription: onManageSubscription,
      onSecuritySettings: onSecuritySettings,
      onAccountPreferences: onAccountPreferences,
      onUpgrade: onUpgrade,
    );
  }
}
