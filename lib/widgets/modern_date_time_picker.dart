import 'package:flutter/material.dart' hide showDatePicker;
import 'package:flutter/material.dart' as material show showDatePicker;
import 'package:sizer/sizer.dart';
import '../services/settings_service.dart';

class ModernDateTimePicker {
  /// Gets the current time for consistent initialization across all entry widgets
  static DateTime getCurrentTime() {
    return DateTime.now();
  }

  /// Formats DateTime to consistent string format: "Date: 7/9/2025 at 1:49 PM"
  static String formatDateTime(DateTime dateTime, BuildContext context) {
    return 'Date: ${dateTime.month}/${dateTime.day}/${dateTime.year} at ${TimeOfDay.fromDateTime(dateTime).format(context)}';
  }

  /// Formats DateTime to time only format: "1:49 PM"
  static String formatTime(DateTime dateTime, BuildContext context) {
    return TimeOfDay.fromDateTime(dateTime).format(context);
  }

  /// Shows a modern date picker dialog
  static Future<DateTime?> showDatePicker({
    required BuildContext context,
    DateTime? initialDate,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    final now = getCurrentTime();
    return await material.showDatePicker(
      context: context,
      initialDate: initialDate ?? now,
      firstDate: firstDate ?? now.subtract(Duration(days: 365)),
      lastDate: lastDate ?? now,
    );
  }

  /// Shows a modern time picker dialog
  static Future<TimeOfDay?> showTimePicker({
    required BuildContext context,
    TimeOfDay? initialTime,
  }) async {
    return showDialog<TimeOfDay>(
      context: context,
      barrierDismissible: false,
      builder: (context) => _ModernTimePickerDialog(
        initialTime: initialTime ?? TimeOfDay.now(),
        use24HourFormat: SettingsService.instance.is24HourFormat,
      ),
    );
  }

  /// Shows a combined date and time picker
  static Future<DateTime?> showDateTimePicker({
    required BuildContext context,
    DateTime? initialDateTime,
    DateTime? firstDate,
    DateTime? lastDate,
  }) async {
    final now = getCurrentTime();
    final effectiveInitialDateTime = initialDateTime ?? now;
    
    final selectedDate = await material.showDatePicker(
      context: context,
      initialDate: effectiveInitialDateTime,
      firstDate: firstDate ?? now.subtract(Duration(days: 365)),
      lastDate: lastDate ?? now,
    );

    if (selectedDate != null) {
      final selectedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(effectiveInitialDateTime),
      );

      if (selectedTime != null) {
        return DateTime(
          selectedDate.year,
          selectedDate.month,
          selectedDate.day,
          selectedTime.hour,
          selectedTime.minute,
        );
      }
    }

    return null;
  }
}

class _ModernTimePickerDialog extends StatefulWidget {
  final TimeOfDay initialTime;
  final bool use24HourFormat;

  const _ModernTimePickerDialog({
    required this.initialTime,
    this.use24HourFormat = false,
  });

  @override
  State<_ModernTimePickerDialog> createState() => _ModernTimePickerDialogState();
}

class _ModernTimePickerDialogState extends State<_ModernTimePickerDialog> {
  late int _hour;
  late int _minute;
  late bool _isAm;

  @override
  void initState() {
    super.initState();
    // Use the initial time provided, not current time
    final initialTime = widget.initialTime;
    
    if (widget.use24HourFormat) {
      _hour = initialTime.hour;
      _minute = initialTime.minute;
      _isAm = true; // Not used in 24-hour format
    } else {
      // Ensure proper conversion for 12-hour format
      // Handle the case where hourOfPeriod is 0 (midnight/noon)
      final hourOfPeriod = initialTime.hourOfPeriod;
      _hour = hourOfPeriod == 0 ? 12 : hourOfPeriod;
      _minute = initialTime.minute;
      _isAm = initialTime.period == DayPeriod.am;
    }
  }

  TimeOfDay _getSelectedTime() {
    if (widget.use24HourFormat) {
      return TimeOfDay(hour: _hour, minute: _minute);
    } else {
      int hour24 = _hour;
      if (_isAm) {
        if (hour24 == 12) hour24 = 0;
      } else {
        if (hour24 != 12) hour24 += 12;
      }
      return TimeOfDay(hour: hour24, minute: _minute);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: AlertDialog(
        elevation: 24.0,
        backgroundColor: Theme.of(context).dialogBackgroundColor,
        title: Text(
          'Select Time',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        content: Container(
          height: 200,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              // Hour picker
              Expanded(
                child: _buildNumberPicker(
                  value: _hour,
                  minValue: widget.use24HourFormat ? 0 : 1,
                  maxValue: widget.use24HourFormat ? 23 : 12,
                  label: 'Hour',
                  onChanged: (value) => setState(() => _hour = value),
                ),
              ),
              
              // Separator
              Container(
                margin: EdgeInsets.symmetric(horizontal: 2.w),
                child: Text(
                  ':',
                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              
              // Minute picker
              Expanded(
                child: _buildNumberPicker(
                  value: _minute,
                  minValue: 0,
                  maxValue: 59,
                  label: 'Minute',
                  onChanged: (value) => setState(() => _minute = value),
                ),
              ),
              
              // AM/PM picker (only for 12-hour format)
              if (!widget.use24HourFormat)
                Container(
                  margin: EdgeInsets.only(left: 2.w),
                  child: _buildAmPmPicker(),
                ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, _getSelectedTime()),
            child: Text('OK'),
          ),
        ],
      ),
    );
  }

  Widget _buildNumberPicker({
    required int value,
    required int minValue,
    required int maxValue,
    required String label,
    required Function(int) onChanged,
  }) {
    // Ensure value is within valid range and prevent negative values
    final clampedValue = value.clamp(minValue, maxValue);
    final initialIndex = (clampedValue - minValue).clamp(0, maxValue - minValue);
    
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),
        Expanded(
          child: Stack(
            children: [
              Container(
                width: 20.w,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ListWheelScrollView.useDelegate(
                  controller: FixedExtentScrollController(
                    initialItem: initialIndex,
                  ),
                  itemExtent: 40,
                  physics: FixedExtentScrollPhysics(),
                  diameterRatio: 1.5,
                  onSelectedItemChanged: (index) {
                    final selectedValue = index + minValue;
                    // Ensure the selected value is within bounds
                    if (selectedValue >= minValue && selectedValue <= maxValue) {
                      onChanged(selectedValue);
                    }
                  },
                  childDelegate: ListWheelChildBuilderDelegate(
                    builder: (context, index) {
                      final number = index + minValue;
                      // Only show valid numbers within range
                      if (number > maxValue || number < minValue || index < 0) return null;
                      return Container(
                        alignment: Alignment.center,
                        margin: EdgeInsets.symmetric(horizontal: 1.w),
                        child: Text(
                          label == 'Minute' ? number.toString().padLeft(2, '0') : number.toString(),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w500,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                        ),
                      );
                    },
                    childCount: (maxValue - minValue + 1),
                  ),
                ),
              ),
              // Center selection indicator - improved positioning and centering
              Positioned.fill(
                child: Center(
                  child: Container(
                    height: 40,
                    margin: EdgeInsets.symmetric(horizontal: 1.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      border: Border.all(
                        color: Theme.of(context).colorScheme.primary,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAmPmPicker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          'Period',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 1.h),
        Container(
          width: 16.w,
          height: 120,
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _isAm = true),
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: _isAm 
                          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.vertical(top: Radius.circular(7)),
                    ),
                    child: Text(
                      'AM',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: _isAm 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
              ),
              Divider(
                height: 1,
                color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _isAm = false),
                  child: Container(
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      color: !_isAm 
                          ? Theme.of(context).colorScheme.primary.withOpacity(0.1)
                          : Colors.transparent,
                      borderRadius: BorderRadius.vertical(bottom: Radius.circular(7)),
                    ),
                    child: Text(
                      'PM',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                        color: !_isAm 
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
