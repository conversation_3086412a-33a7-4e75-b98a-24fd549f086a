import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../presentation/quick_log_bottom_sheet/quick_log_bottom_sheet.dart';
import '../../utils/activity_type_config.dart';

class QuickActionButtonsWidget extends StatefulWidget {
  final VoidCallback onFeedTap;
  final VoidCallback onDiaperTap;
  final VoidCallback onSleepTap;
  final VoidCallback onMoreTap;
  final List<Map<String, dynamic>>? allActivities;
  final BabyProfile? babyProfile;
  final VoidCallback? onDataChanged;

  const QuickActionButtonsWidget({
    super.key,
    required this.onFeedTap,
    required this.onDiaperTap,
    required this.onSleepTap,
    required this.onMoreTap,
    this.allActivities,
    this.babyProfile,
    this.onDataChanged,
  });

  @override
  State<QuickActionButtonsWidget> createState() =>
      _QuickActionButtonsWidgetState();
}

class _QuickActionButtonsWidgetState extends State<QuickActionButtonsWidget> {
  final PageController _pageController = PageController();
  int _currentPage = 0;

  // Complete list of all action buttons from tracker screen
  List<Map<String, dynamic>> get _allQuickActions {
    if (widget.allActivities != null && widget.allActivities!.isNotEmpty) {
      return widget.allActivities!;
    }
    
    // Use centralized ActivityTypeConfig for consistent icons and colors
    final activityTypes = [
      'feeding',
      'sleep', 
      'diaper',
      'medicine',
      'vaccination',
      'temperature',
      'potty',
      'tummy_time',
      'story_time',
      'screen_time',
      'skin_to_skin',
      'outdoor_play',
      'indoor_play',
      'brush_teeth',
      'pumping',
      'growth',
      'milestone',
    ];
    
    final activities = activityTypes.map((type) {
      VoidCallback onTap;
      String displayLabel = ActivityTypeConfig.getLabel(type);
      
      switch (type) {
        case 'feeding':
          onTap = widget.onFeedTap;
          displayLabel = 'Feed'; // Keep shorter label for quick actions
          break;
        case 'sleep':
          onTap = widget.onSleepTap;
          break;
        case 'diaper':
          onTap = widget.onDiaperTap;
          break;
        case 'growth':
          onTap = () => Navigator.pushNamed(context, '/growth-charts');
          break;
        case 'milestone':
          onTap = () => _handleActivityTap('milestone');
          displayLabel = 'Milestones'; // Keep plural for consistency
          break;
        default:
          onTap = () => _handleActivityTap(type);
          break;
      }
      
      return {
        'label': displayLabel,
        'icon': ActivityTypeConfig.getIcon(type),
        'color': ActivityTypeConfig.getColor(type, context),
        'onTap': onTap,
        'type': type,
      };
    }).toList();
    
    // Add More button
    activities.add({
      'label': 'More',
      'icon': 'more_horiz',
      'color': Theme.of(context).colorScheme.onSurfaceVariant,
      'onTap': widget.onMoreTap,
      'type': 'more',
    });
    
    return activities;
  }
  void _handleActivityTap(String type) {
    switch (type) {
      case 'growth':
        Navigator.pushNamed(context, '/growth-charts', arguments: widget.babyProfile);
        break;
      case 'feeding':
        // Use Quick Log for feeding
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'feeding',
            babyProfile: widget.babyProfile,
            onDataSaved: widget.onDataChanged,
          ),
        );
        break;
      case 'sleep':
        // Use Quick Log for sleep
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'sleep',
            babyProfile: widget.babyProfile,
            onDataSaved: widget.onDataChanged,
          ),
        );
        break;
      case 'medicine':
        // Navigate to Quick Log with medicine pre-selected
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'medicine',
          'title': 'Log Medicine'
        });
        break;
      case 'vaccination':
        // Use Quick Log for vaccination
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (context) => QuickLogBottomSheet(
            initialActivityType: 'vaccination',
            babyProfile: widget.babyProfile,
            onDataSaved: widget.onDataChanged,
          ),
        );
        break;
      case 'temperature':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'temperature',
          'title': 'Log Temperature'
        });
        break;
      case 'potty':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'potty',
          'title': 'Log Potty'
        });
        break;
      case 'tummy_time':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'tummy_time',
          'title': 'Log Tummy Time'
        });
        break;
      case 'story_time':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'story_time',
          'title': 'Log Story Time'
        });
        break;
      case 'screen_time':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'screen_time',
          'title': 'Log Screen Time'
        });
        break;
      case 'skin_to_skin':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'skin_to_skin',
          'title': 'Log Skin to Skin'
        });
        break;
      case 'outdoor_play':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'outdoor_play',
          'title': 'Log Outdoor Play'
        });
        break;
      case 'indoor_play':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'indoor_play',
          'title': 'Log Indoor Play'
        });
        break;
      case 'brush_teeth':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'brush_teeth',
          'title': 'Log Brush Teeth'
        });
        break;
      case 'pumping':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'pumping',
          'title': 'Log Pumping'
        });
        break;
      case 'milestone':
        Navigator.pushNamed(context, '/quick-log-bottom-sheet', arguments: {
          'initialActivityType': 'milestone',
          'title': 'Log Milestone Achievement'
        });
        break;
      default:
        Navigator.pushNamed(context, '/quick-log-bottom-sheet');
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final actions = _allQuickActions;
    final pageCount = (actions.length / 4).ceil();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header - clean without icon
        Padding(
          padding: EdgeInsets.only(bottom: 1.h),
          child: Text(
            'Quick Log',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ),
        
        // Swipeable Action Buttons
        SizedBox(
          height: 8.h,
          child: PageView.builder(
            controller: _pageController,
            onPageChanged: (page) {
              setState(() {
                _currentPage = page;
              });
            },
            itemCount: pageCount,
            itemBuilder: (context, pageIndex) {
              final startIndex = pageIndex * 4;
              final endIndex = (startIndex + 4).clamp(0, actions.length);
              final pageActions = actions.sublist(startIndex, endIndex);

              return Row(
                children: List.generate(4, (index) {
                  if (index < pageActions.length) {
                    final action = pageActions[index];
                    return Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(
                          right: index < 3 ? 2.w : 0,
                        ),
                        child: _buildActionButton(
                          context,
                          action['label'] ?? 'Unknown',
                          action['icon'] ?? 'help',
                          action['color'] ?? Theme.of(context).colorScheme.onSurfaceVariant,
                          action['onTap'] ?? () {},
                        ),
                      ),
                    );
                  } else {
                    return Expanded(child: SizedBox());
                  }
                }),
              );
            },
          ),
        ),

        // Page indicator (only show if more than 4 actions)
        if (actions.length > 4) ...[
          SizedBox(height: 1.h),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(
              pageCount,
              (index) => Container(
                margin: EdgeInsets.symmetric(horizontal: 0.5.w),
                width: 1.5.w,
                height: 1.5.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _currentPage == index
                      ? Theme.of(context).colorScheme.primary
                      : Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.4),
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String? label,
    String? iconName,
    Color? color,
    VoidCallback? onTap,
  ) {
    final safeLabel = label ?? 'Unknown';
    final safeIconName = iconName ?? 'help';
    final safeColor = color ?? Theme.of(context).colorScheme.onSurfaceVariant;
    final safeOnTap = onTap ?? () {};
    return GestureDetector(
      onTap: safeOnTap,
      child: Container(
        height: 8.h,
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: safeColor.withValues(alpha: 0.2),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).colorScheme.shadow.withValues(alpha: 0.03),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.all(2.5.w),
              decoration: BoxDecoration(
                color: safeColor.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: CustomIconWidget(
                iconName: safeIconName,
                color: safeColor,
                size: 5.w,
              ),
            ),
            SizedBox(height: 0.5.h),
            Text(
              safeLabel,
              style: Theme.of(context).textTheme.labelSmall?.copyWith(
                    color: safeColor,
                    fontWeight: FontWeight.w600,
                  ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }
}
