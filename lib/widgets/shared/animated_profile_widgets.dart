import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../theme/ui_constants.dart';
import '../account_profile_theme_helper.dart';

/// Collection of animated widgets for smooth profile interactions
class AnimatedProfileWidgets {
  AnimatedProfileWidgets._();

  /// Animated card with hover and press effects
  static Widget animatedCard({
    required Widget child,
    required BuildContext context,
    VoidCallback? onTap,
    bool isEnabled = true,
    Duration? animationDuration,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return _AnimatedCard(
      onTap: isEnabled ? onTap : null,
      animationDuration: animationDuration ?? UIConstants.shortAnimation,
      padding: padding ?? EdgeInsets.all(5.w),
      margin: margin ?? EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: child,
    );
  }

  /// Animated button with scale and color transitions
  static Widget animatedButton({
    required Widget child,
    required BuildContext context,
    required VoidCallback? onPressed,
    bool isPrimary = true,
    bool isEnabled = true,
    Duration? animationDuration,
    EdgeInsets? padding,
  }) {
    return _AnimatedButton(
      onPressed: isEnabled ? onPressed : null,
      isPrimary: isPrimary,
      animationDuration: animationDuration ?? UIConstants.shortAnimation,
      padding: padding ?? EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
      child: child,
    );
  }

  /// Animated avatar with scale and glow effects
  static Widget animatedAvatar({
    required Widget child,
    required BuildContext context,
    VoidCallback? onTap,
    bool isEnabled = true,
    Duration? animationDuration,
  }) {
    return _AnimatedAvatar(
      onTap: isEnabled ? onTap : null,
      animationDuration: animationDuration ?? UIConstants.shortAnimation,
      child: child,
    );
  }

  /// Animated progress bar with smooth transitions
  static Widget animatedProgress({
    required double value,
    required BuildContext context,
    Color? progressColor,
    Color? backgroundColor,
    double height = 8.0,
    Duration? animationDuration,
  }) {
    return _AnimatedProgress(
      value: value,
      progressColor: progressColor,
      backgroundColor: backgroundColor,
      height: height,
      animationDuration: animationDuration ?? UIConstants.mediumAnimation,
    );
  }

  /// Animated badge with scale and color transitions
  static Widget animatedBadge({
    required Widget child,
    required BuildContext context,
    String status = 'default',
    bool isSmall = false,
    Duration? animationDuration,
  }) {
    return _AnimatedBadge(
      status: status,
      isSmall: isSmall,
      animationDuration: animationDuration ?? UIConstants.shortAnimation,
      child: child,
    );
  }

  /// Animated list item with slide and fade effects
  static Widget animatedListItem({
    required Widget child,
    required BuildContext context,
    required int index,
    VoidCallback? onTap,
    Duration? animationDuration,
    Duration? delay,
  }) {
    return _AnimatedListItem(
      index: index,
      onTap: onTap,
      animationDuration: animationDuration ?? UIConstants.mediumAnimation,
      delay: delay ?? Duration(milliseconds: 50 * index),
      child: child,
    );
  }
}

/// Animated card widget with press effects
class _AnimatedCard extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final EdgeInsets padding;
  final EdgeInsets margin;

  const _AnimatedCard({
    required this.child,
    this.onTap,
    required this.animationDuration,
    required this.padding,
    required this.margin,
  });

  @override
  State<_AnimatedCard> createState() => _AnimatedCardState();
}

class _AnimatedCardState extends State<_AnimatedCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.98,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: widget.margin,
            child: GestureDetector(
              onTapDown: widget.onTap != null ? _handleTapDown : null,
              onTapUp: widget.onTap != null ? _handleTapUp : null,
              onTapCancel: _handleTapCancel,
              onTap: widget.onTap,
              child: AnimatedContainer(
                duration: widget.animationDuration,
                decoration: AccountProfileThemeHelper.getCardDecoration(
                  context,
                  isPressed: _isPressed,
                ),
                child: Padding(
                  padding: widget.padding,
                  child: widget.child,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Animated button widget with scale transitions
class _AnimatedButton extends StatefulWidget {
  final Widget child;
  final VoidCallback? onPressed;
  final bool isPrimary;
  final Duration animationDuration;
  final EdgeInsets padding;

  const _AnimatedButton({
    required this.child,
    required this.onPressed,
    required this.isPrimary,
    required this.animationDuration,
    required this.padding,
  });

  @override
  State<_AnimatedButton> createState() => _AnimatedButtonState();
}

class _AnimatedButtonState extends State<_AnimatedButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.onPressed != null ? _handleTapDown : null,
            onTapUp: widget.onPressed != null ? _handleTapUp : null,
            onTapCancel: _handleTapCancel,
            onTap: widget.onPressed,
            child: AnimatedContainer(
              duration: widget.animationDuration,
              decoration: AccountProfileThemeHelper.getButtonDecoration(
                context,
                isPrimary: widget.isPrimary,
                isPressed: _isPressed,
                isDisabled: widget.onPressed == null,
              ),
              child: Padding(
                padding: widget.padding,
                child: widget.child,
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Animated avatar widget with scale effects
class _AnimatedAvatar extends StatefulWidget {
  final Widget child;
  final VoidCallback? onTap;
  final Duration animationDuration;

  const _AnimatedAvatar({
    required this.child,
    this.onTap,
    required this.animationDuration,
  });

  @override
  State<_AnimatedAvatar> createState() => _AnimatedAvatarState();
}

class _AnimatedAvatarState extends State<_AnimatedAvatar>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    setState(() => _isPressed = true);
    _controller.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  void _handleTapCancel() {
    setState(() => _isPressed = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: GestureDetector(
            onTapDown: widget.onTap != null ? _handleTapDown : null,
            onTapUp: widget.onTap != null ? _handleTapUp : null,
            onTapCancel: _handleTapCancel,
            onTap: widget.onTap,
            child: widget.child,
          ),
        );
      },
    );
  }
}

/// Animated progress bar widget
class _AnimatedProgress extends StatefulWidget {
  final double value;
  final Color? progressColor;
  final Color? backgroundColor;
  final double height;
  final Duration animationDuration;

  const _AnimatedProgress({
    required this.value,
    this.progressColor,
    this.backgroundColor,
    required this.height,
    required this.animationDuration,
  });

  @override
  State<_AnimatedProgress> createState() => _AnimatedProgressState();
}

class _AnimatedProgressState extends State<_AnimatedProgress>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _progressAnimation;
  double _previousValue = 0.0;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.value,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _controller.forward();
  }

  @override
  void didUpdateWidget(_AnimatedProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.value != widget.value) {
      _previousValue = oldWidget.value;
      _progressAnimation = Tween<double>(
        begin: _previousValue,
        end: widget.value,
      ).animate(CurvedAnimation(
        parent: _controller,
        curve: Curves.easeInOut,
      ));
      _controller.reset();
      _controller.forward();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final progressConfig = AccountProfileThemeHelper.getProgressConfig(
      context,
      widget.value * 100,
    );
    
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Container(
          height: widget.height,
          decoration: BoxDecoration(
            color: widget.backgroundColor ?? progressConfig.backgroundColor,
            borderRadius: BorderRadius.circular(progressConfig.borderRadius),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: _progressAnimation.value.clamp(0.0, 1.0),
            child: Container(
              decoration: BoxDecoration(
                color: widget.progressColor ?? progressConfig.progressColor,
                borderRadius: BorderRadius.circular(progressConfig.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: (widget.progressColor ?? progressConfig.progressColor)
                        .withValues(alpha: 0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

/// Animated badge widget
class _AnimatedBadge extends StatefulWidget {
  final Widget child;
  final String status;
  final bool isSmall;
  final Duration animationDuration;

  const _AnimatedBadge({
    required this.child,
    required this.status,
    required this.isSmall,
    required this.animationDuration,
  });

  @override
  State<_AnimatedBadge> createState() => _AnimatedBadgeState();
}

class _AnimatedBadgeState extends State<_AnimatedBadge>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));
    
    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final badgeConfig = AccountProfileThemeHelper.getStatusBadgeConfig(
      context,
      widget.status,
      isSmall: widget.isSmall,
    );
    
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            padding: badgeConfig.padding,
            decoration: BoxDecoration(
              color: badgeConfig.backgroundColor,
              borderRadius: BorderRadius.circular(
                AccountProfileThemeHelper.badgeBorderRadius,
              ),
              border: Border.all(
                color: badgeConfig.borderColor,
                width: 0.5,
              ),
            ),
            child: DefaultTextStyle(
              style: TextStyle(
                color: badgeConfig.textColor,
                fontSize: badgeConfig.fontSize,
                fontWeight: FontWeight.w500,
              ),
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}

/// Animated list item widget with slide and fade effects
class _AnimatedListItem extends StatefulWidget {
  final Widget child;
  final int index;
  final VoidCallback? onTap;
  final Duration animationDuration;
  final Duration delay;

  const _AnimatedListItem({
    required this.child,
    required this.index,
    this.onTap,
    required this.animationDuration,
    required this.delay,
  });

  @override
  State<_AnimatedListItem> createState() => _AnimatedListItemState();
}

class _AnimatedListItemState extends State<_AnimatedListItem>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0.3, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    // Start animation after delay
    Future.delayed(widget.delay, () {
      if (mounted) {
        _controller.forward();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return SlideTransition(
          position: _slideAnimation,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: GestureDetector(
              onTap: widget.onTap,
              child: widget.child,
            ),
          ),
        );
      },
    );
  }
}