import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/activity_log_item.dart';
import '../../models/scheduled_activity.dart';
import '../../presentation/scheduler/widgets/scheduled_activity_card.dart';

class RecentLoggedAndScheduledActivitiesWidget extends StatefulWidget {
  final List<ActivityLog> activityLogs;
  final List<ScheduledActivity> scheduledActivities;
  final Function(ScheduledActivity)? onScheduledActivityCompleted;
  final Function(ScheduledActivity)? onScheduledActivityDeleted;

  const RecentLoggedAndScheduledActivitiesWidget({
    super.key,
    required this.activityLogs,
    required this.scheduledActivities,
    this.onScheduledActivityCompleted,
    this.onScheduledActivityDeleted,
  });

  @override
  State<RecentLoggedAndScheduledActivitiesWidget> createState() => _RecentLoggedAndScheduledActivitiesWidgetState();
}

class _RecentLoggedAndScheduledActivitiesWidgetState extends State<RecentLoggedAndScheduledActivitiesWidget> {

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    // Get recent scheduled activities (limit to 5)
    final recentScheduledActivities = widget.scheduledActivities.take(5).toList();
    
    // Get recent logged activities (limit to 5)
    final recentLoggedActivities = widget.activityLogs.take(5).toList();
    
    final hasScheduledActivities = recentScheduledActivities.isNotEmpty;
    final hasLoggedActivities = recentLoggedActivities.isNotEmpty;
    final hasAnyActivities = hasScheduledActivities || hasLoggedActivities;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header
        Padding(
          padding: EdgeInsets.only(bottom: 1.h),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Recent Activities & Schedules',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 0.3.h),
                    Text(
                      'Latest Scheduled and Logged Activities',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pushNamed(context, AppRoutes.activityTimeline);
                },
                child: Row(
                  children: [
                    Text(
                      'View All',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: colorScheme.primary,
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: colorScheme.primary,
                      size: 5.w,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        
        // Activities content
        if (!hasAnyActivities)
          _buildEmptyState(context)
        else ...[
          // Scheduled Activities Section
          if (hasScheduledActivities) ...[
            _buildSectionHeader(context, 'Scheduled Activities', recentScheduledActivities.length),
            SizedBox(height: 1.h),
            ...recentScheduledActivities.map((activity) => Padding(
              padding: EdgeInsets.only(bottom: 1.h),
              child: ScheduledActivityCard(
                activity: activity,
                onCompleted: widget.onScheduledActivityCompleted != null 
                    ? () => widget.onScheduledActivityCompleted!(activity)
                    : () {},
                onDeleted: widget.onScheduledActivityDeleted != null
                    ? () => widget.onScheduledActivityDeleted!(activity)
                    : () {},
                showTimelineButton: false,
              ),
            )),
            
            // Add divider if both sections have content
            if (hasLoggedActivities) ...[
              SizedBox(height: 1.h),
              Divider(
                color: colorScheme.outline.withValues(alpha: 0.3),
                thickness: 1,
              ),
              SizedBox(height: 1.h),
            ],
          ],
          
          // Logged Activities Section
          if (hasLoggedActivities) ...[
            _buildSectionHeader(context, 'Logged Activities', recentLoggedActivities.length),
            SizedBox(height: 1.h),
            ...recentLoggedActivities.map((log) => Padding(
              padding: EdgeInsets.only(bottom: 1.h),
              child: ActivityLogItem(activity: log),
            )),
          ],
        ],
      ],
    );
  }

  Widget _buildSectionHeader(BuildContext context, String title, int count) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Row(
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            color: colorScheme.onSurface,
          ),
        ),
        SizedBox(width: 2.w),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.3.h),
          decoration: BoxDecoration(
            color: colorScheme.primary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'hourglass_empty',
            color: colorScheme.outline.withValues(alpha: 0.5),
            size: 8.w,
          ),
          SizedBox(height: 1.h),
          Text(
            'No recent activities',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 0.5.h),
          Text(
            'Start logging activities or adding schedules to see them here',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}