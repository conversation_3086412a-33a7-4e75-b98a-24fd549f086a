import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class GrowthChartPreviewWidget extends StatelessWidget {
  final Map<String, dynamic> growthData;
  final VoidCallback onTap;

  const GrowthChartPreviewWidget({
    super.key,
    required this.growthData,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final String currentWeight =
        (growthData['currentWeight'] as String?) ?? '0 kg';
    final String currentHeight =
        (growthData['currentHeight'] as String?) ?? '0 cm';
    final String lastMeasurement =
        (growthData['lastMeasurement'] as String?) ?? 'Never';
    final String trend = (growthData['trend'] as String?) ?? 'stable';

    return GestureDetector(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section Header - clean without icon
          Padding(
            padding: EdgeInsets.only(bottom: 1.h),
            child: Row(
              children: [
                Text(
                  'Growth Tracking',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                ),
                Spacer(),
                Row(
                  children: [
                    Text(
                      'View Charts',
                      style: Theme.of(context).textTheme.labelLarge?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                    SizedBox(width: 1.w),
                    CustomIconWidget(
                      iconName: 'chevron_right',
                      color: Theme.of(context).colorScheme.primary,
                      size: 4.w,
                    ),
                  ],
                ),
              ],
            ),
          ),

          SizedBox(height: 2.h),

          // Growth Metrics
          Row(
            children: [
              Expanded(
                child: _buildGrowthMetric(
                  context,
                  'Weight',
                  currentWeight,
                  'monitor_weight',
                  Theme.of(context).colorScheme.primary,
                ),
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: _buildGrowthMetric(
                  context,
                  'Height',
                  currentHeight,
                  'height',
                  ThemeAwareColors.getSuccessColor(context),
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          // Trend Indicator
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: _getTrendColor(trend, context).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getTrendColor(trend, context).withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                CustomIconWidget(
                  iconName: _getTrendIcon(trend),
                  color: _getTrendColor(trend, context),
                  size: 5.w,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Growth Trend',
                        style:
                            Theme.of(context).textTheme.labelMedium?.copyWith(
                                  color: Theme.of(context)
                                      .colorScheme
                                      .onSurfaceVariant,
                                ),
                      ),
                      Text(
                        _getTrendDescription(trend),
                        style:
                            Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  color: _getTrendColor(trend, context),
                                  fontWeight: FontWeight.w500,
                                ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGrowthMetric(
    BuildContext context,
    String label,
    String value,
    String iconName,
    Color color,
  ) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: iconName,
            color: color,
            size: 6.w,
          ),
          SizedBox(height: 1.h),
          Text(
            value,
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w700,
                  color: color,
                ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
          ),
        ],
      ),
    );
  }

  Color _getTrendColor(String trend, BuildContext context) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return ThemeAwareColors.getSuccessColor(context);
      case 'decreasing':
        return ThemeAwareColors.getWarningColor(context);
      case 'stable':
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  String _getTrendIcon(String trend) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return 'trending_up';
      case 'decreasing':
        return 'trending_down';
      case 'stable':
      default:
        return 'trending_flat';
    }
  }

  String _getTrendDescription(String trend) {
    switch (trend.toLowerCase()) {
      case 'increasing':
        return 'Healthy growth pattern';
      case 'decreasing':
        return 'Monitor closely';
      case 'stable':
      default:
        return 'Consistent development';
    }
  }
}
