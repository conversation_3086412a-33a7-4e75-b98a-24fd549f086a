import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../baby_profile_photo_widget.dart';

class BabyProfileHeaderWidget extends StatelessWidget {
  final BabyProfile babyProfile;
  final bool hasMultipleBabies;
  final VoidCallback? onTapProfile;
  final VoidCallback? onTapSwitch;

  const BabyProfileHeaderWidget({
    super.key,
    required this.babyProfile,
    this.onTapProfile,
    this.onTapSwitch,
    this.hasMultipleBabies = false,
  });

  String _calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    final difference = now.difference(birthDate);
    final months = (difference.inDays / 30.44).floor();
    final days = difference.inDays % 30;

    if (months > 0) {
      return '$months months, $days days';
    } else {
      return '${difference.inDays} days';
    }
  }

  String _formatBirthDate(DateTime birthDate) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${birthDate.day} ${months[birthDate.month - 1]} ${birthDate.year}';
  }

  @override
  Widget build(BuildContext context) {
    final String babyName = (babyProfile.name as String?) ?? 'Baby';
    final DateTime? birthDate = babyProfile.birthDate as DateTime?;
    final String? photoUrl = babyProfile.photo as String?;
    final String? note = babyProfile.note;
    
    // Debug output to verify hasMultipleBabies parameter
    debugPrint('👶 BabyProfileHeaderWidget: Baby: $babyName, hasMultipleBabies: $hasMultipleBabies');

    return GestureDetector(
      onTap: onTapProfile,
      child: Container(
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: ThemeAwareColors.getShadowColor(context),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // Main Row: Photo, Name/Age/Birth Date, Switch Indicator
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Baby Photo
                BabyProfilePhotoVariants.medium(
                  photoUrl: photoUrl,
                  babyName: babyName,
                  gender: babyProfile.gender,
                  onTap: onTapProfile,
                ),

                SizedBox(width: 3.w),

                // Baby Info Column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name with dropdown indicator
                      Row(
                        children: [
                          Flexible(
                            child: Text(
                              babyName,
                              style: Theme.of(context)
                                  .textTheme
                                  .titleLarge
                                  ?.copyWith(
                                    fontWeight: FontWeight.w700,
                                    color: Theme.of(context).colorScheme.onSurface,
                                  ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          if (hasMultipleBabies) ...[
                            SizedBox(width: 1.w),
                            GestureDetector(
                              onTap: () {
                                if (onTapSwitch != null) onTapSwitch!();
                              },
                              child: Row(
                                children: [
                                  CustomIconWidget(
                                    iconName: 'keyboard_arrow_down',
                                    color: Theme.of(context).colorScheme.primary,
                                    size: 5.w,
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(left: 1.w),
                                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                                      ),
                                    ),
                                    child: Text(
                                      'Tap to switch',
                                      style: Theme.of(context).textTheme.labelSmall?.copyWith(
                                            color: Theme.of(context).colorScheme.primary,
                                            fontWeight: FontWeight.w600,
                                          ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ],
                      ),
                      
                      // Age
                      if (birthDate != null)
                        Text(
                          _calculateAge(birthDate),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: Theme.of(context).colorScheme.onSurfaceVariant,
                                fontWeight: FontWeight.w500,
                              ),
                          overflow: TextOverflow.ellipsis,
                        ),

                      // Birth Date (moved here under name/age)
                      if (birthDate != null) ...[
                        SizedBox(height: 0.5.h),
                        Row(
                          children: [
                            CustomIconWidget(
                              iconName: 'cake',
                              color: Theme.of(context).colorScheme.secondary,
                              size: 3.5.w,
                            ),
                            SizedBox(width: 1.w),
                            Text(
                              'Born ${_formatBirthDate(birthDate)}',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                                    fontWeight: FontWeight.w500,
                                  ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ],

                      if (note != null && note.isNotEmpty) ...[
                        SizedBox(height: 0.5.h),
                        Text(
                          'Note: $note',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: ThemeAwareColors.getErrorColor(context),
                            fontStyle: FontStyle.italic,
                            fontSize: 10.sp,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
