import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:intl/intl.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_icon_widget.dart';
import '../../models/scheduled_activity.dart';
import '../../utils/activity_icon_manager.dart';
import '../../utils/activity_type_config.dart';

class TodaySchedulesCardWidget extends StatefulWidget {
  final List<ScheduledActivity> schedules;
  final Function(ScheduledActivity)? onScheduleTap;

  const TodaySchedulesCardWidget({
    super.key,
    required this.schedules,
    this.onScheduleTap,
  });

  @override
  State<TodaySchedulesCardWidget> createState() => _TodaySchedulesCardWidgetState();
}

class _TodaySchedulesCardWidgetState extends State<TodaySchedulesCardWidget> {
  bool _isExpanded = false;
  static const int _initialDisplayCount = 5;

  @override
  Widget build(BuildContext context) {
    final todaySchedules = _getTodaySchedules();
    final upcomingSchedules = _getUpcomingSchedules(todaySchedules);
    final completedSchedules = _getCompletedSchedules(todaySchedules);
    final overdueSchedules = _getOverdueSchedules(todaySchedules);
    
    // Combine overdue and upcoming for display, prioritizing overdue
    final allActiveSchedules = [...overdueSchedules, ...upcomingSchedules];
    final displayedSchedules = allActiveSchedules.take(_initialDisplayCount).toList();
    final hasMoreSchedules = allActiveSchedules.length > _initialDisplayCount;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header - matching the activity logs style
        Padding(
          padding: EdgeInsets.only(bottom: 1.h),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Today\'s Schedules',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: ThemeAwareColors.getPrimaryTextColor(context),
                          ),
                    ),
                    Text(
                      _buildSubtitle(upcomingSchedules.length, completedSchedules.length, overdueSchedules.length),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                    ),
                  ],
                ),
              ),
              TextButton(
                onPressed: () => Navigator.pushNamed(context, '/scheduler'),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'View All',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                      ),
                    ),
                    Icon(
                      Icons.chevron_right,
                      color: Theme.of(context).colorScheme.primary,
                      size: 5.w,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // Schedule List Container - matching the activity logs style
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: ThemeAwareColors.getDividerColor(context),
              width: 1,
            ),
          ),
          child: displayedSchedules.isEmpty
              ? _buildEmptyState()
              : Column(
                  children: [
                    // Display schedules
                    ...displayedSchedules.asMap().entries.map((entry) {
                      final index = entry.key;
                      final schedule = entry.value;
                      return Column(
                        children: [
                          _buildScheduleItem(schedule),
                          if (index < displayedSchedules.length - 1)
                            Divider(
                              height: 2.h,
                              color: ThemeAwareColors.getDividerColor(context),
                            ),
                        ],
                      );
                    }),
                    
                    // Show more button if needed
                    if (hasMoreSchedules) ...[
                      Divider(
                        height: 2.h,
                        color: ThemeAwareColors.getDividerColor(context),
                      ),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _isExpanded = !_isExpanded;
                          });
                        },
                        child: Text(
                          _isExpanded ? 'Show Less' : 'Show ${allActiveSchedules.length - _initialDisplayCount} More',
                          style: TextStyle(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
        ),
      ],
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 2.h),
        child: Column(
          children: [
            Icon(
              Icons.schedule,
              size: 8.w,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: 1.h),
            Text(
              'No schedules for today',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleItem(ScheduledActivity schedule) {
    final timeFormat = DateFormat('h:mm a');
    final scheduleTime = schedule.getNextOccurrence() ?? schedule.scheduledTime;
    final isOverdue = scheduleTime.isBefore(DateTime.now()) && !schedule.isCompleted;
    
    return InkWell(
      onTap: () => widget.onScheduleTap?.call(schedule),
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 1.w),
        child: Row(
          children: [
            // Activity Icon
            Container(
              width: 10.w,
              height: 10.w,
              decoration: BoxDecoration(
                color: ActivityTypeConfig.getColor(schedule.correspondingQuickLogActivityType, context).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: ActivityIconManager.getActivityIcon(
                  activityType: schedule.correspondingQuickLogActivityType,
                  size: 5.w,
                  color: ActivityTypeConfig.getColor(schedule.correspondingQuickLogActivityType, context),
                ),
              ),
            ),
            
            SizedBox(width: 3.w),
            
            // Schedule Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    schedule.title,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: ThemeAwareColors.getPrimaryTextColor(context),
                        ),
                  ),
                  if (schedule.description?.isNotEmpty == true) ...[
                    SizedBox(height: 0.5.h),
                    Text(
                      schedule.description!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            
            SizedBox(width: 2.w),
            
            // Time and Status
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  timeFormat.format(scheduleTime),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: isOverdue 
                            ? Theme.of(context).colorScheme.error
                            : Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
                if (schedule.isCompleted)
                  Container(
                    margin: EdgeInsets.only(top: 0.5.h),
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.3.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      'Completed',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.primary,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                  ),
                if (isOverdue && !schedule.isCompleted)
                  Container(
                    margin: EdgeInsets.only(top: 0.5.h),
                    child: CustomIconWidget(
                      iconName: 'warning',
                      color: Theme.of(context).colorScheme.error,
                      size: 3.w,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  List<ScheduledActivity> _getTodaySchedules() {
    final now = DateTime.now();
    
    return widget.schedules.where((schedule) {
      final nextOccurrence = schedule.getNextOccurrence() ?? schedule.scheduledTime;
      final localTime = nextOccurrence.toLocal();
      
      // Check if the schedule is for today by comparing dates
      final scheduleDate = DateTime(localTime.year, localTime.month, localTime.day);
      final todayDate = DateTime(now.year, now.month, now.day);
      final tomorrowDate = todayDate.add(const Duration(days: 1));
      
      // Include both today and tomorrow in case of timezone issues
      // This is a temporary fix while we investigate the timezone handling
      return scheduleDate.isAtSameMomentAs(todayDate) || scheduleDate.isAtSameMomentAs(tomorrowDate);
    }).toList();
  }

  List<ScheduledActivity> _getUpcomingSchedules(List<ScheduledActivity> todaySchedules) {
    final now = DateTime.now();
    return todaySchedules.where((schedule) {
      final nextOccurrence = schedule.getNextOccurrence() ?? schedule.scheduledTime;
      return nextOccurrence.isAfter(now) && !schedule.isCompleted && schedule.isActive;
    }).toList()
      ..sort((a, b) {
        final aTime = a.getNextOccurrence() ?? a.scheduledTime;
        final bTime = b.getNextOccurrence() ?? b.scheduledTime;
        return aTime.compareTo(bTime);
      });
  }

  List<ScheduledActivity> _getCompletedSchedules(List<ScheduledActivity> todaySchedules) {
    return todaySchedules.where((schedule) => schedule.isCompleted).toList();
  }

  List<ScheduledActivity> _getOverdueSchedules(List<ScheduledActivity> todaySchedules) {
    final now = DateTime.now();
    return todaySchedules.where((schedule) {
      final nextOccurrence = schedule.getNextOccurrence() ?? schedule.scheduledTime;
      return nextOccurrence.isBefore(now) && !schedule.isCompleted && !schedule.isRecurring;
    }).toList();
  }

  String _buildSubtitle(int upcoming, int completed, int overdue) {
    return '$upcoming upcoming • $completed completed • $overdue overdue';
  }
}