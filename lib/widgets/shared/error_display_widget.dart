import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../services/error_handling_service.dart';
import '../../theme/theme_aware_colors.dart';
import '../custom_elevated_button.dart';
import '../custom_icon_widget.dart';

/// Configuration for error display styling
class _ErrorDisplayConfig {
  final Color errorColor;
  final String iconName;
  final bool isWarning;
  
  const _ErrorDisplayConfig({
    required this.errorColor,
    required this.iconName,
    required this.isWarning,
  });
  
  factory _ErrorDisplayConfig.fromError(AccountProfileError error, ThemeData theme) {
    final isWarning = error.isWarning;
    final errorColor = isWarning 
        ? theme.colorScheme.secondary 
        : theme.colorScheme.error;
    
    return _ErrorDisplayConfig(
      errorColor: errorColor,
      iconName: error.iconName,
      isWarning: isWarning,
    );
  }
}

/// Comprehensive error display widget for account profile operations
/// Provides user-friendly error messages with recovery options
class ErrorDisplayWidget extends StatefulWidget {
  final AccountProfileError? error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool showDismiss;
  final bool isCompact;
  final EdgeInsetsGeometry? margin;

  const ErrorDisplayWidget({
    super.key,
    required this.error,
    this.onRetry,
    this.onDismiss,
    this.showDismiss = true,
    this.isCompact = false,
    this.margin,
  });

  @override
  State<ErrorDisplayWidget> createState() => _ErrorDisplayWidgetState();
}

class _ErrorDisplayWidgetState extends State<ErrorDisplayWidget> {
  bool _isRetrying = false;
  DateTime? _lastRetryTime;

  /// Handle retry with debouncing
  void _handleRetry() {
    final now = DateTime.now();
    
    // Debounce retry attempts (minimum 1 second between retries)
    if (_lastRetryTime != null && 
        now.difference(_lastRetryTime!).inMilliseconds < 1000) {
      return;
    }
    
    if (_isRetrying) return;
    
    setState(() {
      _isRetrying = true;
      _lastRetryTime = now;
    });
    
    widget.onRetry?.call();
    
    // Reset retry state after a short delay
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted) {
        setState(() {
          _isRetrying = false;
        });
      }
    });
  }

  /// Get safe error with fallbacks for null or empty values
  AccountProfileError get _safeError {
    if (widget.error == null) {
      return AccountProfileError(
        type: AccountProfileErrorType.unknown,
        title: 'Unknown Error',
        message: 'An unexpected error occurred',
        isRetryable: false,
      );
    }
    
    return AccountProfileError(
      type: widget.error!.type,
      title: widget.error!.title.isEmpty ? 'Unknown Error' : widget.error!.title,
      message: widget.error!.message.isEmpty ? 'An error occurred' : widget.error!.message,
      isRetryable: widget.error!.isRetryable,
      operation: widget.error!.operation,
      context: widget.error!.context,
    );
  }

  @override
  Widget build(BuildContext context) {
    final safeError = _safeError;
    
    return Semantics(
      label: 'Error: ${safeError.title}',
      hint: safeError.isRetryable ? 'Tap retry to try again' : 'Error information',
      child: widget.isCompact 
          ? _buildCompactError(context, safeError)
          : _buildFullError(context, safeError),
    );
  }

  /// Build full error display
  Widget _buildFullError(BuildContext context, AccountProfileError safeError) {
    final theme = Theme.of(context);
    final errorColor = safeError.isWarning 
        ? theme.colorScheme.secondary 
        : theme.colorScheme.error;

    return Card(
      elevation: 2,
      margin: widget.margin ?? EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: errorColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Container(
        padding: EdgeInsets.all(6.w),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              errorColor.withValues(alpha: 0.05),
              errorColor.withValues(alpha: 0.02),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Error icon and title
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: errorColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: CustomIconWidget(
                    iconName: safeError.iconName,
                    color: errorColor,
                    size: 8.w,
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        safeError.title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (safeError.operation != null) ...[
                        SizedBox(height: 0.5.h),
                        Text(
                          'Operation: ${safeError.operation}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            
            SizedBox(height: 3.h),
            
            // Error message
            Text(
              safeError.message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                height: 1.4,
              ),
              textAlign: TextAlign.center,
            ),
            
            SizedBox(height: 4.h),
            
            // Action buttons
            _buildActionButtons(context, theme, errorColor, safeError),
          ],
        ),
      ),
    );
  }

  /// Build compact error display
  Widget _buildCompactError(BuildContext context, AccountProfileError safeError) {
    final theme = Theme.of(context);
    final errorColor = safeError.isWarning 
        ? theme.colorScheme.secondary 
        : theme.colorScheme.error;

    return Container(
      margin: widget.margin ?? EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: errorColor.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        children: [
          CustomIconWidget(
            iconName: safeError.iconName,
            color: errorColor,
            size: 5.w,
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  safeError.title,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  safeError.message,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          if (safeError.isRetryable && widget.onRetry != null) ...[
            SizedBox(width: 2.w),
            IconButton(
              onPressed: _isRetrying ? null : _handleRetry,
              icon: CustomIconWidget(
                iconName: 'refresh',
                color: errorColor,
                size: 5.w,
              ),
              tooltip: 'Retry',
            ),
          ],
          if (widget.showDismiss && widget.onDismiss != null) ...[
            SizedBox(width: 1.w),
            IconButton(
              onPressed: widget.onDismiss,
              icon: CustomIconWidget(
                iconName: 'close',
                color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 4.w,
              ),
              tooltip: 'Dismiss',
            ),
          ],
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(BuildContext context, ThemeData theme, Color errorColor, AccountProfileError safeError) {
    final buttons = <Widget>[];

    // Retry button
    if (safeError.isRetryable && widget.onRetry != null) {
      buttons.add(
        Expanded(
          child: CustomElevatedButton(
            text: _isRetrying ? 'Retrying...' : 'Try Again',
            onPressed: _isRetrying ? null : _handleRetry,
            style: ElevatedButton.styleFrom(
              backgroundColor: errorColor,
              foregroundColor: theme.colorScheme.onError,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            icon: CustomIconWidget(
              iconName: 'refresh',
              color: theme.colorScheme.onError,
              size: 5.w,
            ),
          ),
        ),
      );
    }

    // Dismiss button
    if (widget.showDismiss && widget.onDismiss != null) {
      if (buttons.isNotEmpty) {
        buttons.add(SizedBox(width: 3.w));
      }
      
      buttons.add(
        Expanded(
          child: OutlinedButton(
            onPressed: widget.onDismiss,
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              side: BorderSide(
                color: theme.colorScheme.outline.withValues(alpha: 0.5),
              ),
            ),
            child: const Text('Dismiss'),
          ),
        ),
      );
    }

    // Contact support button for non-retryable errors
    if (!safeError.isRetryable && safeError.type != AccountProfileErrorType.authError) {
      if (buttons.isNotEmpty) {
        buttons.add(SizedBox(width: 3.w));
      }
      
      buttons.add(
        Expanded(
          child: OutlinedButton.icon(
            onPressed: () => _showSupportDialog(context, safeError),
            icon: CustomIconWidget(
              iconName: 'support',
              color: theme.colorScheme.primary,
              size: 4.w,
            ),
            label: const Text('Get Help'),
            style: OutlinedButton.styleFrom(
              foregroundColor: theme.colorScheme.primary,
              padding: EdgeInsets.symmetric(vertical: 2.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              side: BorderSide(color: theme.colorScheme.primary),
            ),
          ),
        ),
      );
    }

    if (buttons.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(children: buttons);
  }

  /// Show support dialog
  void _showSupportDialog(BuildContext context, AccountProfileError safeError) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Need Help?'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'If this problem persists, please contact our support team with the following information:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 2.h),
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Error Details:',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Type: ${safeError.type.name}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                  if (safeError.operation != null)
                    Text(
                      'Operation: ${safeError.operation}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        fontFamily: 'monospace',
                      ),
                    ),
                  Text(
                    'Time: ${DateTime.now().toIso8601String()}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontFamily: 'monospace',
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement support contact functionality
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Support contact feature coming soon'),
                ),
              );
            },
            child: const Text('Contact Support'),
          ),
        ],
      ),
    );
  }
}

/// Network status banner widget
class NetworkStatusBanner extends StatelessWidget {
  final bool isOnline;
  final VoidCallback? onRetry;

  const NetworkStatusBanner({
    super.key,
    required this.isOnline,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (isOnline) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: theme.colorScheme.error,
        boxShadow: [
          BoxShadow(
            color: theme.colorScheme.error.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            CustomIconWidget(
              iconName: 'wifi_off',
              color: theme.colorScheme.onError,
              size: 5.w,
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'No Internet Connection',
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onError,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    'Some features may not work properly',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onError.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            if (onRetry != null)
              TextButton(
                onPressed: onRetry,
                style: TextButton.styleFrom(
                  foregroundColor: theme.colorScheme.onError,
                ),
                child: const Text('Retry'),
              ),
          ],
        ),
      ),
    );
  }
}

/// Error boundary widget for catching and displaying widget errors
class ErrorBoundary extends StatefulWidget {
  final Widget child;
  final Widget Function(Object error, StackTrace? stackTrace)? errorBuilder;
  final void Function(Object error, StackTrace? stackTrace)? onError;

  const ErrorBoundary({
    super.key,
    required this.child,
    this.errorBuilder,
    this.onError,
  });

  @override
  State<ErrorBoundary> createState() => _ErrorBoundaryState();
}

class _ErrorBoundaryState extends State<ErrorBoundary> {
  Object? _error;
  StackTrace? _stackTrace;

  @override
  Widget build(BuildContext context) {
    if (_error != null) {
      if (widget.errorBuilder != null) {
        return widget.errorBuilder!(_error!, _stackTrace);
      }
      
      return ErrorDisplayWidget(
        error: AccountProfileError(
          type: AccountProfileErrorType.unknown,
          title: 'Widget Error',
          message: 'An error occurred while displaying this content.',
          isRetryable: true,
        ),
        onRetry: () {
          setState(() {
            _error = null;
            _stackTrace = null;
          });
        },
      );
    }

    return ErrorCatcher(
      onError: (error, stackTrace) {
        setState(() {
          _error = error;
          _stackTrace = stackTrace;
        });
        widget.onError?.call(error, stackTrace);
      },
      child: widget.child,
    );
  }
}

/// Error catcher widget that catches errors in child widgets
class ErrorCatcher extends StatelessWidget {
  final Widget child;
  final void Function(Object error, StackTrace stackTrace) onError;

  const ErrorCatcher({
    super.key,
    required this.child,
    required this.onError,
  });

  @override
  Widget build(BuildContext context) {
    return child;
  }
}