

  String _getActivityDetails(Map<String, dynamic> activity) {
    List<String> details = [];
    
    // Amount (for feeding, medicine)
    if (activity["amount"] != null && activity["amount"] != "") {
      details.add("${activity["amount"]} ${activity["unit"] ?? ""}");
    }
    
    // Duration (for sleep, feeding)
    if (activity["duration"] != null && activity["duration"] != "") {
      details.add(activity["duration"]);
    }
    
    // Feeding specific details
    if (activity["feeding_type"] != null && activity["feeding_type"] != "") {
      details.add(activity["feeding_type"]);
    }
    if (activity["formula_type"] != null && activity["formula_type"] != "") {
      details.add(activity["formula_type"]);
    }
    if (activity["meal_type"] != null && activity["meal_type"] != "") {
      details.add(activity["meal_type"]);
    }
    if (activity["food_items"] != null && activity["food_items"] != "") {
      details.add(activity["food_items"]);
    }
    
    // Diaper specific details
    if (activity["diaper_type"] != null && activity["diaper_type"] != "") {
      details.add(activity["diaper_type"]);
    }
    
    // Sleep specific details
    if (activity["sleep_quality"] != null && activity["sleep_quality"] != "") {
      details.add("Quality: ${activity["sleep_quality"]}");
    }
    if (activity["sleep_location"] != null && activity["sleep_location"] != "") {
      details.add("Location: ${activity["sleep_location"]}");
    }
    if (activity["room_temperature"] != null && activity["room_temperature"] != "") {
      details.add("Temp: ${activity["room_temperature"]}C");
    }
    
    // Milestone specific details
    if (activity["milestone_title"] != null && activity["milestone_title"] != "") {
      details.add(activity["milestone_title"]);
    }
    if (activity["milestone_description"] != null && activity["milestone_description"] != "") {
      details.add(activity["milestone_description"]);
    }
    if (activity["milestone_category"] != null && activity["milestone_category"] != "") {
      details.add("Category: ${activity["milestone_category"]}");
    }
    
    // Medicine specific details
    if (activity["medication"] != null && activity["medication"] != "") {
      details.add(activity["medication"]);
    }
    if (activity["dosage"] != null && activity["dosage"] != "") {
      details.add(activity["dosage"]);
    }
    
    // Vaccination specific details
    if (activity["vaccine"] != null && activity["vaccine"] != "") {
      details.add(activity["vaccine"]);
    }
    if (activity["batch_number"] != null && activity["batch_number"] != "") {
      details.add("Batch: ${activity["batch_number"]}");
    }
    if (activity["provider"] != null && activity["provider"] != "") {
      details.add("Provider: ${activity["provider"]}");
    }
    if (activity["location"] != null && activity["location"] != "") {
      details.add("Location: ${activity["location"]}");
    }
    
    // Mood (for feeding and other activities)
    if (activity["mood"] != null && activity["mood"] != "") {
      details.add("Mood: ${activity["mood"]}");
    }
    
    // Notes (if provided)
    if (activity["notes"] != null && activity["notes"] != "" && activity["notes"] != "null") {
      details.add("Note: ${activity["notes"]}");
    }
    
    return details.join(" • ");
  }
}
