import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../widgets/custom_icon_widget.dart';
import '../../utils/activity_type_config.dart';

class RecentActivitiesWidget extends StatefulWidget {
  final List<Map<String, dynamic>> activities;

  const RecentActivitiesWidget({
    super.key,
    required this.activities,
  });

  @override
  State<RecentActivitiesWidget> createState() => _RecentActivitiesWidgetState();
}

class _RecentActivitiesWidgetState extends State<RecentActivitiesWidget> {
  bool _isExpanded = false;
  static const int _initialDisplayCount = 3;

  @override
  Widget build(BuildContext context) {
    final displayedActivities = _isExpanded 
        ? widget.activities 
        : widget.activities.take(_initialDisplayCount).toList();
    final hasMoreActivities = widget.activities.length > _initialDisplayCount;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header - clean without icon
        Padding(
          padding: EdgeInsets.only(bottom: 1.h),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'Recent Activities',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                    Text(
                      'Latest updates from all logs',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                    ),
                  ],
                ),
              ),
              GestureDetector(
                onTap: () => Navigator.pushNamed(context, '/tracker-screen'),
                child: Container(
                  padding: EdgeInsets.all(1.5.w),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: CustomIconWidget(
                    iconName: 'arrow_forward',
                    color: Theme.of(context).colorScheme.primary,
                    size: 4.w,
                  ),
                ),
              ),
            ],
          ),
        ),
        
        // Activities list
        if (widget.activities.isEmpty)
          Container(
            padding: EdgeInsets.all(3.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
            ),
            child: Column(
              children: [
                CustomIconWidget(
                  iconName: 'hourglass_empty',
                  color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.5),
                  size: 8.w,
                ),
                SizedBox(height: 1.h),
                Text(
                  'No recent activities',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        fontWeight: FontWeight.w500,
                      ),
                ),
                SizedBox(height: 0.5.h),
                Text(
                  'Start logging activities or adding schedules to see them here',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          )
        else ...[
          // Display activities
          ...displayedActivities.map((activity) => _buildActivityItem(context, activity)),
          
          // Show expand/collapse button if there are more activities
          if (hasMoreActivities)
            Padding(
              padding: EdgeInsets.only(top: 1.h),
              child: Center(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _isExpanded 
                              ? 'Show Less' 
                              : 'Show ${widget.activities.length - _initialDisplayCount} More',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: Theme.of(context).colorScheme.onSurface,
                                fontWeight: FontWeight.w600,
                              ),
                        ),
                        SizedBox(width: 1.w),
                        CustomIconWidget(
                          iconName: _isExpanded ? 'expand_less' : 'expand_more',
                          color: Theme.of(context).colorScheme.onSurface,
                          size: 4.w,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
        ],
      ],
    );
  }

  Widget _buildActivityItem(
      BuildContext context, Map<String, dynamic> activity) {
    // Use centralized configuration for consistent colors and icons
    final String activityType = activity['type'] ?? 'custom';
    final Color activityColor = ActivityTypeConfig.getColor(activityType);
    final String activityIcon = ActivityTypeConfig.getIcon(activityType);

    // Compute dynamic time string
    String getDynamicTime(dynamic timestamp) {
      if (timestamp is! DateTime) return '';
      final now = DateTime.now();
      
      // Convert UTC timestamp to local time if needed
      DateTime localTimestamp = timestamp;
      if (timestamp.isUtc) {
        localTimestamp = timestamp.toLocal();
      }
      
      final difference = now.difference(localTimestamp);
      if (difference.inMinutes < 1) {
        return 'Just now';
      } else if (difference.inMinutes < 60) {
        return '${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return '${difference.inHours}h ago';
      } else {
        return '${localTimestamp.month}/${localTimestamp.day} ${localTimestamp.hour}:${localTimestamp.minute.toString().padLeft(2, '0')}';
      }
    }

    // If timestamp is missing or invalid, skip rendering this activity
    if (activity['timestamp'] == null || activity['timestamp'] is! DateTime) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: EdgeInsets.only(bottom: 1.5.h),
      padding: EdgeInsets.all(2.5.w),
      decoration: BoxDecoration(
        color: Theme.of(context).brightness == Brightness.dark
            ? activityColor.withValues(alpha: 0.15)
            : activityColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).brightness == Brightness.dark
              ? activityColor.withValues(alpha: 0.4)
              : activityColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // Activity Icon
          Container(
            padding: EdgeInsets.all(1.5.w),
            decoration: BoxDecoration(
              color: Theme.of(context).brightness == Brightness.dark
                  ? activityColor.withValues(alpha: 0.25)
                  : activityColor.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(8),
            ),
            child: CustomIconWidget(
              iconName: activityIcon,
              color: activityColor,
              size: 4.w,
            ),
          ),

          SizedBox(width: 2.5.w),

          // Activity Info
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        activity['title'] ?? ActivityTypeConfig.getLabel(activityType),
                        style: Theme.of(context).textTheme.labelLarge?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Text(
                      getDynamicTime(activity['timestamp']),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                            fontWeight: FontWeight.w500,
                          ),
                    ),
                  ],
                ),
                
                SizedBox(height: 0.3.h),
                
                // Activity details
                if (_getActivityDetails(activity).isNotEmpty)
                  Text(
                    _getActivityDetails(activity),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  static String _getActivityDetails(Map<String, dynamic> activity) {
    List<String> details = [];
    
    // Extract details from nested details object if it exists
    final activityDetails = activity['details'] as Map<String, dynamic>? ?? {};
    
    // Amount (for feeding, medicine) - check both root level and details
    // But only show amount for bottle feeding, not solid feeding
    final amount = activity['quantity'] ?? activity['amount'] ?? activityDetails['amount'];
    final unit = activity['unit'] ?? activityDetails['unit'] ?? 'ml';
    final feedingType = activityDetails['feeding_type'] ?? activity['feeding_type'];
    
    if (amount != null && amount != '') {
      // Only show amount for bottle feeding and non-feeding activities
      if (feedingType != 'solid' && feedingType != 'breast') {
        details.add('${amount} ${unit}');
      }
    }
    
    // Duration (for sleep, feeding)
    if (activity['duration'] != null && activity['duration'] != '') {
      details.add(activity['duration']);
    }
    
    // Feeding specific details
    if (feedingType != null && feedingType != '') {
      details.add(feedingType);
    }
    
    final formulaType = activityDetails['formula_type'] ?? activity['formula_type'];
    if (formulaType != null && formulaType != '') {
      details.add(formulaType);
    }
    
    final mealType = activityDetails['meal_type'] ?? activity['meal_type'];
    if (mealType != null && mealType != '') {
      details.add(mealType);
    }
    
    final foodItems = activityDetails['food_items'] ?? activity['food_items'];
    if (foodItems != null && foodItems != '') {
      details.add(foodItems);
    }
    
    // Diaper specific details
    final diaperType = activityDetails['diaper_type'] ?? activity['diaper_type'];
    if (diaperType != null && diaperType != '') {
      details.add(diaperType);
    }
    
    // Sleep specific details
    final sleepQuality = activityDetails['sleep_quality'] ?? activity['sleep_quality'];
    if (sleepQuality != null && sleepQuality != '') {
      details.add('Quality: ${sleepQuality}');
    }
    
    final sleepLocation = activityDetails['sleep_location'] ?? activity['sleep_location'];
    if (sleepLocation != null && sleepLocation != '') {
      details.add('Location: ${sleepLocation}');
    }
    
    final roomTemp = activityDetails['room_temperature'] ?? activity['room_temperature'];
    if (roomTemp != null && roomTemp != '') {
      details.add('Temp: ${roomTemp}C');
    }
    
    // Milestone specific details
    final milestoneTitle = activityDetails['milestone_title'] ?? activityDetails['title'] ?? activity['milestone_title'];
    if (milestoneTitle != null && milestoneTitle != '') {
      details.add(milestoneTitle);
    }
    
    final milestoneCategory = activityDetails['milestone_category'] ?? activityDetails['category'] ?? activity['milestone_category'];
    if (milestoneCategory != null && milestoneCategory != '') {
      details.add('Category: ${milestoneCategory}');
    }
    
    // Medicine specific details
    final medication = activityDetails['medication'] ?? activity['medication'];
    if (medication != null && medication != '') {
      details.add(medication);
    }
    
    final dosage = activityDetails['dosage'] ?? activity['dosage'];
    if (dosage != null && dosage != '') {
      details.add(dosage);
    }
    
    // Vaccination specific details
    final vaccine = activityDetails['vaccine'] ?? activity['vaccine'];
    if (vaccine != null && vaccine != '') {
      details.add(vaccine);
    }
    
    final batchNumber = activityDetails['batch_number'] ?? activity['batch_number'];
    if (batchNumber != null && batchNumber != '') {
      details.add('Batch: ${batchNumber}');
    }
    
    // Temperature specific details
    final temperatureReading = activityDetails['temperature_reading'] ?? activity['temperature_reading'];
    if (temperatureReading != null && temperatureReading != '') {
      details.add(temperatureReading);
    }
    
    final temperatureStatus = activityDetails['temperature_status'] ?? activity['temperature_status'];
    if (temperatureStatus != null && temperatureStatus != '') {
      details.add('(${temperatureStatus})');
    }
    
    final measurementMethod = activityDetails['measurement_method'] ?? activity['measurement_method'];
    if (measurementMethod != null && measurementMethod != '') {
      details.add(measurementMethod);
    }
    
    // Potty specific details
    final pottyType = activityDetails['potty_type'] ?? activity['potty_type'];
    if (pottyType != null && pottyType != '') {
      details.add(pottyType);
    }
    
    final successLevel = activityDetails['success_level'] ?? activity['success_level'];
    if (successLevel != null && successLevel != '') {
      details.add('(${successLevel})');
    }
    
    final pottyLocation = activityDetails['location'] ?? activity['location'];
    if (pottyLocation != null && pottyLocation != '') {
      details.add(pottyLocation);
    }
    
    final assistanceNeeded = activityDetails['assistance_needed'] ?? activity['assistance_needed'];
    if (assistanceNeeded == true) {
      details.add('With Help');
    }
    
    // Tummy time specific details
    final tummyTimeActivity = activityDetails['activity'] ?? activity['activity'];
    if (tummyTimeActivity != null && tummyTimeActivity != '') {
      details.add('Activity: ${tummyTimeActivity}');
    }
    
    final tummyTimePosition = activityDetails['position'] ?? activity['position'];
    if (tummyTimePosition != null && tummyTimePosition != '') {
      details.add('Position: ${tummyTimePosition}');
    }
    
    final tummyTimeMood = activityDetails['mood'] ?? activity['mood'];
    if (tummyTimeMood != null && tummyTimeMood != '' && activity['type'] == 'tummy_time') {
      details.add('Mood: ${tummyTimeMood}');
    }
    
    final tummyTimeDuration = activityDetails['duration'] ?? activity['duration'];
    if (tummyTimeDuration != null && tummyTimeDuration != '' && activity['type'] == 'tummy_time') {
      details.add('Duration: ${tummyTimeDuration} min');
    }
    
    // Mood (for feeding and other activities, but not tummy time since it's handled above)
    final mood = activityDetails['mood'] ?? activity['mood'];
    if (mood != null && mood != '' && activity['type'] != 'tummy_time') {
      details.add('Mood: ${mood}');
    }
    
    // Notes (if provided) - check both root level and details
    final notes = activity['notes'] ?? activityDetails['notes'];
    if (notes != null && notes != '' && notes != 'null') {
      details.add('Note: ${notes}');
    }
    
    return details.join(' - ');
  }
}
