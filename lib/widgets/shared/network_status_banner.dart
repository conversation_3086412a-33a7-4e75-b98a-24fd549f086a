import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../theme/theme_aware_colors.dart';
import '../../theme/ui_constants.dart';

/// Network status banner widget that shows connection status and retry options
class NetworkStatusBanner extends StatefulWidget {
  /// Whether the device is currently online
  final bool isOnline;
  
  /// Callback when retry button is tapped
  final VoidCallback? onRetry;
  
  /// Whether to show the banner (defaults to showing when offline)
  final bool? showBanner;
  
  /// Custom message to display
  final String? customMessage;
  
  /// Whether the banner should be dismissible
  final bool isDismissible;
  
  /// Callback when banner is dismissed
  final VoidCallback? onDismiss;

  const NetworkStatusBanner({
    super.key,
    required this.isOnline,
    this.onRetry,
    this.showBanner,
    this.customMessage,
    this.isDismissible = true,
    this.onDismiss,
  });

  @override
  State<NetworkStatusBanner> createState() => _NetworkStatusBannerState();
}

class _NetworkStatusBannerState extends State<NetworkStatusBanner>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _slideAnimation;
  late Animation<double> _fadeAnimation;
  
  bool _isDismissed = false;
  bool _wasOffline = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: UIConstants.mediumAnimation,
      vsync: this,
    );
    
    _slideAnimation = Tween<double>(
      begin: -1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeOutCubic,
    ));
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
    
    _wasOffline = !widget.isOnline;
    if (_shouldShowBanner()) {
      _controller.forward();
    }
  }

  @override
  void didUpdateWidget(NetworkStatusBanner oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    // Handle connection state changes
    if (oldWidget.isOnline != widget.isOnline) {
      if (!widget.isOnline) {
        // Went offline
        _wasOffline = true;
        _isDismissed = false;
        if (_shouldShowBanner()) {
          _controller.forward();
        }
      } else if (_wasOffline && widget.isOnline) {
        // Came back online after being offline
        _showReconnectedMessage();
      }
    }
    
    // Handle show/hide based on showBanner prop
    if (oldWidget.showBanner != widget.showBanner) {
      if (_shouldShowBanner()) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  bool _shouldShowBanner() {
    if (_isDismissed) return false;
    if (widget.showBanner != null) return widget.showBanner!;
    return !widget.isOnline;
  }

  void _showReconnectedMessage() {
    if (!mounted) return;
    
    // Show brief "reconnected" message
    _controller.forward().then((_) {
      Future.delayed(const Duration(seconds: 2), () {
        if (mounted) {
          _controller.reverse();
        }
      });
    });
  }

  void _handleDismiss() {
    setState(() {
      _isDismissed = true;
    });
    _controller.reverse();
    widget.onDismiss?.call();
  }

  void _handleRetry() {
    widget.onRetry?.call();
  }

  @override
  Widget build(BuildContext context) {
    if (!_shouldShowBanner() && widget.isOnline && !_wasOffline) {
      return const SizedBox.shrink();
    }

    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _slideAnimation.value * 100),
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildBanner(context),
          ),
        );
      },
    );
  }

  Widget _buildBanner(BuildContext context) {
    final theme = Theme.of(context);
    final isReconnected = widget.isOnline && _wasOffline;
    
    Color backgroundColor;
    Color textColor;
    Color iconColor;
    IconData icon;
    String message;
    
    if (isReconnected) {
      backgroundColor = ThemeAwareColors.getSuccessColor(context);
      textColor = Colors.white;
      iconColor = Colors.white;
      icon = Icons.wifi;
      message = widget.customMessage ?? 'Connection restored';
    } else {
      backgroundColor = ThemeAwareColors.getWarningColor(context);
      textColor = theme.brightness == Brightness.dark ? Colors.black : Colors.white;
      iconColor = textColor;
      icon = Icons.wifi_off;
      message = widget.customMessage ?? 'No internet connection';
    }
    
    return Container(
      width: double.infinity,
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.5.h),
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: backgroundColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Row(
          children: [
            Icon(
              icon,
              color: iconColor,
              size: 5.w,
              semanticLabel: isReconnected ? 'Connected' : 'Disconnected',
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    message,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: textColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  if (!widget.isOnline && !isReconnected) ...[
                    SizedBox(height: 0.5.h),
                    Text(
                      'Some features may not work properly',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: textColor.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ],
              ),
            ),
            if (!widget.isOnline && widget.onRetry != null) ...[
              SizedBox(width: 2.w),
              _buildRetryButton(context, textColor),
            ],
            if (widget.isDismissible) ...[
              SizedBox(width: 2.w),
              _buildDismissButton(context, iconColor),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildRetryButton(BuildContext context, Color textColor) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _handleRetry,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
          decoration: BoxDecoration(
            border: Border.all(color: textColor.withValues(alpha: 0.5)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            'Retry',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: textColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDismissButton(BuildContext context, Color iconColor) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _handleDismiss,
        borderRadius: BorderRadius.circular(20),
        child: Padding(
          padding: EdgeInsets.all(1.w),
          child: Icon(
            Icons.close,
            color: iconColor,
            size: 4.w,
            semanticLabel: 'Dismiss',
          ),
        ),
      ),
    );
  }
}

/// Convenient factory constructors for common NetworkStatusBanner configurations
extension NetworkStatusBannerVariants on NetworkStatusBanner {
  /// Simple offline banner
  static Widget offline({
    required VoidCallback onRetry,
    VoidCallback? onDismiss,
  }) {
    return NetworkStatusBanner(
      isOnline: false,
      onRetry: onRetry,
      onDismiss: onDismiss,
    );
  }

  /// Custom message banner
  static Widget custom({
    required bool isOnline,
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    bool isDismissible = true,
  }) {
    return NetworkStatusBanner(
      isOnline: isOnline,
      customMessage: message,
      onRetry: onRetry,
      onDismiss: onDismiss,
      isDismissible: isDismissible,
      showBanner: true,
    );
  }

  /// Success message banner
  static Widget success({
    required String message,
    VoidCallback? onDismiss,
    Duration? duration,
  }) {
    return _TimedBanner(
      message: message,
      isSuccess: true,
      onDismiss: onDismiss,
      duration: duration ?? const Duration(seconds: 3),
    );
  }

  /// Error message banner
  static Widget error({
    required String message,
    VoidCallback? onRetry,
    VoidCallback? onDismiss,
    Duration? duration,
  }) {
    return _TimedBanner(
      message: message,
      isSuccess: false,
      onRetry: onRetry,
      onDismiss: onDismiss,
      duration: duration,
    );
  }
}

/// Timed banner that auto-dismisses after a duration
class _TimedBanner extends StatefulWidget {
  final String message;
  final bool isSuccess;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final Duration? duration;

  const _TimedBanner({
    required this.message,
    required this.isSuccess,
    this.onRetry,
    this.onDismiss,
    this.duration,
  });

  @override
  State<_TimedBanner> createState() => _TimedBannerState();
}

class _TimedBannerState extends State<_TimedBanner> {
  bool _isVisible = true;

  @override
  void initState() {
    super.initState();
    if (widget.duration != null) {
      Future.delayed(widget.duration!, () {
        if (mounted) {
          setState(() {
            _isVisible = false;
          });
          widget.onDismiss?.call();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();
    
    return NetworkStatusBanner(
      isOnline: widget.isSuccess,
      customMessage: widget.message,
      onRetry: widget.onRetry,
      onDismiss: () {
        setState(() {
          _isVisible = false;
        });
        widget.onDismiss?.call();
      },
      showBanner: true,
    );
  }
}