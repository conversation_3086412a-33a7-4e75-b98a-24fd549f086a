import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';

class AIInsightsCardWidget extends StatelessWidget {
  final List<Map<String, dynamic>> insights;
  final Map<String, dynamic>? overallSummary;
  final VoidCallback? onAIChatTap;
  final VoidCallback? onViewAllTap;
  final VoidCallback? onRefresh;
  final String? lastUpdated;
  final bool isLoading;
  final bool wasSkippedDueToInsufficientData;

  const AIInsightsCardWidget({
    super.key,
    required this.insights,
    this.overallSummary,
    this.onAIChatTap,
    this.onViewAllTap,
    this.onRefresh,
    this.lastUpdated,
    this.isLoading = false,
    this.wasSkippedDueToInsufficientData = false,
  });

  @override
  Widget build(BuildContext context) {
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section Header - matching Today's Summary style
        Padding(
          padding: EdgeInsets.only(bottom: 1.h),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'AI Insights',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.w700,
                            color: ThemeAwareColors.getPrimaryTextColor(context),
                          ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    Text(
                      _getInsightsSubtitle(),
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: ThemeAwareColors.getSecondaryTextColor(context),
                          ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              if (onRefresh != null && !isLoading)
                IconButton(
                  onPressed: onRefresh,
                  icon: Icon(
                    Icons.refresh,
                    size: 5.w,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  padding: EdgeInsets.all(1.w),
                  constraints: BoxConstraints(
                    minWidth: 8.w,
                    minHeight: 8.w,
                  ),
                ),
            ],
          ),
        ),

        // Main Content Container - matching Today's Summary style
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(3.w),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: ThemeAwareColors.getDividerColor(context),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: ThemeAwareColors.getShadowColor(context),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (isLoading)
                _buildLoadingContent(context)
              else if (insights.isEmpty)
                _buildEmptyContent(context)
              else
                _buildInsightsContent(context),
              
              // Action buttons at the bottom
              SizedBox(height: 2.h),
              _buildActionButtons(context),
            ],
          ),
        ),
      ],
    );
  }

  String _getInsightsSubtitle() {
    if (isLoading) {
      return 'Analyzing patterns...';
    }
    
    // Show timestamp if available
    if (lastUpdated != null && lastUpdated!.isNotEmpty) {
      return 'Last updated $lastUpdated';
    }
    
    // Use overall summary if available
    if (overallSummary != null) {
      final totalInsights = overallSummary!['totalInsights'] as int? ?? 0;
      final hasData = overallSummary!['hasData'] as bool? ?? false;
      
      if (!hasData || totalInsights == 0) {
        return 'No insights yet • Log activities to get started';
      }
      return '$totalInsights insights generated';
    }
    
    // Fallback to old logic
    if (insights.isEmpty) {
      return 'No insights yet • Log activities to get started';
    }
    return '${insights.length} insights generated';
  }

  String _formatTimeDifference(Duration difference) {
    if (difference.inDays > 0) {
      final days = difference.inDays;
      final hours = difference.inHours % 24;
      if (hours > 0) {
        return '$days day${days > 1 ? 's' : ''} $hours hour${hours > 1 ? 's' : ''}';
      }
      return '$days day${days > 1 ? 's' : ''}';
    } else if (difference.inHours > 0) {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      if (minutes > 0) {
        return '$hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''}';
      }
      return '$hours hour${hours > 1 ? 's' : ''}';
    } else {
      final minutes = difference.inMinutes;
      return '$minutes minute${minutes > 1 ? 's' : ''}';
    }
  }

  Widget _buildLoadingContent(BuildContext context) {
    return Center(
      child: Column(
        children: [
          SizedBox(
            width: 8.w,
            height: 8.w,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(
                Theme.of(context).colorScheme.primary,
              ),
            ),
          ),
          SizedBox(height: 2.h),
          Text(
            'AI is analyzing your baby\'s patterns...',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  fontSize: 14.sp,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyContent(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Icon(
            wasSkippedDueToInsufficientData ? Icons.pending_outlined : Icons.psychology_outlined,
            size: 12.w,
            color: ThemeAwareColors.getDividerColor(context),
          ),
          SizedBox(height: 2.h),
          Text(
            wasSkippedDueToInsufficientData ? 'Need More Activity Data' : 'AI Insights Ready',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
          ),
          SizedBox(height: 1.h),
          Text(
            wasSkippedDueToInsufficientData 
                ? 'Log at least 5 activities to unlock AI-powered insights and personalized recommendations for your baby.'
                : 'Start logging feeding, sleep, and diaper activities to unlock personalized insights about your baby\'s patterns.',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsContent(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildInsightsSummary(context),
        // Individual insight previews removed per user request
        // Keep only the summary section showing overall analysis
      ],
    );
  }

  Widget _buildInsightsSummary(BuildContext context) {
    final summaryText = _generateInsightsSummary();
    final detailedText = _generateDetailedDescription();
    
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.auto_awesome,
                size: 5.w,
                color: Theme.of(context).colorScheme.primary,
              ),
              SizedBox(width: 3.w),
              Expanded(
                child: Text(
                  summaryText,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 13.sp,
                        fontWeight: FontWeight.w500,
                      ),
                ),
              ),
            ],
          ),
          if (detailedText.isNotEmpty) ...[
            SizedBox(height: 1.h),
            Text(
              detailedText,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                    fontSize: 11.sp,
                    fontWeight: FontWeight.w400,
                  ),
            ),
          ],
        ],
      ),
    );
  }

  String _generateInsightsSummary() {
    // Use overall summary if available
    if (overallSummary != null) {
      final mainSummary = overallSummary!['mainSummary'] as String? ?? '';
      if (mainSummary.isNotEmpty) {
        return mainSummary;
      }
    }
    
    // Fallback to old logic if no overall summary
    if (insights.isEmpty) {
      return 'No insights available yet. Log more activities to get personalized recommendations.';
    }
    
    // Count insights by type
    final Map<String, int> insightCounts = {};
    final List<String> recommendations = [];
    
    for (final insight in insights) {
      final type = insight['type']?.toString() ?? 'unknown';
      insightCounts[type] = (insightCounts[type] ?? 0) + 1;
      
      final data = insight['data'] as Map<String, dynamic>? ?? {};
      final recs = data['recommendations'] as List? ?? [];
      recommendations.addAll(recs.cast<String>());
    }
    
    // Build summary text
    final parts = <String>[];
    
    if (insightCounts.containsKey('sleep')) {
      parts.add('Sleep patterns analyzed');
    }
    if (insightCounts.containsKey('feeding')) {
      parts.add('Feeding schedule reviewed');
    }
    if (insightCounts.containsKey('growth')) {
      parts.add('Growth trends tracked');
    }
    
    if (parts.isEmpty) {
      return 'AI is ready to analyze your baby\'s patterns. Continue logging activities for personalized insights.';
    }
    
    final recCount = recommendations.where((r) => r.isNotEmpty && !r.toLowerCase().contains('not enough')).length;
    
    String summary = parts.join(', ');
    if (recCount > 0) {
      summary += '. $recCount personalized recommendations available.';
    } else {
      summary += '. Continue logging for more detailed insights.';
    }
    
    return summary;
  }
  
  String _generateDetailedDescription() {
    // Use overall summary if available
    if (overallSummary != null) {
      final detailedDescription = overallSummary!['detailedDescription'] as String? ?? '';
      if (detailedDescription.isNotEmpty) {
        return detailedDescription;
      }
    }
    
    // Return empty string if no detailed description available
    return '';
  }
  

  Widget _buildTopInsights(BuildContext context) {
    final topInsights = insights.take(2).toList();
    
    return Column(
      children: topInsights.map((insight) {
        return Container(
          margin: EdgeInsets.only(bottom: 1.h),
          child: _buildInsightPreview(context, insight),
        );
      }).toList(),
    );
  }

  Widget _buildInsightPreview(BuildContext context, Map<String, dynamic> insight) {
    final String type = insight['type']?.toString() ?? 'unknown';
    final Color typeColor = _getInsightTypeColor(type, context);
    final String title = insight['title']?.toString() ?? 'AI Insight';
    final String description = insight['description']?.toString() ?? 'No description available';
    
    return Container(
      padding: EdgeInsets.all(2.5.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: typeColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(1.5.w),
            decoration: BoxDecoration(
              color: typeColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              _getInsightTypeIcon(type),
              size: 4.w,
              color: typeColor,
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                        fontSize: 12.sp,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 10.sp,
                      ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(5.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.03),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          // Professional AI Icon
          Container(
            padding: EdgeInsets.all(2.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.psychology_outlined,
              size: 6.w,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
          
          SizedBox(width: 3.w),
          
          // Title and Subtitle
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'AI Insights',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                        fontSize: 18.sp,
                      ),
                ),
                Text(
                  'Smart analysis of your baby\'s patterns',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 12.sp,
                      ),
                ),
              ],
            ),
          ),
          
          // Loading Indicator
          if (isLoading)
            Container(
              padding: EdgeInsets.all(2.w),
              decoration: BoxDecoration(
                color: ThemeAwareColors.getCardColor(context),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: ThemeAwareColors.getDividerColor(context),
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: SizedBox(
                width: 5.w,
                height: 5.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfessionalLoadingState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(5.w),
      child: Column(
        children: [
          // Professional loading animation
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              children: [
                // Animated AI Brain Icon
                TweenAnimationBuilder(
                  tween: Tween<double>(begin: 0, end: 1),
                  duration: Duration(seconds: 2),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: 0.8 + (0.2 * value),
                      child: Icon(
                        Icons.psychology_outlined,
                        size: 15.w,
                        color: Theme.of(context).colorScheme.primary.withValues(
                          alpha: 0.3 + (0.4 * value),
                        ),
                      ),
                    );
                  },
                ),
                
                SizedBox(height: 3.h),
                
                Text(
                  'AI is analyzing your baby\'s patterns...',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: 2.h),
                
                // Professional progress indicator
                Container(
                  height: 4,
                  decoration: BoxDecoration(
                    color: ThemeAwareColors.getDividerColor(context),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: TweenAnimationBuilder(
                    tween: Tween<double>(begin: 0, end: 1),
                    duration: Duration(seconds: 3),
                    builder: (context, value, child) {
                      return LinearProgressIndicator(
                        value: value * 0.8,
                        backgroundColor: ThemeAwareColors.getCardColor(context),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Stack(
            alignment: Alignment.center,
            children: [
              Container(
                width: 12.w,
                height: 12.w,
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getCardColor(context),
                  borderRadius: BorderRadius.circular(10.w),
                  boxShadow: [
                    BoxShadow(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                      blurRadius: 8,
                      offset: Offset(0, 2),
                    ),
                  ],
                ),
                child: CustomIconWidget(
                  iconName: 'psychology',
                  color: Theme.of(context).colorScheme.primary,
                  size: 6.w,
                ),
              ),
              SizedBox(
                width: 15.w,
                height: 15.w,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          Text(
            '🤖 AI Analyzing...',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                  fontWeight: FontWeight.w700,
                ),
          ),
          SizedBox(height: 0.8.h),
          Text(
            'Discovering patterns in sleep, feeding & growth',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(5.w),
      child: Column(
        children: [
          // Professional empty state illustration
          Container(
            padding: EdgeInsets.all(8.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Column(
              children: [
                // Professional AI Icon with subtle animation
                TweenAnimationBuilder(
                  tween: Tween<double>(begin: 0, end: 1),
                  duration: Duration(seconds: 2),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: 0.9 + (0.1 * value),
                      child: Container(
                        padding: EdgeInsets.all(4.w),
                        decoration: BoxDecoration(
                          color: ThemeAwareColors.getCardColor(context),
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              blurRadius: 15,
                              offset: Offset(0, 5),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.psychology_outlined,
                          size: 12.w,
                          color: Theme.of(context).colorScheme.primary.withValues(
                            alpha: 0.7 + (0.3 * value),
                          ),
                        ),
                      ),
                    );
                  },
                ),
                
                SizedBox(height: 3.h),
                
                Text(
                  'AI Insights Ready',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: ThemeAwareColors.getPrimaryTextColor(context),
                        fontWeight: FontWeight.w700,
                        fontSize: 16.sp,
                      ),
                ),
                
                SizedBox(height: 1.h),
                
                Text(
                  'Smart analysis of sleep, feeding, and growth patterns',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: ThemeAwareColors.getSecondaryTextColor(context),
                        fontSize: 13.sp,
                      ),
                  textAlign: TextAlign.center,
                ),
                
                SizedBox(height: 2.h),
                
                // Professional tip container
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(15),
                    border: Border.all(
                      color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        size: 4.w,
                        color: ThemeAwareColors.getWarningColor(context),
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          'Log activities to unlock personalized insights',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: ThemeAwareColors.getWarningColor(context),
                                fontWeight: FontWeight.w500,
                                fontSize: 11.sp,
                              ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfessionalInsightsOverview(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(5.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Insights Summary
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  size: 5.w,
                  color: Theme.of(context).colorScheme.primary,
                ),
                SizedBox(width: 3.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${insights.length} Insights Generated',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: ThemeAwareColors.getPrimaryTextColor(context),
                              fontSize: 14.sp,
                            ),
                      ),
                      Text(
                        'Based on your baby\'s activity patterns',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: ThemeAwareColors.getSecondaryTextColor(context),
                              fontSize: 11.sp,
                            ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          
          SizedBox(height: 2.h),
          
          // Top Insights Preview
          ...insights.take(2).map((insight) {
            return _buildProfessionalInsightPreview(context, insight);
          }).toList(),
          
          // Show more indicator if there are more insights
          if (insights.length > 2)
            Container(
              margin: EdgeInsets.only(top: 1.h),
              padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.more_horiz,
                    size: 4.w,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    '+${insights.length - 2} more insights',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w500,
                          fontSize: 11.sp,
                        ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfessionalInsightPreview(BuildContext context, Map<String, dynamic> insight) {
    final String type = insight['type']?.toString() ?? 'unknown';
    final Color typeColor = _getInsightTypeColor(type, context);
    final String title = insight['title']?.toString() ?? 'AI Insight';
    final String description = insight['description']?.toString() ?? 'No description available';
    final double confidence = (insight['confidence'] as num?)?.toDouble() ?? 0.0;
    
    return Container(
      margin: EdgeInsets.only(bottom: 1.5.h),
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: ThemeAwareColors.getCardColor(context),
        borderRadius: BorderRadius.circular(15),
        boxShadow: [
          BoxShadow(
            color: ThemeAwareColors.getShadowColor(context),
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: typeColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Insight header
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: typeColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _getInsightTypeIcon(type),
                  size: 4.w,
                  color: typeColor,
                ),
              ),
              SizedBox(width: 2.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: ThemeAwareColors.getPrimaryTextColor(context),
                            fontSize: 13.sp,
                          ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    if (confidence > 0)
                      Text(
                        '${(confidence * 100).toInt()}% confidence',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: ThemeAwareColors.getSecondaryTextColor(context),
                              fontSize: 10.sp,
                            ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          
          SizedBox(height: 1.h),
          
          // Insight description
          Text(
            description,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                  fontSize: 12.sp,
                ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
            Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2), width: 1),
      ),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.all(2.5.w),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getCardColor(context),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.1),
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: CustomIconWidget(
              iconName: 'psychology',
              color: Theme.of(context).colorScheme.primary,
              size: 6.w,
            ),
          ),
          SizedBox(height: 1.5.h),
          Text(
            '🤖 AI Insights Ready',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                  fontWeight: FontWeight.w700,
                ),
          ),
          SizedBox(height: 0.8.h),
          Text(
            'Smart analysis of sleep, feeding, and growth patterns',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 0.5.h),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
            decoration: BoxDecoration(
              color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Log activities to unlock insights',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getWarningColor(context),
                    fontWeight: FontWeight.w500,
                    fontSize: 8.sp,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildInsightsList(BuildContext context) {
    debugPrint('🎨 Building insights list');
    return insights.take(2).map((insight) {
      debugPrint('📊 Building insight item: $insight');
      return _buildInsightItem(context, insight);
    }).toList();
  }

  Widget _buildInsightItem(BuildContext context, Map<String, dynamic> insight) {
    try {
      debugPrint('🎨 Building insight item with data: $insight');
      
      final String type = insight['type']?.toString() ?? 'unknown';
      final Color typeColor = _getInsightTypeColor(type, context);
      final String title = insight['title']?.toString() ?? 'AI Insight';
      final String description = insight['description']?.toString() ?? 'No description available';
      final double confidence = (insight['confidence'] as num?)?.toDouble() ?? 0.0;
      final Map<String, dynamic> data = insight['data'] as Map<String, dynamic>? ?? {};
      final List<String> insightPoints = _getInsightPoints(insight);
      final List<dynamic> chartData = data['chartData'] as List<dynamic>? ?? [];

      debugPrint('📊 Processed insight - Type: $type, Title: $title, Confidence: $confidence');

      return Container(
        margin: EdgeInsets.only(bottom: 1.5.h),
        padding: EdgeInsets.all(3.w),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              ThemeAwareColors.getCardColor(context),
              typeColor.withValues(alpha: 0.02),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.08),
              blurRadius: 12,
              offset: Offset(0, 4),
            ),
            BoxShadow(
              color: typeColor.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: Offset(0, 1),
            ),
          ],
          border: Border.all(
            color: typeColor.withValues(alpha: 0.2),
            width: 1.5,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with type and confidence
            Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 2.5.w, vertical: 0.8.h),
                  decoration: BoxDecoration(
                    color: typeColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CustomIconWidget(
                        iconName: _getTypeIcon(type),
                        color: ThemeAwareColors.getCardColor(context),
                        size: 3.w,
                      ),
                      SizedBox(width: 1.w),
                      Text(
                        type.toUpperCase(),
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: ThemeAwareColors.getCardColor(context),
                              fontWeight: FontWeight.w700,
                              fontSize: 8.sp,
                            ),
                      ),
                    ],
                  ),
                ),
                Spacer(),
                _buildScoreIndicator(context, data, typeColor),
              ],
            ),
            
            SizedBox(height: 1.5.h),
            
            // Title and description
            Text(
              title,
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.w700,
                    color: ThemeAwareColors.getPrimaryTextColor(context),
                  ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            
            SizedBox(height: 0.5.h),
            
            Text(
              data['subtitle']?.toString() ?? description,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: ThemeAwareColors.getSecondaryTextColor(context),
                  ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            SizedBox(height: 1.h),
            
            // Mini chart preview
            if (chartData.isNotEmpty) _buildMiniChart(context, chartData, typeColor, type),
            
            SizedBox(height: 1.h),
            
            // Key insights
            if (insightPoints.isNotEmpty) _buildKeyInsights(context, insightPoints, typeColor),
            
            // Trend indicator
            if (data['trend'] != null) _buildTrendIndicator(context, data['trend'].toString(), typeColor),
          ],
        ),
      );
    } catch (e, stackTrace) {
      debugPrint('❌ Error building insight item: $e');
      debugPrint('Stack trace: $stackTrace');
      return Container(); // Return empty container on error
    }
  }

  List<String> _getInsightPoints(Map<String, dynamic> insight) {
    try {
      final data = insight['data'] as Map<String, dynamic>?;
      if (data == null) return [];

      final List<String> points = [];
      
      // Add recommendations if available
      if (data['recommendations'] is List) {
        points.addAll(List<String>.from(data['recommendations']));
      }
      
      // Add type-specific points
      switch (insight['type']?.toString().toLowerCase()) {
        case 'sleep':
          if (data['pattern'] != null) points.add('Pattern: ${data['pattern']}');
          if (data['quality'] != null) points.add('Quality: ${data['quality']}');
          break;
        case 'feeding':
          if (data['frequency'] != null) points.add('Frequency: ${data['frequency']}');
          if (data['pattern'] != null) points.add('Pattern: ${data['pattern']}');
          break;
        case 'growth':
          if (data['weightTrend'] != null) points.add('Weight: ${data['weightTrend']}');
          if (data['heightTrend'] != null) points.add('Height: ${data['heightTrend']}');
          break;
      }
      
      return points;
    } catch (e) {
      debugPrint('❌ Error getting insight points: $e');
      return [];
    }
  }

  Widget _buildProfessionalActionButtons(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(5.w),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.03),
            Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(20),
          bottomRight: Radius.circular(20),
        ),
      ),
      child: Row(
        children: [
          // Professional Ask AI Button
          Expanded(
            flex: 2,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Theme.of(context).colorScheme.primary,
                    Theme.of(context).colorScheme.primary.withValues(alpha: 0.8),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(15),
                boxShadow: [
                  BoxShadow(
                    color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                    blurRadius: 12,
                    offset: Offset(0, 6),
                  ),
                ],
              ),
              child: ElevatedButton.icon(
                onPressed: onAIChatTap,
                icon: Icon(
                  Icons.chat_bubble_outline,
                  size: 5.w,
                  color: ThemeAwareColors.getCardColor(context),
                ),
                label: Text(
                  'Ask AI',
                  style: Theme.of(context).textTheme.labelLarge?.copyWith(
                        color: ThemeAwareColors.getCardColor(context),
                        fontWeight: FontWeight.w700,
                        fontSize: 14.sp,
                      ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.transparent,
                  shadowColor: Colors.transparent,
                  padding: EdgeInsets.symmetric(vertical: 2.5.h),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
            ),
          ),
          
          SizedBox(width: 3.w),
          
          // Professional View Full Button
          Expanded(
            flex: 3,
            child: Container(
              decoration: BoxDecoration(
                color: ThemeAwareColors.getCardColor(context),
                borderRadius: BorderRadius.circular(15),
                border: Border.all(
                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.08),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
              ),
                child: OutlinedButton(
                onPressed: onViewAllTap,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.dashboard_outlined,
                      size: 4.w,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    SizedBox(width: 1.5.w),
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'View Full',
                            style: Theme.of(context).textTheme.labelLarge?.copyWith(
                                  color: Theme.of(context).colorScheme.primary,
                                  fontWeight: FontWeight.w700,
                                  fontSize: 12.sp,
                                ),
                          ),
                          Text(
                            'Complete analysis',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.7),
                                  fontSize: 9.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                style: OutlinedButton.styleFrom(
                  side: BorderSide.none,
                  padding: EdgeInsets.symmetric(vertical: 2.5.h, horizontal: 2.w),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(15),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton.icon(
            onPressed: onAIChatTap,
            icon: Icon(
              Icons.chat_bubble_outline,
              size: 4.w,
              color: ThemeAwareColors.getCardColor(context),
            ),
            label: Text(
              'Ask AI',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: ThemeAwareColors.getCardColor(context),
                    fontWeight: FontWeight.w600,
                  ),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              padding: EdgeInsets.symmetric(vertical: 1.5.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              elevation: 2,
            ),
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onViewAllTap,
            icon: Icon(
              Icons.dashboard_outlined,
              size: 4.w,
              color: Theme.of(context).colorScheme.primary,
            ),
            label: Text(
              'View All',
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            style: OutlinedButton.styleFrom(
              side: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 1.5,
              ),
              padding: EdgeInsets.symmetric(vertical: 1.5.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Color _getInsightTypeColor(String type, BuildContext context) {
    // Use theme-aware colors that work in both light and dark modes
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    switch (type.toLowerCase()) {
      case 'sleep':
      case 'feeding':
      case 'diaper':
        return isDark ? const Color(0xFF818CF8) : const Color(0xFF6366F1); // Modern indigo
      case 'medicine':
      case 'temperature':
      case 'health':
        return isDark ? const Color(0xFFFBBF24) : const Color(0xFFF59E0B); // Warm amber
      case 'development':
      case 'milestone':
      case 'milestones':
        return isDark ? const Color(0xFF34D399) : const Color(0xFF10B981); // Clean emerald
      case 'growth':
      case 'pumping':
        return isDark ? const Color(0xFFF87171) : const Color(0xFFEF4444); // Modern red
      default:
        return isDark ? const Color(0xFF818CF8) : const Color(0xFF6366F1); // Default indigo
    }
  }

  String _getTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return 'bedtime';
      case 'feeding':
        return 'restaurant';
      case 'growth':
        return 'trending_up';
      case 'milestone':
        return 'child_care';
      default:
        return 'lightbulb';
    }
  }

  IconData _getInsightTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'sleep':
        return Icons.bedtime_outlined;
      case 'feeding':
        return Icons.restaurant_outlined;
      case 'growth':
        return Icons.trending_up_outlined;
      case 'milestone':
        return Icons.child_care_outlined;
      default:
        return Icons.lightbulb_outline;
    }
  }

  Widget _buildScoreIndicator(BuildContext context, Map<String, dynamic> data, Color typeColor) {
    final score = data['score'];
    if (score == null) return SizedBox.shrink();
    
    final scoreValue = score is num ? score.toDouble() : 0.0;
    final scoreText = scoreValue.toStringAsFixed(1);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(
          color: typeColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.star,
            color: typeColor,
            size: 3.w,
          ),
          SizedBox(width: 1.w),
          Text(
            scoreText,
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: typeColor,
                  fontWeight: FontWeight.w700,
                  fontSize: 9.sp,
                ),
          ),
        ],
      ),
    );
  }

  Widget _buildMiniChart(BuildContext context, List<dynamic> chartData, Color typeColor, String type) {
    if (chartData.isEmpty) return SizedBox.shrink();
    
    return Container(
      height: 8.h,
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: typeColor.withValues(alpha: 0.1),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: chartData.take(7).map((dataPoint) {
          final value = _getChartValue(dataPoint, type);
          final maxValue = _getMaxChartValue(chartData, type);
          final normalizedHeight = maxValue > 0 ? (value / maxValue) * 4.h : 0.5.h;
          
          return Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                width: 1.5.w,
                height: normalizedHeight.clamp(0.5.h, 4.h),
                decoration: BoxDecoration(
                  color: typeColor,
                  borderRadius: BorderRadius.circular(1),
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                _getChartLabel(dataPoint),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getSecondaryTextColor(context),
                      fontSize: 7.sp,
                    ),
              ),
            ],
          );
        }).toList(),
      ),
    );
  }

  double _getChartValue(dynamic dataPoint, String type) {
    if (dataPoint is! Map) return 0.0;
    
    switch (type.toLowerCase()) {
      case 'sleep':
        return (dataPoint['hours'] as num?)?.toDouble() ?? 0.0;
      case 'feeding':
        return (dataPoint['feeds'] as num?)?.toDouble() ?? 0.0;
      case 'growth':
        return (dataPoint['value'] as num?)?.toDouble() ?? 0.0;
      default:
        return 0.0;
    }
  }

  double _getMaxChartValue(List<dynamic> chartData, String type) {
    if (chartData.isEmpty) return 1.0;
    
    double max = 0.0;
    for (final dataPoint in chartData) {
      final value = _getChartValue(dataPoint, type);
      if (value > max) max = value;
    }
    return max > 0 ? max : 1.0;
  }

  String _getChartLabel(dynamic dataPoint) {
    if (dataPoint is Map) {
      // Try label first
      if (dataPoint['label'] != null) {
        final label = dataPoint['label'].toString();
        return label.length > 3 ? label.substring(0, 3) : label;
      }
      
      // Try date field
      if (dataPoint['date'] != null) {
        final date = dataPoint['date'].toString();
        return date.length > 3 ? date.substring(0, 3) : date;
      }
      
      // Try day field
      if (dataPoint['day'] != null) {
        final days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
        final dayIndex = dataPoint['day'] as int? ?? 0;
        if (dayIndex >= 0 && dayIndex < days.length) {
          return days[dayIndex];
        }
      }
    }
    return '';
  }

  Widget _buildKeyInsights(BuildContext context, List<String> insightPoints, Color typeColor) {
    if (insightPoints.isEmpty) return SizedBox.shrink();
    
    return Container(
      padding: EdgeInsets.all(2.w),
      decoration: BoxDecoration(
        color: typeColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: typeColor,
                size: 3.5.w,
              ),
              SizedBox(width: 1.w),
              Text(
                'Key Insights',
                style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: typeColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 9.sp,
                    ),
              ),
            ],
          ),
          SizedBox(height: 0.8.h),
          ...insightPoints.take(2).map((point) => Padding(
            padding: EdgeInsets.only(bottom: 0.5.h),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 1.w,
                  height: 1.w,
                  margin: EdgeInsets.only(top: 1.5.w),
                  decoration: BoxDecoration(
                    color: typeColor,
                    shape: BoxShape.circle,
                  ),
                ),
                SizedBox(width: 2.w),
                Expanded(
                  child: Text(
                    point,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: ThemeAwareColors.getSecondaryTextColor(context),
                          fontSize: 9.sp,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          )).toList(),
        ],
      ),
    );
  }

  Widget _buildTrendIndicator(BuildContext context, String trend, Color typeColor) {
    IconData trendIcon;
    Color trendColor;
    
    switch (trend.toLowerCase()) {
      case 'improving':
      case 'increasing':
        trendIcon = Icons.trending_up;
        trendColor = Colors.green;
        break;
      case 'declining':
      case 'decreasing':
        trendIcon = Icons.trending_down;
        trendColor = Colors.red;
        break;
      case 'stable':
      case 'steady':
        trendIcon = Icons.trending_flat;
        trendColor = Colors.orange;
        break;
      default:
        trendIcon = Icons.help_outline;
        trendColor = Colors.grey;
    }
    
    return Container(
      margin: EdgeInsets.only(top: 1.h),
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: trendColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            trendIcon,
            color: trendColor,
            size: 3.w,
          ),
          SizedBox(width: 1.w),
          Text(
            'Trend: ${trend.toUpperCase()}',
            style: Theme.of(context).textTheme.labelSmall?.copyWith(
                  color: trendColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 8.sp,
                ),
          ),
        ],
      ),
    );
  }
}
