import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../theme/theme_aware_colors.dart';
import '../theme/app_theme.dart';
import '../theme/ui_constants.dart';

/// Enhanced theme helper specifically for account profile components
/// 
/// Provides consistent theming, animations, and visual polish for:
/// - ProfileHeaderCard
/// - FamilySharingCard  
/// - AccountManagementCard
/// - UserAvatarWidget
/// - UserProfileAccountSection
class AccountProfileThemeHelper {
  AccountProfileThemeHelper._();

  // ANIMATION CONSTANTS
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // ELEVATION CONSTANTS
  static const double cardElevation = 2.0;
  static const double hoverElevation = 4.0;
  static const double pressedElevation = 1.0;
  
  // BORDER RADIUS CONSTANTS
  static const double cardBorderRadius = 16.0;
  static const double buttonBorderRadius = 12.0;
  static const double badgeBorderRadius = 20.0;
  static const double avatarBorderRadius = 8.0;

  /// Get enhanced card decoration with theme-aware styling
  static BoxDecoration getCardDecoration(
    BuildContext context, {
    bool isHovered = false,
    bool isPressed = false,
    Color? customColor,
    double? customElevation,
    double? customBorderRadius,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    double elevation = customElevation ?? cardElevation;
    if (isPressed) elevation = pressedElevation;
    if (isHovered) elevation = hoverElevation;
    
    return BoxDecoration(
      color: customColor ?? ThemeAwareColors.getCardColor(context),
      borderRadius: BorderRadius.circular(customBorderRadius ?? cardBorderRadius),
      boxShadow: [
        BoxShadow(
          color: ThemeAwareColors.getShadowColor(context),
          blurRadius: elevation * 2,
          offset: Offset(0, elevation),
          spreadRadius: 0,
        ),
      ],
      border: Border.all(
        color: ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.1),
        width: 0.5,
      ),
    );
  }

  /// Get enhanced button decoration with theme-aware styling
  static BoxDecoration getButtonDecoration(
    BuildContext context, {
    bool isPrimary = true,
    bool isHovered = false,
    bool isPressed = false,
    bool isDisabled = false,
    Color? customColor,
  }) {
    final theme = Theme.of(context);
    
    Color backgroundColor;
    Color borderColor;
    
    if (isDisabled) {
      backgroundColor = ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.1);
      borderColor = ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.2);
    } else if (isPrimary) {
      backgroundColor = customColor ?? ThemeAwareColors.getPrimaryColor(context);
      borderColor = backgroundColor;
      
      if (isPressed) {
        backgroundColor = backgroundColor.withValues(alpha: 0.8);
      } else if (isHovered) {
        backgroundColor = backgroundColor.withValues(alpha: 0.9);
      }
    } else {
      backgroundColor = Colors.transparent;
      borderColor = customColor ?? ThemeAwareColors.getPrimaryColor(context);
      
      if (isPressed) {
        backgroundColor = borderColor.withValues(alpha: 0.1);
      } else if (isHovered) {
        backgroundColor = borderColor.withValues(alpha: 0.05);
      }
    }
    
    return BoxDecoration(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(buttonBorderRadius),
      border: Border.all(color: borderColor, width: isPrimary ? 0 : 1.5),
      boxShadow: isPrimary && !isDisabled ? [
        BoxShadow(
          color: backgroundColor.withValues(alpha: 0.3),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ] : null,
    );
  }

  /// Get role-based avatar styling with enhanced theming
  static AvatarThemeConfig getAvatarThemeConfig(
    BuildContext context,
    String role, {
    double size = 80.0,
    bool isHovered = false,
    bool isPressed = false,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    // Role-based colors with enhanced contrast
    Color borderColor;
    Color backgroundColor;
    double borderWidth;
    
    switch (role.toLowerCase()) {
      case 'admin':
      case 'owner':
        borderColor = isDark ? Colors.amber[300]! : Colors.amber[700]!;
        borderWidth = 3.0;
        break;
      case 'parent':
      case 'mother':
      case 'father':
        borderColor = ThemeAwareColors.getPrimaryColor(context);
        borderWidth = 2.5;
        break;
      case 'caregiver':
      case 'babysitter':
        borderColor = isDark ? Colors.green[300]! : Colors.green[700]!;
        borderWidth = 2.0;
        break;
      case 'grandparent':
      case 'grandmother':
      case 'grandfather':
        borderColor = isDark ? Colors.purple[300]! : Colors.purple[700]!;
        borderWidth = 2.0;
        break;
      case 'family':
      case 'relative':
        borderColor = isDark ? Colors.blue[300]! : Colors.blue[700]!;
        borderWidth = 2.0;
        break;
      default:
        borderColor = ThemeAwareColors.getPrimaryColor(context);
        borderWidth = 2.0;
    }
    
    backgroundColor = borderColor.withValues(alpha: 0.1);
    
    // Adjust for interaction states
    if (isPressed) {
      borderColor = borderColor.withValues(alpha: 0.8);
      backgroundColor = backgroundColor.withValues(alpha: 0.2);
    } else if (isHovered) {
      borderColor = borderColor.withValues(alpha: 0.9);
      backgroundColor = backgroundColor.withValues(alpha: 0.15);
    }
    
    return AvatarThemeConfig(
      borderColor: borderColor,
      backgroundColor: backgroundColor,
      borderWidth: borderWidth,
      shadowColor: borderColor.withValues(alpha: 0.3),
      size: size,
    );
  }

  /// Get status badge styling with enhanced theming
  static BadgeThemeConfig getStatusBadgeConfig(
    BuildContext context,
    String status, {
    bool isSmall = false,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    
    Color backgroundColor;
    Color textColor;
    Color borderColor;
    
    switch (status.toLowerCase()) {
      case 'active':
      case 'verified':
      case 'complete':
        backgroundColor = ThemeAwareColors.getSuccessColor(context);
        textColor = Colors.white;
        borderColor = backgroundColor;
        break;
      case 'pending':
      case 'trial':
        backgroundColor = ThemeAwareColors.getWarningColor(context);
        textColor = isDark ? Colors.black : Colors.white;
        borderColor = backgroundColor;
        break;
      case 'inactive':
      case 'expired':
        backgroundColor = ThemeAwareColors.getErrorColor(context);
        textColor = Colors.white;
        borderColor = backgroundColor;
        break;
      case 'premium':
      case 'pro':
        backgroundColor = ThemeAwareColors.getPrimaryColor(context);
        textColor = Colors.white;
        borderColor = backgroundColor;
        break;
      default:
        backgroundColor = ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.1);
        textColor = ThemeAwareColors.getPrimaryTextColor(context);
        borderColor = ThemeAwareColors.getOutlineColor(context);
    }
    
    return BadgeThemeConfig(
      backgroundColor: backgroundColor,
      textColor: textColor,
      borderColor: borderColor,
      fontSize: isSmall ? 10.sp : 12.sp,
      padding: EdgeInsets.symmetric(
        horizontal: isSmall ? 2.w : 3.w,
        vertical: isSmall ? 0.3.h : 0.5.h,
      ),
    );
  }

  /// Get progress indicator styling with enhanced theming
  static ProgressThemeConfig getProgressConfig(
    BuildContext context,
    double percentage, {
    bool isAnimated = true,
  }) {
    Color progressColor;
    Color backgroundColor;
    
    if (percentage >= 100) {
      progressColor = ThemeAwareColors.getSuccessColor(context);
    } else if (percentage >= 80) {
      progressColor = ThemeAwareColors.getPrimaryColor(context);
    } else if (percentage >= 50) {
      progressColor = ThemeAwareColors.getWarningColor(context);
    } else {
      progressColor = ThemeAwareColors.getErrorColor(context);
    }
    
    backgroundColor = ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.2);
    
    return ProgressThemeConfig(
      progressColor: progressColor,
      backgroundColor: backgroundColor,
      height: 8.0,
      borderRadius: 4.0,
      animationDuration: isAnimated ? mediumAnimation : Duration.zero,
    );
  }

  /// Get section spacing based on screen size and layout
  static SectionSpacing getSectionSpacing(BuildContext context, {bool isCompact = false}) {
    final screenHeight = MediaQuery.of(context).size.height;
    
    double cardSpacing;
    double sectionSpacing;
    double elementSpacing;
    
    if (isCompact || screenHeight < 600) {
      cardSpacing = 2.h;
      sectionSpacing = 1.5.h;
      elementSpacing = 1.h;
    } else if (screenHeight < 800) {
      cardSpacing = 2.5.h;
      sectionSpacing = 2.h;
      elementSpacing = 1.5.h;
    } else {
      cardSpacing = 3.h;
      sectionSpacing = 2.5.h;
      elementSpacing = 2.h;
    }
    
    return SectionSpacing(
      cardSpacing: cardSpacing,
      sectionSpacing: sectionSpacing,
      elementSpacing: elementSpacing,
    );
  }

  /// Create animated container with theme-aware styling
  static Widget createAnimatedContainer({
    required Widget child,
    required BuildContext context,
    bool isHovered = false,
    bool isPressed = false,
    VoidCallback? onTap,
    Duration? animationDuration,
    BoxDecoration? decoration,
    EdgeInsets? padding,
    EdgeInsets? margin,
  }) {
    return AnimatedContainer(
      duration: animationDuration ?? shortAnimation,
      curve: Curves.easeInOut,
      decoration: decoration ?? getCardDecoration(
        context,
        isHovered: isHovered,
        isPressed: isPressed,
      ),
      padding: padding,
      margin: margin,
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(cardBorderRadius),
          child: child,
        ),
      ),
    );
  }

  /// Create shimmer loading effect for skeleton screens
  static Widget createShimmerEffect({
    required BuildContext context,
    required double width,
    required double height,
    double borderRadius = 8.0,
  }) {
    final theme = Theme.of(context);
    final baseColor = ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.1);
    final highlightColor = ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.2);
    
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: baseColor,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: TweenAnimationBuilder<double>(
        tween: Tween(begin: 0.0, end: 1.0),
        duration: const Duration(milliseconds: 1500),
        builder: (context, value, child) {
          return Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(borderRadius),
              gradient: LinearGradient(
                begin: Alignment(-1.0 + 2.0 * value, 0.0),
                end: Alignment(1.0 + 2.0 * value, 0.0),
                colors: [
                  baseColor,
                  highlightColor,
                  baseColor,
                ],
                stops: const [0.0, 0.5, 1.0],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Get responsive text styles based on screen size
  static TextStyle getResponsiveTextStyle(
    BuildContext context,
    TextStyle baseStyle, {
    bool isCompact = false,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    double scaleFactor = 1.0;
    
    if (isCompact || screenWidth < 360) {
      scaleFactor = 0.9;
    } else if (screenWidth > 600) {
      scaleFactor = 1.1;
    }
    
    return baseStyle.copyWith(
      fontSize: (baseStyle.fontSize ?? 14) * scaleFactor,
    );
  }
}

/// Configuration class for avatar theming
class AvatarThemeConfig {
  final Color borderColor;
  final Color backgroundColor;
  final double borderWidth;
  final Color shadowColor;
  final double size;

  const AvatarThemeConfig({
    required this.borderColor,
    required this.backgroundColor,
    required this.borderWidth,
    required this.shadowColor,
    required this.size,
  });
}

/// Configuration class for badge theming
class BadgeThemeConfig {
  final Color backgroundColor;
  final Color textColor;
  final Color borderColor;
  final double fontSize;
  final EdgeInsets padding;

  const BadgeThemeConfig({
    required this.backgroundColor,
    required this.textColor,
    required this.borderColor,
    required this.fontSize,
    required this.padding,
  });
}

/// Configuration class for progress indicator theming
class ProgressThemeConfig {
  final Color progressColor;
  final Color backgroundColor;
  final double height;
  final double borderRadius;
  final Duration animationDuration;

  const ProgressThemeConfig({
    required this.progressColor,
    required this.backgroundColor,
    required this.height,
    required this.borderRadius,
    required this.animationDuration,
  });
}

/// Configuration class for section spacing
class SectionSpacing {
  final double cardSpacing;
  final double sectionSpacing;
  final double elementSpacing;

  const SectionSpacing({
    required this.cardSpacing,
    required this.sectionSpacing,
    required this.elementSpacing,
  });
}