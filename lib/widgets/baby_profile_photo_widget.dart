import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'dart:io';

import '../core/app_export.dart';
import '../services/photo_service.dart';

/// Enhanced baby profile photo widget that handles local and remote images
/// with caching and fallback support
class BabyProfilePhotoWidget extends StatefulWidget {
  final String? photoUrl;
  final double size;
  final bool showBorder;
  final Color? borderColor;
  final double borderWidth;
  final String? babyName;
  final String? gender;
  final VoidCallback? onTap;
  final bool isEditable;

  const BabyProfilePhotoWidget({
    super.key,
    this.photoUrl,
    this.size = 60.0,
    this.showBorder = true,
    this.borderColor,
    this.borderWidth = 2.0,
    this.babyName,
    this.gender,
    this.onTap,
    this.isEditable = false,
  });

  @override
  State<BabyProfilePhotoWidget> createState() => _BabyProfilePhotoWidgetState();
}

class _BabyProfilePhotoWidgetState extends State<BabyProfilePhotoWidget> {
  final PhotoService _photoService = PhotoService();
  File? _cachedImage;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadCachedImage();
  }

  @override
  void didUpdateWidget(BabyProfilePhotoWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.photoUrl != widget.photoUrl) {
      _loadCachedImage();
    }
  }

  Future<void> _loadCachedImage() async {
    if (widget.photoUrl != null && widget.photoUrl!.isNotEmpty) {
      try {
        final cachedFile = await _photoService.getCachedImage(widget.photoUrl!);
        if (mounted && cachedFile != null) {
          setState(() {
            _cachedImage = cachedFile;
          });
        }
      } catch (e) {
        debugPrint('⚠️ Error loading cached image: $e');
      }
    }
  }

  Widget _buildFallbackAvatar() {
    final Color backgroundColor = _getGenderColor().withValues(alpha: 0.1);
    final Color iconColor = _getGenderColor();
    
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: backgroundColor,
        border: widget.showBorder
            ? Border.all(
                color: widget.borderColor ?? _getGenderColor(),
                width: widget.borderWidth,
              )
            : null,
      ),
      child: Center(
        child: widget.babyName != null && widget.babyName!.isNotEmpty
            ? Text(
                widget.babyName!.substring(0, 1).toUpperCase(),
                style: TextStyle(
                  color: iconColor,
                  fontSize: widget.size * 0.4,
                  fontWeight: FontWeight.bold,
                ),
              )
            : CustomIconWidget(
                iconName: 'child_care',
                color: iconColor,
                size: widget.size * 0.5,
              ),
      ),
    );
  }

  Widget _buildImageAvatar() {
    Widget imageWidget;
    
    if (_cachedImage != null) {
      // Use cached local image
      imageWidget = Image.file(
        _cachedImage!,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackAvatar();
        },
      );
    } else if (widget.photoUrl!.startsWith('http')) {
      // Use network image with caching
      imageWidget = CustomImageWidget(
        imageUrl: widget.photoUrl!,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        errorWidget: _buildFallbackAvatar(),
      );
    } else {
      // Use local file
      imageWidget = Image.file(
        File(widget.photoUrl!),
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackAvatar();
        },
      );
    }

    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: widget.showBorder
            ? Border.all(
                color: widget.borderColor ?? _getGenderColor(),
                width: widget.borderWidth,
              )
            : null,
      ),
      child: ClipOval(child: imageWidget),
    );
  }

  Color _getGenderColor() {
    if (widget.gender == null) {
      return Theme.of(context).colorScheme.primary;
    }
    
    switch (widget.gender!.toLowerCase()) {
      case 'boy':
        return Colors.blue;
      case 'girl':
        return Colors.pink;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  Widget _buildEditOverlay() {
    if (!widget.isEditable) return const SizedBox.shrink();
    
    return Positioned(
      bottom: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(widget.size * 0.08),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.primary,
          shape: BoxShape.circle,
          border: Border.all(
            color: Theme.of(context).scaffoldBackgroundColor,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: CustomIconWidget(
          iconName: 'camera_alt',
          color: Colors.white,
          size: widget.size * 0.2,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget photoWidget = widget.photoUrl != null && widget.photoUrl!.isNotEmpty
        ? _buildImageAvatar()
        : _buildFallbackAvatar();

    if (_isLoading) {
      photoWidget = Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
          border: widget.showBorder
              ? Border.all(
                  color: widget.borderColor ?? Theme.of(context).colorScheme.primary,
                  width: widget.borderWidth,
                )
              : null,
        ),
        child: Center(
          child: CircularProgressIndicator(
            color: Theme.of(context).colorScheme.primary,
            strokeWidth: 2,
          ),
        ),
      );
    }

    return GestureDetector(
      onTap: widget.onTap,
      child: Stack(
        children: [
          photoWidget,
          _buildEditOverlay(),
        ],
      ),
    );
  }
}

/// Profile photo widget specifically sized for different contexts
class BabyProfilePhotoVariants {
  /// Large photo for profile screens
  static Widget large({
    String? photoUrl,
    String? babyName,
    String? gender,
    VoidCallback? onTap,
    bool isEditable = false,
  }) {
    return BabyProfilePhotoWidget(
      photoUrl: photoUrl,
      size: 30.w,
      showBorder: true,
      borderWidth: 3,
      babyName: babyName,
      gender: gender,
      onTap: onTap,
      isEditable: isEditable,
    );
  }

  /// Medium photo for cards and lists
  static Widget medium({
    String? photoUrl,
    String? babyName,
    String? gender,
    VoidCallback? onTap,
    bool isEditable = false,
  }) {
    return BabyProfilePhotoWidget(
      photoUrl: photoUrl,
      size: 16.w,
      showBorder: true,
      borderWidth: 2,
      babyName: babyName,
      gender: gender,
      onTap: onTap,
      isEditable: isEditable,
    );
  }

  /// Small photo for headers and compact views
  static Widget small({
    String? photoUrl,
    String? babyName,
    String? gender,
    VoidCallback? onTap,
    bool isEditable = false,
  }) {
    return BabyProfilePhotoWidget(
      photoUrl: photoUrl,
      size: 12.w,
      showBorder: true,
      borderWidth: 1.5,
      babyName: babyName,
      gender: gender,
      onTap: onTap,
      isEditable: isEditable,
    );
  }

  /// Extra small photo for minimal contexts
  static Widget extraSmall({
    String? photoUrl,
    String? babyName,
    String? gender,
    VoidCallback? onTap,
  }) {
    return BabyProfilePhotoWidget(
      photoUrl: photoUrl,
      size: 8.w,
      showBorder: true,
      borderWidth: 1,
      babyName: babyName,
      gender: gender,
      onTap: onTap,
      isEditable: false,
    );
  }
}
