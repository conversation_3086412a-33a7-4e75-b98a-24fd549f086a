import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../services/photo_service.dart';
import 'custom_icon_widget.dart';

/// Handles image operations for user avatar widget
class UserAvatarImageHandler {
  final PhotoService _photoService = PhotoService();

  /// Load cached image if available
  Future<File?> loadCachedImage(String? imageUrl) async {
    if (imageUrl == null || imageUrl.isEmpty) return null;
    
    try {
      return await _photoService.getCachedImage(imageUrl);
    } catch (e) {
      debugPrint('⚠️ Error loading cached avatar image: $e');
      return null;
    }
  }

  /// Show image source selection dialog
  Future<ImageSource?> showImageSourceDialog(BuildContext context) async {
    return await showModalBottomSheet<ImageSource>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      builder: (context) => SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.only(top: 8, bottom: 16),
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            ListTile(
              leading: const CustomIconWidget(iconName: 'camera_alt'),
              title: const Text('Take Photo'),
              onTap: () => Navigator.pop(context, ImageSource.camera),
            ),
            ListTile(
              leading: const CustomIconWidget(iconName: 'photo_library'),
              title: const Text('Choose from Gallery'),
              onTap: () => Navigator.pop(context, ImageSource.gallery),
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// Upload image from selected source
  Future<String?> uploadImage(ImageSource source) async {
    try {
      if (source == ImageSource.camera) {
        return await _photoService.takePhotoFromCamera();
      } else {
        return await _photoService.selectPhotoFromGallery();
      }
    } catch (e) {
      debugPrint('⚠️ Error uploading avatar image: $e');
      rethrow;
    }
  }
}