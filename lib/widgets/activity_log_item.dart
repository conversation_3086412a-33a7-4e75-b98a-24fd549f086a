import 'package:flutter/material.dart';
import 'package:babytracker_pro/models/activity_log.dart';
import 'package:sizer/sizer.dart';
import 'package:babytracker_pro/utils/activity_icon_manager.dart';

class ActivityLogItem extends StatelessWidget {
  final ActivityLog activity;

  const ActivityLogItem({super.key, required this.activity});

  @override
  Widget build(BuildContext context) {
    final activityMap = activity.toRecentActivityMap();
    return Container(
      margin: EdgeInsets.only(bottom: 1.h),
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 10.w,
            height: 10.w,
            child: ActivityIconManager.getActivityIconWithBackground(
              activityType: activity.type.name,
              size: 5.w,
              containerPadding: 0.0, // No additional padding since we control the container size
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activityMap['title'] ?? 'Activity',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                ),
                if (activityMap['amount'] != null ||
                    activityMap['feeding_type'] != null ||
                    activityMap['formula_type'] != null ||
                    activityMap['meal_type'] != null ||
                    activityMap['food_items'] != null ||
                    activityMap['diaper_type'] != null ||
                    activityMap['sleep_quality'] != null ||
                    activityMap['sleep_location'] != null ||
                    activityMap['milestone_title'] != null ||
                    activityMap['medication'] != null ||
                    activityMap['dosage'] != null ||
                    activityMap['vaccine'] != null ||
                    activityMap['type_detail'] != null) ...[
                  SizedBox(height: 0.3.h),
                  Text(
                    activityMap['type_detail'] ?? _buildActivityDetails(activityMap),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                        ),
                  ),
                ],
                // Display notes if available
                if (activityMap['notes'] != null && activityMap['notes'].toString().isNotEmpty) ...[
                  SizedBox(height: 0.3.h),
                  Text(
                    'Note: ${activityMap['notes']}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                          fontStyle: FontStyle.italic,
                        ),
                  ),
                ],
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                _formatRelativeTime(activity.timestamp),
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                    ),
              ),
              if (activityMap['duration'] != null && activityMap['duration'] != '0m') ...[
                SizedBox(height: 0.3.h),
                Text(
                  activityMap['duration'],
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                      ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  String _buildActivityDetails(Map<String, dynamic> activity) {
    final List<String> details = [];

    if (activity['amount'] != null) {
      details.add(activity['amount']);
    }
    if (activity['feeding_type'] != null && activity['feeding_type'] != '') {
      details.add(activity['feeding_type']);
    }
    if (activity['formula_type'] != null && activity['formula_type'] != '') {
      details.add(activity['formula_type']);
    }
    if (activity['meal_type'] != null && activity['meal_type'] != '') {
      details.add(activity['meal_type']);
    }
    if (activity['food_items'] != null && activity['food_items'] != '') {
      details.add(activity['food_items']);
    }
    if (activity['diaper_type'] != null && activity['diaper_type'] != '') {
      details.add(activity['diaper_type']);
    }
    if (activity['mood'] != null && activity['mood'] != '') {
      details.add('Mood: ${activity['mood']}');
    }
    if (activity['sleep_quality'] != null && activity['sleep_quality'] != '') {
      details.add('Quality: ${activity['sleep_quality']}');
    }
    if (activity['sleep_location'] != null && activity['sleep_location'] != '') {
      details.add('Location: ${activity['sleep_location']}');
    }
    if (activity['room_temperature'] != null && activity['room_temperature'] != '') {
      details.add('Temp: ${activity['room_temperature']}C');
    }
    // Milestone details
    if (activity['milestone_title'] != null && activity['milestone_title'] != '') {
      details.add(activity['milestone_title']);
    }
    if (activity['milestone_description'] != null && activity['milestone_description'] != '') {
      details.add(activity['milestone_description']);
    }
    if (activity['milestone_category'] != null && activity['milestone_category'] != '') {
      details.add('Category: ${activity['milestone_category']}');
    }
    if (activity['age_in_days'] != null && activity['age_in_days'] != '') {
      final ageInDays = activity['age_in_days'];
      if (ageInDays is int && ageInDays > 0) {
        final ageInMonths = ageInDays ~/ 30;
        final remainingDays = ageInDays % 30;
        if (ageInMonths > 0) {
          details.add('Age: ${ageInMonths}m ${remainingDays}d');
        } else {
          details.add('Age: ${remainingDays}d');
        }
      }
    }
    // Medicine details
    if (activity['medication'] != null && activity['medication'] != '') {
      details.add(activity['medication']);
    }
    if (activity['dosage'] != null && activity['dosage'] != '') {
      details.add(activity['dosage']);
    }
    // Vaccination details
    if (activity['vaccine'] != null && activity['vaccine'] != '') {
      details.add(activity['vaccine']);
    }
    if (activity['batch_number'] != null && activity['batch_number'] != '') {
      details.add('Batch: ${activity['batch_number']}');
    }
    if (activity['provider'] != null && activity['provider'] != '') {
      details.add('Provider: ${activity['provider']}');
    }
    if (activity['location'] != null && activity['location'] != '') {
      details.add('Location: ${activity['location']}');
    }

    return details.join(', ');
  }

  String _formatRelativeTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    final absDiffMinutes = difference.inMinutes.abs();
    final absDiffHours = difference.inHours.abs();
    final absDiffDays = difference.inDays.abs();

    if (absDiffMinutes < 1) {
      return 'Just now';
    } else if (absDiffMinutes < 60) {
      return '$absDiffMinutes ${absDiffMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (absDiffHours < 24) {
      final remainingMinutes = absDiffMinutes.remainder(60);
      if (remainingMinutes == 0) {
        return '$absDiffHours ${absDiffHours == 1 ? 'hour' : 'hours'} ago';
      }
      return '$absDiffHours ${absDiffHours == 1 ? 'hour' : 'hours'} $remainingMinutes ${remainingMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (absDiffDays < 7) {
      return '$absDiffDays ${absDiffDays == 1 ? 'day' : 'days'} ago';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }
}