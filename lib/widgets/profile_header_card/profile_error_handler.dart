import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../theme/theme_aware_colors.dart';

/// Error states for profile header card
enum ProfileErrorType {
  networkError,
  dataCorrupted,
  permissionDenied,
  unknown,
}

/// Error handler widget for profile header card
class ProfileErrorHandler extends StatelessWidget {
  final ProfileErrorType errorType;
  final String? customMessage;
  final VoidCallback? onRetry;

  const ProfileErrorHandler({
    super.key,
    required this.errorType,
    this.customMessage,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final errorInfo = _getErrorInfo(errorType);
    
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              errorInfo.icon,
              size: 48,
              color: ThemeAwareColors.getErrorColor(context),
            ),
            SizedBox(height: 2.h),
            Text(
              errorInfo.title,
              style: theme.textTheme.titleMedium?.copyWith(
                color: ThemeAwareColors.getPrimaryTextColor(context),
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              customMessage ?? errorInfo.message,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: ThemeAwareColors.getSecondaryTextColor(context),
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null && errorInfo.showRetry) ...[
              SizedBox(height: 2.h),
              TextButton(
                onPressed: onRetry,
                child: Text(errorInfo.retryText),
              ),
            ],
          ],
        ),
      ),
    );
  }

  _ErrorInfo _getErrorInfo(ProfileErrorType type) {
    switch (type) {
      case ProfileErrorType.networkError:
        return _ErrorInfo(
          icon: Icons.wifi_off_outlined,
          title: 'Connection Error',
          message: 'Please check your internet connection and try again',
          showRetry: true,
          retryText: 'Retry',
        );
      case ProfileErrorType.dataCorrupted:
        return _ErrorInfo(
          icon: Icons.error_outline,
          title: 'Data Error',
          message: 'Profile data appears to be corrupted',
          showRetry: false,
          retryText: 'Contact Support',
        );
      case ProfileErrorType.permissionDenied:
        return _ErrorInfo(
          icon: Icons.lock_outline,
          title: 'Access Denied',
          message: 'You don\'t have permission to view this profile',
          showRetry: false,
          retryText: 'Go Back',
        );
      case ProfileErrorType.unknown:
        return _ErrorInfo(
          icon: Icons.help_outline,
          title: 'Something went wrong',
          message: 'An unexpected error occurred',
          showRetry: true,
          retryText: 'Try Again',
        );
    }
  }
}

class _ErrorInfo {
  final IconData icon;
  final String title;
  final String message;
  final bool showRetry;
  final String retryText;

  const _ErrorInfo({
    required this.icon,
    required this.title,
    required this.message,
    required this.showRetry,
    required this.retryText,
  });
}