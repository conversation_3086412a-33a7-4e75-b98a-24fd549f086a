import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../theme/theme_aware_colors.dart';
import '../../utils/user_avatar_theme_helper.dart';
import '../../utils/profile_display_formatter.dart';
import '../user_avatar_widget.dart';

/// Profile information section with avatar and basic user details
class ProfileInfoSection extends StatelessWidget {
  final UserProfile userProfile;
  final double avatarSize;
  final bool isEditable;
  final VoidCallback? onAvatarTap;

  const ProfileInfoSection({
    super.key,
    required this.userProfile,
    required this.avatarSize,
    required this.isEditable,
    this.onAvatarTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final initials = ProfileDisplayFormatter.getInitials(userProfile.fullName);
    
    return Row(
      children: [
        UserAvatarWidget(
          imageUrl: userProfile.avatarUrl,
          initials: initials,
          role: userProfile.role,
          size: avatarSize,
          isEditable: isEditable,
          onTap: onAvatarTap,
          showBorder: true,
        ),
        SizedBox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                userProfile.fullName,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ThemeAwareColors.getPrimaryTextColor(context),
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 0.5.h),
              Text(
                userProfile.email,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: ThemeAwareColors.getSecondaryTextColor(context),
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 1.h),
              _RoleBadge(role: userProfile.role),
            ],
          ),
        ),
      ],
    );
  }
}

/// Role badge widget with consistent styling
class _RoleBadge extends StatelessWidget {
  final String role;

  const _RoleBadge({required this.role});

  @override
  Widget build(BuildContext context) {
    final roleConfig = UserAvatarThemeHelper.getRoleConfig(context, role);
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: roleConfig.borderColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: roleConfig.borderColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Text(
        ProfileDisplayFormatter.formatRoleText(role),
        style: theme.textTheme.labelMedium?.copyWith(
          color: roleConfig.borderColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}