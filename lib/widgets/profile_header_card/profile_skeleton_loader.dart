import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../theme/theme_aware_colors.dart';

/// Skeleton loader for profile header card
class ProfileSkeletonLoader extends StatelessWidget {
  const ProfileSkeletonLoader({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAvatarSection(context),
            SizedBox(height: 3.h),
            _buildProgressSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAvatarSection(BuildContext context) {
    return Row(
      children: [
        _SkeletonBox(width: 80, height: 80, isCircle: true),
        Sized<PERSON>ox(width: 4.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _SkeletonBox(height: 20, width: 60.w),
              <PERSON><PERSON><PERSON><PERSON>(height: 1.h),
              _SkeletonBox(height: 16, width: 40.w),
              SizedBox(height: 1.h),
              _SkeletonBox(height: 24, width: 20.w, borderRadius: 12),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildProgressSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _SkeletonBox(height: 16, width: 30.w),
        SizedBox(height: 1.h),
        _SkeletonBox(height: 8, width: double.infinity),
      ],
    );
  }
}

/// Reusable skeleton box widget
class _SkeletonBox extends StatelessWidget {
  final double? width;
  final double height;
  final double borderRadius;
  final bool isCircle;

  const _SkeletonBox({
    this.width,
    required this.height,
    this.borderRadius = 4,
    this.isCircle = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: ThemeAwareColors.getOutlineColor(context).withValues(alpha: 0.2),
        borderRadius: isCircle ? null : BorderRadius.circular(borderRadius),
        shape: isCircle ? BoxShape.circle : BoxShape.rectangle,
      ),
    );
  }
}