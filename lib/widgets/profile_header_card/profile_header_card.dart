import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../models/profile_completion_status.dart';
import '../user_avatar_widget.dart';
import 'profile_info_section.dart';
import 'profile_completion_section.dart';
import 'profile_account_info_section.dart';
import 'profile_skeleton_loader.dart';

/// Professional profile header card widget with clear separation of concerns
class ProfileHeaderCard extends StatelessWidget {
  final UserProfile? userProfile;
  final ProfileCompletionStatus? completionStatus;
  final VoidCallback? onEditProfile;
  final VoidCallback? onAvatarTap;
  final bool isLoading;
  final bool isEditable;
  final double? avatarSize;
  final bool showCompletionProgress;
  final bool showAccountDates;

  const ProfileHeaderCard({
    super.key,
    this.userProfile,
    this.completionStatus,
    this.onEditProfile,
    this.onAvatarTap,
    this.isLoading = false,
    this.isEditable = true,
    this.avatarSize,
    this.showCompletionProgress = true,
    this.showAccountDates = true,
  });

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return ProfileSkeletonLoader();
    }

    if (userProfile == null) {
      return _buildErrorCard(context);
    }

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ProfileInfoSection(
              userProfile: userProfile!,
              avatarSize: avatarSize ?? 80.0,
              isEditable: isEditable,
              onAvatarTap: onAvatarTap,
            ),
            if (showCompletionProgress && completionStatus != null) ...[
              SizedBox(height: 3.h),
              ProfileCompletionSection(completionStatus: completionStatus!),
            ],
            if (showAccountDates) ...[
              SizedBox(height: 3.h),
              ProfileAccountInfoSection(userProfile: userProfile!),
            ],
            if (isEditable) ...[
              SizedBox(height: 3.h),
              _buildEditButton(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildErrorCard(BuildContext context) {
    // Error card implementation
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
            SizedBox(height: 2.h),
            Text('Unable to load profile', style: Theme.of(context).textTheme.titleMedium),
            SizedBox(height: 1.h),
            Text('Please check your connection and try again', 
                 style: Theme.of(context).textTheme.bodyMedium,
                 textAlign: TextAlign.center),
            if (onEditProfile != null) ...[
              SizedBox(height: 2.h),
              TextButton(onPressed: onEditProfile, child: const Text('Retry')),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEditButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: OutlinedButton.icon(
        onPressed: onEditProfile,
        icon: const Icon(Icons.edit_outlined, size: 18),
        label: const Text('Edit Profile'),
        style: OutlinedButton.styleFrom(
          padding: EdgeInsets.symmetric(vertical: 1.5.h),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        ),
      ),
    );
  }
}