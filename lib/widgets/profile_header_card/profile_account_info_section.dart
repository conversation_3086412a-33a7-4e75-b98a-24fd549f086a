import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../theme/theme_aware_colors.dart';
import '../../utils/profile_display_formatter.dart';

/// Account information section with creation date and activity stats
class ProfileAccountInfoSection extends StatelessWidget {
  final UserProfile userProfile;

  const ProfileAccountInfoSection({
    super.key,
    required this.userProfile,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Information',
          style: theme.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w500,
            color: ThemeAwareColors.getPrimaryTextColor(context),
          ),
        ),
        SizedBox(height: 1.h),
        _InfoRow(
          icon: Icons.calendar_today_outlined,
          label: 'Member since',
          value: ProfileDisplayFormatter.formatDate(userProfile.createdAt),
        ),
        SizedBox(height: 0.5.h),
        if (userProfile.lastSignInAt != null)
          _InfoRow(
            icon: Icons.access_time_outlined,
            label: 'Last active',
            value: ProfileDisplayFormatter.formatLastActivity(userProfile.lastSignInAt!),
          ),
        SizedBox(height: 0.5.h),
        _InfoRow(
          icon: Icons.login_outlined,
          label: 'Total logins',
          value: '${userProfile.signInCount}',
        ),
      ],
    );
  }
}

/// Reusable info row widget
class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;

  const _InfoRow({
    required this.icon,
    required this.label,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: ThemeAwareColors.getSecondaryTextColor(context),
        ),
        SizedBox(width: 2.w),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: ThemeAwareColors.getSecondaryTextColor(context),
          ),
        ),
        SizedBox(width: 2.w),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: ThemeAwareColors.getPrimaryTextColor(context),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }
}