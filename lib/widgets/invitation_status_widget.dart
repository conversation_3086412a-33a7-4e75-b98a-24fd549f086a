import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/family_member.dart';
import 'custom_elevated_button.dart';
import 'custom_icon_widget.dart';
import 'user_avatar_widget.dart';

/// Widget for displaying and managing invitation status
/// 
/// Features:
/// - Shows pending invitations with status
/// - Resend invitation functionality
/// - Cancel invitation option
/// - Expiration tracking
class InvitationStatusWidget extends StatelessWidget {
  /// List of pending family member invitations
  final List<FamilyMember> pendingInvitations;
  
  /// Callback when resend invitation is tapped
  final Function(FamilyMember) onResendInvitation;
  
  /// Callback when cancel invitation is tapped
  final Function(FamilyMember) onCancelInvitation;
  
  /// Whether operations are currently loading
  final bool isLoading;
  
  /// ID of the invitation currently being processed
  final String? processingInvitationId;

  const InvitationStatusWidget({
    super.key,
    required this.pendingInvitations,
    required this.onResendInvitation,
    required this.onCancelInvitation,
    this.isLoading = false,
    this.processingInvitationId,
  });

  @override
  Widget build(BuildContext context) {
    if (pendingInvitations.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    
    return Card(
      elevation: 1,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context, theme),
            SizedBox(height: 3.h),
            ...pendingInvitations.map((invitation) => 
              _buildInvitationItem(context, theme, invitation)
            ),
          ],
        ),
      ),
    );
  }

  /// Build section header
  Widget _buildHeader(BuildContext context, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: theme.colorScheme.secondary.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: CustomIconWidget(
            iconName: 'schedule',
            color: theme.colorScheme.secondary,
            size: 6.w,
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Pending Invitations',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              SizedBox(height: 0.5.h),
              Text(
                '${pendingInvitations.length} ${pendingInvitations.length == 1 ? 'invitation' : 'invitations'} waiting for response',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// Build individual invitation item
  Widget _buildInvitationItem(BuildContext context, ThemeData theme, FamilyMember invitation) {
    final isProcessing = processingInvitationId == invitation.id;
    final isExpired = invitation.isInvitationExpired;
    
    return Container(
      margin: EdgeInsets.only(bottom: 3.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: isExpired 
            ? theme.colorScheme.error.withValues(alpha: 0.05)
            : theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isExpired 
              ? theme.colorScheme.error.withValues(alpha: 0.2)
              : theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              UserAvatarWidget(
                imageUrl: invitation.avatarUrl,
                initials: invitation.initials,
                role: invitation.role,
                size: UserAvatarSizes.medium,
              ),
              SizedBox(width: 4.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            invitation.fullName,
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ),
                        _buildStatusBadge(context, theme, invitation),
                      ],
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      invitation.email,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      invitation.roleDisplayName,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: theme.colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 2.h),
          _buildInvitationDetails(context, theme, invitation),
          SizedBox(height: 3.h),
          _buildActionButtons(context, theme, invitation, isProcessing),
        ],
      ),
    );
  }

  /// Build status badge
  Widget _buildStatusBadge(BuildContext context, ThemeData theme, FamilyMember invitation) {
    final isExpired = invitation.isInvitationExpired;
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: isExpired 
            ? theme.colorScheme.error.withValues(alpha: 0.1)
            : theme.colorScheme.secondary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isExpired 
              ? theme.colorScheme.error.withValues(alpha: 0.3)
              : theme.colorScheme.secondary.withValues(alpha: 0.3),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomIconWidget(
            iconName: isExpired ? 'error' : 'schedule',
            color: isExpired ? theme.colorScheme.error : theme.colorScheme.secondary,
            size: 3.w,
          ),
          SizedBox(width: 1.w),
          Text(
            isExpired ? 'Expired' : 'Pending',
            style: theme.textTheme.bodySmall?.copyWith(
              color: isExpired ? theme.colorScheme.error : theme.colorScheme.secondary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Build invitation details
  Widget _buildInvitationDetails(BuildContext context, ThemeData theme, FamilyMember invitation) {
    final sentDate = invitation.invitationSentAt;
    final expiresDate = invitation.invitationExpiresAt;
    final isExpired = invitation.isInvitationExpired;
    
    return Container(
      padding: EdgeInsets.all(3.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.1),
        ),
      ),
      child: Column(
        children: [
          if (sentDate != null)
            _buildDetailRow(
              context,
              theme,
              'Sent',
              _formatDate(sentDate),
              'send',
            ),
          if (sentDate != null && expiresDate != null)
            SizedBox(height: 1.h),
          if (expiresDate != null)
            _buildDetailRow(
              context,
              theme,
              isExpired ? 'Expired' : 'Expires',
              _formatDate(expiresDate),
              isExpired ? 'error' : 'schedule',
              isError: isExpired,
            ),
        ],
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(
    BuildContext context,
    ThemeData theme,
    String label,
    String value,
    String iconName, {
    bool isError = false,
  }) {
    return Row(
      children: [
        CustomIconWidget(
          iconName: iconName,
          color: isError 
              ? theme.colorScheme.error
              : theme.colorScheme.onSurface.withValues(alpha: 0.6),
          size: 4.w,
        ),
        SizedBox(width: 2.w),
        Text(
          '$label: ',
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            fontWeight: FontWeight.w500,
          ),
        ),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: isError 
                  ? theme.colorScheme.error
                  : theme.colorScheme.onSurface.withValues(alpha: 0.8),
            ),
          ),
        ),
      ],
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(
    BuildContext context,
    ThemeData theme,
    FamilyMember invitation,
    bool isProcessing,
  ) {
    return Row(
      children: [
        Expanded(
          child: CustomElevatedButton(
            text: isProcessing ? 'Cancelling...' : 'Cancel',
            onPressed: isLoading || isProcessing 
                ? null 
                : () => onCancelInvitation(invitation),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.surface,
              foregroundColor: theme.colorScheme.error,
              padding: EdgeInsets.symmetric(vertical: 1.5.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
                side: BorderSide(
                  color: theme.colorScheme.error.withValues(alpha: 0.3),
                ),
              ),
            ),
            icon: isProcessing
                ? SizedBox(
                    width: 4.w,
                    height: 4.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.error,
                      ),
                    ),
                  )
                : CustomIconWidget(
                    iconName: 'cancel',
                    color: theme.colorScheme.error,
                    size: 4.w,
                  ),
          ),
        ),
        SizedBox(width: 3.w),
        Expanded(
          child: CustomElevatedButton(
            text: isProcessing ? 'Resending...' : 'Resend',
            onPressed: isLoading || isProcessing 
                ? null 
                : () => onResendInvitation(invitation),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: EdgeInsets.symmetric(vertical: 1.5.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            icon: isProcessing
                ? SizedBox(
                    width: 4.w,
                    height: 4.w,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        theme.colorScheme.onPrimary,
                      ),
                    ),
                  )
                : CustomIconWidget(
                    iconName: 'refresh',
                    color: theme.colorScheme.onPrimary,
                    size: 4.w,
                  ),
          ),
        ),
      ],
    );
  }

  /// Format date for display
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}