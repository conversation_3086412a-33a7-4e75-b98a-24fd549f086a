import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../models/family_member.dart';
import '../../models/enums.dart';
import '../user_avatar_widget.dart';
import '../custom_icon_widget.dart';

/// Detailed family member list item widget
class FamilyMemberListItem extends StatelessWidget {
  final FamilyMember member;
  final bool canManageFamily;
  final VoidCallback? onTap;

  const FamilyMemberListItem({
    super.key,
    required this.member,
    required this.canManageFamily,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          UserAvatarWidget.medium(
            imageUrl: member.avatarUrl,
            initials: member.initials,
            role: member.role,
            onTap: onTap,
          ),
          SizedBox(width: 4.w),
          Expanded(
            child: _MemberInfo(member: member, theme: theme),
          ),
          if (canManageFamily)
            CustomIconWidget(
              iconName: 'more_vert',
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
              size: 5.w,
            ),
        ],
      ),
    );
  }
}

/// Member information section
class _MemberInfo extends StatelessWidget {
  final FamilyMember member;
  final ThemeData theme;

  const _MemberInfo({
    required this.member,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                member.fullName,
                style: theme.textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
            ),
            _StatusBadge(member: member, theme: theme),
          ],
        ),
        SizedBox(height: 0.5.h),
        Text(
          member.roleDisplayName,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.primary,
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: 0.5.h),
        Text(
          member.activityStatusText,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }
}

/// Status badge for family members
class _StatusBadge extends StatelessWidget {
  final FamilyMember member;
  final ThemeData theme;

  const _StatusBadge({
    required this.member,
    required this.theme,
  });

  @override
  Widget build(BuildContext context) {
    final (badgeColor, badgeText) = _getStatusInfo();
    
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
      decoration: BoxDecoration(
        color: badgeColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: badgeColor.withValues(alpha: 0.3),
        ),
      ),
      child: Text(
        badgeText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: badgeColor,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  (Color, String) _getStatusInfo() {
    return switch (member.status) {
      FamilyMemberStatus.active => (theme.colorScheme.tertiary, 'Active'),
      FamilyMemberStatus.pending => (theme.colorScheme.secondary, 'Pending'),
      FamilyMemberStatus.inactive => (theme.colorScheme.outline, 'Inactive'),
      FamilyMemberStatus.removed => (theme.colorScheme.error, 'Removed'),
    };
  }
}