import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../models/family_member.dart';
import 'family_member_list_item.dart';

/// Expandable list of family members with smooth animations
class ExpandableMemberList extends StatefulWidget {
  final List<FamilyMember> familyMembers;
  final bool canManageFamily;
  final Function(FamilyMember)? onMemberTap;

  const ExpandableMemberList({
    super.key,
    required this.familyMembers,
    required this.canManageFamily,
    this.onMemberTap,
  });

  @override
  State<ExpandableMemberList> createState() => _ExpandableMemberListState();
}

class _ExpandableMemberListState extends State<ExpandableMemberList>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _expandAnimation;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _expandAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _animationController.forward();
      } else {
        _animationController.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.familyMembers.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      children: [
        _ExpandToggle(
          isExpanded: _isExpanded,
          onToggle: _toggleExpanded,
        ),
        SizeTransition(
          sizeFactor: _expandAnimation,
          child: Column(
            children: [
              SizedBox(height: 2.h),
              ...widget.familyMembers.map(
                (member) => FamilyMemberListItem(
                  member: member,
                  canManageFamily: widget.canManageFamily,
                  onTap: () => widget.onMemberTap?.call(member),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

/// Toggle button for expanding/collapsing the member list
class _ExpandToggle extends StatelessWidget {
  final bool isExpanded;
  final VoidCallback onToggle;

  const _ExpandToggle({
    required this.isExpanded,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onToggle,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 4.w),
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: theme.colorScheme.outline.withValues(alpha: 0.2),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              isExpanded ? 'Show Less' : 'View All Members',
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.primary,
              ),
            ),
            SizedBox(width: 2.w),
            AnimatedRotation(
              turns: isExpanded ? 0.5 : 0,
              duration: const Duration(milliseconds: 300),
              child: Icon(
                Icons.expand_more,
                color: theme.colorScheme.primary,
                size: 5.w,
              ),
            ),
          ],
        ),
      ),
    );
  }
}