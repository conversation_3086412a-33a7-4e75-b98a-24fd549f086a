import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../custom_icon_widget.dart';
import '../../services/error_handling_service.dart';
import '../shared/error_display_widget.dart';
import '../shared/skeleton_loading.dart';

/// Loading state for family sharing card - now uses enhanced skeleton loading
class FamilySharingLoadingState extends StatelessWidget {
  const FamilySharingLoadingState({super.key});

  @override
  Widget build(BuildContext context) {
    return const FamilySharingSkeleton();
  }
}

/// Enhanced error state for family sharing card using new error handling system
class FamilySharingErrorState extends StatelessWidget {
  final AccountProfileError? error;
  final VoidCallback? onRetry;
  final VoidCallback? onDismiss;
  final bool isCompact;

  const FamilySharingErrorState({
    super.key,
    this.error,
    this.onRetry,
    this.onDismiss,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    // Use new error display widget if error is provided
    if (error != null) {
      return ErrorDisplayWidget(
        error: error!,
        onRetry: onRetry,
        onDismiss: onDismiss,
        isCompact: isCompact,
      );
    }

    // Fallback to legacy error display for backward compatibility
    return _buildLegacyErrorState(context);
  }

  /// Build legacy error state for backward compatibility
  Widget _buildLegacyErrorState(BuildContext context) {
    final theme = Theme.of(context);
    
    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CustomIconWidget(
              iconName: 'error_outline',
              color: theme.colorScheme.error,
              size: 12.w,
            ),
            SizedBox(height: 2.h),
            Text(
              'Unable to load family information',
              style: theme.textTheme.titleMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 1.h),
            Text(
              'Please check your connection and try again',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              SizedBox(height: 3.h),
              TextButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Try Again'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// Empty state for when no family members exist
class FamilySharingEmptyState extends StatelessWidget {
  final VoidCallback? onInviteMember;

  const FamilySharingEmptyState({
    super.key,
    this.onInviteMember,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Container(
      padding: EdgeInsets.all(6.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        children: [
          CustomIconWidget(
            iconName: 'group_add',
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            size: 12.w,
          ),
          SizedBox(height: 2.h),
          Text(
            'No Family Members Yet',
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
          SizedBox(height: 1.h),
          Text(
            'Invite family members to share baby care responsibilities',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
            ),
            textAlign: TextAlign.center,
          ),
          if (onInviteMember != null) ...[
            SizedBox(height: 3.h),
            TextButton.icon(
              onPressed: onInviteMember,
              icon: const Icon(Icons.person_add),
              label: const Text('Invite First Member'),
            ),
          ],
        ],
      ),
    );
  }
}