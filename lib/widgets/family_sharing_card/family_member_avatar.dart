import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../models/family_member.dart';
import '../user_avatar_widget.dart';
import '../custom_icon_widget.dart';

/// Widget displaying a family member avatar with status indicators
class FamilyMemberAvatar extends StatelessWidget {
  final FamilyMember member;
  final VoidCallback? onTap;

  const FamilyMemberAvatar({
    super.key,
    required this.member,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Stack(
            children: [
              UserAvatarWidget.medium(
                imageUrl: member.avatarUrl,
                initials: member.initials,
                role: member.role,
                onTap: onTap,
              ),
              if (member.isPending)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: _PendingIndicator(theme: theme),
                ),
            ],
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 1.h),
          SizedBox(
            width: 15.w,
            child: Text(
              member.fullName.split(' ').first,
              style: theme.textTheme.bodySmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}

/// Pending status indicator for family member avatars
class _PendingIndicator extends StatelessWidget {
  final ThemeData theme;

  const _PendingIndicator({required this.theme});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(1.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.secondary,
        shape: BoxShape.circle,
        border: Border.all(
          color: theme.scaffoldBackgroundColor,
          width: 2,
        ),
      ),
      child: CustomIconWidget(
        iconName: 'schedule',
        color: theme.colorScheme.onSecondary,
        size: 3.w,
      ),
    );
  }
}