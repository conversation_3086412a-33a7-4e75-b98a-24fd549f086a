import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../models/family_member.dart';
import '../custom_icon_widget.dart';

/// Widget displaying family statistics with visual indicators
class FamilyStatsWidget extends StatelessWidget {
  final List<FamilyMember> familyMembers;

  const FamilyStatsWidget({
    super.key,
    required this.familyMembers,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final totalMembers = familyMembers.length;
    final activeMembers = familyMembers.where((member) => member.isActive).length;
    final pendingMembers = familyMembers.where((member) => member.isPending).length;
    
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: theme.colorScheme.outline.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: _StatItem(
              label: 'Total',
              value: totalMembers.toString(),
              color: theme.colorScheme.primary,
              iconName: 'groups',
            ),
          ),
          _buildDivider(theme),
          Expanded(
            child: _StatItem(
              label: 'Active',
              value: activeMembers.toString(),
              color: theme.colorScheme.tertiary,
              iconName: 'person_check',
            ),
          ),
          if (pendingMembers > 0) ...[
            _buildDivider(theme),
            Expanded(
              child: _StatItem(
                label: 'Pending',
                value: pendingMembers.toString(),
                color: theme.colorScheme.secondary,
                iconName: 'schedule',
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDivider(ThemeData theme) {
    return Container(
      width: 1,
      height: 8.w,
      color: theme.colorScheme.outline.withValues(alpha: 0.2),
    );
  }
}

/// Individual stat item widget
class _StatItem extends StatelessWidget {
  final String label;
  final String value;
  final Color color;
  final String iconName;

  const _StatItem({
    required this.label,
    required this.value,
    required this.color,
    required this.iconName,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: CustomIconWidget(
            iconName: iconName,
            color: color,
            size: 5.w,
          ),
        ),
        SizedBox(height: 1.h),
        Text(
          value,
          style: theme.textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }
}