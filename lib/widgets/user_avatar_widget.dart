import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';

import '../core/app_export.dart';
import '../services/photo_service.dart';
import '../utils/user_avatar_theme_helper.dart';
import '../utils/accessibility_helper.dart';
import 'custom_image_widget.dart';
import 'custom_icon_widget.dart';
import 'user_avatar_image_handler.dart';

/// Professional user avatar widget with role-based styling and image upload functionality
/// 
/// Features:
/// - Photo display with initial fallback
/// - Role-based border colors and styling
/// - Image upload functionality with error handling
/// - Loading states and accessibility support
/// - Responsive sizing and touch targets
class UserAvatarWidget extends StatefulWidget {
  /// URL of the user's profile image
  final String? imageUrl;
  
  /// User's initials for fallback display
  final String initials;
  
  /// User's role for styling (parent, admin, caregiver, etc.)
  final String role;
  
  /// Size of the avatar in logical pixels
  final double size;
  
  /// Callback when avatar is tapped
  final VoidCallback? onTap;
  
  /// Whether the avatar can be edited (shows upload overlay)
  final bool isEditable;
  
  /// Whether to show a border around the avatar
  final bool showBorder;
  
  /// Custom border width (defaults to role-based width)
  final double? borderWidth;
  
  /// Whether the avatar is currently loading
  final bool isLoading;
  
  /// Callback when image upload is initiated
  final Future<String?> Function()? onImageUpload;
  
  /// Callback when image upload completes successfully
  final Function(String imageUrl)? onImageUploaded;
  
  /// Callback when image upload fails
  final Function(String error)? onImageUploadError;

  const UserAvatarWidget({
    super.key,
    this.imageUrl,
    required this.initials,
    required this.role,
    this.size = 80.0,
    this.onTap,
    this.isEditable = false,
    this.showBorder = true,
    this.borderWidth,
    this.isLoading = false,
    this.onImageUpload,
    this.onImageUploaded,
    this.onImageUploadError,
  });

  /// Medium sized avatar (48px) - commonly used in lists
  const UserAvatarWidget.medium({
    super.key,
    this.imageUrl,
    required this.initials,
    required this.role,
    this.onTap,
    this.isEditable = false,
    this.showBorder = true,
    this.borderWidth,
    this.isLoading = false,
    this.onImageUpload,
    this.onImageUploaded,
    this.onImageUploadError,
  }) : size = 48.0;

  /// Small sized avatar (32px) - commonly used in compact layouts
  const UserAvatarWidget.small({
    super.key,
    this.imageUrl,
    required this.initials,
    required this.role,
    this.onTap,
    this.isEditable = false,
    this.showBorder = true,
    this.borderWidth,
    this.isLoading = false,
    this.onImageUpload,
    this.onImageUploaded,
    this.onImageUploadError,
  }) : size = 32.0;

  /// Large sized avatar (96px) - commonly used in profile headers
  const UserAvatarWidget.large({
    super.key,
    this.imageUrl,
    required this.initials,
    required this.role,
    this.onTap,
    this.isEditable = false,
    this.showBorder = true,
    this.borderWidth,
    this.isLoading = false,
    this.onImageUpload,
    this.onImageUploaded,
    this.onImageUploadError,
  }) : size = 96.0;

  @override
  State<UserAvatarWidget> createState() => _UserAvatarWidgetState();
}

class _UserAvatarWidgetState extends State<UserAvatarWidget>
    with SingleTickerProviderStateMixin {
  final UserAvatarImageHandler _imageHandler = UserAvatarImageHandler();
  File? _cachedImage;
  bool _isUploading = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _focusNode = AccessibilityHelper.createKeyboardFocusNode(
      debugLabel: 'UserAvatar_${widget.role}',
    );
    _loadCachedImage();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(UserAvatarWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageUrl != widget.imageUrl) {
      _loadCachedImage();
    }
  }

  /// Load cached image if available
  Future<void> _loadCachedImage() async {
    final cachedFile = await _imageHandler.loadCachedImage(widget.imageUrl);
    if (mounted && cachedFile != null) {
      setState(() {
        _cachedImage = cachedFile;
      });
    }
  }

  /// Get role theme configuration
  RoleThemeConfig _getRoleConfig(BuildContext context) {
    return UserAvatarThemeHelper.getRoleConfig(context, widget.role);
  }

  /// Build fallback avatar with initials
  Widget _buildFallbackAvatar(BuildContext context) {
    final roleConfig = _getRoleConfig(context);
    
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: roleConfig.backgroundColor,
        border: widget.showBorder
            ? Border.all(
                color: roleConfig.borderColor,
                width: widget.borderWidth ?? roleConfig.borderWidth,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          widget.initials.toUpperCase(),
          style: TextStyle(
            color: roleConfig.borderColor,
            fontSize: widget.size * 0.35,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),
    );
  }

  /// Build image avatar
  Widget _buildImageAvatar(BuildContext context) {
    final roleConfig = _getRoleConfig(context);
    
    Widget imageWidget;
    
    if (_cachedImage != null) {
      // Use cached local image
      imageWidget = Image.file(
        _cachedImage!,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackAvatar(context);
        },
      );
    } else if (widget.imageUrl!.startsWith('http')) {
      // Use network image with caching
      imageWidget = CustomImageWidget(
        imageUrl: widget.imageUrl!,
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        errorWidget: _buildFallbackAvatar(context),
      );
    } else {
      // Use local file
      imageWidget = Image.file(
        File(widget.imageUrl!),
        width: widget.size,
        height: widget.size,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          return _buildFallbackAvatar(context);
        },
      );
    }

    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        border: widget.showBorder
            ? Border.all(
                color: roleConfig.borderColor,
                width: widget.borderWidth ?? roleConfig.borderWidth,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipOval(child: imageWidget),
    );
  }

  /// Build loading state
  Widget _buildLoadingAvatar(BuildContext context) {
    final roleConfig = _getRoleConfig(context);
    
    return Container(
      width: widget.size,
      height: widget.size,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: roleConfig.borderColor.withValues(alpha: 0.1),
        border: widget.showBorder
            ? Border.all(
                color: roleConfig.borderColor.withValues(alpha: 0.5),
                width: widget.borderWidth ?? roleConfig.borderWidth,
              )
            : null,
      ),
      child: Center(
        child: SizedBox(
          width: widget.size * 0.4,
          height: widget.size * 0.4,
          child: CircularProgressIndicator(
            color: roleConfig.borderColor,
            strokeWidth: 2,
          ),
        ),
      ),
    );
  }

  /// Build edit overlay for editable avatars
  Widget _buildEditOverlay(BuildContext context) {
    if (!widget.isEditable || _isUploading) return const SizedBox.shrink();
    
    final theme = Theme.of(context);
    
    return Positioned(
      bottom: 0,
      right: 0,
      child: Container(
        padding: EdgeInsets.all(widget.size * 0.08),
        decoration: BoxDecoration(
          color: theme.colorScheme.primary,
          shape: BoxShape.circle,
          border: Border.all(
            color: theme.scaffoldBackgroundColor,
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: CustomIconWidget(
          iconName: 'camera_alt',
          color: theme.colorScheme.onPrimary,
          size: widget.size * 0.15,
        ),
      ),
    );
  }

  /// Handle avatar tap
  Future<void> _handleTap() async {
    if (widget.onTap != null) {
      widget.onTap!();
      return;
    }
    
    if (widget.isEditable && !_isUploading) {
      await _handleImageUpload();
    }
  }

  /// Handle image upload
  Future<void> _handleImageUpload() async {
    if (widget.onImageUpload != null) {
      setState(() {
        _isUploading = true;
      });
      
      try {
        final imageUrl = await widget.onImageUpload!();
        if (imageUrl != null && widget.onImageUploaded != null) {
          widget.onImageUploaded!(imageUrl);
        }
      } catch (e) {
        if (widget.onImageUploadError != null) {
          widget.onImageUploadError!(e.toString());
        }
      } finally {
        if (mounted) {
          setState(() {
            _isUploading = false;
          });
        }
      }
    } else {
      // Default image upload behavior
      await _showImageSourceDialog();
    }
  }

  /// Show image source selection dialog and handle upload
  Future<void> _showImageSourceDialog() async {
    final result = await _imageHandler.showImageSourceDialog(context);
    if (result != null) {
      await _uploadImage(result);
    }
  }

  /// Upload image from selected source
  Future<void> _uploadImage(ImageSource source) async {
    setState(() {
      _isUploading = true;
    });

    try {
      final imageUrl = await _imageHandler.uploadImage(source);
      if (imageUrl != null && widget.onImageUploaded != null) {
        widget.onImageUploaded!(imageUrl);
      }
    } catch (e) {
      if (widget.onImageUploadError != null) {
        widget.onImageUploadError!(e.toString());
      }
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  /// Handle keyboard events for accessibility
  KeyEventResult _handleKeyEvent(KeyEvent event) {
    return AccessibilityHelper.handleKeyboardNavigation(
      event: event,
      onActivate: _handleTap,
      onNext: null,
      onPrevious: null,
    );
  }

  @override
  Widget build(BuildContext context) {
    Widget avatarWidget;
    
    if (widget.isLoading || _isUploading) {
      avatarWidget = _buildLoadingAvatar(context);
    } else if (widget.imageUrl != null && widget.imageUrl!.isNotEmpty) {
      avatarWidget = _buildImageAvatar(context);
    } else {
      avatarWidget = _buildFallbackAvatar(context);
    }

    // Generate comprehensive semantic label
    final semanticLabel = AccessibilityHelper.generateAvatarSemanticLabel(
      userName: widget.initials,
      role: AccessibilityHelper.formatRoleForAccessibility(widget.role),
      hasImage: widget.imageUrl != null && widget.imageUrl!.isNotEmpty,
      isEditable: widget.isEditable,
    );

    final semanticHint = widget.isEditable 
        ? AccessibilityHelper.generateInteractionHint(
            action: 'change profile picture',
            requiresDoubleTab: false,
          )
        : null;

    return AccessibilityHelper.ensureMinTouchTarget(
      minSize: AccessibilityHelper.recommendedTouchTargetSize,
      child: Focus(
        focusNode: _focusNode,
        onKeyEvent: (node, event) => _handleKeyEvent(event),
        child: Semantics(
          label: semanticLabel,
          hint: semanticHint,
          button: widget.onTap != null || widget.isEditable,
          enabled: !widget.isLoading && !_isUploading,
          focusable: widget.onTap != null || widget.isEditable,
          child: GestureDetector(
            onTap: widget.isLoading || _isUploading ? null : _handleTap,
            onTapDown: widget.isLoading || _isUploading ? null : (_) => _animationController.forward(),
            onTapUp: widget.isLoading || _isUploading ? null : (_) => _animationController.reverse(),
            onTapCancel: () => _animationController.reverse(),
            child: AnimatedBuilder(
              animation: _scaleAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Stack(
                    children: [
                      avatarWidget,
                      _buildEditOverlay(context),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

/// Predefined avatar sizes for consistent usage across the app
class UserAvatarSizes {
  const UserAvatarSizes._(); // Private constructor to prevent instantiation
  
  /// Extra large avatar for profile screens (120px)
  static const double extraLarge = 120.0;
  
  /// Large avatar for headers and important displays (80px)
  static const double large = 80.0;
  
  /// Medium avatar for cards and lists (60px)
  static const double medium = 60.0;
  
  /// Small avatar for compact displays (40px)
  static const double small = 40.0;
  
  /// Extra small avatar for minimal contexts (32px)
  static const double extraSmall = 32.0;
}

/// Convenient factory constructors for common avatar configurations
extension UserAvatarVariants on UserAvatarWidget {
  /// Large avatar for profile headers
  static Widget large({
    String? imageUrl,
    required String initials,
    required String role,
    VoidCallback? onTap,
    bool isEditable = false,
    bool isLoading = false,
    Future<String?> Function()? onImageUpload,
    Function(String)? onImageUploaded,
    Function(String)? onImageUploadError,
  }) {
    return UserAvatarWidget(
      imageUrl: imageUrl,
      initials: initials,
      role: role,
      size: UserAvatarSizes.large,
      onTap: onTap,
      isEditable: isEditable,
      isLoading: isLoading,
      onImageUpload: onImageUpload,
      onImageUploaded: onImageUploaded,
      onImageUploadError: onImageUploadError,
    );
  }

  /// Medium avatar for cards and lists
  static Widget medium({
    String? imageUrl,
    required String initials,
    required String role,
    VoidCallback? onTap,
    bool isEditable = false,
    bool isLoading = false,
  }) {
    return UserAvatarWidget(
      imageUrl: imageUrl,
      initials: initials,
      role: role,
      size: UserAvatarSizes.medium,
      onTap: onTap,
      isEditable: isEditable,
      isLoading: isLoading,
    );
  }

  /// Small avatar for compact displays
  static Widget small({
    String? imageUrl,
    required String initials,
    required String role,
    VoidCallback? onTap,
  }) {
    return UserAvatarWidget(
      imageUrl: imageUrl,
      initials: initials,
      role: role,
      size: UserAvatarSizes.small,
      onTap: onTap,
      isEditable: false,
    );
  }
}