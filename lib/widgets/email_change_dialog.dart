import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import '../theme/theme_aware_colors.dart';

/// Professional email change dialog with security features
class EmailChangeDialog extends StatefulWidget {
  final String currentEmail;
  final VoidCallback? onEmailChangeInitiated;

  const EmailChangeDialog({
    super.key,
    required this.currentEmail,
    this.onEmailChangeInitiated,
  });

  @override
  State<EmailChangeDialog> createState() => _EmailChangeDialogState();
}

class _EmailChangeDialogState extends State<EmailChangeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _newEmailController = TextEditingController();
  final _passwordController = TextEditingController();
  
  bool _isLoading = false;
  bool _obscurePassword = true;
  String? _errorMessage;

  @override
  void dispose() {
    _newEmailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
  
  /// Temporary email change implementation
  Future<EmailChangeResult> _initiateEmailChange({
    required String currentEmail,
    required String newEmail,
    required String password,
  }) async {
    try {
      // Validate inputs
      if (!_isValidEmail(newEmail)) {
        return EmailChangeResult.error('Please enter a valid email address');
      }
      
      if (newEmail.toLowerCase() == currentEmail.toLowerCase()) {
        return EmailChangeResult.error('New email must be different from current email');
      }
      
      // Send email change request to Supabase
      final response = await Supabase.instance.client.auth.updateUser(
        UserAttributes(email: newEmail),
        emailRedirectTo: 'https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify?type=email_change&redirect_to=babytracker://email-verified',
      );
      
      if (response.user?.newEmail != null) {
        // Initialize automatic sync service if not already done
        if (!AutomaticEmailSyncService.isInitialized) {
          await AutomaticEmailSyncService.initialize();
        }
        
        return EmailChangeResult.success(
          'Your email will be updated once you confirm the verification emails sent to both your current and new email addresses.',
        );
      } else {
        return EmailChangeResult.error('Failed to initiate email change. Please try again.');
      }
      
    } catch (e) {
      if (e.toString().contains('rate limit')) {
        return EmailChangeResult.error('Too many requests. Please wait before trying again.');
      } else {
        return EmailChangeResult.error('Failed to change email: ${e.toString()}');
      }
    }
  }
  
  /// Validate email format
  bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.security,
            color: ThemeAwareColors.getPrimaryColor(context),
            size: 24,
          ),
          SizedBox(width: 2.w),
          Text(
            'Change Email Address',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Security notice
              Container(
                padding: EdgeInsets.all(3.w),
                decoration: BoxDecoration(
                  color: ThemeAwareColors.getInfoColor(context).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: ThemeAwareColors.getInfoColor(context).withValues(alpha: 0.3),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.security,
                          color: ThemeAwareColors.getInfoColor(context),
                          size: 20,
                        ),
                        SizedBox(width: 2.w),
                        Text(
                          'Two-Step Email Verification',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: ThemeAwareColors.getInfoColor(context),
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 1.h),
                    Text(
                      'For security, you\'ll need to verify BOTH emails:',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getInfoColor(context),
                      ),
                    ),
                    SizedBox(height: 0.5.h),
                    Text(
                      '1. Current email: ${widget.currentEmail}',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getInfoColor(context),
                        fontSize: 11,
                      ),
                    ),
                    Text(
                      '2. New email: (the one you enter below)',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: ThemeAwareColors.getInfoColor(context),
                        fontSize: 11,
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 4.h),
              
              // Current email (read-only)
              Text(
                'Current Email',
                style: Theme.of(context).textTheme.labelMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 1.h),
              Container(
                width: double.infinity,
                padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 2.h),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  widget.currentEmail,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
              ),
              SizedBox(height: 3.h),
              
              // New email input
              TextFormField(
                controller: _newEmailController,
                keyboardType: TextInputType.emailAddress,
                decoration: InputDecoration(
                  labelText: 'New Email Address',
                  hintText: 'Enter your new email address',
                  prefixIcon: Icon(
                    Icons.email_outlined,
                    color: ThemeAwareColors.getIconColor(context),
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: ThemeAwareColors.getPrimaryColor(context),
                      width: 2,
                    ),
                  ),
                  labelStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter your new email address';
                  }
                  if (!RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(value)) {
                    return 'Please enter a valid email address';
                  }
                  if (value.toLowerCase() == widget.currentEmail.toLowerCase()) {
                    return 'New email must be different from current email';
                  }
                  return null;
                },
              ),
              SizedBox(height: 3.h),
              
              // Password confirmation
              TextFormField(
                controller: _passwordController,
                obscureText: _obscurePassword,
                decoration: InputDecoration(
                  labelText: 'Current Password',
                  hintText: 'Enter your current password to confirm',
                  prefixIcon: Icon(
                    Icons.lock_outline,
                    color: ThemeAwareColors.getIconColor(context),
                  ),
                  suffixIcon: IconButton(
                    icon: Icon(
                      _obscurePassword ? Icons.visibility : Icons.visibility_off,
                      color: ThemeAwareColors.getIconColor(context),
                    ),
                    onPressed: () {
                      setState(() {
                        _obscurePassword = !_obscurePassword;
                      });
                    },
                  ),
                  filled: true,
                  fillColor: Theme.of(context).colorScheme.surface,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: ThemeAwareColors.getPrimaryColor(context),
                      width: 2,
                    ),
                  ),
                  labelStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter your current password';
                  }
                  if (value.length < 6) {
                    return 'Password must be at least 6 characters';
                  }
                  return null;
                },
              ),
              
              // Error message
              if (_errorMessage != null) ...[
                SizedBox(height: 2.h),
                Container(
                  padding: EdgeInsets.all(3.w),
                  decoration: BoxDecoration(
                    color: ThemeAwareColors.getErrorColor(context).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: ThemeAwareColors.getErrorColor(context).withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.error_outline,
                        color: ThemeAwareColors.getErrorColor(context),
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Expanded(
                        child: Text(
                          _errorMessage!,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: ThemeAwareColors.getErrorColor(context),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.pop(context),
          child: Text(
            'Cancel',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
            ),
          ),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _handleEmailChange,
          style: ElevatedButton.styleFrom(
            backgroundColor: ThemeAwareColors.getPrimaryColor(context),
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          child: _isLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  'Send Verification',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                  ),
                ),
        ),
      ],
    );
  }

  Future<void> _handleEmailChange() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Temporary direct implementation until service is properly exported
      final result = await _initiateEmailChange(
        currentEmail: widget.currentEmail,
        newEmail: _newEmailController.text.trim(),
        password: _passwordController.text,
      );

      if (result.isSuccess) {
        Navigator.pop(context);
        widget.onEmailChangeInitiated?.call();
        
        // Show success dialog
        _showSuccessDialog(result.message);
      } else {
        setState(() {
          _errorMessage = result.message;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'An unexpected error occurred. Please try again.';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              Icons.mark_email_read,
              color: ThemeAwareColors.getSuccessColor(context),
              size: 24,
            ),
            SizedBox(width: 2.w),
            Text(
              'Verification Sent',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              message,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            SizedBox(height: 3.h),
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: ThemeAwareColors.getWarningColor(context).withValues(alpha: 0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: ThemeAwareColors.getWarningColor(context),
                        size: 20,
                      ),
                      SizedBox(width: 2.w),
                      Text(
                        'Next Steps:',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: ThemeAwareColors.getWarningColor(context),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    '1. Check your CURRENT email first',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getWarningColor(context),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '2. Click the verification link there',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getWarningColor(context),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '3. Then check your NEW email',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getWarningColor(context),
                      fontSize: 12,
                    ),
                  ),
                  Text(
                    '4. Click the verification link there too',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getWarningColor(context),
                      fontSize: 12,
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Text(
                    'Note: You may see error messages when clicking links - this is normal. Both steps are required for security.',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: ThemeAwareColors.getWarningColor(context),
                      fontSize: 11,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            style: ElevatedButton.styleFrom(
              backgroundColor: ThemeAwareColors.getPrimaryColor(context),
              foregroundColor: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 1.5.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(
              'Got it',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }
}

/// Result of email change operation
class EmailChangeResult {
  final bool isSuccess;
  final String message;
  
  const EmailChangeResult._(this.isSuccess, this.message);
  
  factory EmailChangeResult.success(String message) => EmailChangeResult._(true, message);
  factory EmailChangeResult.error(String message) => EmailChangeResult._(false, message);
}