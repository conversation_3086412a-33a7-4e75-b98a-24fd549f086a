import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';

import '../services/measurement_units_service.dart';
import '../services/enhanced_measurement_storage_service.dart';

/// Enhanced measurement-aware text field that preserves original units
class EnhancedMeasurementAwareTextField extends StatefulWidget {
  final String measurementType;
  final MeasurementDisplayData? initialData;
  final Function(Map<String, dynamic>) onMeasurementChanged;
  final String? label;
  final String? hintText;
  final bool isRequired;
  final TextInputType keyboardType;
  final List<TextInputFormatter>? inputFormatters;

  const EnhancedMeasurementAwareTextField({
    super.key,
    required this.measurementType,
    required this.onMeasurementChanged,
    this.initialData,
    this.label,
    this.hintText,
    this.isRequired = false,
    this.keyboardType = TextInputType.number,
    this.inputFormatters,
  });

  @override
  State<EnhancedMeasurementAwareTextField> createState() => _EnhancedMeasurementAwareTextFieldState();
}

class _EnhancedMeasurementAwareTextFieldState extends State<EnhancedMeasurementAwareTextField> {
  late TextEditingController _controller;
  late MeasurementUnitsService _unitsService;
  bool _isInitialized = false;
  MeasurementDisplayData? _currentData;
  bool _hasUserInput = false;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _currentData = widget.initialData;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _unitsService = context.watch<MeasurementUnitsService>();
    
    if (!_isInitialized) {
      _updateDisplayValue();
      _isInitialized = true;
    } else {
      // Units changed, update display value
      _updateDisplayValue();
    }
  }

  void _updateDisplayValue() {
    if (_currentData != null && _currentData!.hasData) {
      final displayValue = _currentData!.getValueForCurrentPreference(_unitsService.isMetric);
      _controller.text = displayValue.toStringAsFixed(1);
    }
  }

  void _onTextChanged(String value) {
    _hasUserInput = true;
    
    if (value.isEmpty) {
      _currentData = null;
      widget.onMeasurementChanged({});
      return;
    }

    final inputValue = double.tryParse(value);
    if (inputValue != null) {
      // Create storage data preserving the user's original input
      final storageData = EnhancedMeasurementStorageService.prepareMeasurementForStorage(
        value: inputValue,
        unit: _currentUnit,
        measurementType: widget.measurementType,
        wasEnteredInMetric: _unitsService.isMetric,
      );
      
      // Update current data for display
      _currentData = EnhancedMeasurementStorageService.extractMeasurementForDisplay(
        data: storageData,
        measurementType: widget.measurementType,
        displayAsMetric: _unitsService.isMetric,
      );
      
      widget.onMeasurementChanged(storageData);
    }
  }

  String get _currentUnit => _unitsService.getUnitForMeasurementType(widget.measurementType);
  
  String get _hintText {
    if (widget.hintText != null) return widget.hintText!;
    return 'Enter ${widget.measurementType.replaceAll('_', ' ')} in $_currentUnit';
  }

  String? _validator(String? value) {
    if (widget.isRequired && (value == null || value.isEmpty)) {
      return '${widget.label ?? widget.measurementType} is required';
    }
    
    if (value != null && value.isNotEmpty) {
      final numValue = double.tryParse(value);
      if (numValue == null) {
        return 'Please enter a valid number';
      }
      
      final range = _getValidationRange();
      if (numValue < range['min']! || numValue > range['max']!) {
        return _unitsService.getValidationMessage(widget.measurementType);
      }
    }
    
    return null;
  }

  Map<String, double> _getValidationRange() {
    switch (widget.measurementType.toLowerCase()) {
      case 'weight':
        return _unitsService.getWeightValidationRange();
      case 'head_circumference':
        return _unitsService.getHeadCircumferenceValidationRange();
      default:
        return _unitsService.getLengthValidationRange();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextFormField(
          controller: _controller,
          onChanged: _onTextChanged,
          validator: _validator,
          keyboardType: widget.keyboardType,
          inputFormatters: widget.inputFormatters ?? [
            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
          ],
          decoration: InputDecoration(
            labelText: widget.label,
            hintText: _hintText,
            suffixText: _currentUnit,
            filled: true,
            fillColor: Theme.of(context).colorScheme.surface,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
              ),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).dividerColor.withValues(alpha: 0.3),
              ),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.primary,
                width: 2,
              ),
            ),
            errorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
              ),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(
                color: Theme.of(context).colorScheme.error,
                width: 2,
              ),
            ),
            suffixStyle: TextStyle(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
        
        // Show original entry info if different from current display
        if (_currentData != null && 
            _currentData!.hasData && 
            _hasUserInput &&
            _currentData!.wasEnteredAsMetric != _unitsService.isMetric) ...[
          SizedBox(height: 0.5),
          Padding(
            padding: EdgeInsets.only(left: 12),
            child: Text(
              'Originally entered as: ${_currentData!.formattedOriginal}',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ],
    );
  }
}