import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/family_member.dart';
import '../models/enums.dart';
import '../widgets/family_sharing_card.dart';

/// Example demonstrating FamilySharingCard widget usage
class FamilySharingCardExample extends StatefulWidget {
  const FamilySharingCardExample({super.key});

  @override
  State<FamilySharingCardExample> createState() => _FamilySharingCardExampleState();
}

class _FamilySharingCardExampleState extends State<FamilySharingCardExample> {
  bool _isLoading = false;
  String? _errorMessage;
  List<FamilyMember> _familyMembers = [];
  
  // Sample family members data
  final List<FamilyMember> _sampleMembers = [
    FamilyMember(
      id: '1',
      fullName: '<PERSON>',
      email: '<EMAIL>',
      role: 'parent',
      avatarUrl: null,
      joinedAt: DateTime.now().subtract(const Duration(days: 45)),
      lastActiveAt: DateTime.now().subtract(const Duration(hours: 2)),
      status: FamilyMemberStatus.active,
      permissions: {
        'view_activities': true,
        'add_activities': true,
        'manage_family': true,
      },
    ),
    FamilyMember(
      id: '2',
      fullName: 'Jane Smith',
      email: '<EMAIL>',
      role: 'caregiver',
      avatarUrl: null,
      joinedAt: DateTime.now().subtract(const Duration(days: 20)),
      lastActiveAt: DateTime.now().subtract(const Duration(days: 1)),
      status: FamilyMemberStatus.active,
      permissions: {
        'view_activities': true,
        'add_activities': true,
      },
    ),
    FamilyMember(
      id: '3',
      fullName: 'Bob Johnson',
      email: '<EMAIL>',
      role: 'grandparent',
      avatarUrl: null,
      joinedAt: DateTime.now().subtract(const Duration(days: 10)),
      status: FamilyMemberStatus.pending,
      permissions: {
        'view_activities': true,
      },
      invitationSentAt: DateTime.now().subtract(const Duration(days: 3)),
      invitationExpiresAt: DateTime.now().add(const Duration(days: 4)),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _familyMembers = List.from(_sampleMembers);
  }

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'FamilySharingCard Example',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          home: Scaffold(
            appBar: AppBar(
              title: const Text('Family Sharing Card Examples'),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _toggleLoading,
                  tooltip: 'Toggle Loading State',
                ),
                IconButton(
                  icon: const Icon(Icons.error),
                  onPressed: _toggleError,
                  tooltip: 'Toggle Error State',
                ),
              ],
            ),
            body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(vertical: 2.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Full Family Sharing Card with Invitation System
                  _buildSectionTitle('Full Family Sharing Card with Invitation System'),
                  FamilySharingCard(
                    familyMembers: _isLoading ? [] : _familyMembers,
                    isLoading: _isLoading,
                    canManageFamily: true,
                    onSendInvitation: _handleSendInvitation,
                    onResendInvitation: _handleResendInvitation,
                    onCancelInvitation: _handleCancelInvitation,
                    onMemberTap: _handleMemberTap,
                    onManageFamily: _handleManageFamily,
                    maxFamilyMembers: 6,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Empty State Example
                  _buildSectionTitle('Empty State'),
                  const FamilySharingCard(
                    familyMembers: [],
                    canManageFamily: true,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Read-Only Example (No Management Permissions)
                  _buildSectionTitle('Read-Only (No Management Permissions)'),
                  FamilySharingCard(
                    familyMembers: _familyMembers,
                    canManageFamily: false,
                    onMemberTap: _handleMemberTap,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Large Family Example
                  _buildSectionTitle('Large Family (7+ Members)'),
                  FamilySharingCard(
                    familyMembers: _generateLargeFamilyList(),
                    canManageFamily: true,
                    onInviteMember: _handleInviteMember,
                    onMemberTap: _handleMemberTap,
                    maxFamilyMembers: 10,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // At Capacity Example
                  _buildSectionTitle('At Maximum Capacity'),
                  FamilySharingCard(
                    familyMembers: _familyMembers,
                    canManageFamily: true,
                    onSendInvitation: _handleSendInvitation,
                    onMemberTap: _handleMemberTap,
                    onManageFamily: _handleManageFamily,
                    maxFamilyMembers: 3, // Same as current member count
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Loading State Example
                  _buildSectionTitle('Loading State'),
                  const FamilySharingCard(
                    familyMembers: [],
                    isLoading: true,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Pending Invitations Example
                  _buildSectionTitle('With Pending Invitations'),
                  FamilySharingCard(
                    familyMembers: _generateMembersWithPendingInvitations(),
                    canManageFamily: true,
                    onSendInvitation: _handleSendInvitation,
                    onResendInvitation: _handleResendInvitation,
                    onCancelInvitation: _handleCancelInvitation,
                    onMemberTap: _handleMemberTap,
                    maxFamilyMembers: 8,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  List<FamilyMember> _generateLargeFamilyList() {
    final roles = ['parent', 'caregiver', 'grandparent', 'babysitter'];
    final statuses = [FamilyMemberStatus.active, FamilyMemberStatus.pending];
    
    return List.generate(8, (index) {
      final role = roles[index % roles.length];
      final status = statuses[index % statuses.length];
      
      return FamilyMember(
        id: 'large_$index',
        fullName: 'Family Member ${index + 1}',
        email: 'member${index + 1}@example.com',
        role: role,
        joinedAt: DateTime.now().subtract(Duration(days: index * 5)),
        lastActiveAt: status == FamilyMemberStatus.active 
            ? DateTime.now().subtract(Duration(hours: index))
            : null,
        status: status,
        permissions: {
          'view_activities': true,
          if (role == 'parent') 'manage_family': true,
        },
        invitationSentAt: status == FamilyMemberStatus.pending 
            ? DateTime.now().subtract(Duration(days: index))
            : null,
        invitationExpiresAt: status == FamilyMemberStatus.pending 
            ? DateTime.now().add(Duration(days: 7 - index))
            : null,
      );
    });
  }

  List<FamilyMember> _generateMembersWithPendingInvitations() {
    return [
      // Active members
      FamilyMember(
        id: 'active_1',
        fullName: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'parent',
        joinedAt: DateTime.now().subtract(const Duration(days: 30)),
        lastActiveAt: DateTime.now().subtract(const Duration(minutes: 15)),
        status: FamilyMemberStatus.active,
        permissions: {
          'view_activities': true,
          'add_activities': true,
          'manage_family': true,
        },
      ),
      // Recent pending invitation
      FamilyMember(
        id: 'pending_1',
        fullName: 'Mike Wilson',
        email: '<EMAIL>',
        role: 'caregiver',
        joinedAt: DateTime.now().subtract(const Duration(days: 2)),
        status: FamilyMemberStatus.pending,
        permissions: {
          'view_activities': true,
          'add_activities': true,
        },
        invitationSentAt: DateTime.now().subtract(const Duration(days: 2)),
        invitationExpiresAt: DateTime.now().add(const Duration(days: 5)),
      ),
      // Expired invitation
      FamilyMember(
        id: 'pending_2',
        fullName: 'Lisa Brown',
        email: '<EMAIL>',
        role: 'grandparent',
        joinedAt: DateTime.now().subtract(const Duration(days: 10)),
        status: FamilyMemberStatus.pending,
        permissions: {
          'view_activities': true,
        },
        invitationSentAt: DateTime.now().subtract(const Duration(days: 10)),
        invitationExpiresAt: DateTime.now().subtract(const Duration(days: 3)), // Expired
      ),
      // Recent invitation
      FamilyMember(
        id: 'pending_3',
        fullName: 'Tom Davis',
        email: '<EMAIL>',
        role: 'babysitter',
        joinedAt: DateTime.now().subtract(const Duration(hours: 6)),
        status: FamilyMemberStatus.pending,
        permissions: {
          'view_activities': true,
          'add_activities': true,
        },
        invitationSentAt: DateTime.now().subtract(const Duration(hours: 6)),
        invitationExpiresAt: DateTime.now().add(const Duration(days: 6, hours: 18)),
      ),
    ];
  }

  void _toggleLoading() {
    setState(() {
      _isLoading = !_isLoading;
      if (_isLoading) {
        _errorMessage = null; // Clear error when loading
      }
    });
  }

  void _toggleError() {
    setState(() {
      _errorMessage = _errorMessage == null 
          ? 'Network connection failed. Please try again.'
          : null;
      if (_errorMessage != null) {
        _isLoading = false; // Clear loading when showing error
      }
    });
  }

  void _handleSendInvitation(
    String email,
    String role,
    Map<String, bool> permissions,
    String? message,
  ) {
    // Simulate sending invitation
    setState(() {
      _isLoading = true;
    });

    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        // Add new pending member
        final newMember = FamilyMember(
          id: 'pending_${DateTime.now().millisecondsSinceEpoch}',
          fullName: email.split('@')[0], // Temporary name
          email: email,
          role: role,
          joinedAt: DateTime.now(),
          status: FamilyMemberStatus.pending,
          permissions: permissions,
          invitationSentAt: DateTime.now(),
          invitationExpiresAt: DateTime.now().add(const Duration(days: 7)),
        );

        setState(() {
          _familyMembers.add(newMember);
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invitation sent to $email as $role'),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    });
  }

  void _handleResendInvitation(FamilyMember member) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Resending invitation to ${member.email}...'),
        duration: const Duration(seconds: 2),
      ),
    );

    // Simulate resending
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        // Update invitation sent time
        final memberIndex = _familyMembers.indexWhere((m) => m.id == member.id);
        if (memberIndex != -1) {
          setState(() {
            _familyMembers[memberIndex] = _familyMembers[memberIndex].copyWith(
              invitationSentAt: DateTime.now(),
              invitationExpiresAt: DateTime.now().add(const Duration(days: 7)),
            );
          });
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Invitation resent to ${member.email}'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  void _handleCancelInvitation(FamilyMember member) {
    setState(() {
      _familyMembers.removeWhere((m) => m.id == member.id);
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Invitation cancelled for ${member.email}'),
        backgroundColor: Colors.orange,
      ),
    );
  }

  void _handleMemberTap(FamilyMember member) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Member tapped: ${member.fullName} (${member.role})'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _handleManageFamily() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Manage Family tapped - would open family management screen'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleRetry() {
    setState(() {
      _errorMessage = null;
      _isLoading = true;
    });
    
    // Simulate retry delay
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }
}

/// Main function to run the example
void main() {
  runApp(const FamilySharingCardExample());
}