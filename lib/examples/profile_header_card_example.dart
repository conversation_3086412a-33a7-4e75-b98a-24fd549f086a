import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../models/profile_completion_status.dart';
import '../widgets/profile_header_card.dart';

/// Example demonstrating ProfileHeaderCard widget usage
class ProfileHeaderCardExample extends StatefulWidget {
  const ProfileHeaderCardExample({super.key});

  @override
  State<ProfileHeaderCardExample> createState() => _ProfileHeaderCardExampleState();
}

class _ProfileHeaderCardExampleState extends State<ProfileHeaderCardExample> {
  bool _isLoading = false;
  
  // Sample user profile data
  final UserProfile _sampleProfile = UserProfile(
    id: 'sample-user-id',
    email: '<EMAIL>',
    fullName: '<PERSON>',
    avatarUrl: null, // No avatar to show initials fallback
    role: 'parent',
    signInCount: 15,
    createdAt: DateTime.now().subtract(const Duration(days: 45)),
    updatedAt: DateTime.now(),
    lastSignInAt: DateTime.now().subtract(const Duration(hours: 3)),
  );
  
  // Sample completion status
  final ProfileCompletionStatus _sampleCompletion = const ProfileCompletionStatus(
    percentage: 75.0,
    completedSteps: ['basic_info', 'baby_profile', 'preferences'],
    remainingSteps: ['profile_photo', 'family_setup'],
    nextRecommendedAction: 'Add a profile photo',
    totalSteps: 5,
    completedCount: 3,
  );

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'ProfileHeaderCard Example',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          home: Scaffold(
            appBar: AppBar(
              title: const Text('Profile Header Card Examples'),
              actions: [
                IconButton(
                  icon: const Icon(Icons.refresh),
                  onPressed: _toggleLoading,
                  tooltip: 'Toggle Loading State',
                ),
              ],
            ),
            body: SingleChildScrollView(
              padding: EdgeInsets.symmetric(vertical: 2.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Full Profile Header Card
                  _buildSectionTitle('Full Profile Header'),
                  ProfileHeaderCard(
                    userProfile: _isLoading ? null : _sampleProfile,
                    completionStatus: _sampleCompletion,
                    isLoading: _isLoading,
                    onEditProfile: _handleEditProfile,
                    onAvatarTap: _handleAvatarTap,
                    showCompletionProgress: true,
                    showAccountDates: true,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Compact Profile Header Card
                  _buildSectionTitle('Compact Profile Header'),
                  ProfileHeaderCardVariants.compact(
                    userProfile: _isLoading ? null : _sampleProfile,
                    isLoading: _isLoading,
                    onEditProfile: _handleEditProfile,
                    onAvatarTap: _handleAvatarTap,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Read-Only Profile Header Card
                  _buildSectionTitle('Read-Only Profile Header'),
                  ProfileHeaderCardVariants.readOnly(
                    userProfile: _isLoading ? null : _sampleProfile,
                    isLoading: _isLoading,
                  ),
                  
                  SizedBox(height: 3.h),
                  
                  // Different Role Examples
                  _buildSectionTitle('Different User Roles'),
                  _buildRoleExample('Admin', 'admin'),
                  SizedBox(height: 1.h),
                  _buildRoleExample('Caregiver', 'caregiver'),
                  SizedBox(height: 1.h),
                  _buildRoleExample('Grandparent', 'grandparent'),
                  
                  SizedBox(height: 3.h),
                  
                  // Different Completion Status Examples
                  _buildSectionTitle('Different Completion Status'),
                  _buildCompletionExample('Just Started', 25.0),
                  SizedBox(height: 1.h),
                  _buildCompletionExample('Almost Complete', 90.0),
                  SizedBox(height: 1.h),
                  _buildCompletionExample('Complete', 100.0),
                  
                  SizedBox(height: 3.h),
                  
                  // Error State Example
                  _buildSectionTitle('Error State'),
                  const ProfileHeaderCard(
                    userProfile: null,
                    isLoading: false,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildRoleExample(String displayName, String role) {
    final roleProfile = _sampleProfile.copyWith(
      fullName: '$displayName User',
      role: role,
      email: '${role.toLowerCase()}@example.com',
    );
    
    return ProfileHeaderCardVariants.compact(
      userProfile: roleProfile,
      isEditable: false,
    );
  }

  Widget _buildCompletionExample(String statusName, double percentage) {
    final completionStatus = ProfileCompletionStatus(
      percentage: percentage,
      completedSteps: _getCompletedStepsForPercentage(percentage),
      remainingSteps: _getRemainingStepsForPercentage(percentage),
      nextRecommendedAction: _getNextActionForPercentage(percentage),
      totalSteps: 5,
      completedCount: (percentage / 20).round(),
    );
    
    final profile = _sampleProfile.copyWith(
      fullName: '$statusName Profile',
    );
    
    return ProfileHeaderCard(
      userProfile: profile,
      completionStatus: completionStatus,
      showAccountDates: false,
      isEditable: false,
    );
  }

  List<String> _getCompletedStepsForPercentage(double percentage) {
    final allSteps = ['basic_info', 'profile_photo', 'baby_profile', 'preferences', 'family_setup'];
    final completedCount = (percentage / 20).round();
    return allSteps.take(completedCount).toList();
  }

  List<String> _getRemainingStepsForPercentage(double percentage) {
    final allSteps = ['basic_info', 'profile_photo', 'baby_profile', 'preferences', 'family_setup'];
    final completedCount = (percentage / 20).round();
    return allSteps.skip(completedCount).toList();
  }

  String _getNextActionForPercentage(double percentage) {
    if (percentage >= 100) return 'Profile complete!';
    if (percentage >= 80) return 'Set up family sharing';
    if (percentage >= 60) return 'Configure preferences';
    if (percentage >= 40) return 'Create baby profile';
    if (percentage >= 20) return 'Add profile photo';
    return 'Complete basic information';
  }

  void _toggleLoading() {
    setState(() {
      _isLoading = !_isLoading;
    });
  }

  void _handleEditProfile() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Edit Profile tapped'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _handleAvatarTap() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Avatar tapped - would open photo picker'),
        duration: Duration(seconds: 2),
      ),
    );
  }
}

/// Main function to run the example
void main() {
  runApp(const ProfileHeaderCardExample());
}