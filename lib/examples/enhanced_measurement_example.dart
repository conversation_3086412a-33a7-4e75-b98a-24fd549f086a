import 'package:flutter/material.dart';
import '../models/measurement.dart';
import '../models/baby_profile.dart';
import '../services/enhanced_measurement_service.dart';
import '../services/growth_analyzer.dart';

/// Example demonstrating the enhanced measurement system
class EnhancedMeasurementExample {
  static final EnhancedMeasurementService _measurementService = EnhancedMeasurementService();

  /// Example of saving a measurement with automatic validation and percentile calculation
  static Future<void> saveMeasurementExample() async {
    // Create a sample baby profile
    final babyProfile = BabyProfile(
      id: 'baby-123',
      name: 'Emma',
      birthDate: DateTime.now().subtract(const Duration(days: 180)), // 6 months old
      gender: 'female',
      birthWeight: 3.2,
      birthHeight: 50.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      // Save a weight measurement
      final measurement = await _measurementService.saveMeasurement(
        babyId: babyProfile.id,
        measurementType: 'weight',
        value: 7.5, // 7.5 kg
        unit: 'kg',
        measuredAt: DateTime.now(),
        babyProfile: babyProfile,
        notes: 'Regular checkup measurement',
        metadata: {
          'source': 'pediatric_visit',
          'measured_by': 'Dr. Smith',
          'scale_type': 'digital',
        },
      );

      debugPrint('✅ Measurement saved successfully!');
      debugPrint('📊 Percentile: ${measurement.percentileDisplay}');
      debugPrint('📈 Z-score: ${measurement.zScoreDisplay}');
      debugPrint('🚨 Requires attention: ${measurement.requiresAttention}');
      
      if (measurement.validationResults != null) {
        final validation = measurement.validationResults!;
        if (validation['warnings'] != null && (validation['warnings'] as List).isNotEmpty) {
          debugPrint('⚠️ Warnings: ${validation['warnings']}');
        }
      }

    } catch (e) {
      debugPrint('❌ Error saving measurement: $e');
    }
  }

  /// Example of validating measurement data before saving
  static Future<void> validateMeasurementExample() async {
    final babyProfile = BabyProfile(
      id: 'baby-123',
      name: 'Emma',
      birthDate: DateTime.now().subtract(const Duration(days: 180)),
      gender: 'female',
      birthWeight: 3.2,
      birthHeight: 50.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Validate a potentially concerning measurement
    final validation = await _measurementService.validateMeasurementData(
      value: 4.0, // Very low weight for 6 months
      ageInMonths: 6.0,
      measurementType: 'weight',
      babyProfile: babyProfile,
      measurementDate: DateTime.now(),
    );

    debugPrint('🔍 Validation Results:');
    debugPrint('Valid: ${validation.isValid}');
    debugPrint('Errors: ${validation.errors}');
    debugPrint('Warnings: ${validation.warnings}');
    debugPrint('Requires attention: ${validation.requiresAttention}');
    
    if (validation.metadata.isNotEmpty) {
      debugPrint('Metadata: ${validation.metadata}');
    }
  }

  /// Example of comprehensive growth analysis
  static Future<void> growthAnalysisExample() async {
    final babyProfile = BabyProfile(
      id: 'baby-123',
      name: 'Emma',
      birthDate: DateTime.now().subtract(const Duration(days: 365)), // 1 year old
      gender: 'female',
      birthWeight: 3.2,
      birthHeight: 50.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Create sample measurements over time
    final measurements = [
      Measurement(
        id: '1',
        babyId: babyProfile.id,
        measurementType: 'weight',
        value: 3.2,
        unit: 'kg',
        measuredAt: babyProfile.birthDate,
        ageInMonths: 0.0,
        percentile: 50.0,
        zScore: 0.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Measurement(
        id: '2',
        babyId: babyProfile.id,
        measurementType: 'weight',
        value: 5.5,
        unit: 'kg',
        measuredAt: babyProfile.birthDate.add(const Duration(days: 90)),
        ageInMonths: 3.0,
        percentile: 45.0,
        zScore: -0.2,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Measurement(
        id: '3',
        babyId: babyProfile.id,
        measurementType: 'weight',
        value: 7.8,
        unit: 'kg',
        measuredAt: babyProfile.birthDate.add(const Duration(days: 180)),
        ageInMonths: 6.0,
        percentile: 55.0,
        zScore: 0.1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
      Measurement(
        id: '4',
        babyId: babyProfile.id,
        measurementType: 'weight',
        value: 9.2,
        unit: 'kg',
        measuredAt: babyProfile.birthDate.add(const Duration(days: 365)),
        ageInMonths: 12.0,
        percentile: 60.0,
        zScore: 0.25,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      ),
    ];

    // Convert to MeasurementData for analysis
    final measurementData = measurements
        .map((m) => m.toMeasurementData(babyProfile.gender))
        .toList();

    // Perform comprehensive growth analysis
    final analysis = GrowthAnalyzer.analyzeGrowthPattern(measurementData, babyProfile);

    debugPrint('📈 Growth Analysis Results:');
    debugPrint('Overall Assessment: ${analysis.overallAssessment}');
    debugPrint('Growth Summary: ${analysis.growthSummary}');
    debugPrint('Alerts: ${analysis.alerts.length}');
    
    for (final alert in analysis.alerts) {
      debugPrint('🚨 ${alert.severity.toUpperCase()}: ${alert.title}');
      debugPrint('   ${alert.description}');
      debugPrint('   Recommendations: ${alert.recommendations.join(', ')}');
    }

    if (analysis.velocityAnalysis != null) {
      final velocity = analysis.velocityAnalysis!;
      debugPrint('🏃 Growth Velocity:');
      debugPrint('   ${velocity.velocityPerMonth.toStringAsFixed(2)} kg/month');
      debugPrint('   ${velocity.velocityPercentile.toStringAsFixed(1)}th percentile');
      debugPrint('   ${velocity.interpretation}');
    }

    debugPrint('💡 Recommendations:');
    for (final recommendation in analysis.recommendations) {
      debugPrint('   • $recommendation');
    }
  }

  /// Example of getting measurements requiring attention
  static Future<void> attentionRequiredExample() async {
    final babyId = 'baby-123';
    
    try {
      final attentionMeasurements = await _measurementService.getMeasurementsRequiringAttention(babyId);
      
      debugPrint('🚨 Measurements Requiring Attention: ${attentionMeasurements.length}');
      
      for (final measurement in attentionMeasurements) {
        debugPrint('📅 ${measurement.measuredAt.toLocal()}');
        debugPrint('   ${measurement.measurementType}: ${measurement.displayValue}');
        debugPrint('   Percentile: ${measurement.percentileDisplay}');
        debugPrint('   Flagged: ${measurement.flaggedForReview}');
        
        if (measurement.validationResults != null) {
          final validation = measurement.validationResults!;
          if (validation['warnings'] != null) {
            debugPrint('   Warnings: ${validation['warnings']}');
          }
        }
        debugPrint('');
      }
    } catch (e) {
      debugPrint('❌ Error getting attention measurements: $e');
    }
  }

  /// Example of getting growth summary
  static Future<void> growthSummaryExample() async {
    final babyProfile = BabyProfile(
      id: 'baby-123',
      name: 'Emma',
      birthDate: DateTime.now().subtract(const Duration(days: 365)),
      gender: 'female',
      birthWeight: 3.2,
      birthHeight: 50.0,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      final summary = await _measurementService.getGrowthSummary(babyProfile.id, babyProfile);
      
      debugPrint('📊 Growth Summary:');
      debugPrint('Total Measurements: ${summary['totalMeasurements']}');
      debugPrint('Measurement Types: ${summary['measurementTypes']}');
      debugPrint('Growth Trends: ${summary['growthTrends']}');
      
      final latestMeasurements = summary['latestMeasurements'] as Map<String, Measurement>;
      debugPrint('Latest Measurements:');
      for (final entry in latestMeasurements.entries) {
        final measurement = entry.value;
        debugPrint('   ${entry.key}: ${measurement.displayValue} (${measurement.percentileDisplay})');
      }
      
      final requiresAttention = summary['requiresAttention'] as List<Measurement>;
      if (requiresAttention.isNotEmpty) {
        debugPrint('🚨 ${requiresAttention.length} measurements require attention');
      }
      
    } catch (e) {
      debugPrint('❌ Error getting growth summary: $e');
    }
  }

  /// Example of measurement statistics
  static Future<void> measurementStatisticsExample() async {
    final babyId = 'baby-123';
    
    try {
      final stats = await _measurementService.getMeasurementStatistics(babyId);
      
      debugPrint('📈 Measurement Statistics:');
      debugPrint('Total Count: ${stats['totalCount']}');
      debugPrint('By Type: ${stats['byType']}');
      debugPrint('Flagged Count: ${stats['flaggedCount']}');
      debugPrint('Average Percentiles: ${stats['averagePercentiles']}');
      debugPrint('Latest Percentiles: ${stats['latestPercentiles']}');
      
    } catch (e) {
      debugPrint('❌ Error getting statistics: $e');
    }
  }

  /// Run all examples
  static Future<void> runAllExamples() async {
    debugPrint('🚀 Running Enhanced Measurement System Examples\n');
    
    debugPrint('1️⃣ Save Measurement Example:');
    await saveMeasurementExample();
    debugPrint('');
    
    debugPrint('2️⃣ Validate Measurement Example:');
    await validateMeasurementExample();
    debugPrint('');
    
    debugPrint('3️⃣ Growth Analysis Example:');
    await growthAnalysisExample();
    debugPrint('');
    
    debugPrint('4️⃣ Attention Required Example:');
    await attentionRequiredExample();
    debugPrint('');
    
    debugPrint('5️⃣ Growth Summary Example:');
    await growthSummaryExample();
    debugPrint('');
    
    debugPrint('6️⃣ Measurement Statistics Example:');
    await measurementStatisticsExample();
    debugPrint('');
    
    debugPrint('✅ All examples completed!');
  }
}

/// Widget demonstrating enhanced measurement functionality
class EnhancedMeasurementExampleWidget extends StatefulWidget {
  const EnhancedMeasurementExampleWidget({super.key});

  @override
  State<EnhancedMeasurementExampleWidget> createState() => _EnhancedMeasurementExampleWidgetState();
}

class _EnhancedMeasurementExampleWidgetState extends State<EnhancedMeasurementExampleWidget> {
  final EnhancedMeasurementService _measurementService = EnhancedMeasurementService();
  List<Measurement> _measurements = [];
  List<Measurement> _attentionMeasurements = [];
  Map<String, dynamic> _growthSummary = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadExampleData();
  }

  Future<void> _loadExampleData() async {
    setState(() => _isLoading = true);
    
    try {
      // Create sample baby profile
      final babyProfile = BabyProfile(
        id: 'example-baby',
        name: 'Example Baby',
        birthDate: DateTime.now().subtract(const Duration(days: 180)),
        gender: 'female',
        birthWeight: 3.2,
        birthHeight: 50.0,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Load measurements (in a real app, these would come from the database)
      _measurements = await _measurementService.getMeasurements(babyProfile.id);
      _attentionMeasurements = await _measurementService.getMeasurementsRequiringAttention(babyProfile.id);
      _growthSummary = await _measurementService.getGrowthSummary(babyProfile.id, babyProfile);
      
    } catch (e) {
      debugPrint('Error loading example data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Enhanced Measurement Example'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSummaryCard(),
                  const SizedBox(height: 16),
                  _buildMeasurementsCard(),
                  const SizedBox(height: 16),
                  _buildAttentionCard(),
                  const SizedBox(height: 16),
                  _buildExampleActionsCard(),
                ],
              ),
            ),
    );
  }

  Widget _buildSummaryCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Growth Summary',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text('Total Measurements: ${_growthSummary['totalMeasurements'] ?? 0}'),
            Text('Measurement Types: ${_growthSummary['measurementTypes']?.join(', ') ?? 'None'}'),
            Text('Requiring Attention: ${_attentionMeasurements.length}'),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurementsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Measurements',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            if (_measurements.isEmpty)
              const Text('No measurements available')
            else
              ..._measurements.take(3).map((measurement) => ListTile(
                title: Text('${measurement.measurementType}: ${measurement.displayValue}'),
                subtitle: Text('${measurement.percentileDisplay} • ${measurement.measuredAt.toLocal()}'),
                trailing: measurement.requiresAttention
                    ? const Icon(Icons.warning, color: Colors.orange)
                    : const Icon(Icons.check_circle, color: Colors.green),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildAttentionCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Measurements Requiring Attention',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            if (_attentionMeasurements.isEmpty)
              const Text('No measurements require attention', 
                style: TextStyle(color: Colors.green))
            else
              ..._attentionMeasurements.map((measurement) => ListTile(
                leading: const Icon(Icons.warning, color: Colors.red),
                title: Text('${measurement.measurementType}: ${measurement.displayValue}'),
                subtitle: Text('${measurement.percentileDisplay} • Flagged: ${measurement.flaggedForReview}'),
              )),
          ],
        ),
      ),
    );
  }

  Widget _buildExampleActionsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Example Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: () async {
                await EnhancedMeasurementExample.runAllExamples();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('Examples completed! Check console output.')),
                );
              },
              child: const Text('Run All Examples'),
            ),
            const SizedBox(height: 8),
            ElevatedButton(
              onPressed: _loadExampleData,
              child: const Text('Refresh Data'),
            ),
          ],
        ),
      ),
    );
  }
}