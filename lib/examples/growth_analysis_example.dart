import 'package:flutter/material.dart';
import '../models/baby_profile.dart';
import '../services/growth_analyzer.dart';
import '../services/enhanced_percentile_calculator.dart';
import '../presentation/growth_charts/widgets/growth_analysis_widget.dart';
import '../presentation/growth_charts/widgets/percentile_indicator_widget.dart';

/// Example demonstrating how to use the Growth Analysis and Alert System
class GrowthAnalysisExample extends StatefulWidget {
  const GrowthAnalysisExample({Key? key}) : super(key: key);

  @override
  State<GrowthAnalysisExample> createState() => _GrowthAnalysisExampleState();
}

class _GrowthAnalysisExampleState extends State<GrowthAnalysisExample> {
  late BabyProfile exampleBaby;
  late List<MeasurementData> exampleMeasurements;
  GrowthAnalysis? analysis;

  @override
  void initState() {
    super.initState();
    _setupExampleData();
    _performAnalysis();
  }

  void _setupExampleData() {
    // Create example baby profile
    exampleBaby = BabyProfile(
      id: 'example-baby',
      name: 'Example Baby',
      birthDate: DateTime.now().subtract(const Duration(days: 180)), // 6 months old
      gender: 'male',
      birthWeight: 3.2,
      birthHeight: 50.0,
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now(),
    );

    // Create example measurements showing normal growth
    exampleMeasurements = [
      MeasurementData(
        value: 3.2,
        ageInMonths: 0,
        date: exampleBaby.birthDate,
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 4.5,
        ageInMonths: 1,
        date: exampleBaby.birthDate.add(const Duration(days: 30)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 5.8,
        ageInMonths: 2,
        date: exampleBaby.birthDate.add(const Duration(days: 60)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 6.8,
        ageInMonths: 3,
        date: exampleBaby.birthDate.add(const Duration(days: 90)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 7.5,
        ageInMonths: 4,
        date: exampleBaby.birthDate.add(const Duration(days: 120)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 8.1,
        ageInMonths: 5,
        date: exampleBaby.birthDate.add(const Duration(days: 150)),
        measurementType: 'weight',
        gender: 'male',
      ),
    ];
  }

  void _performAnalysis() {
    setState(() {
      analysis = GrowthAnalyzer.analyzeGrowthPattern(exampleMeasurements, exampleBaby);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Growth Analysis Example'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: analysis == null
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Column(
                children: [
                  // Example of percentile indicators
                  _buildPercentileExamples(),
                  
                  // Main growth analysis widget
                  GrowthAnalysisWidget(
                    analysis: analysis!,
                    onRefresh: _performAnalysis,
                    onAlertTap: (alert) {
                      _showAlertDetails(alert);
                    },
                  ),
                  
                  // Example of summary widget
                  Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: GrowthAnalysisSummaryWidget(
                      analysis: analysis!,
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('Tapped on growth analysis summary'),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showDifferentScenarios,
        child: const Icon(Icons.science),
        tooltip: 'Try Different Scenarios',
      ),
    );
  }

  Widget _buildPercentileExamples() {
    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Percentile Indicators Examples',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          
          // Different percentile examples
          Wrap(
            spacing: 16,
            runSpacing: 16,
            children: [
              PercentileIndicatorWidget(
                percentile: 2.0,
                measurementType: 'weight',
                showLabel: true,
              ),
              PercentileIndicatorWidget(
                percentile: 15.0,
                measurementType: 'weight',
                showLabel: true,
              ),
              PercentileIndicatorWidget(
                percentile: 50.0,
                measurementType: 'weight',
                showLabel: true,
              ),
              PercentileIndicatorWidget(
                percentile: 85.0,
                measurementType: 'weight',
                showLabel: true,
              ),
              PercentileIndicatorWidget(
                percentile: 98.0,
                measurementType: 'weight',
                showLabel: true,
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Percentile scale example
          const Text('Percentile Scale:'),
          const SizedBox(height: 8),
          PercentileScaleWidget(
            percentile: analysis?.trends.isNotEmpty == true 
                ? analysis!.trends.last.percentile 
                : 50.0,
            showLabels: true,
          ),
        ],
      ),
    );
  }

  void _showAlertDetails(GrowthAlert alert) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(
              _getAlertIcon(alert.severity),
              color: _getAlertColor(alert.severity),
            ),
            const SizedBox(width: 8),
            Expanded(child: Text(alert.title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(alert.description),
            const SizedBox(height: 16),
            if (alert.recommendations.isNotEmpty) ...[
              const Text(
                'Recommendations:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ...alert.recommendations.map((rec) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(rec)),
                  ],
                ),
              )),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showDifferentScenarios() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Try Different Growth Scenarios',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.trending_down, color: Colors.red),
              title: const Text('Concerning Growth Pattern'),
              subtitle: const Text('Simulate percentile crossing'),
              onTap: () {
                Navigator.pop(context);
                _simulateConcerningGrowth();
              },
            ),
            ListTile(
              leading: const Icon(Icons.warning, color: Colors.orange),
              title: const Text('Below Normal Range'),
              subtitle: const Text('Simulate below 3rd percentile'),
              onTap: () {
                Navigator.pop(context);
                _simulateBelowNormalGrowth();
              },
            ),
            ListTile(
              leading: const Icon(Icons.trending_up, color: Colors.green),
              title: const Text('Normal Growth'),
              subtitle: const Text('Reset to normal pattern'),
              onTap: () {
                Navigator.pop(context);
                _setupExampleData();
                _performAnalysis();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _simulateConcerningGrowth() {
    // Create measurements with significant percentile drop
    exampleMeasurements = [
      MeasurementData(
        value: 8.0, // Normal weight at 3 months
        ageInMonths: 3,
        date: exampleBaby.birthDate.add(const Duration(days: 90)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 6.5, // Significant drop
        ageInMonths: 4,
        date: exampleBaby.birthDate.add(const Duration(days: 120)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 6.2, // Continued low growth
        ageInMonths: 5,
        date: exampleBaby.birthDate.add(const Duration(days: 150)),
        measurementType: 'weight',
        gender: 'male',
      ),
    ];
    _performAnalysis();
  }

  void _simulateBelowNormalGrowth() {
    // Create measurements below 3rd percentile
    exampleMeasurements = [
      MeasurementData(
        value: 2.0, // Very low weight
        ageInMonths: 3,
        date: exampleBaby.birthDate.add(const Duration(days: 90)),
        measurementType: 'weight',
        gender: 'male',
      ),
      MeasurementData(
        value: 2.2,
        ageInMonths: 4,
        date: exampleBaby.birthDate.add(const Duration(days: 120)),
        measurementType: 'weight',
        gender: 'male',
      ),
    ];
    _performAnalysis();
  }

  IconData _getAlertIcon(String severity) {
    switch (severity) {
      case 'critical':
        return Icons.error;
      case 'high':
        return Icons.warning;
      case 'medium':
        return Icons.info;
      case 'low':
      default:
        return Icons.info_outline;
    }
  }

  Color _getAlertColor(String severity) {
    switch (severity) {
      case 'critical':
        return Colors.red;
      case 'high':
        return Colors.orange;
      case 'medium':
        return Colors.amber;
      case 'low':
      default:
        return Colors.blue;
    }
  }
}