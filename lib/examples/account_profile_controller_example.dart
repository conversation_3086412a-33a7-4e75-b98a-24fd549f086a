import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../services/account_profile_controller.dart';
import '../services/auth_service.dart';

/// Example demonstrating how to use the AccountProfileController
class AccountProfileControllerExample extends StatelessWidget {
  const AccountProfileControllerExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Profile Controller Example'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              context.read<AccountProfileController>().refresh();
            },
          ),
        ],
      ),
      body: Consumer<AccountProfileController>(
        builder: (context, controller, child) {
          if (controller.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          if (controller.hasErrors) {
            return _buildErrorView(context, controller);
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProfileSection(context, controller),
                const SizedBox(height: 24),
                _buildFamilySection(context, controller),
                const SizedBox(height: 24),
                _buildSubscriptionSection(context, controller),
                const SizedBox(height: 24),
                _buildActionsSection(context, controller),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildErrorView(BuildContext context, AccountProfileController controller) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Colors.red,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Data',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),
          if (controller.profileError != null)
            Text('Profile: ${controller.profileError}'),
          if (controller.familyError != null)
            Text('Family: ${controller.familyError}'),
          if (controller.subscriptionError != null)
            Text('Subscription: ${controller.subscriptionError}'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              controller.clearErrors();
              controller.refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, AccountProfileController controller) {
    final profile = controller.userProfile;
    final completion = controller.profileCompletionStatus;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Profile Information',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            if (profile != null) ...[
              ListTile(
                leading: CircleAvatar(
                  backgroundImage: profile.avatarUrl != null
                      ? NetworkImage(profile.avatarUrl!)
                      : null,
                  child: profile.avatarUrl == null
                      ? Text(profile.fullName.isNotEmpty
                          ? profile.fullName[0].toUpperCase()
                          : '?')
                      : null,
                ),
                title: Text(profile.fullName),
                subtitle: Text('${profile.email} • ${profile.role}'),
              ),
              if (completion != null) ...[
                const SizedBox(height: 12),
                LinearProgressIndicator(
                  value: completion.percentage / 100,
                ),
                const SizedBox(height: 8),
                Text(
                  '${completion.percentage.toInt()}% Complete - ${completion.nextRecommendedAction}',
                  style: Theme.of(context).textTheme.bodySmall,
                ),
              ],
            ] else
              const Text('No profile data available'),
          ],
        ),
      ),
    );
  }

  Widget _buildFamilySection(BuildContext context, AccountProfileController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Family Members',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                if (controller.canManageFamily)
                  TextButton.icon(
                    onPressed: controller.isInvitingMember
                        ? null
                        : () => _showInviteDialog(context, controller),
                    icon: const Icon(Icons.person_add),
                    label: const Text('Invite'),
                  ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              '${controller.activeFamilyMembersCount} active members, '
              '${controller.pendingInvitationsCount} pending invitations',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            if (controller.familyMembers.isNotEmpty)
              ...controller.familyMembers.map((member) => ListTile(
                    leading: CircleAvatar(
                      backgroundImage: member.avatarUrl != null
                          ? NetworkImage(member.avatarUrl!)
                          : null,
                      child: member.avatarUrl == null
                          ? Text(member.initials)
                          : null,
                    ),
                    title: Text(member.fullName),
                    subtitle: Text(
                        '${member.roleDisplayName} • ${member.activityStatusText}'),
                    trailing: member.isPending
                        ? const Chip(
                            label: Text('Pending'),
                            backgroundColor: Colors.orange,
                          )
                        : null,
                  ))
            else
              const Text('No family members found'),
          ],
        ),
      ),
    );
  }

  Widget _buildSubscriptionSection(BuildContext context, AccountProfileController controller) {
    final subscription = controller.subscriptionInfo;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Subscription',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            if (subscription != null) ...[
              ListTile(
                leading: Icon(
                  subscription.isPremium ? Icons.star : Icons.star_border,
                  color: subscription.isPremium ? Colors.amber : null,
                ),
                title: Text(subscription.planName),
                subtitle: Text(
                    '${subscription.formattedPrice} • ${subscription.statusMessage}'),
                trailing: subscription.needsAttention
                    ? const Icon(Icons.warning, color: Colors.orange)
                    : null,
              ),
              if (subscription.features.isNotEmpty) ...[
                const SizedBox(height: 8),
                Wrap(
                  spacing: 8,
                  children: subscription.features
                      .take(3)
                      .map((feature) => Chip(
                            label: Text(feature),
                            backgroundColor: Colors.grey[200],
                          ))
                      .toList(),
                ),
              ],
            ] else
              const Text('No subscription information available'),
          ],
        ),
      ),
    );
  }

  Widget _buildActionsSection(BuildContext context, AccountProfileController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              children: [
                ElevatedButton.icon(
                  onPressed: controller.isUpdatingProfile
                      ? null
                      : () => _showEditProfileDialog(context, controller),
                  icon: const Icon(Icons.edit),
                  label: const Text('Edit Profile'),
                ),
                ElevatedButton.icon(
                  onPressed: () => controller.refresh(),
                  icon: const Icon(Icons.refresh),
                  label: const Text('Refresh'),
                ),
                if (controller.hasErrors)
                  ElevatedButton.icon(
                    onPressed: () => controller.clearErrors(),
                    icon: const Icon(Icons.clear),
                    label: const Text('Clear Errors'),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showInviteDialog(BuildContext context, AccountProfileController controller) {
    final emailController = TextEditingController();
    String selectedRole = 'caregiver';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Invite Family Member'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                hintText: 'Enter email address',
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: selectedRole,
              decoration: const InputDecoration(labelText: 'Role'),
              items: const [
                DropdownMenuItem(value: 'parent', child: Text('Parent')),
                DropdownMenuItem(value: 'caregiver', child: Text('Caregiver')),
                DropdownMenuItem(value: 'grandparent', child: Text('Grandparent')),
                DropdownMenuItem(value: 'babysitter', child: Text('Babysitter')),
              ],
              onChanged: (value) => selectedRole = value ?? 'caregiver',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (emailController.text.trim().isNotEmpty) {
                final success = await controller.inviteFamilyMember(
                  email: emailController.text.trim(),
                  role: selectedRole,
                );
                
                if (context.mounted) {
                  Navigator.of(context).pop();
                  
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(success
                          ? 'Invitation sent successfully!'
                          : 'Failed to send invitation'),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Send Invitation'),
          ),
        ],
      ),
    );
  }

  void _showEditProfileDialog(BuildContext context, AccountProfileController controller) {
    final profile = controller.userProfile;
    if (profile == null) return;

    final nameController = TextEditingController(text: profile.fullName);
    final phoneController = TextEditingController(text: profile.phoneNumber ?? '');

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Profile'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: nameController,
              decoration: const InputDecoration(
                labelText: 'Full Name',
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: 'Optional',
              ),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final success = await controller.updateUserProfile(
                fullName: nameController.text.trim(),
                phoneNumber: phoneController.text.trim().isEmpty
                    ? null
                    : phoneController.text.trim(),
              );
              
              if (context.mounted) {
                Navigator.of(context).pop();
                
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(success
                        ? 'Profile updated successfully!'
                        : 'Failed to update profile'),
                    backgroundColor: success ? Colors.green : Colors.red,
                  ),
                );
              }
            },
            child: const Text('Save Changes'),
          ),
        ],
      ),
    );
  }
}

/// Example of how to set up the controller with Provider
class AccountProfileControllerProvider extends StatelessWidget {
  final Widget child;

  const AccountProfileControllerProvider({
    super.key,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AccountProfileController(
        context.read<AuthService>(),
      ),
      child: child,
    );
  }
}