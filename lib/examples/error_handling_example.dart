import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../services/error_handling_service.dart';
import '../widgets/shared/error_display_widget.dart';
import '../widgets/shared/skeleton_loading.dart';

/// Example demonstrating comprehensive error handling and loading states
/// for the account profile redesign implementation
class ErrorHandlingExample extends StatefulWidget {
  const ErrorHandlingExample({super.key});

  @override
  State<ErrorHandlingExample> createState() => _ErrorHandlingExampleState();
}

class _ErrorHandlingExampleState extends State<ErrorHandlingExample> {
  final ErrorHandlingService _errorService = ErrorHandlingService();
  bool _isLoading = false;
  AccountProfileError? _currentError;
  bool _isOnline = true;

  @override
  void initState() {
    super.initState();
    _errorService.initialize();
    
    // Listen to connectivity changes
    _errorService.connectivityStream.listen((isOnline) {
      setState(() {
        _isOnline = isOnline;
      });
    });
  }

  @override
  void dispose() {
    _errorService.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'Error Handling Example',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            useMaterial3: true,
          ),
          home: Scaffold(
            appBar: AppBar(
              title: const Text('Error Handling & Loading States'),
              backgroundColor: Theme.of(context).colorScheme.inversePrimary,
            ),
            body: Column(
              children: [
                // Network status banner
                NetworkStatusBanner(
                  isOnline: _isOnline,
                  onRetry: _simulateNetworkReconnect,
                ),
                
                Expanded(
                  child: SingleChildScrollView(
                    padding: EdgeInsets.all(4.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildControlSection(),
                        SizedBox(height: 4.h),
                        _buildExampleSection(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build control section with buttons to simulate different states
  Widget _buildControlSection() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Simulate States',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Wrap(
              spacing: 2.w,
              runSpacing: 1.h,
              children: [
                ElevatedButton(
                  onPressed: _simulateLoading,
                  child: const Text('Loading'),
                ),
                ElevatedButton(
                  onPressed: _simulateNetworkError,
                  child: const Text('Network Error'),
                ),
                ElevatedButton(
                  onPressed: _simulateAuthError,
                  child: const Text('Auth Error'),
                ),
                ElevatedButton(
                  onPressed: _simulateValidationError,
                  child: const Text('Validation Error'),
                ),
                ElevatedButton(
                  onPressed: _simulateServerError,
                  child: const Text('Server Error'),
                ),
                ElevatedButton(
                  onPressed: _simulateTimeoutError,
                  child: const Text('Timeout Error'),
                ),
                ElevatedButton(
                  onPressed: _clearError,
                  child: const Text('Clear'),
                ),
              ],
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                Text('Network Status: '),
                Switch(
                  value: _isOnline,
                  onChanged: (value) {
                    setState(() {
                      _isOnline = value;
                    });
                  },
                ),
                Text(_isOnline ? 'Online' : 'Offline'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build example section showing different states
  Widget _buildExampleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Components',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 2.h),
        
        // Profile Header Example
        _buildProfileHeaderExample(),
        SizedBox(height: 3.h),
        
        // Family Sharing Example
        _buildFamilySharingExample(),
        SizedBox(height: 3.h),
        
        // Account Management Example
        _buildAccountManagementExample(),
      ],
    );
  }

  /// Build profile header example
  Widget _buildProfileHeaderExample() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Profile Header',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        
        if (_currentError != null && _currentError!.operation == 'loadUserProfile')
          ErrorDisplayWidget(
            error: _currentError!,
            onRetry: _retryOperation,
            onDismiss: _clearError,
          )
        else if (_isLoading)
          const ProfileHeaderSkeleton()
        else
          _buildMockProfileHeader(),
      ],
    );
  }

  /// Build family sharing example
  Widget _buildFamilySharingExample() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Family Sharing',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        
        if (_currentError != null && _currentError!.operation == 'loadFamilyMembers')
          ErrorDisplayWidget(
            error: _currentError!,
            onRetry: _retryOperation,
            onDismiss: _clearError,
            isCompact: true,
          )
        else if (_isLoading)
          const FamilySharingSkeleton()
        else
          _buildMockFamilySharing(),
      ],
    );
  }

  /// Build account management example
  Widget _buildAccountManagementExample() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Management',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: 1.h),
        
        if (_currentError != null && _currentError!.operation == 'loadSubscription')
          ErrorDisplayWidget(
            error: _currentError!,
            onRetry: _retryOperation,
            onDismiss: _clearError,
          )
        else if (_isLoading)
          const AccountManagementSkeleton()
        else
          _buildMockAccountManagement(),
      ],
    );
  }

  /// Build mock profile header
  Widget _buildMockProfileHeader() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Row(
          children: [
            CircleAvatar(
              radius: 8.w,
              backgroundColor: Theme.of(context).colorScheme.primary,
              child: Text(
                'JD',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(width: 4.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'John Doe',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    '<EMAIL>',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7),
                    ),
                  ),
                  SizedBox(height: 1.h),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      'Parent',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build mock family sharing
  Widget _buildMockFamilySharing() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Family Members (3)',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Row(
              children: [
                _buildMemberAvatar('JD', Colors.blue),
                SizedBox(width: 2.w),
                _buildMemberAvatar('SM', Colors.green),
                SizedBox(width: 2.w),
                _buildMemberAvatar('AB', Colors.orange),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build mock account management
  Widget _buildMockAccountManagement() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(5.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Account Management',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            Container(
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.star,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                  SizedBox(width: 2.w),
                  Text(
                    'Premium Plan',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build member avatar
  Widget _buildMemberAvatar(String initials, Color color) {
    return CircleAvatar(
      radius: 6.w,
      backgroundColor: color,
      child: Text(
        initials,
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
          fontSize: 3.w,
        ),
      ),
    );
  }

  /// Simulate loading state
  void _simulateLoading() {
    setState(() {
      _isLoading = true;
      _currentError = null;
    });

    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  /// Simulate network error
  void _simulateNetworkError() {
    setState(() {
      _isLoading = false;
      _currentError = AccountProfileError(
        type: AccountProfileErrorType.networkError,
        title: 'Connection Failed',
        message: 'Unable to connect to the server. Please check your internet connection.',
        isRetryable: true,
        operation: 'loadUserProfile',
      );
    });
  }

  /// Simulate auth error
  void _simulateAuthError() {
    setState(() {
      _isLoading = false;
      _currentError = AccountProfileError(
        type: AccountProfileErrorType.authError,
        title: 'Authentication Required',
        message: 'Please sign in to continue.',
        isRetryable: false,
        operation: 'loadUserProfile',
      );
    });
  }

  /// Simulate validation error
  void _simulateValidationError() {
    setState(() {
      _isLoading = false;
      _currentError = AccountProfileError(
        type: AccountProfileErrorType.validationError,
        title: 'Invalid Data',
        message: 'The email address is already associated with another family member.',
        isRetryable: false,
        operation: 'loadFamilyMembers',
      );
    });
  }

  /// Simulate server error
  void _simulateServerError() {
    setState(() {
      _isLoading = false;
      _currentError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Server Error',
        message: 'The server is temporarily unavailable. Please try again later.',
        isRetryable: true,
        operation: 'loadSubscription',
        retryDelay: const Duration(seconds: 5),
      );
    });
  }

  /// Simulate timeout error
  void _simulateTimeoutError() {
    setState(() {
      _isLoading = false;
      _currentError = AccountProfileError(
        type: AccountProfileErrorType.timeoutError,
        title: 'Request Timeout',
        message: 'The request took too long to complete. Please try again.',
        isRetryable: true,
        operation: 'loadFamilyMembers',
        retryDelay: const Duration(seconds: 3),
      );
    });
  }

  /// Simulate network reconnect
  void _simulateNetworkReconnect() {
    setState(() {
      _isOnline = true;
    });
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Connection restored'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// Retry operation
  void _retryOperation() {
    _simulateLoading();
  }

  /// Clear error
  void _clearError() {
    setState(() {
      _currentError = null;
    });
  }
}