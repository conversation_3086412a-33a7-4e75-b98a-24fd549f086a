import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../services/account_profile_controller.dart';
import '../services/auth_service.dart';
import '../widgets/user_profile_account_section.dart';

/// Example demonstrating the enhanced UserProfileAccountSection widget
/// 
/// This example shows:
/// - How to integrate the widget with AccountProfileController
/// - Different layout variants (full, compact, settings)
/// - Proper callback handling
/// - Responsive design considerations
class UserProfileAccountSectionExample extends StatefulWidget {
  const UserProfileAccountSectionExample({super.key});

  @override
  State<UserProfileAccountSectionExample> createState() => _UserProfileAccountSectionExampleState();
}

class _UserProfileAccountSectionExampleState extends State<UserProfileAccountSectionExample> {
  int _selectedVariant = 0;
  
  final List<String> _variants = [
    'Full Layout',
    'Compact Layout', 
    'Settings Layout',
  ];

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'UserProfileAccountSection Example',
          theme: ThemeData(
            colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
            useMaterial3: true,
          ),
          home: MultiProvider(
            providers: [
              ChangeNotifierProvider<AuthService>(
                create: (_) => AuthService(),
              ),
              ChangeNotifierProxyProvider<AuthService, AccountProfileController>(
                create: (context) => AccountProfileController(
                  Provider.of<AuthService>(context, listen: false),
                ),
                update: (context, authService, previous) =>
                    previous ?? AccountProfileController(authService),
              ),
            ],
            child: Scaffold(
              appBar: AppBar(
                title: const Text('Account Profile Section'),
                backgroundColor: Theme.of(context).colorScheme.inversePrimary,
                actions: [
                  PopupMenuButton<int>(
                    onSelected: (value) => setState(() => _selectedVariant = value),
                    itemBuilder: (context) => _variants.asMap().entries.map((entry) {
                      return PopupMenuItem<int>(
                        value: entry.key,
                        child: Row(
                          children: [
                            if (_selectedVariant == entry.key)
                              const Icon(Icons.check, size: 16),
                            if (_selectedVariant == entry.key)
                              const SizedBox(width: 8),
                            Text(entry.value),
                          ],
                        ),
                      );
                    }).toList(),
                    child: const Padding(
                      padding: EdgeInsets.all(16.0),
                      child: Icon(Icons.more_vert),
                    ),
                  ),
                ],
              ),
              body: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Variant selector
                    Container(
                      width: double.infinity,
                      padding: EdgeInsets.all(4.w),
                      color: Theme.of(context).colorScheme.surfaceContainerHighest,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Current Variant: ${_variants[_selectedVariant]}',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(height: 1.h),
                          Text(
                            _getVariantDescription(),
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Theme.of(context).colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ],
                      ),
                    ),
                    
                    // Main content
                    _buildSelectedVariant(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build the selected variant
  Widget _buildSelectedVariant() {
    switch (_selectedVariant) {
      case 0:
        return _buildFullLayoutExample();
      case 1:
        return _buildCompactLayoutExample();
      case 2:
        return _buildSettingsLayoutExample();
      default:
        return _buildFullLayoutExample();
    }
  }

  /// Build full layout example
  Widget _buildFullLayoutExample() {
    return UserProfileAccountSectionVariants.full(
      onEditProfile: _handleEditProfile,
      onNavigateToUserManagement: _handleNavigateToUserManagement,
      onPromoteToAdmin: _handlePromoteToAdmin,
      onSubscriptionTap: _handleSubscriptionTap,
      onFamilySharingTap: _handleFamilySharingTap,
      onAvatarTap: _handleAvatarTap,
      sectionTitle: 'Account & Profile Management',
    );
  }

  /// Build compact layout example
  Widget _buildCompactLayoutExample() {
    return Column(
      children: [
        Container(
          padding: EdgeInsets.all(4.w),
          child: Text(
            'Compact Layout Example',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        UserProfileAccountSectionVariants.compact(
          onEditProfile: _handleEditProfile,
          onNavigateToUserManagement: _handleNavigateToUserManagement,
          onSubscriptionTap: _handleSubscriptionTap,
          onFamilySharingTap: _handleFamilySharingTap,
          onAvatarTap: _handleAvatarTap,
        ),
      ],
    );
  }

  /// Build settings layout example
  Widget _buildSettingsLayoutExample() {
    return UserProfileAccountSectionVariants.forSettings(
      onEditProfile: _handleEditProfile,
      onNavigateToUserManagement: _handleNavigateToUserManagement,
      onSubscriptionTap: _handleSubscriptionTap,
      onFamilySharingTap: _handleFamilySharingTap,
    );
  }

  /// Get description for current variant
  String _getVariantDescription() {
    switch (_selectedVariant) {
      case 0:
        return 'Full layout with all components, headers, and detailed information. Best for dedicated profile screens.';
      case 1:
        return 'Compact layout optimized for smaller screens or embedded contexts. Shows essential information with quick actions.';
      case 2:
        return 'Settings-optimized layout designed for integration within settings screens. Balanced information display.';
      default:
        return '';
    }
  }

  /// Handle edit profile action
  void _handleEditProfile() {
    _showSnackBar('Edit Profile tapped');
    // In a real app, this would navigate to profile editing screen
    // Navigator.of(context).pushNamed('/edit-profile');
  }

  /// Handle navigate to user management
  void _handleNavigateToUserManagement() {
    _showSnackBar('Navigate to User Management tapped');
    // In a real app, this would navigate to user management screen
    // Navigator.of(context).pushNamed('/user-management');
  }

  /// Handle promote to admin action
  void _handlePromoteToAdmin() {
    _showSnackBar('Promote to Admin tapped');
    // In a real app, this would show promotion dialog or process
  }

  /// Handle subscription management
  void _handleSubscriptionTap() {
    _showSnackBar('Subscription Management tapped');
    // In a real app, this would navigate to subscription screen
    // Navigator.of(context).pushNamed('/subscription');
  }

  /// Handle family sharing action
  void _handleFamilySharingTap() {
    _showSnackBar('Family Sharing tapped');
    // In a real app, this would show family invitation dialog
    // _showFamilyInvitationDialog();
  }

  /// Handle avatar tap for photo upload
  void _handleAvatarTap() {
    _showSnackBar('Avatar tapped - Photo upload');
    // In a real app, this would show image picker
    // _showImagePicker();
  }

  /// Show snackbar with message
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

/// Example of how to integrate with a custom settings screen
class SettingsScreenIntegrationExample extends StatelessWidget {
  const SettingsScreenIntegrationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Settings'),
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            // Account & Profile Section
            UserProfileAccountSectionVariants.forSettings(
              onEditProfile: () {
                // Navigate to profile editing
              },
              onNavigateToUserManagement: () {
                // Navigate to user management
              },
              onSubscriptionTap: () {
                // Navigate to subscription management
              },
              onFamilySharingTap: () {
                // Show family sharing options
              },
            ),
            
            SizedBox(height: 3.h),
            
            // Other settings sections would go here
            _buildOtherSettingsSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildOtherSettingsSection(BuildContext context) {
    return Card(
      margin: EdgeInsets.symmetric(horizontal: 4.w),
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'App Settings',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            ListTile(
              leading: const Icon(Icons.notifications_outlined),
              title: const Text('Notifications'),
              subtitle: const Text('Manage notification preferences'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to notifications settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.privacy_tip_outlined),
              title: const Text('Privacy'),
              subtitle: const Text('Data and privacy settings'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to privacy settings
              },
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('Help & Support'),
              subtitle: const Text('Get help and contact support'),
              trailing: const Icon(Icons.chevron_right),
              onTap: () {
                // Navigate to help screen
              },
            ),
          ],
        ),
      ),
    );
  }
}

/// Example of responsive usage based on screen size
class ResponsiveUserProfileExample extends StatelessWidget {
  const ResponsiveUserProfileExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        // Use compact layout for small screens or landscape orientation
        final useCompactLayout = deviceType == DeviceType.mobile && 
                                orientation == Orientation.landscape;
        
        return Scaffold(
          appBar: AppBar(
            title: const Text('Responsive Profile'),
          ),
          body: SingleChildScrollView(
            child: useCompactLayout
                ? UserProfileAccountSectionVariants.compact(
                    onEditProfile: () {},
                    onNavigateToUserManagement: () {},
                    onSubscriptionTap: () {},
                    onFamilySharingTap: () {},
                  )
                : UserProfileAccountSectionVariants.full(
                    onEditProfile: () {},
                    onNavigateToUserManagement: () {},
                    onSubscriptionTap: () {},
                    onFamilySharingTap: () {},
                  ),
          ),
        );
      },
    );
  }
}