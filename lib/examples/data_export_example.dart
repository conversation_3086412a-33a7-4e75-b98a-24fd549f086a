import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';
import '../models/baby_profile.dart';
import '../models/measurement.dart';
import '../services/data_export_service.dart';

/// Example demonstrating comprehensive data export functionality
class DataExportExample extends StatefulWidget {
  const DataExportExample({super.key});

  @override
  State<DataExportExample> createState() => _DataExportExampleState();
}

class _DataExportExampleState extends State<DataExportExample> {
  bool _isExporting = false;
  String _exportStatus = '';
  
  // Sample data for demonstration
  late BabyProfile _sampleBaby;
  late List<Measurement> _sampleMeasurements;

  @override
  void initState() {
    super.initState();
    _initializeSampleData();
  }

  void _initializeSampleData() {
    // Create sample baby profile
    _sampleBaby = BabyProfile(
      id: 'sample-baby-1',
      name: '<PERSON>',
      birthDate: DateTime.now().subtract(const Duration(days: 365)), // 1 year old
      gender: 'female',
      birthWeight: 3.2,
      birthHeight: 50.0,
      allergies: ['Peanuts'],
      medications: [],
      healthNotes: 'Born at 39 weeks, healthy delivery',
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now(),
    );

    // Create sample measurements with realistic growth progression
    _sampleMeasurements = [
      // Birth measurements
      _createMeasurement('weight', 3.2, 'kg', 0, 0.0),
      _createMeasurement('height', 50.0, 'cm', 0, 0.0),
      _createMeasurement('head_circumference', 35.0, 'cm', 0, 0.0),
      
      // 2 months
      _createMeasurement('weight', 5.1, 'kg', 60, 2.0),
      _createMeasurement('height', 57.5, 'cm', 60, 2.0),
      _createMeasurement('head_circumference', 38.5, 'cm', 60, 2.0),
      
      // 4 months
      _createMeasurement('weight', 6.8, 'kg', 120, 4.0),
      _createMeasurement('height', 63.0, 'cm', 120, 4.0),
      _createMeasurement('head_circumference', 41.0, 'cm', 120, 4.0),
      
      // 6 months
      _createMeasurement('weight', 7.9, 'kg', 180, 6.0),
      _createMeasurement('height', 67.5, 'cm', 180, 6.0),
      _createMeasurement('head_circumference', 43.0, 'cm', 180, 6.0),
      
      // 9 months
      _createMeasurement('weight', 8.8, 'kg', 270, 9.0),
      _createMeasurement('height', 71.0, 'cm', 270, 9.0),
      _createMeasurement('head_circumference', 44.5, 'cm', 270, 9.0),
      
      // 12 months (current)
      _createMeasurement('weight', 9.5, 'kg', 365, 12.0),
      _createMeasurement('height', 74.0, 'cm', 365, 12.0),
      _createMeasurement('head_circumference', 45.5, 'cm', 365, 12.0),
    ];
  }

  Measurement _createMeasurement(String type, double value, String unit, int daysAgo, double ageInMonths) {
    final measuredAt = DateTime.now().subtract(Duration(days: daysAgo));
    
    // Calculate realistic percentiles based on WHO standards
    double percentile;
    double zScore;
    
    switch (type) {
      case 'weight':
        percentile = _calculateRealisticPercentile(value, [3.2, 5.1, 6.8, 7.9, 8.8, 9.5], ageInMonths);
        break;
      case 'height':
        percentile = _calculateRealisticPercentile(value, [50.0, 57.5, 63.0, 67.5, 71.0, 74.0], ageInMonths);
        break;
      case 'head_circumference':
        percentile = _calculateRealisticPercentile(value, [35.0, 38.5, 41.0, 43.0, 44.5, 45.5], ageInMonths);
        break;
      default:
        percentile = 50.0;
    }
    
    zScore = (percentile - 50.0) / 15.0; // Approximate z-score conversion
    
    return Measurement(
      id: 'measurement-${type}-${daysAgo}',
      babyId: _sampleBaby.id,
      measurementType: type,
      value: value,
      unit: unit,
      measuredAt: measuredAt,
      ageInMonths: ageInMonths,
      percentile: percentile,
      zScore: zScore,
      percentileAnalysis: null,
      flaggedForReview: percentile < 5.0 || percentile > 95.0,
      createdAt: measuredAt,
      updatedAt: measuredAt,
    );
  }

  double _calculateRealisticPercentile(double value, List<double> expectedValues, double ageInMonths) {
    // Simple percentile calculation for demo purposes
    // In real implementation, this would use WHO LMS data
    final ageIndex = (ageInMonths / 2).floor().clamp(0, expectedValues.length - 1);
    final expectedValue = expectedValues[ageIndex];
    final deviation = (value - expectedValue) / expectedValue;
    
    // Convert deviation to percentile (simplified)
    double percentile = 50.0 + (deviation * 30.0);
    return percentile.clamp(1.0, 99.0);
  }

  String _getPercentileInterpretation(double percentile) {
    if (percentile < 10.0) return 'Below Average';
    if (percentile < 25.0) return 'Lower Average';
    if (percentile <= 75.0) return 'Average';
    if (percentile <= 90.0) return 'Higher Average';
    return 'Above Average';
  }

  String _getPercentileCategory(double percentile) {
    if (percentile < 3.0) return 'Below Normal';
    if (percentile < 10.0) return 'Low Normal';
    if (percentile <= 90.0) return 'Normal';
    if (percentile <= 97.0) return 'High Normal';
    return 'Above Normal';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Data Export Example'),
        backgroundColor: Colors.blue.shade100,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Sample data info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Sample Data',
                      style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text('Baby: ${_sampleBaby.name}'),
                    Text('Age: ${_sampleBaby.ageInMonths} months'),
                    Text('Gender: ${_sampleBaby.gender}'),
                    Text('Measurements: ${_sampleMeasurements.length}'),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Export options
            Text(
              'Export Options',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // PDF Export
            ElevatedButton.icon(
              onPressed: _isExporting ? null : () => _exportPDF(),
              icon: const Icon(Icons.picture_as_pdf),
              label: const Text('Generate PDF Report'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red.shade100,
                foregroundColor: Colors.red.shade800,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Medical Format Export
            ElevatedButton.icon(
              onPressed: _isExporting ? null : () => _exportMedicalFormat(),
              icon: const Icon(Icons.local_hospital),
              label: const Text('Medical Standard Format'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green.shade100,
                foregroundColor: Colors.green.shade800,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // CSV Export
            ElevatedButton.icon(
              onPressed: _isExporting ? null : () => _exportCSV(),
              icon: const Icon(Icons.table_chart),
              label: const Text('Export as CSV'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange.shade100,
                foregroundColor: Colors.orange.shade800,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // JSON Export
            ElevatedButton.icon(
              onPressed: _isExporting ? null : () => _exportJSON(),
              icon: const Icon(Icons.code),
              label: const Text('Export as JSON'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple.shade100,
                foregroundColor: Colors.purple.shade800,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            // Data Validation
            ElevatedButton.icon(
              onPressed: _isExporting ? null : () => _validateData(),
              icon: const Icon(Icons.check_circle),
              label: const Text('Validate Export Data'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue.shade100,
                foregroundColor: Colors.blue.shade800,
                padding: const EdgeInsets.all(16),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // Status display
            if (_exportStatus.isNotEmpty) ...[
              Card(
                color: Colors.grey.shade50,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Export Status',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(_exportStatus),
                    ],
                  ),
                ),
              ),
            ],
            
            // Loading indicator
            if (_isExporting) ...[
              const SizedBox(height: 20),
              const Center(
                child: CircularProgressIndicator(),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Future<void> _exportPDF() async {
    setState(() {
      _isExporting = true;
      _exportStatus = 'Generating PDF report...';
    });

    try {
      final pdfBytes = await DataExportService.generatePDFReport(
        measurements: _sampleMeasurements,
        baby: _sampleBaby,
        includeCharts: true,
        includeAnalysis: true,
      );

      // Save to device
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${_sampleBaby.name}_growth_report.pdf');
      await file.writeAsBytes(pdfBytes);

      setState(() {
        _exportStatus = 'PDF report generated successfully!\n'
            'File saved to: ${file.path}\n'
            'Size: ${(pdfBytes.length / 1024).toStringAsFixed(1)} KB';
      });
    } catch (e) {
      setState(() {
        _exportStatus = 'PDF generation failed: $e';
      });
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _exportMedicalFormat() async {
    setState(() {
      _isExporting = true;
      _exportStatus = 'Generating medical format...';
    });

    try {
      final medicalData = await DataExportService.formatForHealthcareProvider(
        measurements: _sampleMeasurements,
        baby: _sampleBaby,
        format: 'medical_standard',
      );

      // Save to device
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${_sampleBaby.name}_medical_report.txt');
      await file.writeAsString(medicalData);

      setState(() {
        _exportStatus = 'Medical format generated successfully!\n'
            'File saved to: ${file.path}\n'
            'Size: ${(medicalData.length / 1024).toStringAsFixed(1)} KB\n\n'
            'Preview:\n${medicalData.substring(0, 200)}...';
      });
    } catch (e) {
      setState(() {
        _exportStatus = 'Medical format generation failed: $e';
      });
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _exportCSV() async {
    setState(() {
      _isExporting = true;
      _exportStatus = 'Generating CSV export...';
    });

    try {
      final csvData = await DataExportService.formatForHealthcareProvider(
        measurements: _sampleMeasurements,
        baby: _sampleBaby,
        format: 'csv',
      );

      // Save to device
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${_sampleBaby.name}_growth_data.csv');
      await file.writeAsString(csvData);

      setState(() {
        _exportStatus = 'CSV export generated successfully!\n'
            'File saved to: ${file.path}\n'
            'Rows: ${csvData.split('\n').length - 1}\n\n'
            'Preview:\n${csvData.split('\n').take(5).join('\n')}';
      });
    } catch (e) {
      setState(() {
        _exportStatus = 'CSV export failed: $e';
      });
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _exportJSON() async {
    setState(() {
      _isExporting = true;
      _exportStatus = 'Generating JSON export...';
    });

    try {
      final jsonData = await DataExportService.formatForHealthcareProvider(
        measurements: _sampleMeasurements,
        baby: _sampleBaby,
        format: 'json',
      );

      // Save to device
      final directory = await getApplicationDocumentsDirectory();
      final file = File('${directory.path}/${_sampleBaby.name}_growth_data.json');
      await file.writeAsString(jsonData);

      setState(() {
        _exportStatus = 'JSON export generated successfully!\n'
            'File saved to: ${file.path}\n'
            'Size: ${(jsonData.length / 1024).toStringAsFixed(1)} KB\n\n'
            'Preview:\n${jsonData.substring(0, 300)}...';
      });
    } catch (e) {
      setState(() {
        _exportStatus = 'JSON export failed: $e';
      });
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  Future<void> _validateData() async {
    setState(() {
      _isExporting = true;
      _exportStatus = 'Validating export data...';
    });

    try {
      final validationResult = DataExportService.validateExportData(
        _sampleMeasurements,
        _sampleBaby,
      );

      final buffer = StringBuffer();
      buffer.writeln('Data Validation Results:');
      buffer.writeln('Valid: ${validationResult['isValid']}');
      buffer.writeln('Measurement Count: ${validationResult['measurementCount']}');
      buffer.writeln('Baby Age: ${validationResult['babyAge']} months');
      
      if (validationResult['errors'].isNotEmpty) {
        buffer.writeln('\nErrors:');
        for (final error in validationResult['errors']) {
          buffer.writeln('- $error');
        }
      }
      
      if (validationResult['warnings'].isNotEmpty) {
        buffer.writeln('\nWarnings:');
        for (final warning in validationResult['warnings']) {
          buffer.writeln('- $warning');
        }
      }

      setState(() {
        _exportStatus = buffer.toString();
      });
    } catch (e) {
      setState(() {
        _exportStatus = 'Data validation failed: $e';
      });
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }
}