import 'package:flutter/material.dart';

import '../widgets/user_avatar_widget.dart';

/// Example usage of UserAvatarWidget with different configurations
class UserAvatarExample extends StatefulWidget {
  const UserAvatarExample({super.key});

  @override
  State<UserAvatarExample> createState() => _UserAvatarExampleState();
}

class _UserAvatarExampleState extends State<UserAvatarExample> {
  String? _uploadedImageUrl;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('User Avatar Examples'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Different Roles',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Role examples
            Wrap(
              spacing: 16,
              runSpacing: 16,
              children: [
                _buildRoleExample('Admin', 'admin', 'AD'),
                _buildRoleExample('Parent', 'parent', 'PA'),
                _buildRoleExample('Caregiver', 'caregiver', 'CG'),
                _buildRoleExample('Grandparent', 'grandparent', 'GP'),
                _buildRoleExample('Babysitter', 'babysitter', 'BS'),
              ],
            ),
            
            const SizedBox(height: 32),
            const Text(
              'Different Sizes',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Size examples
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Column(
                  children: [
                    UserAvatarWidget.small(
                      initials: 'SM',
                      role: 'parent',
                    ),
                    const SizedBox(height: 8),
                    const Text('Small'),
                  ],
                ),
                Column(
                  children: [
                    UserAvatarWidget.medium(
                      initials: 'MD',
                      role: 'parent',
                    ),
                    const SizedBox(height: 8),
                    const Text('Medium'),
                  ],
                ),
                Column(
                  children: [
                    UserAvatarWidget.large(
                      initials: 'LG',
                      role: 'parent',
                    ),
                    const SizedBox(height: 8),
                    const Text('Large'),
                  ],
                ),
              ],
            ),
            
            const SizedBox(height: 32),
            const Text(
              'Editable Avatar',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            
            // Editable example
            Center(
              child: UserAvatarWidget.large(
                imageUrl: _uploadedImageUrl,
                initials: 'ED',
                role: 'parent',
                isEditable: true,
                isLoading: _isLoading,
                onImageUploaded: (imageUrl) {
                  setState(() {
                    _uploadedImageUrl = imageUrl;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Image uploaded successfully!')),
                  );
                },
                onImageUploadError: (error) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Upload failed: $error')),
                  );
                },
                onImageUpload: () async {
                  setState(() {
                    _isLoading = true;
                  });
                  
                  // Simulate upload delay
                  await Future.delayed(const Duration(seconds: 2));
                  
                  setState(() {
                    _isLoading = false;
                  });
                  
                  // Return a mock image URL
                  return 'https://example.com/avatar.jpg';
                },
              ),
            ),
            
            const SizedBox(height: 16),
            const Center(
              child: Text(
                'Tap the avatar above to upload an image',
                style: TextStyle(color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRoleExample(String title, String role, String initials) {
    return Column(
      children: [
        UserAvatarWidget(
          initials: initials,
          role: role,
          size: 60,
        ),
        const SizedBox(height: 8),
        Text(title, style: const TextStyle(fontSize: 12)),
      ],
    );
  }
}