import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../models/measurement.dart';
import '../presentation/growth_charts/widgets/enhanced_growth_chart_renderer.dart';

/// Example demonstrating the comprehensive chart interaction handling system
class ChartInteractionExample extends StatefulWidget {
  const ChartInteractionExample({super.key});

  @override
  State<ChartInteractionExample> createState() => _ChartInteractionExampleState();
}

class _ChartInteractionExampleState extends State<ChartInteractionExample> {
  late List<Measurement> _measurements;
  String _selectedMeasurementType = 'weight';
  String _selectedGender = 'male';
  bool _isMetric = true;
  String _selectedDateRange = '1 year';
  
  String _interactionLog = '';

  @override
  void initState() {
    super.initState();
    _generateSampleMeasurements();
  }

  void _generateSampleMeasurements() {
    final now = DateTime.now();
    final birthDate = now.subtract(const Duration(days: 365)); // 1 year old baby
    
    _measurements = [
      // Birth measurements
      Measurement(
        id: '1',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 3.2,
        unit: 'kg',
        measuredAt: birthDate,
        ageInMonths: 0.0,
        percentile: 45.0,
        zScore: -0.2,
        createdAt: birthDate,
        updatedAt: birthDate,
      ),
      
      // 1 month
      Measurement(
        id: '2',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 4.1,
        unit: 'kg',
        measuredAt: birthDate.add(const Duration(days: 30)),
        ageInMonths: 1.0,
        percentile: 50.0,
        zScore: 0.0,
        createdAt: birthDate.add(const Duration(days: 30)),
        updatedAt: birthDate.add(const Duration(days: 30)),
      ),
      
      // 2 months
      Measurement(
        id: '3',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 5.2,
        unit: 'kg',
        measuredAt: birthDate.add(const Duration(days: 60)),
        ageInMonths: 2.0,
        percentile: 55.0,
        zScore: 0.1,
        createdAt: birthDate.add(const Duration(days: 60)),
        updatedAt: birthDate.add(const Duration(days: 60)),
      ),
      
      // 3 months
      Measurement(
        id: '4',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 6.0,
        unit: 'kg',
        measuredAt: birthDate.add(const Duration(days: 90)),
        ageInMonths: 3.0,
        percentile: 60.0,
        zScore: 0.25,
        createdAt: birthDate.add(const Duration(days: 90)),
        updatedAt: birthDate.add(const Duration(days: 90)),
      ),
      
      // 6 months
      Measurement(
        id: '5',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 7.8,
        unit: 'kg',
        measuredAt: birthDate.add(const Duration(days: 180)),
        ageInMonths: 6.0,
        percentile: 65.0,
        zScore: 0.4,
        createdAt: birthDate.add(const Duration(days: 180)),
        updatedAt: birthDate.add(const Duration(days: 180)),
      ),
      
      // 9 months
      Measurement(
        id: '6',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 9.1,
        unit: 'kg',
        measuredAt: birthDate.add(const Duration(days: 270)),
        ageInMonths: 9.0,
        percentile: 70.0,
        zScore: 0.5,
        createdAt: birthDate.add(const Duration(days: 270)),
        updatedAt: birthDate.add(const Duration(days: 270)),
      ),
      
      // 12 months
      Measurement(
        id: '7',
        babyId: 'sample_baby',
        measurementType: 'weight',
        value: 10.2,
        unit: 'kg',
        measuredAt: birthDate.add(const Duration(days: 365)),
        ageInMonths: 12.0,
        percentile: 75.0,
        zScore: 0.67,
        createdAt: birthDate.add(const Duration(days: 365)),
        updatedAt: birthDate.add(const Duration(days: 365)),
      ),
    ];
  }

  void _logInteraction(String interaction) {
    setState(() {
      _interactionLog = '$interaction\n$_interactionLog';
      // Keep only last 10 interactions
      final lines = _interactionLog.split('\n');
      if (lines.length > 10) {
        _interactionLog = lines.take(10).join('\n');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chart Interaction Demo'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // Controls
          Container(
            padding: EdgeInsets.all(4.w),
            color: Theme.of(context).colorScheme.surface,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Chart Interaction Features:',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: 2.h),
                Wrap(
                  spacing: 2.w,
                  runSpacing: 1.h,
                  children: [
                    _buildFeatureChip('Tap measurement points for details'),
                    _buildFeatureChip('Long press to edit measurements'),
                    _buildFeatureChip('Hover over percentile curves'),
                    _buildFeatureChip('Pinch to zoom and pan'),
                    _buildFeatureChip('Double tap to reset zoom'),
                    _buildFeatureChip('Smooth animations'),
                  ],
                ),
                SizedBox(height: 2.h),
                Row(
                  children: [
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedMeasurementType,
                        decoration: const InputDecoration(
                          labelText: 'Measurement Type',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'weight', child: Text('Weight')),
                          DropdownMenuItem(value: 'height', child: Text('Height')),
                          DropdownMenuItem(value: 'head_circumference', child: Text('Head Circumference')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedMeasurementType = value;
                            });
                          }
                        },
                      ),
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedGender,
                        decoration: const InputDecoration(
                          labelText: 'Gender',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: 'male', child: Text('Male')),
                          DropdownMenuItem(value: 'female', child: Text('Female')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedGender = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 1.h),
                Row(
                  children: [
                    Expanded(
                      child: SwitchListTile(
                        title: const Text('Metric Units'),
                        value: _isMetric,
                        onChanged: (value) {
                          setState(() {
                            _isMetric = value;
                          });
                        },
                      ),
                    ),
                    Expanded(
                      child: DropdownButtonFormField<String>(
                        value: _selectedDateRange,
                        decoration: const InputDecoration(
                          labelText: 'Date Range',
                          border: OutlineInputBorder(),
                        ),
                        items: const [
                          DropdownMenuItem(value: '6 months', child: Text('6 months')),
                          DropdownMenuItem(value: '1 year', child: Text('1 year')),
                          DropdownMenuItem(value: '2 years', child: Text('2 years')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _selectedDateRange = value;
                            });
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          // Chart
          Expanded(
            flex: 3,
            child: Padding(
              padding: EdgeInsets.all(4.w),
              child: EnhancedGrowthChartRenderer(
                measurements: _measurements,
                measurementType: _selectedMeasurementType,
                gender: _selectedGender,
                isMetric: _isMetric,
                dateRange: _selectedDateRange,
                birthDate: DateTime.now().subtract(const Duration(days: 365)),
                onMeasurementTap: (measurement) {
                  _logInteraction(
                    'Tapped measurement: ${measurement.displayValue} at ${measurement.ageInMonths.toStringAsFixed(1)} months (${measurement.percentileDisplay})'
                  );
                },
                onMeasurementEdit: (measurement) {
                  _logInteraction(
                    'Edit requested for measurement: ${measurement.displayValue}'
                  );
                  _showEditDialog(measurement);
                },
                onPercentileCurveHover: (percentile, age) {
                  _logInteraction(
                    'Hovered over $percentile percentile curve at ${age.toStringAsFixed(1)} months'
                  );
                },
              ),
            ),
          ),
          
          // Interaction Log
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.all(4.w),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceVariant,
                border: Border(
                  top: BorderSide(
                    color: Theme.of(context).dividerColor,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        'Interaction Log',
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const Spacer(),
                      TextButton(
                        onPressed: () {
                          setState(() {
                            _interactionLog = '';
                          });
                        },
                        child: const Text('Clear'),
                      ),
                    ],
                  ),
                  SizedBox(height: 1.h),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Text(
                        _interactionLog.isEmpty 
                            ? 'Interact with the chart above to see logs here...'
                            : _interactionLog,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontFamily: 'monospace',
                          color: _interactionLog.isEmpty 
                              ? Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.6)
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFeatureChip(String label) {
    return Chip(
      label: Text(
        label,
        style: Theme.of(context).textTheme.labelSmall,
      ),
      backgroundColor: Theme.of(context).colorScheme.primaryContainer,
      side: BorderSide.none,
    );
  }

  void _showEditDialog(Measurement measurement) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Edit Measurement'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Value: ${measurement.displayValue}'),
            Text('Date: ${measurement.measuredAt.toString().split(' ')[0]}'),
            Text('Age: ${measurement.ageInMonths.toStringAsFixed(1)} months'),
            if (measurement.percentile != null)
              Text('Percentile: ${measurement.percentileDisplay}'),
            SizedBox(height: 2.h),
            const Text(
              'This is a demo - editing functionality would be implemented here.',
              style: TextStyle(fontStyle: FontStyle.italic),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _logInteraction('Measurement edit dialog closed');
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }
}