import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../widgets/account_management_card.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';
import '../theme/app_theme.dart';

/// Example screen demonstrating the AccountManagementCard widget
/// with different subscription states and configurations.
class AccountManagementCardExample extends StatefulWidget {
  const AccountManagementCardExample({super.key});

  @override
  State<AccountManagementCardExample> createState() => _AccountManagementCardExampleState();
}

class _AccountManagementCardExampleState extends State<AccountManagementCardExample> {
  int _selectedExample = 0;
  bool _isLoading = false;

  final List<String> _exampleTitles = [
    'Free Plan',
    'Premium Active',
    'Trial Ending Soon',
    'Expired Subscription',
    'Cancelled Subscription',
    'Loading State',
  ];

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'Account Management Card Example',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          home: Scaffold(
            appBar: AppBar(
              title: const Text('Account Management Card'),
              actions: [
                PopupMenuButton<int>(
                  onSelected: (value) {
                    setState(() {
                      _selectedExample = value;
                      if (value == 5) {
                        _isLoading = true;
                        // Simulate loading
                        Future.delayed(const Duration(seconds: 2), () {
                          if (mounted) {
                            setState(() {
                              _isLoading = false;
                              _selectedExample = 0;
                            });
                          }
                        });
                      }
                    });
                  },
                  itemBuilder: (context) => _exampleTitles
                      .asMap()
                      .entries
                      .map((entry) => PopupMenuItem<int>(
                            value: entry.key,
                            child: Text(entry.value),
                          ))
                      .toList(),
                  child: const Icon(Icons.more_vert),
                ),
              ],
            ),
            body: SingleChildScrollView(
              padding: EdgeInsets.all(4.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Current Example: ${_exampleTitles[_selectedExample]}',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 2.h),
                  _buildCurrentExample(),
                  SizedBox(height: 4.h),
                  _buildExampleDescription(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCurrentExample() {
    switch (_selectedExample) {
      case 0:
        return _buildFreeExample();
      case 1:
        return _buildPremiumExample();
      case 2:
        return _buildTrialExample();
      case 3:
        return _buildExpiredExample();
      case 4:
        return _buildCancelledExample();
      case 5:
        return _buildLoadingExample();
      default:
        return _buildFreeExample();
    }
  }

  Widget _buildFreeExample() {
    return AccountManagementCard(
      subscription: SubscriptionPlans.free,
      hasAdminPrivileges: false,
      isEmailVerified: true,
      isTwoFactorEnabled: false,
      activeSessions: 1,
      onManageSubscription: () => _showSnackBar('Manage Subscription tapped'),
      onSecuritySettings: () => _showSnackBar('Security Settings tapped'),
      onAccountPreferences: () => _showSnackBar('Account Preferences tapped'),
      onUpgrade: () => _showSnackBar('Upgrade tapped'),
      isLoading: _isLoading,
    );
  }

  Widget _buildPremiumExample() {
    final premiumSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.active,
      renewalDate: DateTime.now().add(const Duration(days: 15)),
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
        'Advanced charts and analytics',
        'Data export',
        'Premium support',
        'Unlimited storage',
      ],
      isTrialActive: false,
      paymentMethod: const PaymentMethod(
        type: 'card',
        last4: '4242',
        brand: 'visa',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault: true,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now(),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: premiumSubscription,
      hasAdminPrivileges: true,
      isEmailVerified: true,
      isTwoFactorEnabled: true,
      activeSessions: 1,
      onManageSubscription: () => _showSnackBar('Manage Subscription tapped'),
      onSecuritySettings: () => _showSnackBar('Security Settings tapped'),
      onAccountPreferences: () => _showSnackBar('Account Preferences tapped'),
      isLoading: _isLoading,
    );
  }

  Widget _buildTrialExample() {
    final trialSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium Trial',
      status: SubscriptionStatus.trial,
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
        'Advanced charts and analytics',
      ],
      isTrialActive: true,
      trialEndsAt: DateTime.now().add(const Duration(days: 2)),
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now(),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: trialSubscription,
      hasAdminPrivileges: false,
      isEmailVerified: true,
      isTwoFactorEnabled: false,
      activeSessions: 1,
      onManageSubscription: () => _showSnackBar('Manage Subscription tapped'),
      onSecuritySettings: () => _showSnackBar('Security Settings tapped'),
      onAccountPreferences: () => _showSnackBar('Account Preferences tapped'),
      onUpgrade: () => _showSnackBar('Upgrade tapped'),
      onDowngrade: () => _showSnackBar('Continue Free tapped'),
      isLoading: _isLoading,
    );
  }

  Widget _buildExpiredExample() {
    final expiredSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.expired,
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
      ],
      isTrialActive: false,
      paymentMethod: const PaymentMethod(
        type: 'card',
        last4: '1234',
        brand: 'mastercard',
        expiryMonth: 1,
        expiryYear: 2020, // Expired card
        isDefault: true,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: expiredSubscription,
      hasAdminPrivileges: false,
      isEmailVerified: false,
      isTwoFactorEnabled: false,
      activeSessions: 3,
      onManageSubscription: () => _showSnackBar('Manage Subscription tapped'),
      onSecuritySettings: () => _showSnackBar('Security Settings tapped'),
      onAccountPreferences: () => _showSnackBar('Account Preferences tapped'),
      onUpgrade: () => _showSnackBar('Reactivate Subscription tapped'),
      isLoading: _isLoading,
    );
  }

  Widget _buildCancelledExample() {
    final cancelledSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.cancelled,
      renewalDate: DateTime.now().add(const Duration(days: 5)),
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
      ],
      isTrialActive: false,
      paymentMethod: const PaymentMethod(
        type: 'paypal',
        isDefault: true,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      updatedAt: DateTime.now().subtract(const Duration(days: 10)),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: cancelledSubscription,
      hasAdminPrivileges: true,
      isEmailVerified: true,
      isTwoFactorEnabled: true,
      activeSessions: 2,
      onManageSubscription: () => _showSnackBar('Manage Subscription tapped'),
      onSecuritySettings: () => _showSnackBar('Security Settings tapped'),
      onAccountPreferences: () => _showSnackBar('Account Preferences tapped'),
      isLoading: _isLoading,
    );
  }

  Widget _buildLoadingExample() {
    return const AccountManagementCard(
      isLoading: true,
    );
  }

  Widget _buildExampleDescription() {
    final descriptions = [
      'Free plan with upgrade prompts and basic security settings.',
      'Active premium subscription with full features and secure payment method.',
      'Trial subscription ending soon with upgrade and downgrade options.',
      'Expired subscription with security warnings and expired payment method.',
      'Cancelled subscription still active until renewal date.',
      'Loading state with skeleton placeholders.',
    ];

    return Card(
      child: Padding(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Description',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            Text(
              descriptions[_selectedExample],
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            SizedBox(height: 2.h),
            Text(
              'Features Demonstrated:',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 1.h),
            ..._getFeaturesList().map((feature) => Padding(
              padding: EdgeInsets.only(bottom: 0.5.h),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('• ', style: Theme.of(context).textTheme.bodyMedium),
                  Expanded(
                    child: Text(
                      feature,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ),
                ],
              ),
            )),
          ],
        ),
      ),
    );
  }

  List<String> _getFeaturesList() {
    switch (_selectedExample) {
      case 0:
        return [
          'Free plan display with upgrade prompts',
          'Basic security settings overview',
          'Account preferences section',
          'Upgrade call-to-action button',
        ];
      case 1:
        return [
          'Premium subscription status with renewal date',
          'Complete feature list with truncation',
          'Payment method display',
          'Enhanced security settings',
        ];
      case 2:
        return [
          'Trial status with countdown',
          'Upgrade urgency messaging',
          'Both upgrade and downgrade options',
          'Trial-specific status badge',
        ];
      case 3:
        return [
          'Expired subscription warning',
          'Expired payment method indication',
          'Security warnings for unverified email',
          'Multiple active sessions alert',
        ];
      case 4:
        return [
          'Cancelled subscription with end date',
          'PayPal payment method display',
          'Admin privileges indication',
          'Secure account status',
        ];
      case 5:
        return [
          'Loading skeleton animations',
          'Graceful loading state handling',
          'Maintained layout structure',
          'User-friendly loading experience',
        ];
      default:
        return [];
    }
  }

  void _showSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}

/// Entry point for running the example
void main() {
  runApp(const AccountManagementCardExample());
}