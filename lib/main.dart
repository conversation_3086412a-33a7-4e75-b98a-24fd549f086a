import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../widgets/custom_error_widget.dart';
import 'core/app_export.dart';
import 'services/supabase_service.dart';
import 'services/debug_supabase_service.dart';
import 'services/measurement_units_service.dart';
import 'services/email_verification_handler.dart';
import 'services/feature_access_service.dart';
import 'presentation/subscription/controllers/subscription_controller.dart';
import 'core/global_navigator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Services
  try {
    // Initialize SettingsService first
    await SettingsService.instance.init();
    
    // Initialize MeasurementUnitsService
    await MeasurementUnitsService.instance.init();
    
    // Initialize ThemeService
    final themeService = ThemeService();
    await themeService.init();
    
    // Initialize Supabase
    await SupabaseService.initialize();
    
    // Initialize automatic email sync service
    await AutomaticEmailSyncService.initialize();
    
    // Email verification handler disabled to prevent conflicts
    // EmailVerificationHandler.initialize();
    // await EmailVerificationHandler.checkPendingEmailChanges();
    
    // Run debug test to diagnose any issues
    await DebugSupabaseService.runDebugTest();
    
    // Pass theme service to app
    runApp(MyApp(themeService: themeService));
    return;
  } catch (e) {
    debugPrint('❌ Main initialization error: $e');
  }

  ErrorWidget.builder = (FlutterErrorDetails details) {
    return CustomErrorWidget(
      title: 'Error',
      message: details.exception.toString(),
    );
  };
  Future.wait([
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp])
  ]).then((value) {
    // Fallback if theme service initialization failed
    runApp(MyApp());
  });
}

class MyApp extends StatelessWidget {
  final ThemeService? themeService;
  
  const MyApp({super.key, this.themeService});

  @override
  Widget build(BuildContext context) {
    if (themeService != null) {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: themeService!),
          ChangeNotifierProvider.value(value: MeasurementUnitsService.instance),
          // ...existing code...
          // Subscription Management
          ChangeNotifierProvider(
            create: (_) {
              final controller = SubscriptionController();
              controller.initialize(); // Load current subscription
              return controller;
            },
          ),
          // Feature Access Service
          ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
            create: (context) => FeatureAccessService(
              Provider.of<SubscriptionController>(context, listen: false),
            ),
            update: (context, subscription, previous) => 
                previous ?? FeatureAccessService(subscription),
          ),
        ],
        child: Consumer<ThemeService>(
          builder: (context, themeService, child) {
            return Sizer(builder: (context, orientation, screenType) {
              return MaterialApp(
                  navigatorKey: navigatorKey,
                  title: 'babytracker_pro',
                  theme: AppTheme.lightTheme,
                  darkTheme: AppTheme.darkTheme,
                  themeMode: themeService.themeMode,
                  builder: (context, child) {
                    return MediaQuery(
                        data: MediaQuery.of(context)
                            .copyWith(textScaler: TextScaler.linear(1.0)),
                        child: child!);
                  },
                  debugShowCheckedModeBanner: false,
                  routes: AppRoutes.routes,
                  initialRoute: AppRoutes.initial,
                  navigatorObservers: [
                    NavigatorObserver(),
                  ]);
            });
          },
        ),
      );
    }
    
    // Fallback without theme service
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: MeasurementUnitsService.instance),
        // ...existing code...
        // Subscription Management
        ChangeNotifierProvider(
          create: (_) {
            final controller = SubscriptionController();
            controller.initialize(); // Load current subscription
            return controller;
          },
        ),
        // Feature Access Service
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<SubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
              previous ?? FeatureAccessService(subscription),
        ),
      ],
      child: Sizer(builder: (context, orientation, screenType) {
        return MaterialApp(
            navigatorKey: navigatorKey,
            title: 'babytracker_pro',
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.system,
            builder: (context, child) {
              return MediaQuery(
                  data: MediaQuery.of(context)
                      .copyWith(textScaler: TextScaler.linear(1.0)),
                  child: child!);
            },
            debugShowCheckedModeBanner: false,
            routes: AppRoutes.routes,
            initialRoute: AppRoutes.initial,
            navigatorObservers: [
              NavigatorObserver(),
            ]);
      }),
    );
  }
}