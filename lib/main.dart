import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import '../widgets/custom_error_widget.dart';
import 'core/app_export.dart';
import 'services/supabase_service.dart';
import 'services/debug_supabase_service.dart';
import 'services/measurement_units_service.dart';
import 'services/email_verification_handler.dart';
import 'services/feature_access_service.dart';
import 'services/theme_service.dart';
import 'services/settings_service.dart';
import 'services/automatic_email_sync_service.dart';
import 'services/baby_profile_state_manager.dart';
import 'services/ai_insights_state_manager.dart';
import 'presentation/subscription/controllers/subscription_controller.dart';
import 'presentation/subscription/controllers/feature_access_controller.dart';
import 'core/global_navigator.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Services
  try {
    // Initialize SettingsService first
    await SettingsService.instance.init();
    
    // Initialize MeasurementUnitsService
    await MeasurementUnitsService.instance.init();
    
    // Initialize ThemeService
    final themeService = ThemeService();
    await themeService.init();
    
    // Initialize Supabase
    await SupabaseService.initialize();
    
    // Initialize automatic email sync service
    await AutomaticEmailSyncService.initialize();
    
    // Run debug test to diagnose any issues
    await DebugSupabaseService.runDebugTest();
    
    // Pass theme service to app
    runApp(MyApp(themeService: themeService));
    
    return;
  } catch (e) {
    debugPrint('❌ Main initialization error: $e');
  }

  ErrorWidget.builder = (FlutterErrorDetails details) {
    return CustomErrorWidget(
      title: 'Error',
      message: details.exception.toString(),
    );
  };
}

class MyApp extends StatelessWidget {
  final ThemeService themeService;

  const MyApp({super.key, required this.themeService});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Core providers
        ChangeNotifierProvider(create: (_) => BabyProfileStateManager()),
        ChangeNotifierProvider(create: (_) => AIInsightsStateManager()),
        ChangeNotifierProvider(create: (_) => themeService),
        
        // 🔥 SUBSCRIPTION PROVIDERS:
        ChangeNotifierProvider(
          create: (_) {
            debugPrint('🔥 Creating SubscriptionController...');
            final controller = SubscriptionController();
            // Initialize after widget tree is built
            WidgetsBinding.instance.addPostFrameCallback((_) async {
              debugPrint('🔥 Initializing SubscriptionController...');
              await controller.initialize();
              debugPrint('🔥 SubscriptionController initialized successfully');
            });
            return controller;
          },
        ),
        
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) {
            debugPrint('🔥 Creating FeatureAccessService...');
            return FeatureAccessService(
              Provider.of<SubscriptionController>(context, listen: false),
            );
          },
          update: (context, subscription, previous) {
            debugPrint('🔥 Updating FeatureAccessService...');
            return previous ?? FeatureAccessService(subscription);
          },
        ),
        
        ChangeNotifierProxyProvider<FeatureAccessService, FeatureAccessController>(
          create: (context) {
            debugPrint('🔥 Creating FeatureAccessController...');
            return FeatureAccessController(
              Provider.of<FeatureAccessService>(context, listen: false),
            );
          },
          update: (context, featureAccess, previous) {
            debugPrint('🔥 Updating FeatureAccessController...');
            return previous ?? FeatureAccessController(featureAccess);
          },
        ),
      ],
      child: Sizer(
        builder: (context, orientation, deviceType) {
          return Consumer<ThemeService>(
            builder: (context, themeService, child) {
              return MaterialApp(
                title: 'Baby Tracker Pro',
                navigatorKey: navigatorKey,
                theme: ThemeData.light(),
                darkTheme: ThemeData.dark(),
                themeMode: ThemeMode.system,
                initialRoute: AppRoutes.splashScreen,
                routes: AppRoutes.routes,
                debugShowCheckedModeBanner: false,
              );
            },
          );
        },
      ),
    );
  }
}
