import 'database/activity_service.dart';
import 'database/milestone_repository.dart';
import 'error_handling_service.dart';
import 'auth_service.dart';

/// Factory for creating and managing service instances
/// Provides dependency injection and testing support
class ServiceFactory {
  static final ServiceFactory _instance = ServiceFactory._internal();
  factory ServiceFactory() => _instance;
  ServiceFactory._internal();

  final Map<Type, dynamic> _services = {};
  bool _isTestMode = false;

  /// Register a service instance (useful for testing)
  void register<T>(T service) {
    _services[T] = service;
  }

  /// Get or create a service instance
  T get<T>() {
    if (_services.containsKey(T)) {
      return _services[T] as T;
    }

    // Create service instances based on type
    final service = _createService<T>();
    _services[T] = service;
    return service;
  }

  /// Create service instance based on type
  T _createService<T>() {
    switch (T) {
      case ActivityService:
        return ActivityService() as T;
      case MilestoneRepository:
        return MilestoneRepository() as T;
      case ErrorHandlingService:
        return ErrorHandlingService() as T;
      case AuthService:
        return AuthService() as T;
      default:
        throw Exception('Service type $T not registered in ServiceFactory');
    }
  }

  /// Enable test mode (allows service mocking)
  void enableTestMode() {
    _isTestMode = true;
  }

  /// Clear all services (useful for testing)
  void clear() {
    _services.clear();
  }

  bool get isTestMode => _isTestMode;
}

/// Mixin for easy service access
mixin ServiceMixin {
  T getService<T>() => ServiceFactory().get<T>();
}