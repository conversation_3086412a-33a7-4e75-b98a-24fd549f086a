import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import '../core/global_navigator.dart';
import 'supabase_service.dart';

/// Manual email verification service that doesn't rely on redirect URLs
class ManualEmailVerificationService {
  static const String _tag = 'ManualEmailVerificationService';
  static Timer? _verificationTimer;
  
  /// Start manual verification process
  static void startManualVerification(String oldEmail, String newEmail) {
    debugPrint('$_tag: Starting manual verification for email change from $oldEmail to $newEmail');
    
    // Show instruction dialog to user
    _showVerificationInstructions(newEmail);
    
    // Start checking for verification every 5 seconds
    _verificationTimer?.cancel();
    _verificationTimer = Timer.periodic(Duration(seconds: 5), (timer) async {
      await _checkVerificationStatus(oldEmail, newEmail, timer);
    });
  }
  
  /// Show verification instructions to user
  static void _showVerificationInstructions(String newEmail) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(
                Icons.mark_email_read,
                color: Colors.blue,
                size: 24,
              ),
              SizedBox(width: 12),
              Text('Verify Your Email'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'We\'ve sent a verification email to:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                ),
                child: Text(
                  newEmail,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Colors.blue,
                  ),
                ),
              ),
              SizedBox(height: 16),
              Text(
                'Please:',
                style: TextStyle(fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 8),
              Text('1. Check your email inbox'),
              Text('2. Click the verification link'),
              Text('3. Return to this app'),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.orange, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'The app will automatically detect when you\'ve verified your email.',
                        style: TextStyle(
                          color: Colors.orange.shade700,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                _verificationTimer?.cancel();
                Navigator.pop(context);
              },
              child: Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              child: Text('I\'ll verify now'),
            ),
          ],
        ),
      );
    }
  }
  
  /// Check verification status
  static Future<void> _checkVerificationStatus(String oldEmail, String newEmail, Timer timer) async {
    try {
      debugPrint('$_tag: Checking verification status...');
      
      // Refresh the session to get the latest user data
      final response = await Supabase.instance.client.auth.refreshSession();
      final user = response.user;
      
      if (user != null) {
        debugPrint('$_tag: Current email: ${user.email}');
        debugPrint('$_tag: New email: ${user.newEmail}');
        debugPrint('$_tag: Email confirmed at: ${user.emailConfirmedAt}');
        
        // Check if email change is complete
        if (user.newEmail == null && user.email == newEmail) {
          debugPrint('$_tag: ✅ Email verification completed! New email: ${user.email}');
          
          // Update local user profile
          await _updateLocalUserProfile(user);
          
          // Show success notification
          _showEmailChangeSuccess(user.email!);
          
          // Stop checking
          timer.cancel();
          _verificationTimer = null;
        } else if (user.newEmail != null) {
          debugPrint('$_tag: ⏳ Email change still pending. Waiting for verification...');
        }
      }
    } catch (e) {
      debugPrint('$_tag: Error checking verification status: $e');
    }
  }
  
  /// Update local user profile after email change
  static Future<void> _updateLocalUserProfile(User user) async {
    try {
      final supabaseService = SupabaseService();
      
      // Update user_profiles table with new email
      await supabaseService.update(
        'user_profiles',
        {
          'email': user.email,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        user.id,
      );
      
      debugPrint('$_tag: ✅ Updated user profile with new email: ${user.email}');
    } catch (e) {
      debugPrint('$_tag: ❌ Error updating user profile: $e');
    }
  }
  
  /// Show success notification for email change
  static void _showEmailChangeSuccess(String newEmail) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  '✅ Email successfully changed to $newEmail',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(16),
          duration: Duration(seconds: 5),
        ),
      );
      
      // Also show a dialog for more prominent feedback
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.green, size: 24),
              SizedBox(width: 12),
              Text('Email Changed Successfully'),
            ],
          ),
          content: Text(
            'Your email has been successfully changed to $newEmail. You can now use this email to sign in.',
          ),
          actions: [
            ElevatedButton(
              onPressed: () => Navigator.pop(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
              ),
              child: Text('Great!'),
            ),
          ],
        ),
      );
    }
  }
  
  /// Stop verification checking
  static void stopVerification() {
    _verificationTimer?.cancel();
    _verificationTimer = null;
  }
}