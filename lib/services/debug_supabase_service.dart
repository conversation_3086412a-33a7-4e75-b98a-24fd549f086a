import 'package:flutter/foundation.dart';
import './supabase_service.dart';

class DebugSupabaseService {
  static final DebugSupabaseService _instance = DebugSupabaseService._internal();
  factory DebugSupabaseService() => _instance;
  DebugSupabaseService._internal();

  /// Test basic Supabase connection and permissions
  static Future<Map<String, dynamic>> testConnection() async {
    final results = <String, dynamic>{};
    
    try {
      // Test 1: Check if Supabase is initialized
      final supabaseService = SupabaseService();
      final client = await supabaseService.client;
      results['supabase_initialized'] = true;
      
      // Test 2: Check current user
      final currentUser = supabaseService.currentUser;
      results['user_authenticated'] = currentUser != null;
      results['user_id'] = currentUser?.id;
      
      // Test 3: Test simple query (user profiles)
      try {
        final userProfiles = await supabaseService.select(
          'user_profiles',
          limit: 1,
        );
        results['user_profiles_accessible'] = true;
        results['user_profiles_count'] = userProfiles.length;
      } catch (e) {
        results['user_profiles_accessible'] = false;
        results['user_profiles_error'] = e.toString();
      }

      // Test 4: Test baby profiles query
      try {
        final babyProfiles = await supabaseService.select(
          'baby_profiles',
          filters: currentUser != null ? {'user_id': currentUser.id} : null,
          limit: 1,
        );
        results['baby_profiles_accessible'] = true;
        results['baby_profiles_count'] = babyProfiles.length;
      } catch (e) {
        results['baby_profiles_accessible'] = false;
        results['baby_profiles_error'] = e.toString();
      }

      // Test 5: Test activity logs query
      try {
        final activities = await supabaseService.select(
          'activity_logs',
          limit: 1,
        );
        results['activity_logs_accessible'] = true;
        results['activity_logs_count'] = activities.length;
      } catch (e) {
        results['activity_logs_accessible'] = false;
        results['activity_logs_error'] = e.toString();
      }

      // Test 6: Test AI insights query
      try {
        final insights = await supabaseService.select(
          'ai_insights',
          limit: 1,
        );
        results['ai_insights_accessible'] = true;
        results['ai_insights_count'] = insights.length;
      } catch (e) {
        results['ai_insights_accessible'] = false;
        results['ai_insights_error'] = e.toString();
      }

    } catch (e) {
      results['supabase_initialized'] = false;
      results['initialization_error'] = e.toString();
    }

    return results;
  }

  /// Print debug results in a readable format
  static void printDebugResults(Map<String, dynamic> results) {
    debugPrint('🔍 === SUPABASE DEBUG RESULTS ===');
    
    // Connection status
    if (results['supabase_initialized'] == true) {
      debugPrint('✅ Supabase initialized successfully');
    } else {
      debugPrint('❌ Supabase initialization failed');
      debugPrint('🚨 Error: ${results['initialization_error']}');
      return;
    }
    
    // Authentication status
    if (results['user_authenticated'] == true) {
      debugPrint('✅ User authenticated: ${results['user_id']}');
    } else {
      debugPrint('❌ User not authenticated');
    }
    
    // Table access tests
    final tables = ['user_profiles', 'baby_profiles', 'activity_logs', 'ai_insights'];
    
    for (final table in tables) {
      final accessible = results['${table}_accessible'];
      if (accessible == true) {
        debugPrint('✅ $table: ${results['${table}_count']} records accessible');
      } else {
        debugPrint('❌ $table: ACCESS DENIED');
        debugPrint('   Error: ${results['${table}_error']}');
      }
    }
    
    debugPrint('🔍 === END DEBUG RESULTS ===');
  }

  /// Run comprehensive debug test
  static Future<void> runDebugTest() async {
    debugPrint('🚀 Running Supabase debug test...');
    final results = await testConnection();
    printDebugResults(results);
  }
}
