import 'package:uuid/uuid.dart';
import '../models/measurement.dart';
import '../models/baby_profile.dart';
import '../services/supabase_service.dart';
import '../services/measurement_validation_service.dart';
import '../services/enhanced_percentile_calculator.dart';
import '../services/who_data_service.dart';

/// Enhanced measurement service with automatic percentile calculation and validation
class EnhancedMeasurementService {
  final SupabaseService _supabaseService = SupabaseService();

  /// Save measurement with automatic percentile calculation and validation
  Future<Measurement> saveMeasurement({
    required String babyId,
    required String measurementType,
    required double value,
    required String unit,
    required DateTime measuredAt,
    required BabyProfile babyProfile,
    String? notes,
    Map<String, dynamic>? metadata,
  }) async {
    // Calculate age in months
    final ageInMonths = measuredAt.difference(babyProfile.birthDate).inDays / 30.44;
    
    // Get previous measurements for validation and velocity calculation
    final previousMeasurements = await getMeasurements(babyId, measurementType);
    
    // Validate measurement
    final validation = MeasurementValidationService.validateMeasurement(
      value,
      ageInMonths,
      measurementType,
      _normalizeGender(babyProfile.gender),
      babyProfile,
      measurementDate: measuredAt,
      previousMeasurements: previousMeasurements,
    );

    // Calculate WHO percentile and analysis
    PercentileResult? percentileAnalysis;
    double? percentile;
    double? zScore;
    
    try {
      percentileAnalysis = EnhancedPercentileCalculator.calculateLMSPercentile(
        value,
        ageInMonths,
        measurementType,
        _normalizeGender(babyProfile.gender),
      );
      percentile = percentileAnalysis.percentile;
      zScore = percentileAnalysis.zScore;
    } catch (e) {
      // If percentile calculation fails, continue without it
      print('Warning: Could not calculate percentile for measurement: $e');
    }

    // Calculate growth velocity if previous measurements exist
    GrowthVelocity? velocityFromPrevious;
    if (previousMeasurements.isNotEmpty) {
      final measurementData = [
        ...previousMeasurements.map((m) => m.toMeasurementData(_normalizeGender(babyProfile.gender))),
        MeasurementData(
          value: value,
          ageInMonths: ageInMonths,
          date: measuredAt,
          measurementType: measurementType,
          gender: _normalizeGender(babyProfile.gender),
        ),
      ];
      
      final velocityAnalysis = EnhancedPercentileCalculator.calculateGrowthVelocity(measurementData);
      if (velocityAnalysis != null) {
        velocityFromPrevious = GrowthVelocity(
          velocityPerMonth: velocityAnalysis.velocityPerMonth,
          velocityPercentile: velocityAnalysis.velocityPercentile,
          interpretation: velocityAnalysis.interpretation,
          isNormal: velocityAnalysis.isNormal,
          timePeriod: velocityAnalysis.timePeriod,
        );
      }
    }

    // Determine if measurement should be flagged for review
    final flaggedForReview = MeasurementValidationService.shouldFlagForReview(validation);

    // Create measurement object
    final now = DateTime.now();
    final measurement = Measurement(
      id: const Uuid().v4(),
      babyId: babyId,
      measurementType: measurementType,
      value: value,
      unit: unit,
      measuredAt: measuredAt,
      ageInMonths: ageInMonths,
      notes: notes,
      percentile: percentile,
      zScore: zScore,
      velocityFromPrevious: velocityFromPrevious,
      percentileAnalysis: percentileAnalysis,
      flaggedForReview: flaggedForReview,
      validationResults: validation.toJson(),
      createdAt: now,
      updatedAt: now,
      metadata: metadata,
    );

    try {
      // Try to save to database with all fields
      await _supabaseService.insert('growth_measurements', measurement.toJson());
    } catch (e) {
      // If we get a schema error, try again with a simplified version
      if (e.toString().contains('Could not find the') || 
          e.toString().contains('PGRST204') ||
          e.toString().contains('schema cache')) {
        print('Warning: Schema issue detected. Trying with simplified schema...');
        
        // Create a simplified version of the measurement data
        final simplifiedData = {
          'id': measurement.id,
          'baby_id': measurement.babyId,
          'measurement_type': measurement.measurementType,
          'value': measurement.value,
          'unit': measurement.unit,
          'measured_at': measurement.measuredAt.toIso8601String(),
          'measurement_date': measurement.measuredAt.toIso8601String(), // For backward compatibility
          'notes': measurement.notes,
          'percentile': measurement.percentile,
          'z_score': measurement.zScore,
          'created_at': measurement.createdAt.toIso8601String(),
          'updated_at': measurement.updatedAt.toIso8601String(),
          // Legacy column support
          if (measurementType == 'weight') 'weight_kg': value,
          if (measurementType == 'height' || measurementType == 'length') 'height_cm': value,
          if (measurementType == 'head_circumference') 'head_circumference_cm': value,
        };
        
        await _supabaseService.insert('growth_measurements', simplifiedData);
      } else {
        // If it's not a schema issue, rethrow the error
        rethrow;
      }
    }

    return measurement;
  }

  /// Update existing measurement with recalculation of percentiles and validation
  Future<Measurement> updateMeasurement({
    required String measurementId,
    required String babyId,
    required String measurementType,
    required double value,
    required String unit,
    required DateTime measuredAt,
    required BabyProfile babyProfile,
    String? notes,
    Map<String, dynamic>? metadata,
  }) async {
    // Get existing measurement
    final existingData = await _supabaseService.select(
      'growth_measurements',
      filters: {'id': measurementId},
    );
    
    if (existingData.isEmpty) {
      throw Exception('Measurement not found');
    }

    final existingMeasurement = Measurement.fromJson(existingData.first);
    
    // Calculate age in months
    final ageInMonths = measuredAt.difference(babyProfile.birthDate).inDays / 30.44;
    
    // Get other measurements for validation (excluding current one)
    final otherMeasurements = await getMeasurements(babyId, measurementType);
    final previousMeasurements = otherMeasurements
        .where((m) => m.id != measurementId)
        .toList();
    
    // Validate measurement
    final validation = MeasurementValidationService.validateMeasurement(
      value,
      ageInMonths,
      measurementType,
      _normalizeGender(babyProfile.gender),
      babyProfile,
      measurementDate: measuredAt,
      previousMeasurements: previousMeasurements,
    );

    // Calculate WHO percentile and analysis
    PercentileResult? percentileAnalysis;
    double? percentile;
    double? zScore;
    
    try {
      percentileAnalysis = EnhancedPercentileCalculator.calculateLMSPercentile(
        value,
        ageInMonths,
        measurementType,
        _normalizeGender(babyProfile.gender),
      );
      percentile = percentileAnalysis.percentile;
      zScore = percentileAnalysis.zScore;
    } catch (e) {
      print('Warning: Could not calculate percentile for measurement: $e');
    }

    // Calculate growth velocity
    GrowthVelocity? velocityFromPrevious;
    if (previousMeasurements.isNotEmpty) {
      final measurementData = [
        ...previousMeasurements.map((m) => m.toMeasurementData(_normalizeGender(babyProfile.gender))),
        MeasurementData(
          value: value,
          ageInMonths: ageInMonths,
          date: measuredAt,
          measurementType: measurementType,
          gender: _normalizeGender(babyProfile.gender),
        ),
      ];
      
      final velocityAnalysis = EnhancedPercentileCalculator.calculateGrowthVelocity(measurementData);
      if (velocityAnalysis != null) {
        velocityFromPrevious = GrowthVelocity(
          velocityPerMonth: velocityAnalysis.velocityPerMonth,
          velocityPercentile: velocityAnalysis.velocityPercentile,
          interpretation: velocityAnalysis.interpretation,
          isNormal: velocityAnalysis.isNormal,
          timePeriod: velocityAnalysis.timePeriod,
        );
      }
    }

    // Determine if measurement should be flagged for review
    final flaggedForReview = MeasurementValidationService.shouldFlagForReview(validation);

    // Update measurement object
    final updatedMeasurement = existingMeasurement.copyWith(
      measurementType: measurementType,
      value: value,
      unit: unit,
      measuredAt: measuredAt,
      ageInMonths: ageInMonths,
      notes: notes,
      percentile: percentile,
      zScore: zScore,
      velocityFromPrevious: velocityFromPrevious,
      percentileAnalysis: percentileAnalysis,
      flaggedForReview: flaggedForReview,
      validationResults: validation.toJson(),
      updatedAt: DateTime.now(),
      metadata: metadata,
    );

    // Update in database
    await _supabaseService.update(
      'growth_measurements',
      updatedMeasurement.toJson(),
      'id',
      measurementId,
    );

    return updatedMeasurement;
  }

  /// Get measurements for a baby, optionally filtered by type
  Future<List<Measurement>> getMeasurements(String babyId, [String? measurementType]) async {
    final filters = {'baby_id': babyId};
    if (measurementType != null) {
      filters['measurement_type'] = measurementType;
    }

    final data = await _supabaseService.select(
      'growth_measurements',
      filters: filters,
      orderBy: 'measured_at',
      ascending: true,
    );

    return data.map((json) => Measurement.fromJson(json)).toList();
  }

  /// Get measurements requiring attention
  Future<List<Measurement>> getMeasurementsRequiringAttention(String babyId) async {
    final data = await _supabaseService.select(
      'growth_measurements',
      filters: {
        'baby_id': babyId,
        'flagged_for_review': true,
      },
      orderBy: 'measured_at',
      ascending: false,
    );

    final flaggedMeasurements = data.map((json) => Measurement.fromJson(json)).toList();
    
    // Also get measurements with extreme percentiles
    final allMeasurements = await getMeasurements(babyId);
    final extremeMeasurements = allMeasurements.where((m) => 
      m.percentile != null && (m.percentile! < 3.0 || m.percentile! > 97.0)
    ).toList();

    // Combine and deduplicate
    final allRequiringAttention = <String, Measurement>{};
    for (final measurement in [...flaggedMeasurements, ...extremeMeasurements]) {
      allRequiringAttention[measurement.id] = measurement;
    }

    final result = allRequiringAttention.values.toList();
    result.sort((a, b) => b.measuredAt.compareTo(a.measuredAt));
    
    return result;
  }

  /// Delete measurement
  Future<void> deleteMeasurement(String measurementId) async {
    await _supabaseService.delete('growth_measurements', 'id', measurementId);
  }

  /// Recalculate percentiles for all measurements of a baby
  Future<void> recalculatePercentilesForBaby(String babyId, BabyProfile babyProfile) async {
    final measurements = await getMeasurements(babyId);
    
    for (final measurement in measurements) {
      try {
        // Recalculate percentile and z-score
        final percentileAnalysis = EnhancedPercentileCalculator.calculateLMSPercentile(
          measurement.value,
          measurement.ageInMonths,
          measurement.measurementType,
          _normalizeGender(babyProfile.gender),
        );

        // Update measurement with new calculations
        final updatedData = {
          'percentile': percentileAnalysis.percentile,
          'z_score': percentileAnalysis.zScore,
          'percentile_analysis': percentileAnalysis.toJson(),
          'updated_at': DateTime.now().toIso8601String(),
        };

        await _supabaseService.update(
          'growth_measurements',
          updatedData,
          'id',
          measurement.id,
        );
      } catch (e) {
        print('Warning: Could not recalculate percentile for measurement ${measurement.id}: $e');
      }
    }
  }

  /// Get growth summary for a baby
  Future<Map<String, dynamic>> getGrowthSummary(String babyId, BabyProfile babyProfile) async {
    final measurements = await getMeasurements(babyId);
    
    if (measurements.isEmpty) {
      return {
        'totalMeasurements': 0,
        'measurementTypes': <String>[],
        'latestMeasurements': <String, Measurement>{},
        'requiresAttention': <Measurement>[],
        'growthTrends': <String, String>{},
      };
    }

    // Group measurements by type
    final measurementsByType = <String, List<Measurement>>{};
    for (final measurement in measurements) {
      measurementsByType.putIfAbsent(measurement.measurementType, () => []);
      measurementsByType[measurement.measurementType]!.add(measurement);
    }

    // Get latest measurement for each type
    final latestMeasurements = <String, Measurement>{};
    for (final entry in measurementsByType.entries) {
      entry.value.sort((a, b) => b.measuredAt.compareTo(a.measuredAt));
      latestMeasurements[entry.key] = entry.value.first;
    }

    // Get measurements requiring attention
    final requiresAttention = await getMeasurementsRequiringAttention(babyId);

    // Analyze growth trends
    final growthTrends = <String, String>{};
    for (final entry in measurementsByType.entries) {
      if (entry.value.length >= 2) {
        final sorted = List<Measurement>.from(entry.value)
          ..sort((a, b) => a.measuredAt.compareTo(b.measuredAt));
        
        final latest = sorted.last;
        final previous = sorted[sorted.length - 2];
        
        if (latest.percentile != null && previous.percentile != null) {
          final change = latest.percentile! - previous.percentile!;
          if (change > 10) {
            growthTrends[entry.key] = 'Increasing';
          } else if (change < -10) {
            growthTrends[entry.key] = 'Decreasing';
          } else {
            growthTrends[entry.key] = 'Stable';
          }
        } else {
          growthTrends[entry.key] = 'Unknown';
        }
      } else {
        growthTrends[entry.key] = 'Insufficient data';
      }
    }

    return {
      'totalMeasurements': measurements.length,
      'measurementTypes': measurementsByType.keys.toList(),
      'latestMeasurements': latestMeasurements,
      'requiresAttention': requiresAttention,
      'growthTrends': growthTrends,
      'measurementsByType': measurementsByType,
    };
  }

  /// Normalize gender string for WHO calculations
  String _normalizeGender(String gender) {
    final lowercaseGender = gender.toLowerCase();
    if (lowercaseGender == 'male' || lowercaseGender == 'boy' || lowercaseGender == 'boys') {
      return 'boys';
    } else if (lowercaseGender == 'female' || lowercaseGender == 'girl' || lowercaseGender == 'girls') {
      return 'girls';
    }
    // Default to boys if gender is unclear
    return 'boys';
  }

  /// Validate measurement data before saving
  Future<MeasurementValidationResult> validateMeasurementData({
    required double value,
    required double ageInMonths,
    required String measurementType,
    required BabyProfile babyProfile,
    DateTime? measurementDate,
    String? measurementId,
  }) async {
    // Get previous measurements for validation
    final previousMeasurements = await getMeasurements(babyProfile.id, measurementType);
    
    // Exclude current measurement if updating
    final filteredMeasurements = measurementId != null
        ? previousMeasurements.where((m) => m.id != measurementId).toList()
        : previousMeasurements;

    return MeasurementValidationService.validateMeasurement(
      value,
      ageInMonths,
      measurementType,
      _normalizeGender(babyProfile.gender),
      babyProfile,
      measurementDate: measurementDate,
      previousMeasurements: filteredMeasurements,
    );
  }

  /// Get measurement statistics for a baby
  Future<Map<String, dynamic>> getMeasurementStatistics(String babyId) async {
    final measurements = await getMeasurements(babyId);
    
    if (measurements.isEmpty) {
      return {
        'totalCount': 0,
        'byType': <String, int>{},
        'flaggedCount': 0,
        'averagePercentiles': <String, double>{},
        'latestPercentiles': <String, double>{},
      };
    }

    // Count by type
    final byType = <String, int>{};
    final percentilesByType = <String, List<double>>{};
    final latestPercentiles = <String, double>{};
    int flaggedCount = 0;

    for (final measurement in measurements) {
      // Count by type
      byType[measurement.measurementType] = (byType[measurement.measurementType] ?? 0) + 1;
      
      // Collect percentiles
      if (measurement.percentile != null) {
        percentilesByType.putIfAbsent(measurement.measurementType, () => []);
        percentilesByType[measurement.measurementType]!.add(measurement.percentile!);
      }
      
      // Count flagged measurements
      if (measurement.flaggedForReview) {
        flaggedCount++;
      }
    }

    // Calculate average percentiles
    final averagePercentiles = <String, double>{};
    for (final entry in percentilesByType.entries) {
      if (entry.value.isNotEmpty) {
        averagePercentiles[entry.key] = entry.value.reduce((a, b) => a + b) / entry.value.length;
      }
    }

    // Get latest percentiles
    final measurementsByType = <String, List<Measurement>>{};
    for (final measurement in measurements) {
      measurementsByType.putIfAbsent(measurement.measurementType, () => []);
      measurementsByType[measurement.measurementType]!.add(measurement);
    }

    for (final entry in measurementsByType.entries) {
      entry.value.sort((a, b) => b.measuredAt.compareTo(a.measuredAt));
      final latest = entry.value.first;
      if (latest.percentile != null) {
        latestPercentiles[entry.key] = latest.percentile!;
      }
    }

    return {
      'totalCount': measurements.length,
      'byType': byType,
      'flaggedCount': flaggedCount,
      'averagePercentiles': averagePercentiles,
      'latestPercentiles': latestPercentiles,
    };
  }
}