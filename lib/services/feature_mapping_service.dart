import '../models/feature_access.dart';
import '../models/enums.dart';

/// Centralized service for mapping between string-based feature names and AppFeature enum
/// This eliminates duplicate mapping logic across controllers and services
class FeatureMappingService {
  /// Map string feature names to AppFeature enum values
  static const Map<String, AppFeature> _stringToFeatureMap = {
    // AI Features
    'ai_insights': AppFeature.aiInsights,
    'ai_chat': AppFeature.aiChat,
    'ask_ai': AppFeature.aiChat,
    
    // Growth & Analytics
    'who_growth_charts': AppFeature.whoGrowthCharts,
    'growth_charts': AppFeature.whoGrowthCharts,
    'advanced_analytics': AppFeature.advancedAnalytics,
    
    // Profile & Family
    'unlimited_profiles': AppFeature.multipleBabyProfiles,
    'multiple_baby_profiles': AppFeature.multipleBabyProfiles,
    'family_sharing': AppFeature.familySharing,
    'family_management': AppFeature.familySharing,
    
    // Data & Export
    'data_export': AppFeature.dataExport,
    
    // Notifications & Support
    'custom_notifications': AppFeature.customReminders,
    'advanced_notifications': AppFeature.customReminders,
    'premium_support': AppFeature.prioritySupport,
    'priority_support': AppFeature.prioritySupport,
  };

  /// Convert string feature name to AppFeature enum
  static AppFeature? stringToFeature(String featureName) {
    return _stringToFeatureMap[featureName.toLowerCase()];
  }

  /// Convert AppFeature enum to primary string representation
  static String featureToString(AppFeature feature) {
    return _stringToFeatureMap.entries
        .firstWhere((entry) => entry.value == feature)
        .key;
  }

  /// Check if a string feature name is valid
  static bool isValidFeatureName(String featureName) {
    return _stringToFeatureMap.containsKey(featureName.toLowerCase());
  }

  /// Get all valid string feature names
  static List<String> getAllFeatureNames() {
    return _stringToFeatureMap.keys.toList();
  }

  /// Get feature access rules for subscription checking
  static bool checkFeatureAccess(AppFeature feature, SubscriptionStatus status, {
    bool includesAiInsights = false,
    bool includesDataExport = false,
    bool includesPremiumSupport = false,
    int maxFamilyMembers = 1,
  }) {
    final isPremium = status.isPremium;
    
    switch (feature) {
      case AppFeature.aiInsights:
      case AppFeature.aiChat:
        return isPremium && includesAiInsights;
      case AppFeature.whoGrowthCharts:
      case AppFeature.advancedAnalytics:
      case AppFeature.customReminders:
        return isPremium;
      case AppFeature.multipleBabyProfiles:
        return isPremium; // Free users limited to 1, premium unlimited
      case AppFeature.familySharing:
        return isPremium && maxFamilyMembers > 1;
      case AppFeature.dataExport:
        return isPremium && includesDataExport;
      case AppFeature.prioritySupport:
        return isPremium && includesPremiumSupport;
    }
  }

  /// Get feature usage limits
  static int? getFeatureLimit(AppFeature feature, SubscriptionStatus status) {
    final isPremium = status.isPremium;
    
    switch (feature) {
      case AppFeature.multipleBabyProfiles:
        return isPremium ? null : 1; // null = unlimited, 1 for free
      case AppFeature.familySharing:
        return isPremium ? 10 : 1; // 10 for premium, 1 for free
      case AppFeature.aiInsights:
      case AppFeature.aiChat:
      case AppFeature.whoGrowthCharts:
      case AppFeature.dataExport:
      case AppFeature.advancedAnalytics:
      case AppFeature.customReminders:
      case AppFeature.prioritySupport:
        return isPremium ? null : 0; // unlimited for premium, none for free
    }
  }

  /// Get user-friendly restriction message
  static String getRestrictionMessage(AppFeature feature) {
    switch (feature) {
      case AppFeature.aiInsights:
        return 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.';
      case AppFeature.aiChat:
        return 'AI Chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.';
      case AppFeature.whoGrowthCharts:
        return 'WHO Growth Charts require Premium plan. Upgrade to track your baby\'s growth with official WHO percentile charts.';
      case AppFeature.familySharing:
        return 'Family Sharing requires Premium plan. Upgrade to invite up to 10 family members.';
      case AppFeature.multipleBabyProfiles:
        return 'Multiple baby profiles require Premium plan. Free plan is limited to 1 baby profile.';
      case AppFeature.dataExport:
        return 'Data Export requires Premium plan. Upgrade to export your data for healthcare providers.';
      case AppFeature.advancedAnalytics:
        return 'Advanced analytics require Premium plan. Upgrade for detailed insights and trend analysis.';
      case AppFeature.customReminders:
        return 'Custom notifications require Premium plan. Upgrade to set personalized reminders.';
      case AppFeature.prioritySupport:
        return 'Priority customer support requires Premium plan. Upgrade for faster response times.';
    }
  }
}