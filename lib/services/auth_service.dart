import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/user_profile.dart';
import './supabase_service.dart';

class AuthService extends ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();
  User? _currentUser;
  UserProfile? _userProfile;
  bool _isLoading = false;

  User? get currentUser => _currentUser;
  UserProfile? get userProfile => _userProfile;
  bool get isLoading => _isLoading;
  bool get isAuthenticated => _currentUser != null;

  AuthService() {
    _initializeAuth();
  }

  void _initializeAuth() {
    _currentUser = _supabaseService.currentUser;
    if (_currentUser != null) {
      _loadUserProfile();
    }

    _supabaseService.authStateChanges.listen((AuthState data) {
      _currentUser = data.session?.user;
      notifyListeners();

      if (_currentUser != null) {
        _loadUserProfile();
        _trackUserSession();
      } else {
        _userProfile = null;
      }
    });
  }

  Future<void> _loadUserProfile() async {
    if (_currentUser == null) return;

    try {
      final response = await _supabaseService.select(
        'user_profiles',
        filters: {'id': _currentUser!.id},
      );

      if (response.isNotEmpty) {
        _userProfile = UserProfile.fromJson(response.first);
        notifyListeners();
      }
    } catch (e) {
      debugPrint('Error loading user profile: $e');
    }
  }

  Future<void> _trackUserSession() async {
    try {
      final supabase = await _supabaseService.client;
      await supabase.rpc('track_user_session', params: {
        'p_device_info': 'Mobile App - Flutter',
        'p_ip_address': null, // Could be obtained from device
      });
    } catch (e) {
      debugPrint('Error tracking user session: $e');
    }
  }

  Future<AuthResult> signUp({
    required String email,
    required String password,
    required String fullName,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      // Determine appropriate role for new user
      final userRole = await _determineUserRole(email);

      final response = await _supabaseService.signUp(
        email: email,
        password: password,
        data: {'full_name': fullName, 'role': userRole},
      );

      if (response.user != null) {
        // If signed up via invitation, mark invitation as accepted
        await _processInvitationAcceptance(email);
        
        return AuthResult.success(
            'Account created successfully! Please check your email to verify your account.');
      } else {
        return AuthResult.error('Failed to create account. Please try again.');
      }
    } catch (e) {
      return AuthResult.error(_getErrorMessage(e));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<AuthResult> signIn({
    required String email,
    required String password,
  }) async {
    try {
      _isLoading = true;
      notifyListeners();

      final response = await _supabaseService.signIn(
        email: email,
        password: password,
      );

      if (response.user != null) {
        return AuthResult.success('Signed in successfully!');
      } else {
        return AuthResult.error(
            'Failed to sign in. Please check your credentials.');
      }
    } catch (e) {
      return AuthResult.error(_getErrorMessage(e));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<AuthResult> signInWithGoogle() async {
    try {
      _isLoading = true;
      notifyListeners();

      final supabase = await _supabaseService.client;
      final success = await supabase.auth.signInWithOAuth(
        OAuthProvider.google,
        redirectTo: 'io.supabase.flutter://signin-callback/',
        authScreenLaunchMode: LaunchMode.externalApplication,
      );

      if (success) {
        return AuthResult.success('Google sign-in initiated successfully!');
      } else {
        return AuthResult.error('Failed to initiate Google sign-in.');
      }
    } catch (e) {
      return AuthResult.error(_getErrorMessage(e));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<AuthResult> signInWithApple() async {
    try {
      _isLoading = true;
      notifyListeners();

      final supabase = await _supabaseService.client;
      final success = await supabase.auth.signInWithOAuth(
        OAuthProvider.apple,
        redirectTo: 'io.supabase.flutter://signin-callback/',
        authScreenLaunchMode: LaunchMode.externalApplication,
      );

      if (success) {
        return AuthResult.success('Apple sign-in initiated successfully!');
      } else {
        return AuthResult.error('Failed to initiate Apple sign-in.');
      }
    } catch (e) {
      return AuthResult.error(_getErrorMessage(e));
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<void> signOut() async {
    try {
      _isLoading = true;
      notifyListeners();

      await _supabaseService.signOut();
      _userProfile = null;
    } catch (e) {
      debugPrint('Error signing out: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  Future<bool> updateUserProfile(UserProfile profile) async {
    print('*** AUTHSERVICE.UPDATEUSERPROFILE CALLED ***');
    try {
      debugPrint('=== DEBUG: Starting updateUserProfile ===');
      
      if (_currentUser == null) {
        debugPrint('ERROR: No authenticated user found');
        throw Exception('No authenticated user');
      }

      debugPrint('Current user ID: ${_currentUser!.id}');
      debugPrint('Profile to update: ${profile.toJson()}');

      // Use auth_id instead of id for the filter, and only update specific fields
      final updateData = {
        'full_name': profile.fullName,
        'email': profile.email,
        'updated_at': DateTime.now().toIso8601String(),
      };

      debugPrint('Update data: $updateData');
      debugPrint('Updating table: user_profiles');
      debugPrint('Filter field: auth_id');
      debugPrint('Filter value: ${_currentUser!.id}');

      await _supabaseService.update(
        'user_profiles',
        updateData,
        'auth_id',
        _currentUser!.id,
      );
      
      debugPrint('SUCCESS: Profile updated successfully');
      _userProfile = profile;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('ERROR: Failed to update user profile: $e');
      debugPrint('Error type: ${e.runtimeType}');
      if (e.toString().contains('PGRST116')) {
        debugPrint('PGRST116 Error - This means 0 rows were returned/updated');
        debugPrint('Possible causes:');
        debugPrint('1. No user_profiles record exists with auth_id: ${_currentUser?.id}');
        debugPrint('2. RLS policies are blocking the update');
        debugPrint('3. The auth_id field name is incorrect');
      }
      return false;
    }
  }

  // Family management methods
  Future<String?> inviteFamilyMember({
    required String email,
    required String role,
    Map<String, bool>? permissions,
  }) async {
    try {
      final supabase = await _supabaseService.client;

      // Get current user's family
      final userProfile = await supabase
          .from('user_profiles')
          .select('*, families!inner(*)')
          .eq('id', _currentUser!.id)
          .single();

      final familyId = userProfile['families']['id'];

      final response = await supabase.rpc('invite_family_member', params: {
        'p_family_id': familyId,
        'p_email': email,
        'p_role': role,
        'p_permissions': permissions ??
            {
              'view_data': true,
              'log_activities': false,
              'view_ai_insights': false,
              'export_data': false,
            },
      });

      return response as String?;
    } catch (e) {
      debugPrint('Error inviting family member: $e');
      return null;
    }
  }

  /// Send family invitation email
  Future<bool> sendFamilyInvitationEmail({
    required String email,
    required String inviterName,
    required String role,
    String? customMessage,
    required String invitationId,
  }) async {
    try {
      final supabase = await _supabaseService.client;

      // Generate invitation link
      final invitationLink = 'https://your-app-domain.com/accept-invitation?id=$invitationId';
      
      // Prepare email content
      final emailSubject = '$inviterName invited you to join their family on BabyTracker Pro';
      final emailBody = _buildInvitationEmailBody(
        inviterName: inviterName,
        role: role,
        customMessage: customMessage,
        invitationLink: invitationLink,
      );

      // Send email using Supabase Edge Function or external service
      final response = await supabase.functions.invoke(
        'send-invitation-email',
        body: {
          'to': email,
          'subject': emailSubject,
          'html': emailBody,
          'invitation_id': invitationId,
        },
      );

      return response.status == 200;
    } catch (e) {
      debugPrint('Error sending invitation email: $e');
      return false;
    }
  }

  /// Build invitation email HTML body
  String _buildInvitationEmailBody({
    required String inviterName,
    required String role,
    String? customMessage,
    required String invitationLink,
  }) {
    final roleDisplayName = role.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1).toLowerCase()
    ).join(' ');

    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Family Invitation - BabyTracker Pro</title>
        <style>
            body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background: white; padding: 30px; border: 1px solid #e1e5e9; }
            .footer { background: #f8f9fa; padding: 20px; text-align: center; border-radius: 0 0 8px 8px; font-size: 14px; color: #6c757d; }
            .button { display: inline-block; background: #667eea; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 600; margin: 20px 0; }
            .custom-message { background: #f8f9fa; padding: 15px; border-left: 4px solid #667eea; margin: 20px 0; font-style: italic; }
            .role-badge { background: #e3f2fd; color: #1976d2; padding: 4px 12px; border-radius: 16px; font-size: 14px; font-weight: 500; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🍼 BabyTracker Pro</h1>
                <p>You've been invited to join a family!</p>
            </div>
            <div class="content">
                <h2>Hello!</h2>
                <p><strong>$inviterName</strong> has invited you to join their family on BabyTracker Pro as a <span class="role-badge">$roleDisplayName</span>.</p>
                
                ${customMessage != null ? '<div class="custom-message">"$customMessage"</div>' : ''}
                
                <p>BabyTracker Pro helps families track their baby's activities, growth, and development with AI-powered insights and seamless collaboration.</p>
                
                <p>As a $roleDisplayName, you'll be able to:</p>
                <ul>
                    ${_getRolePermissionsList(role)}
                </ul>
                
                <div style="text-align: center;">
                    <a href="$invitationLink" class="button">Accept Invitation</a>
                </div>
                
                <p><small>This invitation will expire in 7 days. If you don't have the BabyTracker Pro app yet, you can download it from the App Store or Google Play.</small></p>
            </div>
            <div class="footer">
                <p>© 2024 BabyTracker Pro. This invitation was sent by $inviterName.</p>
                <p>If you didn't expect this invitation, you can safely ignore this email.</p>
            </div>
        </div>
    </body>
    </html>
    ''';
  }

  /// Get role permissions list for email
  String _getRolePermissionsList(String role) {
    switch (role.toLowerCase()) {
      case 'parent':
        return '''
          <li>View and track all baby activities</li>
          <li>Access AI insights and recommendations</li>
          <li>Manage baby profiles and settings</li>
          <li>Export data and generate reports</li>
        ''';
      case 'caregiver':
        return '''
          <li>View and track baby activities</li>
          <li>Access AI insights and recommendations</li>
          <li>Add new activity entries</li>
          <li>View growth charts and milestones</li>
        ''';
      case 'grandparent':
        return '''
          <li>View baby activities and progress</li>
          <li>Add basic activity entries</li>
          <li>View photos and milestones</li>
          <li>Stay connected with baby's development</li>
        ''';
      case 'babysitter':
        return '''
          <li>View recent activities</li>
          <li>Add basic care entries</li>
          <li>Access emergency information</li>
          <li>Limited access for temporary care</li>
        ''';
      default:
        return '''
          <li>View baby activities</li>
          <li>Basic tracking capabilities</li>
          <li>Stay connected with family</li>
        ''';
    }
  }

  Future<bool> acceptInvitation(String invitationToken) async {
    try {
      final supabase = await _supabaseService.client;

      final result = await supabase.rpc('accept_invitation', params: {
        'p_invitation_token': invitationToken,
      });

      return result as bool;
    } catch (e) {
      debugPrint('Error accepting invitation: $e');
      return false;
    }
  }

  // Role management methods
  Future<String> _determineUserRole(String email) async {
    try {
      // Check if this is the first user
      final isFirstUser = await _isFirstUser();
      if (isFirstUser) {
        return 'admin';
      }

      // Check for pending invitation
      final invitationRole = await _getInvitationRole(email);
      if (invitationRole != null) {
        return invitationRole;
      }

      // Default role for direct signups
      return 'parent';
    } catch (e) {
      debugPrint('Error determining user role: $e');
      return 'parent'; // Safe default
    }
  }

  Future<bool> _isFirstUser() async {
    try {
      final response = await _supabaseService.select(
        'user_profiles',
        limit: 1,
      );
      return response.isEmpty;
    } catch (e) {
      debugPrint('Error checking first user: $e');
      return false;
    }
  }

  Future<String?> _getInvitationRole(String email) async {
    try {
      final response = await _supabaseService.select(
        'invitations',
        filters: {
          'email': email,
          'status': 'pending',
        },
        orderBy: 'created_at',
        ascending: false,
        limit: 1,
      );

      if (response.isNotEmpty) {
        final invitation = response.first;
        final expiresAt = DateTime.parse(invitation['expires_at']);
        if (expiresAt.isAfter(DateTime.now())) {
          return invitation['role'];
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error getting invitation role: $e');
      return null;
    }
  }

  Future<void> _processInvitationAcceptance(String email) async {
    try {
      final invitation = await _supabaseService.select(
        'invitations',
        filters: {
          'email': email,
          'status': 'pending',
        },
        orderBy: 'created_at',
        ascending: false,
        limit: 1,
      );

      if (invitation.isNotEmpty) {
        final inviteId = invitation.first['id'];
        await _supabaseService.update(
          'invitations',
          {
            'status': 'accepted',
            'updated_at': DateTime.now().toIso8601String(),
          },
          'id',
          inviteId,
        );
      }
    } catch (e) {
      debugPrint('Error processing invitation acceptance: $e');
    }
  }

  Future<bool> updateUserRole(String userId, String newRole) async {
    try {
      if (_userProfile?.role != 'admin') {
        throw Exception('Only admins can change user roles');
      }

      // Prevent demoting the last admin
      if (_userProfile?.id == userId && newRole != 'admin') {
        final adminCount = await _getAdminCount();
        if (adminCount <= 1) {
          throw Exception('Cannot demote the last admin user');
        }
      }

      await _supabaseService.update(
        'user_profiles',
        {
          'role': newRole,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'id',
        userId,
      );

      return true;
    } catch (e) {
      debugPrint('Error updating user role: $e');
      return false;
    }
  }

  Future<int> _getAdminCount() async {
    try {
      final response = await _supabaseService.select(
        'user_profiles',
        filters: {'role': 'admin'},
      );
      return response.length;
    } catch (e) {
      debugPrint('Error getting admin count: $e');
      return 0;
    }
  }

  Map<String, bool> getUserPermissions(String role) {
    switch (role) {
      case 'admin':
        return {
          'view_data': true,
          'log_activities': true,
          'view_ai_insights': true,
          'export_data': true,
          'manage_users': true,
          'delete_data': true,
        };
      case 'parent':
        return {
          'view_data': true,
          'log_activities': true,
          'view_ai_insights': true,
          'export_data': true,
          'manage_users': false,
          'delete_data': false,
        };
      case 'grandparent':
        return {
          'view_data': true,
          'log_activities': true,
          'view_ai_insights': false,
          'export_data': false,
          'manage_users': false,
          'delete_data': false,
        };
      case 'babysitter':
        return {
          'view_data': true,
          'log_activities': true,
          'view_ai_insights': false,
          'export_data': false,
          'manage_users': false,
          'delete_data': false,
        };
      case 'other_carer':
        return {
          'view_data': true,
          'log_activities': false,
          'view_ai_insights': false,
          'export_data': false,
          'manage_users': false,
          'delete_data': false,
        };
      default:
        return {
          'view_data': false,
          'log_activities': false,
          'view_ai_insights': false,
          'export_data': false,
          'manage_users': false,
          'delete_data': false,
        };
    }
  }

  Future<List<Map<String, dynamic>>> getFamilyMembers() async {
    try {
      final supabase = await _supabaseService.client;

      final response = await supabase.from('family_members').select('''
            *,
            user_profiles(*),
            families(*)
          ''').eq('families.admin_user_id', _currentUser!.id);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      debugPrint('Error getting family members: $e');
      return [];
    }
  }

  String _getErrorMessage(dynamic error) {
    debugPrint('Authentication error: $error');
    debugPrint('Error type: ${error.runtimeType}');
    
    if (error is AuthException) {
      switch (error.message) {
        case 'Invalid login credentials':
          return 'Invalid email or password. Please try again.';
        case 'Email not confirmed':
          return 'Please verify your email address before signing in.';
        case 'User already registered':
          return 'An account with this email already exists.';
        default:
          return error.message;
      }
    }
    
    // Handle network and other common errors
    String errorString = error.toString().toLowerCase();
    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network error. Please check your internet connection.';
    }
    if (errorString.contains('timeout')) {
      return 'Request timeout. Please try again.';
    }
    if (errorString.contains('invalid') && errorString.contains('credentials')) {
      return 'Invalid email or password. Please try again.';
    }
    
    // Return the actual error message for debugging
    return 'Error: ${error.toString()}';
  }
}

class AuthResult {
  final bool isSuccess;
  final String message;

  AuthResult._(this.isSuccess, this.message);

  factory AuthResult.success(String message) => AuthResult._(true, message);
  factory AuthResult.error(String message) => AuthResult._(false, message);
}
