import 'package:flutter/foundation.dart';
import 'measurement_units_service.dart';
import 'unit_conversion_service.dart';

/// Enhanced measurement storage service that preserves original units
/// and provides accurate conversions for display
class EnhancedMeasurementStorageService {
  static const String _originalUnitsKey = 'original_measurement_units';
  
  /// Store measurement with original units preserved
  static Map<String, dynamic> prepareMeasurementForStorage({
    required double value,
    required String unit,
    required String measurementType,
    required bool wasEnteredInMetric,
  }) {
    // Calculate both metric and imperial values for storage
    double metricValue;
    double imperialValue;
    String metricUnit;
    String imperialUnit;
    
    switch (measurementType.toLowerCase()) {
      case 'weight':
        metricUnit = 'kg';
        imperialUnit = 'lbs';
        if (wasEnteredInMetric) {
          metricValue = value;
          imperialValue = UnitConversionService.convertWeightToImperial(value);
        } else {
          imperialValue = value;
          metricValue = UnitConversionService.convertWeightToMetric(value);
        }
        break;
        
      case 'height':
      case 'length':
      case 'head_circumference':
        metricUnit = 'cm';
        imperialUnit = 'in';
        if (wasEnteredInMetric) {
          metricValue = value;
          imperialValue = UnitConversionService.convertLengthToImperial(value);
        } else {
          imperialValue = value;
          metricValue = UnitConversionService.convertLengthToMetric(value);
        }
        break;
        
      case 'temperature':
        metricUnit = '°C';
        imperialUnit = '°F';
        if (wasEnteredInMetric) {
          metricValue = value;
          imperialValue = (value * 9/5) + 32;
        } else {
          imperialValue = value;
          metricValue = (value - 32) * 5/9;
        }
        break;
        
      case 'volume':
        metricUnit = 'ml';
        imperialUnit = 'fl oz';
        if (wasEnteredInMetric) {
          metricValue = value;
          imperialValue = value * 0.033814; // ml to fl oz
        } else {
          imperialValue = value;
          metricValue = value / 0.033814; // fl oz to ml
        }
        break;
        
      default:
        throw ArgumentError('Unsupported measurement type: $measurementType');
    }
    
    return {
      // Store both values for accuracy
      '${measurementType}_metric_value': metricValue,
      '${measurementType}_metric_unit': metricUnit,
      '${measurementType}_imperial_value': imperialValue,
      '${measurementType}_imperial_unit': imperialUnit,
      
      // Store original entry information
      '${measurementType}_original_value': value,
      '${measurementType}_original_unit': unit,
      '${measurementType}_entered_as_metric': wasEnteredInMetric,
      
      // Legacy compatibility (can be removed later)
      measurementType: metricValue, // For backward compatibility
    };
  }
  
  /// Extract measurement for display based on current preference
  static MeasurementDisplayData extractMeasurementForDisplay({
    required Map<String, dynamic> data,
    required String measurementType,
    required bool displayAsMetric,
  }) {
    // Try to get stored values (new format)
    final metricValue = data['${measurementType}_metric_value'] as double?;
    final imperialValue = data['${measurementType}_imperial_value'] as double?;
    final originalValue = data['${measurementType}_original_value'] as double?;
    final originalUnit = data['${measurementType}_original_unit'] as String?;
    final wasEnteredAsMetric = data['${measurementType}_entered_as_metric'] as bool?;
    
    // If new format data exists, use it
    if (metricValue != null && imperialValue != null) {
      return MeasurementDisplayData(
        displayValue: displayAsMetric ? metricValue : imperialValue,
        displayUnit: displayAsMetric 
            ? _getMetricUnit(measurementType)
            : _getImperialUnit(measurementType),
        originalValue: originalValue ?? (displayAsMetric ? metricValue : imperialValue),
        originalUnit: originalUnit ?? (displayAsMetric 
            ? _getMetricUnit(measurementType)
            : _getImperialUnit(measurementType)),
        wasEnteredAsMetric: wasEnteredAsMetric ?? displayAsMetric,
        metricValue: metricValue,
        imperialValue: imperialValue,
      );
    }
    
    // Fallback to legacy format (single value, assume metric)
    final legacyValue = data[measurementType] as double?;
    if (legacyValue != null) {
      return _convertLegacyMeasurement(
        value: legacyValue,
        measurementType: measurementType,
        displayAsMetric: displayAsMetric,
      );
    }
    
    // No data found
    return MeasurementDisplayData.empty(measurementType);
  }
  
  /// Convert legacy single-value measurements
  static MeasurementDisplayData _convertLegacyMeasurement({
    required double value,
    required String measurementType,
    required bool displayAsMetric,
  }) {
    // Legacy data is assumed to be in metric
    double displayValue;
    String displayUnit;
    double imperialValue;
    
    if (displayAsMetric) {
      displayValue = value;
      displayUnit = _getMetricUnit(measurementType);
      imperialValue = _convertMetricToImperial(value, measurementType);
    } else {
      imperialValue = _convertMetricToImperial(value, measurementType);
      displayValue = imperialValue;
      displayUnit = _getImperialUnit(measurementType);
    }
    
    return MeasurementDisplayData(
      displayValue: displayValue,
      displayUnit: displayUnit,
      originalValue: value,
      originalUnit: _getMetricUnit(measurementType),
      wasEnteredAsMetric: true,
      metricValue: value,
      imperialValue: imperialValue,
    );
  }
  
  static double _convertMetricToImperial(double metricValue, String measurementType) {
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return UnitConversionService.convertWeightToImperial(metricValue);
      case 'height':
      case 'length':
      case 'head_circumference':
        return UnitConversionService.convertLengthToImperial(metricValue);
      case 'temperature':
        return (metricValue * 9/5) + 32;
      case 'volume':
        return metricValue * 0.033814;
      default:
        return metricValue;
    }
  }
  
  static String _getMetricUnit(String measurementType) {
    switch (measurementType.toLowerCase()) {
      case 'weight': return 'kg';
      case 'height':
      case 'length':
      case 'head_circumference': return 'cm';
      case 'temperature': return '°C';
      case 'volume': return 'ml';
      default: return '';
    }
  }
  
  static String _getImperialUnit(String measurementType) {
    switch (measurementType.toLowerCase()) {
      case 'weight': return 'lbs';
      case 'height':
      case 'length':
      case 'head_circumference': return 'in';
      case 'temperature': return '°F';
      case 'volume': return 'fl oz';
      default: return '';
    }
  }
  
  /// Migrate legacy data to new format
  static Map<String, dynamic> migrateLegacyMeasurement({
    required Map<String, dynamic> data,
    required String measurementType,
  }) {
    final legacyValue = data[measurementType] as double?;
    if (legacyValue == null) return data;
    
    // Assume legacy data is metric
    final enhancedData = prepareMeasurementForStorage(
      value: legacyValue,
      unit: _getMetricUnit(measurementType),
      measurementType: measurementType,
      wasEnteredInMetric: true,
    );
    
    // Merge with existing data
    return {...data, ...enhancedData};
  }
}

/// Data class for measurement display information
class MeasurementDisplayData {
  final double displayValue;
  final String displayUnit;
  final double originalValue;
  final String originalUnit;
  final bool wasEnteredAsMetric;
  final double metricValue;
  final double imperialValue;
  
  const MeasurementDisplayData({
    required this.displayValue,
    required this.displayUnit,
    required this.originalValue,
    required this.originalUnit,
    required this.wasEnteredAsMetric,
    required this.metricValue,
    required this.imperialValue,
  });
  
  factory MeasurementDisplayData.empty(String measurementType) {
    return MeasurementDisplayData(
      displayValue: 0.0,
      displayUnit: '',
      originalValue: 0.0,
      originalUnit: '',
      wasEnteredAsMetric: true,
      metricValue: 0.0,
      imperialValue: 0.0,
    );
  }
  
  /// Get formatted display string
  String get formattedDisplay => '${displayValue.toStringAsFixed(1)} $displayUnit';
  
  /// Get original entry string
  String get formattedOriginal => '${originalValue.toStringAsFixed(1)} $originalUnit';
  
  /// Check if this measurement has data
  bool get hasData => displayValue > 0;
  
  /// Get precision-preserved value for the current display preference
  double getValueForCurrentPreference(bool isMetric) {
    return isMetric ? metricValue : imperialValue;
  }
  
  /// Get unit for current display preference
  String getUnitForCurrentPreference(bool isMetric, String measurementType) {
    if (isMetric) {
      return EnhancedMeasurementStorageService._getMetricUnit(measurementType);
    } else {
      return EnhancedMeasurementStorageService._getImperialUnit(measurementType);
    }
  }
}