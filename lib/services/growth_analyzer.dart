import 'dart:math';
import '../models/baby_profile.dart';
import 'enhanced_percentile_calculator.dart';

/// Growth alert types for concerning patterns
enum GrowthAlertType {
  percentileCrossing,
  slowGrowthVelocity,
  rapidGrowthVelocity,
  belowNormalRange,
  aboveNormalRange,
  inconsistentMeasurements,
  missingMeasurements,
}

/// Growth alert with severity and recommendations
class GrowthAlert {
  final GrowthAlertType type;
  final String title;
  final String description;
  final String severity; // 'low', 'medium', 'high', 'critical'
  final List<String> recommendations;
  final DateTime detectedAt;
  final Map<String, dynamic> metadata;

  const GrowthAlert({
    required this.type,
    required this.title,
    required this.description,
    required this.severity,
    required this.recommendations,
    required this.detectedAt,
    this.metadata = const {},
  });

  /// Get color indicator based on severity
  String get colorIndicator {
    switch (severity) {
      case 'low':
        return '#4CAF50'; // Green
      case 'medium':
        return '#FF9800'; // Orange
      case 'high':
        return '#F44336'; // Red
      case 'critical':
        return '#9C27B0'; // Purple
      default:
        return '#757575'; // Grey
    }
  }

  /// Check if alert requires immediate attention
  bool get requiresImmediateAttention => severity == 'high' || severity == 'critical';
}

/// Comprehensive growth analysis result
class GrowthAnalysis {
  final List<PercentileTrend> trends;
  final GrowthVelocityAnalysis? velocityAnalysis;
  final List<GrowthAlert> alerts;
  final String overallAssessment;
  final Map<String, dynamic> percentileCrossingAnalysis;
  final String growthSummary;
  final List<String> recommendations;
  final DateTime analyzedAt;

  const GrowthAnalysis({
    required this.trends,
    this.velocityAnalysis,
    required this.alerts,
    required this.overallAssessment,
    required this.percentileCrossingAnalysis,
    required this.growthSummary,
    required this.recommendations,
    required this.analyzedAt,
  });

  /// Get the most severe alert level
  String get highestSeverity {
    if (alerts.isEmpty) return 'normal';
    
    final severityLevels = ['low', 'medium', 'high', 'critical'];
    String highest = 'low';
    
    for (final alert in alerts) {
      final currentIndex = severityLevels.indexOf(alert.severity);
      final highestIndex = severityLevels.indexOf(highest);
      if (currentIndex > highestIndex) {
        highest = alert.severity;
      }
    }
    
    return highest;
  }

  /// Check if any alerts require immediate attention
  bool get hasUrgentAlerts => alerts.any((alert) => alert.requiresImmediateAttention);

  /// Get count of alerts by severity
  Map<String, int> get alertCountBySeverity {
    final counts = <String, int>{'low': 0, 'medium': 0, 'high': 0, 'critical': 0};
    for (final alert in alerts) {
      counts[alert.severity] = (counts[alert.severity] ?? 0) + 1;
    }
    return counts;
  }
}

/// Comprehensive growth analyzer for pattern analysis and alerts
class GrowthAnalyzer {
  
  /// Analyze complete growth pattern for a baby
  static GrowthAnalysis analyzeGrowthPattern(
    List<MeasurementData> measurements,
    BabyProfile baby,
  ) {
    final now = DateTime.now();
    
    // Calculate percentile trends
    final trends = EnhancedPercentileCalculator.calculatePercentileTrends(measurements);
    
    // Calculate growth velocity analysis
    final velocityAnalysis = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
    
    // Analyze percentile crossing patterns
    final crossingAnalysis = EnhancedPercentileCalculator.analyzePercentileCrossing(trends);
    
    // Check for concerning patterns and generate alerts
    final alerts = checkForConcerns(measurements, baby);
    
    // Generate overall assessment
    final overallAssessment = _generateOverallAssessment(trends, velocityAnalysis, alerts);
    
    // Generate growth summary
    final growthSummary = generateGrowthSummary(trends, velocityAnalysis, crossingAnalysis);
    
    // Generate recommendations
    final recommendations = _generateRecommendations(trends, velocityAnalysis, alerts, baby);
    
    return GrowthAnalysis(
      trends: trends,
      velocityAnalysis: velocityAnalysis,
      alerts: alerts,
      overallAssessment: overallAssessment,
      percentileCrossingAnalysis: crossingAnalysis,
      growthSummary: growthSummary,
      recommendations: recommendations,
      analyzedAt: now,
    );
  }

  /// Check for concerning growth patterns and generate alerts
  static List<GrowthAlert> checkForConcerns(
    List<MeasurementData> measurements,
    BabyProfile baby,
  ) {
    final alerts = <GrowthAlert>[];
    final now = DateTime.now();
    
    if (measurements.isEmpty) {
      alerts.add(GrowthAlert(
        type: GrowthAlertType.missingMeasurements,
        title: 'No Growth Data',
        description: 'No growth measurements have been recorded yet.',
        severity: 'medium',
        recommendations: [
          'Record your baby\'s first measurements',
          'Take measurements regularly for trend analysis',
          'Consult your pediatrician for measurement guidance',
        ],
        detectedAt: now,
      ));
      return alerts;
    }

    // Calculate trends for analysis
    final trends = EnhancedPercentileCalculator.calculatePercentileTrends(measurements);
    
    // Check for percentile crossing concerns
    final crossingAnalysis = EnhancedPercentileCalculator.analyzePercentileCrossing(trends);
    if (crossingAnalysis['requiresAttention'] == true) {
      final magnitude = crossingAnalysis['crossingMagnitude'] as double;
      final direction = crossingAnalysis['crossingDirection'] as String;
      
      String severity;
      List<String> recommendations;
      
      if (magnitude > 50.0) {
        severity = 'critical';
        recommendations = [
          'Schedule immediate pediatric consultation',
          'Bring growth chart data to appointment',
          'Monitor for other symptoms or changes',
          'Continue regular measurements',
        ];
      } else if (magnitude > 25.0) {
        severity = 'high';
        recommendations = [
          'Schedule pediatric consultation within 1-2 weeks',
          'Monitor growth patterns closely',
          'Ensure accurate measurement techniques',
          'Track feeding and sleep patterns',
        ];
      } else {
        severity = 'medium';
        recommendations = [
          'Monitor growth trend over next few measurements',
          'Ensure consistent measurement conditions',
          'Discuss with pediatrician at next visit',
        ];
      }
      
      alerts.add(GrowthAlert(
        type: GrowthAlertType.percentileCrossing,
        title: 'Significant Percentile Change',
        description: 'Growth percentile has changed by ${magnitude.toStringAsFixed(1)} percentiles in a $direction direction.',
        severity: severity,
        recommendations: recommendations,
        detectedAt: now,
        metadata: {
          'magnitude': magnitude,
          'direction': direction,
          'totalChange': crossingAnalysis['totalChange'],
        },
      ));
    }

    // Check current percentile ranges
    if (trends.isNotEmpty) {
      final latestTrend = trends.last;
      
      if (latestTrend.percentile < 3.0) {
        alerts.add(GrowthAlert(
          type: GrowthAlertType.belowNormalRange,
          title: 'Below Normal Growth Range',
          description: 'Current ${latestTrend.measurementType} is below the 3rd percentile (${latestTrend.percentile.toStringAsFixed(1)}th percentile).',
          severity: 'high',
          recommendations: [
            'Consult pediatrician about growth concerns',
            'Review feeding patterns and nutrition',
            'Consider additional medical evaluation',
            'Monitor weight gain and development',
          ],
          detectedAt: now,
          metadata: {
            'percentile': latestTrend.percentile,
            'measurementType': latestTrend.measurementType,
            'value': latestTrend.value,
          },
        ));
      } else if (latestTrend.percentile > 97.0) {
        alerts.add(GrowthAlert(
          type: GrowthAlertType.aboveNormalRange,
          title: 'Above Normal Growth Range',
          description: 'Current ${latestTrend.measurementType} is above the 97th percentile (${latestTrend.percentile.toStringAsFixed(1)}th percentile).',
          severity: 'medium',
          recommendations: [
            'Discuss growth pattern with pediatrician',
            'Monitor for consistent growth trend',
            'Ensure measurements are accurate',
            'Consider family growth patterns',
          ],
          detectedAt: now,
          metadata: {
            'percentile': latestTrend.percentile,
            'measurementType': latestTrend.measurementType,
            'value': latestTrend.value,
          },
        ));
      }
    }

    // Check growth velocity concerns
    final velocityAnalysis = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
    if (velocityAnalysis != null && velocityAnalysis.requiresAttention) {
      String alertType = velocityAnalysis.velocityPercentile < 10.0 ? 'Slow' : 'Rapid';
      GrowthAlertType growthAlertType = velocityAnalysis.velocityPercentile < 10.0 
          ? GrowthAlertType.slowGrowthVelocity 
          : GrowthAlertType.rapidGrowthVelocity;
      
      String severity;
      List<String> recommendations;
      
      if (velocityAnalysis.velocityPercentile < 3.0 || velocityAnalysis.velocityPercentile > 97.0) {
        severity = 'high';
        recommendations = [
          'Schedule pediatric consultation',
          'Review nutrition and feeding patterns',
          'Monitor for underlying health issues',
          'Track growth velocity over time',
        ];
      } else {
        severity = 'medium';
        recommendations = [
          'Monitor growth velocity trends',
          'Ensure consistent measurement timing',
          'Discuss with pediatrician if pattern continues',
        ];
      }
      
      alerts.add(GrowthAlert(
        type: growthAlertType,
        title: '$alertType Growth Velocity',
        description: 'Growth velocity is at the ${velocityAnalysis.velocityPercentile.toStringAsFixed(1)}th percentile. ${velocityAnalysis.interpretation}',
        severity: severity,
        recommendations: recommendations,
        detectedAt: now,
        metadata: {
          'velocityPercentile': velocityAnalysis.velocityPercentile,
          'velocityPerMonth': velocityAnalysis.velocityPerMonth,
          'timePeriod': velocityAnalysis.timePeriod.inDays,
        },
      ));
    }

    // Check for measurement consistency and frequency
    if (measurements.length >= 2) {
      final sortedMeasurements = List<MeasurementData>.from(measurements)
        ..sort((a, b) => a.date.compareTo(b.date));
      
      // Check for large gaps in measurements
      final daysSinceLastMeasurement = now.difference(sortedMeasurements.last.date).inDays;
      if (daysSinceLastMeasurement > 90 && baby.ageInMonths < 12) {
        alerts.add(GrowthAlert(
          type: GrowthAlertType.missingMeasurements,
          title: 'Measurement Gap',
          description: 'It has been ${daysSinceLastMeasurement} days since the last measurement. Regular tracking is important for infants.',
          severity: 'low',
          recommendations: [
            'Schedule regular measurement sessions',
            'Track growth monthly for infants under 1 year',
            'Use consistent measurement techniques',
          ],
          detectedAt: now,
          metadata: {
            'daysSinceLastMeasurement': daysSinceLastMeasurement,
            'babyAgeInMonths': baby.ageInMonths,
          },
        ));
      }

      // Check for inconsistent measurements (large variations that might indicate measurement errors)
      final recentMeasurements = sortedMeasurements.where((m) => 
        now.difference(m.date).inDays <= 30
      ).toList();
      
      if (recentMeasurements.length >= 3) {
        final values = recentMeasurements.map((m) => m.value).toList();
        final mean = values.reduce((a, b) => a + b) / values.length;
        final variance = values.map((v) => pow(v - mean, 2)).reduce((a, b) => a + b) / values.length;
        final standardDeviation = sqrt(variance);
        final coefficientOfVariation = standardDeviation / mean;
        
        // If coefficient of variation is unusually high, flag as inconsistent
        if (coefficientOfVariation > 0.1) { // 10% variation threshold
          alerts.add(GrowthAlert(
            type: GrowthAlertType.inconsistentMeasurements,
            title: 'Inconsistent Measurements',
            description: 'Recent measurements show high variation. This might indicate measurement errors or rapid changes.',
            severity: 'low',
            recommendations: [
              'Ensure consistent measurement conditions',
              'Use the same scale/measuring device',
              'Measure at similar times of day',
              'Consider measurement technique training',
            ],
            detectedAt: now,
            metadata: {
              'coefficientOfVariation': coefficientOfVariation,
              'standardDeviation': standardDeviation,
              'measurementCount': recentMeasurements.length,
            },
          ));
        }
      }
    }

    return alerts;
  }

  /// Generate comprehensive growth summary text
  static String generateGrowthSummary(
    List<PercentileTrend> trends,
    GrowthVelocityAnalysis? velocityAnalysis,
    Map<String, dynamic> crossingAnalysis,
  ) {
    if (trends.isEmpty) {
      return 'No growth data available. Start tracking measurements to see growth patterns and analysis.';
    }

    final currentTrend = trends.last;
    final measurementType = currentTrend.measurementType;
    final currentPercentile = currentTrend.percentile;
    
    final summaryParts = <String>[];
    
    // Current status
    String percentileDescription;
    if (currentPercentile < 3.0) {
      percentileDescription = 'below the normal range (below 3rd percentile)';
    } else if (currentPercentile < 10.0) {
      percentileDescription = 'in the lower range (${currentPercentile.toStringAsFixed(1)}th percentile)';
    } else if (currentPercentile < 25.0) {
      percentileDescription = 'below average (${currentPercentile.toStringAsFixed(1)}th percentile)';
    } else if (currentPercentile <= 75.0) {
      percentileDescription = 'in the average range (${currentPercentile.toStringAsFixed(1)}th percentile)';
    } else if (currentPercentile <= 90.0) {
      percentileDescription = 'above average (${currentPercentile.toStringAsFixed(1)}th percentile)';
    } else if (currentPercentile <= 97.0) {
      percentileDescription = 'in the higher range (${currentPercentile.toStringAsFixed(1)}th percentile)';
    } else {
      percentileDescription = 'above the normal range (above 97th percentile)';
    }
    
    summaryParts.add('Your baby\'s current $measurementType is $percentileDescription.');

    // Trend analysis
    if (trends.length >= 2) {
      final crossingDirection = crossingAnalysis['crossingDirection'] as String;
      final totalChange = crossingAnalysis['totalChange'] as double;
      
      if (crossingDirection == 'stable') {
        summaryParts.add('Growth is tracking consistently along the same percentile curve.');
      } else if (crossingDirection == 'upward') {
        if (totalChange > 25.0) {
          summaryParts.add('There has been significant upward growth, crossing ${totalChange.toStringAsFixed(1)} percentile points.');
        } else {
          summaryParts.add('Growth is trending upward, showing positive development.');
        }
      } else if (crossingDirection == 'downward') {
        if (totalChange.abs() > 25.0) {
          summaryParts.add('There has been a concerning downward trend, dropping ${totalChange.abs().toStringAsFixed(1)} percentile points.');
        } else {
          summaryParts.add('Growth is trending slightly downward, which should be monitored.');
        }
      }
    }

    // Velocity analysis
    if (velocityAnalysis != null) {
      final velocityCategory = velocityAnalysis.velocityCategory;
      if (velocityCategory.contains('Normal')) {
        summaryParts.add('Growth velocity is within the normal range.');
      } else if (velocityCategory.contains('Slow')) {
        summaryParts.add('Growth velocity is slower than average and should be monitored.');
      } else if (velocityCategory.contains('Rapid')) {
        summaryParts.add('Growth velocity is faster than average.');
      }
    }

    return summaryParts.join(' ');
  }

  /// Generate overall assessment based on all factors
  static String _generateOverallAssessment(
    List<PercentileTrend> trends,
    GrowthVelocityAnalysis? velocityAnalysis,
    List<GrowthAlert> alerts,
  ) {
    if (trends.isEmpty) {
      return 'Insufficient data for assessment';
    }

    final criticalAlerts = alerts.where((a) => a.severity == 'critical').length;
    final highAlerts = alerts.where((a) => a.severity == 'high').length;
    final mediumAlerts = alerts.where((a) => a.severity == 'medium').length;

    if (criticalAlerts > 0) {
      return 'Critical - Immediate medical attention recommended';
    } else if (highAlerts > 0) {
      return 'Concerning - Medical consultation recommended';
    } else if (mediumAlerts > 1) {
      return 'Monitor closely - Multiple areas of concern';
    } else if (mediumAlerts > 0) {
      return 'Monitor closely - Some concerns noted';
    } else {
      final currentPercentile = trends.last.percentile;
      if (currentPercentile >= 10.0 && currentPercentile <= 90.0) {
        return 'Normal growth pattern';
      } else {
        return 'Growth within acceptable range';
      }
    }
  }

  /// Generate personalized recommendations
  static List<String> _generateRecommendations(
    List<PercentileTrend> trends,
    GrowthVelocityAnalysis? velocityAnalysis,
    List<GrowthAlert> alerts,
    BabyProfile baby,
  ) {
    final recommendations = <String>[];
    final recommendationSet = <String>{}; // To avoid duplicates

    // Add alert-specific recommendations
    for (final alert in alerts) {
      for (final rec in alert.recommendations) {
        recommendationSet.add(rec);
      }
    }

    // Add general recommendations based on baby's age
    if (baby.ageInMonths < 6) {
      recommendationSet.add('Track growth weekly for infants under 6 months');
      recommendationSet.add('Ensure adequate feeding frequency and duration');
    } else if (baby.ageInMonths < 12) {
      recommendationSet.add('Monitor growth monthly for infants 6-12 months');
      recommendationSet.add('Track introduction of solid foods and growth response');
    } else {
      recommendationSet.add('Monitor growth every 2-3 months for toddlers');
      recommendationSet.add('Ensure balanced nutrition and active play');
    }

    // Add measurement quality recommendations
    if (trends.length < 3) {
      recommendationSet.add('Take more frequent measurements for better trend analysis');
    }

    recommendationSet.add('Use consistent measurement techniques and timing');
    recommendationSet.add('Keep a growth diary with feeding and sleep patterns');
    recommendationSet.add('Discuss growth patterns with your pediatrician regularly');

    return recommendationSet.toList();
  }

  /// Get percentile interpretation with color-coded indicators
  static Map<String, dynamic> getPercentileInterpretation(double percentile) {
    String category;
    String interpretation;
    String colorCode;
    String textColor;
    bool requiresAttention;

    if (percentile < 3.0) {
      category = "Below Normal";
      interpretation = "Below 3rd percentile - medical consultation recommended";
      colorCode = "#F44336"; // Red
      textColor = "#FFFFFF"; // White text
      requiresAttention = true;
    } else if (percentile < 10.0) {
      category = "Low Normal";
      interpretation = "Below 10th percentile - monitor closely";
      colorCode = "#FF9800"; // Orange
      textColor = "#FFFFFF"; // White text
      requiresAttention = true;
    } else if (percentile < 25.0) {
      category = "Lower Average";
      interpretation = "Below average but within normal range";
      colorCode = "#FFC107"; // Amber
      textColor = "#000000"; // Black text
      requiresAttention = false;
    } else if (percentile <= 75.0) {
      category = "Average";
      interpretation = "Within average range";
      colorCode = "#4CAF50"; // Green
      textColor = "#FFFFFF"; // White text
      requiresAttention = false;
    } else if (percentile <= 90.0) {
      category = "Higher Average";
      interpretation = "Above average but within normal range";
      colorCode = "#2196F3"; // Blue
      textColor = "#FFFFFF"; // White text
      requiresAttention = false;
    } else if (percentile <= 97.0) {
      category = "High Normal";
      interpretation = "Above 90th percentile - monitor for consistency";
      colorCode = "#9C27B0"; // Purple
      textColor = "#FFFFFF"; // White text
      requiresAttention = false;
    } else {
      category = "Above Normal";
      interpretation = "Above 97th percentile - consider medical consultation";
      colorCode = "#E91E63"; // Pink
      textColor = "#FFFFFF"; // White text
      requiresAttention = true;
    }

    return {
      'category': category,
      'interpretation': interpretation,
      'colorCode': colorCode,
      'textColor': textColor,
      'requiresAttention': requiresAttention,
      'percentile': percentile,
    };
  }

  /// Analyze growth velocity and compare to WHO standards
  static GrowthVelocityAnalysis? analyzeGrowthVelocity(List<MeasurementData> measurements) {
    return EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
  }

  /// Generate alert summary for dashboard display
  static Map<String, dynamic> generateAlertSummary(List<GrowthAlert> alerts) {
    final summary = {
      'totalAlerts': alerts.length,
      'criticalCount': 0,
      'highCount': 0,
      'mediumCount': 0,
      'lowCount': 0,
      'hasUrgentAlerts': false,
      'mostSevereAlert': null,
      'alertTypes': <String>[],
    };

    if (alerts.isEmpty) {
      return summary;
    }

    // Count alerts by severity
    for (final alert in alerts) {
      switch (alert.severity) {
        case 'critical':
          summary['criticalCount'] = (summary['criticalCount'] as int) + 1;
          break;
        case 'high':
          summary['highCount'] = (summary['highCount'] as int) + 1;
          break;
        case 'medium':
          summary['mediumCount'] = (summary['mediumCount'] as int) + 1;
          break;
        case 'low':
          summary['lowCount'] = (summary['lowCount'] as int) + 1;
          break;
      }
      
      // Track alert types
      final alertTypes = summary['alertTypes'] as List<String>;
      if (!alertTypes.contains(alert.type.name)) {
        alertTypes.add(alert.type.name);
      }
    }

    // Find most severe alert
    final severityOrder = ['critical', 'high', 'medium', 'low'];
    GrowthAlert? mostSevere;
    
    for (final severity in severityOrder) {
      final alertsOfSeverity = alerts.where((a) => a.severity == severity);
      if (alertsOfSeverity.isNotEmpty) {
        mostSevere = alertsOfSeverity.first;
        break;
      }
    }

    summary['mostSevereAlert'] = mostSevere;
    summary['hasUrgentAlerts'] = alerts.any((a) => a.requiresImmediateAttention);

    return summary;
  }
}