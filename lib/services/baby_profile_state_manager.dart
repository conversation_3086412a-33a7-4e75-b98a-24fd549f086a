import 'package:flutter/foundation.dart';
import '../models/baby_profile.dart';
import '../services/supabase_service.dart';
import '../services/auth_service.dart';

/// Centralized state manager for baby profiles
/// Ensures consistent baby profile data across all screens
class BabyProfileStateManager extends ChangeNotifier {
  static final BabyProfileStateManager _instance = BabyProfileStateManager._internal();
  factory BabyProfileStateManager() => _instance;
  BabyProfileStateManager._internal();

  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService = AuthService();

  // Core state
  List<BabyProfile> _allBabies = [];
  BabyProfile? _activeBaby;
  bool _isLoading = false;
  String? _error;
  DateTime? _lastUpdated;

  // Getters
  List<BabyProfile> get allBabies => List.unmodifiable(_allBabies);
  BabyProfile? get activeBaby => _activeBaby;
  bool get isLoading => _isLoading;
  String? get error => _error;
  DateTime? get lastUpdated => _lastUpdated;
  bool get hasActiveBaby => _activeBaby != null;
  bool get hasBabies => _allBabies.isNotEmpty;

  /// Initialize the state manager - loads babies and sets active baby
  Future<void> initialize() async {
    debugPrint('🔄 BabyProfileStateManager: Initializing...');
    await _loadBabies();
    await _setActiveBabyFromDatabase();
    debugPrint('✅ BabyProfileStateManager: Initialized with ${_allBabies.length} babies, active: ${_activeBaby?.name}');
  }

  /// Refresh all baby data
  Future<void> refresh() async {
    debugPrint('🔄 BabyProfileStateManager: Refreshing...');
    await _loadBabies();
    await _setActiveBabyFromDatabase();
    debugPrint('✅ BabyProfileStateManager: Refreshed');
  }

  /// Load all babies for current user
  Future<void> _loadBabies() async {
    try {
      _setLoading(true);
      _error = null;

      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw Exception('No authenticated user found');
      }

      debugPrint('📥 Loading babies for user: ${currentUser.id}');
      
      final response = await _supabaseService.select(
        'baby_profiles',
        filters: {'user_id': currentUser.id},
        orderBy: 'created_at',
        ascending: false,
      );

      debugPrint('📊 Raw database response: $response');

      final babies = response
          .map((data) => BabyProfile.fromJson(data))
          .toList();

      _allBabies = babies;
      _lastUpdated = DateTime.now();
      debugPrint('✅ Loaded ${babies.length} babies');
      for (var baby in babies) {
        debugPrint('   - ${baby.name} (ID: ${baby.id})');
      }
      
    } catch (e) {
      _error = 'Failed to load babies: $e';
      debugPrint('❌ Error loading babies: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Set active baby from database
  Future<void> _setActiveBabyFromDatabase() async {
    try {
      // First try to get active baby from database
      final activeBabyId = await _supabaseService.getActiveBabyId();
      
      if (activeBabyId != null) {
        // Find the active baby in our loaded babies
        final baby = _allBabies.where((b) => b.id == activeBabyId).firstOrNull;
        if (baby != null) {
          debugPrint('✅ Found active baby from database: ${baby.name} (${baby.id})');
          _activeBaby = baby;
          notifyListeners();
          return;
        } else {
          debugPrint('⚠️ Active baby ID $activeBabyId not found in loaded babies');
        }
      }

      // Fallback: Set first baby as active if we have babies but no active baby
      if (_allBabies.isNotEmpty && _activeBaby == null) {
        final firstBaby = _allBabies.first;
        debugPrint('📝 Setting first baby as active: ${firstBaby.name} (${firstBaby.id})');
        await setActiveBaby(firstBaby);
      }
    } catch (e) {
      debugPrint('❌ Error setting active baby from database: $e');
    }
  }

  /// Set a specific baby as active
  Future<void> setActiveBaby(BabyProfile baby) async {
    // Check if baby exists in loaded babies
    final babyExists = _allBabies.any((b) => b.id == baby.id);
    if (!babyExists) {
      debugPrint('⚠️ Baby not found in loaded list, refreshing babies...');
      // Try refreshing babies first
      await _loadBabies();
      
      // Check again after refresh
      if (!_allBabies.any((b) => b.id == baby.id)) {
        throw Exception('Baby not found: ${baby.name} (${baby.id})');
      }
    }

    try {
      debugPrint('🔄 Setting active baby: ${baby.name} (${baby.id})');
      
      // Update database
      await _supabaseService.setActiveBaby(baby.id);
      
      // Update local state with the baby from our loaded list (ensure consistency)
      final validBaby = _allBabies.firstWhere((b) => b.id == baby.id);
      _activeBaby = validBaby;
      _error = null;
      
      debugPrint('✅ Active baby set to: ${validBaby.name}');
      notifyListeners();
    } catch (e) {
      _error = 'Failed to set active baby: $e';
      debugPrint('❌ Error setting active baby: $e');
      notifyListeners();
      rethrow;
    }
  }

  /// Add a new baby and optionally make it active
  Future<BabyProfile> addBaby(Map<String, dynamic> babyData, {bool makeActive = true}) async {
    try {
      _setLoading(true);
      debugPrint('📝 Adding new baby: ${babyData['name']}');
      debugPrint('👤 User ID: ${babyData['user_id']}');
      debugPrint('🔢 Current babies count: ${_allBabies.length}');
      debugPrint('🎯 Make active: $makeActive');
      
      // Insert into database
      final result = await _supabaseService.insert('baby_profiles', babyData);
      debugPrint('✅ Database insert result: $result');
      
      if (result.isEmpty) {
        throw Exception('Failed to create baby profile');
      }

      // Fetch the complete profile
      final babyResponse = await _supabaseService.select(
        'baby_profiles',
        filters: {'id': result['id']},
      );
      debugPrint('👶 Fetched baby profile: $babyResponse');
      
      if (babyResponse.isEmpty) {
        throw Exception('Failed to fetch created baby profile');
      }

      final newBaby = BabyProfile.fromJson(babyResponse.first);
      debugPrint('🆕 New baby created: ${newBaby.name} (${newBaby.id})');
      
      // Add to local state
      _allBabies.insert(0, newBaby); // Add at beginning (most recent first)
      debugPrint('📊 Updated babies count: ${_allBabies.length}');
      
      // Make active if requested
      if (makeActive) {
        debugPrint('🎯 Setting new baby as active...');
        await setActiveBaby(newBaby);
      }
      
      debugPrint('✅ Baby added successfully: ${newBaby.name} (${newBaby.id})');
      return newBaby;
    } catch (e) {
      _error = 'Failed to add baby: $e';
      debugPrint('❌ Error adding baby: $e');
      notifyListeners();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing baby
  Future<BabyProfile> updateBaby(String babyId, Map<String, dynamic> updates) async {
    try {
      _setLoading(true);
      debugPrint('🔄 Updating baby: $babyId');
      
      // Update in database
      await _supabaseService.update('baby_profiles', updates, 'id', babyId);
      
      // Fetch updated profile
      final babyResponse = await _supabaseService.select(
        'baby_profiles',
        filters: {'id': babyId},
      );
      
      if (babyResponse.isEmpty) {
        throw Exception('Failed to fetch updated baby profile');
      }

      final updatedBaby = BabyProfile.fromJson(babyResponse.first);
      
      // Update local state
      final index = _allBabies.indexWhere((b) => b.id == babyId);
      if (index != -1) {
        _allBabies[index] = updatedBaby;
      }
      
      // Update active baby if it's the same baby
      if (_activeBaby?.id == babyId) {
        _activeBaby = updatedBaby;
      }
      
      debugPrint('✅ Baby updated successfully: ${updatedBaby.name}');
      notifyListeners();
      return updatedBaby;
    } catch (e) {
      _error = 'Failed to update baby: $e';
      debugPrint('❌ Error updating baby: $e');
      notifyListeners();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a baby
  Future<void> deleteBaby(String babyId) async {
    try {
      _setLoading(true);
      debugPrint('🗑️ Deleting baby: $babyId');
      
      // Delete from database
      await _supabaseService.delete('baby_profiles', 'id', babyId);
      
      // Remove from local state
      _allBabies.removeWhere((b) => b.id == babyId);
      
      // If this was the active baby, set a new active baby
      if (_activeBaby?.id == babyId) {
        _activeBaby = null;
        if (_allBabies.isNotEmpty) {
          await setActiveBaby(_allBabies.first);
        }
      }
      
      debugPrint('✅ Baby deleted successfully');
      notifyListeners();
    } catch (e) {
      _error = 'Failed to delete baby: $e';
      debugPrint('❌ Error deleting baby: $e');
      notifyListeners();
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Get active baby ID - guaranteed to return valid ID or null
  String? getActiveBabyId() {
    return _activeBaby?.id;
  }

  /// Get active baby profile - guaranteed to return valid profile or null
  BabyProfile? getActiveBabyProfile() {
    return _activeBaby;
  }

  /// Clear all state (for logout)
  void clearState() {
    debugPrint('🔄 BabyProfileStateManager: Clearing state');
    _allBabies.clear();
    _activeBaby = null;
    _error = null;
    _lastUpdated = null;
    _isLoading = false;
    notifyListeners();
  }

  /// Set loading state
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// Get baby by ID
  BabyProfile? getBabyById(String id) {
    return _allBabies.where((b) => b.id == id).firstOrNull;
  }

  /// Check if a specific baby exists
  bool hasBaby(String id) {
    return _allBabies.any((b) => b.id == id);
  }

  /// Get babies count
  int get babiesCount => _allBabies.length;

  @override
  void dispose() {
    debugPrint('🔄 BabyProfileStateManager: Disposing');
    super.dispose();
  }
}

/// Extension to add null safety helper
extension IterableExtension<T> on Iterable<T> {
  T? get firstOrNull {
    final iterator = this.iterator;
    if (iterator.moveNext()) {
      return iterator.current;
    }
    return null;
  }
}
