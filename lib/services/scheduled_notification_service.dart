import 'package:flutter/material.dart';
import 'dart:async';

import '../core/app_export.dart';
import '../models/notification_item.dart';
import '../models/scheduled_activity.dart';
import './unified_notification_service.dart';
import './supabase_service.dart';
import './baby_profile_state_manager.dart';

/// Service to check for upcoming scheduled activities and create notifications
class ScheduledNotificationService {
  static const Duration _checkInterval = Duration(minutes: 5); // Check every 5 minutes
  static const int _lookAheadMinutes = 60; // Look ahead 1 hour
  
  static ScheduledNotificationService? _instance;
  static ScheduledNotificationService get instance => _instance ??= ScheduledNotificationService._();
  
  ScheduledNotificationService._();

  Timer? _checkTimer;
  bool _isRunning = false;
  final UnifiedNotificationService _notificationService = UnifiedNotificationService.instance;
  final SupabaseService _supabaseService = SupabaseService();
  final BabyProfileStateManager _babyProfileManager = BabyProfileStateManager();
  
  // Track processed notifications to avoid duplicates
  final Set<String> _processedNotifications = <String>{};

  /// Start the scheduled notification service
  void start() {
    if (_isRunning) return;
    
    debugPrint('🔔 Starting scheduled notification service...');
    _isRunning = true;
    
    // Check immediately
    _checkScheduledActivities();
    
    // Set up periodic checks
    _checkTimer = Timer.periodic(_checkInterval, (timer) {
      _checkScheduledActivities();
    });
    
    debugPrint('✅ Scheduled notification service started');
  }

  /// Stop the scheduled notification service
  void stop() {
    if (!_isRunning) return;
    
    debugPrint('🛑 Stopping scheduled notification service...');
    _checkTimer?.cancel();
    _checkTimer = null;
    _isRunning = false;
    _processedNotifications.clear();
    
    debugPrint('✅ Scheduled notification service stopped');
  }

  /// Check for upcoming scheduled activities and create notifications
  Future<void> _checkScheduledActivities() async {
    try {
      // Get current active baby
      final activeBaby = _babyProfileManager.activeBaby;
      if (activeBaby == null) {
        debugPrint('⚠️ No active baby found, skipping scheduled notification check');
        return;
      }

      debugPrint('🔍 Checking scheduled activities for baby: ${activeBaby.name}');
      
      // Get upcoming scheduled activities
      final now = DateTime.now();
      final lookAheadTime = now.add(Duration(minutes: _lookAheadMinutes));
      
      final upcomingActivities = await _supabaseService.getScheduledActivitiesForDateRange(
        activeBaby.id,
        now,
        lookAheadTime,
      );
      
      debugPrint('📅 Found ${upcomingActivities.length} upcoming activities');
      
      for (final activity in upcomingActivities) {
        await _processScheduledActivity(activity, activeBaby);
      }
      
    } catch (e) {
      debugPrint('❌ Error checking scheduled activities: $e');
    }
  }

  /// Process a single scheduled activity for notifications
  Future<void> _processScheduledActivity(ScheduledActivity activity, BabyProfile baby) async {
    try {
      // Skip if already completed
      if (activity.isCompleted) {
        return;
      }

      final now = DateTime.now();
      final scheduledTime = activity.scheduledTime;
      final notificationTime = scheduledTime.subtract(Duration(minutes: activity.notifyBeforeMinutes));
      
      // Check if it's time to send the notification
      if (now.isAfter(notificationTime) && now.isBefore(scheduledTime)) {
        // Create unique key for this notification
        final notificationKey = '${activity.id}_${activity.notifyBeforeMinutes}';
        
        // Skip if already processed
        if (_processedNotifications.contains(notificationKey)) {
          return;
        }
        
        // Create notification
        await _createScheduledActivityNotification(activity, baby);
        
        // Mark as processed
        _processedNotifications.add(notificationKey);
        
        debugPrint('✅ Created notification for scheduled activity: ${activity.title}');
      }
      
      // Also check for overdue activities (past scheduled time and not completed)
      if (now.isAfter(scheduledTime)) {
        final overdueKey = '${activity.id}_overdue';
        
        if (!_processedNotifications.contains(overdueKey)) {
          await _createOverdueActivityNotification(activity, baby);
          _processedNotifications.add(overdueKey);
          
          debugPrint('⚠️ Created overdue notification for: ${activity.title}');
        }
      }
      
    } catch (e) {
      debugPrint('❌ Error processing scheduled activity ${activity.id}: $e');
    }
  }

  /// Create notification for upcoming scheduled activity
  Future<void> _createScheduledActivityNotification(ScheduledActivity activity, BabyProfile baby) async {
    final timeUntil = activity.scheduledTime.difference(DateTime.now());
    final minutesUntil = timeUntil.inMinutes;
    
    String timeMessage;
    if (minutesUntil <= 5) {
      timeMessage = 'in ${minutesUntil} minutes';
    } else if (minutesUntil <= 60) {
      timeMessage = 'in ${minutesUntil} minutes';
    } else {
      final hoursUntil = timeUntil.inHours;
      timeMessage = 'in ${hoursUntil} hour${hoursUntil > 1 ? 's' : ''}';
    }
    
    await _notificationService.createNotification(
      title: '${activity.title} Reminder',
      message: '${activity.title} for ${baby.name} is scheduled $timeMessage',
      type: _getNotificationTypeFromActivity(activity.type),
      priority: _getNotificationPriority(activity.type),
      scheduledFor: DateTime.now(),
      babyId: baby.id,
      metadata: {
        'source': 'scheduled_activity',
        'activity_id': activity.id,
        'activity_type': activity.type.value,
        'scheduled_time': activity.scheduledTime.toIso8601String(),
        'minutes_until': minutesUntil,
      },
    );
  }

  /// Create notification for overdue scheduled activity
  Future<void> _createOverdueActivityNotification(ScheduledActivity activity, BabyProfile baby) async {
    final timeSince = DateTime.now().difference(activity.scheduledTime);
    final minutesSince = timeSince.inMinutes;
    
    String timeMessage;
    if (minutesSince <= 60) {
      timeMessage = '${minutesSince} minutes ago';
    } else {
      final hoursSince = timeSince.inHours;
      timeMessage = '${hoursSince} hour${hoursSince > 1 ? 's' : ''} ago';
    }
    
    await _notificationService.createNotification(
      title: '⚠️ Overdue: ${activity.title}',
      message: '${activity.title} for ${baby.name} was scheduled $timeMessage',
      type: _getNotificationTypeFromActivity(activity.type),
      priority: NotificationPriority.high,
      scheduledFor: DateTime.now(),
      babyId: baby.id,
      metadata: {
        'source': 'scheduled_activity_overdue',
        'activity_id': activity.id,
        'activity_type': activity.type.value,
        'scheduled_time': activity.scheduledTime.toIso8601String(),
        'minutes_overdue': minutesSince,
      },
    );
  }

  /// Map scheduled activity type to notification type
  NotificationType _getNotificationTypeFromActivity(ScheduledActivityType activityType) {
    switch (activityType) {
      case ScheduledActivityType.feedingReminder:
      case ScheduledActivityType.nursingSession:
      case ScheduledActivityType.bottleFeeding:
        return NotificationType.feeding;
      case ScheduledActivityType.sleepReminder:
      case ScheduledActivityType.napTime:
        return NotificationType.sleep;
      case ScheduledActivityType.medicationReminder:
        return NotificationType.medicine;
      case ScheduledActivityType.vaccinationAppointment:
        return NotificationType.vaccination;
      case ScheduledActivityType.doctorAppointment:
        return NotificationType.appointment;
      default:
        return NotificationType.custom;
    }
  }

  /// Get notification priority based on scheduled activity type
  NotificationPriority _getNotificationPriority(ScheduledActivityType type) {
    switch (type) {
      case ScheduledActivityType.medicationReminder:
        return NotificationPriority.urgent;
      case ScheduledActivityType.doctorAppointment:
      case ScheduledActivityType.vaccinationAppointment:
        return NotificationPriority.high;
      case ScheduledActivityType.feedingReminder:
      case ScheduledActivityType.sleepReminder:
        return NotificationPriority.normal;
      default:
        return NotificationPriority.low;
    }
  }

  /// Clean up old processed notifications (call periodically)
  void cleanupProcessedNotifications() {
    // Keep only recent notifications (last 24 hours worth)
    // This is a simple cleanup - in a real app you might want more sophisticated logic
    if (_processedNotifications.length > 1000) {
      _processedNotifications.clear();
      debugPrint('🧹 Cleaned up processed notifications cache');
    }
  }

  /// Get service status
  Map<String, dynamic> get status => {
    'isRunning': _isRunning,
    'checkInterval': _checkInterval.inMinutes,
    'lookAheadMinutes': _lookAheadMinutes,
    'processedNotificationsCount': _processedNotifications.length,
  };
}