import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'dart:math';

import '../models/notification_item.dart';
import '../models/baby_profile.dart';
import '../models/activity_log.dart';
import '../models/scheduled_activity.dart';

/// Enhanced notification service that manages all notifications in the app
class EnhancedNotificationService extends ChangeNotifier {
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _notificationsKey = 'stored_notifications';
  static const String _notificationCounterKey = 'notification_counter';

  static EnhancedNotificationService? _instance;
  static EnhancedNotificationService get instance => _instance ??= EnhancedNotificationService._();
  
  EnhancedNotificationService._();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  bool _notificationsEnabled = true;
  List<NotificationItem> _notifications = [];
  int _notificationCounter = 0;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get notificationsEnabled => _notificationsEnabled;
  List<NotificationItem> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _notifications.where((n) => !n.isRead).length;
  int get totalCount => _notifications.length;

  /// Initialize the notification service
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _loadNotifications();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('EnhancedNotificationService initialization error: $e');
      _isInitialized = true;
    }
  }

  /// Load notification settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      _notificationsEnabled = _prefs?.getBool(_notificationsEnabledKey) ?? true;
      _notificationCounter = _prefs?.getInt(_notificationCounterKey) ?? 0;
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  /// Load stored notifications from shared preferences
  Future<void> _loadNotifications() async {
    try {
      final notificationsJson = _prefs?.getString(_notificationsKey);
      if (notificationsJson != null) {
        final List<dynamic> notificationsList = json.decode(notificationsJson);
        _notifications = notificationsList
            .map((json) => NotificationItem.fromJson(json as Map<String, dynamic>))
            .toList();
        
        // Sort by creation time (newest first)
        _notifications.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      }
    } catch (e) {
      debugPrint('Error loading notifications: $e');
      _notifications = [];
    }
  }

  /// Save notifications to shared preferences
  Future<void> _saveNotifications() async {
    try {
      final notificationsJson = json.encode(
        _notifications.map((n) => n.toJson()).toList(),
      );
      await _prefs?.setString(_notificationsKey, notificationsJson);
    } catch (e) {
      debugPrint('Error saving notifications: $e');
    }
  }

  /// Set master notification setting
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled == enabled) return;
    
    _notificationsEnabled = enabled;
    await _prefs?.setBool(_notificationsEnabledKey, enabled);
    notifyListeners();
  }

  /// Create a new notification
  Future<NotificationItem> createNotification({
    required String title,
    required String message,
    required NotificationType type,
    NotificationPriority priority = NotificationPriority.normal,
    DateTime? scheduledFor,
    String? babyId,
    Map<String, dynamic>? metadata,
  }) async {
    final notification = NotificationItem(
      id: _generateNotificationId(),
      title: title,
      message: message,
      type: type,
      priority: priority,
      createdAt: DateTime.now(),
      scheduledFor: scheduledFor,
      babyId: babyId,
      metadata: metadata,
    );

    // Add to list
    _notifications.insert(0, notification);
    
    // Limit total notifications to prevent storage bloat
    if (_notifications.length > 100) {
      _notifications = _notifications.take(100).toList();
    }

    await _saveNotifications();
    notifyListeners();

    // If notifications are enabled, show system notification
    if (_notificationsEnabled) {
      await _showSystemNotification(notification);
    }

    return notification;
  }

  /// Generate unique notification ID
  String _generateNotificationId() {
    _notificationCounter++;
    _prefs?.setInt(_notificationCounterKey, _notificationCounter);
    return 'notification_${DateTime.now().millisecondsSinceEpoch}_$_notificationCounter';
  }

  /// Show system notification (placeholder for actual implementation)
  Future<void> _showSystemNotification(NotificationItem notification) async {
    // In a real implementation, this would use flutter_local_notifications
    // or firebase_messaging to show actual system notifications
    debugPrint('System notification: ${notification.title} - ${notification.message}');
  }

  /// Get all notifications
  Future<List<NotificationItem>> getAllNotifications() async {
    return List.unmodifiable(_notifications);
  }

  /// Get unread notifications
  Future<List<NotificationItem>> getUnreadNotifications() async {
    return _notifications.where((n) => !n.isRead).toList();
  }

  /// Get notifications for today
  Future<List<NotificationItem>> getTodayNotifications() async {
    final today = DateTime.now();
    return _notifications.where((n) {
      final notificationDate = n.createdAt;
      return notificationDate.year == today.year &&
             notificationDate.month == today.month &&
             notificationDate.day == today.day;
    }).toList();
  }

  /// Get notifications by type
  Future<List<NotificationItem>> getNotificationsByType(NotificationType type) async {
    return _notifications.where((n) => n.type == type).toList();
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      await _saveNotifications();
      notifyListeners();
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    _notifications = _notifications.map((n) => n.copyWith(isRead: true)).toList();
    await _saveNotifications();
    notifyListeners();
  }

  /// Delete notification
  Future<void> deleteNotification(String notificationId) async {
    _notifications.removeWhere((n) => n.id == notificationId);
    await _saveNotifications();
    notifyListeners();
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    _notifications.clear();
    await _saveNotifications();
    notifyListeners();
  }

  /// Get notification icon based on current state
  IconData get notificationIcon {
    if (!_notificationsEnabled) return Icons.notifications_off;
    if (unreadCount > 0) return Icons.notifications_active;
    return Icons.notifications;
  }

  /// Get notification summary
  String get notificationSummary {
    if (!_notificationsEnabled) return 'Notifications disabled';
    if (unreadCount == 0) return 'No unread notifications';
    return '$unreadCount unread notification${unreadCount > 1 ? 's' : ''}';
  }

  // Convenience methods for creating specific types of notifications

  /// Create feeding reminder notification
  Future<NotificationItem> createFeedingReminder({
    required String babyName,
    required String babyId,
    DateTime? scheduledFor,
  }) async {
    return createNotification(
      title: 'Feeding Time',
      message: 'Time to feed $babyName',
      type: NotificationType.feeding,
      priority: NotificationPriority.high,
      scheduledFor: scheduledFor,
      babyId: babyId,
      metadata: {'baby_name': babyName},
    );
  }

  /// Create sleep reminder notification
  Future<NotificationItem> createSleepReminder({
    required String babyName,
    required String babyId,
    DateTime? scheduledFor,
  }) async {
    return createNotification(
      title: 'Sleep Time',
      message: 'Time for $babyName to sleep',
      type: NotificationType.sleep,
      priority: NotificationPriority.high,
      scheduledFor: scheduledFor,
      babyId: babyId,
      metadata: {'baby_name': babyName},
    );
  }

  /// Create milestone notification
  Future<NotificationItem> createMilestoneNotification({
    required String babyName,
    required String babyId,
    required String milestone,
  }) async {
    return createNotification(
      title: 'New Milestone!',
      message: '$babyName has reached a new milestone: $milestone',
      type: NotificationType.milestone,
      priority: NotificationPriority.normal,
      babyId: babyId,
      metadata: {'baby_name': babyName, 'milestone': milestone},
    );
  }

  /// Create AI insight notification
  Future<NotificationItem> createAIInsightNotification({
    required String babyName,
    required String babyId,
    required String insight,
  }) async {
    return createNotification(
      title: 'New AI Insight',
      message: 'New insights available for $babyName: $insight',
      type: NotificationType.aiInsight,
      priority: NotificationPriority.normal,
      babyId: babyId,
      metadata: {'baby_name': babyName, 'insight': insight},
    );
  }

  /// Create medicine reminder notification
  Future<NotificationItem> createMedicineReminder({
    required String babyName,
    required String babyId,
    required String medicineName,
    DateTime? scheduledFor,
  }) async {
    return createNotification(
      title: 'Medicine Time',
      message: 'Time to give $medicineName to $babyName',
      type: NotificationType.medicine,
      priority: NotificationPriority.urgent,
      scheduledFor: scheduledFor,
      babyId: babyId,
      metadata: {'baby_name': babyName, 'medicine_name': medicineName},
    );
  }

  /// Create daily summary notification
  Future<NotificationItem> createDailySummaryNotification({
    required String babyName,
    required String babyId,
    required Map<String, dynamic> summaryData,
  }) async {
    final activities = summaryData['totalActivities'] ?? 0;
    return createNotification(
      title: 'Daily Summary',
      message: '$babyName had $activities activities today',
      type: NotificationType.dailySummary,
      priority: NotificationPriority.low,
      babyId: babyId,
      metadata: {'baby_name': babyName, 'summary_data': summaryData},
    );
  }

  /// Create appointment reminder notification
  Future<NotificationItem> createAppointmentReminder({
    required String title,
    required String description,
    required String babyId,
    DateTime? scheduledFor,
  }) async {
    return createNotification(
      title: 'Appointment Reminder',
      message: '$title - $description',
      type: NotificationType.appointment,
      priority: NotificationPriority.high,
      scheduledFor: scheduledFor,
      babyId: babyId,
      metadata: {'appointment_title': title, 'appointment_description': description},
    );
  }

  /// Auto-generate notifications from scheduled activities
  Future<void> processScheduledActivities(List<ScheduledActivity> activities) async {
    for (final activity in activities) {
      if (activity.isCompleted) continue;
      
      final now = DateTime.now();
      final scheduledTime = activity.scheduledTime;
      
      // Create notification for overdue activities
      if (scheduledTime.isBefore(now)) {
        final existingNotification = _notifications.any((n) => 
          n.metadata?['scheduled_activity_id'] == activity.id);
        
        if (!existingNotification) {
          await createNotification(
            title: 'Overdue Activity',
            message: '${activity.title} was scheduled for ${_formatTime(scheduledTime)}',
            type: _mapActivityTypeToNotificationType(activity.type),
            priority: NotificationPriority.high,
            babyId: activity.babyId,
            metadata: {
              'scheduled_activity_id': activity.id,
              'activity_title': activity.title,
              'scheduled_time': scheduledTime.toIso8601String(),
            },
          );
        }
      }
      
      // Create notification for upcoming activities (within next hour)
      final oneHourFromNow = now.add(Duration(hours: 1));
      if (scheduledTime.isAfter(now) && scheduledTime.isBefore(oneHourFromNow)) {
        final existingNotification = _notifications.any((n) => 
          n.metadata?['scheduled_activity_id'] == activity.id);
        
        if (!existingNotification) {
          await createNotification(
            title: 'Upcoming Activity',
            message: '${activity.title} is scheduled for ${_formatTime(scheduledTime)}',
            type: _mapActivityTypeToNotificationType(activity.type),
            priority: NotificationPriority.normal,
            scheduledFor: scheduledTime,
            babyId: activity.babyId,
            metadata: {
              'scheduled_activity_id': activity.id,
              'activity_title': activity.title,
              'scheduled_time': scheduledTime.toIso8601String(),
            },
          );
        }
      }
    }
  }

  /// Map scheduled activity type to notification type
  NotificationType _mapActivityTypeToNotificationType(ScheduledActivityType activityType) {
    switch (activityType) {
      case ScheduledActivityType.feedingReminder:
      case ScheduledActivityType.bottleFeeding:
      case ScheduledActivityType.nursingSession:
        return NotificationType.feeding;
      case ScheduledActivityType.sleepReminder:
      case ScheduledActivityType.napTime:
        return NotificationType.sleep;
      case ScheduledActivityType.medicationReminder:
        return NotificationType.medicine;
      case ScheduledActivityType.vaccinationAppointment:
        return NotificationType.vaccination;
      case ScheduledActivityType.doctorAppointment:
        return NotificationType.appointment;
      default:
        return NotificationType.custom;
    }
  }

  /// Format time for display
  String _formatTime(DateTime dateTime) {
    final hour = dateTime.hour;
    final minute = dateTime.minute.toString().padLeft(2, '0');
    final period = hour >= 12 ? 'PM' : 'AM';
    final displayHour = hour > 12 ? hour - 12 : (hour == 0 ? 12 : hour);
    return '$displayHour:$minute $period';
  }

  /// Clean up old notifications (older than 30 days)
  Future<void> cleanupOldNotifications() async {
    final thirtyDaysAgo = DateTime.now().subtract(Duration(days: 30));
    final originalCount = _notifications.length;
    
    _notifications.removeWhere((n) => n.createdAt.isBefore(thirtyDaysAgo));
    
    if (_notifications.length != originalCount) {
      await _saveNotifications();
      notifyListeners();
      debugPrint('Cleaned up ${originalCount - _notifications.length} old notifications');
    }
  }
}