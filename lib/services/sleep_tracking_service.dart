import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SleepTrackingService extends ChangeNotifier {
  static final SleepTrackingService _instance = SleepTrackingService._internal();
  factory SleepTrackingService() => _instance;
  SleepTrackingService._internal();

  bool _isSleeping = false;
  DateTime? _sleepStartTime;
  String? _babyId;
  Timer? _sleepTimer;
  Duration _sleepDuration = Duration.zero;

  // Getters
  bool get isSleeping => _isSleeping;
  DateTime? get sleepStartTime => _sleepStartTime;
  String? get babyId => _babyId;
  Duration get sleepDuration => _sleepDuration;

  // Initialize service and restore any ongoing sleep session
  Future<void> initialize() async {
    await _restoreSleepSession();
  }

  // Start sleep tracking
  Future<void> startSleep(String babyId) async {
    _isSleeping = true;
    _sleepStartTime = DateTime.now();
    _babyId = babyId;
    _sleepDuration = Duration.zero;

    // Save to persistent storage
    await _saveSleepSession();

    // Start timer
    _startTimer();
    
    notifyListeners();
    debugPrint('🛌 Sleep tracking started for baby: $babyId at ${_sleepStartTime}');
  }

  // Stop sleep tracking
  Future<void> stopSleep() async {
    _isSleeping = false;
    _sleepTimer?.cancel();
    
    // Clear persistent storage
    await _clearSleepSession();
    
    notifyListeners();
    debugPrint('🛌 Sleep tracking stopped. Duration: $_sleepDuration');
  }

  // Get current sleep data
  Map<String, dynamic> getCurrentSleepData() {
    return {
      'isSleeping': _isSleeping,
      'startTime': _sleepStartTime,
      'duration': _sleepDuration.inMinutes,
      'babyId': _babyId,
    };
  }

  // Private methods
  void _startTimer() {
    _sleepTimer?.cancel();
    _sleepTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_isSleeping && _sleepStartTime != null) {
        _sleepDuration = DateTime.now().difference(_sleepStartTime!);
        notifyListeners();
      }
    });
  }

  Future<void> _saveSleepSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('sleep_is_sleeping', _isSleeping);
    if (_sleepStartTime != null) {
      await prefs.setString('sleep_start_time', _sleepStartTime!.toIso8601String());
    }
    if (_babyId != null) {
      await prefs.setString('sleep_baby_id', _babyId!);
    }
  }

  Future<void> _restoreSleepSession() async {
    final prefs = await SharedPreferences.getInstance();
    _isSleeping = prefs.getBool('sleep_is_sleeping') ?? false;
    
    if (_isSleeping) {
      final startTimeStr = prefs.getString('sleep_start_time');
      _babyId = prefs.getString('sleep_baby_id');
      
      if (startTimeStr != null) {
        _sleepStartTime = DateTime.parse(startTimeStr);
        _sleepDuration = DateTime.now().difference(_sleepStartTime!);
        _startTimer();
        
        debugPrint('🛌 Restored ongoing sleep session: ${_sleepDuration.inMinutes} minutes');
        notifyListeners();
      }
    }
  }

  Future<void> _clearSleepSession() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('sleep_is_sleeping');
    await prefs.remove('sleep_start_time');
    await prefs.remove('sleep_baby_id');
  }

  // Format duration for display
  String formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  @override
  void dispose() {
    _sleepTimer?.cancel();
    super.dispose();
  }
}