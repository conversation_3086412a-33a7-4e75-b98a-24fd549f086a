import 'package:flutter/material.dart';
import '../presentation/quick_log_bottom_sheet/quick_log_bottom_sheet.dart';
import '../models/baby_profile.dart';

/// Service to handle activity-specific navigation
class ActivityNavigationService {
  // Route constants to avoid magic strings
  static const String _growthChartsRoute = '/growth-charts';
  static const String _quickLogRoute = '/quick-log-bottom-sheet';
  
  // Supported activity types
  static const Set<String> _supportedActivityTypes = {
    'growth', 'feeding', 'sleep', 'vaccination', 'medicine', 'temperature',
    'potty', 'tummy_time', 'story_time', 'screen_time', 'skin_to_skin',
    'outdoor_play', 'indoor_play', 'brush_teeth', 'pumping', 'milestone'
  };

  /// Handle navigation for different activity types with error handling
  static Future<bool> navigateToActivity({
    required BuildContext context,
    required String activityType,
    BabyProfile? babyProfile,
    VoidCallback? onDataChanged,
  }) async {
    try {
      // Validate activity type
      if (!_supportedActivityTypes.contains(activityType)) {
        debugPrint('Unsupported activity type: $activityType');
        _showErrorSnackBar(context, 'Activity type "$activityType" is not supported');
        return false;
      }

      switch (activityType) {
        case 'growth':
          await Navigator.pushNamed(
            context, 
            _growthChartsRoute, 
            arguments: babyProfile,
          );
          break;
          
        case 'feeding':
        case 'sleep':
        case 'vaccination':
          _showQuickLogBottomSheet(
            context: context,
            activityType: activityType,
            babyProfile: babyProfile,
            onDataChanged: onDataChanged,
          );
          break;
          
        default:
          _navigateToQuickLog(context, activityType);
          break;
      }
      return true;
    } catch (e) {
      debugPrint('Navigation error for activity $activityType: $e');
      _showErrorSnackBar(context, 'Failed to open $activityType tracker');
      return false;
    }
  }

  /// Show error message to user
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  /// Show quick log bottom sheet for immediate logging
  static void _showQuickLogBottomSheet({
    required BuildContext context,
    required String activityType,
    BabyProfile? babyProfile,
    VoidCallback? onDataChanged,
  }) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => QuickLogBottomSheet(
        initialActivityType: activityType,
        babyProfile: babyProfile,
        onDataSaved: onDataChanged,
      ),
    );
  }

  /// Navigate to quick log screen with pre-selected activity
  static void _navigateToQuickLog(BuildContext context, String activityType) {
    final titles = {
      'medicine': 'Log Medicine',
      'temperature': 'Log Temperature',
      'potty': 'Log Potty',
      'tummy_time': 'Log Tummy Time',
      'story_time': 'Log Story Time',
      'screen_time': 'Log Screen Time',
      'skin_to_skin': 'Log Skin to Skin',
      'outdoor_play': 'Log Outdoor Play',
      'indoor_play': 'Log Indoor Play',
      'brush_teeth': 'Log Brush Teeth',
      'pumping': 'Log Pumping',
      'milestone': 'Log Milestone Achievement',
    };

    Navigator.pushNamed(
      context, 
      _quickLogRoute,
      arguments: {
        'initialActivityType': activityType,
        'title': titles[activityType] ?? 'Quick Log',
      },
    );
  }
}