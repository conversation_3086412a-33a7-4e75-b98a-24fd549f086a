import 'package:dio/dio.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class OpenAIService {
  static final OpenAIService _instance = OpenAIService._internal();
  Dio? _dio;
  static const String apiKey = String.fromEnvironment('OPENAI_API_KEY');
  bool _isInitialized = false;

  // Factory constructor to return the singleton instance
  factory OpenAIService() {
    return _instance;
  }

  // Private constructor for singleton pattern
  OpenAIService._internal();

  // Load API key from environment or env.json file
  static Future<String> _loadApiKey() async {
    String key = apiKey;
    
    // If dart-define value is empty, try to load from env.json
    if (key.isEmpty) {
      try {
        final String envJson = await rootBundle.loadString('env.json');
        final Map<String, dynamic> env = json.decode(envJson);
        
        key = env['OPENAI_API_KEY'] ?? '';
        
        print('📁 Loaded OpenAI API key from env.json');
      } catch (e) {
        print('⚠️ Failed to load OpenAI API key from env.json: $e');
      }
    }
    
    return key;
  }

  Future<void> initialize() async {
    if (_isInitialized) return;
    
    // Load API key from environment variables or env.json
    final apiKey = await _loadApiKey();
    
    if (apiKey.isEmpty) {
      throw Exception('OPENAI_API_KEY must be provided via --dart-define or in env.json file');
    }

    // Configure Dio with base URL and headers
    _dio = Dio(
      BaseOptions(
        baseUrl: 'https://api.openai.com/v1',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $apiKey',
        },
      ),
    );
    
    _isInitialized = true;
    print('✅ OpenAI service initialized successfully');
  }

  Future<Dio> get dio async {
    if (!_isInitialized) {
      await initialize();
    }
    return _dio!;
  }
}
