import 'package:flutter/foundation.dart';
import '../models/feature_access.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';

/// Service for managing feature access based on subscription status
class FeatureAccessService extends ChangeNotifier {
  final dynamic _subscriptionController;
  
  /// Cache for feature access results to improve performance
  final Map<AppFeature, FeatureAccessResult> _accessResultCache = {};
  
  /// Usage tracking for limited features
  final Map<AppFeature, int> _usageTracking = {};
  
  /// Create a new FeatureAccessService
  FeatureAccessService(this._subscriptionController) {
    // Listen to subscription changes and clear cache
    _subscriptionController.addListener(_onSubscriptionChanged);
  }
  
  /// Handle subscription changes by clearing cache
  void _onSubscriptionChanged() {
    _clearCache();
    notifyListeners();
  }
  
  /// Check if a user has access to a specific feature
  bool hasFeatureAccess(AppFeature feature) {
    final result = checkFeatureAccess(feature);
    debugPrint('DEBUG: FeatureAccessService - hasFeatureAccess(${feature.toString().split('.').last}) = ${result.hasAccess}, reason: ${result.restrictionReason}');
    return result.hasAccess;
  }
  
  /// Get detailed feature access information
  FeatureAccessResult checkFeatureAccess(AppFeature feature) {
    // Check cache first
    if (_accessResultCache.containsKey(feature)) {
      return _accessResultCache[feature]!;
    }
    
    final subscription = _subscriptionController.currentSubscription;
    debugPrint('DEBUG: FeatureAccessService - checkFeatureAccess(${feature.toString().split('.').last}) - Subscription: ${subscription.planName}, Status: ${subscription.status.toString().split('.').last}, isPremium: ${subscription.isPremium}');
    final result = _evaluateFeatureAccess(feature, subscription);
    
    // Cache the result
    _accessResultCache[feature] = result;
    return result;
  }
  
  /// Evaluate feature access based on subscription and usage
  FeatureAccessResult _evaluateFeatureAccess(AppFeature feature, SubscriptionInfo subscription) {
    final isPremium = subscription.isPremium;
    final currentUsage = getCurrentUsage(feature);
    
    debugPrint('DEBUG: FeatureAccessService - Evaluating ${feature.toString().split('.').last} - Subscription: ${subscription.planName}, Status: ${subscription.status.toString().split('.').last}, isPremium: $isPremium');
    
    switch (feature) {
      case AppFeature.multipleBabyProfiles:
        final limit = isPremium ? null : 1; // Free users get 1 profile
        final hasAccess = isPremium || currentUsage < 1;
        return FeatureAccessResult(
          hasAccess: hasAccess,
          restrictionReason: hasAccess ? null : 'Free plan limited to 1 baby profile',
          upgradePrompt: hasAccess ? null : _getUpgradePrompt(feature),
          currentUsage: currentUsage,
          usageLimit: limit,
        );
        
      case AppFeature.familySharing:
        return FeatureAccessResult(
          hasAccess: isPremium,
          restrictionReason: isPremium ? null : 'Family sharing requires Premium plan',
          upgradePrompt: isPremium ? null : _getUpgradePrompt(feature),
        );
        
      case AppFeature.whoGrowthCharts:
        return FeatureAccessResult(
          hasAccess: isPremium,
          restrictionReason: isPremium ? null : 'WHO Growth Charts require Premium plan',
          upgradePrompt: isPremium ? null : _getUpgradePrompt(feature),
        );
        
      case AppFeature.aiInsights:
        return FeatureAccessResult(
          hasAccess: isPremium,
          restrictionReason: isPremium ? null : 'AI Insights require Premium plan',
          upgradePrompt: isPremium ? null : _getUpgradePrompt(feature),
        );
        
      case AppFeature.aiChat:
        return FeatureAccessResult(
          hasAccess: isPremium,
          restrictionReason: isPremium ? null : 'AI Chat requires Premium plan',
          upgradePrompt: isPremium ? null : _getUpgradePrompt(feature),
        );
        
      case AppFeature.dataExport:
        final limit = isPremium ? null : 1; // Free users get 1 export per month
        final hasAccess = isPremium || currentUsage < 1;
        return FeatureAccessResult(
          hasAccess: hasAccess,
          restrictionReason: hasAccess ? null : 'Free plan limited to 1 export per month',
          upgradePrompt: hasAccess ? null : _getUpgradePrompt(feature),
          currentUsage: currentUsage,
          usageLimit: limit,
        );
        
      case AppFeature.advancedAnalytics:
      case AppFeature.customReminders:
      case AppFeature.prioritySupport:
        return FeatureAccessResult(
          hasAccess: isPremium,
          restrictionReason: isPremium ? null : '${feature.displayName} requires Premium plan',
          upgradePrompt: isPremium ? null : _getUpgradePrompt(feature),
        );
    }
  }
  
  /// Get current usage for a feature
  int getCurrentUsage(AppFeature feature) {
    return _usageTracking[feature] ?? 0;
  }
  
  /// Increment usage for a feature
  void incrementUsage(AppFeature feature) {
    _usageTracking[feature] = getCurrentUsage(feature) + 1;
    _clearCache(); // Clear cache to recalculate access
    notifyListeners();
  }
  
  /// Reset usage for a feature (e.g., monthly reset)
  void resetUsage(AppFeature feature) {
    _usageTracking[feature] = 0;
    _clearCache();
    notifyListeners();
  }
  
  /// Get feature limit for current subscription
  int? getFeatureLimit(AppFeature feature) {
    final subscription = _subscriptionController.currentSubscription;
    final isPremium = subscription.isPremium;
    
    switch (feature) {
      case AppFeature.multipleBabyProfiles:
        return isPremium ? null : 1; // Unlimited for premium, 1 for free
      case AppFeature.dataExport:
        return isPremium ? null : 1; // Unlimited for premium, 1 per month for free
      default:
        return isPremium ? null : 0; // Premium only features
    }
  }
  
  /// Check if user is near their usage limit
  bool isNearUsageLimit(AppFeature feature) {
    final limit = getFeatureLimit(feature);
    if (limit == null) return false; // No limit
    
    final usage = getCurrentUsage(feature);
    return usage >= (limit * 0.8); // 80% threshold
  }
  
  /// Get upgrade prompt configuration for a feature
  UpgradePromptConfig _getUpgradePrompt(AppFeature feature) {
    switch (feature) {
      case AppFeature.multipleBabyProfiles:
        return UpgradePromptConfig(
          title: 'Upgrade to Track Multiple Children',
          description: 'Create unlimited baby profiles and track all your children in one place.',
          benefits: [
            'Unlimited baby profiles',
            'Individual tracking for each child',
            'Family-wide insights and analytics',
            'Shared access with family members',
          ],
          ctaText: 'Upgrade to Premium',
        );
        
      case AppFeature.familySharing:
        return UpgradePromptConfig(
          title: 'Share with Family Members',
          description: 'Invite up to 10 family members to collaborate on your baby\'s care.',
          benefits: [
            'Invite up to 10 family members',
            'Real-time activity sharing',
            'Collaborative care tracking',
            'Role-based permissions',
          ],
          ctaText: 'Enable Family Sharing',
        );
        
      case AppFeature.whoGrowthCharts:
        return UpgradePromptConfig(
          title: 'WHO Growth Charts',
          description: 'Track your baby\'s growth with official WHO percentile charts.',
          benefits: [
            'Official WHO growth standards',
            'Percentile tracking and trends',
            'Growth milestone alerts',
            'Pediatrician-ready reports',
          ],
          ctaText: 'Unlock Growth Charts',
        );
        
      case AppFeature.aiInsights:
        return UpgradePromptConfig(
          title: 'AI-Powered Insights',
          description: 'Get personalized insights about your baby\'s development patterns.',
          benefits: [
            'AI-powered pattern analysis',
            'Personalized recommendations',
            'Development milestone predictions',
            'Sleep and feeding insights',
          ],
          ctaText: 'Get AI Insights',
        );
        
      case AppFeature.aiChat:
        return UpgradePromptConfig(
          title: '24/7 AI Assistant',
          description: 'Get instant answers to your parenting questions anytime.',
          benefits: [
            '24/7 AI chat assistant',
            'Personalized parenting advice',
            'Evidence-based recommendations',
            'Unlimited conversations',
          ],
          ctaText: 'Chat with AI',
        );
        
      case AppFeature.dataExport:
        return UpgradePromptConfig(
          title: 'Export Your Data',
          description: 'Export unlimited reports for healthcare providers and personal records.',
          benefits: [
            'Unlimited data exports',
            'PDF and CSV formats',
            'Healthcare provider reports',
            'Complete activity history',
          ],
          ctaText: 'Upgrade for Exports',
        );
        
      default:
        return UpgradePromptConfig(
          title: 'Upgrade to Premium',
          description: 'Unlock ${feature.displayName} and all premium features.',
          benefits: [
            'Access to ${feature.displayName}',
            'All premium features included',
            'Priority customer support',
            'Regular feature updates',
          ],
          ctaText: 'Upgrade Now',
        );
    }
  }
  
  /// Get all current feature restrictions
  List<FeatureRestriction> getCurrentRestrictions() {
    final subscription = _subscriptionController.currentSubscription;
    final isPremium = subscription.isPremium;
    
    if (isPremium) return []; // No restrictions for premium users
    
    return [
      FeatureRestriction(
        feature: AppFeature.multipleBabyProfiles,
        isBlocked: false,
        usageLimit: 1,
        reason: 'Free plan limited to 1 baby profile',
      ),
      FeatureRestriction(
        feature: AppFeature.familySharing,
        isBlocked: true,
        reason: 'Family sharing requires Premium plan',
      ),
      FeatureRestriction(
        feature: AppFeature.whoGrowthCharts,
        isBlocked: true,
        reason: 'WHO Growth Charts require Premium plan',
      ),
      FeatureRestriction(
        feature: AppFeature.aiInsights,
        isBlocked: true,
        reason: 'AI Insights require Premium plan',
      ),
      FeatureRestriction(
        feature: AppFeature.aiChat,
        isBlocked: true,
        reason: 'AI Chat requires Premium plan',
      ),
      FeatureRestriction(
        feature: AppFeature.dataExport,
        isBlocked: false,
        usageLimit: 1,
        reason: 'Free plan limited to 1 export per month',
      ),
      FeatureRestriction(
        feature: AppFeature.advancedAnalytics,
        isBlocked: true,
        reason: 'Advanced Analytics require Premium plan',
      ),
      FeatureRestriction(
        feature: AppFeature.customReminders,
        isBlocked: true,
        reason: 'Custom Reminders require Premium plan',
      ),
      FeatureRestriction(
        feature: AppFeature.prioritySupport,
        isBlocked: true,
        reason: 'Priority Support require Premium plan',
      ),
    ];
  }
  
  /// Get all current feature benefits
  List<FeatureBenefit> getCurrentBenefits() {
    final subscription = _subscriptionController.currentSubscription;
    final isPremium = subscription.isPremium;
    
    if (!isPremium) {
      return [
        FeatureBenefit(
          feature: AppFeature.multipleBabyProfiles,
          description: '1 baby profile',
          usageLimit: 1,
        ),
        FeatureBenefit(
          feature: AppFeature.dataExport,
          description: '1 export per month',
          usageLimit: 1,
        ),
      ];
    }
    
    return AppFeature.values.map((feature) => FeatureBenefit(
      feature: feature,
      description: 'Unlimited ${feature.displayName}',
      isUnlimited: true,
    )).toList();
  }

  /// Clear the access result cache
  void _clearCache() {
    _accessResultCache.clear();
  }
  
  @override
  void dispose() {
    _subscriptionController.removeListener(_onSubscriptionChanged);
    super.dispose();
  }
}