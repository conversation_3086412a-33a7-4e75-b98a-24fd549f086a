import 'package:flutter/foundation.dart';
import '../models/feature_access.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';
import 'feature_access_config.dart';

/// Configuration for a specific feature's access rules
class _FeatureConfig {
  final AppFeature feature;
  final bool Function(SubscriptionInfo subscription, int currentUsage) accessChecker;
  final String restrictionMessage;
  final UpgradePromptConfig upgradePrompt;
  final int? Function(SubscriptionInfo subscription) usageLimitProvider;
  
  const _FeatureConfig({
    required this.feature,
    required this.accessChecker,
    required this.restrictionMessage,
    required this.upgradePrompt,
    required this.usageLimitProvider,
  });
  
  bool checkAccess(SubscriptionInfo subscription, int currentUsage) {
    return accessChecker(subscription, currentUsage);
  }
  
  int? getUsageLimit(SubscriptionInfo subscription) {
    return usageLimitProvider(subscription);
  }
  
  /// Factory for premium-only features
  factory _FeatureConfig.premiumOnly(AppFeature feature) {
    return _FeatureConfig(
      feature: feature,
      accessChecker: (subscription, usage) => subscription.isPremium,
      restrictionMessage: '${feature.displayName} requires Premium plan. Upgrade to unlock this feature.',
      upgradePrompt: _getDefaultUpgradePrompt(feature),
      usageLimitProvider: (subscription) => subscription.isPremium ? null : 0,
    );
  }
  
  /// Factory for usage-limited features
  factory _FeatureConfig.usageLimited(
    AppFeature feature, {
    required int freeLimit,
    int? premiumLimit,
    required String restrictionMessage,
    required UpgradePromptConfig upgradePrompt,
  }) {
    return _FeatureConfig(
      feature: feature,
      accessChecker: (subscription, usage) {
        final limit = subscription.isPremium ? premiumLimit : freeLimit;
        return limit == null || usage < limit;
      },
      restrictionMessage: restrictionMessage,
      upgradePrompt: upgradePrompt,
      usageLimitProvider: (subscription) => subscription.isPremium ? premiumLimit : freeLimit,
    );
  }
  
  static UpgradePromptConfig _getDefaultUpgradePrompt(AppFeature feature) {
    return UpgradePromptConfig(
      title: 'Upgrade to Premium',
      description: 'Unlock ${feature.displayName} and all premium features.',
      benefits: [
        'Access to ${feature.displayName}',
        'All premium features included',
        'Priority customer support',
        'Regular feature updates',
      ],
      ctaText: 'Upgrade Now',
    );
  }
}

/// Service for managing feature access based on subscription status
class FeatureAccessService extends ChangeNotifier {
  final dynamic _subscriptionController;
  
  /// Listeners for specific feature access changes
  final Map<AppFeature, List<VoidCallback>> _featureListeners = {};
  
  /// Cache for feature access results to improve performance
  final Map<AppFeature, FeatureAccessResult> _accessResultCache = {};
  
  /// Usage tracking for limited features
  final Map<AppFeature, int> _usageTracking = {};
  
  /// Feature configurations defining access rules
  static final Map<AppFeature, _FeatureConfig> _featureConfigs = {
    AppFeature.multipleBabyProfiles: _FeatureConfig.usageLimited(
      AppFeature.multipleBabyProfiles,
      freeLimit: 1,
      premiumLimit: null, // Unlimited
      restrictionMessage: 'Free plan limited to 1 baby profile. Upgrade to Premium for unlimited baby profiles.',
      upgradePrompt: UpgradePromptConfig(
        title: 'Upgrade to Track Multiple Children',
        description: 'Create unlimited baby profiles and track all your children in one place.',
        benefits: [
          'Unlimited baby profiles',
          'Individual tracking for each child',
          'Family-wide insights and analytics',
          'Shared access with family members',
        ],
        ctaText: 'Upgrade to Premium',
      ),
    ),
    
    AppFeature.familySharing: _FeatureConfig(
      feature: AppFeature.familySharing,
      accessChecker: (subscription, usage) => subscription.isPremium && subscription.maxFamilyMembers > 1,
      restrictionMessage: 'Family sharing requires Premium plan. Upgrade to invite up to 10 family members.',
      upgradePrompt: UpgradePromptConfig(
        title: 'Share with Family Members',
        description: 'Invite up to 10 family members to collaborate on your baby\'s care.',
        benefits: [
          'Invite up to 10 family members',
          'Real-time activity sharing',
          'Collaborative care tracking',
          'Role-based permissions',
        ],
        ctaText: 'Enable Family Sharing',
      ),
      usageLimitProvider: (subscription) => subscription.isPremium ? 10 : 1,
    ),
    
    AppFeature.aiInsights: _FeatureConfig(
      feature: AppFeature.aiInsights,
      accessChecker: (subscription, usage) => subscription.isPremium && subscription.includesAiInsights,
      restrictionMessage: 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
      upgradePrompt: UpgradePromptConfig(
        title: 'AI-Powered Insights',
        description: 'Get personalized insights about your baby\'s development patterns.',
        benefits: [
          'AI-powered pattern analysis',
          'Personalized recommendations',
          'Development milestone predictions',
          'Sleep and feeding insights',
        ],
        ctaText: 'Get AI Insights',
      ),
      usageLimitProvider: (subscription) => subscription.isPremium ? null : 0,
    ),
    
    AppFeature.aiChat: _FeatureConfig(
      feature: AppFeature.aiChat,
      accessChecker: (subscription, usage) => subscription.isPremium && subscription.includesAiInsights,
      restrictionMessage: 'Ask AI chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
      upgradePrompt: UpgradePromptConfig(
        title: '24/7 AI Assistant',
        description: 'Get instant answers to your parenting questions anytime.',
        benefits: [
          '24/7 AI chat assistant',
          'Personalized parenting advice',
          'Evidence-based recommendations',
          'Unlimited conversations',
        ],
        ctaText: 'Chat with AI',
      ),
      usageLimitProvider: (subscription) => subscription.isPremium ? null : 0,
    ),
    
    AppFeature.dataExport: _FeatureConfig(
      feature: AppFeature.dataExport,
      accessChecker: (subscription, usage) => subscription.isPremium && subscription.includesDataExport,
      restrictionMessage: 'Data export requires Premium plan. Upgrade to export your data for healthcare providers.',
      upgradePrompt: UpgradePromptConfig(
        title: 'Export Your Data',
        description: 'Export unlimited reports for healthcare providers and personal records.',
        benefits: [
          'Unlimited data exports',
          'PDF and CSV formats',
          'Healthcare provider reports',
          'Complete activity history',
        ],
        ctaText: 'Upgrade for Exports',
      ),
      usageLimitProvider: (subscription) => subscription.isPremium ? null : 0,
    ),
  };
  
  /// Create a new FeatureAccessService
  FeatureAccessService(this._subscriptionController) {
    // Listen to subscription changes and clear cache
    _subscriptionController.addListener(_onSubscriptionChanged);
  }
  
  /// Handle subscription changes by clearing cache
  void _onSubscriptionChanged() {
    _invalidateCache();
    notifyListeners();
  }
  
  /// Check if a user has access to a specific feature
  bool hasFeatureAccess(AppFeature feature) {
    final result = checkFeatureAccess(feature);
    if (FeatureAccessConfig.enableDebugLogging) {
      debugPrint('${FeatureAccessConfig.debugPrefix} - hasFeatureAccess(${feature.toString().split('.').last}) = ${result.hasAccess}, reason: ${result.restrictionReason}');
    }
    return result.hasAccess;
  }
  
  /// Get detailed feature access information
  FeatureAccessResult checkFeatureAccess(AppFeature feature) {
    // Check cache first
    if (_accessResultCache.containsKey(feature)) {
      return _accessResultCache[feature]!;
    }
    
    final subscription = _subscriptionController.currentSubscription;
    if (FeatureAccessConfig.enableDebugLogging) {
      debugPrint('${FeatureAccessConfig.debugPrefix} - checkFeatureAccess(${feature.toString().split('.').last}) - Subscription: ${subscription.planName}, Status: ${subscription.status.toString().split('.').last}, isPremium: ${subscription.isPremium}');
    }
    final result = _evaluateFeatureAccess(feature, subscription);
    
    // Cache the result
    _accessResultCache[feature] = result;
    return result;
  }
  
  /// Evaluate feature access based on subscription and usage
  FeatureAccessResult _evaluateFeatureAccess(AppFeature feature, SubscriptionInfo subscription) {
    final isPremium = subscription.isPremium;
    final currentUsage = getCurrentUsage(feature);
    
    if (FeatureAccessConfig.enableDebugLogging) {
      debugPrint('${FeatureAccessConfig.debugPrefix} - Evaluating ${feature.toString().split('.').last} - Subscription: ${subscription.planName}, Status: ${subscription.status.toString().split('.').last}, isPremium: $isPremium');
    }
    
    final config = _getFeatureConfig(feature);
    final hasAccess = config.checkAccess(subscription, currentUsage);
    
    return FeatureAccessResult(
      hasAccess: hasAccess,
      restrictionReason: hasAccess ? null : config.restrictionMessage,
      upgradePrompt: hasAccess ? null : config.upgradePrompt,
      currentUsage: currentUsage,
      usageLimit: config.getUsageLimit(subscription),
    );
  }
  
  /// Get feature configuration for access evaluation
  _FeatureConfig _getFeatureConfig(AppFeature feature) {
    return _featureConfigs[feature] ?? _FeatureConfig.premiumOnly(feature);
  }
  

  
  /// Get current usage for a feature
  int getCurrentUsage(AppFeature feature) {
    return _usageTracking[feature] ?? 0;
  }
  
  /// Increment usage for a feature
  /// 
  /// This should be called when a user successfully uses a limited feature.
  /// For example, when creating a new baby profile or exporting data.
  /// 
  /// [feature] The feature whose usage should be incremented
  void incrementUsage(AppFeature feature) {
    _usageTracking[feature] = getCurrentUsage(feature) + 1;
    // Only clear cache for the specific feature that changed
    _accessResultCache.remove(feature);
    notifyListeners();
  }
  
  /// Reset usage for a feature (e.g., monthly reset)
  /// 
  /// This can be used to reset usage counters for features that have
  /// time-based limits (e.g., monthly export limits).
  /// 
  /// [feature] The feature whose usage should be reset to 0
  void resetUsage(AppFeature feature) {
    _usageTracking[feature] = 0;
    _clearCache();
    notifyListeners();
  }
  
  /// Get feature limit for current subscription
  /// 
  /// Returns the usage limit for a specific feature based on the user's subscription.
  /// Returns `null` for unlimited access, or a positive integer for limited access.
  /// 
  /// [feature] The feature to check the limit for
  int? getFeatureLimit(AppFeature feature) {
    final subscription = _subscriptionController.currentSubscription;
    final isPremium = subscription.isPremium;
    
    switch (feature) {
      case AppFeature.multipleBabyProfiles:
        return isPremium ? null : 1; // Unlimited for premium, 1 for free
      case AppFeature.familySharing:
        return isPremium ? 10 : 1; // 10 family members for premium, 1 for free
      case AppFeature.dataExport:
        return isPremium ? null : 0; // Unlimited for premium, none for free
      case AppFeature.whoGrowthCharts:
      case AppFeature.aiInsights:
      case AppFeature.aiChat:
      case AppFeature.advancedAnalytics:
      case AppFeature.customReminders:
      case AppFeature.prioritySupport:
        return isPremium ? null : 0; // Premium only features
    }
  }
  
  /// Check if user is near their usage limit
  bool isNearUsageLimit(AppFeature feature) {
    final limit = getFeatureLimit(feature);
    if (limit == null) return false; // No limit
    
    final usage = getCurrentUsage(feature);
    return usage >= (limit * FeatureAccessConfig.nearLimitThreshold);
  }
  

  
  /// Get all current feature restrictions
  List<FeatureRestriction> getCurrentRestrictions() {
    final subscription = _subscriptionController.currentSubscription;
    final isPremium = subscription.isPremium;
    
    if (isPremium) return []; // No restrictions for premium users
    
    return [
      FeatureRestriction(
        feature: AppFeature.multipleBabyProfiles,
        isBlocked: false,
        usageLimit: 1,
        reason: 'Free plan limited to 1 baby profile. Upgrade to Premium for unlimited baby profiles.',
      ),
      FeatureRestriction(
        feature: AppFeature.familySharing,
        isBlocked: true,
        reason: 'Family sharing requires Premium plan. Upgrade to invite up to 10 family members.',
      ),
      FeatureRestriction(
        feature: AppFeature.whoGrowthCharts,
        isBlocked: true,
        reason: 'WHO Growth Charts require Premium plan. Upgrade to track your baby\'s growth with official WHO percentile charts.',
      ),
      FeatureRestriction(
        feature: AppFeature.aiInsights,
        isBlocked: true,
        reason: 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
      ),
      FeatureRestriction(
        feature: AppFeature.aiChat,
        isBlocked: true,
        reason: 'Ask AI chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
      ),
      FeatureRestriction(
        feature: AppFeature.dataExport,
        isBlocked: true,
        reason: 'Data export requires Premium plan. Upgrade to export your data for healthcare providers.',
      ),
      FeatureRestriction(
        feature: AppFeature.advancedAnalytics,
        isBlocked: true,
        reason: 'Advanced analytics require Premium plan. Upgrade for detailed insights and trend analysis.',
      ),
      FeatureRestriction(
        feature: AppFeature.customReminders,
        isBlocked: true,
        reason: 'Custom notifications require Premium plan. Upgrade to set personalized reminders.',
      ),
      FeatureRestriction(
        feature: AppFeature.prioritySupport,
        isBlocked: true,
        reason: 'Priority customer support requires Premium plan. Upgrade for faster response times.',
      ),
    ];
  }
  
  /// Get all current feature benefits
  List<FeatureBenefit> getCurrentBenefits() {
    final subscription = _subscriptionController.currentSubscription;
    final isPremium = subscription.isPremium;
    
    if (!isPremium) {
      return [
        FeatureBenefit(
          feature: AppFeature.multipleBabyProfiles,
          description: '1 baby profile',
          usageLimit: 1,
        ),
        // Free users don't get data export, AI features, etc.
      ];
    }
    
    return [
      FeatureBenefit(
        feature: AppFeature.multipleBabyProfiles,
        description: 'Unlimited baby profiles',
        isUnlimited: true,
      ),
      FeatureBenefit(
        feature: AppFeature.familySharing,
        description: 'Family sharing with up to 10 members',
        usageLimit: 10,
      ),
      FeatureBenefit(
        feature: AppFeature.whoGrowthCharts,
        description: 'WHO Growth Charts',
        isUnlimited: true,
      ),
      FeatureBenefit(
        feature: AppFeature.aiInsights,
        description: 'AI insights',
        isUnlimited: true,
      ),
      FeatureBenefit(
        feature: AppFeature.aiChat,
        description: 'Ask AI chat',
        isUnlimited: true,
      ),
      FeatureBenefit(
        feature: AppFeature.dataExport,
        description: 'Data export',
        isUnlimited: true,
      ),
      FeatureBenefit(
        feature: AppFeature.prioritySupport,
        description: 'Priority customer support',
        isUnlimited: true,
      ),
      FeatureBenefit(
        feature: AppFeature.customReminders,
        description: 'Custom notifications',
        isUnlimited: true,
      ),
    ];
  }

  /// Clear the access result cache
  void _clearCache() {
    _accessResultCache.clear();
  }
  
  /// Invalidate cache selectively based on subscription changes
  void _invalidateCache() {
    final currentSubscription = _subscriptionController.currentSubscription;
    
    // Check if any subscription properties that affect feature access have changed
    if (_lastKnownSubscriptionStatus != currentSubscription.status ||
        _lastKnownIncludesAiInsights != currentSubscription.includesAiInsights ||
        _lastKnownIncludesDataExport != currentSubscription.includesDataExport ||
        _lastKnownMaxFamilyMembers != currentSubscription.maxFamilyMembers) {
      
      _accessResultCache.clear();
      _updateLastKnownValues(currentSubscription);
    }
  }
  
  void _updateLastKnownValues(SubscriptionInfo subscription) {
    _lastKnownSubscriptionStatus = subscription.status;
    _lastKnownIncludesAiInsights = subscription.includesAiInsights;
    _lastKnownIncludesDataExport = subscription.includesDataExport;
    _lastKnownMaxFamilyMembers = subscription.maxFamilyMembers;
  }
  
  SubscriptionStatus? _lastKnownSubscriptionStatus;
  bool? _lastKnownIncludesAiInsights;
  bool? _lastKnownIncludesDataExport;
  int? _lastKnownMaxFamilyMembers;
  
  @override
  void dispose() {
    // Clean up feature listeners
    _featureListeners.clear();
    
    // Remove subscription controller listener
    _subscriptionController.removeListener(_onSubscriptionChanged);
    
    // Clear caches to prevent memory leaks
    _accessResultCache.clear();
    _usageTracking.clear();
    
    super.dispose();
  }
}