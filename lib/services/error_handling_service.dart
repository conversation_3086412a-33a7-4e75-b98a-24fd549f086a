import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Comprehensive error handling service for account profile operations
/// Provides centralized error classification, user-friendly messages, and recovery options
class ErrorHandlingService {
  static final ErrorHandlingService _instance = ErrorHandlingService._internal();
  factory ErrorHandlingService() => _instance;
  ErrorHandlingService._internal();

  final Connectivity _connectivity = Connectivity();
  StreamSubscription<ConnectivityResult>? _connectivitySubscription;
  
  bool _isOnline = true;
  final StreamController<bool> _connectivityController = StreamController<bool>.broadcast();
  
  /// Stream of connectivity status changes
  Stream<bool> get connectivityStream => _connectivityController.stream;
  
  /// Current connectivity status
  bool get isOnline => _isOnline;

  /// Initialize the error handling service
  void initialize() {
    _checkInitialConnectivity();
    _setupConnectivityListener();
  }

  /// Check initial connectivity status
  void _checkInitialConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectivityStatus(result);
    } catch (e) {
      debugPrint('Error checking initial connectivity: $e');
      _isOnline = false;
    }
  }

  /// Setup connectivity listener
  void _setupConnectivityListener() {
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(
      _updateConnectivityStatus,
      onError: (error) {
        debugPrint('Connectivity stream error: $error');
        _isOnline = false;
        _connectivityController.add(false);
      },
    );
  }

  /// Update connectivity status
  void _updateConnectivityStatus(ConnectivityResult result) {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;
    
    if (wasOnline != _isOnline) {
      _connectivityController.add(_isOnline);
      debugPrint('Connectivity changed: ${_isOnline ? 'Online' : 'Offline'}');
    }
  }

  /// Handle and classify errors from account profile operations
  AccountProfileError handleError(dynamic error, {
    String? operation,
    Map<String, dynamic>? context,
  }) {
    debugPrint('Error in $operation: $error');
    
    // Network connectivity errors
    if (!_isOnline) {
      return AccountProfileError(
        type: AccountProfileErrorType.networkError,
        title: 'No Internet Connection',
        message: 'Please check your internet connection and try again.',
        isRetryable: true,
        retryDelay: const Duration(seconds: 2),
        operation: operation,
        context: context,
      );
    }

    // Supabase specific errors
    if (error is PostgrestException) {
      return _handlePostgrestError(error, operation, context);
    }

    if (error is AuthException) {
      return _handleAuthError(error, operation, context);
    }

    // Socket/HTTP errors
    if (error is SocketException) {
      return AccountProfileError(
        type: AccountProfileErrorType.networkError,
        title: 'Connection Failed',
        message: 'Unable to connect to the server. Please check your internet connection.',
        isRetryable: true,
        retryDelay: const Duration(seconds: 3),
        operation: operation,
        context: context,
      );
    }

    if (error is HttpException) {
      return AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Server Error',
        message: 'The server is temporarily unavailable. Please try again later.',
        isRetryable: true,
        retryDelay: const Duration(seconds: 5),
        operation: operation,
        context: context,
      );
    }

    // Timeout errors
    if (error is TimeoutException) {
      return AccountProfileError(
        type: AccountProfileErrorType.timeoutError,
        title: 'Request Timeout',
        message: 'The request took too long to complete. Please try again.',
        isRetryable: true,
        retryDelay: const Duration(seconds: 3),
        operation: operation,
        context: context,
      );
    }

    // Format/parsing errors
    if (error is FormatException) {
      return AccountProfileError(
        type: AccountProfileErrorType.dataError,
        title: 'Data Format Error',
        message: 'The received data is in an unexpected format.',
        isRetryable: false,
        operation: operation,
        context: context,
      );
    }

    // Generic errors
    return AccountProfileError(
      type: AccountProfileErrorType.unknown,
      title: 'Something Went Wrong',
      message: 'An unexpected error occurred. Please try again.',
      isRetryable: true,
      retryDelay: const Duration(seconds: 2),
      operation: operation,
      context: context,
      originalError: error,
    );
  }

  /// Handle Postgrest (database) errors
  AccountProfileError _handlePostgrestError(
    PostgrestException error,
    String? operation,
    Map<String, dynamic>? context,
  ) {
    switch (error.code) {
      case '23505': // Unique constraint violation
        return AccountProfileError(
          type: AccountProfileErrorType.validationError,
          title: 'Duplicate Entry',
          message: 'This information already exists. Please use different details.',
          isRetryable: false,
          operation: operation,
          context: context,
        );
      
      case '23503': // Foreign key constraint violation
        return AccountProfileError(
          type: AccountProfileErrorType.validationError,
          title: 'Invalid Reference',
          message: 'The referenced data no longer exists.',
          isRetryable: false,
          operation: operation,
          context: context,
        );
      
      case '42501': // Insufficient privilege
        return AccountProfileError(
          type: AccountProfileErrorType.permissionError,
          title: 'Access Denied',
          message: 'You don\'t have permission to perform this action.',
          isRetryable: false,
          operation: operation,
          context: context,
        );
      
      case 'PGRST116': // No rows found
        return AccountProfileError(
          type: AccountProfileErrorType.notFoundError,
          title: 'Data Not Found',
          message: 'The requested information could not be found.',
          isRetryable: true,
          retryDelay: const Duration(seconds: 2),
          operation: operation,
          context: context,
        );
      
      default:
        return AccountProfileError(
          type: AccountProfileErrorType.serverError,
          title: 'Database Error',
          message: error.message ?? 'A database error occurred.',
          isRetryable: true,
          retryDelay: const Duration(seconds: 3),
          operation: operation,
          context: context,
        );
    }
  }

  /// Handle authentication errors
  AccountProfileError _handleAuthError(
    AuthException error,
    String? operation,
    Map<String, dynamic>? context,
  ) {
    switch (error.statusCode) {
      case '401':
        return AccountProfileError(
          type: AccountProfileErrorType.authError,
          title: 'Authentication Required',
          message: 'Please sign in to continue.',
          isRetryable: false,
          operation: operation,
          context: context,
        );
      
      case '403':
        return AccountProfileError(
          type: AccountProfileErrorType.permissionError,
          title: 'Access Forbidden',
          message: 'You don\'t have permission to access this resource.',
          isRetryable: false,
          operation: operation,
          context: context,
        );
      
      case '429':
        return AccountProfileError(
          type: AccountProfileErrorType.rateLimitError,
          title: 'Too Many Requests',
          message: 'Please wait a moment before trying again.',
          isRetryable: true,
          retryDelay: const Duration(seconds: 10),
          operation: operation,
          context: context,
        );
      
      default:
        return AccountProfileError(
          type: AccountProfileErrorType.authError,
          title: 'Authentication Error',
          message: error.message ?? 'An authentication error occurred.',
          isRetryable: false,
          operation: operation,
          context: context,
        );
    }
  }

  /// Get user-friendly error message for specific operations
  String getOperationErrorMessage(String operation, AccountProfileErrorType errorType) {
    final operationMessages = {
      'loadUserProfile': {
        AccountProfileErrorType.networkError: 'Unable to load your profile. Please check your connection.',
        AccountProfileErrorType.authError: 'Please sign in to view your profile.',
        AccountProfileErrorType.permissionError: 'You don\'t have permission to view this profile.',
        AccountProfileErrorType.notFoundError: 'Your profile could not be found.',
      },
      'loadFamilyMembers': {
        AccountProfileErrorType.networkError: 'Unable to load family members. Please check your connection.',
        AccountProfileErrorType.permissionError: 'You don\'t have permission to view family members.',
        AccountProfileErrorType.notFoundError: 'No family members found.',
      },
      'inviteFamilyMember': {
        AccountProfileErrorType.networkError: 'Unable to send invitation. Please check your connection.',
        AccountProfileErrorType.validationError: 'Invalid email address or member already exists.',
        AccountProfileErrorType.permissionError: 'You don\'t have permission to invite family members.',
        AccountProfileErrorType.rateLimitError: 'Too many invitations sent. Please wait before sending more.',
      },
      'updateProfile': {
        AccountProfileErrorType.networkError: 'Unable to save changes. Please check your connection.',
        AccountProfileErrorType.validationError: 'Please check your information and try again.',
        AccountProfileErrorType.permissionError: 'You don\'t have permission to update this profile.',
      },
    };

    return operationMessages[operation]?[errorType] ?? 
           'An error occurred while performing this action.';
  }

  /// Create retry function for failed operations
  Future<T> withRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    Duration initialDelay = const Duration(seconds: 1),
    double backoffMultiplier = 2.0,
    String? operationName,
  }) async {
    int attempts = 0;
    Duration delay = initialDelay;

    while (attempts < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        attempts++;
        
        final accountError = handleError(error, operation: operationName);
        
        // Don't retry if error is not retryable or we've exceeded max attempts
        if (!accountError.isRetryable || attempts >= maxRetries) {
          rethrow;
        }

        // Wait before retrying
        await Future.delayed(accountError.retryDelay ?? delay);
        delay = Duration(milliseconds: (delay.inMilliseconds * backoffMultiplier).round());
        
        debugPrint('Retrying $operationName (attempt $attempts/$maxRetries)');
      }
    }

    throw Exception('Max retries exceeded for $operationName');
  }

  /// Dispose resources
  void dispose() {
    _connectivitySubscription?.cancel();
    _connectivityController.close();
  }
}

/// Error types for account profile operations
enum AccountProfileErrorType {
  networkError,
  serverError,
  authError,
  permissionError,
  validationError,
  notFoundError,
  timeoutError,
  rateLimitError,
  dataError,
  unknown,
}

/// Structured error information for account profile operations
class AccountProfileError {
  final AccountProfileErrorType type;
  final String title;
  final String message;
  final bool isRetryable;
  final Duration? retryDelay;
  final String? operation;
  final Map<String, dynamic>? context;
  final dynamic originalError;

  const AccountProfileError({
    required this.type,
    required this.title,
    required this.message,
    required this.isRetryable,
    this.retryDelay,
    this.operation,
    this.context,
    this.originalError,
  });

  /// Get appropriate icon for the error type
  String get iconName {
    switch (type) {
      case AccountProfileErrorType.networkError:
        return 'wifi_off';
      case AccountProfileErrorType.serverError:
        return 'cloud_off';
      case AccountProfileErrorType.authError:
        return 'lock';
      case AccountProfileErrorType.permissionError:
        return 'block';
      case AccountProfileErrorType.validationError:
        return 'error';
      case AccountProfileErrorType.notFoundError:
        return 'search_off';
      case AccountProfileErrorType.timeoutError:
        return 'schedule';
      case AccountProfileErrorType.rateLimitError:
        return 'hourglass_empty';
      case AccountProfileErrorType.dataError:
        return 'data_usage';
      case AccountProfileErrorType.unknown:
        return 'help';
    }
  }

  /// Get appropriate color for the error type
  bool get isWarning {
    return type == AccountProfileErrorType.networkError ||
           type == AccountProfileErrorType.timeoutError ||
           type == AccountProfileErrorType.rateLimitError;
  }

  @override
  String toString() {
    return 'AccountProfileError(type: $type, title: $title, message: $message, operation: $operation)';
  }
}