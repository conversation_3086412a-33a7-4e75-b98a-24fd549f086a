import 'package:supabase_flutter/supabase_flutter.dart';
import 'supabase_service.dart';

/// Service to test database setup and verify enum fixes
class DatabaseTestService {
  static final SupabaseService _supabaseService = SupabaseService();

  /// Test all database functionality to ensure enum fixes work
  static Future<Map<String, dynamic>> runDatabaseTests() async {
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'tests': <String, dynamic>{},
      'overallSuccess': false,
      'errors': <String>[],
    };

    try {
      // Test 1: Verify valid activity types
      final validTypesTest = await _testValidActivityTypes();
      results['tests']['validActivityTypes'] = validTypesTest;

      // Test 2: Test temperature activity validation
      final tempValidationTest = await _testTemperatureValidation();
      results['tests']['temperatureValidation'] = tempValidationTest;

      // Test 3: Test enum values existence
      final enumTest = await _testEnumValues();
      results['tests']['enumValues'] = enumTest;

      // Test 4: Test database schema
      final schemaTest = await _testDatabaseSchema();
      results['tests']['databaseSchema'] = schemaTest;

      // Test 5: Test activity logging (if user is authenticated)
      final loggingTest = await _testActivityLogging();
      results['tests']['activityLogging'] = loggingTest;

      // Calculate overall success
      final allTests = results['tests'] as Map<String, dynamic>;
      final allSuccessful = allTests.values.every((test) => 
          test is Map && test['success'] == true);
      
      results['overallSuccess'] = allSuccessful;

      if (allSuccessful) {
        print('✅ All database tests passed successfully!');
      } else {
        print('❌ Some database tests failed. Check results for details.');
      }

    } catch (e) {
      results['errors'].add('Test execution failed: ${e.toString()}');
      print('❌ Database test execution failed: $e');
    }

    return results;
  }

  /// Test if we can get valid activity types
  static Future<Map<String, dynamic>> _testValidActivityTypes() async {
    try {
      final validTypes = await _supabaseService.getValidActivityTypes();
      
      final expectedTypes = [
        'feeding', 'sleep', 'diaper', 'growth', 'medicine', 'play', 
        'milestone', 'temperature', 'potty', 'bath', 'tummy_time', 
        'story_time', 'screen_time', 'skin_to_skin', 'outdoor_play', 
        'indoor_play', 'brush_teeth', 'pumping', 'custom'
      ];
      
      final missingTypes = expectedTypes.where((type) => !validTypes.contains(type)).toList();
      final extraTypes = validTypes.where((type) => !expectedTypes.contains(type)).toList();
      
      return {
        'success': missingTypes.isEmpty,
        'message': missingTypes.isEmpty 
            ? 'All expected activity types found' 
            : 'Missing activity types: $missingTypes',
        'data': {
          'found': validTypes,
          'expected': expectedTypes,
          'missing': missingTypes,
          'extra': extraTypes,
          'count': validTypes.length,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to get valid activity types: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test temperature activity type validation
  static Future<Map<String, dynamic>> _testTemperatureValidation() async {
    try {
      final tempValid = await _supabaseService.validateActivityType('temperature');
      final feedingValid = await _supabaseService.validateActivityType('feeding');
      final invalidValid = await _supabaseService.validateActivityType('invalid_type');
      
      final allValidationsCorrect = tempValid && feedingValid && !invalidValid;
      
      return {
        'success': allValidationsCorrect,
        'message': allValidationsCorrect 
            ? 'Activity type validation working correctly' 
            : 'Activity type validation has issues',
        'data': {
          'temperature': tempValid,
          'feeding': feedingValid,
          'invalid_type': invalidValid,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Temperature validation test failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test enum values existence in database
  static Future<Map<String, dynamic>> _testEnumValues() async {
    try {
      final client = await _supabaseService.client;
      
      // Test for temperature_unit enum
      final tempUnitResult = await client.rpc('test_enum_exists', params: {
        'enum_name': 'temperature_unit',
        'enum_values': ['celsius', 'fahrenheit']
      }).catchError((e) {
        // If function doesn't exist, try direct query
        return null;
      });
      
      // Test for activity_quality enum  
      final activityQualityResult = await client.rpc('test_enum_exists', params: {
        'enum_name': 'activity_quality', 
        'enum_values': ['excellent', 'good', 'fair', 'poor', 'not_rated']
      }).catchError((e) {
        return null;
      });
      
      return {
        'success': true, // We'll assume success if no errors thrown
        'message': 'Enum existence test completed',
        'data': {
          'temperature_unit': tempUnitResult,
          'activity_quality': activityQualityResult,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Enum existence test failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test database schema (tables exist)
  static Future<Map<String, dynamic>> _testDatabaseSchema() async {
    try {
      final client = await _supabaseService.client;
      
      final requiredTables = [
        'activities',
        'baby_profiles', 
        'user_profiles',
        'feeding_activities',
        'sleep_activities',
        'diaper_activities',
        'temperature_activities',
        'development_activities',
        'health_activities',
        'pumping_activities',
      ];
      
      final tableChecks = <String, bool>{};
      
      for (final table in requiredTables) {
        try {
          // Try to query the table with limit 0 to check if it exists
          await client.from(table).select('*').limit(0);
          tableChecks[table] = true;
        } catch (e) {
          tableChecks[table] = false;
        }
      }
      
      final missingTables = tableChecks.entries
          .where((entry) => !entry.value)
          .map((entry) => entry.key)
          .toList();
      
      return {
        'success': missingTables.isEmpty,
        'message': missingTables.isEmpty 
            ? 'All required tables exist' 
            : 'Missing tables: $missingTables',
        'data': {
          'tables': tableChecks,
          'missing': missingTables,
          'total': requiredTables.length,
          'found': requiredTables.length - missingTables.length,
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Database schema test failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Test activity logging (requires authentication)
  static Future<Map<String, dynamic>> _testActivityLogging() async {
    try {
      final currentUser = _supabaseService.currentUser;
      
      if (currentUser == null) {
        return {
          'success': true, // Not a failure, just not applicable
          'message': 'Skipped - user not authenticated',
          'skipped': true,
        };
      }

      // Try to get user's babies for testing
      final client = await _supabaseService.client;
      final babies = await client
          .from('baby_profiles')
          .select('id, name')
          .eq('user_id', currentUser.id)
          .limit(1);
      
      if (babies.isEmpty) {
        return {
          'success': true, // Not a failure, just no data
          'message': 'Skipped - no baby profiles found',
          'skipped': true,
        };
      }
      
      final babyId = babies.first['id'] as String;
      
      // Test temperature activity logging
      final logResult = await _supabaseService.logActivity(
        babyId: babyId,
        activityType: 'temperature',
        title: 'Database Test Temperature',
        notes: 'Automated test of temperature logging',
        activityData: {
          'temperature_value': 37.5,
          'unit': 'celsius',
          'measurement_location': 'forehead',
        },
      );
      
      final activityId = logResult['activity_id'] as String;
      
      // Verify the activity was created
      final activity = await client
          .from('activities')
          .select('*, temperature_activities(*)')
          .eq('id', activityId)
          .single();
      
      // Clean up test activity
      await client.from('activities').delete().eq('id', activityId);
      
      return {
        'success': true,
        'message': 'Temperature activity logging successful',
        'data': {
          'activityId': activityId,
          'babyId': babyId,
          'temperatureData': activity['temperature_activities'],
        }
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Activity logging test failed: ${e.toString()}',
        'error': e.toString(),
      };
    }
  }

  /// Quick database health check
  static Future<bool> isHealthy() async {
    try {
      final validTypes = await _supabaseService.getValidActivityTypes();
      final tempValid = await _supabaseService.validateActivityType('temperature');
      
      return validTypes.isNotEmpty && 
             validTypes.contains('temperature') && 
             tempValid;
    } catch (e) {
      print('Database health check failed: $e');
      return false;
    }
  }

  /// Get a summary of database status
  static Future<String> getDatabaseStatus() async {
    try {
      final results = await runDatabaseTests();
      final tests = results['tests'] as Map<String, dynamic>;
      final errors = results['errors'] as List<String>;
      
      final passedTests = tests.values.where((test) => 
          test is Map && test['success'] == true).length;
      final totalTests = tests.length;
      
      if (results['overallSuccess'] == true) {
        return '✅ Database Status: Healthy ($passedTests/$totalTests tests passed)';
      } else {
        final errorSummary = errors.isNotEmpty 
            ? '\nErrors: ${errors.join(', ')}'
            : '';
        return '❌ Database Status: Issues Found ($passedTests/$totalTests tests passed)$errorSummary';
      }
    } catch (e) {
      return '❌ Database Status: Unable to determine ($e)';
    }
  }
} 