import 'dart:typed_data';
import 'dart:io';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:permission_handler/permission_handler.dart';

/// Professional photo service for baby profile management
/// Handles image capture, compression, upload, and local caching
class PhotoService {
  static final PhotoService _instance = PhotoService._internal();
  factory PhotoService() => _instance;
  PhotoService._internal();

  final ImagePicker _picker = ImagePicker();
  static const int maxImageSize = 1024; // Max width/height in pixels
  static const int jpegQuality = 85; // Compression quality (0-100)
  static const String cacheBucket = 'baby-photos';
  
  /// Check and request necessary permissions
  Future<bool> checkPermissions() async {
    try {
      // Check camera permission
      final cameraStatus = await Permission.camera.status;
      if (cameraStatus.isDenied) {
        final cameraResult = await Permission.camera.request();
        if (!cameraResult.isGranted) {
          debugPrint('❌ Camera permission denied');
          return false;
        }
      }

      // Check photo library permission
      final photosStatus = await Permission.photos.status;
      if (photosStatus.isDenied) {
        final photosResult = await Permission.photos.request();
        if (!photosResult.isGranted) {
          debugPrint('❌ Photos permission denied');
          return false;
        }
      }

      debugPrint('✅ All permissions granted');
      return true;
    } catch (e) {
      debugPrint('❌ Error checking permissions: $e');
      return false;
    }
  }

  /// Take photo from camera
  Future<String?> takePhotoFromCamera() async {
    try {
      final hasPermission = await checkPermissions();
      if (!hasPermission) {
        throw Exception('Camera permission not granted');
      }

      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
        preferredCameraDevice: CameraDevice.rear,
      );

      if (photo != null) {
        debugPrint('📸 Photo captured from camera');
        return await _processAndUploadImage(photo);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error taking photo from camera: $e');
      rethrow;
    }
  }

  /// Select photo from gallery
  Future<String?> selectPhotoFromGallery() async {
    try {
      final hasPermission = await checkPermissions();
      if (!hasPermission) {
        throw Exception('Gallery permission not granted');
      }

      final XFile? photo = await _picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 2048,
        maxHeight: 2048,
        imageQuality: 90,
      );

      if (photo != null) {
        debugPrint('🖼️ Photo selected from gallery');
        return await _processAndUploadImage(photo);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error selecting photo from gallery: $e');
      rethrow;
    }
  }

  /// Process image: compress, resize, and upload to Supabase
  Future<String?> _processAndUploadImage(XFile imageFile) async {
    try {
      debugPrint('🔄 Processing image: ${imageFile.path}');
      
      // Read image bytes
      final Uint8List originalBytes = await imageFile.readAsBytes();
      debugPrint('📊 Original image size: ${(originalBytes.length / 1024).toStringAsFixed(1)} KB');

      // Compress and resize image
      final Uint8List compressedBytes = await _compressImage(originalBytes);
      debugPrint('📊 Compressed image size: ${(compressedBytes.length / 1024).toStringAsFixed(1)} KB');

      // Generate unique filename
      final String fileName = 'baby_${DateTime.now().millisecondsSinceEpoch}.jpg';
      
      // Upload to Supabase Storage
      final String? publicUrl = await _uploadToSupabase(compressedBytes, fileName);
      
      if (publicUrl != null) {
        // Cache locally for faster loading
        await _cacheImageLocally(publicUrl, compressedBytes);
        debugPrint('✅ Image uploaded and cached successfully');
        return publicUrl;
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ Error processing image: $e');
      rethrow;
    }
  }

  /// Compress image to professional size while maintaining quality
  Future<Uint8List> _compressImage(Uint8List imageBytes) async {
    try {
      // Decode image
      final img.Image? image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Failed to decode image');
      }

      debugPrint('📐 Original dimensions: ${image.width}x${image.height}');

      // Calculate new dimensions maintaining aspect ratio
      int newWidth = image.width;
      int newHeight = image.height;

      if (image.width > maxImageSize || image.height > maxImageSize) {
        if (image.width > image.height) {
          newWidth = maxImageSize;
          newHeight = (image.height * maxImageSize / image.width).round();
        } else {
          newHeight = maxImageSize;
          newWidth = (image.width * maxImageSize / image.height).round();
        }
      }

      debugPrint('📐 New dimensions: ${newWidth}x${newHeight}');

      // Resize image
      final img.Image resizedImage = img.copyResize(
        image,
        width: newWidth,
        height: newHeight,
        interpolation: img.Interpolation.linear,
      );

      // Convert to JPEG with compression
      final Uint8List compressedBytes = Uint8List.fromList(
        img.encodeJpg(resizedImage, quality: jpegQuality),
      );

      return compressedBytes;
    } catch (e) {
      debugPrint('❌ Error compressing image: $e');
      rethrow;
    }
  }

  /// Upload image to Supabase Storage
  Future<String?> _uploadToSupabase(Uint8List imageBytes, String fileName) async {
    try {
      final supabaseClient = Supabase.instance.client;
      
      // Upload to storage bucket
      await supabaseClient.storage
          .from(cacheBucket)
          .uploadBinary(fileName, imageBytes);

      // Get public URL
      final String publicUrl = supabaseClient.storage
          .from(cacheBucket)
          .getPublicUrl(fileName);

      debugPrint('☁️ Image uploaded to Supabase: $publicUrl');
      return publicUrl;
    } catch (e) {
      debugPrint('❌ Error uploading to Supabase: $e');
      // Don't rethrow here to allow fallback behavior
      return null;
    }
  }

  /// Cache image locally for faster access
  Future<void> _cacheImageLocally(String url, Uint8List imageBytes) async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final Directory cacheDir = Directory('${appDir.path}/photo_cache');
      
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      // Create cache file
      final String cacheKey = _getCacheKey(url);
      final File cacheFile = File('${cacheDir.path}/$cacheKey.jpg');
      
      await cacheFile.writeAsBytes(imageBytes);
      
      // Store metadata in SharedPreferences
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> metadata = {
        'url': url,
        'cached_at': DateTime.now().millisecondsSinceEpoch,
        'file_path': cacheFile.path,
      };
      
      await prefs.setString('photo_cache_$cacheKey', json.encode(metadata));
      debugPrint('💾 Image cached locally: ${cacheFile.path}');
    } catch (e) {
      debugPrint('⚠️ Warning: Failed to cache image locally: $e');
      // Don't rethrow - caching is optional
    }
  }

  /// Get cached image if available
  Future<File?> getCachedImage(String url) async {
    try {
      final String cacheKey = _getCacheKey(url);
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? metadataJson = prefs.getString('photo_cache_$cacheKey');
      
      if (metadataJson != null) {
        final Map<String, dynamic> metadata = json.decode(metadataJson);
        final File cacheFile = File(metadata['file_path']);
        
        if (await cacheFile.exists()) {
          debugPrint('🎯 Using cached image: ${cacheFile.path}');
          return cacheFile;
        } else {
          // Clean up invalid cache entry
          await prefs.remove('photo_cache_$cacheKey');
        }
      }
      
      return null;
    } catch (e) {
      debugPrint('⚠️ Error accessing cached image: $e');
      return null;
    }
  }

  /// Clear photo cache
  Future<void> clearCache() async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final Directory cacheDir = Directory('${appDir.path}/photo_cache');
      
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
      }
      
      // Clear SharedPreferences cache metadata
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys().where((key) => key.startsWith('photo_cache_'));
      
      for (final key in keys) {
        await prefs.remove(key);
      }
      
      debugPrint('🗑️ Photo cache cleared');
    } catch (e) {
      debugPrint('❌ Error clearing cache: $e');
    }
  }

  /// Get cache statistics
  Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final Directory cacheDir = Directory('${appDir.path}/photo_cache');
      
      if (!await cacheDir.exists()) {
        return {'count': 0, 'size': 0};
      }
      
      final List<FileSystemEntity> files = await cacheDir.list().toList();
      int totalSize = 0;
      
      for (final file in files) {
        if (file is File) {
          final stat = await file.stat();
          totalSize += stat.size;
        }
      }
      
      return {
        'count': files.length,
        'size': totalSize,
        'size_mb': (totalSize / (1024 * 1024)).toStringAsFixed(2),
      };
    } catch (e) {
      debugPrint('❌ Error getting cache stats: $e');
      return {'count': 0, 'size': 0};
    }
  }

  /// Delete photo from Supabase storage
  Future<bool> deletePhoto(String photoUrl) async {
    try {
      // Extract filename from URL
      final Uri uri = Uri.parse(photoUrl);
      final String fileName = uri.pathSegments.last;
      
      final supabaseClient = Supabase.instance.client;
      await supabaseClient.storage.from(cacheBucket).remove([fileName]);
      
      // Also remove from local cache
      final String cacheKey = _getCacheKey(photoUrl);
      final SharedPreferences prefs = await SharedPreferences.getInstance();
      final String? metadataJson = prefs.getString('photo_cache_$cacheKey');
      
      if (metadataJson != null) {
        final Map<String, dynamic> metadata = json.decode(metadataJson);
        final File cacheFile = File(metadata['file_path']);
        
        if (await cacheFile.exists()) {
          await cacheFile.delete();
        }
        
        await prefs.remove('photo_cache_$cacheKey');
      }
      
      debugPrint('🗑️ Photo deleted from storage and cache');
      return true;
    } catch (e) {
      debugPrint('❌ Error deleting photo: $e');
      return false;
    }
  }

  /// Generate cache key from URL
  String _getCacheKey(String url) {
    return url.hashCode.abs().toString();
  }

  /// Validate image format
  bool isValidImageFormat(String path) {
    final String extension = path.toLowerCase().split('.').last;
    return ['jpg', 'jpeg', 'png', 'webp'].contains(extension);
  }

  /// Get optimal image picker configuration for platform
  static ImagePickerConfig getOptimalConfig() {
    return ImagePickerConfig(
      maxWidth: 2048,
      maxHeight: 2048,
      imageQuality: 90,
      enableRotation: true,
    );
  }
}

/// Configuration class for image picker
class ImagePickerConfig {
  final double maxWidth;
  final double maxHeight;
  final int imageQuality;
  final bool enableRotation;

  const ImagePickerConfig({
    required this.maxWidth,
    required this.maxHeight,
    required this.imageQuality,
    required this.enableRotation,
  });
}
