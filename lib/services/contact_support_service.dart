import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class ContactSupportService {
  static const String supportEmail = '<EMAIL>';
  
  static ContactSupportService? _instance;
  static ContactSupportService get instance => _instance ??= ContactSupportService._();
  
  ContactSupportService._();

  /// Get comprehensive device and app information for support
  Future<Map<String, String>> getDeviceAndAppInfo() async {
    final Map<String, String> info = {};
    
    try {
      // Get package info
      final packageInfo = await PackageInfo.fromPlatform();
      info['App Name'] = packageInfo.appName;
      info['App Version'] = packageInfo.version;
      info['Build Number'] = packageInfo.buildNumber;
      info['Package Name'] = packageInfo.packageName;
      
      // Get device info
      final deviceInfo = DeviceInfoPlugin();
      
      if (kIsWeb) {
        final webInfo = await deviceInfo.webBrowserInfo;
        info['Platform'] = 'Web';
        info['Browser'] = webInfo.browserName.name;
        info['User Agent'] = webInfo.userAgent ?? 'Unknown';
        info['Platform Version'] = webInfo.platform ?? 'Unknown';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        info['Platform'] = 'Android';
        info['Device Model'] = '${androidInfo.brand} ${androidInfo.model}';
        info['Android Version'] = 'Android ${androidInfo.version.release} (API ${androidInfo.version.sdkInt})';
        info['Device ID'] = androidInfo.id;
        info['Manufacturer'] = androidInfo.manufacturer;
        info['Hardware'] = androidInfo.hardware;
        info['Board'] = androidInfo.board;
        info['Display'] = androidInfo.display;
        info['Fingerprint'] = androidInfo.fingerprint;
        info['Host'] = androidInfo.host;
        info['Product'] = androidInfo.product;
        info['Tags'] = androidInfo.tags;
        info['Type'] = androidInfo.type;
        info['Is Physical Device'] = androidInfo.isPhysicalDevice.toString();
        info['Supported ABIs'] = androidInfo.supportedAbis.join(', ');
        info['System Features'] = androidInfo.systemFeatures.join(', ');
      } else if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        info['Platform'] = 'iOS';
        info['Device Model'] = iosInfo.model;
        info['Device Name'] = iosInfo.name;
        info['iOS Version'] = '${iosInfo.systemName} ${iosInfo.systemVersion}';
        info['Device ID'] = iosInfo.identifierForVendor ?? 'Unknown';
        info['Machine'] = iosInfo.utsname.machine;
        info['Is Physical Device'] = iosInfo.isPhysicalDevice.toString();
        info['Localized Model'] = iosInfo.localizedModel;
      } else if (Platform.isMacOS) {
        final macInfo = await deviceInfo.macOsInfo;
        info['Platform'] = 'macOS';
        info['Computer Name'] = macInfo.computerName;
        info['Host Name'] = macInfo.hostName;
        info['Arch'] = macInfo.arch;
        info['Model'] = macInfo.model;
        info['Kernel Version'] = macInfo.kernelVersion;
        info['OS Release'] = macInfo.osRelease;
        info['Major Version'] = macInfo.majorVersion.toString();
        info['Minor Version'] = macInfo.minorVersion.toString();
        info['Patch Version'] = macInfo.patchVersion.toString();
        info['CPU Frequency'] = '${macInfo.cpuFrequency} MHz';
        info['Memory Size'] = '${(macInfo.memorySize / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
      } else if (Platform.isWindows) {
        final windowsInfo = await deviceInfo.windowsInfo;
        info['Platform'] = 'Windows';
        info['Computer Name'] = windowsInfo.computerName;
        info['Number of Cores'] = windowsInfo.numberOfCores.toString();
        info['System Memory'] = '${(windowsInfo.systemMemoryInMegabytes / 1024).toStringAsFixed(1)} GB';
        info['User Name'] = windowsInfo.userName;
        info['Major Version'] = windowsInfo.majorVersion.toString();
        info['Minor Version'] = windowsInfo.minorVersion.toString();
        info['Build Number'] = windowsInfo.buildNumber.toString();
        info['Platform ID'] = windowsInfo.platformId.toString();
        info['CSD Version'] = windowsInfo.csdVersion ?? 'Unknown';
        info['Service Pack Major'] = windowsInfo.servicePackMajor.toString();
        info['Service Pack Minor'] = windowsInfo.servicePackMinor.toString();
        info['Suite Mask'] = windowsInfo.suitMask.toString();
        info['Product Type'] = windowsInfo.productType.toString();
      } else if (Platform.isLinux) {
        final linuxInfo = await deviceInfo.linuxInfo;
        info['Platform'] = 'Linux';
        info['Name'] = linuxInfo.name;
        info['Version'] = linuxInfo.version ?? 'Unknown';
        info['ID'] = linuxInfo.id ?? 'Unknown';
        info['ID Like'] = linuxInfo.idLike?.join(', ') ?? 'Unknown';
        info['Version Codename'] = linuxInfo.versionCodename ?? 'Unknown';
        info['Version ID'] = linuxInfo.versionId ?? 'Unknown';
        info['Pretty Name'] = linuxInfo.prettyName ?? 'Unknown';
        info['Build ID'] = linuxInfo.buildId ?? 'Unknown';
        info['Variant'] = linuxInfo.variant ?? 'Unknown';
        info['Variant ID'] = linuxInfo.variantId ?? 'Unknown';
        info['Machine ID'] = linuxInfo.machineId ?? 'Unknown';
      }
      
      // Add timestamp
      info['Report Generated'] = DateTime.now().toIso8601String();
      
    } catch (e) {
      info['Error'] = 'Failed to get device info: $e';
    }
    
    return info;
  }

  /// Format device info for email body
  String formatDeviceInfoForEmail(Map<String, String> deviceInfo) {
    final buffer = StringBuffer();
    buffer.writeln('\n\n--- DEVICE & APP INFORMATION ---');
    
    deviceInfo.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    
    buffer.writeln('--- END DEVICE INFO ---');
    return buffer.toString();
  }

  /// Launch email app with pre-filled support information
  Future<bool> launchEmailSupport({
    required String subject,
    required String body,
    Map<String, String>? deviceInfo,
  }) async {
    try {
      final deviceInfoMap = deviceInfo ?? await getDeviceAndAppInfo();
      final fullBody = body + formatDeviceInfoForEmail(deviceInfoMap);
      
      final Uri emailUri = Uri(
        scheme: 'mailto',
        path: supportEmail,
        query: _encodeQueryParameters({
          'subject': subject,
          'body': fullBody,
        }),
      );

      if (await canLaunchUrl(emailUri)) {
        return await launchUrl(emailUri);
      } else {
        return false;
      }
    } catch (e) {
      debugPrint('Error launching email: $e');
      return false;
    }
  }

  /// Copy email address to clipboard
  Future<void> copyEmailToClipboard() async {
    await Clipboard.setData(ClipboardData(text: supportEmail));
  }

  /// Encode query parameters for URL
  String _encodeQueryParameters(Map<String, String> params) {
    return params.entries
        .map((e) => '${Uri.encodeComponent(e.key)}=${Uri.encodeComponent(e.value)}')
        .join('&');
  }

  /// Get formatted device info as a readable string
  Future<String> getFormattedDeviceInfo() async {
    final deviceInfo = await getDeviceAndAppInfo();
    final buffer = StringBuffer();
    
    deviceInfo.forEach((key, value) {
      buffer.writeln('$key: $value');
    });
    
    return buffer.toString();
  }

  /// Submit support request (for future API integration)
  Future<bool> submitSupportRequest({
    required String type,
    required String subject,
    required String description,
    required String userEmail,
    String? userName,
    Map<String, String>? deviceInfo,
  }) async {
    try {
      final deviceInfoMap = deviceInfo ?? await getDeviceAndAppInfo();
      
      // For now, this would integrate with your backend API
      // Example structure for the request:
      final requestData = {
        'type': type,
        'subject': subject,
        'description': description,
        'user_email': userEmail,
        'user_name': userName,
        'device_info': deviceInfoMap,
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      // TODO: Implement actual API call to your support system
      debugPrint('Support request data: $requestData');
      
      // Simulate API call delay
      await Future.delayed(Duration(seconds: 2));
      
      // For now, return true to simulate success
      return true;
    } catch (e) {
      debugPrint('Error submitting support request: $e');
      return false;
    }
  }
}