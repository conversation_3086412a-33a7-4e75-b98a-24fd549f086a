import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';

import '../core/app_export.dart';
import '../models/activity_log.dart';
import '../models/ai_insight.dart';
import '../models/baby_profile.dart';

class AIAnalysisService {
  static final AIAnalysisService _instance = AIAnalysisService._internal();
  
  factory AIAnalysisService() => _instance;
  
  AIAnalysisService._internal();

  final SupabaseService _supabaseService = SupabaseService();
  final OpenAIService _openAIService = OpenAIService();

  // Cache duration: 24 hours
  static const Duration _cacheExpiration = Duration(hours: 24);

  /// Get cached insights from database
  Future<Map<String, dynamic>?> getCachedInsights(String babyId) async {
    try {
      debugPrint('🔍 Fetching cached insights for baby: $babyId');
      
      final cachedInsights = await _supabaseService.select(
        'ai_insights',
        filters: {'baby_id': babyId},
        orderBy: 'generated_at',
        ascending: false,
        limit: 50, // Get all types of insights
      );

      if (cachedInsights.isNotEmpty) {
        debugPrint('✅ Found ${cachedInsights.length} cached insights');
        
        // Check if insights are stale (older than 24 hours) - use local time
        final latestInsight = cachedInsights.first;
        final generatedAt = DateTime.parse(latestInsight['generated_at']);
        final now = DateTime.now();
        final age = now.difference(generatedAt);
        
        if (age.inHours > 24) {
          debugPrint('⏰ Cached insights are ${age.inHours} hours old, considered stale');
          // Clear stale insights and return null to force regeneration
          await clearCachedInsights(babyId);
          return null;
        }
        
        // Check if we should invalidate cache based on data availability vs insight quality
        // Quick check for available activity data
        final recentActivities = await _supabaseService.select(
          'activity_logs',
          filters: {'baby_id': babyId},
          orderBy: 'recorded_at',
          ascending: false,
          limit: 20,
        );
        
        // Check if we have low confidence insights but actual data exists
        bool hasLowConfidenceInsights = false;
        bool hasActualData = recentActivities.length >= 5; // At least 5 activities
        
        for (final insight in cachedInsights) {
          final confidence = insight['confidence'] as num? ?? 0;
          final description = insight['description'] as String? ?? '';
          
          if (confidence <= 0.15 && (description.toLowerCase().contains('start logging') || 
                                   description.toLowerCase().contains('not enough data') ||
                                   description.toLowerCase().contains('no data yet'))) {
            hasLowConfidenceInsights = true;
          }
        }
        
        // If we have actual data but cached insights are low confidence placeholders, refresh
        if (hasActualData && hasLowConfidenceInsights) {
          debugPrint('🔄 Cache invalid: Found real data but low confidence insights - refreshing');
          await clearCachedInsights(babyId);
          return null;
        }
        
        // Rebuild the insights structure from individual records
        final insights = <String, dynamic>{};
        for (final insight in cachedInsights) {
          final type = insight['insight_type'] as String;
          final data = insight['data'] as Map<String, dynamic>? ?? {};
          final confidence = insight['confidence'] as num? ?? 0;
          final description = insight['description'] as String? ?? '';
          
          insights['${type}Analysis'] = {
            'type': type,
            'title': insight['title'] ?? 'Analysis',
            'description': description,
            'confidence': confidence,
            'data': data,
          };
        }
        
        // Use timestamps as-is from cache (local time)
        insights['lastUpdated'] = cachedInsights.first['generated_at'];
        insights['cacheExpiresAt'] = cachedInsights.first['expires_at'];
        
        debugPrint('✅ Cache retrieved with timestamp: ${cachedInsights.first['generated_at']}');
        
        return insights;
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error retrieving cached insights: $e');
      return null;
    }
  }

  /// Store insights with proper insight_type to fix database constraint
  Future<void> storeInsights(String babyId, Map<String, dynamic> insights) async {
    try {
      debugPrint('💾 Storing AI insights for baby: $babyId');
      
      final insightTypes = {
        'sleep': insights['sleepAnalysis'],
        'feeding': insights['feedingAnalysis'],
        'growth': insights['growthAnalysis'],
        'milestone': insights['milestoneAnalysis'],
      };
      
      for (final entry in insightTypes.entries) {
        final type = entry.key;
        final typeData = entry.value;
        
        if (typeData == null || typeData is! Map<String, dynamic>) {
          continue;
        }
        
        // Use device local time consistently
        final now = DateTime.now();
        final expiresAt = now.add(_cacheExpiration);
        debugPrint('🕒 Storing insight with local timestamp: $now');
        
        final insightData = {
          'baby_id': babyId,
          'insight_type': type,
          'title': typeData['title'] ?? '$type Analysis',
          'description': typeData['description'] ?? 'No description available',
          'data': typeData['data'] ?? {},
          'confidence': typeData['confidence'] ?? 0,
          'generated_at': now.toString(),
          'expires_at': expiresAt.toString(),
        };

        try {
          await _supabaseService.upsert(
            'ai_insights',
            insightData,
            onConflict: 'baby_id,insight_type',
          );
        } catch (e, stackTrace) {
          debugPrint('❌ Error storing $type insight: $e');
        }
      }
    } catch (e) {
      debugPrint('❌ Error storing insights: $e');
    }
  }

  /// Check if there are new activities since a given timestamp
  Future<bool> hasNewActivitiesSince(String babyId, DateTime timestamp) async {
    try {
      // Use raw SQL query for timestamp comparison with local time
      final client = await _supabaseService.client;
      final result = await client
          .from('activity_logs')
          .select('id')
          .eq('baby_id', babyId)
          .gt('recorded_at', timestamp.toString())
          .limit(1);
      
      return result.isNotEmpty;
    } catch (e) {
      debugPrint('❌ Error checking for new activities: $e');
      return true; // Assume new activities exist on error
    }
  }

  /// Main method to generate comprehensive AI insights with professional caching
  Future<Map<String, dynamic>> generateComprehensiveInsights({
    required String babyId,
    required BabyProfile babyProfile,
    bool forceRefresh = false,
  }) async {
    try {
      debugPrint('🔄 Starting AI insights generation for baby: $babyId');
      debugPrint('🔄 Force refresh: $forceRefresh');
      
      // CRITICAL: Check minimum activity requirement before any processing
      final activityCount = await _supabaseService.select(
        'activity_logs',
        filters: {'baby_id': babyId},
        orderBy: 'recorded_at',
        ascending: false,
        limit: 10,
      );
      
      if (activityCount.length < 5) {
        debugPrint('⬇️ Insufficient activities for AI analysis (${activityCount.length}/5 minimum)');
        return _getEmptyInsightsTemplate();
      }
      
      // Only check cache if we're not forcing refresh
      Map<String, dynamic>? cached;
      if (!forceRefresh) {
        cached = await getCachedInsights(babyId).timeout(
          Duration(seconds: 10),
          onTimeout: () {
            debugPrint('⏰ Cache lookup timed out after 10 seconds');
            return null;
          },
        );
        
        if (cached != null) {
          debugPrint('✅ Using cached insights (no new AI generation)');
        // Add overall summary if missing (this is just a local computation, not AI generation)
        if (!cached.containsKey('overallSummary')) {
          debugPrint('📝 Computing summary from cached insights');
          try {
            cached['overallSummary'] = _generateOverallSummary(cached);
          } catch (e) {
            debugPrint('⚠️ Error generating summary from cached insights: $e');
            cached['overallSummary'] = {
              'mainSummary': 'AI insights are available based on your logged activities.',
              'detailedDescription': 'Continue logging activities for updated insights.',
              'totalInsights': 0,
              'availableRecommendations': 0,
              'topRecommendations': [],
              'hasData': true,
            };
          }
        }
          return cached;
        }
      } else {
        debugPrint('🔄 Force refresh requested - skipping cache lookup');
      }

      debugPrint('🔄 Generating fresh AI insights...');
      
      // Gather all baby data with timeout
      final babyData = await _gatherBabyData(babyId, babyProfile).timeout(
        Duration(seconds: 15),
        onTimeout: () {
          debugPrint('⏰ Data gathering timed out after 15 seconds');
          throw TimeoutException('Data gathering timed out', Duration(seconds: 15));
        },
      );
      
      // Generate insights using OpenAI with timeout
      final insights = await _generateAIInsights(babyData).timeout(
        Duration(seconds: 30),
        onTimeout: () {
          debugPrint('⏰ AI insights generation timed out after 30 seconds');
          throw TimeoutException('AI insights generation timed out', Duration(seconds: 30));
        },
      );
      
      // Generate overall summary (with error handling)
      try {
        insights['overallSummary'] = _generateOverallSummary(insights);
      } catch (e) {
        debugPrint('⚠️ Error generating overall summary: $e');
        insights['overallSummary'] = {
          'mainSummary': 'AI insights are being prepared for your baby\'s care patterns.',
          'detailedDescription': 'Continue logging activities to unlock personalized insights and recommendations.',
          'totalInsights': 0,
          'availableRecommendations': 0,
          'topRecommendations': [],
          'hasData': false,
        };
      }
      
      // Cache the insights (don't block on this)
      storeInsights(babyId, insights).catchError((e) {
        debugPrint('⚠️ Failed to cache insights: $e');
      });

      debugPrint('✅ Fresh AI insights generated successfully');
      return insights;
    } catch (e) {
      debugPrint('❌ Error generating insights: $e');
      if (e.toString().contains('timeout') || e.toString().contains('TimeoutException')) {
        debugPrint('⏰ Timeout occurred, returning fallback insights');
      }
      final emptyTemplate = _getEmptyInsightsTemplate();
      try {
        emptyTemplate['overallSummary'] = _generateOverallSummary(emptyTemplate);
      } catch (e) {
        debugPrint('⚠️ Error generating summary for empty template: $e');
        emptyTemplate['overallSummary'] = {
          'mainSummary': 'AI insights are being prepared for your baby\'s care patterns.',
          'detailedDescription': 'Continue logging activities to unlock personalized insights and recommendations.',
          'totalInsights': 0,
          'availableRecommendations': 0,
          'topRecommendations': [],
          'hasData': false,
        };
      }
      return emptyTemplate;
    }
  }

  Future<Map<String, dynamic>> _generateAIInsights(Map<String, dynamic> babyData) async {
    try {
      final openAIClient = OpenAIClient(await _openAIService.dio);
      
      // Prepare data for analysis
      final babyProfile = babyData['babyProfile'] as BabyProfile;
      final sleepLogs = babyData['sleep'] as List<ActivityLog>;
      final feedingLogs = babyData['feeding'] as List<ActivityLog>;
      final growthLogs = babyData['growth'] as List<ActivityLog>;
      
      // Create analysis prompt
      final prompt = _createAnalysisPrompt(
        babyProfile: babyProfile,
        sleepLogs: sleepLogs,
        feedingLogs: feedingLogs,
        growthLogs: growthLogs,
        milestoneLogs: babyData['milestone'] as List<ActivityLog>?,
      );

      // Get AI analysis
      final completion = await openAIClient.createChatCompletion(
        messages: [
          Message(role: 'system', content: _getAnalysisSystemPrompt()),
          Message(role: 'user', content: prompt),
        ],
        model: 'gpt-4o-mini',  // Correct model name as per OpenAI docs
        options: {
          'max_completion_tokens': 1000,
        },
      );

      // Parse AI response - handle potential markdown formatting
      String responseText = completion.text.trim();
      
      // Remove markdown code blocks if present
      if (responseText.startsWith('```json')) {
        responseText = responseText.substring(7); // Remove '```json'
      }
      if (responseText.startsWith('```')) {
        responseText = responseText.substring(3); // Remove '```'
      }
      if (responseText.endsWith('```')) {
        responseText = responseText.substring(0, responseText.length - 3); // Remove trailing '```'
      }
      
      responseText = responseText.trim();
      debugPrint('🔧 Cleaned response text: ${responseText.substring(0, responseText.length > 200 ? 200 : responseText.length)}...');
      
      final analysisJson = json.decode(responseText);
      
      // Calculate confidence scores
      final sleepConfidence = _calculateConfidence(sleepLogs, 'sleep');
      final feedingConfidence = _calculateConfidence(feedingLogs, 'feeding');
      final growthConfidence = _calculateConfidence(growthLogs, 'growth');

      // Structure insights with category-specific data to ensure each category has unique content
      // Use local timestamp for user-friendly time calculations
      final now = DateTime.now();
      debugPrint('🕒 Generating fresh insights with local timestamp: $now');
      return {
        'sleepAnalysis': {
          'type': 'sleep',
          'title': analysisJson['sleep']['title'] ?? 'Sleep Pattern Analysis',
          'description': analysisJson['sleep']['description'] ?? 'Analyzing sleep patterns and quality from recent logs',
          'confidence': sleepConfidence,
          'data': {
            ...(analysisJson['sleep']['data'] ?? {}),
            'analysisType': 'sleep',
            'category': 'Sleep Analytics',
            'subtitle': analysisJson['sleep']['subtitle'] ?? 'Sleep patterns & quality analysis',
            'recommendations': analysisJson['sleep']['recommendations'] ?? [
              'Continue tracking sleep sessions for better insights',
              'Aim for consistent bedtime routine'
            ],
          },
        },
        'feedingAnalysis': {
          'type': 'feeding',
          'title': analysisJson['feeding']['title'] ?? 'Feeding Schedule Analysis',
          'description': analysisJson['feeding']['description'] ?? 'Analyzing feeding patterns and nutrition from recent logs',
          'confidence': feedingConfidence,
          'data': {
            ...(analysisJson['feeding']['data'] ?? {}),
            'analysisType': 'feeding',
            'category': 'Feeding Analytics',
            'subtitle': analysisJson['feeding']['subtitle'] ?? 'Feeding schedule & intake tracking',
            'recommendations': analysisJson['feeding']['recommendations'] ?? [
              'Continue tracking feeding times and amounts',
              "Monitor baby's hunger cues"
            ],
          },
        },
        'growthAnalysis': {
          'type': 'growth',
          'title': analysisJson['growth']['title'] ?? 'Growth Trend Analysis',
          'description': analysisJson['growth']['description'] ?? 'Analyzing growth trends and development from measurements',
          'confidence': growthConfidence,
          'data': {
            ...(analysisJson['growth']['data'] ?? {}),
            'analysisType': 'growth',
            'category': 'Growth Analytics',
            'subtitle': analysisJson['growth']['subtitle'] ?? 'Growth milestones & trends',
            'recommendations': analysisJson['growth']['recommendations'] ?? [
              'Continue regular growth measurements',
              'Schedule pediatric checkups'
            ],
          },
        },
        'milestoneAnalysis': {
          'type': 'milestone',
          'title': analysisJson['milestone']?['title'] ?? 'Milestone Predictions',
          'description': analysisJson['milestone']?['description'] ?? 'AI-powered predictions for upcoming developmental milestones',
          'confidence': 0.85,
          'data': {
            ...(analysisJson['milestone']?['data'] ?? {}),
            'analysisType': 'milestone',
            'category': 'Milestone Analytics',
            'subtitle': 'Developmental predictions & tracking',
            'nextMilestone': analysisJson['milestone']?['data']?['nextMilestone'] ?? 'First steps',
            'expectedAge': analysisJson['milestone']?['data']?['expectedAge'] ?? '12-14 months',
            'currentProgress': analysisJson['milestone']?['data']?['currentProgress'] ?? 'On track',
            'recommendations': analysisJson['milestone']?['recommendations'] ?? [
              'Continue documenting milestones',
              'Provide appropriate stimulation for development'
            ],
          },
        },
        'lastUpdated': now.toString(),
      };
    } catch (e) {
      debugPrint('❌ Error generating AI insights: $e');
      // Use existing gathered data instead of re-gathering
      final babyProfile = babyData['babyProfile'] as BabyProfile;
      final sleepLogs = babyData['sleep'] as List<ActivityLog>;
      final feedingLogs = babyData['feeding'] as List<ActivityLog>;
      final growthLogs = babyData['growth'] as List<ActivityLog>;
      final diaperLogs = babyData['diaper'] as List<ActivityLog>? ?? <ActivityLog>[];
      final dataQuality = babyData['dataQuality'] as Map<String, dynamic>? ?? {};
      
      debugPrint('🔍 AI Fallback Analysis with gathered data:');
      debugPrint('   😴 Sleep: ${sleepLogs.length} logs');
      debugPrint('   🍼 Feeding: ${feedingLogs.length} logs');
      debugPrint('   📈 Growth: ${growthLogs.length} logs');
      debugPrint('   👶 Diaper: ${diaperLogs.length} logs');
      debugPrint('   📊 Total activities: ${dataQuality['totalActivities'] ?? 0}');
      
      // If we have substantial data, generate intelligent analysis instead of placeholders
      final totalDataPoints = sleepLogs.length + feedingLogs.length + growthLogs.length + diaperLogs.length;
      final hasSubstantialData = totalDataPoints >= 5 || (dataQuality['recentFeedings'] ?? 0) >= 3;
      
      if (hasSubstantialData) {
        debugPrint('💡 Found substantial data ($totalDataPoints activities), generating intelligent fallback analysis');
        
        // Generate smarter feeding analysis
        String feedingDescription = 'No feeding data available yet';
        double feedingConfidence = 0.1;
        Map<String, dynamic> feedingDataAnalysis = {
          'frequency': 'No data yet',
          'amount': 'No data yet', 
          'pattern': 'No data yet',
          'recommendations': ['Log your first feeding to get started!']
        };
        
        if (feedingLogs.isNotEmpty) {
          final recentFeedings = dataQuality['recentFeedings'] ?? feedingLogs.length;
          final avgPerDay = (feedingLogs.length / 7).toStringAsFixed(1);
          
          // Calculate total volume if available
          double totalVolume = 0;
          int volumeCount = 0;
          for (final log in feedingLogs.take(10)) {
            final quantity = log.data['quantity'];
            if (quantity != null && quantity is num) {
              totalVolume += quantity;
              volumeCount++;
            }
          }
          
          final avgVolume = volumeCount > 0 ? (totalVolume / volumeCount).toStringAsFixed(0) : '150';
          
          feedingDescription = 'Analyzing ${feedingLogs.length} feeding records showing good nutrition tracking';
          feedingConfidence = _calculateConfidence(feedingLogs, 'feeding');
          feedingDataAnalysis = {
            'frequency': '$avgPerDay feeds/day average over last week',
            'amount': '${avgVolume}ml average per feeding',
            'pattern': feedingLogs.length >= 5 ? 'Consistent feeding pattern developing' : 'Building feeding routine',
            'recommendations': [
              'Continue tracking feeding times and amounts',
              "Monitor baby's hunger cues for optimal timing",
              if (recentFeedings >= 5) 'Great job maintaining feeding records!'
            ],
          };
        }
        
        // Generate smarter sleep analysis
        String sleepDescription = 'No sleep data available yet';
        double sleepConfidence = 0.1;
        Map<String, dynamic> sleepDataAnalysis = {
          'averageDuration': 'No data yet',
          'quality': 'No data yet',
          'pattern': 'No data yet',
          'recommendations': ['Log your first sleep session to get started!']
        };
        
        if (sleepLogs.isNotEmpty) {
          final recentSleep = dataQuality['recentSleep'] ?? sleepLogs.length;
          double totalHours = 0;
          int durationCount = 0;
          
          for (final log in sleepLogs.take(10)) {
            if (log.duration != null) {
              totalHours += log.duration!.inHours;
              durationCount++;
            }
          }
          
          final avgHours = durationCount > 0 ? (totalHours / durationCount).toStringAsFixed(1) : '8.0';
          
          sleepDescription = 'Analyzing ${sleepLogs.length} sleep records to identify rest patterns';
          sleepConfidence = _calculateConfidence(sleepLogs, 'sleep');
          sleepDataAnalysis = {
            'averageDuration': '${avgHours} hours average sleep duration',
            'quality': 'Sleep tracking in progress',
            'pattern': sleepLogs.length >= 3 ? 'Sleep pattern emerging from data' : 'Continue logging for pattern analysis',
            'recommendations': [
              'Continue logging sleep sessions consistently',
              'Aim for regular bedtime routine',
              if (recentSleep >= 3) 'Good sleep tracking consistency!'
            ],
          };
        }
        
        final now = DateTime.now();
        return {
          'sleepAnalysis': {
            'type': 'sleep',
            'title': 'Sleep Pattern Analysis',
            'description': sleepLogs.isNotEmpty 
                ? 'Analyzing ${sleepLogs.length} sleep records to identify patterns'
                : 'Log sleep sessions to unlock detailed sleep analysis',
            'confidence': _calculateConfidence(sleepLogs, 'sleep'),
            'data': {
              'analysisType': 'sleep',
              'category': 'Sleep Analytics',
              'subtitle': 'Sleep patterns & quality analysis',
              'averageDuration': sleepLogs.isNotEmpty ? '${(sleepLogs.map((l) => l.duration?.inHours ?? 0).reduce((a, b) => a + b) / sleepLogs.length).toStringAsFixed(1)} hours' : 'Log sleep to see average',
              'quality': 'Tracking in progress',
              'pattern': sleepLogs.length >= 3 ? 'Pattern emerging' : 'Need more data for pattern',
              'recommendations': sleepLogs.isNotEmpty 
                  ? ['Continue logging sleep sessions', 'Aim for consistent bedtime routine'] 
                  : ['Start logging sleep sessions to see insights'],
            }
          },
          'feedingAnalysis': {
            'type': 'feeding',
            'title': 'Feeding Schedule Analysis', 
            'description': feedingLogs.isNotEmpty 
                ? 'Analyzing ${feedingLogs.length} feeding records for patterns'
                : 'Log feeding sessions to unlock detailed feeding analysis',
            'confidence': _calculateConfidence(feedingLogs, 'feeding'),
            'data': {
              'analysisType': 'feeding',
              'category': 'Feeding Analytics',
              'subtitle': 'Feeding schedule & intake tracking',
              'frequency': feedingLogs.isNotEmpty ? '${(feedingLogs.length / 7).toStringAsFixed(1)} feeds/day average' : 'Log feeds to see frequency',
              'amount': 'Tracking in progress',
              'pattern': feedingLogs.length >= 5 ? 'Pattern developing' : 'Need more data for pattern',
              'recommendations': feedingLogs.isNotEmpty 
                  ? ['Continue tracking feeding times', "Monitor baby's hunger cues"] 
                  : ['Start logging feedings to see insights'],
            }
          },
          'growthAnalysis': {
            'type': 'growth',
            'title': 'Growth Trend Analysis',
            'description': growthLogs.isNotEmpty 
                ? 'Monitoring growth with ${growthLogs.length} measurements'
                : 'Add growth measurements to track development',
            'confidence': _calculateConfidence(growthLogs, 'growth'),
            'data': {
              'analysisType': 'growth',
              'category': 'Growth Analytics',
              'subtitle': 'Growth milestones & trends',
              'weightTrend': growthLogs.isNotEmpty ? 'Tracking in progress' : 'Add measurements to see trends',
              'heightTrend': growthLogs.isNotEmpty ? 'Tracking in progress' : 'Add measurements to see trends',
              'recommendations': growthLogs.isNotEmpty 
                  ? ['Continue regular measurements', 'Schedule pediatric checkups'] 
                  : ['Add weight and height measurements regularly'],
            }
          },
          'lastUpdated': now.toString(),
        };
      }
      
      // Return empty template only if truly no data
      final now = DateTime.now();
      return {
        'sleepAnalysis': {
          'type': 'sleep',
          'title': 'Sleep Pattern Analysis',
          'description': 'Start logging sleep sessions to unlock AI insights',
          'confidence': 0.1,
          'data': {
            'analysisType': 'sleep',
            'category': 'Sleep Analytics',
            'subtitle': 'Sleep patterns & quality analysis',
            'averageDuration': 'No data yet',
            'quality': 'No data yet',
            'pattern': 'No data yet',
            'recommendations': ['Log your first sleep session to get started!'],
          }
        },
        'feedingAnalysis': {
          'type': 'feeding',
          'title': 'Feeding Schedule Analysis',
          'description': 'Start logging feedings to unlock AI insights',
          'confidence': 0.1,
          'data': {
            'analysisType': 'feeding',
            'category': 'Feeding Analytics',
            'subtitle': 'Feeding schedule & intake tracking',
            'frequency': 'No data yet',
            'amount': 'No data yet',
            'pattern': 'No data yet',
            'recommendations': ['Log your first feeding to get started!'],
          }
        },
        'growthAnalysis': {
          'type': 'growth',
          'title': 'Growth Trend Analysis',
          'description': 'Add growth measurements to unlock AI insights',
          'confidence': 0.1,
          'data': {
            'analysisType': 'growth',
            'category': 'Growth Analytics',
            'subtitle': 'Growth milestones & trends',
            'weightTrend': 'No data yet',
            'heightTrend': 'No data yet',
            'recommendations': ['Add your first growth measurement!'],
          }
        },
        'lastUpdated': now.toString(),
      };
    }
  }

  String _getAnalysisSystemPrompt() {
    return '''You are an expert pediatric AI assistant specializing in comprehensive baby development analysis. Analyze the provided data and generate detailed insights in the following JSON format.

Important: Always include comprehensive chartData arrays with 7 data points (Mon-Sun) for visualization, trend analysis scores (1-10), and actionable recommendations.

CRITICAL: Each category (sleep, feeding, growth, milestone) must have UNIQUE and SPECIFIC insights. Do NOT reuse generic content across categories. Tailor all analysis specifically to that category's domain.

{
  "sleep": {
    "title": "Sleep Pattern Analysis",
    "description": "Comprehensive analysis of sleep patterns and quality",
    "data": {
      "score": 8.5,
      "trend": "improving",
      "qualityScore": 9,
      "qualityTrend": "stable",
      "subtitle": "Brief summary of current sleep status",
      "averageDuration": "8.2 hours",
      "quality": "Excellent",
      "pattern": "Detailed pattern description",
      "recommendations": ["Specific actionable recommendation 1", "Specific actionable recommendation 2"],
      "chartData": [{"label": "Mon", "hours": 8.0}, {"label": "Tue", "hours": 7.5}, {"label": "Wed", "hours": 8.5}, {"label": "Thu", "hours": 8.0}, {"label": "Fri", "hours": 8.2}, {"label": "Sat", "hours": 8.1}, {"label": "Sun", "hours": 8.3}],
      "insights": ["Key insight about sleep patterns", "Important observation"]
    }
  },
  "feeding": {
    "title": "Feeding Schedule Analysis",
    "description": "Comprehensive analysis of feeding patterns and nutrition",
    "data": {
      "score": 7.8,
      "trend": "stable",
      "qualityScore": 8,
      "qualityTrend": "improving",
      "subtitle": "Brief summary of current feeding status",
      "frequency": "7 times/day",
      "amount": "130ml per feed",
      "pattern": "Detailed feeding pattern description",
      "recommendations": ["Specific feeding recommendation 1", "Specific feeding recommendation 2"],
      "chartData": [{"label": "Mon", "feeds": 7}, {"label": "Tue", "feeds": 7}, {"label": "Wed", "feeds": 6}, {"label": "Thu", "feeds": 7}, {"label": "Fri", "feeds": 8}, {"label": "Sat", "feeds": 7}, {"label": "Sun", "feeds": 7}],
      "insights": ["Key insight about feeding patterns", "Important nutrition observation"]
    }
  },
  "growth": {
    "title": "Growth Trend Analysis",
    "description": "Comprehensive analysis of growth patterns and development",
    "data": {
      "score": 9.1,
      "trend": "improving",
      "qualityScore": 9,
      "qualityTrend": "stable",
      "subtitle": "Brief summary of current growth status",
      "weightTrend": "Steady increase",
      "heightTrend": "Consistent growth",
      "recommendations": ["Specific growth recommendation 1", "Specific growth recommendation 2"],
      "chartData": [{"label": "Mon", "value": 6.5}, {"label": "Tue", "value": 6.6}, {"label": "Wed", "value": 6.7}, {"label": "Thu", "value": 6.7}, {"label": "Fri", "value": 6.8}, {"label": "Sat", "value": 6.9}, {"label": "Sun", "value": 7.0}],
      "insights": ["Key insight about growth patterns", "Important development observation"]
    }
  },
  "milestone": {
    "title": "Milestone Predictions",
    "description": "AI-powered predictions for upcoming developmental milestones",
    "data": {
      "score": 8.3,
      "trend": "on-track",
      "subtitle": "Developmental progress assessment",
      "nextMilestone": "First steps",
      "expectedTimeframe": "2-4 weeks",
      "currentProgress": "85%",
      "recommendations": ["Encourage standing with support", "Provide safe exploration space"],
      "milestones": [{"name": "Sitting without support", "status": "achieved", "ageAchieved": "6 months"}, {"name": "Crawling", "status": "achieved", "ageAchieved": "8 months"}, {"name": "First steps", "status": "upcoming", "expectedAge": "12-14 months"}],
      "insights": ["Milestone insight 1", "Development observation"]
    }
  }
}

Always provide:
- Realistic scores (1-10 scale)
- Meaningful trend analysis
- Specific, actionable recommendations
- Complete chartData arrays (7 points each)
- Evidence-based insights
- Age-appropriate milestone predictions''';
  }

  String _createAnalysisPrompt({
    required BabyProfile babyProfile,
    required List<ActivityLog> sleepLogs,
    required List<ActivityLog> feedingLogs,
    required List<ActivityLog> growthLogs,
    List<ActivityLog>? milestoneLogs,
  }) {
    final babyAge = '${babyProfile.ageInMonths} months (${babyProfile.ageInWeeks} weeks)';
    
    String prompt = '''Analyze the following baby data and provide insights:

Baby Information:
- Name: ${babyProfile.name}
- Age: $babyAge
- Gender: ${babyProfile.gender}
- Birth Weight: ${babyProfile.birthWeight ?? 'Not recorded'} kg
- Birth Height: ${babyProfile.birthHeight ?? 'Not recorded'} cm

Recent Activities:''';

    // Add sleep data
    if (sleepLogs.isNotEmpty) {
      prompt += '\n\nSleep Patterns (last ${sleepLogs.length} records):';
      for (final log in sleepLogs.take(5)) {
        prompt += '\n- ${log.timestamp.toString()}: Duration: ${log.duration?.inMinutes ?? 0} minutes';
      }
    }

    // Add feeding data
    if (feedingLogs.isNotEmpty) {
      prompt += '\n\nFeeding Patterns (last ${feedingLogs.length} records):';
      for (final log in feedingLogs.take(5)) {
        prompt += '\n- ${log.timestamp.toString()}: ${log.data['quantity'] ?? 'Unknown'} ${log.data['unit'] ?? ''}';
      }
    }

    // Add growth data
    if (growthLogs.isNotEmpty) {
      prompt += '\n\nGrowth Records (last ${growthLogs.length} records):';
      for (final log in growthLogs.take(5)) {
        prompt += '\n- ${log.timestamp.toString()}: Weight: ${log.data['weight'] ?? 'Unknown'} kg, Height: ${log.data['height'] ?? 'Unknown'} cm';
      }
    }

    // Add milestone data
    if (milestoneLogs != null && milestoneLogs.isNotEmpty) {
      prompt += '\n\nMilestone Achievements (last ${milestoneLogs.length} records):';
      for (final log in milestoneLogs.take(10)) {
        final details = log.details ?? {};
        final title = details['milestone_title'] ?? 'Unknown milestone';
        final description = details['milestone_description'] ?? '';
        final category = details['milestone_category'] ?? '';
        final ageInMonths = details['age_in_months'] ?? '';
        prompt += '\n- ${log.timestamp.toString()}: $title ($category) - $description (achieved at $ageInMonths months)';
      }
    }

    prompt += '\n\nProvide comprehensive insights about sleep patterns, feeding schedule, growth trends, and developmental milestones. Format the response as specified JSON.';
    
    return prompt;
  }

  double _calculateConfidence(List<dynamic> dataPoints, String type) {
    if (dataPoints.isEmpty) {
      debugPrint('📉 No data points for $type analysis, confidence: 0.1');
      return 0.1;
    }
    
    // Base confidence calculation on data points
    int requiredPoints;
    switch (type) {
      case 'sleep':
        requiredPoints = 10; // Need about 10 sleep records for good analysis
        break;
      case 'feeding':
        requiredPoints = 15; // Need about 15 feeding records
        break;
      case 'growth':
        requiredPoints = 3; // Need at least 3 growth measurements
        break;
      case 'milestone':
        requiredPoints = 5; // Need about 5 milestone records
        break;
      default:
        requiredPoints = 10;
    }

    // Calculate confidence based on data points vs required points
    double confidence = dataPoints.length / requiredPoints;
    
    // Normalize confidence between 0.1 and 0.95
    confidence = confidence.clamp(0.1, 0.95);
    
    // Add some randomness to avoid static values
    final random = Random();
    final randomFactor = random.nextDouble() * 0.1 - 0.05; // ±5%
    confidence = (confidence + randomFactor).clamp(0.1, 0.95);
    
    final roundedConfidence = double.parse(confidence.toStringAsFixed(2));
    debugPrint('📊 $type analysis confidence: $roundedConfidence (${dataPoints.length} records)');
    
    return roundedConfidence;
  }

  Map<String, dynamic> _generateMockSleepAnalysis(Map<String, dynamic> data, double confidence) {
    return {
      'type': 'sleep',
      'title': 'Sleep Pattern Analysis',
      'description': 'Analysis of sleep patterns and quality',
      'confidence': confidence,
      'data': {
        'averageDuration': '7.5 hours',
        'quality': 'Good',
        'pattern': 'Regular',
      },
    };
  }

  Map<String, dynamic> _generateMockFeedingAnalysis(Map<String, dynamic> data, double confidence) {
    return {
      'type': 'feeding',
      'title': 'Feeding Schedule Analysis',
      'description': 'Analysis of feeding patterns and intake',
      'confidence': confidence,
      'data': {
        'frequency': '6-8 times/day',
        'amount': '120-150ml per feed',
        'pattern': 'Regular',
      },
    };
  }

  Map<String, dynamic> _generateMockGrowthAnalysis(Map<String, dynamic> data, double confidence) {
    return {
      'type': 'growth',
      'title': 'Growth Trend Analysis',
      'description': 'Analysis of growth patterns and trends',
      'confidence': confidence,
      'data': {
        'weightPercentile': '75th',
        'heightPercentile': '80th',
        'trend': 'Steady growth',
      },
    };
  }

  Map<String, dynamic> _generateMockMilestoneAnalysis(Map<String, dynamic> data, double confidence) {
    return {
      'type': 'milestone',
      'title': 'Milestone Predictions',
      'description': 'Predictions for upcoming developmental milestones',
      'confidence': confidence,
      'data': {
        'nextMilestone': 'First steps',
        'expectedAge': '12-14 months',
        'currentProgress': 'On track',
      },
    };
  }

  Map<String, dynamic> _getEmptyInsightsTemplate() {
    return {
      'sleepAnalysis': {
        'type': 'sleep',
        'title': 'Sleep Pattern Analysis',
        'description': 'Not enough data for analysis',
        'confidence': 0.1,
        'data': {
          'analysisType': 'sleep',
          'category': 'Sleep Analytics',
          'subtitle': 'Sleep patterns & quality analysis',
          'averageDuration': 'Unknown',
          'quality': 'Unknown',
          'pattern': 'Unknown',
          'recommendations': ['Collect more sleep data for analysis']
        }
      },
      'feedingAnalysis': {
        'type': 'feeding',
        'title': 'Feeding Schedule Analysis',
        'description': 'Not enough data for analysis',
        'confidence': 0.1,
        'data': {
          'analysisType': 'feeding',
          'category': 'Feeding Analytics',
          'subtitle': 'Feeding schedule & intake tracking',
          'frequency': 'Unknown',
          'amount': 'Unknown',
          'pattern': 'Unknown',
          'recommendations': ['Collect more feeding data for analysis']
        }
      },
      'growthAnalysis': {
        'type': 'growth',
        'title': 'Growth Trend Analysis',
        'description': 'Not enough data for analysis',
        'confidence': 0.1,
        'data': {
          'analysisType': 'growth',
          'category': 'Growth Analytics',
          'subtitle': 'Growth milestones & trends',
          'weightTrend': 'Unknown',
          'heightTrend': 'Unknown',
          'recommendations': ['Add growth measurements for analysis']
        }
      },
      'lastUpdated': DateTime.now().toString(),
    };
  }

  // Methods called from ai_insights_screen.dart for backwards compatibility
  Future<List<AIInsight>> analyzeActivityPatterns({
    required BabyProfile babyProfile,
    required List<ActivityLog> activities,
  }) async {
    try {
      final insights = await generateComprehensiveInsights(
        babyId: babyProfile.id,
        babyProfile: babyProfile,
      );
      
      return [
        AIInsight(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          babyId: babyProfile.id,
          title: 'Activity Pattern Analysis',
          description: 'Comprehensive analysis of baby\'s daily activities',
          recommendations: ['Continue monitoring activity patterns', 'Look for consistent schedules'],
          confidence: 0.85,
          type: InsightType.behavior,
          priority: InsightPriority.medium,
          generatedAt: DateTime.now(),
          isRead: false,
          isArchived: false,
        ),
      ];
    } catch (e) {
      debugPrint('Error in analyzeActivityPatterns: $e');
      return [];
    }
  }

  Future<AIInsight?> analyzeSleepPatterns({
    required BabyProfile babyProfile,
    required List<ActivityLog> sleepLogs,
  }) async {
    try {
      final insights = await generateComprehensiveInsights(
        babyId: babyProfile.id,
        babyProfile: babyProfile,
      );
      
      final sleepAnalysis = insights['sleepAnalysis'] as Map<String, dynamic>;
      
      return AIInsight(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        babyId: babyProfile.id,
        title: sleepAnalysis['title'] ?? 'Sleep Pattern Analysis',
        description: sleepAnalysis['subtitle'] ?? 'Analysis of sleep patterns',
        recommendations: List<String>.from(sleepAnalysis['recommendations'] ?? []),
        confidence: (sleepAnalysis['confidence'] ?? 0.0) / 10.0,
        type: InsightType.sleep,
        priority: InsightPriority.medium,
        generatedAt: DateTime.now(),
        isRead: false,
        isArchived: false,
      );
    } catch (e) {
      debugPrint('Error in analyzeSleepPatterns: $e');
      return null;
    }
  }

  Future<AIInsight?> analyzeFeedingPatterns({
    required BabyProfile babyProfile,
    required List<ActivityLog> feedingLogs,
  }) async {
    try {
      final insights = await generateComprehensiveInsights(
        babyId: babyProfile.id,
        babyProfile: babyProfile,
      );
      
      final feedingAnalysis = insights['feedingAnalysis'] as Map<String, dynamic>;
      
      return AIInsight(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        babyId: babyProfile.id,
        title: feedingAnalysis['title'] ?? 'Feeding Schedule Analysis',
        description: feedingAnalysis['subtitle'] ?? 'Analysis of feeding patterns',
        recommendations: List<String>.from(feedingAnalysis['recommendations'] ?? []),
        confidence: (feedingAnalysis['confidence'] ?? 0.0) / 100.0,
        type: InsightType.feeding,
        priority: InsightPriority.medium,
        generatedAt: DateTime.now(),
        isRead: false,
        isArchived: false,
      );
    } catch (e) {
      debugPrint('Error in analyzeFeedingPatterns: $e');
      return null;
    }
  }

  Future<AIInsight?> analyzeGrowthTrends({
    required BabyProfile babyProfile,
    required List<ActivityLog> growthLogs,
  }) async {
    try {
      final insights = await generateComprehensiveInsights(
        babyId: babyProfile.id,
        babyProfile: babyProfile,
      );
      
      final growthAnalysis = insights['growthAnalysis'] as Map<String, dynamic>;
      
      return AIInsight(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        babyId: babyProfile.id,
        title: growthAnalysis['title'] ?? 'Growth Trend Analysis',
        description: growthAnalysis['subtitle'] ?? 'Analysis of growth patterns',
        recommendations: List<String>.from(growthAnalysis['recommendations'] ?? []),
        confidence: (growthAnalysis['confidence'] ?? 0.0) / 100.0,
        type: InsightType.growth,
        priority: InsightPriority.medium,
        generatedAt: DateTime.now(),
        isRead: false,
        isArchived: false,
      );
    } catch (e) {
      debugPrint('Error in analyzeGrowthTrends: $e');
      return null;
    }
  }

  // Gather all relevant baby data for analysis
  Future<Map<String, dynamic>> _gatherBabyData(String babyId, BabyProfile babyProfile) async {
    try {
      debugPrint('📈 Starting comprehensive data gathering for baby: $babyId');
      
      // Get recent activities from the database
      final activities = await _supabaseService.select(
        'activity_logs',
        filters: {'baby_id': babyId},
        orderBy: 'recorded_at',
        ascending: false,
        limit: 100, // Get last 100 activities for better analysis
      );

      debugPrint('📊 Raw activities fetched: ${activities.length}');
      
      // Parse activities into typed lists
      final sleepLogs = <ActivityLog>[];
      final feedingLogs = <ActivityLog>[];
      final growthLogs = <ActivityLog>[];
      final diaperLogs = <ActivityLog>[];
      final healthLogs = <ActivityLog>[];
      final milestoneLogs = <ActivityLog>[];

      for (final activity in activities) {
        try {
          final log = ActivityLog.fromJson(activity);
          switch (log.type) {
            case ActivityType.sleep:
              sleepLogs.add(log);
              break;
            case ActivityType.feeding:
              feedingLogs.add(log);
              break;
            case ActivityType.growth:
              growthLogs.add(log);
              break;
            case ActivityType.diaper:
              diaperLogs.add(log);
              break;
            case ActivityType.doctor:
            case ActivityType.temperature:
              healthLogs.add(log);
              break;
            case ActivityType.milestone:
              milestoneLogs.add(log);
              break;
            default:
              debugPrint('🔄 Unhandled activity type: ${log.type}');
              break;
          }
        } catch (e) {
          debugPrint('⚠️ Error parsing activity: $e');
        }
      }

      debugPrint('✅ Gathered comprehensive activity data:');
      debugPrint('   😴 Sleep logs: ${sleepLogs.length}');
      debugPrint('   🍼 Feeding logs: ${feedingLogs.length}');
      debugPrint('   📈 Growth logs: ${growthLogs.length}');
      debugPrint('   👶 Diaper logs: ${diaperLogs.length}');
      debugPrint('   🏥 Health logs: ${healthLogs.length}');
      debugPrint('   🏆 Milestone logs: ${milestoneLogs.length}');
      
      // Log recent activity details for feeding to help debug
      if (feedingLogs.isNotEmpty) {
        debugPrint('🍼 Recent feeding data details:');
        for (int i = 0; i < feedingLogs.take(5).length; i++) {
          final log = feedingLogs[i];
          debugPrint('   ${i+1}. ${log.timestamp}: ${log.data['quantity'] ?? 'Unknown'} ${log.data['unit'] ?? ''} (${log.data['feeding_type'] ?? 'Unknown type'})');
        }
      }
      
      // Calculate data quality indicators
      final now = DateTime.now();
      final last7Days = now.subtract(Duration(days: 7));
      final recentFeedings = feedingLogs.where((log) => log.timestamp.isAfter(last7Days)).length;
      final recentSleep = sleepLogs.where((log) => log.timestamp.isAfter(last7Days)).length;
      
      debugPrint('📅 Data quality in last 7 days:');
      debugPrint('   🍼 Recent feedings: $recentFeedings');
      debugPrint('   😴 Recent sleep: $recentSleep');
      
      return {
        'babyProfile': babyProfile,
        'sleep': sleepLogs,
        'feeding': feedingLogs,
        'growth': growthLogs,
        'diaper': diaperLogs,
        'health': healthLogs,
        'milestone': milestoneLogs,
        'dataQuality': {
          'totalActivities': activities.length,
          'recentFeedings': recentFeedings,
          'recentSleep': recentSleep,
          'hasGrowthData': growthLogs.isNotEmpty,
          'hasDiaperData': diaperLogs.isNotEmpty,
        }
      };
    } catch (e) {
      debugPrint('❌ Error gathering baby data: $e');
      return {
        'babyProfile': babyProfile,
        'sleep': <ActivityLog>[],
        'feeding': <ActivityLog>[],
        'growth': <ActivityLog>[],
        'diaper': <ActivityLog>[],
        'health': <ActivityLog>[],
        'milestone': <ActivityLog>[],
        'dataQuality': {
          'totalActivities': 0,
          'recentFeedings': 0,
          'recentSleep': 0,
          'hasGrowthData': false,
          'hasDiaperData': false,
        }
      };
    }
  }

  Future<void> clearCachedInsights(String babyId) async {
    try {
      debugPrint('🗑️ Clearing cached insights for baby: $babyId');
      await _supabaseService.delete('ai_insights', 'baby_id', babyId);
      debugPrint('✅ Cleared cached insights');
    } catch (e) {
      debugPrint('❌ Error clearing cached insights: $e');
    }
  }

  /// Generate an overall summary for the home screen preview
  Map<String, dynamic> _generateOverallSummary(Map<String, dynamic> insights) {
    try {
      debugPrint('📝 Generating overall summary from insights');
      
      // Safely convert to Map<String, dynamic> to handle database JSON parsing
      final sleepAnalysis = _safelyConvertMap(insights['sleepAnalysis']);
      final feedingAnalysis = _safelyConvertMap(insights['feedingAnalysis']);
      final growthAnalysis = _safelyConvertMap(insights['growthAnalysis']);
      
      final List<String> analysisItems = [];
      final List<String> recommendationItems = [];
      int totalInsights = 0;
      
      // Process sleep insights
      if (sleepAnalysis != null) {
        final confidence = (sleepAnalysis['confidence'] as num?) ?? 0;
        if (confidence > 0.2) {
          analysisItems.add('Sleep patterns analyzed');
          final sleepData = sleepAnalysis['data'] as Map<String, dynamic>? ?? {};
          final recommendations = sleepData['recommendations'] as List? ?? [];
          if (recommendations.isNotEmpty && !recommendations.first.toString().toLowerCase().contains('not enough')) {
            recommendationItems.addAll(recommendations.cast<String>().take(2));
          }
          totalInsights++;
        }
      }
      
      // Process feeding insights
      if (feedingAnalysis != null) {
        final confidence = (feedingAnalysis['confidence'] as num?) ?? 0;
        if (confidence > 0.2) {
          analysisItems.add('Feeding schedule reviewed');
          final feedingData = feedingAnalysis['data'] as Map<String, dynamic>? ?? {};
          final recommendations = feedingData['recommendations'] as List? ?? [];
          if (recommendations.isNotEmpty && !recommendations.first.toString().toLowerCase().contains('not enough')) {
            recommendationItems.addAll(recommendations.cast<String>().take(2));
          }
          totalInsights++;
        }
      }
      
      // Process growth insights
      if (growthAnalysis != null) {
        final confidence = (growthAnalysis['confidence'] as num?) ?? 0;
        if (confidence > 0.2) {
          analysisItems.add('Growth trends tracked');
          final growthData = growthAnalysis['data'] as Map<String, dynamic>? ?? {};
          final recommendations = growthData['recommendations'] as List? ?? [];
          if (recommendations.isNotEmpty && !recommendations.first.toString().toLowerCase().contains('not enough')) {
            recommendationItems.addAll(recommendations.cast<String>().take(2));
          }
          totalInsights++;
        }
      }
      
      // Generate main summary text
      String mainSummary;
      if (analysisItems.isEmpty) {
        mainSummary = 'AI is ready to analyze your baby\'s patterns. Start logging activities to unlock AI-powered insights and personalized recommendations.';
      } else {
        final analysisText = analysisItems.join(', ');
        final recCount = recommendationItems.length;
        if (recCount > 0) {
          mainSummary = '$analysisText. $recCount personalized recommendations available.';
        } else {
          mainSummary = '$analysisText. Continue logging for more detailed insights.';
        }
      }
      
      // Generate detailed description based on available data
      String detailedDescription;
      if (totalInsights == 0) {
        detailedDescription = '';
      } else if (totalInsights == 1) {
        detailedDescription = 'Based on your logs analysis, we have initial insights available. Continue logging activities to unlock more comprehensive analysis and recommendations.';
      } else {
        // Generate more specific description based on actual analysis
        final descriptions = <String>[];
        
        if (sleepAnalysis != null && ((sleepAnalysis['confidence'] as num?) ?? 0) > 0.2) {
          final sleepData = sleepAnalysis['data'] as Map<String, dynamic>? ?? {};
          final avgDuration = sleepData['averageDuration']?.toString() ?? 'tracking in progress';
          if (!avgDuration.toLowerCase().contains('unknown')) {
            descriptions.add('sleep averaging $avgDuration');
          } else {
            descriptions.add('sleep patterns being tracked');
          }
        }
        
        if (feedingAnalysis != null && ((feedingAnalysis['confidence'] as num?) ?? 0) > 0.2) {
          final feedingData = feedingAnalysis['data'] as Map<String, dynamic>? ?? {};
          final frequency = feedingData['frequency']?.toString() ?? 'tracking in progress';
          if (!frequency.toLowerCase().contains('unknown')) {
            descriptions.add('feeding $frequency');
          } else {
            descriptions.add('feeding schedule being optimized');
          }
        }
        
        if (growthAnalysis != null && ((growthAnalysis['confidence'] as num?) ?? 0) > 0.2) {
          final growthData = growthAnalysis['data'] as Map<String, dynamic>? ?? {};
          final trend = growthData['weightTrend']?.toString() ?? 'tracking in progress';
          if (!trend.toLowerCase().contains('unknown')) {
            descriptions.add('growth showing $trend');
          } else {
            descriptions.add('growth being monitored');
          }
        }
        
        if (descriptions.isNotEmpty) {
          detailedDescription = 'Based on your logs analysis, your baby\'s ${descriptions.join(', ')}.';
        } else {
          detailedDescription = 'Based on your logs analysis, we\'re tracking your baby\'s patterns and will provide insights as more data becomes available.';
        }
      }
      
      final summary = {
        'mainSummary': mainSummary,
        'detailedDescription': detailedDescription,
        'totalInsights': totalInsights,
        'availableRecommendations': recommendationItems.length,
        'topRecommendations': recommendationItems.take(3).toList(),
        'hasData': totalInsights > 0,
      };
      
      debugPrint('✨ Generated overall summary: $summary');
      return summary;
    } catch (e) {
      debugPrint('❌ Error generating overall summary: $e');
      return {
        'mainSummary': 'AI insights are being prepared for your baby\'s care patterns.',
        'detailedDescription': 'Continue logging activities to unlock personalized insights and recommendations.',
        'totalInsights': 0,
        'availableRecommendations': 0,
        'topRecommendations': <String>[],
        'hasData': false,
      };
    }
  }

  /// Safely convert Map<dynamic, dynamic> to Map<String, dynamic>
  /// This handles the case where JSON from database comes back as _Map<dynamic, dynamic>
  Map<String, dynamic>? _safelyConvertMap(dynamic input) {
    if (input == null) return null;
    
    try {
      // If it's already the right type, return it
      if (input is Map<String, dynamic>) {
        return input;
      }
      
      // If it's a Map but with dynamic keys, convert it
      if (input is Map) {
        final result = <String, dynamic>{};
        input.forEach((key, value) {
          result[key.toString()] = value;
        });
        return result;
      }
      
      // If it's not a Map, return null
      return null;
    } catch (e) {
      debugPrint('❌ Error converting map: $e');
      return null;
    }
  }
}
