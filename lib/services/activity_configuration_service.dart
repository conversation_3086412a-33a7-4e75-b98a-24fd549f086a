import '../utils/activity_type_config.dart';

/// Centralized service for activity configurations
class ActivityConfigurationService {
  static const List<String> _quickLogActivityTypes = [
    'feeding',
    'sleep', 
    'diaper',
    'medicine',
    'vaccination',
    'temperature',
    'potty',
    'tummy_time',
    'story_time',
    'screen_time',
    'skin_to_skin',
    'outdoor_play',
    'indoor_play',
    'brush_teeth',
    'pumping',
    'growth',
    'milestone',
    'doctor_appointment',
  ];

  static const List<String> _primaryQuickActions = [
    'feeding',
    'sleep',
    'diaper',
  ];

  /// Get all quick log activity types
  static List<String> getQuickLogActivityTypes() => _quickLogActivityTypes;

  /// Get primary quick action types
  static List<String> getPrimaryQuickActionTypes() => _primaryQuickActions;

  /// Get activity configuration for UI display
  static Map<String, dynamic> getActivityUIConfig(String type, context) {
    return {
      'label': ActivityTypeConfig.getLabel(type),
      'icon': ActivityTypeConfig.getIcon(type),
      'color': ActivityTypeConfig.getColor(type, context),
      'type': type,
      'description': ActivityTypeConfig.getDescription(type),
    };
  }

  /// Get all quick log activities as UI configs
  static List<Map<String, dynamic>> getAllQuickLogConfigs(context) {
    return _quickLogActivityTypes
        .map((type) => getActivityUIConfig(type, context))
        .toList();
  }
}