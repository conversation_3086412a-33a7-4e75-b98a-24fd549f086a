import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'unit_conversion_service.dart';

/// Centralized service for managing measurement units throughout the app
class MeasurementUnitsService extends ChangeNotifier {
  static const String _measurementUnitsKey = 'measurement_units_preference';
  
  static MeasurementUnitsService? _instance;
  static MeasurementUnitsService get instance => _instance ??= MeasurementUnitsService._();
  
  MeasurementUnitsService._();

  SharedPreferences? _prefs;
  bool _isMetric = true;
  bool _isInitialized = false;

  /// Initialize the service
  Future<void> init() async {
    if (_isInitialized) return;
    
    _prefs = await SharedPreferences.getInstance();
    _isMetric = _prefs?.getBool(_measurementUnitsKey) ?? true;
    _isInitialized = true;
    
    debugPrint('MeasurementUnitsService initialized: isMetric = $_isMetric');
  }

  /// Get current measurement system preference
  bool get isMetric {
    if (!_isInitialized) {
      debugPrint('Warning: MeasurementUnitsService not initialized, returning default value');
      return true;
    }
    return _isMetric;
  }

  /// Get current measurement system as string
  String get measurementSystem => _isMetric ? 'Metric' : 'Imperial';

  /// Get measurement system description
  String get measurementSystemDescription => _isMetric 
    ? 'Metric (kg, cm, °C)' 
    : 'Imperial (lbs, in, °F)';

  /// Set measurement system preference
  Future<void> setMeasurementSystem(bool isMetric) async {
    if (!_isInitialized) {
      await init();
    }
    
    if (_isMetric != isMetric) {
      _isMetric = isMetric;
      await _prefs?.setBool(_measurementUnitsKey, isMetric);
      
      debugPrint('Measurement system changed to: ${isMetric ? "Metric" : "Imperial"}');
      notifyListeners();
    }
  }

  /// Toggle between metric and imperial
  Future<void> toggleMeasurementSystem() async {
    await setMeasurementSystem(!_isMetric);
  }

  // Weight unit helpers
  String get weightUnit => _isMetric ? 'kg' : 'lbs';
  String get weightUnitShort => _isMetric ? 'kg' : 'lb';
  List<String> get weightUnits => _isMetric ? ['kg', 'g'] : ['lbs', 'oz'];

  // Length/Height unit helpers
  String get lengthUnit => _isMetric ? 'cm' : 'in';
  String get lengthUnitShort => _isMetric ? 'cm' : 'in';
  List<String> get lengthUnits => _isMetric ? ['cm', 'm'] : ['in', 'ft'];

  // Temperature unit helpers
  String get temperatureUnit => _isMetric ? '°C' : '°F';

  // Volume unit helpers (for feeding)
  String get volumeUnit => _isMetric ? 'ml' : 'fl oz';
  List<String> get volumeUnits => _isMetric ? ['ml', 'l'] : ['fl oz', 'cups'];

  /// Convert weight value to display format
  String formatWeight(double value, {int decimals = 1}) {
    if (_isMetric) {
      return '${value.toStringAsFixed(decimals)} kg';
    } else {
      final lbs = UnitConversionService.convertWeightToImperial(value);
      return '${lbs.toStringAsFixed(decimals)} lbs';
    }
  }

  /// Convert length value to display format
  String formatLength(double value, {int decimals = 1}) {
    if (_isMetric) {
      return '${value.toStringAsFixed(decimals)} cm';
    } else {
      final inches = UnitConversionService.convertLengthToImperial(value);
      return '${inches.toStringAsFixed(decimals)} in';
    }
  }

  /// Convert temperature value to display format
  String formatTemperature(double value, {int decimals = 1}) {
    if (_isMetric) {
      return '${value.toStringAsFixed(decimals)}°C';
    } else {
      final fahrenheit = (value * 9/5) + 32;
      return '${fahrenheit.toStringAsFixed(decimals)}°F';
    }
  }

  /// Convert volume value to display format
  String formatVolume(double value, {int decimals = 0}) {
    if (_isMetric) {
      return '${value.toStringAsFixed(decimals)} ml';
    } else {
      final flOz = value * 0.033814; // ml to fl oz
      return '${flOz.toStringAsFixed(decimals)} fl oz';
    }
  }

  /// Get weight validation range based on current units
  Map<String, double> getWeightValidationRange() {
    if (_isMetric) {
      return {'min': 0.5, 'max': 50.0}; // kg
    } else {
      return {'min': 1.0, 'max': 110.0}; // lbs
    }
  }

  /// Get length validation range based on current units
  Map<String, double> getLengthValidationRange() {
    if (_isMetric) {
      return {'min': 30.0, 'max': 200.0}; // cm
    } else {
      return {'min': 12.0, 'max': 78.0}; // inches
    }
  }

  /// Get head circumference validation range based on current units
  Map<String, double> getHeadCircumferenceValidationRange() {
    if (_isMetric) {
      return {'min': 25.0, 'max': 70.0}; // cm
    } else {
      return {'min': 10.0, 'max': 28.0}; // inches
    }
  }

  /// Convert input value to metric for storage
  double convertToMetricForStorage(double value, String unit, String measurementType) {
    if (_isMetric) return value;

    switch (measurementType.toLowerCase()) {
      case 'weight':
        if (unit == 'lbs') {
          return UnitConversionService.convertWeightToMetric(value);
        } else if (unit == 'oz') {
          return UnitConversionService.convertWeightOzToMetric(value);
        }
        break;
      case 'height':
      case 'length':
      case 'head_circumference':
        if (unit == 'in') {
          return UnitConversionService.convertLengthToMetric(value);
        } else if (unit == 'ft') {
          return UnitConversionService.convertLengthToMetric(value * 12); // ft to inches first
        }
        break;
    }
    return value;
  }

  /// Convert metric value from storage to display units
  double convertFromMetricForDisplay(double metricValue, String measurementType) {
    if (_isMetric) return metricValue;

    switch (measurementType.toLowerCase()) {
      case 'weight':
        return UnitConversionService.convertWeightToImperial(metricValue);
      case 'height':
      case 'length':
      case 'head_circumference':
        return UnitConversionService.convertLengthToImperial(metricValue);
    }
    return metricValue;
  }

  /// Get appropriate unit for measurement type
  String getUnitForMeasurementType(String measurementType) {
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return weightUnit;
      case 'height':
      case 'length':
      case 'head_circumference':
        return lengthUnit;
      case 'temperature':
        return temperatureUnit;
      case 'volume':
        return volumeUnit;
      default:
        return '';
    }
  }

  /// Get validation message for measurement type
  String getValidationMessage(String measurementType) {
    final range = measurementType.toLowerCase() == 'weight' 
        ? getWeightValidationRange()
        : measurementType.toLowerCase() == 'head_circumference'
            ? getHeadCircumferenceValidationRange()
            : getLengthValidationRange();
    
    final unit = getUnitForMeasurementType(measurementType);
    final typeName = measurementType.replaceAll('_', ' ').toLowerCase();
    
    return '${typeName.substring(0, 1).toUpperCase()}${typeName.substring(1)} should be between ${range['min']} and ${range['max']} $unit';
  }

  /// Clear all preferences
  Future<void> clearPreferences() async {
    await _prefs?.remove(_measurementUnitsKey);
    _isMetric = true;
    notifyListeners();
  }
}