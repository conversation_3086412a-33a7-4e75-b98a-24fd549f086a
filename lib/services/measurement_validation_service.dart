import 'dart:math';
import '../models/measurement.dart';
import '../models/baby_profile.dart';
import 'who_data_service.dart';
import 'enhanced_percentile_calculator.dart';

/// Validation result for measurement data
class MeasurementValidationResult {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;
  final Map<String, dynamic> metadata;
  final bool requiresAttention;

  const MeasurementValidationResult({
    required this.isValid,
    required this.errors,
    required this.warnings,
    required this.metadata,
    required this.requiresAttention,
  });

  /// Check if validation has any issues
  bool get hasIssues => errors.isNotEmpty || warnings.isNotEmpty;

  /// Check if validation has warnings
  bool get hasWarnings => warnings.isNotEmpty;

  /// Get all issues combined
  List<String> get allIssues => [...errors, ...warnings];

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'isValid': isValid,
      'errors': errors,
      'warnings': warnings,
      'metadata': metadata,
      'requiresAttention': requiresAttention,
      'hasWarnings': hasWarnings,
    };
  }
}

/// Comprehensive measurement validation service
class MeasurementValidationService {
  
  /// Validate measurement against age and gender norms
  static MeasurementValidationResult validateMeasurement(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
    BabyProfile babyProfile, {
    DateTime? measurementDate,
    List<Measurement>? previousMeasurements,
  }) {
    final errors = <String>[];
    final warnings = <String>[];
    final metadata = <String, dynamic>{};
    
    // Basic parameter validation
    if (value <= 0) {
      errors.add('Measurement value must be positive');
    }
    
    if (ageInMonths < 0 || ageInMonths > 60) {
      errors.add('Age must be between 0 and 60 months');
    }
    
    if (!['weight', 'height', 'length', 'head_circumference'].contains(measurementType)) {
      errors.add('Invalid measurement type');
    }
    
    if (!['boys', 'girls', 'male', 'female'].contains(gender.toLowerCase())) {
      errors.add('Invalid gender specification');
    }

    // Date validation
    if (measurementDate != null) {
      final now = DateTime.now();
      if (measurementDate.isAfter(now)) {
        errors.add('Measurement date cannot be in the future');
      }
      
      if (measurementDate.isBefore(babyProfile.birthDate)) {
        errors.add('Measurement date cannot be before birth date');
      }
      
      // Check if date is too far from calculated age
      final expectedAge = measurementDate.difference(babyProfile.birthDate).inDays / 30.44;
      if ((expectedAge - ageInMonths).abs() > 1.0) {
        warnings.add('Age in months doesn\'t match measurement date');
        metadata['expectedAge'] = expectedAge;
        metadata['providedAge'] = ageInMonths;
      }
    }

    // Return early if basic validation fails
    if (errors.isNotEmpty) {
      return MeasurementValidationResult(
        isValid: false,
        errors: errors,
        warnings: warnings,
        metadata: metadata,
        requiresAttention: true,
      );
    }

    // Biological range validation
    final biologicalValidation = _validateBiologicalRanges(
      value, ageInMonths, measurementType, gender);
    errors.addAll(biologicalValidation['errors'] as List<String>);
    warnings.addAll(biologicalValidation['warnings'] as List<String>);
    metadata.addAll(biologicalValidation['metadata'] as Map<String, dynamic>);

    // WHO percentile validation
    final percentileValidation = _validateWHOPercentiles(
      value, ageInMonths, measurementType, gender);
    warnings.addAll(percentileValidation['warnings'] as List<String>);
    metadata.addAll(percentileValidation['metadata'] as Map<String, dynamic>);

    // Growth pattern validation (if previous measurements available)
    if (previousMeasurements != null && previousMeasurements.isNotEmpty) {
      final patternValidation = _validateGrowthPattern(
        value, ageInMonths, measurementType, previousMeasurements);
      warnings.addAll(patternValidation['warnings'] as List<String>);
      metadata.addAll(patternValidation['metadata'] as Map<String, dynamic>);
    }

    // Measurement consistency validation
    final consistencyValidation = _validateMeasurementConsistency(
      value, ageInMonths, measurementType, previousMeasurements ?? []);
    warnings.addAll(consistencyValidation['warnings'] as List<String>);
    metadata.addAll(consistencyValidation['metadata'] as Map<String, dynamic>);

    final requiresAttention = errors.isNotEmpty || 
        warnings.any((w) => w.contains('extreme') || w.contains('concerning'));

    return MeasurementValidationResult(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
      metadata: metadata,
      requiresAttention: requiresAttention,
    );
  }

  /// Validate biological ranges for measurements
  static Map<String, dynamic> _validateBiologicalRanges(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    final errors = <String>[];
    final warnings = <String>[];
    final metadata = <String, dynamic>{};

    switch (measurementType) {
      case 'weight':
        // Weight validation (kg)
        if (value < 0.5) {
          errors.add('Weight too low - minimum viable weight is 0.5kg');
        } else if (value < 1.0 && ageInMonths > 0) {
          warnings.add('Extremely low weight - please verify measurement');
        }
        
        if (value > 50.0) {
          errors.add('Weight too high - maximum expected weight is 50kg for this age range');
        } else if (value > 30.0 && ageInMonths < 60) {
          warnings.add('Unusually high weight - please verify measurement');
        }
        
        // Age-specific weight ranges
        final expectedMinWeight = _getExpectedMinWeight(ageInMonths);
        final expectedMaxWeight = _getExpectedMaxWeight(ageInMonths);
        
        if (value < expectedMinWeight) {
          warnings.add('Weight below expected range for age');
          metadata['expectedMinWeight'] = expectedMinWeight;
        }
        
        if (value > expectedMaxWeight) {
          warnings.add('Weight above expected range for age');
          metadata['expectedMaxWeight'] = expectedMaxWeight;
        }
        
        break;

      case 'height':
      case 'length':
        // Height/Length validation (cm)
        if (value < 25.0) {
          errors.add('Height/Length too low - minimum expected is 25cm');
        } else if (value < 35.0 && ageInMonths > 0) {
          warnings.add('Extremely low height/length - please verify measurement');
        }
        
        if (value > 150.0) {
          errors.add('Height/Length too high - maximum expected is 150cm for this age range');
        } else if (value > 120.0 && ageInMonths < 60) {
          warnings.add('Unusually high height/length - please verify measurement');
        }
        
        // Age-specific height ranges
        final expectedMinHeight = _getExpectedMinHeight(ageInMonths);
        final expectedMaxHeight = _getExpectedMaxHeight(ageInMonths);
        
        if (value < expectedMinHeight) {
          warnings.add('Height/Length below expected range for age');
          metadata['expectedMinHeight'] = expectedMinHeight;
        }
        
        if (value > expectedMaxHeight) {
          warnings.add('Height/Length above expected range for age');
          metadata['expectedMaxHeight'] = expectedMaxHeight;
        }
        
        break;

      case 'head_circumference':
        // Head circumference validation (cm)
        if (value < 25.0) {
          errors.add('Head circumference too low - minimum expected is 25cm');
        } else if (value < 30.0 && ageInMonths > 0) {
          warnings.add('Extremely low head circumference - please verify measurement');
        }
        
        if (value > 65.0) {
          errors.add('Head circumference too high - maximum expected is 65cm');
        } else if (value > 60.0 && ageInMonths < 60) {
          warnings.add('Unusually high head circumference - please verify measurement');
        }
        
        // Age-specific head circumference ranges
        final expectedMinHC = _getExpectedMinHeadCircumference(ageInMonths);
        final expectedMaxHC = _getExpectedMaxHeadCircumference(ageInMonths);
        
        if (value < expectedMinHC) {
          warnings.add('Head circumference below expected range for age');
          metadata['expectedMinHC'] = expectedMinHC;
        }
        
        if (value > expectedMaxHC) {
          warnings.add('Head circumference above expected range for age');
          metadata['expectedMaxHC'] = expectedMaxHC;
        }
        
        break;
    }

    return {
      'errors': errors,
      'warnings': warnings,
      'metadata': metadata,
    };
  }

  /// Validate WHO percentiles and flag concerning values
  static Map<String, dynamic> _validateWHOPercentiles(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    final warnings = <String>[];
    final metadata = <String, dynamic>{};

    try {
      final percentile = WHODataService.calculateExactPercentile(
        value, ageInMonths, measurementType, gender);
      
      metadata['percentile'] = percentile;
      
      if (percentile < 0.1) {
        warnings.add('Extremely low percentile (below 0.1st) - medical consultation recommended');
      } else if (percentile < 3.0) {
        warnings.add('Below 3rd percentile - monitor closely and consult pediatrician');
      } else if (percentile > 99.9) {
        warnings.add('Extremely high percentile (above 99.9th) - medical consultation recommended');
      } else if (percentile > 97.0) {
        warnings.add('Above 97th percentile - monitor for consistency');
      }
      
      // Calculate Z-score for additional validation
      final zScore = WHODataService.calculateZScore(
        value, ageInMonths, measurementType, gender);
      
      metadata['zScore'] = zScore;
      
      if (zScore.abs() > 3.0) {
        warnings.add('Z-score indicates extreme value (|Z| > 3) - verify measurement accuracy');
      } else if (zScore.abs() > 2.0) {
        warnings.add('Z-score indicates concerning value (|Z| > 2) - monitor closely');
      }
      
    } catch (e) {
      warnings.add('Unable to calculate WHO percentiles - check measurement parameters');
      metadata['percentileError'] = e.toString();
    }

    return {
      'warnings': warnings,
      'metadata': metadata,
    };
  }

  /// Validate growth pattern against previous measurements
  static Map<String, dynamic> _validateGrowthPattern(
    double value,
    double ageInMonths,
    String measurementType,
    List<Measurement> previousMeasurements,
  ) {
    final warnings = <String>[];
    final metadata = <String, dynamic>{};

    if (previousMeasurements.isEmpty) return {'warnings': warnings, 'metadata': metadata};

    // Filter measurements of the same type and sort by age
    final sameMeasurements = previousMeasurements
        .where((m) => m.measurementType == measurementType)
        .toList()
      ..sort((a, b) => a.ageInMonths.compareTo(b.ageInMonths));

    if (sameMeasurements.isEmpty) return {'warnings': warnings, 'metadata': metadata};

    final latestMeasurement = sameMeasurements.last;
    
    // Check for backward growth (should not happen)
    if (value < latestMeasurement.value && ageInMonths > latestMeasurement.ageInMonths) {
      if (measurementType == 'weight') {
        warnings.add('Weight loss detected - monitor nutrition and health status');
      } else {
        warnings.add('Measurement decrease detected - verify measurement accuracy');
      }
      metadata['previousValue'] = latestMeasurement.value;
      metadata['valueChange'] = value - latestMeasurement.value;
    }

    // Check growth velocity
    final timeDiff = ageInMonths - latestMeasurement.ageInMonths;
    if (timeDiff > 0) {
      final growthRate = (value - latestMeasurement.value) / timeDiff;
      metadata['growthRatePerMonth'] = growthRate;
      
      // Define concerning growth rates by measurement type
      double maxNormalGrowthRate;
      double minNormalGrowthRate;
      
      switch (measurementType) {
        case 'weight':
          maxNormalGrowthRate = ageInMonths < 6 ? 1.0 : 0.5; // kg per month
          minNormalGrowthRate = ageInMonths < 6 ? 0.3 : 0.1;
          break;
        case 'height':
        case 'length':
          maxNormalGrowthRate = ageInMonths < 12 ? 3.0 : 1.5; // cm per month
          minNormalGrowthRate = ageInMonths < 12 ? 1.0 : 0.5;
          break;
        case 'head_circumference':
          maxNormalGrowthRate = ageInMonths < 6 ? 2.0 : 0.5; // cm per month
          minNormalGrowthRate = ageInMonths < 6 ? 0.5 : 0.1;
          break;
        default:
          maxNormalGrowthRate = double.infinity;
          minNormalGrowthRate = 0.0;
      }
      
      if (growthRate > maxNormalGrowthRate) {
        warnings.add('Unusually rapid growth rate - verify measurement accuracy');
      } else if (growthRate < minNormalGrowthRate && measurementType != 'weight') {
        warnings.add('Slow growth rate detected - monitor development');
      }
    }

    // Check percentile crossing if percentiles are available
    if (latestMeasurement.percentile != null) {
      try {
        final currentPercentile = WHODataService.calculateExactPercentile(
          value, ageInMonths, measurementType, 
          sameMeasurements.first.babyId.contains('girl') ? 'girls' : 'boys' // Simplified gender detection
        );
        
        final percentileChange = currentPercentile - latestMeasurement.percentile!;
        metadata['percentileChange'] = percentileChange;
        
        if (percentileChange.abs() > 25.0) {
          warnings.add('Significant percentile crossing (${percentileChange.toStringAsFixed(1)} percentiles) - monitor closely');
        }
      } catch (e) {
        // Ignore percentile calculation errors
      }
    }

    return {
      'warnings': warnings,
      'metadata': metadata,
    };
  }

  /// Validate measurement consistency
  static Map<String, dynamic> _validateMeasurementConsistency(
    double value,
    double ageInMonths,
    String measurementType,
    List<Measurement> allMeasurements,
  ) {
    final warnings = <String>[];
    final metadata = <String, dynamic>{};

    if (allMeasurements.length < 2) return {'warnings': warnings, 'metadata': metadata};

    // Check for measurement outliers
    final sameMeasurements = allMeasurements
        .where((m) => m.measurementType == measurementType)
        .map((m) => m.value)
        .toList();

    if (sameMeasurements.length >= 3) {
      final mean = sameMeasurements.reduce((a, b) => a + b) / sameMeasurements.length;
      final variance = sameMeasurements
          .map((v) => pow(v - mean, 2))
          .reduce((a, b) => a + b) / sameMeasurements.length;
      final standardDeviation = sqrt(variance);
      
      metadata['mean'] = mean;
      metadata['standardDeviation'] = standardDeviation;
      
      // Check if current value is an outlier (more than 2 standard deviations from mean)
      if ((value - mean).abs() > 2 * standardDeviation) {
        warnings.add('Measurement appears to be an outlier - verify accuracy');
        metadata['deviationFromMean'] = (value - mean).abs();
      }
    }

    return {
      'warnings': warnings,
      'metadata': metadata,
    };
  }

  /// Get expected minimum weight for age (rough estimates)
  static double _getExpectedMinWeight(double ageInMonths) {
    if (ageInMonths == 0) return 1.5; // Minimum viable birth weight
    if (ageInMonths <= 6) return 2.5 + (ageInMonths * 0.5);
    if (ageInMonths <= 12) return 5.5 + ((ageInMonths - 6) * 0.3);
    return 8.0 + ((ageInMonths - 12) * 0.2);
  }

  /// Get expected maximum weight for age (rough estimates)
  static double _getExpectedMaxWeight(double ageInMonths) {
    if (ageInMonths == 0) return 5.5; // Maximum typical birth weight
    if (ageInMonths <= 6) return 4.5 + (ageInMonths * 1.2);
    if (ageInMonths <= 12) return 11.7 + ((ageInMonths - 6) * 0.8);
    return 16.5 + ((ageInMonths - 12) * 0.6);
  }

  /// Get expected minimum height for age (rough estimates)
  static double _getExpectedMinHeight(double ageInMonths) {
    if (ageInMonths == 0) return 40.0; // Minimum viable birth length
    if (ageInMonths <= 12) return 45.0 + (ageInMonths * 1.8);
    return 66.6 + ((ageInMonths - 12) * 0.8);
  }

  /// Get expected maximum height for age (rough estimates)
  static double _getExpectedMaxHeight(double ageInMonths) {
    if (ageInMonths == 0) return 60.0; // Maximum typical birth length
    if (ageInMonths <= 12) return 55.0 + (ageInMonths * 2.5);
    return 85.0 + ((ageInMonths - 12) * 1.2);
  }

  /// Get expected minimum head circumference for age (rough estimates)
  static double _getExpectedMinHeadCircumference(double ageInMonths) {
    if (ageInMonths == 0) return 30.0; // Minimum viable birth HC
    if (ageInMonths <= 6) return 32.0 + (ageInMonths * 1.5);
    if (ageInMonths <= 12) return 41.0 + ((ageInMonths - 6) * 0.8);
    return 45.8 + ((ageInMonths - 12) * 0.3);
  }

  /// Get expected maximum head circumference for age (rough estimates)
  static double _getExpectedMaxHeadCircumference(double ageInMonths) {
    if (ageInMonths == 0) return 38.0; // Maximum typical birth HC
    if (ageInMonths <= 6) return 38.0 + (ageInMonths * 2.2);
    if (ageInMonths <= 12) return 51.2 + ((ageInMonths - 6) * 1.0);
    return 57.2 + ((ageInMonths - 12) * 0.4);
  }

  /// Flag measurement for review based on validation results
  static bool shouldFlagForReview(MeasurementValidationResult validation) {
    // Flag if there are errors
    if (!validation.isValid) return true;
    
    // Flag if there are concerning warnings
    final concerningKeywords = ['extreme', 'concerning', 'medical consultation', 'verify'];
    for (final warning in validation.warnings) {
      for (final keyword in concerningKeywords) {
        if (warning.toLowerCase().contains(keyword)) {
          return true;
        }
      }
    }
    
    // Flag if percentile is outside normal range
    final percentile = validation.metadata['percentile'] as double?;
    if (percentile != null && (percentile < 3.0 || percentile > 97.0)) {
      return true;
    }
    
    // Flag if Z-score is extreme
    final zScore = validation.metadata['zScore'] as double?;
    if (zScore != null && zScore.abs() > 2.0) {
      return true;
    }
    
    return false;
  }
}