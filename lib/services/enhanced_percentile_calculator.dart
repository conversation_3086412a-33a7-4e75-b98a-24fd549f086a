import 'dart:math';
import 'who_data_service.dart';

/// Percentile trend data point for tracking growth patterns over time
class PercentileTrend {
  final DateTime date;
  final double ageInMonths;
  final double value;
  final double percentile;
  final double zScore;
  final String measurementType;

  const PercentileTrend({
    required this.date,
    required this.ageInMonths,
    required this.value,
    required this.percentile,
    required this.zScore,
    required this.measurementType,
  });
}

/// Growth velocity analysis result with WHO-standard velocity percentiles
class GrowthVelocityAnalysis {
  final double velocityPerMonth;
  final double velocityPercentile;
  final String interpretation;
  final bool isNormal;
  final Duration timePeriod;
  final String velocityCategory;
  final bool requiresAttention;

  const GrowthVelocityAnalysis({
    required this.velocityPerMonth,
    required this.velocityPercentile,
    required this.interpretation,
    required this.isNormal,
    required this.timePeriod,
    required this.velocityCategory,
    required this.requiresAttention,
  });
}

/// Comprehensive measurement data for enhanced calculations
class MeasurementData {
  final double value;
  final double ageInMonths;
  final DateTime date;
  final String measurementType;
  final String gender;

  const MeasurementData({
    required this.value,
    required this.ageInMonths,
    required this.date,
    required this.measurementType,
    required this.gender,
  });
}

/// Enhanced Percentile Calculator with medical-grade accuracy
class EnhancedPercentileCalculator {
  
  /// Calculate LMS-based percentile with medical-grade accuracy
  static PercentileResult calculateLMSPercentile(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    try {
      // Validate input parameters
      if (!WHODataService.isValidMeasurement(value, ageInMonths, measurementType, gender)) {
        throw ArgumentError('Invalid measurement parameters');
      }

      // Calculate exact percentile using WHO LMS method
      final percentile = WHODataService.calculateExactPercentile(
        value, ageInMonths, measurementType, gender);
      
      // Calculate Z-score for standardized assessment
      final zScore = WHODataService.calculateZScore(
        value, ageInMonths, measurementType, gender);

      // Generate comprehensive analysis
      return WHODataService.analyzePercentile(
        value, ageInMonths, measurementType, gender);
        
    } catch (e) {
      // Return error result for invalid calculations
      return PercentileResult(
        percentile: 0.0,
        zScore: 0.0,
        interpretation: 'Calculation error: ${e.toString()}',
        requiresAttention: true,
        category: 'Error',
      );
    }
  }

  /// Calculate Z-score for standardized growth assessment
  static double calculateZScore(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    try {
      // Validate input first
      if (!WHODataService.isValidMeasurement(value, ageInMonths, measurementType, gender)) {
        return 0.0;
      }
      
      final zScore = WHODataService.calculateZScore(
        value, ageInMonths, measurementType, gender);
      
      // Check for NaN or infinite values
      if (zScore.isNaN || zScore.isInfinite) {
        return 0.0;
      }
      
      return zScore;
    } catch (e) {
      return 0.0;
    }
  }

  /// Build percentile trend analysis to track growth patterns over time
  static List<PercentileTrend> calculatePercentileTrends(
    List<MeasurementData> measurements,
  ) {
    if (measurements.isEmpty) return [];

    // Sort measurements by date
    final sortedMeasurements = List<MeasurementData>.from(measurements)
      ..sort((a, b) => a.date.compareTo(b.date));

    final trends = <PercentileTrend>[];

    for (final measurement in sortedMeasurements) {
      // Validate measurement first
      if (!validateMeasurementData(measurement)) {
        continue; // Skip invalid measurements
      }
      
      try {
        final percentile = WHODataService.calculateExactPercentile(
          measurement.value,
          measurement.ageInMonths,
          measurement.measurementType,
          measurement.gender,
        );

        final zScore = WHODataService.calculateZScore(
          measurement.value,
          measurement.ageInMonths,
          measurement.measurementType,
          measurement.gender,
        );

        trends.add(PercentileTrend(
          date: measurement.date,
          ageInMonths: measurement.ageInMonths,
          value: measurement.value,
          percentile: percentile,
          zScore: zScore,
          measurementType: measurement.measurementType,
        ));
      } catch (e) {
        // Skip invalid measurements but continue processing
        continue;
      }
    }

    return trends;
  }



  /// Calculate growth velocity with WHO-standard velocity percentiles
  static GrowthVelocityAnalysis? calculateGrowthVelocity(
    List<MeasurementData> measurements,
  ) {
    if (measurements.length < 2) return null;

    // Sort measurements by date
    final sortedMeasurements = List<MeasurementData>.from(measurements)
      ..sort((a, b) => a.date.compareTo(b.date));

    // Use the two most recent measurements
    final latest = sortedMeasurements.last;
    final previous = sortedMeasurements[sortedMeasurements.length - 2];

    // Calculate time difference in months
    final timeDifference = latest.date.difference(previous.date);
    final monthsDifference = timeDifference.inDays / 30.44; // Average days per month

    if (monthsDifference <= 0) return null;

    // Calculate velocity per month
    final velocityPerMonth = (latest.value - previous.value) / monthsDifference;

    // Calculate velocity percentile using WHO standards
    final velocityPercentile = _calculateVelocityPercentile(
      velocityPerMonth,
      latest.ageInMonths,
      latest.measurementType,
      latest.gender,
    );

    // Analyze velocity
    final analysis = _analyzeGrowthVelocity(
      velocityPerMonth,
      velocityPercentile,
      latest.measurementType,
    );

    return GrowthVelocityAnalysis(
      velocityPerMonth: velocityPerMonth,
      velocityPercentile: velocityPercentile,
      interpretation: analysis['interpretation']!,
      isNormal: analysis['isNormal'] == 'true',
      timePeriod: timeDifference,
      velocityCategory: analysis['category']!,
      requiresAttention: analysis['requiresAttention'] == 'true',
    );
  }

  /// Calculate velocity percentile using WHO standards
  static double _calculateVelocityPercentile(
    double velocityPerMonth,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    // WHO velocity standards (simplified approximation)
    // In a real implementation, this would use actual WHO velocity data
    
    final expectedVelocity = _getExpectedVelocity(ageInMonths, measurementType, gender);
    
    if (expectedVelocity == 0.0) return 50.0; // Default to median if no standard available

    // Calculate percentile based on velocity relative to expected
    // Very low velocities (< 10% of expected) = very low percentile
    // Normal velocities (50-150% of expected) = normal percentiles
    // Very high velocities (> 200% of expected) = very high percentile
    
    final velocityRatio = velocityPerMonth / expectedVelocity;
    
    double percentile;
    if (velocityRatio < 0.1) {
      percentile = 1.0; // Very slow growth
    } else if (velocityRatio < 0.3) {
      percentile = 5.0; // Slow growth
    } else if (velocityRatio < 0.7) {
      percentile = 25.0; // Below average
    } else if (velocityRatio <= 1.3) {
      percentile = 50.0; // Normal
    } else if (velocityRatio <= 1.7) {
      percentile = 75.0; // Above average
    } else if (velocityRatio <= 2.0) {
      percentile = 90.0; // Rapid growth
    } else {
      percentile = 95.0; // Very rapid growth
    }
    
    return percentile;
  }

  /// Get expected velocity for measurement type, age, and gender
  static double _getExpectedVelocity(double ageInMonths, String measurementType, String gender) {
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return _getWeightVelocityStandard(ageInMonths, gender);
      case 'height':
      case 'length':
        return _getHeightVelocityStandard(ageInMonths, gender);
      case 'head_circumference':
      case 'head circumference':
        return _getHeadCircumferenceVelocityStandard(ageInMonths, gender);
      default:
        return 0.0;
    }
  }

  /// Get expected weight velocity standard for age and gender
  static double _getWeightVelocityStandard(double ageInMonths, String gender) {
    // WHO weight velocity approximations (kg/month)
    if (ageInMonths <= 3) return 0.8; // Rapid early growth
    if (ageInMonths <= 6) return 0.5; // Moderate growth
    if (ageInMonths <= 12) return 0.3; // Slower growth
    if (ageInMonths <= 24) return 0.2; // Toddler growth
    return 0.15; // Preschool growth
  }

  /// Get expected height velocity standard for age and gender
  static double _getHeightVelocityStandard(double ageInMonths, String gender) {
    // WHO height velocity approximations (cm/month)
    if (ageInMonths <= 3) return 3.5; // Rapid early growth
    if (ageInMonths <= 6) return 2.0; // Moderate growth
    if (ageInMonths <= 12) return 1.2; // Slower growth
    if (ageInMonths <= 24) return 0.8; // Toddler growth
    return 0.5; // Preschool growth
  }

  /// Get expected head circumference velocity standard for age and gender
  static double _getHeadCircumferenceVelocityStandard(double ageInMonths, String gender) {
    // WHO head circumference velocity approximations (cm/month)
    if (ageInMonths <= 3) return 1.2; // Rapid early growth
    if (ageInMonths <= 6) return 0.8; // Moderate growth
    if (ageInMonths <= 12) return 0.4; // Slower growth
    if (ageInMonths <= 24) return 0.2; // Toddler growth
    return 0.1; // Preschool growth
  }

  /// Analyze growth velocity and provide interpretation
  static Map<String, String> _analyzeGrowthVelocity(
    double velocityPerMonth,
    double velocityPercentile,
    String measurementType,
  ) {
    String category;
    String interpretation;
    bool isNormal;
    bool requiresAttention;

    if (velocityPercentile < 3.0) {
      category = "Very Slow Growth";
      interpretation = "Growth velocity below 3rd percentile - medical consultation recommended";
      isNormal = false;
      requiresAttention = true;
    } else if (velocityPercentile < 10.0) {
      category = "Slow Growth";
      interpretation = "Growth velocity below 10th percentile - monitor closely";
      isNormal = false;
      requiresAttention = true;
    } else if (velocityPercentile < 25.0) {
      category = "Below Average Growth";
      interpretation = "Growth velocity below average but may be normal";
      isNormal = true;
      requiresAttention = false;
    } else if (velocityPercentile <= 75.0) {
      category = "Normal Growth";
      interpretation = "Growth velocity within normal range";
      isNormal = true;
      requiresAttention = false;
    } else if (velocityPercentile <= 90.0) {
      category = "Above Average Growth";
      interpretation = "Growth velocity above average";
      isNormal = true;
      requiresAttention = false;
    } else if (velocityPercentile <= 97.0) {
      category = "Rapid Growth";
      interpretation = "Growth velocity above 90th percentile - monitor for consistency";
      isNormal = true;
      requiresAttention = false;
    } else {
      category = "Very Rapid Growth";
      interpretation = "Growth velocity above 97th percentile - consider medical consultation";
      isNormal = false;
      requiresAttention = true;
    }

    return {
      'category': category,
      'interpretation': interpretation,
      'isNormal': isNormal.toString(),
      'requiresAttention': requiresAttention.toString(),
    };
  }

  /// Calculate percentile crossing analysis
  static Map<String, dynamic> analyzePercentileCrossing(
    List<PercentileTrend> trends,
  ) {
    if (trends.length < 2) {
      return {
        'hasCrossing': false,
        'crossingMagnitude': 0.0,
        'crossingDirection': 'none',
        'requiresAttention': false,
        'interpretation': 'Insufficient data for crossing analysis',
      };
    }

    // Calculate percentile changes between consecutive measurements
    final percentileChanges = <double>[];
    for (int i = 1; i < trends.length; i++) {
      final change = trends[i].percentile - trends[i - 1].percentile;
      percentileChanges.add(change);
    }

    // Calculate total percentile change
    final totalChange = trends.last.percentile - trends.first.percentile;
    final averageChange = percentileChanges.reduce((a, b) => a + b) / percentileChanges.length;

    // Determine if significant crossing occurred
    final significantCrossing = totalChange.abs() > 25.0; // More than 25 percentile points
    final rapidCrossing = percentileChanges.any((change) => change.abs() > 15.0); // Single measurement change > 15 percentiles

    String direction;
    if (totalChange.abs() <= 5.0) {
      direction = 'stable'; // Small changes are considered stable
    } else if (totalChange > 0) {
      direction = 'upward';
    } else {
      direction = 'downward';
    }

    bool requiresAttention = significantCrossing || rapidCrossing;
    
    String interpretation;
    if (significantCrossing) {
      interpretation = 'Significant percentile crossing detected (${totalChange.toStringAsFixed(1)} percentiles). Consider medical evaluation.';
    } else if (rapidCrossing) {
      interpretation = 'Rapid percentile change detected in recent measurements. Monitor closely.';
    } else {
      interpretation = 'Growth tracking along consistent percentile curve.';
    }

    return {
      'hasCrossing': significantCrossing || rapidCrossing,
      'crossingMagnitude': totalChange.abs(),
      'crossingDirection': direction,
      'requiresAttention': requiresAttention,
      'interpretation': interpretation,
      'averageChange': averageChange,
      'totalChange': totalChange,
    };
  }

  /// Generate comprehensive growth pattern summary
  static Map<String, dynamic> generateGrowthSummary(
    List<PercentileTrend> trends,
    GrowthVelocityAnalysis? velocityAnalysis,
  ) {
    if (trends.isEmpty) {
      return {
        'summary': 'No measurement data available',
        'currentPercentile': 0.0,
        'trend': 'unknown',
        'velocity': 'unknown',
        'overallAssessment': 'Insufficient data',
        'recommendations': ['Add measurements to track growth patterns'],
      };
    }

    final currentTrend = trends.last;
    final crossingAnalysis = analyzePercentileCrossing(trends);
    
    final recommendations = <String>[];
    String overallAssessment;

    // Assess current percentile
    if (currentTrend.percentile < 3.0 || currentTrend.percentile > 97.0) {
      recommendations.add('Consult healthcare provider about current percentile');
      overallAssessment = 'Requires medical attention';
    } else if (crossingAnalysis['requiresAttention']) {
      recommendations.add('Monitor growth pattern closely');
      overallAssessment = 'Monitor closely';
    } else if (velocityAnalysis?.requiresAttention == true) {
      recommendations.add('Track growth velocity trends');
      overallAssessment = 'Monitor velocity';
    } else {
      recommendations.add('Continue regular measurements');
      overallAssessment = 'Normal growth pattern';
    }

    // Add measurement frequency recommendations
    if (trends.length < 3) {
      recommendations.add('Take more frequent measurements for better trend analysis');
    }

    return {
      'summary': 'Current ${currentTrend.measurementType} at ${currentTrend.percentile.toStringAsFixed(1)}th percentile',
      'currentPercentile': currentTrend.percentile,
      'trend': crossingAnalysis['crossingDirection'],
      'velocity': velocityAnalysis?.velocityCategory ?? 'Unknown',
      'overallAssessment': overallAssessment,
      'recommendations': recommendations,
      'crossingAnalysis': crossingAnalysis,
      'velocityAnalysis': velocityAnalysis,
    };
  }

  /// Validate measurement data for calculations
  static bool validateMeasurementData(MeasurementData measurement) {
    return WHODataService.isValidMeasurement(
      measurement.value,
      measurement.ageInMonths,
      measurement.measurementType,
      measurement.gender,
    );
  }

  /// Calculate age in months from birth date
  static double calculateAgeInMonths(DateTime birthDate, DateTime measurementDate) {
    final difference = measurementDate.difference(birthDate);
    return difference.inDays / 30.44; // Average days per month
  }
}