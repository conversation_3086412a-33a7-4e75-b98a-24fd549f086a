import '../models/feature_access.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';

/// Abstract strategy for feature access checking
abstract class FeatureAccessStrategy {
  /// Check if user has access to the feature
  bool hasAccess(SubscriptionInfo subscription, int currentUsage);
  
  /// Get usage limit for the feature
  int? getUsageLimit(SubscriptionInfo subscription);
  
  /// Get restriction message when access is denied
  String getRestrictionMessage();
  
  /// Get upgrade prompt configuration
  UpgradePromptConfig getUpgradePrompt();
}

/// Strategy for premium-only features (binary access)
class PremiumOnlyStrategy implements FeatureAccessStrategy {
  final AppFeature feature;
  
  const PremiumOnlyStrategy(this.feature);
  
  @override
  bool hasAccess(SubscriptionInfo subscription, int currentUsage) {
    return subscription.isPremium;
  }
  
  @override
  int? getUsageLimit(SubscriptionInfo subscription) {
    return subscription.isPremium ? null : 0;
  }
  
  @override
  String getRestrictionMessage() {
    return '${feature.displayName} requires Premium plan. Upgrade to unlock this feature.';
  }
  
  @override
  UpgradePromptConfig getUpgradePrompt() {
    return UpgradePromptConfig(
      title: 'Upgrade to Premium',
      description: 'Unlock ${feature.displayName} and all premium features.',
      benefits: _getFeatureBenefits(feature),
      ctaText: 'Upgrade Now',
    );
  }
  
  List<String> _getFeatureBenefits(AppFeature feature) {
    switch (feature) {
      case AppFeature.aiInsights:
        return [
          'AI-powered pattern analysis',
          'Personalized recommendations',
          'Development milestone predictions',
          'Sleep and feeding insights',
        ];
      case AppFeature.aiChat:
        return [
          '24/7 AI chat assistant',
          'Personalized parenting advice',
          'Evidence-based recommendations',
          'Unlimited conversations',
        ];
      case AppFeature.whoGrowthCharts:
        return [
          'Official WHO growth standards',
          'Percentile tracking and trends',
          'Growth milestone alerts',
          'Pediatrician-ready reports',
        ];
      default:
        return [
          'Access to ${feature.displayName}',
          'All premium features included',
          'Priority customer support',
          'Regular feature updates',
        ];
    }
  }
}

/// Strategy for usage-limited features
class UsageLimitedStrategy implements FeatureAccessStrategy {
  final AppFeature feature;
  final int freeLimit;
  final int? premiumLimit;
  final String customMessage;
  final List<String> benefits;
  
  const UsageLimitedStrategy({
    required this.feature,
    required this.freeLimit,
    this.premiumLimit,
    required this.customMessage,
    required this.benefits,
  });
  
  @override
  bool hasAccess(SubscriptionInfo subscription, int currentUsage) {
    final limit = getUsageLimit(subscription);
    return limit == null || currentUsage < limit;
  }
  
  @override
  int? getUsageLimit(SubscriptionInfo subscription) {
    return subscription.isPremium ? premiumLimit : freeLimit;
  }
  
  @override
  String getRestrictionMessage() {
    return customMessage;
  }
  
  @override
  UpgradePromptConfig getUpgradePrompt() {
    return UpgradePromptConfig(
      title: 'Upgrade for More Access',
      description: customMessage,
      benefits: benefits,
      ctaText: 'Upgrade to Premium',
    );
  }
}

/// Strategy for conditional premium features (requires specific subscription flags)
class ConditionalPremiumStrategy implements FeatureAccessStrategy {
  final AppFeature feature;
  final bool Function(SubscriptionInfo) accessChecker;
  final String message;
  final List<String> benefits;
  
  const ConditionalPremiumStrategy({
    required this.feature,
    required this.accessChecker,
    required this.message,
    required this.benefits,
  });
  
  @override
  bool hasAccess(SubscriptionInfo subscription, int currentUsage) {
    return accessChecker(subscription);
  }
  
  @override
  int? getUsageLimit(SubscriptionInfo subscription) {
    return hasAccess(subscription, 0) ? null : 0;
  }
  
  @override
  String getRestrictionMessage() {
    return message;
  }
  
  @override
  UpgradePromptConfig getUpgradePrompt() {
    return UpgradePromptConfig(
      title: 'Upgrade to Premium',
      description: message,
      benefits: benefits,
      ctaText: 'Upgrade Now',
    );
  }
}

/// Factory for creating feature access strategies
class FeatureAccessStrategyFactory {
  static final Map<AppFeature, FeatureAccessStrategy> _strategies = {
    AppFeature.multipleBabyProfiles: UsageLimitedStrategy(
      feature: AppFeature.multipleBabyProfiles,
      freeLimit: 1,
      premiumLimit: null, // Unlimited
      customMessage: 'Free plan limited to 1 baby profile. Upgrade to Premium for unlimited baby profiles.',
      benefits: [
        'Unlimited baby profiles',
        'Individual tracking for each child',
        'Family-wide insights and analytics',
        'Shared access with family members',
      ],
    ),
    
    AppFeature.familySharing: ConditionalPremiumStrategy(
      feature: AppFeature.familySharing,
      accessChecker: (subscription) => subscription.isPremium && subscription.maxFamilyMembers > 1,
      message: 'Family sharing requires Premium plan. Upgrade to invite up to 10 family members.',
      benefits: [
        'Invite up to 10 family members',
        'Real-time activity sharing',
        'Collaborative care tracking',
        'Role-based permissions',
      ],
    ),
    
    AppFeature.aiInsights: ConditionalPremiumStrategy(
      feature: AppFeature.aiInsights,
      accessChecker: (subscription) => subscription.isPremium && subscription.includesAiInsights,
      message: 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
      benefits: [
        'AI-powered pattern analysis',
        'Personalized recommendations',
        'Development milestone predictions',
        'Sleep and feeding insights',
      ],
    ),
    
    AppFeature.aiChat: ConditionalPremiumStrategy(
      feature: AppFeature.aiChat,
      accessChecker: (subscription) => subscription.isPremium && subscription.includesAiInsights,
      message: 'Ask AI chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
      benefits: [
        '24/7 AI chat assistant',
        'Personalized parenting advice',
        'Evidence-based recommendations',
        'Unlimited conversations',
      ],
    ),
    
    AppFeature.dataExport: ConditionalPremiumStrategy(
      feature: AppFeature.dataExport,
      accessChecker: (subscription) => subscription.isPremium && subscription.includesDataExport,
      message: 'Data export requires Premium plan. Upgrade to export your data for healthcare providers.',
      benefits: [
        'Unlimited data exports',
        'PDF and CSV formats',
        'Healthcare provider reports',
        'Complete activity history',
      ],
    ),
    
    // Premium-only features
    AppFeature.whoGrowthCharts: PremiumOnlyStrategy(AppFeature.whoGrowthCharts),
    AppFeature.advancedAnalytics: PremiumOnlyStrategy(AppFeature.advancedAnalytics),
    AppFeature.customReminders: PremiumOnlyStrategy(AppFeature.customReminders),
    AppFeature.prioritySupport: PremiumOnlyStrategy(AppFeature.prioritySupport),
  };
  
  /// Get strategy for a specific feature
  static FeatureAccessStrategy getStrategy(AppFeature feature) {
    return _strategies[feature] ?? PremiumOnlyStrategy(feature);
  }
}