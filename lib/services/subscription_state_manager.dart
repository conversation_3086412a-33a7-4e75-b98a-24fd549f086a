import 'package:flutter/foundation.dart';
import '../models/subscription_info.dart';
import '../models/enums.dart';

/// Manages subscription state and persistence
class SubscriptionStateManager extends ChangeNotifier {
  SubscriptionInfo _currentSubscription = SubscriptionPlans.free;
  bool _isLoading = false;
  SubscriptionError? _error;

  // Getters
  SubscriptionInfo get currentSubscription => _currentSubscription;
  bool get isLoading => _isLoading;
  SubscriptionError? get error => _error;
  String? get errorMessage => _error?.message;

  /// Update subscription state
  void updateSubscription(SubscriptionInfo subscription) {
    _currentSubscription = subscription;
    _clearError();
    notifyListeners();
  }

  /// Set loading state
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// Set error state
  void setError(SubscriptionError error) {
    _error = error;
    _isLoading = false;
    notifyListeners();
  }

  /// Clear error state
  void clearError() {
    _error = null;
    notifyListeners();
  }

  void _clearError() => clearError();
}