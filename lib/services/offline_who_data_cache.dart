import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'who_data_service.dart';

/// Offline WHO data cache service for maintaining functionality without internet
class OfflineWHODataCache {
  static const String _cacheKeyPrefix = 'who_data_cache_';
  static const String _cacheVersionKey = 'who_cache_version';
  static const String _lastUpdateKey = 'who_cache_last_update';
  static const int _currentCacheVersion = 1;
  static const Duration _cacheValidityDuration = Duration(days: 30);
  
  /// Initialize cache with WHO data and enhanced offline support
  static Future<void> initializeCache() async {
    final prefs = await SharedPreferences.getInstance();
    final cacheVersion = prefs.getInt(_cacheVersionKey) ?? 0;
    final lastUpdate = prefs.getString(_lastUpdateKey);
    
    bool needsUpdate = false;
    
    // Check if cache needs update
    if (cacheVersion < _currentCacheVersion) {
      needsUpdate = true;
    } else if (lastUpdate != null) {
      final lastUpdateDate = DateTime.parse(lastUpdate);
      final now = DateTime.now();
      if (now.difference(lastUpdateDate) > _cacheValidityDuration) {
        needsUpdate = true;
      }
    } else {
      needsUpdate = true;
    }
    
    if (needsUpdate) {
      await _cacheWHOData(prefs);
    }
    
    // Pre-cache commonly used calculations for better offline performance
    await _precacheCommonCalculations(prefs);
  }
  
  /// Pre-cache common percentile calculations for offline use
  static Future<void> _precacheCommonCalculations(SharedPreferences prefs) async {
    try {
      final commonAges = [0.5, 1.0, 2.0, 3.0, 6.0, 9.0, 12.0, 18.0, 24.0, 36.0, 48.0, 60.0];
      final measurementTypes = ['weight', 'height', 'head_circumference'];
      final genders = ['male', 'female'];
      
      final precacheData = <String, Map<String, dynamic>>{};
      
      for (final measurementType in measurementTypes) {
        for (final gender in genders) {
          final typeGenderKey = '${measurementType}_$gender';
          precacheData[typeGenderKey] = <String, dynamic>{};
          
          for (final age in commonAges) {
            final ageKey = 'age_$age';
            precacheData[typeGenderKey]![ageKey] = <String, double>{};
            
            // Cache percentile values for common percentiles
            final percentiles = [3.0, 10.0, 25.0, 50.0, 75.0, 90.0, 97.0];
            for (final percentile in percentiles) {
              try {
                final value = WHODataService.getPercentileValue(
                  percentile, age, measurementType, gender
                );
                precacheData[typeGenderKey]![ageKey]['p$percentile'] = value;
              } catch (e) {
                // Skip if calculation fails
                continue;
              }
            }
          }
        }
      }
      
      // Store precached calculations
      await prefs.setString('${_cacheKeyPrefix}precalculated', jsonEncode(precacheData));
      
    } catch (e) {
      // If precaching fails, continue without it
      print('Failed to precache WHO calculations: $e');
    }
  }
  
  /// Cache WHO data to local storage
  static Future<void> _cacheWHOData(SharedPreferences prefs) async {
    try {
      // Cache percentile curves for common ranges
      final percentiles = [3.0, 10.0, 25.0, 50.0, 75.0, 90.0, 97.0];
      final measurementTypes = ['weight', 'height', 'head_circumference'];
      final genders = ['male', 'female'];
      final ageRanges = [
        {'max': 6.0, 'step': 0.25},   // 0-6 months, every week
        {'max': 12.0, 'step': 0.5},   // 6-12 months, every 2 weeks
        {'max': 24.0, 'step': 1.0},   // 1-2 years, every month
        {'max': 60.0, 'step': 3.0},   // 2-5 years, every 3 months
      ];
      
      for (final measurementType in measurementTypes) {
        for (final gender in genders) {
          final cacheData = <String, dynamic>{};
          
          // Cache percentile curves
          for (final percentile in percentiles) {
            final curvePoints = <Map<String, double>>[];
            
            for (final ageRange in ageRanges) {
              final maxAge = ageRange['max'] as double;
              final step = ageRange['step'] as double;
              final startAge = ageRange == ageRanges.first ? 0.0 : 
                               ageRanges[ageRanges.indexOf(ageRange) - 1]['max'] as double;
              
              for (double age = startAge; age <= maxAge; age += step) {
                try {
                  final value = WHODataService.getPercentileValue(
                    percentile, age, measurementType, gender
                  );
                  curvePoints.add({
                    'age': age,
                    'value': value,
                  });
                } catch (e) {
                  // Skip invalid points
                  continue;
                }
              }
            }
            
            cacheData['percentile_${percentile.toInt()}'] = curvePoints;
          }
          
          // Cache LMS parameters for exact calculations
          final lmsData = <Map<String, double>>[];
          for (double age = 0; age <= 60; age += 0.5) {
            try {
              // Get sample percentile values to reverse-engineer LMS
              final p3 = WHODataService.getPercentileValue(3.0, age, measurementType, gender);
              final p50 = WHODataService.getPercentileValue(50.0, age, measurementType, gender);
              final p97 = WHODataService.getPercentileValue(97.0, age, measurementType, gender);
              
              lmsData.add({
                'age': age,
                'p3': p3,
                'p50': p50,
                'p97': p97,
              });
            } catch (e) {
              // Skip invalid ages
              continue;
            }
          }
          
          cacheData['lms_data'] = lmsData;
          
          // Store in SharedPreferences
          final cacheKey = '${_cacheKeyPrefix}${measurementType}_$gender';
          await prefs.setString(cacheKey, jsonEncode(cacheData));
        }
      }
      
      // Update cache metadata
      await prefs.setInt(_cacheVersionKey, _currentCacheVersion);
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());
      
    } catch (e) {
      // If caching fails, we'll fall back to online calculations
      print('Failed to cache WHO data: $e');
    }
  }
  
  /// Get cached percentile curve points
  static Future<List<Map<String, double>>?> getCachedPercentileCurve({
    required double percentile,
    required String measurementType,
    required String gender,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cacheKeyPrefix}${measurementType}_$gender';
      final cachedDataString = prefs.getString(cacheKey);
      
      if (cachedDataString == null) return null;
      
      final cachedData = jsonDecode(cachedDataString) as Map<String, dynamic>;
      final curveKey = 'percentile_${percentile.toInt()}';
      
      if (!cachedData.containsKey(curveKey)) return null;
      
      final curveData = cachedData[curveKey] as List<dynamic>;
      return curveData.map((point) => Map<String, double>.from(point)).toList();
      
    } catch (e) {
      return null;
    }
  }
  
  /// Calculate percentile using cached data
  static Future<double?> calculatePercentileOffline({
    required double value,
    required double ageInMonths,
    required String measurementType,
    required String gender,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = '${_cacheKeyPrefix}${measurementType}_$gender';
      final cachedDataString = prefs.getString(cacheKey);
      
      if (cachedDataString == null) return null;
      
      final cachedData = jsonDecode(cachedDataString) as Map<String, dynamic>;
      final lmsData = cachedData['lms_data'] as List<dynamic>;
      
      // Find closest age points for interpolation
      Map<String, double>? lowerPoint;
      Map<String, double>? upperPoint;
      
      for (int i = 0; i < lmsData.length - 1; i++) {
        final current = Map<String, double>.from(lmsData[i]);
        final next = Map<String, double>.from(lmsData[i + 1]);
        
        if (ageInMonths >= current['age']! && ageInMonths <= next['age']!) {
          lowerPoint = current;
          upperPoint = next;
          break;
        }
      }
      
      if (lowerPoint == null || upperPoint == null) {
        // Use closest point if exact range not found
        double minDistance = double.infinity;
        Map<String, double>? closestPoint;
        
        for (final point in lmsData) {
          final pointData = Map<String, double>.from(point);
          final distance = (pointData['age']! - ageInMonths).abs();
          if (distance < minDistance) {
            minDistance = distance;
            closestPoint = pointData;
          }
        }
        
        if (closestPoint == null) return null;
        return _estimatePercentileFromValues(value, closestPoint);
      }
      
      // Interpolate between points
      final ratio = (ageInMonths - lowerPoint['age']!) / 
                   (upperPoint['age']! - lowerPoint['age']!);
      
      final interpolatedP3 = lowerPoint['p3']! + (upperPoint['p3']! - lowerPoint['p3']!) * ratio;
      final interpolatedP50 = lowerPoint['p50']! + (upperPoint['p50']! - lowerPoint['p50']!) * ratio;
      final interpolatedP97 = lowerPoint['p97']! + (upperPoint['p97']! - lowerPoint['p97']!) * ratio;
      
      final interpolatedPoint = {
        'p3': interpolatedP3,
        'p50': interpolatedP50,
        'p97': interpolatedP97,
      };
      
      return _estimatePercentileFromValues(value, interpolatedPoint);
      
    } catch (e) {
      return null;
    }
  }
  
  /// Estimate percentile from cached reference values
  static double _estimatePercentileFromValues(double value, Map<String, double> referenceValues) {
    final p3 = referenceValues['p3']!;
    final p50 = referenceValues['p50']!;
    final p97 = referenceValues['p97']!;
    
    if (value <= p3) {
      // Below 3rd percentile - estimate between 0.1 and 3
      final ratio = (value / p3).clamp(0.0, 1.0);
      return 0.1 + (2.9 * ratio);
    } else if (value <= p50) {
      // Between 3rd and 50th percentile
      final ratio = (value - p3) / (p50 - p3);
      return 3.0 + (47.0 * ratio);
    } else if (value <= p97) {
      // Between 50th and 97th percentile
      final ratio = (value - p50) / (p97 - p50);
      return 50.0 + (47.0 * ratio);
    } else {
      // Above 97th percentile - estimate between 97 and 99.9
      final ratio = ((value / p97) - 1.0).clamp(0.0, 1.0);
      return 97.0 + (2.9 * ratio);
    }
  }
  
  /// Check if cache is available and valid
  static Future<bool> isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheVersion = prefs.getInt(_cacheVersionKey) ?? 0;
      final lastUpdate = prefs.getString(_lastUpdateKey);
      
      if (cacheVersion < _currentCacheVersion) return false;
      
      if (lastUpdate != null) {
        final lastUpdateDate = DateTime.parse(lastUpdate);
        final now = DateTime.now();
        if (now.difference(lastUpdateDate) > _cacheValidityDuration) {
          return false;
        }
      } else {
        return false;
      }
      
      // Check if at least one cache entry exists
      final testKey = '${_cacheKeyPrefix}weight_male';
      return prefs.containsKey(testKey);
      
    } catch (e) {
      return false;
    }
  }
  
  /// Clear all cached data
  static Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix) || 
            key == _cacheVersionKey || 
            key == _lastUpdateKey) {
          await prefs.remove(key);
        }
      }
    } catch (e) {
      print('Failed to clear WHO data cache: $e');
    }
  }
  
  /// Get precached percentile value for fast offline access
  static Future<double?> getPrecachedPercentileValue({
    required double percentile,
    required double ageInMonths,
    required String measurementType,
    required String gender,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final precacheDataString = prefs.getString('${_cacheKeyPrefix}precalculated');
      
      if (precacheDataString == null) return null;
      
      final precacheData = jsonDecode(precacheDataString) as Map<String, dynamic>;
      final typeGenderKey = '${measurementType}_$gender';
      
      if (!precacheData.containsKey(typeGenderKey)) return null;
      
      final typeData = precacheData[typeGenderKey] as Map<String, dynamic>;
      
      // Find closest cached age
      String? closestAgeKey;
      double minDistance = double.infinity;
      
      for (final ageKey in typeData.keys) {
        if (ageKey.startsWith('age_')) {
          final age = double.parse(ageKey.substring(4));
          final distance = (age - ageInMonths).abs();
          if (distance < minDistance) {
            minDistance = distance;
            closestAgeKey = ageKey;
          }
        }
      }
      
      if (closestAgeKey == null) return null;
      
      final ageData = typeData[closestAgeKey] as Map<String, dynamic>;
      final percentileKey = 'p$percentile';
      
      if (ageData.containsKey(percentileKey)) {
        return (ageData[percentileKey] as num).toDouble();
      }
      
      return null;
      
    } catch (e) {
      return null;
    }
  }
  
  /// Get offline chart data for time range
  static Future<OfflineChartData?> getOfflineChartData({
    required String measurementType,
    required String gender,
    required double maxAgeMonths,
    required List<double> percentiles,
  }) async {
    try {
      final curves = <double, List<Map<String, double>>>{};
      
      for (final percentile in percentiles) {
        final curvePoints = await getCachedPercentileCurve(
          percentile: percentile,
          measurementType: measurementType,
          gender: gender,
        );
        
        if (curvePoints != null) {
          // Filter points within age range
          final filteredPoints = curvePoints
              .where((point) => point['age']! <= maxAgeMonths)
              .toList();
          
          if (filteredPoints.isNotEmpty) {
            curves[percentile] = filteredPoints;
          }
        }
      }
      
      if (curves.isEmpty) return null;
      
      return OfflineChartData(
        percentileCurves: curves,
        measurementType: measurementType,
        gender: gender,
        maxAge: maxAgeMonths,
        isComplete: curves.length == percentiles.length,
      );
      
    } catch (e) {
      return null;
    }
  }
  
  /// Check offline functionality status
  static Future<OfflineStatus> getOfflineStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final isValid = await isCacheValid();
      final stats = await getCacheStatistics();
      
      // Check completeness of cache
      final expectedEntries = 6; // 3 measurement types × 2 genders
      final hasBasicCache = stats.entryCount >= expectedEntries;
      final hasPrecache = prefs.containsKey('${_cacheKeyPrefix}precalculated');
      
      OfflineCapability capability;
      if (!isValid || stats.entryCount == 0) {
        capability = OfflineCapability.none;
      } else if (hasBasicCache && hasPrecache) {
        capability = OfflineCapability.full;
      } else if (hasBasicCache) {
        capability = OfflineCapability.basic;
      } else {
        capability = OfflineCapability.limited;
      }
      
      return OfflineStatus(
        isAvailable: isValid,
        capability: capability,
        cacheSize: stats.formattedSize,
        lastUpdate: stats.lastUpdate,
        completeness: hasBasicCache ? 1.0 : (stats.entryCount / expectedEntries).clamp(0.0, 1.0),
      );
      
    } catch (e) {
      return OfflineStatus(
        isAvailable: false,
        capability: OfflineCapability.none,
        cacheSize: '0 B',
        lastUpdate: null,
        completeness: 0.0,
      );
    }
  }
  
  /// Get cache statistics
  static Future<CacheStatistics> getCacheStatistics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      int cacheEntries = 0;
      int totalSize = 0;
      String? lastUpdate;
      
      for (final key in keys) {
        if (key.startsWith(_cacheKeyPrefix)) {
          cacheEntries++;
          final data = prefs.getString(key);
          if (data != null) {
            totalSize += data.length;
          }
        } else if (key == _lastUpdateKey) {
          lastUpdate = prefs.getString(key);
        }
      }
      
      return CacheStatistics(
        entryCount: cacheEntries,
        totalSizeBytes: totalSize,
        lastUpdate: lastUpdate != null ? DateTime.parse(lastUpdate) : null,
        isValid: await isCacheValid(),
      );
      
    } catch (e) {
      return CacheStatistics(
        entryCount: 0,
        totalSizeBytes: 0,
        lastUpdate: null,
        isValid: false,
      );
    }
  }
}

/// Offline chart data for rendering without internet
class OfflineChartData {
  final Map<double, List<Map<String, double>>> percentileCurves;
  final String measurementType;
  final String gender;
  final double maxAge;
  final bool isComplete;
  
  const OfflineChartData({
    required this.percentileCurves,
    required this.measurementType,
    required this.gender,
    required this.maxAge,
    required this.isComplete,
  });
}

/// Offline functionality status
class OfflineStatus {
  final bool isAvailable;
  final OfflineCapability capability;
  final String cacheSize;
  final DateTime? lastUpdate;
  final double completeness; // 0.0 to 1.0
  
  const OfflineStatus({
    required this.isAvailable,
    required this.capability,
    required this.cacheSize,
    required this.lastUpdate,
    required this.completeness,
  });
}

/// Offline capability levels
enum OfflineCapability {
  none,     // No offline functionality
  limited,  // Basic percentile curves only
  basic,    // Percentile curves and basic calculations
  full,     // Complete offline functionality with precached data
}

/// Offline chart configuration for optimal performance
class OfflineChartConfiguration {
  final bool isOptimized;
  final List<double> recommendedPercentiles;
  final int maxDataPoints;
  final double cacheHitRate;
  final OfflinePerformanceLevel performanceLevel;
  
  const OfflineChartConfiguration({
    required this.isOptimized,
    required this.recommendedPercentiles,
    required this.maxDataPoints,
    required this.cacheHitRate,
    required this.performanceLevel,
  });
}

/// Offline performance levels
enum OfflinePerformanceLevel {
  excellent,    // Full cache with precalculated data
  good,         // Basic cache with curve data
  limited,      // Limited cache functionality
  unavailable,  // No offline capability
}

/// Offline performance metrics
class OfflinePerformanceMetrics {
  final double cacheHitRate;
  final double averageResponseTimeMs;
  final int totalCacheSize;
  final OfflineCapability availableCapability;
  final DateTime? lastOptimization;
  final bool recommendsOptimization;
  
  const OfflinePerformanceMetrics({
    required this.cacheHitRate,
    required this.averageResponseTimeMs,
    required this.totalCacheSize,
    required this.availableCapability,
    required this.lastOptimization,
    required this.recommendsOptimization,
  });
}

/// Cache statistics information
class CacheStatistics {
  final int entryCount;
  final int totalSizeBytes;
  final DateTime? lastUpdate;
  final bool isValid;
  
  const CacheStatistics({
    required this.entryCount,
    required this.totalSizeBytes,
    required this.lastUpdate,
    required this.isValid,
  });
  
  /// Get human-readable size
  String get formattedSize {
    if (totalSizeBytes < 1024) {
      return '$totalSizeBytes B';
    } else if (totalSizeBytes < 1024 * 1024) {
      return '${(totalSizeBytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(totalSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}