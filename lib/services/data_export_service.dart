import 'dart:io';
import 'dart:typed_data';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import '../models/baby_profile.dart';
import '../models/measurement.dart';
import '../services/growth_analyzer.dart';
import '../services/enhanced_percentile_calculator.dart';

/// Professional data export service for growth charts and medical reports
class DataExportService {
  static const String _reportVersion = '1.0';
  static const String _appName = 'BabyTracker Pro';
  
  /// Generate comprehensive PDF growth report
  static Future<Uint8List> generatePDFReport({
    required List<Measurement> measurements,
    required BabyProfile baby,
    String reportType = 'comprehensive',
    bool includeCharts = true,
    bool includeAnalysis = true,
    Map<String, dynamic>? customOptions,
  }) async {
    try {
      // Validate input data
      final validationResult = _validateExportData(measurements, baby);
      if (!validationResult['isValid']) {
        throw Exception('Export validation failed: ${validationResult['errors'].join(', ')}');
      }

      // Prepare measurement data for analysis
      final measurementData = measurements
          .map((m) => m.toMeasurementData(baby.gender))
          .toList();

      // Generate growth analysis
      final growthAnalysis = GrowthAnalyzer.analyzeGrowthPattern(measurementData, baby);

      // Create PDF document
      final pdf = pw.Document(
        version: PdfVersion.pdf_1_5,
        compress: true,
        title: '${baby.name} - Growth Report',
        author: _appName,
        creator: _appName,
        subject: 'Growth Chart Report',
        keywords: 'growth, baby, percentiles, WHO, medical',
      );

      // Load fonts for better typography
      final regularFont = await PdfGoogleFonts.interRegular();
      final boldFont = await PdfGoogleFonts.interBold();
      final monoFont = await PdfGoogleFonts.jetBrainsMonoRegular();

      // Generate report pages
      await _addCoverPage(pdf, baby, growthAnalysis, regularFont, boldFont);
      
      if (includeAnalysis) {
        await _addSummaryPage(pdf, baby, growthAnalysis, measurements, regularFont, boldFont);
        await _addAnalysisPage(pdf, growthAnalysis, regularFont, boldFont, monoFont);
      }
      
      if (includeCharts) {
        await _addChartsPages(pdf, measurements, baby, regularFont, boldFont);
      }
      
      await _addDataTablesPage(pdf, measurements, baby, regularFont, boldFont, monoFont);
      await _addAppendixPage(pdf, baby, growthAnalysis, regularFont, boldFont, monoFont);

      return await pdf.save();
    } catch (e) {
      throw Exception('PDF generation failed: $e');
    }
  }

  /// Generate healthcare provider formatted data
  static Future<String> formatForHealthcareProvider({
    required List<Measurement> measurements,
    required BabyProfile baby,
    String format = 'medical_standard',
  }) async {
    try {
      final validationResult = _validateExportData(measurements, baby);
      if (!validationResult['isValid']) {
        throw Exception('Data validation failed: ${validationResult['errors'].join(', ')}');
      }

      final measurementData = measurements
          .map((m) => m.toMeasurementData(baby.gender))
          .toList();
      final growthAnalysis = GrowthAnalyzer.analyzeGrowthPattern(measurementData, baby);

      switch (format) {
        case 'medical_standard':
          return _generateMedicalStandardFormat(measurements, baby, growthAnalysis);
        case 'csv':
          return _generateCSVFormat(measurements, baby);
        case 'json':
          return _generateJSONFormat(measurements, baby, growthAnalysis);
        default:
          return _generateMedicalStandardFormat(measurements, baby, growthAnalysis);
      }
    } catch (e) {
      throw Exception('Healthcare provider formatting failed: $e');
    }
  }

  /// Share growth data and reports
  static Future<void> shareGrowthData({
    required List<Measurement> measurements,
    required BabyProfile baby,
    required String shareType, // 'pdf', 'csv', 'medical_text'
    String? customFileName,
  }) async {
    try {
      final timestamp = DateFormat('yyyy-MM-dd_HH-mm').format(DateTime.now());
      final defaultFileName = '${baby.name}_growth_report_$timestamp';
      final fileName = customFileName ?? defaultFileName;

      switch (shareType) {
        case 'pdf':
          final pdfData = await generatePDFReport(
            measurements: measurements,
            baby: baby,
          );
          await Printing.sharePdf(
            bytes: pdfData,
            filename: '$fileName.pdf',
            subject: '${baby.name} - Growth Chart Report',
          );
          break;

        case 'csv':
          final csvData = await formatForHealthcareProvider(
            measurements: measurements,
            baby: baby,
            format: 'csv',
          );
          final tempDir = await getTemporaryDirectory();
          final file = File('${tempDir.path}/$fileName.csv');
          await file.writeAsString(csvData);
          await Printing.sharePdf(
            bytes: await file.readAsBytes(),
            filename: '$fileName.csv',
            subject: '${baby.name} - Growth Data (CSV)',
          );
          break;

        case 'medical_text':
          final medicalData = await formatForHealthcareProvider(
            measurements: measurements,
            baby: baby,
            format: 'medical_standard',
          );
          final tempDir = await getTemporaryDirectory();
          final file = File('${tempDir.path}/$fileName.txt');
          await file.writeAsString(medicalData);
          await Printing.sharePdf(
            bytes: await file.readAsBytes(),
            filename: '$fileName.txt',
            subject: '${baby.name} - Medical Growth Report',
          );
          break;

        default:
          throw Exception('Unsupported share type: $shareType');
      }
    } catch (e) {
      throw Exception('Sharing failed: $e');
    }
  }

  /// Validate export data for accuracy and completeness
  static Map<String, dynamic> validateExportData(
    List<Measurement> measurements,
    BabyProfile baby,
  ) {
    return _validateExportData(measurements, baby);
  }

  /// Internal validation method
  static Map<String, dynamic> _validateExportData(
    List<Measurement> measurements,
    BabyProfile baby,
  ) {
    final errors = <String>[];
    final warnings = <String>[];

    // Basic validation
    if (baby.name.isEmpty) {
      errors.add('Baby name is required');
    }

    if (baby.gender.isEmpty) {
      errors.add('Baby gender is required for WHO calculations');
    }

    if (measurements.isEmpty) {
      warnings.add('No measurements available for export');
    }

    // Measurement validation
    for (final measurement in measurements) {
      if (measurement.value <= 0) {
        errors.add('Invalid measurement value: ${measurement.value}');
      }

      if (measurement.ageInMonths < 0 || measurement.ageInMonths > 60) {
        warnings.add('Measurement age outside typical range: ${measurement.ageInMonths} months');
      }

      if (measurement.percentile == null) {
        warnings.add('Missing percentile calculation for measurement on ${DateFormat('yyyy-MM-dd').format(measurement.measuredAt)}');
      }
    }

    // Age validation
    final babyAge = baby.ageInMonths;
    if (babyAge < 0) {
      errors.add('Invalid baby age calculated');
    }

    if (babyAge > 60) {
      warnings.add('Baby age exceeds WHO chart range (60 months)');
    }

    return {
      'isValid': errors.isEmpty,
      'errors': errors,
      'warnings': warnings,
      'measurementCount': measurements.length,
      'babyAge': babyAge,
    };
  }

  /// Add cover page to PDF report
  static Future<void> _addCoverPage(
    pw.Document pdf,
    BabyProfile baby,
    GrowthAnalysis analysis,
    pw.Font regularFont,
    pw.Font boldFont,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        theme: pw.ThemeData.withFont(
          base: regularFont,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Header
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(20),
                decoration: pw.BoxDecoration(
                  color: PdfColors.blue50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Growth Chart Report',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 28,
                        color: PdfColors.blue800,
                      ),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      'WHO Growth Standards Analysis',
                      style: pw.TextStyle(
                        fontSize: 16,
                        color: PdfColors.blue600,
                      ),
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 30),

              // Baby Information
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey300),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Patient Information',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 18,
                        color: PdfColors.grey800,
                      ),
                    ),
                    pw.SizedBox(height: 12),
                    _buildInfoRow('Name:', baby.name, boldFont, regularFont),
                    _buildInfoRow('Gender:', baby.gender.toUpperCase(), boldFont, regularFont),
                    _buildInfoRow('Date of Birth:', DateFormat('MMMM dd, yyyy').format(baby.birthDate), boldFont, regularFont),
                    _buildInfoRow('Age:', '${baby.ageInMonths} months (${baby.ageInDays} days)', boldFont, regularFont),
                    if (baby.birthWeight != null)
                      _buildInfoRow('Birth Weight:', '${baby.birthWeight!.toStringAsFixed(2)} kg', boldFont, regularFont),
                    if (baby.birthHeight != null)
                      _buildInfoRow('Birth Height:', '${baby.birthHeight!.toStringAsFixed(1)} cm', boldFont, regularFont),
                  ],
                ),
              ),

              pw.SizedBox(height: 30),

              // Report Summary
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: _getAssessmentColor(analysis.overallAssessment),
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Overall Assessment',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 18,
                        color: PdfColors.white,
                      ),
                    ),
                    pw.SizedBox(height: 8),
                    pw.Text(
                      analysis.overallAssessment,
                      style: pw.TextStyle(
                        fontSize: 16,
                        color: PdfColors.white,
                      ),
                    ),
                  ],
                ),
              ),

              pw.Spacer(),

              // Report metadata
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(12),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(4),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Report Information',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 12,
                        color: PdfColors.grey700,
                      ),
                    ),
                    pw.SizedBox(height: 4),
                    pw.Text(
                      'Generated: ${DateFormat('MMMM dd, yyyy \'at\' HH:mm').format(DateTime.now())}',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                    pw.Text(
                      'Application: $_appName v$_reportVersion',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                    pw.Text(
                      'Standards: WHO Child Growth Standards',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Add summary page with key metrics
  static Future<void> _addSummaryPage(
    pw.Document pdf,
    BabyProfile baby,
    GrowthAnalysis analysis,
    List<Measurement> measurements,
    pw.Font regularFont,
    pw.Font boldFont,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        theme: pw.ThemeData.withFont(
          base: regularFont,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Page header
              pw.Text(
                'Growth Summary',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 24,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 20),

              // Current measurements summary
              if (measurements.isNotEmpty) ...[
                pw.Text(
                  'Current Measurements',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.grey800,
                  ),
                ),
                pw.SizedBox(height: 12),
                _buildCurrentMeasurementsTable(measurements, baby, regularFont, boldFont),
                pw.SizedBox(height: 20),
              ],

              // Growth summary text
              pw.Text(
                'Growth Analysis',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 18,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 12),
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(16),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey50,
                  borderRadius: pw.BorderRadius.circular(8),
                ),
                child: pw.Text(
                  analysis.growthSummary,
                  style: const pw.TextStyle(fontSize: 14, lineSpacing: 1.4),
                ),
              ),

              pw.SizedBox(height: 20),

              // Alerts summary
              if (analysis.alerts.isNotEmpty) ...[
                pw.Text(
                  'Alerts and Recommendations',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.grey800,
                  ),
                ),
                pw.SizedBox(height: 12),
                ...analysis.alerts.take(5).map((alert) => _buildAlertItem(alert, regularFont, boldFont)),
              ],

              pw.Spacer(),

              // Recommendations
              if (analysis.recommendations.isNotEmpty) ...[
                pw.Text(
                  'Recommendations',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 16,
                    color: PdfColors.grey800,
                  ),
                ),
                pw.SizedBox(height: 8),
                ...analysis.recommendations.take(6).map((rec) => pw.Padding(
                  padding: const pw.EdgeInsets.only(bottom: 4),
                  child: pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text('• ', style: pw.TextStyle(font: boldFont)),
                      pw.Expanded(child: pw.Text(rec, style: const pw.TextStyle(fontSize: 12))),
                    ],
                  ),
                )),
              ],
            ],
          );
        },
      ),
    );
  }

  /// Add detailed analysis page
  static Future<void> _addAnalysisPage(
    pw.Document pdf,
    GrowthAnalysis analysis,
    pw.Font regularFont,
    pw.Font boldFont,
    pw.Font monoFont,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        theme: pw.ThemeData.withFont(
          base: regularFont,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Page header
              pw.Text(
                'Detailed Growth Analysis',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 24,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 20),

              // Percentile trends
              if (analysis.trends.isNotEmpty) ...[
                pw.Text(
                  'Percentile Trends',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.grey800,
                  ),
                ),
                pw.SizedBox(height: 12),
                _buildPercentileTrendsTable(analysis.trends, regularFont, boldFont, monoFont),
                pw.SizedBox(height: 20),
              ],

              // Growth velocity analysis
              if (analysis.velocityAnalysis != null) ...[
                pw.Text(
                  'Growth Velocity Analysis',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.grey800,
                  ),
                ),
                pw.SizedBox(height: 12),
                _buildVelocityAnalysisSection(analysis.velocityAnalysis!, regularFont, boldFont),
                pw.SizedBox(height: 20),
              ],

              // Percentile crossing analysis
              pw.Text(
                'Percentile Crossing Analysis',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 18,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 12),
              _buildPercentileCrossingSection(analysis.percentileCrossingAnalysis, regularFont, boldFont),

              pw.Spacer(),

              // Analysis metadata
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(12),
                decoration: pw.BoxDecoration(
                  color: PdfColors.grey100,
                  borderRadius: pw.BorderRadius.circular(4),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Analysis Information',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 12,
                        color: PdfColors.grey700,
                      ),
                    ),
                    pw.SizedBox(height: 4),
                    pw.Text(
                      'Analysis Date: ${DateFormat('MMMM dd, yyyy \'at\' HH:mm').format(analysis.analyzedAt)}',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                    pw.Text(
                      'Calculation Method: WHO LMS (Lambda-Mu-Sigma) Method',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                    pw.Text(
                      'Reference: WHO Child Growth Standards (2006)',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.grey600),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Add charts pages (placeholder for future chart rendering)
  static Future<void> _addChartsPages(
    pw.Document pdf,
    List<Measurement> measurements,
    BabyProfile baby,
    pw.Font regularFont,
    pw.Font boldFont,
  ) async {
    // Group measurements by type
    final measurementsByType = <String, List<Measurement>>{};
    for (final measurement in measurements) {
      measurementsByType.putIfAbsent(measurement.measurementType, () => []).add(measurement);
    }

    for (final entry in measurementsByType.entries) {
      final measurementType = entry.key;
      final typeMeasurements = entry.value;
      
      pdf.addPage(
        pw.Page(
          pageFormat: PdfPageFormat.a4,
          theme: pw.ThemeData.withFont(
            base: regularFont,
            bold: boldFont,
          ),
          build: (pw.Context context) {
            return pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                // Page header
                pw.Text(
                  '${_formatMeasurementType(measurementType)} Growth Chart',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 24,
                    color: PdfColors.blue800,
                  ),
                ),
                pw.SizedBox(height: 20),

                // Chart placeholder (future implementation)
                pw.Container(
                  width: double.infinity,
                  height: 300,
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey300),
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Center(
                    child: pw.Column(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Text(
                          'Growth Chart',
                          style: pw.TextStyle(
                            font: boldFont,
                            fontSize: 18,
                            color: PdfColors.grey600,
                          ),
                        ),
                        pw.SizedBox(height: 8),
                        pw.Text(
                          '${typeMeasurements.length} measurements plotted',
                          style: const pw.TextStyle(
                            fontSize: 14,
                            color: PdfColors.grey500,
                          ),
                        ),
                        pw.SizedBox(height: 8),
                        pw.Text(
                          'Chart rendering will be implemented in future version',
                          style: const pw.TextStyle(
                            fontSize: 10,
                            color: PdfColors.grey400,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                pw.SizedBox(height: 20),

                // Measurement data table for this type
                pw.Text(
                  'Measurement Data',
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 18,
                    color: PdfColors.grey800,
                  ),
                ),
                pw.SizedBox(height: 12),
                _buildMeasurementDataTable(typeMeasurements, regularFont, boldFont),
              ],
            );
          },
        ),
      );
    }
  }

  /// Add data tables page
  static Future<void> _addDataTablesPage(
    pw.Document pdf,
    List<Measurement> measurements,
    BabyProfile baby,
    pw.Font regularFont,
    pw.Font boldFont,
    pw.Font monoFont,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        theme: pw.ThemeData.withFont(
          base: regularFont,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Page header
              pw.Text(
                'Complete Measurement Data',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 24,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 20),

              // All measurements table
              if (measurements.isNotEmpty) ...[
                _buildCompleteMeasurementsTable(measurements, regularFont, boldFont, monoFont),
              ] else ...[
                pw.Container(
                  width: double.infinity,
                  padding: const pw.EdgeInsets.all(20),
                  decoration: pw.BoxDecoration(
                    color: PdfColors.grey100,
                    borderRadius: pw.BorderRadius.circular(8),
                  ),
                  child: pw.Center(
                    child: pw.Text(
                      'No measurement data available',
                      style: pw.TextStyle(
                        fontSize: 16,
                        color: PdfColors.grey600,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  /// Add appendix page with technical information
  static Future<void> _addAppendixPage(
    pw.Document pdf,
    BabyProfile baby,
    GrowthAnalysis analysis,
    pw.Font regularFont,
    pw.Font boldFont,
    pw.Font monoFont,
  ) async {
    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        theme: pw.ThemeData.withFont(
          base: regularFont,
          bold: boldFont,
        ),
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // Page header
              pw.Text(
                'Technical Appendix',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 24,
                  color: PdfColors.blue800,
                ),
              ),
              pw.SizedBox(height: 20),

              // WHO Standards information
              pw.Text(
                'WHO Child Growth Standards',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 18,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 12),
              pw.Text(
                'This report uses the World Health Organization (WHO) Child Growth Standards, which describe the growth of healthy children living under optimal conditions. The standards are based on data from the WHO Multicentre Growth Reference Study (MGRS) conducted between 1997 and 2003.',
                style: const pw.TextStyle(fontSize: 12, lineSpacing: 1.4),
              ),
              pw.SizedBox(height: 16),

              // Percentile explanation
              pw.Text(
                'Understanding Percentiles',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 16,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                'Percentiles indicate what percentage of children of the same age and gender have measurements below your child\'s measurement. For example, if your child is at the 75th percentile for weight, it means 75% of children the same age weigh less.',
                style: const pw.TextStyle(fontSize: 12, lineSpacing: 1.4),
              ),
              pw.SizedBox(height: 16),

              // Calculation methods
              pw.Text(
                'Calculation Methods',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 16,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              pw.Text(
                'Percentiles are calculated using the LMS (Lambda-Mu-Sigma) method, which accounts for skewness in the distribution of measurements. Z-scores are calculated to provide standardized measures of how far a measurement deviates from the median.',
                style: const pw.TextStyle(fontSize: 12, lineSpacing: 1.4),
              ),
              pw.SizedBox(height: 16),

              // Alert thresholds
              pw.Text(
                'Alert Thresholds',
                style: pw.TextStyle(
                  font: boldFont,
                  fontSize: 16,
                  color: PdfColors.grey800,
                ),
              ),
              pw.SizedBox(height: 8),
              _buildAlertThresholdsTable(regularFont, boldFont),

              pw.Spacer(),

              // Disclaimer
              pw.Container(
                width: double.infinity,
                padding: const pw.EdgeInsets.all(12),
                decoration: pw.BoxDecoration(
                  color: PdfColors.amber50,
                  border: pw.Border.all(color: PdfColors.amber200),
                  borderRadius: pw.BorderRadius.circular(4),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'Medical Disclaimer',
                      style: pw.TextStyle(
                        font: boldFont,
                        fontSize: 12,
                        color: PdfColors.amber800,
                      ),
                    ),
                    pw.SizedBox(height: 4),
                    pw.Text(
                      'This report is for informational purposes only and should not replace professional medical advice. Always consult with your pediatrician or healthcare provider for medical concerns about your child\'s growth and development.',
                      style: const pw.TextStyle(fontSize: 10, color: PdfColors.amber700, lineSpacing: 1.3),
                    ),
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  /// Generate medical standard format text
  static String _generateMedicalStandardFormat(
    List<Measurement> measurements,
    BabyProfile baby,
    GrowthAnalysis analysis,
  ) {
    final buffer = StringBuffer();
    final dateFormat = DateFormat('yyyy-MM-dd');
    final timeFormat = DateFormat('HH:mm');

    // Header
    buffer.writeln('GROWTH CHART REPORT - MEDICAL FORMAT');
    buffer.writeln('=' * 50);
    buffer.writeln();

    // Patient information
    buffer.writeln('PATIENT INFORMATION:');
    buffer.writeln('Name: ${baby.name}');
    buffer.writeln('Gender: ${baby.gender.toUpperCase()}');
    buffer.writeln('Date of Birth: ${dateFormat.format(baby.birthDate)}');
    buffer.writeln('Age: ${baby.ageInMonths} months (${baby.ageInDays} days)');
    if (baby.birthWeight != null) {
      buffer.writeln('Birth Weight: ${baby.birthWeight!.toStringAsFixed(2)} kg');
    }
    if (baby.birthHeight != null) {
      buffer.writeln('Birth Height: ${baby.birthHeight!.toStringAsFixed(1)} cm');
    }
    buffer.writeln();

    // Overall assessment
    buffer.writeln('OVERALL ASSESSMENT:');
    buffer.writeln(analysis.overallAssessment);
    buffer.writeln();

    // Growth summary
    buffer.writeln('GROWTH SUMMARY:');
    buffer.writeln(analysis.growthSummary);
    buffer.writeln();

    // Current measurements
    if (measurements.isNotEmpty) {
      buffer.writeln('CURRENT MEASUREMENTS:');
      final currentMeasurements = <String, Measurement>{};
      for (final measurement in measurements) {
        final existing = currentMeasurements[measurement.measurementType];
        if (existing == null || measurement.measuredAt.isAfter(existing.measuredAt)) {
          currentMeasurements[measurement.measurementType] = measurement;
        }
      }

      for (final measurement in currentMeasurements.values) {
        buffer.writeln('${_formatMeasurementType(measurement.measurementType)}:');
        buffer.writeln('  Value: ${measurement.displayValue}');
        buffer.writeln('  Percentile: ${measurement.percentileDisplay}');
        if (measurement.zScore != null) {
          buffer.writeln('  Z-Score: ${measurement.zScore!.toStringAsFixed(2)}');
        }
        buffer.writeln('  Date: ${dateFormat.format(measurement.measuredAt)}');
        buffer.writeln();
      }
    }

    // Alerts
    if (analysis.alerts.isNotEmpty) {
      buffer.writeln('ALERTS AND CONCERNS:');
      for (int i = 0; i < analysis.alerts.length; i++) {
        final alert = analysis.alerts[i];
        buffer.writeln('${i + 1}. ${alert.title} (${alert.severity.toUpperCase()})');
        buffer.writeln('   ${alert.description}');
        if (alert.recommendations.isNotEmpty) {
          buffer.writeln('   Recommendations:');
          for (final rec in alert.recommendations) {
            buffer.writeln('   - $rec');
          }
        }
        buffer.writeln();
      }
    }

    // Recommendations
    if (analysis.recommendations.isNotEmpty) {
      buffer.writeln('RECOMMENDATIONS:');
      for (int i = 0; i < analysis.recommendations.length; i++) {
        buffer.writeln('${i + 1}. ${analysis.recommendations[i]}');
      }
      buffer.writeln();
    }

    // Report metadata
    buffer.writeln('REPORT INFORMATION:');
    buffer.writeln('Generated: ${dateFormat.format(DateTime.now())} at ${timeFormat.format(DateTime.now())}');
    buffer.writeln('Application: $_appName v$_reportVersion');
    buffer.writeln('Standards: WHO Child Growth Standards (2006)');
    buffer.writeln('Calculation Method: LMS (Lambda-Mu-Sigma)');

    return buffer.toString();
  }

  /// Generate CSV format
  static String _generateCSVFormat(List<Measurement> measurements, BabyProfile baby) {
    final buffer = StringBuffer();
    
    // CSV Header
    buffer.writeln('Date,Age_Months,Measurement_Type,Value,Unit,Percentile,Z_Score,Notes');
    
    // Sort measurements by date
    final sortedMeasurements = List<Measurement>.from(measurements)
      ..sort((a, b) => a.measuredAt.compareTo(b.measuredAt));
    
    // CSV Data
    for (final measurement in sortedMeasurements) {
      final date = DateFormat('yyyy-MM-dd').format(measurement.measuredAt);
      final ageMonths = measurement.ageInMonths.toStringAsFixed(2);
      final measurementType = measurement.measurementType;
      final value = measurement.value.toString();
      final unit = measurement.unit;
      final percentile = measurement.percentile?.toStringAsFixed(1) ?? '';
      final zScore = measurement.zScore?.toStringAsFixed(2) ?? '';
      final notes = (measurement.notes ?? '').replaceAll(',', ';').replaceAll('\n', ' ');
      
      buffer.writeln('$date,$ageMonths,$measurementType,$value,$unit,$percentile,$zScore,"$notes"');
    }
    
    return buffer.toString();
  }

  /// Generate JSON format
  static String _generateJSONFormat(
    List<Measurement> measurements,
    BabyProfile baby,
    GrowthAnalysis analysis,
  ) {
    final data = {
      'baby': {
        'name': baby.name,
        'gender': baby.gender,
        'birthDate': baby.birthDate.toIso8601String(),
        'ageInMonths': baby.ageInMonths,
        'ageInDays': baby.ageInDays,
        'birthWeight': baby.birthWeight,
        'birthHeight': baby.birthHeight,
      },
      'measurements': measurements.map((m) => {
        'id': m.id,
        'date': m.measuredAt.toIso8601String(),
        'ageInMonths': m.ageInMonths,
        'measurementType': m.measurementType,
        'value': m.value,
        'unit': m.unit,
        'percentile': m.percentile,
        'zScore': m.zScore,
        'notes': m.notes,
        'flaggedForReview': m.flaggedForReview,
      }).toList(),
      'analysis': {
        'overallAssessment': analysis.overallAssessment,
        'growthSummary': analysis.growthSummary,
        'analyzedAt': analysis.analyzedAt.toIso8601String(),
        'alerts': analysis.alerts.map((a) => {
          'type': a.type.name,
          'title': a.title,
          'description': a.description,
          'severity': a.severity,
          'recommendations': a.recommendations,
        }).toList(),
        'recommendations': analysis.recommendations,
      },
      'reportMetadata': {
        'generatedAt': DateTime.now().toIso8601String(),
        'application': _appName,
        'version': _reportVersion,
        'standards': 'WHO Child Growth Standards (2006)',
      },
    };
    
    return const JsonEncoder.withIndent('  ').convert(data);
  }

  // Helper methods for PDF building

  /// Build info row for patient information
  static pw.Widget _buildInfoRow(String label, String value, pw.Font boldFont, pw.Font regularFont) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 6),
      child: pw.Row(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.SizedBox(
            width: 120,
            child: pw.Text(
              label,
              style: pw.TextStyle(
                font: boldFont,
                fontSize: 12,
                color: PdfColors.grey700,
              ),
            ),
          ),
          pw.Expanded(
            child: pw.Text(
              value,
              style: const pw.TextStyle(fontSize: 12),
            ),
          ),
        ],
      ),
    );
  }

  /// Get assessment color based on severity
  static PdfColor _getAssessmentColor(String assessment) {
    if (assessment.toLowerCase().contains('critical')) {
      return PdfColors.red600;
    } else if (assessment.toLowerCase().contains('concerning')) {
      return PdfColors.orange600;
    } else if (assessment.toLowerCase().contains('monitor')) {
      return PdfColors.amber600;
    } else {
      return PdfColors.green600;
    }
  }

  /// Build current measurements table
  static pw.Widget _buildCurrentMeasurementsTable(
    List<Measurement> measurements,
    BabyProfile baby,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    // Get most recent measurement for each type
    final currentMeasurements = <String, Measurement>{};
    for (final measurement in measurements) {
      final existing = currentMeasurements[measurement.measurementType];
      if (existing == null || measurement.measuredAt.isAfter(existing.measuredAt)) {
        currentMeasurements[measurement.measurementType] = measurement;
      }
    }

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(2),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(2),
        3: const pw.FlexColumnWidth(1.5),
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Measurement', boldFont, isHeader: true),
            _buildTableCell('Value', boldFont, isHeader: true),
            _buildTableCell('Percentile', boldFont, isHeader: true),
            _buildTableCell('Date', boldFont, isHeader: true),
          ],
        ),
        // Data rows
        ...currentMeasurements.values.map((measurement) => pw.TableRow(
          children: [
            _buildTableCell(_formatMeasurementType(measurement.measurementType), regularFont),
            _buildTableCell(measurement.displayValue, regularFont),
            _buildTableCell(
              measurement.percentile != null 
                ? '${measurement.percentile!.toStringAsFixed(1)}th'
                : 'N/A',
              regularFont,
              color: measurement.requiresAttention ? PdfColors.red600 : null,
            ),
            _buildTableCell(
              DateFormat('MM/dd/yyyy').format(measurement.measuredAt),
              regularFont,
            ),
          ],
        )),
      ],
    );
  }

  /// Build alert item
  static pw.Widget _buildAlertItem(GrowthAlert alert, pw.Font regularFont, pw.Font boldFont) {
    return pw.Container(
      margin: const pw.EdgeInsets.only(bottom: 12),
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: _getAlertColor(alert.severity),
        borderRadius: pw.BorderRadius.circular(6),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Row(
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: pw.BoxDecoration(
                  color: PdfColors.white,
                  borderRadius: pw.BorderRadius.circular(12),
                ),
                child: pw.Text(
                  alert.severity.toUpperCase(),
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 8,
                    color: _getAlertTextColor(alert.severity),
                  ),
                ),
              ),
              pw.SizedBox(width: 8),
              pw.Expanded(
                child: pw.Text(
                  alert.title,
                  style: pw.TextStyle(
                    font: boldFont,
                    fontSize: 12,
                    color: PdfColors.white,
                  ),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 6),
          pw.Text(
            alert.description,
            style: const pw.TextStyle(
              fontSize: 11,
              color: PdfColors.white,
              lineSpacing: 1.3,
            ),
          ),
        ],
      ),
    );
  }

  /// Build percentile trends table
  static pw.Widget _buildPercentileTrendsTable(
    List<PercentileTrend> trends,
    pw.Font regularFont,
    pw.Font boldFont,
    pw.Font monoFont,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.5),
        1: const pw.FlexColumnWidth(1.5),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1),
        4: const pw.FlexColumnWidth(1.5),
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Date', boldFont, isHeader: true),
            _buildTableCell('Type', boldFont, isHeader: true),
            _buildTableCell('Value', boldFont, isHeader: true),
            _buildTableCell('Percentile', boldFont, isHeader: true),
            _buildTableCell('Z-Score', boldFont, isHeader: true),
          ],
        ),
        // Data rows
        ...trends.take(10).map((trend) => pw.TableRow(
          children: [
            _buildTableCell(
              DateFormat('MM/dd/yy').format(trend.date),
              regularFont,
            ),
            _buildTableCell(
              _formatMeasurementType(trend.measurementType),
              regularFont,
            ),
            _buildTableCell(
              '${trend.value.toStringAsFixed(1)} ${_getUnitForMeasurementType(trend.measurementType)}',
              regularFont,
            ),
            _buildTableCell(
              '${trend.percentile.toStringAsFixed(1)}th',
              monoFont,
              color: trend.percentile < 10 || trend.percentile > 90 
                ? PdfColors.orange600 
                : null,
            ),
            _buildTableCell(
              trend.zScore?.toStringAsFixed(2) ?? 'N/A',
              monoFont,
            ),
          ],
        )),
      ],
    );
  }

  /// Build velocity analysis section
  static pw.Widget _buildVelocityAnalysisSection(
    GrowthVelocityAnalysis velocityAnalysis,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColors.grey50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Growth Velocity: ${velocityAnalysis.velocityCategory}',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 14,
              color: velocityAnalysis.requiresAttention ? PdfColors.orange600 : PdfColors.green600,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            'Velocity: ${velocityAnalysis.velocityPerMonth.toStringAsFixed(2)} units/month',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            'Velocity Percentile: ${velocityAnalysis.velocityPercentile.toStringAsFixed(1)}th',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.Text(
            'Time Period: ${velocityAnalysis.timePeriod.inDays} days',
            style: const pw.TextStyle(fontSize: 12),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            velocityAnalysis.interpretation,
            style: const pw.TextStyle(fontSize: 12, lineSpacing: 1.3),
          ),
        ],
      ),
    );
  }

  /// Build percentile crossing section
  static pw.Widget _buildPercentileCrossingSection(
    Map<String, dynamic> crossingAnalysis,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    final requiresAttention = crossingAnalysis['requiresAttention'] ?? false;
    final crossingDirection = crossingAnalysis['crossingDirection'] ?? 'stable';
    final totalChange = crossingAnalysis['totalChange'] ?? 0.0;
    final crossingMagnitude = crossingAnalysis['crossingMagnitude'] ?? 0.0;

    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: requiresAttention ? PdfColors.orange50 : PdfColors.green50,
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'Percentile Crossing: ${crossingDirection.toUpperCase()}',
            style: pw.TextStyle(
              font: boldFont,
              fontSize: 14,
              color: requiresAttention ? PdfColors.orange600 : PdfColors.green600,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            'Total Change: ${totalChange.toStringAsFixed(1)} percentile points',
            style: const pw.TextStyle(fontSize: 12),
          ),
          if (crossingMagnitude > 0) ...[
            pw.Text(
              'Crossing Magnitude: ${crossingMagnitude.toStringAsFixed(1)} percentiles',
              style: const pw.TextStyle(fontSize: 12),
            ),
          ],
          pw.SizedBox(height: 8),
          pw.Text(
            requiresAttention
                ? 'Significant percentile crossing detected. Monitor closely and consider medical consultation.'
                : 'Growth is tracking consistently along percentile curves.',
            style: const pw.TextStyle(fontSize: 12, lineSpacing: 1.3),
          ),
        ],
      ),
    );
  }

  /// Build measurement data table for specific type
  static pw.Widget _buildMeasurementDataTable(
    List<Measurement> measurements,
    pw.Font regularFont,
    pw.Font boldFont,
  ) {
    final sortedMeasurements = List<Measurement>.from(measurements)
      ..sort((a, b) => b.measuredAt.compareTo(a.measuredAt));

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.5),
        1: const pw.FlexColumnWidth(1),
        2: const pw.FlexColumnWidth(1.5),
        3: const pw.FlexColumnWidth(1),
        4: const pw.FlexColumnWidth(1),
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Date', boldFont, isHeader: true),
            _buildTableCell('Age', boldFont, isHeader: true),
            _buildTableCell('Value', boldFont, isHeader: true),
            _buildTableCell('Percentile', boldFont, isHeader: true),
            _buildTableCell('Z-Score', boldFont, isHeader: true),
          ],
        ),
        // Data rows
        ...sortedMeasurements.take(15).map((measurement) => pw.TableRow(
          children: [
            _buildTableCell(
              DateFormat('MM/dd/yyyy').format(measurement.measuredAt),
              regularFont,
            ),
            _buildTableCell(
              '${measurement.ageInMonths.toStringAsFixed(1)}m',
              regularFont,
            ),
            _buildTableCell(
              measurement.displayValue,
              regularFont,
            ),
            _buildTableCell(
              measurement.percentile != null 
                ? '${measurement.percentile!.toStringAsFixed(1)}th'
                : 'N/A',
              regularFont,
              color: measurement.requiresAttention ? PdfColors.red600 : null,
            ),
            _buildTableCell(
              measurement.zScore?.toStringAsFixed(2) ?? 'N/A',
              regularFont,
            ),
          ],
        )),
      ],
    );
  }

  /// Build complete measurements table
  static pw.Widget _buildCompleteMeasurementsTable(
    List<Measurement> measurements,
    pw.Font regularFont,
    pw.Font boldFont,
    pw.Font monoFont,
  ) {
    final sortedMeasurements = List<Measurement>.from(measurements)
      ..sort((a, b) => b.measuredAt.compareTo(a.measuredAt));

    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.2),
        1: const pw.FlexColumnWidth(1.5),
        2: const pw.FlexColumnWidth(0.8),
        3: const pw.FlexColumnWidth(1.2),
        4: const pw.FlexColumnWidth(1),
        5: const pw.FlexColumnWidth(1),
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Date', boldFont, isHeader: true),
            _buildTableCell('Type', boldFont, isHeader: true),
            _buildTableCell('Age', boldFont, isHeader: true),
            _buildTableCell('Value', boldFont, isHeader: true),
            _buildTableCell('Percentile', boldFont, isHeader: true),
            _buildTableCell('Z-Score', boldFont, isHeader: true),
          ],
        ),
        // Data rows
        ...sortedMeasurements.take(20).map((measurement) => pw.TableRow(
          children: [
            _buildTableCell(
              DateFormat('MM/dd/yy').format(measurement.measuredAt),
              regularFont,
            ),
            _buildTableCell(
              _formatMeasurementType(measurement.measurementType),
              regularFont,
            ),
            _buildTableCell(
              '${measurement.ageInMonths.toStringAsFixed(1)}m',
              regularFont,
            ),
            _buildTableCell(
              measurement.displayValue,
              regularFont,
            ),
            _buildTableCell(
              measurement.percentile != null 
                ? '${measurement.percentile!.toStringAsFixed(1)}th'
                : 'N/A',
              monoFont,
              color: measurement.requiresAttention ? PdfColors.red600 : null,
            ),
            _buildTableCell(
              measurement.zScore?.toStringAsFixed(2) ?? 'N/A',
              monoFont,
            ),
          ],
        )),
      ],
    );
  }

  /// Build alert thresholds table
  static pw.Widget _buildAlertThresholdsTable(pw.Font regularFont, pw.Font boldFont) {
    return pw.Table(
      border: pw.TableBorder.all(color: PdfColors.grey300),
      columnWidths: {
        0: const pw.FlexColumnWidth(1),
        1: const pw.FlexColumnWidth(2),
        2: const pw.FlexColumnWidth(2),
      },
      children: [
        // Header
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey100),
          children: [
            _buildTableCell('Severity', boldFont, isHeader: true),
            _buildTableCell('Threshold', boldFont, isHeader: true),
            _buildTableCell('Action', boldFont, isHeader: true),
          ],
        ),
        // Data rows
        pw.TableRow(
          children: [
            _buildTableCell('Critical', regularFont, color: PdfColors.red600),
            _buildTableCell('Percentile crossing >50 points', regularFont),
            _buildTableCell('Immediate medical consultation', regularFont),
          ],
        ),
        pw.TableRow(
          children: [
            _buildTableCell('High', regularFont, color: PdfColors.orange600),
            _buildTableCell('<3rd or >97th percentile, crossing >25 points', regularFont),
            _buildTableCell('Medical consultation within 1-2 weeks', regularFont),
          ],
        ),
        pw.TableRow(
          children: [
            _buildTableCell('Medium', regularFont, color: PdfColors.amber600),
            _buildTableCell('<10th or >90th percentile, crossing >10 points', regularFont),
            _buildTableCell('Monitor closely, discuss at next visit', regularFont),
          ],
        ),
        pw.TableRow(
          children: [
            _buildTableCell('Low', regularFont, color: PdfColors.green600),
            _buildTableCell('Minor variations, measurement gaps', regularFont),
            _buildTableCell('Continue regular monitoring', regularFont),
          ],
        ),
      ],
    );
  }

  /// Build table cell with consistent styling
  static pw.Widget _buildTableCell(
    String text,
    pw.Font font, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: font,
          fontSize: isHeader ? 11 : 10,
          color: color ?? (isHeader ? PdfColors.grey800 : PdfColors.grey700),
        ),
      ),
    );
  }

  /// Get alert color based on severity
  static PdfColor _getAlertColor(String severity) {
    switch (severity) {
      case 'critical':
        return PdfColors.red600;
      case 'high':
        return PdfColors.orange600;
      case 'medium':
        return PdfColors.amber600;
      case 'low':
        return PdfColors.blue600;
      default:
        return PdfColors.grey600;
    }
  }

  /// Get alert text color based on severity
  static PdfColor _getAlertTextColor(String severity) {
    switch (severity) {
      case 'critical':
        return PdfColors.red600;
      case 'high':
        return PdfColors.orange600;
      case 'medium':
        return PdfColors.amber600;
      case 'low':
        return PdfColors.blue600;
      default:
        return PdfColors.grey600;
    }
  }

  /// Format measurement type for display
  static String _formatMeasurementType(String type) {
    switch (type) {
      case 'weight':
        return 'Weight';
      case 'height':
      case 'length':
        return 'Height';
      case 'head_circumference':
        return 'Head Circumference';
      default:
        return type.split('_').map((word) => 
          word[0].toUpperCase() + word.substring(1)
        ).join(' ');
    }
  }

  /// Get unit for measurement type
  static String _getUnitForMeasurementType(String type) {
    switch (type) {
      case 'weight':
        return 'kg';
      case 'height':
      case 'length':
      case 'head_circumference':
        return 'cm';
      default:
        return '';
    }
  }
}