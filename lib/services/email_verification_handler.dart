import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import '../core/global_navigator.dart';

/// Handles email verification and deep linking for email changes
class EmailVerificationHandler {
  static const String _tag = 'EmailVerificationHandler';
  
  /// Initialize email verification handling
  static void initialize() {
    // Listen for auth state changes to detect email verification
    Supabase.instance.client.auth.onAuthStateChange.listen((data) {
      final event = data.event;
      final user = data.session?.user;
      
      debugPrint('$_tag: Auth state changed: $event');
      
      if (event == AuthChangeEvent.tokenRefreshed && user != null) {
        _handleEmailVerification(user);
      }
    });
  }
  
  /// Handle email verification completion
  static void _handleEmailVerification(User user) {
    debugPrint('$_tag: Checking email verification for user: ${user.email}');
    
    // Check if email was recently changed
    if (user.emailConfirmedAt != null) {
      debugPrint('$_tag: Email verified at: ${user.emailConfirmedAt}');
      
      // Update local user profile with new email
      _updateLocalUserProfile(user);
      
      // Show success notification
      _showEmailChangeSuccess(user.email!);
    }
  }
  
  /// Update local user profile after email change
  static Future<void> _updateLocalUserProfile(User user) async {
    try {
      final supabaseService = SupabaseService();
      
      // Update user_profiles table with new email
      await supabaseService.update(
        'user_profiles',
        {
          'email': user.email,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        user.id,
      );
      
      debugPrint('$_tag: Updated user profile with new email: ${user.email}');
    } catch (e) {
      debugPrint('$_tag: Error updating user profile: $e');
    }
  }
  
  /// Show success notification for email change
  static void _showEmailChangeSuccess(String newEmail) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Email successfully changed to $newEmail',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(16),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }
  
  /// Check for pending email changes on app startup
  static Future<void> checkPendingEmailChanges() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user?.newEmail != null) {
        debugPrint('$_tag: Found pending email change to: ${user!.newEmail}');
        
        // Show pending email change status
        _showPendingEmailChangeStatus(user.email!, user.newEmail!);
      }
    } catch (e) {
      debugPrint('$_tag: Error checking pending email changes: $e');
    }
  }
  
  /// Show pending email change status
  static void _showPendingEmailChangeStatus(String currentEmail, String newEmail) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.pending,
                    color: Colors.orange,
                    size: 20,
                  ),
                  SizedBox(width: 12),
                  Text(
                    'Email Change Pending',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 4),
              Text(
                'Please check $newEmail and click the verification link to complete the email change.',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(16),
          duration: Duration(seconds: 8),
        ),
      );
    }
  }
  
  /// Handle deep link from email verification
  static Future<void> handleEmailVerificationDeepLink(String url) async {
    try {
      debugPrint('$_tag: Handling email verification deep link: $url');
      
      // Parse the URL to extract verification parameters
      final uri = Uri.parse(url);
      final type = uri.queryParameters['type'];
      
      if (type == 'email_change') {
        // The email change verification was successful
        debugPrint('$_tag: Email change verification completed');
        
        // Refresh the current session to get updated user data
        await Supabase.instance.client.auth.refreshSession();
        
        // Show success message
        final context = navigatorKey.currentContext;
        if (context != null) {
          _showEmailChangeSuccess('your new email address');
        }
      }
    } catch (e) {
      debugPrint('$_tag: Error handling email verification deep link: $e');
    }
  }
}