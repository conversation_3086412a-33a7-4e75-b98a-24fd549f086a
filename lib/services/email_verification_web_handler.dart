import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import '../core/global_navigator.dart';
import 'supabase_service.dart';

/// Handles email verification completion for web-based verification
class EmailVerificationWebHandler {
  static const String _tag = 'EmailVerificationWebHandler';
  
  /// Initialize periodic checking for email verification completion
  static void startPeriodicCheck() {
    // Check every 5 seconds for email verification completion
    Timer.periodic(Duration(seconds: 5), (timer) async {
      await _checkEmailVerificationStatus();
    });
  }
  
  /// Check if email verification has been completed
  static Future<void> _checkEmailVerificationStatus() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;
      
      // Refresh the session to get the latest user data
      final response = await Supabase.instance.client.auth.refreshSession();
      final updatedUser = response.user;
      
      if (updatedUser != null) {
        // Check if the email has been updated (no longer pending)
        if (updatedUser.newEmail == null && updatedUser.emailConfirmedAt != null) {
          debugPrint('$_tag: Email verification completed successfully');
          
          // Update local user profile
          await _updateLocalUserProfile(updatedUser);
          
          // Show success notification
          _showEmailChangeSuccess(updatedUser.email!);
          
          // Stop checking
          return;
        }
      }
    } catch (e) {
      debugPrint('$_tag: Error checking email verification status: $e');
    }
  }
  
  /// Update local user profile after email change
  static Future<void> _updateLocalUserProfile(User user) async {
    try {
      final supabaseService = SupabaseService();
      
      // Update user_profiles table with new email
      await supabaseService.update(
        'user_profiles',
        {
          'email': user.email,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        user.id,
      );
      
      debugPrint('$_tag: Updated user profile with new email: ${user.email}');
    } catch (e) {
      debugPrint('$_tag: Error updating user profile: $e');
    }
  }
  
  /// Show success notification for email change
  static void _showEmailChangeSuccess(String newEmail) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Email successfully changed to $newEmail',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(16),
          duration: Duration(seconds: 5),
        ),
      );
      
      // Trigger a rebuild of the settings screen to show new email
      if (context.mounted) {
        // Find the settings screen and refresh it
        Navigator.of(context).popUntil((route) => route.isFirst);
      }
    }
  }
}