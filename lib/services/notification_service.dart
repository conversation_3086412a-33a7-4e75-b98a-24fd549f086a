import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import './unified_notification_service.dart';

/// Service to manage notification settings and state
class NotificationService extends ChangeNotifier {
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _feedingRemindersKey = 'feeding_reminders';
  static const String _sleepAlertsKey = 'sleep_alerts';
  static const String _milestoneNotificationsKey = 'milestone_notifications';
  static const String _aiInsightUpdatesKey = 'ai_insight_updates';
  static const String _dailySummaryKey = 'daily_summary';
  static const String _weeklyReportKey = 'weekly_report';
  static const String _quietHoursStartKey = 'quiet_hours_start';
  static const String _quietHoursEndKey = 'quiet_hours_end';

  static NotificationService? _instance;
  static NotificationService get instance => _instance ??= NotificationService._();
  
  NotificationService._();

  SharedPreferences? _prefs;
  bool _isInitialized = false;

  // Notification settings
  bool _notificationsEnabled = true;
  bool _feedingReminders = true;
  bool _sleepAlerts = true;
  bool _milestoneNotifications = true;
  bool _aiInsightUpdates = true;
  bool _dailySummary = false;
  bool _weeklyReport = true;
  TimeOfDay _quietHoursStart = TimeOfDay(hour: 22, minute: 0);
  TimeOfDay _quietHoursEnd = TimeOfDay(hour: 7, minute: 0);

  // Getters
  bool get isInitialized => _isInitialized;
  bool get notificationsEnabled => _notificationsEnabled;
  bool get feedingReminders => _feedingReminders;
  bool get sleepAlerts => _sleepAlerts;
  bool get milestoneNotifications => _milestoneNotifications;
  bool get aiInsightUpdates => _aiInsightUpdates;
  bool get dailySummary => _dailySummary;
  bool get weeklyReport => _weeklyReport;
  TimeOfDay get quietHoursStart => _quietHoursStart;
  TimeOfDay get quietHoursEnd => _quietHoursEnd;

  /// Initialize the notification service
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('NotificationService initialization error: $e');
      _isInitialized = true;
    }
  }

  /// Load notification settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      _notificationsEnabled = _prefs?.getBool(_notificationsEnabledKey) ?? true;
      _feedingReminders = _prefs?.getBool(_feedingRemindersKey) ?? true;
      _sleepAlerts = _prefs?.getBool(_sleepAlertsKey) ?? true;
      _milestoneNotifications = _prefs?.getBool(_milestoneNotificationsKey) ?? true;
      _aiInsightUpdates = _prefs?.getBool(_aiInsightUpdatesKey) ?? true;
      _dailySummary = _prefs?.getBool(_dailySummaryKey) ?? false;
      _weeklyReport = _prefs?.getBool(_weeklyReportKey) ?? true;
      
      // Load quiet hours
      final startHour = _prefs?.getInt('${_quietHoursStartKey}_hour') ?? 22;
      final startMinute = _prefs?.getInt('${_quietHoursStartKey}_minute') ?? 0;
      _quietHoursStart = TimeOfDay(hour: startHour, minute: startMinute);
      
      final endHour = _prefs?.getInt('${_quietHoursEndKey}_hour') ?? 7;
      final endMinute = _prefs?.getInt('${_quietHoursEndKey}_minute') ?? 0;
      _quietHoursEnd = TimeOfDay(hour: endHour, minute: endMinute);
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  /// Toggle master notification setting
  Future<void> toggleNotifications() async {
    _notificationsEnabled = !_notificationsEnabled;
    await _prefs?.setBool(_notificationsEnabledKey, _notificationsEnabled);
    
    // Sync with unified notification service
    await UnifiedNotificationService.instance.setNotificationsEnabled(_notificationsEnabled);
    
    notifyListeners();
    showToast(_notificationsEnabled ? 'Notifications enabled' : 'Notifications disabled');
  }

  void showToast(String message) {
    // This is a placeholder for the toast implementation
    debugPrint('Toast: $message');
  }

  /// Add toast notification on toggle
  void notify(String message) {
    showToast(message);
  }

  /// Set master notification setting
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled == enabled) return;
    
    _notificationsEnabled = enabled;
    await _prefs?.setBool(_notificationsEnabledKey, enabled);
    
    // Sync with unified notification service
    await UnifiedNotificationService.instance.setNotificationsEnabled(enabled);
    
    notifyListeners();
  }

  /// Set feeding reminders
  Future<void> setFeedingReminders(bool enabled) async {
    _feedingReminders = enabled;
    await _prefs?.setBool(_feedingRemindersKey, enabled);
    notifyListeners();
  }

  /// Set sleep alerts
  Future<void> setSleepAlerts(bool enabled) async {
    _sleepAlerts = enabled;
    await _prefs?.setBool(_sleepAlertsKey, enabled);
    notifyListeners();
  }

  /// Set milestone notifications
  Future<void> setMilestoneNotifications(bool enabled) async {
    _milestoneNotifications = enabled;
    await _prefs?.setBool(_milestoneNotificationsKey, enabled);
    notifyListeners();
  }

  /// Set AI insight updates
  Future<void> setAiInsightUpdates(bool enabled) async {
    _aiInsightUpdates = enabled;
    await _prefs?.setBool(_aiInsightUpdatesKey, enabled);
    notifyListeners();
  }

  /// Set daily summary
  Future<void> setDailySummary(bool enabled) async {
    _dailySummary = enabled;
    await _prefs?.setBool(_dailySummaryKey, enabled);
    notifyListeners();
  }

  /// Set weekly report
  Future<void> setWeeklyReport(bool enabled) async {
    _weeklyReport = enabled;
    await _prefs?.setBool(_weeklyReportKey, enabled);
    notifyListeners();
  }

  /// Set quiet hours start time
  Future<void> setQuietHoursStart(TimeOfDay time) async {
    _quietHoursStart = time;
    await _prefs?.setInt('${_quietHoursStartKey}_hour', time.hour);
    await _prefs?.setInt('${_quietHoursStartKey}_minute', time.minute);
    notifyListeners();
  }

  /// Set quiet hours end time
  Future<void> setQuietHoursEnd(TimeOfDay time) async {
    _quietHoursEnd = time;
    await _prefs?.setInt('${_quietHoursEndKey}_hour', time.hour);
    await _prefs?.setInt('${_quietHoursEndKey}_minute', time.minute);
    notifyListeners();
  }

  /// Update multiple notification settings at once
  Future<void> updateSettings(Map<String, dynamic> settings) async {
    bool hasChanges = false;

    if (settings.containsKey('feedingReminders')) {
      await setFeedingReminders(settings['feedingReminders']);
      hasChanges = true;
    }
    if (settings.containsKey('sleepAlerts')) {
      await setSleepAlerts(settings['sleepAlerts']);
      hasChanges = true;
    }
    if (settings.containsKey('milestoneNotifications')) {
      await setMilestoneNotifications(settings['milestoneNotifications']);
      hasChanges = true;
    }
    if (settings.containsKey('aiInsightUpdates')) {
      await setAiInsightUpdates(settings['aiInsightUpdates']);
      hasChanges = true;
    }
    if (settings.containsKey('dailySummary')) {
      await setDailySummary(settings['dailySummary']);
      hasChanges = true;
    }
    if (settings.containsKey('weeklyReport')) {
      await setWeeklyReport(settings['weeklyReport']);
      hasChanges = true;
    }
    if (settings.containsKey('quietHoursStart')) {
      await setQuietHoursStart(settings['quietHoursStart']);
      hasChanges = true;
    }
    if (settings.containsKey('quietHoursEnd')) {
      await setQuietHoursEnd(settings['quietHoursEnd']);
      hasChanges = true;
    }

    if (hasChanges) {
      notifyListeners();
    }
  }

  /// Get notification summary for display
  String get notificationSummary {
    if (!_notificationsEnabled) return 'Notifications disabled';
    
    int enabledCount = 0;
    if (_feedingReminders) enabledCount++;
    if (_sleepAlerts) enabledCount++;
    if (_milestoneNotifications) enabledCount++;
    if (_aiInsightUpdates) enabledCount++;
    if (_dailySummary) enabledCount++;
    if (_weeklyReport) enabledCount++;
    
    return '$enabledCount notifications enabled';
  }

  /// Check if currently in quiet hours
  bool get isInQuietHours {
    final now = TimeOfDay.now();
    final nowInMinutes = now.hour * 60 + now.minute;
    final startInMinutes = _quietHoursStart.hour * 60 + _quietHoursStart.minute;
    final endInMinutes = _quietHoursEnd.hour * 60 + _quietHoursEnd.minute;
    
    if (startInMinutes <= endInMinutes) {
      // Same day quiet hours (e.g., 22:00 - 23:00)
      return nowInMinutes >= startInMinutes && nowInMinutes <= endInMinutes;
    } else {
      // Overnight quiet hours (e.g., 22:00 - 07:00)
      return nowInMinutes >= startInMinutes || nowInMinutes <= endInMinutes;
    }
  }

  /// Get icon for notification state
  IconData get notificationIcon {
    if (!_notificationsEnabled) return Icons.notifications_off;
    if (isInQuietHours) return Icons.notifications_paused;
    return Icons.notifications;
  }
}
