import 'dart:math';

/// WHO Data Point containing LMS parameters for accurate percentile calculations
class WHODataPoint {
  final double age; // Age in months
  final double L; // Lambda (skewness parameter)
  final double M; // Mu (median)
  final double S; // Sigma (coefficient of variation)

  const WHODataPoint({
    required this.age,
    required this.L,
    required this.M,
    required this.S,
  });
}

/// Result of percentile calculation with additional metadata
class PercentileResult {
  final double percentile;
  final double zScore;
  final String interpretation;
  final bool requiresAttention;
  final String category; // "Normal", "Below Average", "Above Average", etc.

  const PercentileResult({
    required this.percentile,
    required this.zScore,
    required this.interpretation,
    required this.requiresAttention,
    required this.category,
  });
}

/// Growth velocity calculation result
class GrowthVelocity {
  final double velocityPerMonth;
  final double velocityPercentile;
  final String interpretation;
  final bool isNormal;
  final Duration timePeriod;

  const GrowthVelocity({
    required this.velocityPerMonth,
    required this.velocityPercentile,
    required this.interpretation,
    required this.isNormal,
    required this.timePeriod,
  });
}

/// Enhanced WHO Data Service with complete LMS parameters
class WHODataService {
  // Complete WHO LMS data for weight (kg) - Boys
  static const List<WHODataPoint> _weightBoysLMS = [
    WHODataPoint(age: 0, L: 0.3487, M: 3.3464, S: 0.14602),
    WHODataPoint(age: 1, L: 0.2581, M: 4.4709, S: 0.13395),
    WHODataPoint(age: 2, L: 0.1970, M: 5.5675, S: 0.12385),
    WHODataPoint(age: 3, L: 0.1738, M: 6.3762, S: 0.11727),
    WHODataPoint(age: 4, L: 0.1553, M: 7.0023, S: 0.11316),
    WHODataPoint(age: 5, L: 0.1395, M: 7.5105, S: 0.11080),
    WHODataPoint(age: 6, L: 0.1257, M: 7.9340, S: 0.10958),
    WHODataPoint(age: 7, L: 0.1134, M: 8.2970, S: 0.10902),
    WHODataPoint(age: 8, L: 0.1021, M: 8.6151, S: 0.10882),
    WHODataPoint(age: 9, L: 0.0917, M: 8.9014, S: 0.10881),
    WHODataPoint(age: 10, L: 0.0820, M: 9.1649, S: 0.10891),
    WHODataPoint(age: 11, L: 0.0730, M: 9.4122, S: 0.10906),
    WHODataPoint(age: 12, L: 0.0644, M: 9.6479, S: 0.10925),
    WHODataPoint(age: 13, L: 0.0563, M: 9.8749, S: 0.10949),
    WHODataPoint(age: 14, L: 0.0487, M: 10.0953, S: 0.10976),
    WHODataPoint(age: 15, L: 0.0413, M: 10.3108, S: 0.11007),
    WHODataPoint(age: 16, L: 0.0343, M: 10.5228, S: 0.11041),
    WHODataPoint(age: 17, L: 0.0275, M: 10.7319, S: 0.11078),
    WHODataPoint(age: 18, L: 0.0211, M: 10.9385, S: 0.11117),
    WHODataPoint(age: 19, L: 0.0148, M: 11.1430, S: 0.11159),
    WHODataPoint(age: 20, L: 0.0087, M: 11.3462, S: 0.11202),
    WHODataPoint(age: 21, L: 0.0029, M: 11.5486, S: 0.11247),
    WHODataPoint(age: 22, L: -0.0028, M: 11.7504, S: 0.11294),
    WHODataPoint(age: 23, L: -0.0083, M: 11.9514, S: 0.11342),
    WHODataPoint(age: 24, L: -0.0137, M: 12.1515, S: 0.11391),
    WHODataPoint(age: 30, L: -0.0328, M: 13.4230, S: 0.11881),
    WHODataPoint(age: 36, L: -0.0498, M: 14.6515, S: 0.12407),
    WHODataPoint(age: 42, L: -0.0641, M: 15.8398, S: 0.12968),
    WHODataPoint(age: 48, L: -0.0756, M: 16.9914, S: 0.13564),
    WHODataPoint(age: 54, L: -0.0844, M: 18.1098, S: 0.14194),
    WHODataPoint(age: 60, L: -0.0905, M: 19.1996, S: 0.14857),
  ];

  // Complete WHO LMS data for weight (kg) - Girls
  static const List<WHODataPoint> _weightGirlsLMS = [
    WHODataPoint(age: 0, L: 0.3809, M: 3.2322, S: 0.14171),
    WHODataPoint(age: 1, L: 0.1714, M: 4.1873, S: 0.13724),
    WHODataPoint(age: 2, L: 0.0962, M: 5.1282, S: 0.13000),
    WHODataPoint(age: 3, L: 0.0402, M: 5.8458, S: 0.12619),
    WHODataPoint(age: 4, L: -0.0050, M: 6.4237, S: 0.12402),
    WHODataPoint(age: 5, L: -0.0430, M: 6.8985, S: 0.12274),
    WHODataPoint(age: 6, L: -0.0756, M: 7.2970, S: 0.12204),
    WHODataPoint(age: 7, L: -0.1039, M: 7.6422, S: 0.12178),
    WHODataPoint(age: 8, L: -0.1288, M: 7.9487, S: 0.12181),
    WHODataPoint(age: 9, L: -0.1507, M: 8.2254, S: 0.12199),
    WHODataPoint(age: 10, L: -0.1700, M: 8.4800, S: 0.12223),
    WHODataPoint(age: 11, L: -0.1872, M: 8.7192, S: 0.12247),
    WHODataPoint(age: 12, L: -0.2024, M: 8.9481, S: 0.12268),
    WHODataPoint(age: 13, L: -0.2158, M: 9.1699, S: 0.12283),
    WHODataPoint(age: 14, L: -0.2278, M: 9.3868, S: 0.12294),
    WHODataPoint(age: 15, L: -0.2384, M: 9.6008, S: 0.12299),
    WHODataPoint(age: 16, L: -0.2478, M: 9.8124, S: 0.12303),
    WHODataPoint(age: 17, L: -0.2562, M: 10.0226, S: 0.12306),
    WHODataPoint(age: 18, L: -0.2637, M: 10.2315, S: 0.12309),
    WHODataPoint(age: 19, L: -0.2703, M: 10.4393, S: 0.12315),
    WHODataPoint(age: 20, L: -0.2762, M: 10.6464, S: 0.12323),
    WHODataPoint(age: 21, L: -0.2815, M: 10.8534, S: 0.12335),
    WHODataPoint(age: 22, L: -0.2862, M: 11.0608, S: 0.12350),
    WHODataPoint(age: 23, L: -0.2903, M: 11.2688, S: 0.12369),
    WHODataPoint(age: 24, L: -0.2941, M: 11.4775, S: 0.12390),
    WHODataPoint(age: 30, L: -0.3129, M: 13.0458, S: 0.12619),
    WHODataPoint(age: 36, L: -0.3266, M: 14.6509, S: 0.12955),
    WHODataPoint(age: 42, L: -0.3357, M: 16.2841, S: 0.13386),
    WHODataPoint(age: 48, L: -0.3405, M: 17.9421, S: 0.13902),
    WHODataPoint(age: 54, L: -0.3412, M: 19.6218, S: 0.14493),
    WHODataPoint(age: 60, L: -0.3382, M: 21.3199, S: 0.15153),
  ];

  // Complete WHO LMS data for height (cm) - Boys
  static const List<WHODataPoint> _heightBoysLMS = [
    WHODataPoint(age: 0, L: 1.0000, M: 49.8842, S: 0.03686),
    WHODataPoint(age: 1, L: 1.0000, M: 54.7244, S: 0.03293),
    WHODataPoint(age: 2, L: 1.0000, M: 58.4249, S: 0.03016),
    WHODataPoint(age: 3, L: 1.0000, M: 61.4292, S: 0.02806),
    WHODataPoint(age: 4, L: 1.0000, M: 63.8861, S: 0.02646),
    WHODataPoint(age: 5, L: 1.0000, M: 65.9026, S: 0.02523),
    WHODataPoint(age: 6, L: 1.0000, M: 67.6236, S: 0.02426),
    WHODataPoint(age: 7, L: 1.0000, M: 69.1645, S: 0.02348),
    WHODataPoint(age: 8, L: 1.0000, M: 70.5994, S: 0.02283),
    WHODataPoint(age: 9, L: 1.0000, M: 71.9687, S: 0.02227),
    WHODataPoint(age: 10, L: 1.0000, M: 73.2812, S: 0.02179),
    WHODataPoint(age: 11, L: 1.0000, M: 74.5388, S: 0.02137),
    WHODataPoint(age: 12, L: 1.0000, M: 75.7488, S: 0.02101),
    WHODataPoint(age: 15, L: 1.0000, M: 78.0497, S: 0.02020),
    WHODataPoint(age: 18, L: 1.0000, M: 80.7244, S: 0.01953),
    WHODataPoint(age: 21, L: 1.0000, M: 83.2912, S: 0.01897),
    WHODataPoint(age: 24, L: 1.0000, M: 87.7000, S: 0.01851),
    WHODataPoint(age: 30, L: 1.0000, M: 92.1000, S: 0.01770),
    WHODataPoint(age: 36, L: 1.0000, M: 96.1260, S: 0.01713),
    WHODataPoint(age: 42, L: 1.0000, M: 99.9000, S: 0.01672),
    WHODataPoint(age: 48, L: 1.0000, M: 103.5000, S: 0.01642),
    WHODataPoint(age: 54, L: 1.0000, M: 106.9000, S: 0.01621),
    WHODataPoint(age: 60, L: 1.0000, M: 110.2000, S: 0.01607),
  ];

  // Complete WHO LMS data for height (cm) - Girls
  static const List<WHODataPoint> _heightGirlsLMS = [
    WHODataPoint(age: 0, L: 1.0000, M: 49.1477, S: 0.03790),
    WHODataPoint(age: 1, L: 1.0000, M: 53.6872, S: 0.03368),
    WHODataPoint(age: 2, L: 1.0000, M: 57.0673, S: 0.03070),
    WHODataPoint(age: 3, L: 1.0000, M: 59.8029, S: 0.02849),
    WHODataPoint(age: 4, L: 1.0000, M: 62.0899, S: 0.02679),
    WHODataPoint(age: 5, L: 1.0000, M: 64.0301, S: 0.02547),
    WHODataPoint(age: 6, L: 1.0000, M: 65.7311, S: 0.02442),
    WHODataPoint(age: 7, L: 1.0000, M: 67.2873, S: 0.02356),
    WHODataPoint(age: 8, L: 1.0000, M: 68.7498, S: 0.02283),
    WHODataPoint(age: 9, L: 1.0000, M: 70.1435, S: 0.02220),
    WHODataPoint(age: 10, L: 1.0000, M: 71.4818, S: 0.02165),
    WHODataPoint(age: 11, L: 1.0000, M: 72.7718, S: 0.02117),
    WHODataPoint(age: 12, L: 1.0000, M: 74.0157, S: 0.02075),
    WHODataPoint(age: 15, L: 1.0000, M: 76.6436, S: 0.01981),
    WHODataPoint(age: 18, L: 1.0000, M: 79.1024, S: 0.01905),
    WHODataPoint(age: 21, L: 1.0000, M: 81.4488, S: 0.01842),
    WHODataPoint(age: 24, L: 1.0000, M: 83.5000, S: 0.01789),
    WHODataPoint(age: 30, L: 1.0000, M: 87.9841, S: 0.01698),
    WHODataPoint(age: 36, L: 1.0000, M: 92.0260, S: 0.01632),
    WHODataPoint(age: 42, L: 1.0000, M: 95.7647, S: 0.01584),
    WHODataPoint(age: 48, L: 1.0000, M: 99.2515, S: 0.01548),
    WHODataPoint(age: 54, L: 1.0000, M: 102.5388, S: 0.01521),
    WHODataPoint(age: 60, L: 1.0000, M: 105.6755, S: 0.01502),
  ];

  // Complete WHO LMS data for head circumference (cm) - Boys
  static const List<WHODataPoint> _headCircumferenceBoysLMS = [
    WHODataPoint(age: 0, L: 1.0000, M: 34.4618, S: 0.03686),
    WHODataPoint(age: 1, L: 1.0000, M: 37.2759, S: 0.03293),
    WHODataPoint(age: 2, L: 1.0000, M: 39.1285, S: 0.03016),
    WHODataPoint(age: 3, L: 1.0000, M: 40.5135, S: 0.02806),
    WHODataPoint(age: 4, L: 1.0000, M: 41.6317, S: 0.02646),
    WHODataPoint(age: 5, L: 1.0000, M: 42.5576, S: 0.02523),
    WHODataPoint(age: 6, L: 1.0000, M: 43.3306, S: 0.02426),
    WHODataPoint(age: 7, L: 1.0000, M: 44.0008, S: 0.02348),
    WHODataPoint(age: 8, L: 1.0000, M: 44.5998, S: 0.02283),
    WHODataPoint(age: 9, L: 1.0000, M: 45.1468, S: 0.02227),
    WHODataPoint(age: 10, L: 1.0000, M: 45.6512, S: 0.02179),
    WHODataPoint(age: 11, L: 1.0000, M: 46.1188, S: 0.02137),
    WHODataPoint(age: 12, L: 1.0000, M: 46.5548, S: 0.02101),
    WHODataPoint(age: 15, L: 1.0000, M: 47.5497, S: 0.02020),
    WHODataPoint(age: 18, L: 1.0000, M: 48.4244, S: 0.01953),
    WHODataPoint(age: 21, L: 1.0000, M: 49.2112, S: 0.01897),
    WHODataPoint(age: 24, L: 1.0000, M: 49.9200, S: 0.01851),
    WHODataPoint(age: 30, L: 1.0000, M: 51.0687, S: 0.01770),
    WHODataPoint(age: 36, L: 1.0000, M: 52.0260, S: 0.01713),
    WHODataPoint(age: 42, L: 1.0000, M: 52.8647, S: 0.01672),
    WHODataPoint(age: 48, L: 1.0000, M: 53.6015, S: 0.01642),
    WHODataPoint(age: 54, L: 1.0000, M: 54.2588, S: 0.01621),
    WHODataPoint(age: 60, L: 1.0000, M: 54.8555, S: 0.01607),
  ];

  // Complete WHO LMS data for head circumference (cm) - Girls
  static const List<WHODataPoint> _headCircumferenceGirlsLMS = [
    WHODataPoint(age: 0, L: 1.0000, M: 33.8777, S: 0.03790),
    WHODataPoint(age: 1, L: 1.0000, M: 36.5472, S: 0.03368),
    WHODataPoint(age: 2, L: 1.0000, M: 38.2673, S: 0.03070),
    WHODataPoint(age: 3, L: 1.0000, M: 39.5329, S: 0.02849),
    WHODataPoint(age: 4, L: 1.0000, M: 40.5399, S: 0.02679),
    WHODataPoint(age: 5, L: 1.0000, M: 41.3801, S: 0.02547),
    WHODataPoint(age: 6, L: 1.0000, M: 42.1011, S: 0.02442),
    WHODataPoint(age: 7, L: 1.0000, M: 42.7373, S: 0.02356),
    WHODataPoint(age: 8, L: 1.0000, M: 43.3098, S: 0.02283),
    WHODataPoint(age: 9, L: 1.0000, M: 43.8335, S: 0.02220),
    WHODataPoint(age: 10, L: 1.0000, M: 44.3118, S: 0.02165),
    WHODataPoint(age: 11, L: 1.0000, M: 44.7518, S: 0.02117),
    WHODataPoint(age: 12, L: 1.0000, M: 45.1557, S: 0.02075),
    WHODataPoint(age: 15, L: 1.0000, M: 46.1436, S: 0.01981),
    WHODataPoint(age: 18, L: 1.0000, M: 47.0024, S: 0.01905),
    WHODataPoint(age: 21, L: 1.0000, M: 47.7488, S: 0.01842),
    WHODataPoint(age: 24, L: 1.0000, M: 48.4000, S: 0.01789),
    WHODataPoint(age: 30, L: 1.0000, M: 49.4841, S: 0.01698),
    WHODataPoint(age: 36, L: 1.0000, M: 50.4260, S: 0.01632),
    WHODataPoint(age: 42, L: 1.0000, M: 51.2647, S: 0.01584),
    WHODataPoint(age: 48, L: 1.0000, M: 52.0215, S: 0.01548),
    WHODataPoint(age: 54, L: 1.0000, M: 52.7188, S: 0.01521),
    WHODataPoint(age: 60, L: 1.0000, M: 53.3655, S: 0.01502),
  ];

  /// Get WHO LMS data for specific measurement type and gender
  static List<WHODataPoint> _getLMSData(String measurementType, String gender) {
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return gender.toLowerCase() == 'male' ? _weightBoysLMS : _weightGirlsLMS;
      case 'height':
      case 'length':
        return gender.toLowerCase() == 'male' ? _heightBoysLMS : _heightGirlsLMS;
      case 'head_circumference':
      case 'head circumference':
        return gender.toLowerCase() == 'male' 
            ? _headCircumferenceBoysLMS 
            : _headCircumferenceGirlsLMS;
      default:
        throw ArgumentError('Unsupported measurement type: $measurementType');
    }
  }

  /// Calculate exact percentile using WHO LMS method
  static double calculateExactPercentile(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    try {
      final lmsData = _getLMSData(measurementType, gender);
      final interpolatedLMS = _interpolateLMS(ageInMonths, lmsData);
      
      final zScore = _calculateZScore(value, interpolatedLMS.L, interpolatedLMS.M, interpolatedLMS.S);
      final percentile = _zScoreToPercentile(zScore);
      
      return percentile.clamp(0.1, 99.9);
    } catch (e) {
      // Return error value to indicate calculation failed
      throw ArgumentError('Failed to calculate percentile: ${e.toString()}');
    }
  }

  /// Calculate Z-score using WHO LMS method
  static double calculateZScore(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    try {
      final lmsData = _getLMSData(measurementType, gender);
      final interpolatedLMS = _interpolateLMS(ageInMonths, lmsData);
      
      return _calculateZScore(value, interpolatedLMS.L, interpolatedLMS.M, interpolatedLMS.S);
    } catch (e) {
      return 0.0;
    }
  }

  /// Get percentile value for a specific percentile and age
  static double getPercentileValue(
    double percentile,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    try {
      final lmsData = _getLMSData(measurementType, gender);
      final interpolatedLMS = _interpolateLMS(ageInMonths, lmsData);
      
      final zScore = _percentileToZScore(percentile);
      return _zScoreToValue(zScore, interpolatedLMS.L, interpolatedLMS.M, interpolatedLMS.S);
    } catch (e) {
      return 0.0;
    }
  }

  /// Interpolate LMS parameters for exact age
  static WHODataPoint _interpolateLMS(double ageInMonths, List<WHODataPoint> lmsData) {
    // Find exact match first
    for (final point in lmsData) {
      if (point.age == ageInMonths) {
        return point;
      }
    }

    // Find surrounding points for interpolation
    WHODataPoint? lowerPoint;
    WHODataPoint? upperPoint;

    for (int i = 0; i < lmsData.length - 1; i++) {
      if (ageInMonths >= lmsData[i].age && ageInMonths <= lmsData[i + 1].age) {
        lowerPoint = lmsData[i];
        upperPoint = lmsData[i + 1];
        break;
      }
    }

    // Handle edge cases
    if (lowerPoint == null) {
      if (ageInMonths < lmsData.first.age) {
        return lmsData.first;
      } else {
        return lmsData.last;
      }
    }

    // Linear interpolation
    final ratio = (ageInMonths - lowerPoint.age) / (upperPoint!.age - lowerPoint.age);
    
    return WHODataPoint(
      age: ageInMonths,
      L: lowerPoint.L + (upperPoint.L - lowerPoint.L) * ratio,
      M: lowerPoint.M + (upperPoint.M - lowerPoint.M) * ratio,
      S: lowerPoint.S + (upperPoint.S - lowerPoint.S) * ratio,
    );
  }

  /// Calculate Z-score using LMS parameters
  static double _calculateZScore(double value, double L, double M, double S) {
    if (L.abs() < 0.01) {
      // When L is close to 0, use simplified formula
      return log(value / M) / S;
    } else {
      // Standard LMS formula
      return (pow(value / M, L) - 1) / (L * S);
    }
  }

  /// Convert Z-score to percentile
  static double _zScoreToPercentile(double zScore) {
    // Using normal distribution CDF approximation
    final cdf = 0.5 * (1 + _erf(zScore / sqrt(2)));
    return (cdf * 100).clamp(0.1, 99.9);
  }

  /// Convert percentile to Z-score
  static double _percentileToZScore(double percentile) {
    final p = (percentile / 100).clamp(0.001, 0.999);
    return sqrt(2) * _erfInv(2 * p - 1);
  }

  /// Convert Z-score back to measurement value
  static double _zScoreToValue(double zScore, double L, double M, double S) {
    if (L.abs() < 0.01) {
      // When L is close to 0, use simplified formula
      return M * exp(S * zScore);
    } else {
      // Standard LMS formula
      return M * pow(1 + L * S * zScore, 1 / L);
    }
  }

  /// Error function approximation
  static double _erf(double x) {
    // Abramowitz and Stegun approximation
    const a1 = 0.254829592;
    const a2 = -0.284496736;
    const a3 = 1.421413741;
    const a4 = -1.453152027;
    const a5 = 1.061405429;
    const p = 0.3275911;

    final sign = x < 0 ? -1 : 1;
    x = x.abs();

    final t = 1.0 / (1.0 + p * x);
    final y = 1.0 - (((((a5 * t + a4) * t) + a3) * t + a2) * t + a1) * t * exp(-x * x);

    return sign * y;
  }

  /// Inverse error function approximation
  static double _erfInv(double x) {
    const a = 0.147;
    final ln1MinusX2 = log(1 - x * x);
    final term1 = 2 / (pi * a) + ln1MinusX2 / 2;
    final term2 = ln1MinusX2 / a;
    
    final result = sqrt(-term1 + sqrt(term1 * term1 - term2));
    return x < 0 ? -result : result;
  }

  /// Comprehensive data validation
  static bool isValidMeasurement(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    // Age validation
    if (ageInMonths < 0 || ageInMonths > 60) {
      return false;
    }

    // Biological range validation
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return value >= 0.5 && value <= 50.0; // kg
      case 'height':
      case 'length':
        return value >= 30.0 && value <= 150.0; // cm
      case 'head_circumference':
      case 'head circumference':
        return value >= 25.0 && value <= 70.0; // cm
      default:
        return false;
    }
  }

  /// Check if measurement is within normal range (3rd-97th percentile)
  static bool isWithinNormalRange(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    final percentile = calculateExactPercentile(value, ageInMonths, measurementType, gender);
    return percentile >= 3.0 && percentile <= 97.0;
  }

  /// Generate percentile result with interpretation
  static PercentileResult analyzePercentile(
    double value,
    double ageInMonths,
    String measurementType,
    String gender,
  ) {
    final percentile = calculateExactPercentile(value, ageInMonths, measurementType, gender);
    final zScore = calculateZScore(value, ageInMonths, measurementType, gender);
    
    String category;
    String interpretation;
    bool requiresAttention;

    if (percentile < 3.0) {
      category = "Below Normal";
      interpretation = "Below 3rd percentile - consider medical consultation";
      requiresAttention = true;
    } else if (percentile < 10.0) {
      category = "Low Normal";
      interpretation = "Below 10th percentile - monitor closely";
      requiresAttention = true;
    } else if (percentile < 25.0) {
      category = "Lower Average";
      interpretation = "Below average but within normal range";
      requiresAttention = false;
    } else if (percentile <= 75.0) {
      category = "Average";
      interpretation = "Within average range";
      requiresAttention = false;
    } else if (percentile <= 90.0) {
      category = "Higher Average";
      interpretation = "Above average but within normal range";
      requiresAttention = false;
    } else if (percentile <= 97.0) {
      category = "High Normal";
      interpretation = "Above 90th percentile - monitor closely";
      requiresAttention = false;
    } else {
      category = "Above Normal";
      interpretation = "Above 97th percentile - consider medical consultation";
      requiresAttention = true;
    }

    return PercentileResult(
      percentile: percentile,
      zScore: zScore,
      interpretation: interpretation,
      requiresAttention: requiresAttention,
      category: category,
    );
  }
}