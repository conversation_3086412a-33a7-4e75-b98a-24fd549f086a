import 'dart:math';

/// Enhanced time range service with dynamic scaling and age format support
class TimeRangeService {
  TimeRangeService._();
  
  /// Available time range options
  static const List<TimeRangeOption> availableRanges = [
    TimeRangeOption(
      id: '3_months',
      label: '3 Months',
      shortLabel: '3M',
      maxAgeMonths: 3.0,
      intervalMonths: 0.5,
      description: 'Early infancy growth',
      icon: 'baby_changing_station',
    ),
    TimeRangeOption(
      id: '6_months',
      label: '6 Months',
      shortLabel: '6M',
      maxAgeMonths: 6.0,
      intervalMonths: 1.0,
      description: 'First half year',
      icon: 'child_care',
    ),
    TimeRangeOption(
      id: '1_year',
      label: '1 Year',
      shortLabel: '1Y',
      maxAgeMonths: 12.0,
      intervalMonths: 2.0,
      description: 'First year milestones',
      icon: 'cake',
    ),
    TimeRangeOption(
      id: '18_months',
      label: '18 Months',
      shortLabel: '18M',
      maxAgeMonths: 18.0,
      intervalMonths: 3.0,
      description: 'Toddler development',
      icon: 'directions_walk',
    ),
    TimeRangeOption(
      id: '2_years',
      label: '2 Years',
      shortLabel: '2Y',
      maxAgeMonths: 24.0,
      intervalMonths: 3.0,
      description: 'Early childhood',
      icon: 'child_friendly',
    ),
    TimeRangeOption(
      id: '3_years',
      label: '3 Years',
      shortLabel: '3Y',
      maxAgeMonths: 36.0,
      intervalMonths: 6.0,
      description: 'Preschool age',
      icon: 'school',
    ),
    TimeRangeOption(
      id: '4_years',
      label: '4 Years',
      shortLabel: '4Y',
      maxAgeMonths: 48.0,
      intervalMonths: 6.0,
      description: 'Pre-kindergarten',
      icon: 'auto_stories',
    ),
    TimeRangeOption(
      id: '5_years',
      label: '5 Years',
      shortLabel: '5Y',
      maxAgeMonths: 60.0,
      intervalMonths: 12.0,
      description: 'Complete WHO range',
      icon: 'timeline',
    ),
    TimeRangeOption(
      id: 'all',
      label: 'All Data',
      shortLabel: 'All',
      maxAgeMonths: 60.0,
      intervalMonths: 12.0,
      description: 'Complete growth history',
      icon: 'show_chart',
    ),
  ];
  
  /// Get time range option by ID
  static TimeRangeOption? getTimeRangeById(String id) {
    try {
      return availableRanges.firstWhere((range) => range.id == id);
    } catch (e) {
      return null;
    }
  }
  
  /// Get appropriate time ranges for baby's current age with smart filtering
  static List<TimeRangeOption> getRelevantTimeRanges(double currentAgeMonths) {
    final relevant = <TimeRangeOption>[];
    
    // Always include ranges that encompass current age
    for (final range in availableRanges) {
      if (range.maxAgeMonths >= currentAgeMonths) {
        relevant.add(range);
      }
    }
    
    // Add shorter ranges for detailed view (but not too many)
    final detailedRanges = availableRanges.where((range) => 
      range.maxAgeMonths >= currentAgeMonths * 0.3 && 
      range.maxAgeMonths < currentAgeMonths &&
      !relevant.contains(range)
    ).take(2).toList();
    
    relevant.addAll(detailedRanges);
    
    // Always include "All Data" option
    if (!relevant.any((r) => r.id == 'all')) {
      relevant.add(availableRanges.last);
    }
    
    // Sort by maxAgeMonths for logical ordering
    relevant.sort((a, b) => a.maxAgeMonths.compareTo(b.maxAgeMonths));
    
    return relevant;
  }
  
  /// Get smart time range recommendation based on data density
  static TimeRangeOption getRecommendedTimeRange({
    required double currentAgeMonths,
    required int measurementCount,
    required List<double> measurementAges,
  }) {
    // If no measurements, recommend based on age
    if (measurementCount == 0) {
      if (currentAgeMonths <= 6) return getTimeRangeById('6_months')!;
      if (currentAgeMonths <= 12) return getTimeRangeById('1_year')!;
      if (currentAgeMonths <= 24) return getTimeRangeById('2_years')!;
      return getTimeRangeById('3_years')!;
    }
    
    // Calculate data span
    final minAge = measurementAges.reduce((a, b) => a < b ? a : b);
    final maxAge = measurementAges.reduce((a, b) => a > b ? a : b);
    final dataSpan = maxAge - minAge;
    
    // Find optimal range that shows all data with some padding
    final targetSpan = dataSpan * 1.2; // 20% padding
    
    for (final range in availableRanges) {
      if (range.maxAgeMonths >= targetSpan && range.maxAgeMonths >= currentAgeMonths) {
        return range;
      }
    }
    
    // Fallback to "All Data"
    return availableRanges.last;
  }
  
  /// Calculate optimal chart scaling for time range
  static ChartScaling calculateChartScaling({
    required TimeRangeOption timeRange,
    required List<Map<String, dynamic>> measurements,
    required String measurementType,
    required bool isMetric,
  }) {
    if (measurements.isEmpty) {
      return _getDefaultScaling(timeRange, measurementType, isMetric);
    }
    
    // Calculate data bounds
    double minAge = 0.0;
    double maxAge = timeRange.maxAgeMonths;
    double minValue = double.infinity;
    double maxValue = double.negativeInfinity;
    
    for (final measurement in measurements) {
      final ageInMonths = (measurement['ageInMonths'] as num?)?.toDouble() ?? 0.0;
      final value = (measurement['value'] as num?)?.toDouble() ?? 0.0;
      
      if (ageInMonths <= maxAge) {
        minValue = min(minValue, value);
        maxValue = max(maxValue, value);
      }
    }
    
    // Add padding to value range
    final valueRange = maxValue - minValue;
    final padding = valueRange * 0.1; // 10% padding
    
    return ChartScaling(
      minX: minAge,
      maxX: maxAge,
      minY: max(0, minValue - padding),
      maxY: maxValue + padding,
      xInterval: timeRange.intervalMonths,
      yInterval: _calculateYInterval(minValue - padding, maxValue + padding, measurementType, isMetric),
      xAxisTitle: _getXAxisTitle(timeRange),
      yAxisTitle: _getYAxisTitle(measurementType, isMetric),
    );
  }
  
  /// Get default scaling when no measurements exist
  static ChartScaling _getDefaultScaling(TimeRangeOption timeRange, String measurementType, bool isMetric) {
    double minY, maxY, yInterval;
    
    switch (measurementType.toLowerCase()) {
      case 'weight':
        if (isMetric) {
          minY = 0.0;
          maxY = timeRange.maxAgeMonths <= 12 ? 15.0 : 25.0;
          yInterval = timeRange.maxAgeMonths <= 12 ? 2.0 : 5.0;
        } else {
          minY = 0.0;
          maxY = timeRange.maxAgeMonths <= 12 ? 35.0 : 55.0;
          yInterval = timeRange.maxAgeMonths <= 12 ? 5.0 : 10.0;
        }
        break;
        
      case 'height':
      case 'length':
        if (isMetric) {
          minY = 40.0;
          maxY = timeRange.maxAgeMonths <= 12 ? 85.0 : 120.0;
          yInterval = timeRange.maxAgeMonths <= 12 ? 10.0 : 20.0;
        } else {
          minY = 15.0;
          maxY = timeRange.maxAgeMonths <= 12 ? 35.0 : 48.0;
          yInterval = timeRange.maxAgeMonths <= 12 ? 5.0 : 8.0;
        }
        break;
        
      case 'head_circumference':
      case 'head circumference':
        if (isMetric) {
          minY = 30.0;
          maxY = timeRange.maxAgeMonths <= 12 ? 50.0 : 60.0;
          yInterval = 5.0;
        } else {
          minY = 12.0;
          maxY = timeRange.maxAgeMonths <= 12 ? 20.0 : 24.0;
          yInterval = 2.0;
        }
        break;
        
      default:
        minY = 0.0;
        maxY = 100.0;
        yInterval = 10.0;
    }
    
    return ChartScaling(
      minX: 0.0,
      maxX: timeRange.maxAgeMonths,
      minY: minY,
      maxY: maxY,
      xInterval: timeRange.intervalMonths,
      yInterval: yInterval,
      xAxisTitle: _getXAxisTitle(timeRange),
      yAxisTitle: _getYAxisTitle(measurementType, isMetric),
    );
  }
  
  /// Calculate appropriate Y-axis interval
  static double _calculateYInterval(double minY, double maxY, String measurementType, bool isMetric) {
    final range = maxY - minY;
    final targetIntervals = 8; // Aim for ~8 intervals
    final rawInterval = range / targetIntervals;
    
    // Round to nice numbers
    final magnitude = pow(10, (log(rawInterval) / ln10).floor());
    final normalizedInterval = rawInterval / magnitude;
    
    double niceInterval;
    if (normalizedInterval <= 1) {
      niceInterval = 1;
    } else if (normalizedInterval <= 2) {
      niceInterval = 2;
    } else if (normalizedInterval <= 5) {
      niceInterval = 5;
    } else {
      niceInterval = 10;
    }
    
    return niceInterval * magnitude;
  }
  
  /// Get X-axis title based on time range
  static String _getXAxisTitle(TimeRangeOption timeRange) {
    if (timeRange.maxAgeMonths <= 6) {
      return 'Age (months)';
    } else if (timeRange.maxAgeMonths <= 24) {
      return 'Age (months)';
    } else {
      return 'Age (years)';
    }
  }
  
  /// Get Y-axis title based on measurement type and units
  static String _getYAxisTitle(String measurementType, bool isMetric) {
    switch (measurementType.toLowerCase()) {
      case 'weight':
        return isMetric ? 'Weight (kg)' : 'Weight (lbs)';
      case 'height':
      case 'length':
        return isMetric ? 'Height (cm)' : 'Height (in)';
      case 'head_circumference':
      case 'head circumference':
        return isMetric ? 'Head Circumference (cm)' : 'Head Circumference (in)';
      default:
        return 'Value';
    }
  }
  
  /// Format age for display based on age and preference
  static String formatAge(double ageInMonths, AgeDisplayFormat format) {
    switch (format) {
      case AgeDisplayFormat.monthsOnly:
        return '${ageInMonths.toStringAsFixed(0)}m';
        
      case AgeDisplayFormat.yearsAndMonths:
        final years = (ageInMonths / 12).floor();
        final months = (ageInMonths % 12).round();
        if (years == 0) {
          return '${months}m';
        } else if (months == 0) {
          return '${years}y';
        } else {
          return '${years}y ${months}m';
        }
        
      case AgeDisplayFormat.decimalYears:
        final years = ageInMonths / 12;
        return '${years.toStringAsFixed(1)}y';
        
      case AgeDisplayFormat.adaptive:
        if (ageInMonths < 24) {
          return '${ageInMonths.toStringAsFixed(0)}m';
        } else {
          final years = (ageInMonths / 12).floor();
          final months = (ageInMonths % 12).round();
          if (months == 0) {
            return '${years}y';
          } else {
            return '${years}y ${months}m';
          }
        }
    }
  }
  
  /// Get optimal age display format for time range
  static AgeDisplayFormat getOptimalAgeFormat(TimeRangeOption timeRange) {
    if (timeRange.maxAgeMonths <= 12) {
      return AgeDisplayFormat.monthsOnly;
    } else if (timeRange.maxAgeMonths <= 36) {
      return AgeDisplayFormat.yearsAndMonths;
    } else {
      return AgeDisplayFormat.adaptive;
    }
  }
  
  /// Generate age labels for chart axis
  static List<AgeLabel> generateAgeLabels(TimeRangeOption timeRange, AgeDisplayFormat format) {
    final labels = <AgeLabel>[];
    final interval = timeRange.intervalMonths;
    
    for (double age = 0; age <= timeRange.maxAgeMonths; age += interval) {
      labels.add(AgeLabel(
        ageInMonths: age,
        label: formatAge(age, format),
        isMinor: false,
      ));
      
      // Add minor labels for better granularity
      if (interval > 1 && age + interval / 2 <= timeRange.maxAgeMonths) {
        labels.add(AgeLabel(
          ageInMonths: age + interval / 2,
          label: formatAge(age + interval / 2, format),
          isMinor: true,
        ));
      }
    }
    
    return labels;
  }
  
  /// Get adaptive time range based on screen size and data density
  static TimeRangeOption getAdaptiveTimeRange({
    required double currentAgeMonths,
    required List<double> measurementAges,
    required double screenWidth,
    required int dataPointCount,
  }) {
    // Calculate data density
    final dataDensity = dataPointCount / screenWidth;
    
    // Get base recommendation
    final baseRange = getRecommendedTimeRange(
      currentAgeMonths: currentAgeMonths,
      measurementCount: dataPointCount,
      measurementAges: measurementAges,
    );
    
    // Adjust based on screen size and density
    if (screenWidth < 400) {
      // Small screen - prefer shorter ranges for better readability
      if (baseRange.maxAgeMonths > 24) {
        return getTimeRangeById('2_years') ?? baseRange;
      }
    } else if (screenWidth > 800) {
      // Large screen - can handle longer ranges
      if (dataDensity < 0.1 && currentAgeMonths > 12) {
        return getTimeRangeById('all') ?? baseRange;
      }
    }
    
    return baseRange;
  }
  
  /// Calculate optimal chart intervals based on data and display constraints
  static ChartIntervals calculateOptimalIntervals({
    required TimeRangeOption timeRange,
    required double screenWidth,
    required double screenHeight,
    required int dataPointCount,
    required String measurementType,
    required bool isMetric,
  }) {
    // Calculate pixel density
    final pixelsPerMonth = screenWidth / timeRange.maxAgeMonths;
    
    // Adjust X-axis intervals based on pixel density
    double xInterval = timeRange.intervalMonths;
    if (pixelsPerMonth < 20) {
      // Too crowded - increase interval
      xInterval = timeRange.intervalMonths * 2;
    } else if (pixelsPerMonth > 80) {
      // Too sparse - decrease interval
      xInterval = max(0.5, timeRange.intervalMonths / 2);
    }
    
    // Calculate Y-axis intervals based on measurement type and screen height
    double yInterval = _calculateOptimalYInterval(
      measurementType: measurementType,
      isMetric: isMetric,
      screenHeight: screenHeight,
      timeRange: timeRange,
    );
    
    return ChartIntervals(
      xInterval: xInterval,
      yInterval: yInterval,
      xMinorInterval: xInterval / 2,
      yMinorInterval: yInterval / 2,
      showMinorGridLines: screenWidth > 600,
    );
  }
  
  /// Calculate optimal Y-axis interval for measurement type
  static double _calculateOptimalYInterval({
    required String measurementType,
    required bool isMetric,
    required double screenHeight,
    required TimeRangeOption timeRange,
  }) {
    // Base intervals for different measurement types
    Map<String, Map<String, double>> baseIntervals = {
      'weight': {
        'metric': timeRange.maxAgeMonths <= 12 ? 1.0 : 2.0,
        'imperial': timeRange.maxAgeMonths <= 12 ? 2.0 : 5.0,
      },
      'height': {
        'metric': timeRange.maxAgeMonths <= 12 ? 5.0 : 10.0,
        'imperial': timeRange.maxAgeMonths <= 12 ? 2.0 : 4.0,
      },
      'head_circumference': {
        'metric': 2.0,
        'imperial': 1.0,
      },
    };
    
    final system = isMetric ? 'metric' : 'imperial';
    final baseInterval = baseIntervals[measurementType.toLowerCase()]?[system] ?? 1.0;
    
    // Adjust based on screen height
    final targetGridLines = 8;
    final pixelsPerGridLine = screenHeight / targetGridLines;
    
    if (pixelsPerGridLine < 30) {
      return baseInterval * 2; // Fewer grid lines for small screens
    } else if (pixelsPerGridLine > 80) {
      return baseInterval / 2; // More grid lines for large screens
    }
    
    return baseInterval;
  }
  
  /// Get smart time range suggestions based on usage patterns
  static List<TimeRangeOption> getSmartSuggestions({
    required double currentAgeMonths,
    required List<double> measurementAges,
    required int measurementCount,
    required String measurementType,
  }) {
    final suggestions = <TimeRangeOption>[];
    
    // Always include current age appropriate range
    final currentRange = getRecommendedTimeRange(
      currentAgeMonths: currentAgeMonths,
      measurementCount: measurementCount,
      measurementAges: measurementAges,
    );
    suggestions.add(currentRange);
    
    // Add milestone-based suggestions
    if (currentAgeMonths >= 12 && !suggestions.any((s) => s.id == '1_year')) {
      final yearRange = getTimeRangeById('1_year');
      if (yearRange != null) suggestions.add(yearRange);
    }
    
    if (currentAgeMonths >= 24 && !suggestions.any((s) => s.id == '2_years')) {
      final twoYearRange = getTimeRangeById('2_years');
      if (twoYearRange != null) suggestions.add(twoYearRange);
    }
    
    // Add data-density based suggestions
    if (measurementCount > 20) {
      // Dense data - suggest shorter ranges for detail
      final detailRanges = availableRanges.where((r) => 
        r.maxAgeMonths <= currentAgeMonths && 
        r.maxAgeMonths >= currentAgeMonths * 0.5 &&
        !suggestions.contains(r)
      ).take(2);
      suggestions.addAll(detailRanges);
    }
    
    // Always include "All Data" if not already present
    if (!suggestions.any((s) => s.id == 'all')) {
      suggestions.add(availableRanges.last);
    }
    
    return suggestions;
  }
  
  /// Calculate time range performance metrics
  static TimeRangePerformance calculatePerformanceMetrics({
    required TimeRangeOption timeRange,
    required int dataPointCount,
    required double screenWidth,
    required double screenHeight,
  }) {
    // Calculate rendering complexity
    final pixelDensity = (screenWidth * screenHeight) / 1000000; // Megapixels
    final dataDensity = dataPointCount / timeRange.maxAgeMonths;
    final renderingComplexity = pixelDensity * dataDensity;
    
    // Determine performance level
    PerformanceLevel performanceLevel;
    if (renderingComplexity < 1.0) {
      performanceLevel = PerformanceLevel.excellent;
    } else if (renderingComplexity < 5.0) {
      performanceLevel = PerformanceLevel.good;
    } else if (renderingComplexity < 15.0) {
      performanceLevel = PerformanceLevel.fair;
    } else {
      performanceLevel = PerformanceLevel.poor;
    }
    
    // Calculate estimated render time
    final baseRenderTime = 50; // Base 50ms
    final complexityMultiplier = 1 + (renderingComplexity * 0.1);
    final estimatedRenderTime = (baseRenderTime * complexityMultiplier).round();
    
    return TimeRangePerformance(
      performanceLevel: performanceLevel,
      estimatedRenderTimeMs: estimatedRenderTime,
      dataDensity: dataDensity,
      renderingComplexity: renderingComplexity,
      recommendOptimization: performanceLevel == PerformanceLevel.poor,
    );
  }
}

/// Time range configuration option
class TimeRangeOption {
  final String id;
  final String label;
  final String shortLabel;
  final double maxAgeMonths;
  final double intervalMonths;
  final String description;
  final String icon;
  
  const TimeRangeOption({
    required this.id,
    required this.label,
    required this.shortLabel,
    required this.maxAgeMonths,
    required this.intervalMonths,
    required this.description,
    required this.icon,
  });
}

/// Chart scaling configuration
class ChartScaling {
  final double minX;
  final double maxX;
  final double minY;
  final double maxY;
  final double xInterval;
  final double yInterval;
  final String xAxisTitle;
  final String yAxisTitle;
  
  const ChartScaling({
    required this.minX,
    required this.maxX,
    required this.minY,
    required this.maxY,
    required this.xInterval,
    required this.yInterval,
    required this.xAxisTitle,
    required this.yAxisTitle,
  });
}

/// Age display format options
enum AgeDisplayFormat {
  monthsOnly,      // "15m"
  yearsAndMonths,  // "1y 3m"
  decimalYears,    // "1.3y"
  adaptive,        // Automatically choose based on age
}

/// Age label for chart axis
class AgeLabel {
  final double ageInMonths;
  final String label;
  final bool isMinor;
  
  const AgeLabel({
    required this.ageInMonths,
    required this.label,
    required this.isMinor,
  });
}

/// Chart intervals configuration for optimal display
class ChartIntervals {
  final double xInterval;
  final double yInterval;
  final double xMinorInterval;
  final double yMinorInterval;
  final bool showMinorGridLines;
  
  const ChartIntervals({
    required this.xInterval,
    required this.yInterval,
    required this.xMinorInterval,
    required this.yMinorInterval,
    required this.showMinorGridLines,
  });
}

/// Time range performance metrics
class TimeRangePerformance {
  final PerformanceLevel performanceLevel;
  final int estimatedRenderTimeMs;
  final double dataDensity;
  final double renderingComplexity;
  final bool recommendOptimization;
  
  const TimeRangePerformance({
    required this.performanceLevel,
    required this.estimatedRenderTimeMs,
    required this.dataDensity,
    required this.renderingComplexity,
    required this.recommendOptimization,
  });
}

/// Performance level indicators
enum PerformanceLevel {
  excellent,  // Very fast rendering
  good,       // Good performance
  fair,       // Acceptable performance
  poor,       // May have performance issues
}