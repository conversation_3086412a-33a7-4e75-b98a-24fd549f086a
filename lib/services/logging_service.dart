import 'package:flutter/foundation.dart';

/// Centralized logging service with different log levels
class LoggingService {
  static const bool _enableDebugLogs = kDebugMode;
  static const bool _enableInfoLogs = true;
  static const bool _enableErrorLogs = true;

  /// Log debug information (only in debug mode)
  static void debug(String message, [String? tag]) {
    if (_enableDebugLogs) {
      final tagPrefix = tag != null ? '[$tag] ' : '';
      debugPrint('🔍 $tagPrefix$message');
    }
  }

  /// Log general information
  static void info(String message, [String? tag]) {
    if (_enableInfoLogs) {
      final tagPrefix = tag != null ? '[$tag] ' : '';
      debugPrint('ℹ️ $tagPrefix$message');
    }
  }

  /// Log warnings
  static void warning(String message, [String? tag]) {
    final tagPrefix = tag != null ? '[$tag] ' : '';
    debugPrint('⚠️ $tagPrefix$message');
  }

  /// Log errors
  static void error(String message, [dynamic error, String? tag]) {
    if (_enableErrorLogs) {
      final tagPrefix = tag != null ? '[$tag] ' : '';
      final errorSuffix = error != null ? ' - Error: $error' : '';
      debugPrint('❌ $tagPrefix$message$errorSuffix');
    }
  }

  /// Log success messages
  static void success(String message, [String? tag]) {
    final tagPrefix = tag != null ? '[$tag] ' : '';
    debugPrint('✅ $tagPrefix$message');
  }

  /// Log database operations
  static void database(String operation, String table, [Map<String, dynamic>? data]) {
    if (_enableDebugLogs) {
      final dataInfo = data != null ? ' - Data: ${data.keys.join(', ')}' : '';
      debugPrint('🗄️ [DB] $operation on $table$dataInfo');
    }
  }

  /// Log network operations
  static void network(String operation, [String? endpoint]) {
    if (_enableDebugLogs) {
      final endpointInfo = endpoint != null ? ' - $endpoint' : '';
      debugPrint('🌐 [NET] $operation$endpointInfo');
    }
  }
}