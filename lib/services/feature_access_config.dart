/// Configuration constants for feature access system
class FeatureAccessConfig {
  /// Cache performance settings
  static const double nearLimitThreshold = 0.8; // 80% of limit
  static const Duration cacheTimeout = Duration(minutes: 5);
  static const int maxCacheSize = 100;
  
  /// Performance monitoring thresholds
  static const Duration slowOperationThreshold = Duration(milliseconds: 100);
  static const int maxRetryAttempts = 3;
  static const Duration retryDelay = Duration(seconds: 2);
  
  /// Free plan limits
  static const int freeBabyProfiles = 1;
  static const int freeFamilyMembers = 1;
  static const int freeDataExports = 0;
  static const int freeStorageGB = 1;
  static const int freeScheduledActivities = 5;
  
  /// Premium plan limits
  static const int premiumFamilyMembers = 10;
  static const int? premiumBabyProfiles = null; // Unlimited
  static const int? premiumDataExports = null; // Unlimited
  static const int? premiumStorageGB = null; // Unlimited
  static const int? premiumScheduledActivities = null; // Unlimited
  
  /// Subscription pricing (centralized for consistency)
  static const double monthlyPrice = 14.90;
  static const double annualPrice = 9.90; // Per month when billed annually
  static const double annualTotalPrice = 118.80; // Total annual price
  
  /// Product IDs for app store integration
  static const String monthlyProductId = 'babytracker_premium_monthly';
  static const String annualProductId = 'babytracker_premium_annual';
  
  /// Feature access messages (moved from hardcoded strings)
  static const Map<String, String> restrictionMessages = {
    'multipleBabyProfiles': 'Free plan limited to 1 baby profile. Upgrade to Premium for unlimited baby profiles.',
    'familySharing': 'Family sharing requires Premium plan. Upgrade to invite up to 10 family members.',
    'whoGrowthCharts': 'WHO Growth Charts require Premium plan. Upgrade to track your baby\'s growth with official WHO percentile charts.',
    'aiInsights': 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
    'aiChat': 'Ask AI chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
    'dataExport': 'Data export requires Premium plan. Upgrade to export your data for healthcare providers.',
    'advancedAnalytics': 'Advanced analytics require Premium plan. Upgrade for detailed insights and trend analysis.',
    'customReminders': 'Custom notifications require Premium plan. Upgrade to set personalized reminders.',
    'prioritySupport': 'Priority customer support requires Premium plan. Upgrade for faster response times.',
  };
  
  /// Premium features list (centralized for consistency)
  static const List<String> premiumFeatures = [
    'Unlimited activity tracking',
    'Unlimited scheduled activities',
    'Unlimited baby profiles',
    'Family sharing with up to 10 members',
    'WHO Growth Charts',
    'AI insights',
    'Ask AI chat',
    'Data export',
    'Priority customer support',
    'Advanced analytics',
    'Custom notifications',
  ];
  
  /// Free features list
  static const List<String> freeFeatures = [
    'Basic activity tracking',
    'Basic scheduled activities',
    'Up to 1 baby profile',
  ];
  
  /// Debug settings
  static const bool enableDebugLogging = true;
  static const bool enablePerformanceMonitoring = true;
  static const String debugPrefix = 'DEBUG: FeatureAccessService';
  
  /// UI Configuration
  static const Duration upgradePromptCooldown = Duration(hours: 24);
  static const int maxUpgradePromptsPerDay = 3;
}