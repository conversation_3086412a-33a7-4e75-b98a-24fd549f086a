import '../core/app_export.dart';

/// Factory for creating AI insights with consistent structure and validation
class AIInsightsFactory {
  AIInsightsFactory._();

  /// Create default AI insights when no data is available
  static Map<String, String> createDefaultInsights() {
    return {
      'sleep_insights': 'Continue tracking sleep patterns to get personalized insights about rest quality and schedule.',
      'sleep_recommendations': 'Maintain consistent bedtime routines and ensure a calm sleep environment.',
      'feeding_insights': 'Keep logging feeding sessions to understand nutrition patterns and preferences.',
      'feeding_recommendations': 'Follow your baby\'s hunger cues and maintain regular feeding schedules.',
      'diaper_insights': 'Regular diaper changes indicate healthy digestion and hydration patterns.',
      'diaper_recommendations': 'Continue monitoring diaper changes as they reflect your baby\'s health.',
      'growth_insights': 'Track growth measurements regularly to monitor healthy development.',
      'growth_recommendations': 'Consult your pediatrician for regular checkups and growth assessments.',
      'health_insights': 'Monitor health indicators regularly for optimal care.',
      'health_recommendations': 'Maintain regular health checkups and follow medical advice.',
      'medicine_insights': 'Track medication adherence for optimal health outcomes.',
      'medicine_recommendations': 'Follow prescribed medication schedules and consult healthcare providers.',
      'development_insights': 'Monitor developmental milestones for age-appropriate progress.',
      'development_recommendations': 'Engage in age-appropriate activities to support development.',
      'overall_insights': 'Your baby\'s development is progressing well. Continue with consistent care routines.',
      'overall_recommendations': 'Maintain regular feeding, sleeping, and play schedules for optimal development.',
    };
  }

  /// Create demo insights for demonstration purposes
  static Map<String, String> createDemoInsights() {
    return {
      'sleep_insights': 'Your baby shows excellent sleep patterns with 8.2 hours average sleep and improving consistency. Sleep quality scores are trending upward.',
      'sleep_recommendations': 'Maintain current bedtime routine at 7:30 PM. Consider blackout curtains for better sleep quality during growth spurts.',
      'feeding_insights': 'Healthy feeding schedule with 6.8 average feeds per day. Intake volumes are appropriate for age with good consistency.',
      'feeding_recommendations': 'Continue current 3-hour feeding schedule. Watch for appetite changes during growth spurts and introduce new foods gradually.',
      'diaper_insights': 'Normal diaper patterns with 8.2 average changes daily. Healthy frequency and consistency patterns observed.',
      'diaper_recommendations': 'Maintain current diet and regular changes. Monitor for any unusual changes in frequency or consistency.',
      'growth_insights': 'Growth tracking shows healthy progression at 65th percentile for weight and 70th for height. Steady growth trend maintained.',
      'growth_recommendations': 'Continue regular check-ups and balanced nutrition. Monitor developmental milestones alongside physical growth.',
      'health_insights': 'Overall health indicators show excellent trends with strong immune system and normal vital signs.',
      'health_recommendations': 'Continue current care routine, maintain vaccination schedule, and keep regular pediatric appointments.',
      'medicine_insights': 'Medication adherence at 95% with good effectiveness. Vitamin D and iron supplements well-tolerated.',
      'medicine_recommendations': 'Continue current medication schedule. Monitor for side effects and consult doctor for any concerns.',
      'development_insights': 'Motor and cognitive skills developing appropriately for age. Rolling over achieved at 4 months, sitting up in progress.',
      'development_recommendations': 'Continue tummy time and sensory play. Read daily to support language development and provide varied experiences.',
      'overall_insights': 'Comprehensive analysis shows excellent care patterns across all areas. Baby is thriving with consistent routines and attentive care.',
      'overall_recommendations': 'Maintain current excellent care standards. Continue logging activities for ongoing insights and celebrate developmental achievements.',
    };
  }

  /// Convert shared insights from state manager to text format
  static Map<String, String> convertSharedInsights(Map<String, dynamic> sharedInsights) {
    return {
      'sleep_insights': _extractDetailedInsights(sharedInsights['sleepAnalysis']) ?? 
          'Continue tracking sleep patterns to get personalized insights.',
      'sleep_recommendations': _extractRecommendations(sharedInsights['sleepAnalysis']),
      'feeding_insights': _extractDetailedInsights(sharedInsights['feedingAnalysis']) ?? 
          'Keep logging feeding sessions to understand nutrition patterns.',
      'feeding_recommendations': _extractRecommendations(sharedInsights['feedingAnalysis']),
      'diaper_insights': _extractDetailedInsights(sharedInsights['diaperAnalysis']) ?? 
          'Regular diaper changes indicate healthy digestion patterns.',
      'diaper_recommendations': _extractRecommendations(sharedInsights['diaperAnalysis']),
      'growth_insights': _extractDetailedInsights(sharedInsights['growthAnalysis']) ?? 
          'Track growth measurements regularly to monitor development.',
      'growth_recommendations': _extractRecommendations(sharedInsights['growthAnalysis']),
      'health_insights': _extractDetailedInsights(sharedInsights['healthAnalysis']) ?? 
          'Monitor health indicators regularly for optimal care.',
      'health_recommendations': _extractRecommendations(sharedInsights['healthAnalysis']),
      'medicine_insights': _extractDetailedInsights(sharedInsights['medicineAnalysis']) ?? 
          'Track medication adherence for optimal health outcomes.',
      'medicine_recommendations': _extractRecommendations(sharedInsights['medicineAnalysis']),
      'development_insights': _extractDetailedInsights(sharedInsights['developmentAnalysis']) ?? 
          'Monitor developmental milestones for age-appropriate progress.',
      'development_recommendations': _extractRecommendations(sharedInsights['developmentAnalysis']),
      'overall_insights': sharedInsights['overallSummary']?['mainSummary'] ?? 
          _generateOverallInsightsText(sharedInsights),
      'overall_recommendations': sharedInsights['overallSummary']?['topRecommendations']?.join('. ') ?? 
          _generateOverallRecommendationsText(sharedInsights),
    };
  }

  /// Validate insights structure and content
  static bool validateInsights(Map<String, String> insights) {
    final requiredKeys = [
      'sleep_insights', 'sleep_recommendations',
      'feeding_insights', 'feeding_recommendations',
      'diaper_insights', 'diaper_recommendations',
      'growth_insights', 'growth_recommendations',
      'overall_insights', 'overall_recommendations',
    ];

    // Check all required keys exist
    for (final key in requiredKeys) {
      if (!insights.containsKey(key) || insights[key]?.isEmpty == true) {
        return false;
      }
    }

    // Check content quality (minimum length)
    for (final value in insights.values) {
      if (value.length < 10) {
        return false;
      }
    }

    return true;
  }

  /// Create insights with fallback handling
  static Map<String, String> createInsightsWithFallback(
    Map<String, dynamic>? sharedInsights, {
    bool useDemoData = false,
  }) {
    if (useDemoData) {
      return createDemoInsights();
    }

    if (sharedInsights != null && sharedInsights.isNotEmpty) {
      final converted = convertSharedInsights(sharedInsights);
      if (validateInsights(converted)) {
        return converted;
      }
    }

    return createDefaultInsights();
  }

  // Private helper methods
  static String? _extractDetailedInsights(Map<String, dynamic>? analysis) {
    if (analysis == null) return null;
    
    final data = _safeMapCast(analysis['data']);
    final insights = data?['insights'] as List?;
    
    if (insights != null && insights.isNotEmpty) {
      return insights.join(' ');
    }
    
    final description = analysis['description'] as String?;
    if (description != null && 
        description.isNotEmpty && 
        !description.toLowerCase().contains('not enough data')) {
      return description;
    }
    
    return null;
  }

  static String _extractRecommendations(Map<String, dynamic>? analysis) {
    if (analysis == null) return 'Continue logging activities for personalized recommendations.';
    
    final data = _safeMapCast(analysis['data']);
    final recommendations = data?['recommendations'] as List?;
    
    if (recommendations != null && recommendations.isNotEmpty) {
      return recommendations.take(3).join('. ') + '.';
    }
    
    return 'Continue logging activities for personalized recommendations.';
  }

  static Map<String, dynamic>? _safeMapCast(dynamic value) {
    if (value == null) return null;
    if (value is Map<String, dynamic>) return value;
    if (value is Map) return Map<String, dynamic>.from(value);
    return null;
  }

  static String _generateOverallInsightsText(Map<String, dynamic> insights) {
    final components = <String>[];
    
    for (final category in ['sleepAnalysis', 'feedingAnalysis', 'growthAnalysis']) {
      final analysis = _safeMapCast(insights[category]);
      if (analysis != null) {
        final confidence = (analysis['confidence'] as num?) ?? 0;
        if (confidence > 0.2) {
          components.add(category.replaceAll('Analysis', ' patterns analyzed'));
        }
      }
    }
    
    if (components.isEmpty) {
      return 'Continue logging activities to unlock personalized AI insights and recommendations.';
    }
    
    return '${_capitalize(components.join(', '))}. AI insights are being generated based on your data.';
  }

  static String _generateOverallRecommendationsText(Map<String, dynamic> insights) {
    final recommendations = <String>[];
    
    for (final category in ['sleepAnalysis', 'feedingAnalysis', 'growthAnalysis']) {
      final analysis = _safeMapCast(insights[category]);
      if (analysis != null) {
        final data = _safeMapCast(analysis['data']);
        final recs = data?['recommendations'] as List?;
        if (recs != null && recs.isNotEmpty) {
          recommendations.add(recs.first.toString());
        }
      }
    }
    
    if (recommendations.isEmpty) {
      return 'Maintain consistent routines for feeding, sleeping, and growth tracking.';
    }
    
    return recommendations.take(3).join('. ') + '.';
  }

  static String _capitalize(String text) {
    if (text.isEmpty) return text;
    return text[0].toUpperCase() + text.substring(1);
  }
}