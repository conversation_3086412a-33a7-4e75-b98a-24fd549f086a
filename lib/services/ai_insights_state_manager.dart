import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../core/app_export.dart';

/// Centralized state manager for AI insights to ensure synchronization
/// between Home screen and AI Insights Dashboard
class AIInsightsStateManager extends ChangeNotifier {
  static final AIInsightsStateManager _instance = AIInsightsStateManager._internal();
  
  factory AIInsightsStateManager() => _instance;
  
  AIInsightsStateManager._internal();

  final AIAnalysisService _aiAnalysisService = AIAnalysisService();
  final SupabaseService _supabaseService = SupabaseService();
  
  // Shared state
  Map<String, dynamic> _insights = {};
  Map<String, dynamic>? _overallSummary;
  DateTime? _lastUpdateTime;
  DateTime? _lastManualRefreshTime;
  bool _isLoading = false;
  bool _isUpdating = false;
  String? _currentBabyId;
  bool _wasSkippedDueToInsufficientData = false; // Track if last generation was skipped
  
  // User activity tracking
  DateTime? _lastUserActivity;
  static const Duration _userActiveThreshold = Duration(minutes: 30);
  
  // Professional rate limiting settings
  static const Duration _manualRefreshCooldown = Duration(hours: 4); // 4-hour cooldown for manual refresh
  static const Duration _autoRefreshWindow = Duration(hours: 24); // 24-hour window for auto refresh
  static const Duration _cacheValidDuration = Duration(hours: 24);    // Cache expiration
  
  // Getters
  Map<String, dynamic> get insights => _insights;
  Map<String, dynamic>? get overallSummary => _overallSummary;
  DateTime? get lastUpdateTime => _lastUpdateTime;
  DateTime? get lastManualRefreshTime => _lastManualRefreshTime;
  bool get isLoading => _isLoading;
  bool get isUpdating => _isUpdating;
  bool get wasSkippedDueToInsufficientData => _wasSkippedDueToInsufficientData;
  
  /// Get the most recent timestamp (either cache or manual refresh)
  DateTime? get mostRecentTimestamp {
    if (_lastManualRefreshTime == null && _lastUpdateTime == null) return null;
    if (_lastManualRefreshTime == null) return _lastUpdateTime;
    if (_lastUpdateTime == null) return _lastManualRefreshTime;
    
    return _lastManualRefreshTime!.isAfter(_lastUpdateTime!) 
        ? _lastManualRefreshTime 
        : _lastUpdateTime;
  }
  
  /// Format the most recent timestamp for display (recalculated each time for dynamic updates)
  String? get formattedLastUpdate {
    final timestamp = mostRecentTimestamp;
    if (timestamp == null) return null;
    
    // Use local time for both current time and timestamp
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    // Handle negative time differences (future timestamps)
    if (difference.inMinutes < 0) {
      debugPrint('⚠️ Timestamp is in the future, treating as recent');
      return 'just now';
    }
    
    if (difference.inDays > 0) {
      final days = difference.inDays;
      final hours = difference.inHours % 24;
      if (hours > 0) {
        return '$days day${days > 1 ? 's' : ''} $hours hour${hours > 1 ? 's' : ''} ago';
      }
      return '$days day${days > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;
      if (minutes > 0) {
        return '$hours hour${hours > 1 ? 's' : ''} $minutes minute${minutes > 1 ? 's' : ''} ago';
      }
      return '$hours hour${hours > 1 ? 's' : ''} ago';
    } else {
      final minutes = difference.inMinutes;
      if (minutes < 1) {
        return 'just now';
      }
      return '$minutes minute${minutes > 1 ? 's' : ''} ago';
    }
  }
  
  /// Start a timer to refresh timestamp display every minute
  Timer? _timestampTimer;
  
  void startTimestampUpdates() {
    _timestampTimer?.cancel();
    _timestampTimer = Timer.periodic(Duration(minutes: 1), (timer) {
      // Timer update logging reduced for performance
      // Notify listeners to refresh the UI with updated timestamp
      notifyListeners();
    });
  }
  
  void stopTimestampUpdates() {
    _timestampTimer?.cancel();
    _timestampTimer = null;
  }
  
  // Test helpers (only available in debug mode)
  @visibleForTesting
  Timer? get timestampTimer => _timestampTimer;
  
  @visibleForTesting
  void setLastUpdateTimeForTesting(DateTime? time) {
    _lastUpdateTime = time;
  }
  
  @visibleForTesting
  void setLastManualRefreshTimeForTesting(DateTime? time) {
    _lastManualRefreshTime = time;
  }
  
  /// Track user activity for intelligent refresh decisions
  void trackUserActivity() {
    _lastUserActivity = DateTime.now();
    debugPrint('👤 User activity tracked: $_lastUserActivity');
  }
  
  /// Check if user is currently active
  bool get isUserActive {
    if (_lastUserActivity == null) return true; // Assume active if no tracking yet
    final timeSinceActivity = DateTime.now().difference(_lastUserActivity!);
    return timeSinceActivity < _userActiveThreshold;
  }
  
  /// Clear user activity (called when app goes to background)
  void clearUserActivity() {
    _lastUserActivity = null;
    debugPrint('😴 User activity cleared (app backgrounded)');
  }
  
  /// Safely parse timestamp string, handling both UTC and local formats
  DateTime? _parseTimestampSafely(String timestampStr) {
    try {
      final parsed = DateTime.parse(timestampStr);
      
      // Handle the transition period where we have mixed timestamp formats:
      // 1. Legacy UTC timestamps from database (need conversion)
      // 2. New local timestamps stored in our app (no conversion needed)
      
      if (timestampStr.contains('Z') || timestampStr.contains('+00:00')) {
        // This timestamp has UTC format markers
        // But we need to determine if it's actually UTC or local time stored with UTC format
        
        // Strategy: Check if this would result in a "future" timestamp when converted
        final convertedToLocal = parsed.toLocal();
        final now = DateTime.now();
        
        // If converting to local puts us more than 6 hours in the future, 
        // it's likely local time stored with UTC format - don't convert
        if (convertedToLocal.isAfter(now.add(Duration(hours: 6)))) {
          debugPrint('🕰️ Detected local time with UTC format: $timestampStr');
          // Treat as local time - parse without timezone conversion
          final localStr = timestampStr.replaceAll('+00:00', '').replaceAll('Z', '');
          return DateTime.parse(localStr);
        } else {
          // Likely genuine UTC timestamp - convert to local
          debugPrint('🕰️ Detected genuine UTC timestamp: $timestampStr');
          return convertedToLocal;
        }
      }
      
      // No UTC markers - treat as local time
      return parsed;
    } catch (e) {
      debugPrint('⚠️ Error parsing timestamp $timestampStr: $e');
      return null;
    }
  }
  
  /// Check if insights are already loaded for the given baby
  bool hasInsightsForBaby(String babyId) {
    return _currentBabyId == babyId && _insights.isNotEmpty && _lastUpdateTime != null;
  }
  
  /// Clear cache for problematic data
  Future<void> clearCacheForBaby(String babyId) async {
    try {
      debugPrint('🧝 Clearing all cache for baby: $babyId');
      await _aiAnalysisService.clearCachedInsights(babyId);
      
      // Also clear in-memory cache if it's the current baby
      if (_currentBabyId == babyId) {
        _insights = {};
        _overallSummary = null;
        _lastUpdateTime = null;
        _lastManualRefreshTime = null;
        debugPrint('✅ In-memory cache cleared for current baby');
        notifyListeners(); // Notify UI to update
      }
    } catch (e) {
      debugPrint('❌ Error clearing cache: $e');
    }
  }
  
  /// Public method to force clear all cache and reset state (useful for debugging)
  Future<void> forceResetCache(String babyId) async {
    debugPrint('🗞️ Force reset requested for baby: $babyId');
    await clearCacheForBaby(babyId);
    
    // Also clear any persisted timestamps
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('ai_manual_refresh_$babyId');
      debugPrint('✅ Persisted timestamps cleared');
    } catch (e) {
      debugPrint('❌ Error clearing persisted timestamps: $e');
    }
  }
  
  /// Load insights for a specific baby with professional caching logic
  Future<void> loadInsights(BabyProfile babyProfile) async {
    debugPrint('🔍 loadInsights() called for baby: ${babyProfile.name} (${babyProfile.id})');
    debugPrint('   - Current state: isLoading=$_isLoading, isUpdating=$_isUpdating');
    debugPrint('   - Current baby ID: $_currentBabyId');
    debugPrint('   - Has insights: ${_insights.isNotEmpty}');
    debugPrint('   - Last update: $_lastUpdateTime');
    
    if (_isLoading || _isUpdating) {
      debugPrint('⚠️ Already loading insights, skipping duplicate request');
      return;
    }
    
    // CRITICAL: Prevent multiple simultaneous calls from different screens
    if (_isUpdating) {
      debugPrint('⚠️ AI insights update in progress, skipping duplicate request');
      return;
    }
    
    // If we already have insights for this baby, don't reload unless necessary
    if (hasInsightsForBaby(babyProfile.id)) {
      debugPrint('✅ Insights already loaded for baby ${babyProfile.name} - skipping reload');
      return;
    }
    
    // PROFESSIONAL: Check cached insights first, then determine if refresh is needed
    final cachedInsights = await _aiAnalysisService.getCachedInsights(babyProfile.id);
    if (cachedInsights != null) {
      debugPrint('✅ Found cached insights, checking if refresh is needed');
      
      // Use cached insights by default
      _insights = cachedInsights;
      _overallSummary = cachedInsights['overallSummary'] is Map<String, dynamic> 
          ? cachedInsights['overallSummary'] as Map<String, dynamic> : null;
      _currentBabyId = babyProfile.id;
      
      // Parse timestamp safely
      final cachedTimestamp = cachedInsights['lastUpdated'] as String?;
      if (cachedTimestamp != null) {
        _lastUpdateTime = _parseTimestampSafely(cachedTimestamp);
      }
      
      startTimestampUpdates();
      notifyListeners();
      
      // Only check if we need to refresh if we have significant new data
      // Pass the cached timestamp to the refresh logic
      final shouldAutoRefresh = await _shouldPerformAutoRefresh(babyProfile.id, cachedTimestamp: cachedTimestamp);
      if (!shouldAutoRefresh.allowed) {
        debugPrint('🔄 Using cached insights - ${shouldAutoRefresh.reason}');
        return; // Use cached insights
      }
      
      debugPrint('🔄 Cached insights available but refresh conditions met: ${shouldAutoRefresh.reason}');
      // Continue to refresh below
    } else {
      debugPrint('⚠️ No cached insights available, checking if refresh is allowed');
      
      // Check the number of activity logs before proceeding
      final recentActivities = await _supabaseService.select(
          'activity_logs',
          filters: {'baby_id': babyProfile.id},
          orderBy: 'recorded_at',
          ascending: false,
          limit: 10,
      );

      if (recentActivities.length < 5) {
          debugPrint('⬇️ Skipping AI insight generation - insufficient activity data (${recentActivities.length} activities, need 5+)');
          _wasSkippedDueToInsufficientData = true;
          _insights = _getEmptyInsights();
          _overallSummary = {
              'mainSummary': 'AI is ready to analyze your baby\'s patterns. Log at least 5 activities to unlock AI-powered insights and personalized recommendations.',
              'detailedDescription': 'You have ${recentActivities.length} activities logged. Continue logging to reach the minimum of 5 activities needed for AI analysis.',
              'totalInsights': 0,
              'availableRecommendations': 0,
              'topRecommendations': [],
              'hasData': false,
          };
          _lastUpdateTime = DateTime.now();
          _currentBabyId = babyProfile.id;
          notifyListeners();
          return;
      }
      
      final shouldAutoRefresh = await _shouldPerformAutoRefresh(babyProfile.id);
      if (!shouldAutoRefresh.allowed) {
        debugPrint('🚫 No cached insights and auto-refresh not allowed: ${shouldAutoRefresh.reason}');
        _insights = _getEmptyInsights();
        _overallSummary = _getEmptyInsightsSummary();
        _currentBabyId = babyProfile.id;
        _lastUpdateTime = DateTime.now();
        notifyListeners();
        return;
      }
      
      debugPrint('✅ No cached insights, refresh conditions met: ${shouldAutoRefresh.reason}');
      // Continue to refresh below
    }
    
    debugPrint('✅ Auto-refresh conditions met - proceeding with fresh insights generation');
    
    try {
      _setLoading(true);
      _currentBabyId = babyProfile.id;
      
      debugPrint('🔄 Loading AI insights for baby: ${babyProfile.name} (ID: ${babyProfile.id})');
      
      // Track app open for user activity
      trackUserActivity();
      
      // Load persisted manual refresh timestamp
      await _loadPersistedManualRefreshTime(babyProfile.id);
      
      // Since auto-refresh conditions are met, we should generate fresh insights
      // Skip cache validation and proceed directly to AI generation
      debugPrint('🔄 Auto-refresh approved - generating fresh insights...');
      
      // Step 3: Generate fresh insights (cache miss or invalid)
      debugPrint('🔄 Generating fresh AI insights...');
      
      // Check if we have sufficient activity logs before calling OpenAI (minimum 5 activities)
      final recentActivities = await _supabaseService.select(
        'activity_logs',
        filters: {'baby_id': babyProfile.id},
        orderBy: 'recorded_at',
        ascending: false,
        limit: 10, // Check for sufficient activities
      );
      
      if (recentActivities.length < 5) {
        debugPrint('⬇️ Skipping AI insight generation - insufficient activity data (${recentActivities.length} activities, need 5+)');
        _insights = _getEmptyInsights();
        _overallSummary = {
          'mainSummary': 'AI is ready to analyze your baby\'s patterns. Log at least 5 activities to unlock AI-powered insights and personalized recommendations.',
          'detailedDescription': 'You have ${recentActivities.length} activities logged. Continue logging to reach the minimum of 5 activities needed for AI analysis.',
          'totalInsights': 0,
          'availableRecommendations': 0,
          'topRecommendations': [],
          'hasData': false,
        };
        _lastUpdateTime = DateTime.now();
        startTimestampUpdates();
        return;
      }
      
      final insights = await _aiAnalysisService.generateComprehensiveInsights(
        babyId: babyProfile.id,
        babyProfile: babyProfile,
        forceRefresh: true, // Always force refresh when we get here
      );
      
      // Note: AI service automatically stores insights when generating fresh ones
      // No need to store again here to avoid duplicates
      
      // For fresh insights, use current time
      final finalTimestamp = DateTime.now();
      debugPrint('🆕 Using current time for fresh insights: $finalTimestamp');
      
      // PROFESSIONAL: Save both generation and auto-refresh timestamps
      await _saveLastInsightGenerationTime(babyProfile.id, finalTimestamp);
      await _saveLastAutoRefreshTime(babyProfile.id, finalTimestamp);
      
      // Update state with fresh insights
      _insights = insights;
      _wasSkippedDueToInsufficientData = false; // Reset flag since we successfully generated insights
      _overallSummary = insights['overallSummary'] is Map<String, dynamic> 
          ? insights['overallSummary'] as Map<String, dynamic> : null;
      _lastUpdateTime = finalTimestamp;
      
      debugPrint('✅ Fresh insights loaded: ${_insights.keys.length} categories, ${getFormattedInsightsForHome().length} displayable insights');
      debugPrint('📅 Final timestamp: $finalTimestamp');
      
      startTimestampUpdates();
      
    } catch (e) {
      debugPrint('❌ Error loading insights: $e');
      debugPrint('🔧 Stack trace: ${StackTrace.current}');
      _insights = _getEmptyInsights();
      _overallSummary = null;
      _lastUpdateTime = null;
    } finally {
      _setLoading(false);
    }
  }
  
  /// Manual refresh with professional 2-hour cooldown and new data validation
  Future<bool> manualRefresh(BabyProfile babyProfile, {bool forceRefresh = false}) async {
    if (_isUpdating) {
      debugPrint('⚠️ Manual refresh already in progress, skipping duplicate request');
      return false;
    }
    
    // Track user activity
    trackUserActivity();
    
    // PROFESSIONAL: 4-hour cooldown for manual refresh button (skip if forceRefresh is true for dev mode)
    if (!forceRefresh) {
      final lastManualRefreshTime = _lastManualRefreshTime;
      if (lastManualRefreshTime != null) {
        final now = DateTime.now();
        final timeSinceLastManualRefresh = now.difference(lastManualRefreshTime);
        if (timeSinceLastManualRefresh < _manualRefreshCooldown) {
          final hoursRemaining = (_manualRefreshCooldown.inHours - timeSinceLastManualRefresh.inHours);
          final minutesRemaining = (_manualRefreshCooldown.inMinutes - timeSinceLastManualRefresh.inMinutes) % 60;
          debugPrint('🚫 Manual refresh BLOCKED - Only ${timeSinceLastManualRefresh.inMinutes} minutes since last manual refresh');
          debugPrint('   Manual refresh cooldown: ${_manualRefreshCooldown.inHours} hours');
          debugPrint('   Time remaining: ${hoursRemaining}h ${minutesRemaining}m');
          return false;
        }
      }
    }
    
    // Check if there's new data to analyze (unless forcing refresh)
    if (!forceRefresh && _lastUpdateTime != null) {
      final hasNewData = await _aiAnalysisService.hasNewActivitiesSince(babyProfile.id, _lastUpdateTime!);
      if (!hasNewData) {
        debugPrint('⚠️ Manual refresh skipped: No new activity logs since last update');
        return false; // Special return value to indicate "no new data"
      }
    }
    
    if (forceRefresh) {
      debugPrint('🔧 Dev mode: Rate limiting bypassed for forced refresh');
    }
    
    try {
      _setUpdating(true);
      
      debugPrint('🔄 Manual AI insights refresh requested for baby: ${babyProfile.name}');
      
      // Check if we have sufficient activity logs before calling OpenAI for manual refresh (minimum 5 activities)
      final recentActivities = await _supabaseService.select(
        'activity_logs',
        filters: {'baby_id': babyProfile.id},
        orderBy: 'recorded_at',
        ascending: false,
        limit: 10, // Check for sufficient activities
      );
      
      if (recentActivities.length < 5) {
        debugPrint('⬇️ Skipping manual AI refresh - insufficient activity data (${recentActivities.length} activities, need 5+)');
        _insights = _getEmptyInsights();
        _overallSummary = {
          'mainSummary': 'AI is ready to analyze your baby\'s patterns. Log at least 5 activities to unlock AI-powered insights and personalized recommendations.',
          'detailedDescription': 'You have ${recentActivities.length} activities logged. Continue logging to reach the minimum of 5 activities needed for AI analysis.',
          'totalInsights': 0,
          'availableRecommendations': 0,
          'topRecommendations': [],
          'hasData': false,
        };
        _lastUpdateTime = DateTime.now();
        _lastManualRefreshTime = DateTime.now();
        startTimestampUpdates();
        return true; // Return true to indicate "success" with empty insights
      }
      
      // Clear cached insights to force fresh generation
      debugPrint('🗱️ Clearing cache to ensure fresh insights generation');
      await _aiAnalysisService.clearCachedInsights(babyProfile.id);
      
      // Force fresh insights generation (cache now cleared)
      final insights = await _aiAnalysisService.generateComprehensiveInsights(
        babyId: babyProfile.id,
        babyProfile: babyProfile,
      );
      
      // Note: AI service automatically stores insights when generating fresh ones
      // No need to store again here to avoid duplicates
      
      // For manual refresh, always use current time to ensure accuracy
      final now = DateTime.now();
      
      // PROFESSIONAL: Save generation timestamp for manual refresh cooldown
      await _saveLastInsightGenerationTime(babyProfile.id, now);
      // Note: Don't save auto-refresh timestamp for manual refresh
      
      // Update state with safe casting
      _insights = insights;
      _overallSummary = insights['overallSummary'] is Map<String, dynamic> 
          ? insights['overallSummary'] as Map<String, dynamic> : null;
      _lastManualRefreshTime = now;
      _lastUpdateTime = now; // Always use current time for manual refresh
      
      // Persist manual refresh timestamp
      await _saveManualRefreshTime(babyProfile.id, now);
      
      debugPrint('✅ Manual refresh completed successfully at $now');
      debugPrint('   - New insights available: ${_insights.isNotEmpty}');
      debugPrint('   - Overall summary updated: ${_overallSummary != null}');
      
      // Start timestamp updates for dynamic display
      startTimestampUpdates();
      
      return true;
      
    } catch (e) {
      debugPrint('❌ Error during manual refresh: $e');
      debugPrint('🔧 Stack trace: ${StackTrace.current}');
      return false;
    } finally {
      _setUpdating(false);
    }
  }
  
  /// Check if manual refresh is allowed (not rate limited)
  bool canManualRefresh() {
    final lastManualRefreshTime = _lastManualRefreshTime;
    if (lastManualRefreshTime == null) return true;
    
    final timeSinceLastManualRefresh = DateTime.now().difference(lastManualRefreshTime);
    return timeSinceLastManualRefresh >= _manualRefreshCooldown;
  }
  
  /// Get time remaining until next manual refresh is allowed
  Duration? getTimeUntilNextRefresh() {
    final lastManualRefreshTime = _lastManualRefreshTime;
    if (lastManualRefreshTime == null) return null;
    
    final timeSinceLastManualRefresh = DateTime.now().difference(lastManualRefreshTime);
    if (timeSinceLastManualRefresh >= _manualRefreshCooldown) return null;
    
    return _manualRefreshCooldown - timeSinceLastManualRefresh;
  }
  
  /// Professional auto-refresh decision logic with strict conditions
  Future<({bool allowed, String reason})> _shouldPerformAutoRefresh(String babyId, {String? cachedTimestamp}) async {
    try {
      // ENHANCED Condition 1: User must be logged in first
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        return (allowed: false, reason: 'User not logged in - preventing API costs for deleted accounts');
      }
      
      // Condition 2: User must be active (logged in and recently active)
      if (!isUserActive) {
        return (allowed: false, reason: 'User not active - preventing API costs for inactive users');
      }
      
      // ENHANCED Condition 3: If we have cached insights, use their timestamp for comparison
      DateTime? referenceTime;
      if (cachedTimestamp != null) {
        referenceTime = _parseTimestampSafely(cachedTimestamp);
        debugPrint('📅 Using cached insights timestamp as reference: $referenceTime');
      } else {
        referenceTime = await _getLastAutoRefreshTime(babyId);
        debugPrint('📅 Using stored auto-refresh timestamp as reference: $referenceTime');
      }
      
      if (referenceTime != null) {
        final timeSinceLastRefresh = DateTime.now().difference(referenceTime);
        if (timeSinceLastRefresh < _autoRefreshWindow) {
          return (allowed: false, reason: 'Within 24-hour auto-refresh window (${timeSinceLastRefresh.inHours}h ago)');
        }
        
        // ENHANCED: Even if 24+ hours passed, still require 5+ new activities
        final hasNewLogs = await _hasSignificantNewLogs(babyId, referenceTime);
        if (!hasNewLogs) {
          return (allowed: false, reason: 'Over 24 hours since last refresh but less than 5 new activities - no API call needed');
        }
      }
      
      // Condition 4: Must have new activity logs since last refresh (fallback check)
      final lastRefreshTime = referenceTime ?? await _getLastInsightGenerationTime(babyId);
      if (lastRefreshTime != null) {
        final hasNewLogs = await _hasSignificantNewLogs(babyId, lastRefreshTime);
        if (!hasNewLogs) {
          return (allowed: false, reason: 'No significant new activity logs since last refresh');
        }
      }
      
      // Condition 5: Check if we have sufficient activity logs (minimum 5)
      final recentActivities = await _supabaseService.select(
        'activity_logs',
        filters: {'baby_id': babyId},
        orderBy: 'recorded_at',
        ascending: false,
        limit: 10,
      );
      
      if (recentActivities.length < 5) {
        return (allowed: false, reason: 'Insufficient activity logs for analysis (${recentActivities.length} activities, need 5+)');
      }
      
      // All conditions met
      return (allowed: true, reason: 'User active, 24h+ since last refresh, new logs available');
      
    } catch (e) {
      debugPrint('❌ Error checking auto-refresh conditions: $e');
      return (allowed: false, reason: 'Error checking conditions: $e');
    }
  }
  
  /// Check if there are significant new logs since last refresh (5+ activities)
  Future<bool> _hasSignificantNewLogs(String babyId, DateTime since) async {
    try {
      final recentActivities = await _supabaseService.select(
        'activity_logs',
        filters: {'baby_id': babyId},
        orderBy: 'recorded_at',
        ascending: false,
        limit: 10,
      );
      
      int newLogsCount = 0;
      for (final activity in recentActivities) {
        final activityTime = DateTime.parse(activity['recorded_at']);
        if (activityTime.isAfter(since)) {
          newLogsCount++;
        }
      }
      
      debugPrint('📊 Found $newLogsCount new logs since $since');
      return newLogsCount >= 5; // Require at least 5 new activities
      
    } catch (e) {
      debugPrint('❌ Error checking for new logs: $e');
      return false;
    }
  }
  
  /// Generate overall summary from cached insights
  Map<String, dynamic> _generateOverallSummaryFromCache(Map<String, dynamic> cachedInsights) {
    try {
      int totalInsights = 0;
      final List<String> analysisItems = [];
      final List<String> recommendationItems = [];
      
      // Count valid insights from cache
      for (final key in ['sleepAnalysis', 'feedingAnalysis', 'growthAnalysis', 'milestoneAnalysis']) {
        final analysisField = cachedInsights[key];
        final Map<String, dynamic>? analysis;
        if (analysisField is Map<String, dynamic>) {
          analysis = analysisField;
        } else if (analysisField is Map) {
          analysis = Map<String, dynamic>.from(analysisField);
        } else {
          analysis = null;
        }
        
        if (analysis != null) {
          final confidence = (analysis['confidence'] as num?) ?? 0;
          if (confidence > 0.2) {
            totalInsights++;
            
            switch (key) {
              case 'sleepAnalysis':
                analysisItems.add('Sleep patterns analyzed');
                break;
              case 'feedingAnalysis':
                analysisItems.add('Feeding schedule reviewed');
                break;
              case 'growthAnalysis':
                analysisItems.add('Growth trends tracked');
                break;
              case 'milestoneAnalysis':
                analysisItems.add('Milestones predicted');
                break;
            }
            
            // Extract recommendations with safe casting
            final dataField = analysis['data'];
            final Map<String, dynamic>? data;
            if (dataField is Map<String, dynamic>) {
              data = dataField;
            } else if (dataField is Map) {
              data = Map<String, dynamic>.from(dataField);
            } else {
              data = null;
            }
            
            final recommendations = data?['recommendations'] as List? ?? [];
            if (recommendations.isNotEmpty) {
              recommendationItems.addAll(recommendations.cast<String>().take(2));
            }
          }
        }
      }
      
      // Generate summary text
      String mainSummary;
      if (analysisItems.isEmpty) {
        mainSummary = 'AI insights are available based on your logged activities.';
      } else {
        final analysisText = analysisItems.join(', ');
        final recCount = recommendationItems.length;
        if (recCount > 0) {
          mainSummary = '$analysisText. $recCount personalized recommendations available.';
        } else {
          mainSummary = '$analysisText. Continue logging for more detailed insights.';
        }
      }
      
      return {
        'mainSummary': mainSummary,
        'detailedDescription': totalInsights > 0 
            ? 'Based on your logged activities, insights are available.' 
            : 'Continue logging activities for personalized insights.',
        'totalInsights': totalInsights,
        'availableRecommendations': recommendationItems.length,
        'topRecommendations': recommendationItems.take(3).toList(),
        'hasData': totalInsights > 0,
      };
    } catch (e) {
      debugPrint('❌ Error generating summary from cache: $e');
      return {
        'mainSummary': 'AI insights are available based on your logged activities.',
        'detailedDescription': 'Continue logging activities for updated insights.',
        'totalInsights': 0,
        'availableRecommendations': 0,
        'topRecommendations': <String>[],
        'hasData': true,
      };
    }
  }
  
  /// Get formatted insights for the Home screen preview
  List<Map<String, dynamic>> getFormattedInsightsForHome() {
    final List<Map<String, dynamic>> formattedInsights = [];
    
    void addInsightIfValid(String type, Map<String, dynamic>? analysis) {
      if (analysis != null) {
        final confidence = ((analysis['confidence'] as num?) ?? 0.0) * 100;
        final description = analysis['description'] as String? ?? 'No description available';
        
        // Skip insights with very low confidence or "not enough data" messages
        if (confidence < 15 && description.toLowerCase().contains('not enough data')) {
          return;
        }
        
        final insight = {
          'type': type,
          'title': analysis['title'] ?? '$type Analysis',
          'description': description,
          'confidence': confidence.toInt(),
          'data': analysis['data'] ?? {},
        };
        
        formattedInsights.add(insight);
      }
    }
    
    // Process each type of insight with safe casting
    final sleepAnalysis = _insights['sleepAnalysis'];
    if (sleepAnalysis is Map<String, dynamic>) {
      addInsightIfValid('sleep', sleepAnalysis);
    } else if (sleepAnalysis is Map) {
      addInsightIfValid('sleep', Map<String, dynamic>.from(sleepAnalysis));
    }
    
    final feedingAnalysis = _insights['feedingAnalysis'];
    if (feedingAnalysis is Map<String, dynamic>) {
      addInsightIfValid('feeding', feedingAnalysis);
    } else if (feedingAnalysis is Map) {
      addInsightIfValid('feeding', Map<String, dynamic>.from(feedingAnalysis));
    }
    
    final growthAnalysis = _insights['growthAnalysis'];
    if (growthAnalysis is Map<String, dynamic>) {
      addInsightIfValid('growth', growthAnalysis);
    } else if (growthAnalysis is Map) {
      addInsightIfValid('growth', Map<String, dynamic>.from(growthAnalysis));
    }
    
    if (_insights.containsKey('milestoneAnalysis')) {
      final milestoneAnalysis = _insights['milestoneAnalysis'];
      if (milestoneAnalysis is Map<String, dynamic>) {
        addInsightIfValid('milestone', milestoneAnalysis);
      } else if (milestoneAnalysis is Map) {
        addInsightIfValid('milestone', Map<String, dynamic>.from(milestoneAnalysis));
      }
    }
    
    return formattedInsights;
  }
  
  /// Get summary text for display
  String getSummaryText() {
    if (_overallSummary != null) {
      final mainSummary = _overallSummary!['mainSummary'] as String? ?? '';
      if (mainSummary.isNotEmpty) {
        return mainSummary;
      }
    }
    
    // If no data available, return ready message
    if (_insights.isEmpty || getFormattedInsightsForHome().isEmpty) {
      return 'AI is ready to analyze your baby\'s patterns. Start logging activities to unlock AI-powered insights and personalized recommendations.';
    }
    
    // Generate summary from available insights
    final insights = getFormattedInsightsForHome();
    final types = insights.map((i) => i['type']).toList();
    
    if (types.isEmpty) {
      return 'Continue logging activities for personalized insights.';
    }
    
    final typeDescriptions = {
      'sleep': 'Sleep patterns analyzed',
      'feeding': 'Feeding schedule reviewed',
      'growth': 'Growth trends tracked',
      'milestone': 'Milestones predicted',
    };
    
    final analyzedTypes = types.map((type) => typeDescriptions[type] ?? type).toList();
    return '${analyzedTypes.join(', ')}. Personalized recommendations available.';
  }
  
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }
  
  void _setUpdating(bool updating) {
    _isUpdating = updating;
    notifyListeners();
  }
  
  Map<String, dynamic> _getEmptyInsights() {
    return {
      'sleepAnalysis': null,
      'feedingAnalysis': null,
      'growthAnalysis': null,
      'milestoneAnalysis': null,
      'lastUpdated': null,
    };
  }
  
  Map<String, dynamic> _getEmptyInsightsSummary() {
    return {
      'mainSummary': 'AI is ready to analyze your baby\'s patterns. Start logging activities to unlock AI-powered insights and personalized recommendations.',
      'detailedDescription': 'Continue logging activities for personalized insights.',
      'totalInsights': 0,
      'availableRecommendations': 0,
      'topRecommendations': <String>[],
      'hasData': false,
    };
  }
  
  /// Persist manual refresh timestamp
  Future<void> _saveManualRefreshTime(String babyId, DateTime timestamp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('ai_manual_refresh_$babyId', timestamp.toString());
      debugPrint('💾 Saved manual refresh timestamp: $timestamp');
    } catch (e) {
      debugPrint('❌ Error saving manual refresh timestamp: $e');
    }
  }
  
  /// Load persisted manual refresh timestamp
  Future<void> _loadPersistedManualRefreshTime(String babyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampStr = prefs.getString('ai_manual_refresh_$babyId');
      if (timestampStr != null) {
        final timestamp = _parseTimestampSafely(timestampStr);
        
        if (timestamp != null) {
          // Only use timestamp if it's not older than 24 hours
          final age = DateTime.now().difference(timestamp);
          if (age.inHours <= 24) {
            _lastManualRefreshTime = timestamp;
          }
        }
      }
    } catch (e) {
      debugPrint('❌ Error loading manual refresh timestamp: $e');
    }
  }
  
  /// Get last insight generation time for rate limiting
  Future<DateTime?> _getLastInsightGenerationTime(String babyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampStr = prefs.getString('ai_last_generation_$babyId');
      if (timestampStr != null) {
        return _parseTimestampSafely(timestampStr);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error loading last generation timestamp: $e');
      return null;
    }
  }
  
  /// Save last insight generation time for rate limiting
  Future<void> _saveLastInsightGenerationTime(String babyId, DateTime timestamp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('ai_last_generation_$babyId', timestamp.toString());
      debugPrint('💾 Saved last generation timestamp: $timestamp');
    } catch (e) {
      debugPrint('❌ Error saving last generation timestamp: $e');
    }
  }
  
  /// Get last auto-refresh time (separate from manual refresh)
  Future<DateTime?> _getLastAutoRefreshTime(String babyId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestampStr = prefs.getString('ai_last_auto_refresh_$babyId');
      if (timestampStr != null) {
        return _parseTimestampSafely(timestampStr);
      }
      return null;
    } catch (e) {
      debugPrint('❌ Error loading last auto-refresh timestamp: $e');
      return null;
    }
  }
  
  /// Save last auto-refresh time (separate from manual refresh)
  Future<void> _saveLastAutoRefreshTime(String babyId, DateTime timestamp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('ai_last_auto_refresh_$babyId', timestamp.toString());
      debugPrint('💾 Saved last auto-refresh timestamp: $timestamp');
    } catch (e) {
      debugPrint('❌ Error saving last auto-refresh timestamp: $e');
    }
  }
  
  /// Optimized cache validation to avoid redundant API calls
  Future<bool> _validateCacheQuality(String babyId, Map<String, dynamic> cachedInsights) async {
    try {
      // 1. Check cache expiration (24 hours)
      final lastUpdated = cachedInsights['lastUpdated'] as String?;
      if (lastUpdated != null) {
        final cacheTimestamp = _parseTimestampSafely(lastUpdated);
        if (cacheTimestamp != null) {
          final cacheAge = DateTime.now().difference(cacheTimestamp);
          if (cacheAge > _cacheValidDuration) {
            debugPrint('🔄 Cache expired (${cacheAge.inHours}h old)');
            return false;
          }
          
          // 2. Check for SIGNIFICANT new activity data since last cache
          // Only invalidate cache if there are 5+ new activities to avoid excessive API calls
          final recentActivities = await _supabaseService.select(
            'activity_logs',
            filters: {'baby_id': babyId},
            orderBy: 'recorded_at',
            ascending: false,
            limit: 10,
          );
          
          int newActivitiesCount = 0;
          for (final activity in recentActivities) {
            final activityTime = DateTime.parse(activity['recorded_at']);
            if (activityTime.isAfter(cacheTimestamp)) {
              newActivitiesCount++;
            }
          }
          
          if (newActivitiesCount >= 5) {
            debugPrint('🔄 Significant new activities detected since cache ($newActivitiesCount activities)');
            return false;
          } else if (newActivitiesCount > 0) {
            debugPrint('✅ Minor new activities since cache ($newActivitiesCount), keeping cache valid');
          }
          
          debugPrint('✅ Cache is valid and current (age: ${cacheAge.inMinutes} minutes)');
          return true;
        }
      }
      
      // If we can't parse timestamp, consider cache invalid
      debugPrint('⚠️ Cannot parse cache timestamp, considering invalid');
      return false;
      
    } catch (e) {
      debugPrint('❌ Error validating cache quality: $e');
      // On error, consider cache invalid to be safe
      return false;
    }
  }
  
  @override
  void dispose() {
    stopTimestampUpdates();
    super.dispose();
  }
}
