import 'package:flutter/foundation.dart';
import '../models/enums.dart';

/// Strategy interface for handling different types of subscription errors
abstract class SubscriptionErrorHandler {
  String getErrorMessage();
  bool shouldRetry();
  Duration? getRetryDelay();
  void logError(dynamic error, StackTrace? stackTrace);
}

/// Handles network-related subscription errors
class NetworkErrorHandler implements SubscriptionErrorHandler {
  final dynamic originalError;
  
  NetworkErrorHandler(this.originalError);
  
  @override
  String getErrorMessage() => 'Network error. Please check your connection and try again.';
  
  @override
  bool shouldRetry() => true;
  
  @override
  Duration? getRetryDelay() => const Duration(seconds: 3);
  
  @override
  void logError(dynamic error, StackTrace? stackTrace) {
    debugPrint('Network error in subscription: $error');
    if (stackTrace != null) debugPrint('Stack trace: $stackTrace');
  }
}

/// Handles purchase-related errors
class PurchaseErrorHandler implements SubscriptionErrorHandler {
  final dynamic originalError;
  
  PurchaseErrorHandler(this.originalError);
  
  @override
  String getErrorMessage() => 'Unable to complete purchase. Please try again or contact support.';
  
  @override
  bool shouldRetry() => false; // Don't auto-retry purchases
  
  @override
  Duration? getRetryDelay() => null;
  
  @override
  void logError(dynamic error, StackTrace? stackTrace) {
    debugPrint('Purchase error: $error');
    // Could integrate with crash reporting service here
  }
}

/// Factory for creating appropriate error handlers
class SubscriptionErrorHandlerFactory {
  static SubscriptionErrorHandler createHandler(SubscriptionError errorType, dynamic originalError) {
    switch (errorType) {
      case SubscriptionError.networkError:
        return NetworkErrorHandler(originalError);
      case SubscriptionError.purchaseFailed:
      case SubscriptionError.userCancelled:
        return PurchaseErrorHandler(originalError);
      default:
        return GenericErrorHandler(originalError);
    }
  }
}

/// Generic error handler for unspecified errors
class GenericErrorHandler implements SubscriptionErrorHandler {
  final dynamic originalError;
  
  GenericErrorHandler(this.originalError);
  
  @override
  String getErrorMessage() => 'An unexpected error occurred. Please try again.';
  
  @override
  bool shouldRetry() => false;
  
  @override
  Duration? getRetryDelay() => null;
  
  @override
  void logError(dynamic error, StackTrace? stackTrace) {
    debugPrint('Generic subscription error: $error');
  }
}