import 'package:shared_preferences/shared_preferences.dart';

class SettingsService {
  static const String _timeFormatKey = 'time_format_24h';
  static const String _metricUnitsKey = 'metric_units';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _autoLockEnabledKey = 'auto_lock_enabled';
  static const String _backgroundSyncKey = 'background_sync';
  static const String _analyticsEnabledKey = 'analytics_enabled';
  static const String _selectedThemeKey = 'selected_theme';

  static SettingsService? _instance;
  static SettingsService get instance => _instance ??= SettingsService._();
  
  SettingsService._();

  SharedPreferences? _prefs;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  SharedPreferences get prefs {
    if (_prefs == null) {
      throw Exception('SettingsService not initialized. Call init() first.');
    }
    return _prefs!;
  }

  // Time format settings
  bool get is24HourFormat => prefs.getBool(_timeFormatKey) ?? _getSystemTimeFormat();
  
  Future<void> setTimeFormat(bool is24Hour) async {
    await prefs.setBool(_timeFormatKey, is24Hour);
  }

  // Metric units settings
  bool get isMetric => prefs.getBool(_metricUnitsKey) ?? true;
  
  Future<void> setMetricUnits(bool isMetric) async {
    await prefs.setBool(_metricUnitsKey, isMetric);
  }

  // Biometric settings
  bool get biometricEnabled => prefs.getBool(_biometricEnabledKey) ?? false;
  
  Future<void> setBiometricEnabled(bool enabled) async {
    await prefs.setBool(_biometricEnabledKey, enabled);
  }

  // Auto lock settings
  bool get autoLockEnabled => prefs.getBool(_autoLockEnabledKey) ?? true;
  
  Future<void> setAutoLockEnabled(bool enabled) async {
    await prefs.setBool(_autoLockEnabledKey, enabled);
  }

  // Background sync settings
  bool get backgroundSync => prefs.getBool(_backgroundSyncKey) ?? true;
  
  Future<void> setBackgroundSync(bool enabled) async {
    await prefs.setBool(_backgroundSyncKey, enabled);
  }

  // Analytics settings
  bool get analyticsEnabled => prefs.getBool(_analyticsEnabledKey) ?? true;
  
  Future<void> setAnalyticsEnabled(bool enabled) async {
    await prefs.setBool(_analyticsEnabledKey, enabled);
  }

  // Theme settings
  String get selectedTheme => prefs.getString(_selectedThemeKey) ?? 'System';
  
  Future<void> setSelectedTheme(String theme) async {
    await prefs.setString(_selectedThemeKey, theme);
  }


  // Get system time format preference
  bool _getSystemTimeFormat() {
    // Use system locale to determine default time format
    // For now, default to 12-hour format as it's more common in many regions
    return false; // false = 12-hour, true = 24-hour
  }

  // Clear all settings
  Future<void> clearAll() async {
    await prefs.clear();
  }
}
