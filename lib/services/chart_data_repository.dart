import '../models/baby_profile.dart';
import '../services/who_percentile_service.dart';

/// Abstract repository for chart data operations
abstract class ChartDataRepository {
  Future<List<Map<String, dynamic>>> getPercentileData({
    required String percentile,
    required String dateRange,
    required String measurementType,
    required String gender,
  });
  
  double calculatePercentile({
    required double value,
    required double ageInMonths,
    required String measurementType,
    required String gender,
  });
}

/// Implementation using WHO percentile service
class WHOChartDataRepository implements ChartDataRepository {
  final WHOPercentileService _percentileService;
  
  const WHOChartDataRepository(this._percentileService);
  
  @override
  Future<List<Map<String, dynamic>>> getPercentileData({
    required String percentile,
    required String dateRange,
    required String measurementType,
    required String gender,
  }) async {
    try {
      final ageRange = WHOPercentileService.getAgeRange(dateRange);
      
      return ageRange.map((age) {
        final value = WHOPercentileService.getPercentileValue(
          percentile,
          age,
          measurementType,
          gender,
        );
        return {
          'age': age,
          'value': value,
          'percentile': percentile,
        };
      }).toList();
    } catch (e) {
      throw ChartDataException('Failed to load percentile data: $e');
    }
  }
  
  @override
  double calculatePercentile({
    required double value,
    required double ageInMonths,
    required String measurementType,
    required String gender,
  }) {
    return WHOPercentileService.calculatePercentile(
      value,
      ageInMonths,
      measurementType,
      gender,
    );
  }
}

/// Custom exception for chart data operations
class ChartDataException implements Exception {
  final String message;
  const ChartDataException(this.message);
  
  @override
  String toString() => 'ChartDataException: $message';
}