import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

/// Cache service for AI chat responses to reduce API calls and costs
class ChatCacheService {
  static const String _cacheKeyPrefix = 'chat_cache_';
  static const String _cacheTimestampPrefix = 'chat_timestamp_';
  static const Duration _cacheValidityDuration = Duration(days: 1); // 24 hours cache

  /// Generate a unique cache key for a message with conversation context
  static String _generateCacheKey(String message, String babyId, [String? contextHash]) {
    // Create a more unique identifier by including baby ID, normalized message, and context
    final cleanMessage = message.toLowerCase().trim().replaceAll(RegExp(r'\s+'), ' ');
    
    // Create base key with baby ID and message
    String baseKey = '$babyId|$cleanMessage';
    
    // Add context hash if provided (to differentiate same questions in different conversations)
    if (contextHash != null && contextHash.isNotEmpty) {
      baseKey += '|$contextHash';
    }
    
    // Use a more robust hash (combining multiple hash codes to reduce collisions)
    final hash1 = baseKey.hashCode;
    final hash2 = cleanMessage.hashCode;
    final hash3 = babyId.hashCode;
    final combinedHash = (hash1 ^ hash2 ^ hash3).abs();
    
    return '${_cacheKeyPrefix}$combinedHash';
  }

  /// Get cached response for a message
  static Future<String?> getCachedResponse(String message, String babyId, {String? contextHash}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _generateCacheKey(message, babyId, contextHash);
      final timestampKey = '${_cacheTimestampPrefix}${cacheKey}';
      
      print('🔍 Checking cache for key: $cacheKey');
      
      // Check if cache exists
      final cachedResponse = prefs.getString(cacheKey);
      final cachedTimestamp = prefs.getInt(timestampKey);
      
      if (cachedResponse == null || cachedTimestamp == null) {
        print('❌ No cache found for message: ${message.length > 50 ? message.substring(0, 50) : message}...');
        return null;
      }
      
      // Check if cache is still valid (within 24 hours)
      final cacheTime = DateTime.fromMillisecondsSinceEpoch(cachedTimestamp);
      final now = DateTime.now();
      
      if (now.difference(cacheTime) > _cacheValidityDuration) {
        // Cache expired, remove it
        print('⏰ Cache expired for message: ${message.length > 50 ? message.substring(0, 50) : message}...');
        await _removeCachedResponse(cacheKey, timestampKey, prefs);
        return null;
      }
      
      print('✅ Cache hit for message: ${message.length > 50 ? message.substring(0, 50) : message}...');
      return cachedResponse;
    } catch (e) {
      print('Error retrieving cached response: $e');
      return null;
    }
  }

  /// Cache a response for a message
  static Future<void> cacheResponse(String message, String babyId, String response, {String? contextHash}) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cacheKey = _generateCacheKey(message, babyId, contextHash);
      final timestampKey = '${_cacheTimestampPrefix}${cacheKey}';
      
      // Store response and timestamp
      await prefs.setString(cacheKey, response);
      await prefs.setInt(timestampKey, DateTime.now().millisecondsSinceEpoch);
      
      print('💾 Cached response for message: ${message.length > 50 ? message.substring(0, 50) : message}... (key: $cacheKey)');
    } catch (e) {
      print('Error caching response: $e');
    }
  }

  /// Remove expired cache entry
  static Future<void> _removeCachedResponse(String cacheKey, String timestampKey, SharedPreferences prefs) async {
    await prefs.remove(cacheKey);
    await prefs.remove(timestampKey);
  }

  /// Clear all chat cache (useful for debugging or storage management)
  static Future<void> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      final cacheKeys = keys.where((key) => 
          key.startsWith(_cacheKeyPrefix) || 
          key.startsWith(_cacheTimestampPrefix)
      ).toList();
      
      for (final key in cacheKeys) {
        await prefs.remove(key);
      }
      
      print('🗑️ Cleared ${cacheKeys.length} chat cache entries');
    } catch (e) {
      print('Error clearing chat cache: $e');
    }
  }

  /// Get cache statistics for debugging
  static Future<Map<String, dynamic>> getCacheStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs.getKeys();
      
      final cacheKeys = keys.where((key) => key.startsWith(_cacheKeyPrefix)).toList();
      final timestampKeys = keys.where((key) => key.startsWith(_cacheTimestampPrefix)).toList();
      
      int validEntries = 0;
      int expiredEntries = 0;
      
      for (final timestampKey in timestampKeys) {
        final timestamp = prefs.getInt(timestampKey);
        if (timestamp != null) {
          final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
          if (DateTime.now().difference(cacheTime) <= _cacheValidityDuration) {
            validEntries++;
          } else {
            expiredEntries++;
          }
        }
      }
      
      return {
        'totalEntries': cacheKeys.length,
        'validEntries': validEntries,
        'expiredEntries': expiredEntries,
        'cacheValidityHours': _cacheValidityDuration.inHours,
      };
    } catch (e) {
      return {
        'error': e.toString(),
      };
    }
  }
}
