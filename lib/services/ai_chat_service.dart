import '../models/activity_log.dart';
import '../models/baby_profile.dart';
import './openai_client.dart';
import './openai_service.dart';
import './supabase_service.dart';
import './chat_cache_service.dart';
import 'package:flutter/foundation.dart';
import 'dart:math';

class AIChatService {
  static final AIChatService _instance = AIChatService._internal();
  OpenAIClient? _openAIClient;
  final SupabaseService _supabaseService = SupabaseService();

  factory AIChatService() {
    return _instance;
  }

  AIChatService._internal();

  Future<OpenAIClient> get _client async {
    if (_openAIClient == null) {
      final openAIService = OpenAIService();
      await openAIService.initialize();
      final dio = await openAIService.dio;
      _openAIClient = OpenAIClient(dio);
    }
    return _openAIClient!;
  }

  /// Get AI response for parenting questions
  Future<String> getChatResponse({
    required String userMessage,
    required BabyProfile babyProfile,
    List<ActivityLog>? recentActivities,
    List<Map<String, String>>? conversationHistory,
  }) async {
    try {
      // Create context hash from recent conversation for cache differentiation
      final contextHash = _createContextHash(conversationHistory);
      
      // Check cache first
      final cachedResponse = await ChatCacheService.getCachedResponse(
        userMessage, 
        babyProfile.id,
        contextHash: contextHash,
      );
      if (cachedResponse != null) {
        print('🎯 Using cached response for: ${userMessage.length > 50 ? userMessage.substring(0, 50) : userMessage}...');
        return cachedResponse;
      }

      final systemPrompt = _buildSystemPrompt(babyProfile, recentActivities);
      final messages = <Message>[
        Message(role: 'system', content: systemPrompt),
      ];

      // Add conversation history if provided
      if (conversationHistory != null) {
        for (final message in conversationHistory) {
          messages.add(Message(
            role: message['role']!,
            content: message['content']!,
          ));
        }
      }

      // Add current user message
      messages.add(Message(role: 'user', content: userMessage));

      final completion = await (await _client).createChatCompletion(
        messages: messages,
        model: 'gpt-4o-mini',
        options: {
          'max_completion_tokens': 1000,
        },
      );

      final response = completion.text;
      
      // Cache the response for future use
      await ChatCacheService.cacheResponse(
        userMessage, 
        babyProfile.id, 
        response,
        contextHash: contextHash,
      );

      return response;
    } catch (e) {
      throw Exception('Failed to get AI chat response: $e');
    }
  }

  /// Stream AI response for real-time chat
  Stream<String> streamChatResponse({
    required String userMessage,
    required BabyProfile babyProfile,
    List<ActivityLog>? recentActivities,
    List<Map<String, String>>? conversationHistory,
  }) async* {
    try {
      // Create context hash from recent conversation for cache differentiation
      final contextHash = _createContextHash(conversationHistory);
      
      // Check cache first
      final cachedResponse = await ChatCacheService.getCachedResponse(
        userMessage, 
        babyProfile.id,
        contextHash: contextHash,
      );
      if (cachedResponse != null) {
        print('🎯 Using cached response for: ${userMessage.length > 50 ? userMessage.substring(0, 50) : userMessage}...');
        
        // Stream cached response in chunks to simulate real-time experience
        final words = cachedResponse.split(' ');
        for (int i = 0; i < words.length; i++) {
          if (i == 0) {
            yield words[i];
          } else {
            yield ' ${words[i]}';
          }
          // Small delay to simulate typing
          await Future.delayed(const Duration(milliseconds: 50));
        }
        return;
      }

      final systemPrompt = _buildSystemPrompt(babyProfile, recentActivities);
      final messages = <Message>[
        Message(role: 'system', content: systemPrompt),
      ];

      // Add conversation history if provided
      if (conversationHistory != null) {
        for (final message in conversationHistory) {
          messages.add(Message(
            role: message['role']!,
            content: message['content']!,
          ));
        }
      }

      // Add current user message
      messages.add(Message(role: 'user', content: userMessage));

      // Stream the response (simplified version)
      String fullResponse = '';
      try {
        final completion = await (await _client).createChatCompletion(
          messages: messages,
          model: 'gpt-4o-mini',
          options: {
            'max_completion_tokens': 1000,
          },
        );

        final response = completion.text;
        fullResponse = response;
        
        // Cache the response for future use
        await ChatCacheService.cacheResponse(
          userMessage, 
          babyProfile.id, 
          response,
          contextHash: contextHash,
        );
        
        // Stream response in chunks
        final words = response.split(' ');
        for (int i = 0; i < words.length; i++) {
          if (i == 0) {
            yield words[i];
          } else {
            yield ' ${words[i]}';
          }
          await Future.delayed(const Duration(milliseconds: 100));
        }
      } catch (e) {
        print('Error in streamChatResponse: $e');
        yield 'I apologize, but I encountered an error while processing your request. Please try again.';
      }
    } catch (e) {
      debugPrint('❌ AI Chat Service Error: $e');
      yield 'I apologize, but I encountered an error: ${e.toString()}. Please check your internet connection and try again.';
    }
  }

  /// Get quick advice for specific topics
  Future<String> getQuickAdvice({
    required String topic,
    required BabyProfile babyProfile,
    String? specificQuestion,
  }) async {
    try {
      final prompt =
          _buildQuickAdvicePrompt(topic, babyProfile, specificQuestion);

      final completion = await (await _client).createChatCompletion(
        messages: [
          Message(role: 'system', content: _getQuickAdviceSystemPrompt()),
          Message(role: 'user', content: prompt),
        ],
        model: 'o4-mini',
        options: {
          'max_completion_tokens': 500,
        },
      );

      return completion.text;
    } catch (e) {
      throw Exception('Failed to get quick advice: $e');
    }
  }

  /// Generate feeding advice
  Future<String> getFeedingAdvice({
    required BabyProfile babyProfile,
    List<ActivityLog>? recentFeedings,
    String? concern,
  }) async {
    try {
      final prompt =
          _buildFeedingAdvicePrompt(babyProfile, recentFeedings, concern);

      final completion = await (await _client).createChatCompletion(
        messages: [
          Message(role: 'system', content: _getFeedingAdviceSystemPrompt()),
          Message(role: 'user', content: prompt),
        ],
        model: 'o4-mini',
        options: {},
      );

      return completion.text;
    } catch (e) {
      throw Exception('Failed to get feeding advice: $e');
    }
  }

  /// Generate sleep advice
  Future<String> getSleepAdvice({
    required BabyProfile babyProfile,
    List<ActivityLog>? recentSleep,
    String? concern,
  }) async {
    try {
      final prompt = _buildSleepAdvicePrompt(babyProfile, recentSleep, concern);

      final completion = await (await _client).createChatCompletion(
        messages: [
          Message(role: 'system', content: _getSleepAdviceSystemPrompt()),
          Message(role: 'user', content: prompt),
        ],
        model: 'o4-mini',
        options: {},
      );

      return completion.text;
    } catch (e) {
      throw Exception('Failed to get sleep advice: $e');
    }
  }

  /// Store chat message in database for persistence
  Future<void> storeChatMessage({
    required String babyId,
    required String content,
    required bool isFromUser,
    required DateTime timestamp,
    String? messageId,
    String? messageType,
    bool isVoiceInput = false,
  }) async {
    try {
      // Generate a proper UUID instead of timestamp string
      final uuid = messageId ?? _generateUuid();
      
      final messageData = {
        'id': uuid, // Now using proper UUID
        'baby_id': babyId,
        'content': content,
        'is_from_user': isFromUser,
        'message_type': messageType ?? (isFromUser ? 'user' : 'assistant'),
        'is_voice_input': isVoiceInput,
        'timestamp': timestamp.toIso8601String(),
        'created_at': DateTime.now().toIso8601String(),
      };

      await _supabaseService.insert('chat_messages', messageData);
      debugPrint('✅ Chat message stored: ${content.substring(0, content.length > 50 ? 50 : content.length)}...');
    } catch (e) {
      debugPrint('❌ Error storing chat message: $e');
    }
  }

  /// Load chat history for a baby profile
  Future<List<Map<String, dynamic>>> loadChatHistory({
    required String babyId,
    int limit = 50,
  }) async {
    try {
      final response = await _supabaseService.select(
        'chat_messages',
        filters: {'baby_id': babyId},
        orderBy: 'created_at',
        ascending: true,
        limit: limit,
      );

      debugPrint('✅ Loaded ${response.length} chat messages from history');
      return response;
    } catch (e) {
      debugPrint('❌ Error loading chat history: $e');
      return [];
    }
  }

  /// Get recent conversation context (last N messages for AI context)
  Future<List<Map<String, String>>> getConversationContext({
    required String babyId,
    int contextLimit = 10,
  }) async {
    try {
      final messages = await loadChatHistory(babyId: babyId, limit: contextLimit);
      
      return messages.map((msg) => {
        'role': msg['is_from_user'] == true ? 'user' : 'assistant',
        'content': msg['content'] as String,
      }).toList();
    } catch (e) {
      debugPrint('❌ Error getting conversation context: $e');
      return [];
    }
  }

  /// Clear chat history for a baby (if needed)
  Future<void> clearChatHistory(String babyId) async {
    try {
      await _supabaseService.delete('chat_messages', 'baby_id', babyId);
      debugPrint('✅ Chat history cleared for baby: $babyId');
    } catch (e) {
      debugPrint('❌ Error clearing chat history: $e');
    }
  }

  // Private helper methods
  String _buildSystemPrompt(
      BabyProfile babyProfile, List<ActivityLog>? recentActivities) {
    final babyAge =
        '${babyProfile.ageInDays} days (${babyProfile.ageInWeeks} weeks, ${babyProfile.ageInMonths} months)';

    String contextInfo = '''
You are a knowledgeable, supportive pediatric AI assistant specializing in baby care advice. 

Baby Information:
- Name: ${babyProfile.name}
- Age: $babyAge
- Gender: ${babyProfile.gender}
- Birth Weight: ${babyProfile.birthWeight ?? 'Not recorded'} kg
- Birth Height: ${babyProfile.birthHeight ?? 'Not recorded'} cm
- Known Allergies: ${babyProfile.allergies.isEmpty ? 'None recorded' : babyProfile.allergies.join(', ')}
- Current Medications: ${babyProfile.medications.isEmpty ? 'None' : babyProfile.medications.join(', ')}
''';

    if (recentActivities != null && recentActivities.isNotEmpty) {
      contextInfo += '\nRecent Activity Summary:\n';
      final feedingCount =
          recentActivities.where((a) => a.type == ActivityType.feeding).length;
      final sleepCount =
          recentActivities.where((a) => a.type == ActivityType.sleep).length;
      final diaperCount =
          recentActivities.where((a) => a.type == ActivityType.diaper).length;
      final milestoneCount =
          recentActivities.where((a) => a.type == ActivityType.milestone).length;

      contextInfo += '''
- Recent feedings: $feedingCount
- Recent sleep sessions: $sleepCount  
- Recent diaper changes: $diaperCount
- Recent milestones: $milestoneCount
- Last activity: ${recentActivities.first.type.name} at ${_formatDateTime(recentActivities.first.timestamp)}
''';
    }

    return '''$contextInfo

You are a friendly, supportive AI assistant specializing in baby care and parenting advice.

CONVERSATION GUIDELINES:
- You SHOULD engage naturally with greetings, pleasantries, and friendly conversation
- You SHOULD respond warmly to questions like "how are you", "hello", "good morning", etc.
- You CAN have normal friendly chat while gently steering toward baby care topics
- You SHOULD be conversational and personable, not robotic

TOPIC FOCUS:
- Your PRIMARY expertise is: baby care, parenting, child development, baby health, feeding, sleeping, activities, milestones, safety, and child-related concerns
- For topics clearly unrelated to babies/parenting (like politics, entertainment, technical support, etc.), politely redirect: "I'd love to help you with baby care and parenting questions! Feel free to ask about ${babyProfile.name}'s feeding, sleep, development, activities, or any other parenting concerns you might have."

RESPONSE STYLE:
- Be warm, friendly, and conversational
- Show genuine interest in the parent's well-being
- Provide evidence-based, age-appropriate advice
- Be supportive and empathetic to parental concerns
- Always recommend consulting healthcare providers for medical concerns
- Focus on safe practices and current pediatric guidelines
- Consider the baby's specific age and developmental stage

Remember: You're a helpful companion for parents, not just a strict information source. Be personable while maintaining your expertise in baby care and parenting.''';
  }

  String _getQuickAdviceSystemPrompt() {
    return '''You are a friendly pediatric AI assistant providing quick, practical advice.

CONVERSATION STYLE:
- Be warm, conversational, and naturally responsive to greetings and friendly chat
- Engage naturally with "how are you", "hello", and similar pleasantries
- Your specialty is baby care, parenting, and child development

TOPIC GUIDANCE:
- Focus on: baby care, parenting, child development, health, feeding, sleeping, activities, milestones, safety
- For clearly unrelated topics, redirect helpfully: "I'd love to help with your baby care and parenting questions instead!"

Provide concise, actionable advice while emphasizing safety and age-appropriateness.
Always recommend consulting healthcare providers for serious concerns.''';
  }

  String _getFeedingAdviceSystemPrompt() {
    return '''You are a pediatric nutrition specialist AI assistant.
Provide evidence-based feeding advice considering the baby's age, growth, and feeding patterns.
Focus on safe feeding practices, nutritional needs, and addressing common feeding challenges.''';
  }

  String _getSleepAdviceSystemPrompt() {
    return '''You are a pediatric sleep specialist AI assistant.
Provide evidence-based sleep advice considering the baby's age, sleep patterns, and safe sleep guidelines.
Focus on sleep safety, age-appropriate sleep expectations, and gentle sleep strategies.''';
  }

  String _buildQuickAdvicePrompt(
      String topic, BabyProfile babyProfile, String? specificQuestion) {
    final babyAge =
        '${babyProfile.ageInDays} days (${babyProfile.ageInWeeks} weeks)';

    return '''
Baby: ${babyProfile.name}, Age: $babyAge, Gender: ${babyProfile.gender}
Topic: $topic
${specificQuestion != null ? 'Specific Question: $specificQuestion' : ''}

Provide quick, practical advice for this topic considering the baby's age and developmental stage.
''';
  }

  String _buildFeedingAdvicePrompt(BabyProfile babyProfile,
      List<ActivityLog>? recentFeedings, String? concern) {
    final babyAge =
        '${babyProfile.ageInDays} days (${babyProfile.ageInWeeks} weeks)';

    String prompt = '''
Baby: ${babyProfile.name}, Age: $babyAge
${concern != null ? 'Feeding Concern: $concern' : ''}
''';

    if (recentFeedings != null && recentFeedings.isNotEmpty) {
      prompt += '\nRecent Feeding Pattern:\n';
      for (final feeding in recentFeedings.take(5)) {
        prompt +=
            '- ${feeding.data['feedingType']} at ${_formatDateTime(feeding.timestamp)}';
        if (feeding.data['amount'] != null) {
          prompt += ', Amount: ${feeding.data['amount']}ml';
        }
        if (feeding.duration != null) {
          prompt += ', Duration: ${feeding.duration!.inMinutes}min';
        }
        prompt += '\n';
      }
    }

    prompt +=
        '\nProvide feeding advice considering age, patterns, and any concerns.';
    return prompt;
  }

  String _buildSleepAdvicePrompt(BabyProfile babyProfile,
      List<ActivityLog>? recentSleep, String? concern) {
    final babyAge =
        '${babyProfile.ageInDays} days (${babyProfile.ageInWeeks} weeks)';

    String prompt = '''
Baby: ${babyProfile.name}, Age: $babyAge
${concern != null ? 'Sleep Concern: $concern' : ''}
''';

    if (recentSleep != null && recentSleep.isNotEmpty) {
      prompt += '\nRecent Sleep Pattern:\n';
      for (final sleep in recentSleep.take(5)) {
        prompt += '- Sleep at ${_formatDateTime(sleep.timestamp)}';
        if (sleep.duration != null) {
          prompt += ', Duration: ${_formatDuration(sleep.duration!)}';
        }
        if (sleep.data['quality'] != null) {
          prompt += ', Quality: ${sleep.data['quality']}';
        }
        prompt += '\n';
      }
    }

    prompt +=
        '\nProvide sleep advice considering age, patterns, and any concerns.';
    return prompt;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  // Helper method to generate proper UUIDs
  String _generateUuid() {
    // Simple UUID v4 generation
    final random = Random();
    final bytes = List<int>.generate(16, (i) => random.nextInt(256));
    
    // Set version (4) and variant bits
    bytes[6] = (bytes[6] & 0x0f) | 0x40;
    bytes[8] = (bytes[8] & 0x3f) | 0x80;
    
    final hex = bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join();
    return '${hex.substring(0, 8)}-${hex.substring(8, 12)}-${hex.substring(12, 16)}-${hex.substring(16, 20)}-${hex.substring(20, 32)}';
  }

  /// Create context hash from conversation history for cache differentiation
  String? _createContextHash(List<Map<String, String>>? conversationHistory) {
    if (conversationHistory == null || conversationHistory.isEmpty) {
      return null;
    }
    
    // Use last 3 messages for context (excluding current message)
    final recentMessages = conversationHistory.length > 3 
        ? conversationHistory.sublist(conversationHistory.length - 3)
        : conversationHistory;
    
    // Create a simple hash from recent conversation content
    final contextString = recentMessages
        .map((msg) {
          final content = msg['content'] ?? '';
          final truncatedContent = content.length > 30 
              ? content.substring(0, 30) 
              : content;
          return '${msg['role']}:$truncatedContent';
        })
        .join('|');
    
    return contextString.isNotEmpty ? contextString.hashCode.abs().toString() : null;
  }
}
