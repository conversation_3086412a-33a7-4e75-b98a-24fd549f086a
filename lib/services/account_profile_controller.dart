import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import '../models/user_profile.dart';
import '../models/family_member.dart';
import '../models/subscription_info.dart';
import 'supabase_service.dart';
import 'auth_service.dart';
import 'error_handling_service.dart';

/// Centralized state management controller for Account Profile section
/// Handles profile data loading, family member management, and subscription status
class AccountProfileController extends ChangeNotifier {
  final SupabaseService _supabaseService = SupabaseService();
  final AuthService _authService;
  final ErrorHandlingService _errorService = ErrorHandlingService();

  // State variables
  UserProfile? _userProfile;
  List<FamilyMember> _familyMembers = [];
  SubscriptionInfo? _subscriptionInfo;
  
  // Loading states
  bool _isLoadingProfile = false;
  bool _isLoadingFamilyMembers = false;
  bool _isLoadingSubscription = false;
  bool _isUpdatingProfile = false;
  bool _isInvitingMember = false;
  
  // Enhanced error states
  AccountProfileError? _profileError;
  AccountProfileError? _familyError;
  AccountProfileError? _subscriptionError;
  
  // Network connectivity
  bool _isOnline = true;
  StreamSubscription<bool>? _connectivitySubscription;
  
  // Realtime subscriptions
  RealtimeChannel? _familyMembersChannel;
  RealtimeChannel? _userProfileChannel;

  AccountProfileController(this._authService) {
    _initializeController();
  }

  // Getters
  UserProfile? get userProfile => _userProfile;
  List<FamilyMember> get familyMembers => List.unmodifiable(_familyMembers);
  SubscriptionInfo? get subscriptionInfo => _subscriptionInfo;
  
  // Loading state getters
  bool get isLoadingProfile => _isLoadingProfile;
  bool get isLoadingFamilyMembers => _isLoadingFamilyMembers;
  bool get isLoadingSubscription => _isLoadingSubscription;
  bool get isUpdatingProfile => _isUpdatingProfile;
  bool get isInvitingMember => _isInvitingMember;
  bool get isLoading => _isLoadingProfile || _isLoadingFamilyMembers || _isLoadingSubscription;
  
  // Enhanced error state getters
  AccountProfileError? get profileError => _profileError;
  AccountProfileError? get familyError => _familyError;
  AccountProfileError? get subscriptionError => _subscriptionError;
  bool get hasErrors => _profileError != null || _familyError != null || _subscriptionError != null;
  
  // Network connectivity getters
  bool get isOnline => _isOnline;
  
  // Computed properties
  int get activeFamilyMembersCount => _familyMembers.where((m) => m.isActive).length;
  int get pendingInvitationsCount => _familyMembers.where((m) => m.isPending).length;
  bool get canManageFamily => _userProfile?.role == 'admin' || _userProfile?.role == 'parent';
  bool get isPremiumUser => _subscriptionInfo?.isPremium ?? false;

  /// Initialize the controller and load initial data
  void _initializeController() {
    // Initialize error handling service
    _errorService.initialize();
    
    // Listen to connectivity changes
    _connectivitySubscription = _errorService.connectivityStream.listen((isOnline) {
      _isOnline = isOnline;
      notifyListeners();
      
      // Retry failed operations when coming back online
      if (isOnline && hasErrors) {
        _retryFailedOperations();
      }
    });
    
    // Listen to auth state changes
    _authService.addListener(_onAuthStateChanged);
    
    // Load initial data if user is authenticated
    if (_authService.isAuthenticated) {
      loadAllData();
      // Schedule periodic cleanup of expired invitations
      _scheduleInvitationCleanup();
    }
  }

  /// Schedule periodic cleanup of expired invitations
  void _scheduleInvitationCleanup() {
    // Clean up expired invitations every hour
    Timer.periodic(const Duration(hours: 1), (timer) {
      if (_authService.isAuthenticated && canManageFamily) {
        cleanupExpiredInvitations();
      } else {
        timer.cancel();
      }
    });
  }

  /// Handle authentication state changes
  void _onAuthStateChanged() {
    if (_authService.isAuthenticated) {
      loadAllData();
    } else {
      _clearAllData();
    }
  }

  /// Load all profile-related data
  Future<void> loadAllData() async {
    await Future.wait([
      loadUserProfile(),
      loadFamilyMembers(),
      loadSubscriptionInfo(),
    ]);
  }

  /// Load user profile data with enhanced error handling and retry logic
  Future<void> loadUserProfile() async {
    if (!_authService.isAuthenticated) return;

    await _errorService.withRetry(
      () async {
        _isLoadingProfile = true;
        _profileError = null;
        notifyListeners();

        final currentUser = _authService.currentUser;
        if (currentUser == null) throw Exception('No authenticated user');

        // Load user profile using auth_id instead of id
        final response = await _supabaseService.select(
          'user_profiles',
          filters: {'auth_id': currentUser.id},
        );

        if (response.isNotEmpty) {
          final profileData = response.first;
          _userProfile = UserProfile.fromJson(profileData);
          
          
          // Set up realtime subscription for profile updates
          _setupUserProfileSubscription();
        } else {
          // Profile doesn't exist, create it using the simpler RPC function
          final client = await _supabaseService.client;
          final profileId = await client.rpc('ensure_user_profile');
          
          if (profileId != null) {
            // Now load the created profile
            final response = await _supabaseService.select(
              'user_profiles',
              filters: {'auth_id': currentUser.id},
            );
            
            if (response.isNotEmpty) {
              _userProfile = UserProfile.fromJson(response.first);
              
              // Set up realtime subscription for profile updates
              _setupUserProfileSubscription();
            } else {
              throw Exception('Failed to load created user profile');
            }
          } else {
            throw Exception('Failed to create user profile');
          }
        }
      },
      maxRetries: 3,
      operationName: 'loadUserProfile',
    ).catchError((error) {
      _profileError = _errorService.handleError(
        error,
        operation: 'loadUserProfile',
        context: {'userId': _authService.currentUser?.id},
      );
      debugPrint('Error loading user profile: $error');
    }).whenComplete(() {
      _isLoadingProfile = false;
      notifyListeners();
    });
  }

  /// Load family members data with enhanced error handling
  Future<void> loadFamilyMembers() async {
    if (!_authService.isAuthenticated) return;

    await _errorService.withRetry(
      () async {
        _isLoadingFamilyMembers = true;
        _familyError = null;
        notifyListeners();

        final currentUser = _authService.currentUser;
        if (currentUser == null) throw Exception('No authenticated user');

        // Load family members for the current user's family
        final familyId = await _getUserFamilyId();
        if (familyId == null) {
          _familyMembers = [];
          return;
        }
        
        final response = await _supabaseService.select(
          'family_members',
          select: '''
            *,
            user_profiles!inner(*)
          ''',
          filters: {'family_id': familyId},
          orderBy: 'joined_at',
          ascending: false,
        );

        _familyMembers = response.map((data) => FamilyMember.fromJson(data)).toList();
        
        // Set up realtime subscription for family member updates
        _setupFamilyMembersSubscription();
      },
      maxRetries: 3,
      operationName: 'loadFamilyMembers',
    ).catchError((error) {
      _familyError = _errorService.handleError(
        error,
        operation: 'loadFamilyMembers',
        context: {'userId': _authService.currentUser?.id},
      );
      debugPrint('Error loading family members: $error');
    }).whenComplete(() {
      _isLoadingFamilyMembers = false;
      notifyListeners();
    });
  }

  /// Load subscription information with enhanced error handling
  Future<void> loadSubscriptionInfo() async {
    if (!_authService.isAuthenticated) return;

    await _errorService.withRetry(
      () async {
        _isLoadingSubscription = true;
        _subscriptionError = null;
        notifyListeners();

        final currentUser = _authService.currentUser;
        if (currentUser == null) throw Exception('No authenticated user');

        // Load subscription data
        final response = await _supabaseService.select(
          'subscriptions',
          filters: {'user_id': currentUser.id},
          orderBy: 'created_at',
          ascending: false,
          limit: 1,
        );

        if (response.isNotEmpty) {
          _subscriptionInfo = SubscriptionInfo.fromJson(response.first);
        } else {
          // Default to free plan if no subscription found
          _subscriptionInfo = SubscriptionPlans.free;
        }
      },
      maxRetries: 2, // Lower retry count for subscription as it's less critical
      operationName: 'loadSubscriptionInfo',
    ).catchError((error) {
      _subscriptionError = _errorService.handleError(
        error,
        operation: 'loadSubscriptionInfo',
        context: {'userId': _authService.currentUser?.id},
      );
      debugPrint('Error loading subscription info: $error');
      
      // Fallback to free plan on error
      _subscriptionInfo = SubscriptionPlans.free;
    }).whenComplete(() {
      _isLoadingSubscription = false;
      notifyListeners();
    });
  }

  /// Update user profile using the new RPC function
  Future<bool> updateUserProfile({
    String? fullName,
    String? avatarUrl,
    String? phoneNumber,
    String? timezone,
    Map<String, dynamic>? preferences,
  }) async {
    if (!_authService.isAuthenticated) return false;

    try {
      _isUpdatingProfile = true;
      _profileError = null;
      notifyListeners();

      // Use the new RPC function that handles both create and update
      final client = await _supabaseService.client;
      final response = await client.rpc('create_or_update_user_profile', params: {
        'p_full_name': fullName,
        'p_avatar_url': avatarUrl,
        'p_phone_number': phoneNumber,
        'p_timezone': timezone,
        'p_notification_preferences': preferences,
      });

      if (response != null && response is List && response.isNotEmpty) {
        _userProfile = UserProfile.fromJson(response.first);
        
        return true;
      } else {
        throw Exception('No data returned from profile update');
      }
    } catch (e) {
      _profileError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Profile Update Failed',
        message: 'Failed to update profile: ${e.toString()}',
        isRetryable: true,
        operation: 'updateUserProfile',
        originalError: e,
      );
      debugPrint('Error updating user profile: $e');
      return false;
    } finally {
      _isUpdatingProfile = false;
      notifyListeners();
    }
  }

  /// Invite a new family member with enhanced error handling
  Future<bool> inviteFamilyMember({
    required String email,
    required String role,
    Map<String, bool>? permissions,
    String? customMessage,
  }) async {
    if (!_authService.isAuthenticated || !canManageFamily) return false;

    try {
      return await _errorService.withRetry(
        () async {
          _isInvitingMember = true;
          _familyError = null;
          notifyListeners();

          // Check if user is already a family member
          if (_familyMembers.any((member) => member.email.toLowerCase() == email.toLowerCase())) {
            throw Exception('This email is already associated with a family member');
          }

          final currentUser = _authService.currentUser;
          if (currentUser == null) throw Exception('No authenticated user');

          final familyId = await _getUserFamilyId();
          if (familyId == null) {
            throw Exception('No family found for user');
          }
          final invitationExpiresAt = DateTime.now().add(const Duration(days: 7)); // 7 days expiration

          // Create invitation record
          final invitationData = {
            'family_id': familyId,
            'email': email.toLowerCase(),
            'full_name': email.split('@')[0], // Temporary name until they accept
            'role': role,
            'status': 'pending',
            'permissions': permissions ?? FamilyMemberPermissions.getDefaultPermissions(role),
            'invited_by': currentUser.id,
            'invitation_sent_at': DateTime.now().toIso8601String(),
            'invitation_expires_at': invitationExpiresAt.toIso8601String(),
            'custom_message': customMessage,
            'joined_at': DateTime.now().toIso8601String(), // Required field, will be updated on acceptance
          };

          final response = await _supabaseService.insert('family_members', invitationData);
          
          if (response != null) {
            // Send invitation email through auth service
            final emailSent = await _authService.sendFamilyInvitationEmail(
              email: email,
              inviterName: _userProfile?.fullName ?? 'A family member',
              role: role,
              customMessage: customMessage,
              invitationId: response['id'],
            );

            if (emailSent) {
              // Reload family members to include the new invitation
              await loadFamilyMembers();
              return true;
            } else {
              // Remove the invitation record if email failed to send
              await _supabaseService.delete('family_members', 'id', response['id']);
              throw Exception('Failed to send invitation email');
            }
          }
          
          throw Exception('Failed to create invitation record');
        },
        maxRetries: 2,
        operationName: 'inviteFamilyMember',
      );
    } catch (error) {
      _familyError = _errorService.handleError(
        error,
        operation: 'inviteFamilyMember',
        context: {
          'email': email,
          'role': role,
          'familyMemberCount': _familyMembers.length,
        },
      );
      debugPrint('Error inviting family member: $error');
      return false;
    } finally {
      _isInvitingMember = false;
      notifyListeners();
    }
  }

  /// Remove a family member
  Future<bool> removeFamilyMember(String memberId) async {
    if (!_authService.isAuthenticated || !canManageFamily) return false;

    try {
      _familyError = null;
      notifyListeners();

      await _supabaseService.delete('family_members', 'id', memberId);
      
      // Remove from local list
      _familyMembers.removeWhere((member) => member.id == memberId);
      notifyListeners();
      
      return true;
    } catch (e) {
      _familyError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Remove Member Failed',
        message: 'Failed to remove family member: ${e.toString()}',
        isRetryable: true,
        operation: 'removeFamilyMember',
        originalError: e,
      );
      debugPrint('Error removing family member: $e');
      return false;
    }
  }

  /// Update family member permissions
  Future<bool> updateFamilyMemberPermissions(
    String memberId,
    Map<String, bool> permissions,
  ) async {
    if (!_authService.isAuthenticated || !canManageFamily) return false;

    try {
      _familyError = null;
      notifyListeners();

      await _supabaseService.update(
        'family_members',
        {
          'permissions': permissions,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'id',
        memberId,
      );

      // Update local member
      final memberIndex = _familyMembers.indexWhere((m) => m.id == memberId);
      if (memberIndex != -1) {
        _familyMembers[memberIndex] = _familyMembers[memberIndex].copyWith(
          permissions: permissions,
        );
        notifyListeners();
      }
      
      return true;
    } catch (e) {
      _familyError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Permission Update Failed',
        message: 'Failed to update permissions: ${e.toString()}',
        isRetryable: true,
        operation: 'updateFamilyMemberPermissions',
        originalError: e,
      );
      debugPrint('Error updating family member permissions: $e');
      return false;
    }
  }

  /// Resend invitation to a pending family member
  Future<bool> resendInvitation(String memberId) async {
    if (!_authService.isAuthenticated || !canManageFamily) return false;

    try {
      _familyError = null;
      notifyListeners();

      final member = _familyMembers.firstWhere((m) => m.id == memberId);
      
      if (!member.isPending) {
        _familyError = AccountProfileError(
          type: AccountProfileErrorType.validationError,
          title: 'Invalid Operation',
          message: 'Can only resend invitations to pending members',
          isRetryable: false,
          operation: 'resendInvitation',
        );
        return false;
      }

      final currentUser = _authService.currentUser;
      if (currentUser == null) throw Exception('No authenticated user');

      // Update invitation expiration date
      final newExpirationDate = DateTime.now().add(const Duration(days: 7));
      
      await _supabaseService.update(
        'family_members',
        {
          'invitation_sent_at': DateTime.now().toIso8601String(),
          'invitation_expires_at': newExpirationDate.toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        'id',
        memberId,
      );

      // Send invitation email
      final emailSent = await _authService.sendFamilyInvitationEmail(
        email: member.email,
        inviterName: _userProfile?.fullName ?? 'A family member',
        role: member.role,
        customMessage: null,
        invitationId: memberId,
      );

      if (emailSent) {
        // Update local member data
        final memberIndex = _familyMembers.indexWhere((m) => m.id == memberId);
        if (memberIndex != -1) {
          _familyMembers[memberIndex] = _familyMembers[memberIndex].copyWith(
            invitationSentAt: DateTime.now(),
            invitationExpiresAt: newExpirationDate,
          );
          notifyListeners();
        }
        return true;
      } else {
        _familyError = AccountProfileError(
          type: AccountProfileErrorType.serverError,
          title: 'Email Failed',
          message: 'Failed to send invitation email',
          isRetryable: true,
          operation: 'resendInvitation',
        );
        return false;
      }
    } catch (e) {
      _familyError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Resend Failed',
        message: 'Failed to resend invitation: ${e.toString()}',
        isRetryable: true,
        operation: 'resendInvitation',
        originalError: e,
      );
      debugPrint('Error resending invitation: $e');
      return false;
    }
  }

  /// Cancel a pending invitation
  Future<bool> cancelInvitation(String memberId) async {
    if (!_authService.isAuthenticated || !canManageFamily) return false;

    try {
      _familyError = null;
      notifyListeners();

      final member = _familyMembers.firstWhere((m) => m.id == memberId);
      
      if (!member.isPending) {
        _familyError = AccountProfileError(
          type: AccountProfileErrorType.validationError,
          title: 'Invalid Operation',
          message: 'Can only cancel pending invitations',
          isRetryable: false,
          operation: 'cancelInvitation',
        );
        return false;
      }

      // Remove the invitation record
      await _supabaseService.delete('family_members', 'id', memberId);
      
      // Remove from local list
      _familyMembers.removeWhere((member) => member.id == memberId);
      notifyListeners();
      
      return true;
    } catch (e) {
      _familyError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Cancel Failed',
        message: 'Failed to cancel invitation: ${e.toString()}',
        isRetryable: true,
        operation: 'cancelInvitation',
        originalError: e,
      );
      debugPrint('Error cancelling invitation: $e');
      return false;
    }
  }

  /// Accept a family invitation (called by the invited user)
  Future<bool> acceptFamilyInvitation(String invitationId, String fullName) async {
    if (!_authService.isAuthenticated) return false;

    try {
      _familyError = null;
      notifyListeners();

      final currentUser = _authService.currentUser;
      if (currentUser == null) throw Exception('No authenticated user');

      // Update the invitation to active status
      await _supabaseService.update(
        'family_members',
        {
          'status': 'active',
          'full_name': fullName,
          'user_id': currentUser.id,
          'joined_at': DateTime.now().toIso8601String(),
          'last_active_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        },
        'id',
        invitationId,
      );

      // Reload family members
      await loadFamilyMembers();
      return true;
    } catch (e) {
      _familyError = AccountProfileError(
        type: AccountProfileErrorType.serverError,
        title: 'Accept Failed',
        message: 'Failed to accept invitation: ${e.toString()}',
        isRetryable: true,
        operation: 'acceptFamilyInvitation',
        originalError: e,
      );
      debugPrint('Error accepting invitation: $e');
      return false;
    }
  }

  /// Clean up expired invitations
  Future<void> cleanupExpiredInvitations() async {
    if (!_authService.isAuthenticated || !canManageFamily) return;

    try {
      final currentUser = _authService.currentUser;
      if (currentUser == null) return;

      final familyId = await _getUserFamilyId();
      if (familyId == null) return; // No family to clean up
      
      // Delete expired pending invitations
      final client = await _supabaseService.client;
      await client
          .from('family_members')
          .delete()
          .eq('family_id', familyId)
          .eq('status', 'pending')
          .lt('invitation_expires_at', DateTime.now().toIso8601String());

      // Reload family members to reflect changes
      await loadFamilyMembers();
    } catch (e) {
      debugPrint('Error cleaning up expired invitations: $e');
    }
  }

  /// Clear all error states
  void clearErrors() {
    _profileError = null;
    _familyError = null;
    _subscriptionError = null;
    notifyListeners();
  }

  /// Clear specific error
  void clearError(String errorType) {
    switch (errorType) {
      case 'profile':
        _profileError = null;
        break;
      case 'family':
        _familyError = null;
        break;
      case 'subscription':
        _subscriptionError = null;
        break;
    }
    notifyListeners();
  }

  /// Retry failed operations when connectivity is restored
  void _retryFailedOperations() {
    if (_profileError != null && _profileError!.isRetryable) {
      loadUserProfile();
    }
    if (_familyError != null && _familyError!.isRetryable) {
      loadFamilyMembers();
    }
    if (_subscriptionError != null && _subscriptionError!.isRetryable) {
      loadSubscriptionInfo();
    }
  }

  /// Retry specific operation
  Future<void> retryOperation(String operation) async {
    switch (operation) {
      case 'loadUserProfile':
        await loadUserProfile();
        break;
      case 'loadFamilyMembers':
        await loadFamilyMembers();
        break;
      case 'loadSubscriptionInfo':
        await loadSubscriptionInfo();
        break;
      default:
        debugPrint('Unknown operation: $operation');
    }
  }

  /// Refresh all data
  Future<void> refresh() async {
    await loadAllData();
  }


  /// Get user's family ID
  Future<String?> _getUserFamilyId() async {
    final currentUser = _authService.currentUser;
    if (currentUser == null) throw Exception('No authenticated user');

    final response = await _supabaseService.select(
      'user_profiles',
      select: 'family_id',
      filters: {'auth_id': currentUser.id},
    );

    if (response.isEmpty || response.first['family_id'] == null) {
      // Create a default family for the user if none exists
      try {
        final familyResponse = await _supabaseService.insert('families', {
          'name': '${_userProfile?.fullName ?? 'User'}\'s Family',
          'created_by': _userProfile?.id,
          'created_at': DateTime.now().toIso8601String(),
          'updated_at': DateTime.now().toIso8601String(),
        });
        
        if (familyResponse != null) {
          final familyId = familyResponse['id'];
          
          // Update user profile with family_id
          await _supabaseService.update(
            'user_profiles',
            {'family_id': familyId},
            'auth_id',
            currentUser.id,
          );
          
          return familyId;
        }
      } catch (e) {
        debugPrint('Error creating family: $e');
        return null;
      }
    }
    
    return response.first['family_id'];
  }


  /// Set up realtime subscription for user profile updates
  void _setupUserProfileSubscription() async {
    try {
      _userProfileChannel?.unsubscribe();
      
      _userProfileChannel = await _supabaseService.subscribeToTable(
        'user_profiles',
        onChange: (data) {
          if (data['id'] == _userProfile?.id) {
            _userProfile = UserProfile.fromJson(data);
            notifyListeners();
          }
        },
      );
    } catch (e) {
      debugPrint('Error setting up user profile subscription: $e');
    }
  }

  /// Set up realtime subscription for family member updates
  void _setupFamilyMembersSubscription() async {
    try {
      _familyMembersChannel?.unsubscribe();
      
      _familyMembersChannel = await _supabaseService.subscribeToTable(
        'family_members',
        onChange: (data) {
          // Reload family members when changes occur
          loadFamilyMembers();
        },
      );
    } catch (e) {
      debugPrint('Error setting up family members subscription: $e');
    }
  }

  /// Create a new user profile when one doesn't exist
  Future<void> _createUserProfile(User currentUser) async {
    try {
      // Create a basic user profile
      final newUserProfile = UserProfile(
        id: currentUser.id,
        email: currentUser.email ?? '',
        fullName: currentUser.userMetadata?['display_name'] as String? ?? 
                    currentUser.userMetadata?['full_name'] as String? ?? 
                    currentUser.email?.split('@').first ?? 'User',
        role: 'parent',
        signInCount: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        isEmailVerified: currentUser.emailConfirmedAt != null,
        preferences: const {},
        permissions: const {},
      );
      
      // Save the profile to Supabase
      final response = await _supabaseService.insert('user_profiles', newUserProfile.toJson());
      
      if (response != null) {
        _userProfile = UserProfile.fromJson(response);
        
        // Set up realtime subscription for profile updates
        _setupUserProfileSubscription();
        
        debugPrint('Created new user profile for user: ${currentUser.id}');
      } else {
        throw Exception('Failed to create user profile - no response from server');
      }
    } catch (e) {
      debugPrint('Error creating user profile: $e');
      throw Exception('Failed to create user profile: ${e.toString()}');
    }
  }

  /// Clear all data when user signs out
  void _clearAllData() {
    _userProfile = null;
    _familyMembers.clear();
    _subscriptionInfo = null;
    
    _isLoadingProfile = false;
    _isLoadingFamilyMembers = false;
    _isLoadingSubscription = false;
    _isUpdatingProfile = false;
    _isInvitingMember = false;
    
    _profileError = null;
    _familyError = null;
    _subscriptionError = null;
    
    // Clean up subscriptions
    _userProfileChannel?.unsubscribe();
    _familyMembersChannel?.unsubscribe();
    _userProfileChannel = null;
    _familyMembersChannel = null;
    
    notifyListeners();
  }

  @override
  void dispose() {
    _authService.removeListener(_onAuthStateChanged);
    _userProfileChannel?.unsubscribe();
    _familyMembersChannel?.unsubscribe();
    _connectivitySubscription?.cancel();
    _errorService.dispose();
    super.dispose();
  }
}