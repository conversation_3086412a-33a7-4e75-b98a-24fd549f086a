import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';

import '../models/notification_item.dart';
import '../core/app_export.dart';

/// Unified notification service that manages all notifications centrally
class UnifiedNotificationService extends ChangeNotifier {
  static const String _notificationsEnabledKey = 'notifications_enabled';
  static const String _notificationsStorageKey = 'stored_notifications';
  static const String _unreadCountKey = 'unread_notification_count';

  static UnifiedNotificationService? _instance;
  static UnifiedNotificationService get instance => _instance ??= UnifiedNotificationService._();
  
  UnifiedNotificationService._();

  SharedPreferences? _prefs;
  bool _isInitialized = false;
  bool _notificationsEnabled = true;
  List<NotificationItem> _notifications = [];
  int _unreadCount = 0;

  // Getters
  bool get isInitialized => _isInitialized;
  bool get notificationsEnabled => _notificationsEnabled;
  List<NotificationItem> get notifications => List.unmodifiable(_notifications);
  int get unreadCount => _unreadCount;
  bool get hasUnreadNotifications => _unreadCount > 0;

  /// Initialize the unified notification service
  Future<void> init() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      await _loadSettings();
      await _loadStoredNotifications();
      _isInitialized = true;
      notifyListeners();
    } catch (e) {
      debugPrint('UnifiedNotificationService initialization error: $e');
      _isInitialized = true;
    }
  }

  /// Load notification settings from shared preferences
  Future<void> _loadSettings() async {
    try {
      _notificationsEnabled = _prefs?.getBool(_notificationsEnabledKey) ?? true;
      _unreadCount = _prefs?.getInt(_unreadCountKey) ?? 0;
    } catch (e) {
      debugPrint('Error loading notification settings: $e');
    }
  }

  /// Load stored notifications from shared preferences
  Future<void> _loadStoredNotifications() async {
    try {
      final notificationsJson = _prefs?.getString(_notificationsStorageKey);
      if (notificationsJson != null) {
        final List<dynamic> notificationsList = json.decode(notificationsJson);
        _notifications = notificationsList
            .map((json) => NotificationItem.fromJson(json as Map<String, dynamic>))
            .toList();
        
        // Update unread count
        _updateUnreadCount();
      }
    } catch (e) {
      debugPrint('Error loading stored notifications: $e');
      _notifications = [];
    }
  }

  /// Save notifications to shared preferences
  Future<void> _saveNotifications() async {
    try {
      final notificationsJson = json.encode(
        _notifications.map((notification) => notification.toJson()).toList(),
      );
      await _prefs?.setString(_notificationsStorageKey, notificationsJson);
      await _prefs?.setInt(_unreadCountKey, _unreadCount);
    } catch (e) {
      debugPrint('Error saving notifications: $e');
    }
  }

  /// Update unread count
  void _updateUnreadCount() {
    _unreadCount = _notifications.where((n) => !n.isRead).length;
  }

  /// Set master notification setting
  Future<void> setNotificationsEnabled(bool enabled) async {
    if (_notificationsEnabled == enabled) return;
    
    _notificationsEnabled = enabled;
    await _prefs?.setBool(_notificationsEnabledKey, enabled);
    notifyListeners();
  }

  /// Add a new notification
  Future<void> addNotification(NotificationItem notification) async {
    // Always store notifications regardless of enabled state
    _notifications.insert(0, notification);
    
    // Limit stored notifications to prevent excessive storage usage
    if (_notifications.length > 100) {
      _notifications = _notifications.take(100).toList();
    }
    
    _updateUnreadCount();
    await _saveNotifications();
    notifyListeners();
    
    // Only show system notification if enabled
    if (_notificationsEnabled) {
      _showSystemNotification(notification);
    }
  }

  /// Create and add a notification
  Future<void> createNotification({
    required String title,
    required String message,
    required NotificationType type,
    NotificationPriority priority = NotificationPriority.normal,
    DateTime? scheduledFor,
    String? babyId,
    Map<String, dynamic>? metadata,
  }) async {
    final notification = NotificationItem(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      message: message,
      type: type,
      priority: priority,
      createdAt: DateTime.now(),
      scheduledFor: scheduledFor,
      babyId: babyId,
      metadata: metadata,
    );
    
    await addNotification(notification);
  }

  /// Create scheduled activity notification
  Future<void> createScheduledActivityNotification({
    required String title,
    required String description,
    required DateTime scheduledTime,
    String? babyId,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    await createNotification(
      title: title,
      message: description,
      type: NotificationType.appointment,
      priority: priority,
      scheduledFor: scheduledTime,
      babyId: babyId,
      metadata: {
        'source': 'scheduled_activity',
        'scheduled_time': scheduledTime.toIso8601String(),
      },
    );
  }

  /// Show system notification (placeholder for actual implementation)
  void _showSystemNotification(NotificationItem notification) {
    // This would integrate with flutter_local_notifications or similar
    debugPrint('System notification: ${notification.title} - ${notification.message}');
  }

  /// Mark notification as read
  Future<void> markAsRead(String notificationId) async {
    final index = _notifications.indexWhere((n) => n.id == notificationId);
    if (index != -1 && !_notifications[index].isRead) {
      _notifications[index] = _notifications[index].copyWith(isRead: true);
      _updateUnreadCount();
      await _saveNotifications();
      notifyListeners();
    }
  }

  /// Mark all notifications as read
  Future<void> markAllAsRead() async {
    bool hasChanges = false;
    for (int i = 0; i < _notifications.length; i++) {
      if (!_notifications[i].isRead) {
        _notifications[i] = _notifications[i].copyWith(isRead: true);
        hasChanges = true;
      }
    }
    
    if (hasChanges) {
      _updateUnreadCount();
      await _saveNotifications();
      notifyListeners();
    }
  }

  /// Delete a notification
  Future<void> deleteNotification(String notificationId) async {
    final initialLength = _notifications.length;
    _notifications.removeWhere((n) => n.id == notificationId);
    
    if (_notifications.length != initialLength) {
      _updateUnreadCount();
      await _saveNotifications();
      notifyListeners();
    }
  }

  /// Clear all notifications
  Future<void> clearAllNotifications() async {
    _notifications.clear();
    _updateUnreadCount();
    await _saveNotifications();
    notifyListeners();
  }

  /// Get all notifications
  Future<List<NotificationItem>> getAllNotifications() async {
    return List.unmodifiable(_notifications);
  }

  /// Get unread notifications
  Future<List<NotificationItem>> getUnreadNotifications() async {
    return _notifications.where((n) => !n.isRead).toList();
  }

  /// Get notifications by type
  Future<List<NotificationItem>> getNotificationsByType(NotificationType type) async {
    return _notifications.where((n) => n.type == type).toList();
  }

  /// Get notifications for today
  Future<List<NotificationItem>> getTodayNotifications() async {
    final today = DateTime.now();
    return _notifications.where((n) {
      final notificationDate = n.createdAt;
      return notificationDate.year == today.year &&
             notificationDate.month == today.month &&
             notificationDate.day == today.day;
    }).toList();
  }

  /// Get notification icon based on state
  IconData get notificationIcon {
    if (!_notificationsEnabled) return Icons.notifications_off;
    if (_unreadCount > 0) return Icons.notifications_active;
    return Icons.notifications;
  }

  /// Get notification summary
  String get notificationSummary {
    if (!_notificationsEnabled) return 'Notifications disabled';
    if (_unreadCount == 0) return 'No unread notifications';
    return '$_unreadCount unread notification${_unreadCount > 1 ? 's' : ''}';
  }

  /// Create feeding reminder notification
  Future<void> createFeedingReminder({
    required String babyName,
    DateTime? scheduledFor,
    String? babyId,
  }) async {
    await createNotification(
      title: 'Feeding Time',
      message: 'Time to feed $babyName',
      type: NotificationType.feeding,
      priority: NotificationPriority.high,
      scheduledFor: scheduledFor,
      babyId: babyId,
    );
  }

  /// Create sleep reminder notification
  Future<void> createSleepReminder({
    required String babyName,
    DateTime? scheduledFor,
    String? babyId,
  }) async {
    await createNotification(
      title: 'Sleep Time',
      message: 'Time for $babyName to sleep',
      type: NotificationType.sleep,
      priority: NotificationPriority.normal,
      scheduledFor: scheduledFor,
      babyId: babyId,
    );
  }

  /// Create milestone notification
  Future<void> createMilestoneNotification({
    required String babyName,
    required String milestone,
    String? babyId,
  }) async {
    await createNotification(
      title: 'New Milestone!',
      message: '$babyName has reached a new milestone: $milestone',
      type: NotificationType.milestone,
      priority: NotificationPriority.high,
      babyId: babyId,
    );
  }

  /// Create AI insight notification
  Future<void> createAIInsightNotification({
    required String insight,
    String? babyId,
  }) async {
    await createNotification(
      title: 'New AI Insight',
      message: insight,
      type: NotificationType.aiInsight,
      priority: NotificationPriority.normal,
      babyId: babyId,
    );
  }

  /// Create medicine reminder notification
  Future<void> createMedicineReminder({
    required String babyName,
    required String medicineName,
    DateTime? scheduledFor,
    String? babyId,
  }) async {
    await createNotification(
      title: 'Medicine Time',
      message: 'Time to give $medicineName to $babyName',
      type: NotificationType.medicine,
      priority: NotificationPriority.urgent,
      scheduledFor: scheduledFor,
      babyId: babyId,
    );
  }

  /// Create daily summary notification
  Future<void> createDailySummaryNotification({
    required String summary,
    String? babyId,
  }) async {
    await createNotification(
      title: 'Daily Summary',
      message: summary,
      type: NotificationType.dailySummary,
      priority: NotificationPriority.low,
      babyId: babyId,
    );
  }

  /// Create weekly report notification
  Future<void> createWeeklyReportNotification({
    required String report,
    String? babyId,
  }) async {
    await createNotification(
      title: 'Weekly Report',
      message: report,
      type: NotificationType.weeklyReport,
      priority: NotificationPriority.normal,
      babyId: babyId,
    );
  }

  /// Clean up old notifications (older than 30 days)
  Future<void> cleanupOldNotifications() async {
    final thirtyDaysAgo = DateTime.now().subtract(Duration(days: 30));
    final initialLength = _notifications.length;
    
    _notifications.removeWhere((notification) => 
        notification.createdAt.isBefore(thirtyDaysAgo));
    
    if (_notifications.length != initialLength) {
      _updateUnreadCount();
      await _saveNotifications();
      notifyListeners();
    }
  }

  /// Get notification badge count for UI
  int get badgeCount => _notificationsEnabled ? _unreadCount : 0;

  /// Check if should show notification badge
  bool get shouldShowBadge => _notificationsEnabled && _unreadCount > 0;
}