import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:realtime_client/src/types.dart' show PostgresChangeEvent;
import '../models/activity_log.dart';
import '../models/milestone.dart';
import '../models/scheduled_activity.dart';
import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:postgrest/postgrest.dart' show PostgrestException, PostgrestResponse;

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  late final SupabaseClient _client;
  bool _isInitialized = false;
  static Future<void>? _initFuture;

  // Singleton pattern
  factory SupabaseService() {
    return _instance;
  }

  SupabaseService._internal();

  static const String supabaseUrl =
      String.fromEnvironment('SUPABASE_URL', defaultValue: '');
  static const String supabaseAnonKey =
      String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: '');

  // Load credentials from env.json if dart-define values are empty
  static Future<Map<String, String>> _loadCredentials() async {
    String url = supabaseUrl;
    String key = supabaseAnonKey;
    
    // If dart-define values are empty, try to load from env.json
    if (url.isEmpty || key.isEmpty) {
      try {
        final String envJson = await rootBundle.loadString('env.json');
        final Map<String, dynamic> env = json.decode(envJson);
        
        url = env['SUPABASE_URL'] ?? '';
        key = env['SUPABASE_ANON_KEY'] ?? '';
        
        print('📁 Loaded Supabase credentials from env.json');
      } catch (e) {
        print('⚠️ Failed to load env.json: $e');
      }
    }
    
    return {
      'url': url,
      'key': key,
    };
  }

  // Initialize Supabase
  static Future<void> initialize() async {
    if (_initFuture != null) {
      return _initFuture!;
    }

    _initFuture = _initializeSupabase();
    return _initFuture!;
  }

  // Internal initialization logic
  static Future<void> _initializeSupabase() async {
    print('Initializing Supabase...');
    
    final credentials = await _loadCredentials();
    final url = credentials['url']!;
    final key = credentials['key']!;
    
    print('URL: ${url.isEmpty ? 'EMPTY' : 'PROVIDED'}');
    print('Key: ${key.isEmpty ? 'EMPTY' : 'PROVIDED'}');
    
    if (url.isEmpty || key.isEmpty) {
      throw Exception(
          'SUPABASE_URL and SUPABASE_ANON_KEY must be defined using --dart-define or in env.json file.');
    }

    try {
      await Supabase.initialize(
        url: url,
        anonKey: key,
      );

      _instance._client = Supabase.instance.client;
      _instance._isInitialized = true;
      print('Supabase initialized successfully');
    } catch (e) {
      print('Supabase initialization failed: $e');
      rethrow;
    }
  }

  // Client getter (async)
  Future<SupabaseClient> get client async {
    if (!_isInitialized) {
      await initialize();
    }
    return _client;
  }

  // Synchronous client getter (throws if not initialized)
  SupabaseClient get clientSync {
    if (!_isInitialized) {
      throw Exception(
          'SupabaseService not initialized. Call initialize() first.');
    }
    return _client;
  }

  // Realtime subscription methods
  RealtimeChannel createChannel(String channelName) {
    if (!_isInitialized) {
      throw Exception('SupabaseService not initialized. Call initialize() first.');
    }
    return _client.channel(channelName);
  }

  Future<RealtimeChannel> subscribeToTable(
    String table, {
    String? schema = 'public',
    PostgresChangeEvent event = PostgresChangeEvent.all,
    Function(Map<String, dynamic>)? onChange,
  }) async {
    try {
      debugPrint('🔄 Setting up subscription to $table table...');
      
      final channel = _client.channel('realtime:$table');
      
      // Subscribe to the channel and set up the Postgres changes listener
      channel.onPostgresChanges(
        event: event,
        schema: schema ?? 'public',
        table: table,
        callback: (payload) {
          debugPrint('📥 Received change for $table: ${payload.toString()}');
          if (onChange != null) {
            final record = payload.newRecord;
            if (record != null) {
              onChange(record);
            }
          }
        },
      );

      // Subscribe to the channel
      await channel.subscribe((status, [error]) {
        debugPrint('📡 Subscription status for $table: $status');
        if (error != null) {
          debugPrint('❌ Subscription error for $table: $error');
        }
      });

      debugPrint('✅ Successfully subscribed to $table table');
      return channel;
    } catch (e) {
      debugPrint('❌ Error subscribing to $table table: $e');
      rethrow;
    }
  }

  // Auth methods
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    Map<String, dynamic>? data,
  }) async {
    final client = await this.client;
    return await client.auth.signUp(
      email: email,
      password: password,
      data: data,
    );
  }

  Future<AuthResponse> signIn({
    required String email,
    required String password,
  }) async {
    final client = await this.client;
    return await client.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  Future<void> signOut() async {
    final client = await this.client;
    await client.auth.signOut();
  }

  User? get currentUser {
    if (!_isInitialized) return null;
    return _client.auth.currentUser;
  }

  Stream<AuthState> get authStateChanges {
    if (!_isInitialized) {
      return Stream.empty();
    }
    return _client.auth.onAuthStateChange;
  }

  // Database methods
  Future<List<Map<String, dynamic>>> select(
    String table, {
    String? select,
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    final client = await this.client;
    dynamic query = client.from(table).select(select ?? '*');

    if (filters != null) {
      filters.forEach((key, value) {
        query = query.eq(key, value);
      });
    }

    if (orderBy != null) {
      query = query.order(orderBy, ascending: ascending);
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return await query;
  }

  Future<Map<String, dynamic>> insert(
      String table, Map<String, dynamic> data) async {
    final client = await this.client;
    final response = await client.from(table).insert(data).select().single();
    return response;
  }

  Future<Map<String, dynamic>> update(
    String table,
    Map<String, dynamic> data,
    String idColumn,
    dynamic id,
  ) async {
    debugPrint('=== DEBUG: SupabaseService.update ===');
    debugPrint('Table: $table');
    debugPrint('Data: $data');
    debugPrint('ID Column: $idColumn');
    debugPrint('ID Value: $id');
    debugPrint('ID Type: ${id.runtimeType}');
    
    final client = await this.client;
    
    try {
      // First, let's check if the record exists
      debugPrint('Checking if record exists...');
      final existingRecords = await client
          .from(table)
          .select()
          .eq(idColumn, id);
      debugPrint('Existing records found: ${existingRecords.length}');
      debugPrint('Existing records: $existingRecords');
      
      if (existingRecords.isEmpty) {
        debugPrint('ERROR: No records found with $idColumn = $id');
        throw Exception('No records found with $idColumn = $id');
      }
      
      debugPrint('Proceeding with update...');
      final response = await client
          .from(table)
          .update(data)
          .eq(idColumn, id)
          .select()
          .single();
      
      debugPrint('SUCCESS: Update completed');
      debugPrint('Response: $response');
      return response;
    } catch (e) {
      debugPrint('ERROR in SupabaseService.update: $e');
      debugPrint('Error type: ${e.runtimeType}');
      rethrow;
    }
  }

  Future<void> delete(String table, String idColumn, dynamic id) async {
    final client = await this.client;
    await client.from(table).delete().eq(idColumn, id);
  }

  Future<Map<String, dynamic>> upsert(
    String table, 
    Map<String, dynamic> data, {
    String onConflict = 'id',
  }) async {
    final client = await this.client;
    final response = await client
        .from(table)
        .upsert(data, onConflict: onConflict)
        .select()
        .single();
    return response;
  }

  Future<Map<String, dynamic>> insertActivityLog(ActivityLog log) async {
    try {
      debugPrint('📝 Saving activity log...');
      debugPrint('🔍 Activity data: ${log.toJson()}');
      
      final client = await this.client;
      final insertResp = await client
          .from('activity_logs')
          .insert(log.toJson())
          .select()
          .single();

      debugPrint('🟢 Insert response: ${insertResp}');
      return insertResp;
    } catch (e) {
      debugPrint('❌ Error saving activity log: ${e}');
      rethrow;
    }
  }

  Future<List<ActivityLog>> getRecentActivities(
    String babyId, {
    int limit = 20,
    bool todayOnly = false,
  }) async {
    try {
      debugPrint('🔄 Fetching recent activities for baby: $babyId');
      // Use local time so that "today" reflects the user's locale
      final now = DateTime.now();
      debugPrint('📅 Current time (local): $now');
      
      final client = await this.client;
      
      // Get activities from the activity_logs table
      var activityQuery = client
          .from('activity_logs')
          .select()
          .eq('baby_id', babyId);
          
      if (todayOnly) {
        // Calculate the start/end of the day in the device's local timezone
        final startOfDay = DateTime(now.year, now.month, now.day);
        final endOfDay = startOfDay.add(Duration(days: 1));
        
        debugPrint('📅 Filtering activities between (local): $startOfDay and $endOfDay');
        
        activityQuery = activityQuery
          .gte('recorded_at', startOfDay.toString())
          .lt('recorded_at', endOfDay.toString());
      }
      
      final activityResponse = await activityQuery
          .order('recorded_at', ascending: false)
          .limit(limit);

      debugPrint('📥 Raw activity response from Supabase: $activityResponse');
      
      final activities = (activityResponse as List)
          .map((data) {
            debugPrint('🔄 Processing activity data: ${data['recorded_at']}');
            return ActivityLog.fromJson(data);
          })
          .toList();
          
      // Get milestones from the milestones table
      var milestoneQuery = client
          .from('milestones')
          .select()
          .eq('baby_id', babyId);
          
      if (todayOnly) {
        final startOfDay = DateTime(now.year, now.month, now.day);
        final endOfDay = startOfDay.add(Duration(days: 1));
        
        milestoneQuery = milestoneQuery
          .gte('achieved_date', startOfDay.toString())
          .lt('achieved_date', endOfDay.toString());
      }
      
      final milestoneResponse = await milestoneQuery
          .order('achieved_date', ascending: false)
          .limit(limit);

      debugPrint('📥 Raw milestone response from Supabase: $milestoneResponse');
      
      final milestones = (milestoneResponse as List)
          .map((data) {
            debugPrint('🔄 Processing milestone data: ${data['achieved_date']}');
            return Milestone.fromJson(data);
          })
          .toList();
          
      // Convert milestones to ActivityLog format
      final milestoneActivities = milestones.map((milestone) {
        return ActivityLog.fromRawData(
          babyId: babyId,
          type: 'milestone',
          data: {
            'startTime': DateTime.parse(milestone.achievedDate.toString().replaceAll('+00:00', '').replaceAll('Z', '')), // Parse as local time, remove UTC markers
            'title': milestone.title,
            'description': milestone.description,
            'category': milestone.category.name,
            'type': milestone.type.name,
            'age_in_months': milestone.ageInMonths,
            'age_in_days': milestone.ageInDays,
            'is_custom': milestone.isCustom,
            'notes': milestone.notes,
          },
        );
      }).toList();
      
      // Combine and sort all activities by timestamp
      final allActivities = [...activities, ...milestoneActivities];
      allActivities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      
      // Apply limit to combined results
      final limitedActivities = allActivities.take(limit).toList();
          
      debugPrint('✅ Fetched ${activities.length} activities and ${milestones.length} milestones');
      debugPrint('📊 Total combined activities: ${limitedActivities.length}');
      
      if (limitedActivities.isNotEmpty) {
        debugPrint('📊 Latest activity timestamp: ${limitedActivities.first.timestamp}');
        debugPrint('📊 First activity type: ${limitedActivities.first.type}');
      }
      
      return limitedActivities;
    } catch (e) {
      debugPrint('❌ Error fetching recent activities: $e');
      throw Exception('Failed to get recent activities: ${e.toString()}');
    }
  }

  Future<List<ActivityLog>> getActivitiesForDate(
    String babyId,
    DateTime date,
  ) async {
    try {
      debugPrint('🔄 Fetching activities for baby: $babyId on date: $date');
      
      final client = await this.client;
      
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(Duration(days: 1));
      
      debugPrint('📅 Filtering activities between (local): $startOfDay and $endOfDay');
      
      // Get activities from the activity_logs table
      final activityResponse = await client
          .from('activity_logs')
          .select()
          .eq('baby_id', babyId)
          .gte('recorded_at', startOfDay.toString())
          .lt('recorded_at', endOfDay.toString())
          .order('recorded_at', ascending: false);

      debugPrint('📥 Raw activity response from Supabase: $activityResponse');
      
      final activities = (activityResponse as List)
          .map((data) {
            debugPrint('🔄 Processing activity data: ${data['recorded_at']}');
            return ActivityLog.fromJson(data);
          })
          .toList();
          
      // Get milestones from the milestones table
      final milestoneResponse = await client
          .from('milestones')
          .select()
          .eq('baby_id', babyId)
          .gte('achieved_date', startOfDay.toString())
          .lt('achieved_date', endOfDay.toString())
          .order('achieved_date', ascending: false);

      debugPrint('📥 Raw milestone response from Supabase: $milestoneResponse');
      
      final milestones = (milestoneResponse as List)
          .map((data) {
            debugPrint('🔄 Processing milestone data: ${data['achieved_date']}');
            return Milestone.fromJson(data);
          })
          .toList();
          
      // Convert milestones to ActivityLog format (same as getRecentActivities)
      final milestoneActivities = milestones.map((milestone) {
        return ActivityLog.fromRawData(
          babyId: babyId,
          type: 'milestone',
          data: {
            'startTime': DateTime.parse(milestone.achievedDate.toString().replaceAll('+00:00', '').replaceAll('Z', '')), // Parse as local time, remove UTC markers
            'title': milestone.title,
            'description': milestone.description,
            'category': milestone.category.name,
            'type': milestone.type.name,
            'age_in_months': milestone.ageInMonths,
            'age_in_days': milestone.ageInDays,
            'is_custom': milestone.isCustom,
            'notes': milestone.notes,
          },
        );
      }).toList();
      
      // Combine and sort all activities by timestamp
      final allActivities = [...activities, ...milestoneActivities];
      allActivities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
          
      debugPrint('✅ Fetched ${activities.length} activities and ${milestones.length} milestones for the date');
      debugPrint('📊 Total combined activities: ${allActivities.length}');
      
      return allActivities;
    } catch (e) {
      debugPrint('❌ Error fetching activities for date: $e');
      throw Exception('Failed to get activities for date: ${e.toString()}');
    }
  }

  Future<List<ActivityLog>> getSleepHistory(
    String babyId, {
    int limit = 10,
    bool todayOnly = false,
  }) async {
    try {
      debugPrint('🔄 Fetching sleep history for baby: $babyId');
      
      final client = await this.client;
      var query = client
          .from('activity_logs')
          .select('*')
          .eq('baby_id', babyId)
          .eq('activity_type', ActivityType.sleep.name);
          
      if (todayOnly) {
        final now = DateTime.now();
        final startOfDay = DateTime(now.year, now.month, now.day);
        final endOfDay = startOfDay.add(Duration(days: 1));
        
        query = query
          .gte('recorded_at', startOfDay.toString())
          .lt('recorded_at', endOfDay.toString());
      }
      
      final response = await query
          .order('recorded_at', ascending: false)
          .limit(limit);

      final activities = (response as List)
          .map((data) => ActivityLog.fromJson(data))
          .toList();
          
      debugPrint('✅ Fetched ${activities.length} sleep records');
      return activities;
    } catch (e) {
      debugPrint('❌ Error fetching sleep history: $e');
      throw Exception('Failed to get sleep history: ${e.toString()}');
    }
  }

  Future<List<Map<String, dynamic>>> getActivitySummary(String babyId) async {
    try {
      debugPrint('📊 Fetching activity summary for baby: $babyId');
      debugPrint('📅 Current time (local): ${DateTime.now()}');
      
      final client = await this.client;
      final response = await client
          .rpc('get_todays_activity_summary', params: {'p_baby_id': babyId});
          
      final summary = List<Map<String, dynamic>>.from(response);
      debugPrint('✅ Activity summary fetched: ${summary.length} records');
      return summary;
    } catch (e) {
      debugPrint('❌ Error fetching activity summary: $e');
      throw Exception('Failed to get activity summary: ${e.toString()}');
    }
  }

  Future<void> setActiveBaby(String babyId) async {
    try {
      final client = await this.client;
      final currentUser = client.auth.currentUser;
      
      if (currentUser == null) {
        debugPrint('❌ No authenticated user found');
        return;
      }
      
      debugPrint('🔄 Setting active baby: $babyId for user: ${currentUser.id}');
      
      // First get current babies to see what we're working with
      final currentBabies = await client
        .from('baby_profiles')
        .select('id, name, is_active')
        .eq('user_id', currentUser.id);
      debugPrint('👶 Current babies before update: $currentBabies');
      
      // First set all babies as inactive
      final deactivateResult = await client
        .from('baby_profiles')
        .update({'is_active': false})
        .eq('user_id', currentUser.id);
      debugPrint('❌ Deactivated all babies result: $deactivateResult');
      
      // Then set the new baby as active
      final activateResult = await client
        .from('baby_profiles')
        .update({'is_active': true})
        .eq('id', babyId);
      debugPrint('✅ Activated baby result: $activateResult');
        
      // Verify the final state
      final finalBabies = await client
        .from('baby_profiles')
        .select('id, name, is_active')
        .eq('user_id', currentUser.id);
      debugPrint('👶 Final babies state: $finalBabies');
        
      debugPrint('✅ Set active baby: $babyId');
    } catch (e) {
      debugPrint('❌ Error setting active baby: $e');
    }
  }

  Future<String?> getActiveBabyId() async {
    try {
      final client = await this.client;
      final currentUser = client.auth.currentUser;
      
      if (currentUser == null) {
        debugPrint('❌ No authenticated user found');
        return null;
      }
      
      debugPrint('🔄 Getting active baby ID for user: ${currentUser.id}');
      
      // Get active baby
      final activeResponse = await client
          .from('baby_profiles')
          .select('id')
          .eq('user_id', currentUser.id)
          .eq('is_active', true)
          .limit(1)
          .maybeSingle();
      
      if (activeResponse != null) {
        debugPrint('✅ Found active baby: ${activeResponse['id']}');
        return activeResponse['id'] as String;
      }
      
      debugPrint('❌ No active baby found');
      return null;
    } catch (e) {
      debugPrint('❌ Error getting active baby ID: $e');
      return null;
    }
  }
  // ============================================================================
  // MILESTONE METHODS
  // ============================================================================

  /// Save a milestone to the database
  Future<void> saveMilestone(Milestone milestone) async {
    try {
      await _client.from('milestones').upsert(milestone.toJson());
      print('✅ Milestone saved successfully');
    } catch (e) {
      print('❌ Error saving milestone: $e');
      throw Exception('Failed to save milestone: $e');
    }
  }

  /// Get all milestones for a baby
  Future<List<Milestone>> getMilestones(String babyId) async {
    try {
      final response = await _client
          .from('milestones')
          .select()
          .eq('baby_id', babyId)
          .order('achieved_date', ascending: false);

      return (response as List)
          .map((json) => Milestone.fromJson(json))
          .toList();
    } catch (e) {
      print('❌ Error fetching milestones: $e');
      throw Exception('Failed to fetch milestones: $e');
    }
  }

  /// Get milestones by category
  Future<List<Milestone>> getMilestonesByCategory(
    String babyId, 
    MilestoneCategory category
  ) async {
    try {
      final response = await _client
          .from('milestones')
          .select()
          .eq('baby_id', babyId)
          .eq('category', category.name)
          .order('achieved_date', ascending: false);

      return (response as List)
          .map((json) => Milestone.fromJson(json))
          .toList();
    } catch (e) {
      print('❌ Error fetching milestones by category: $e');
      throw Exception('Failed to fetch milestones by category: $e');
    }
  }

  /// Get recent milestones (last 30 days)
  Future<List<Milestone>> getRecentMilestones(String babyId) async {
    try {
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      
      final response = await _client
          .from('milestones')
          .select()
          .eq('baby_id', babyId)
          .gte('achieved_date', thirtyDaysAgo.toIso8601String())
          .order('achieved_date', ascending: false);

      return (response as List)
          .map((json) => Milestone.fromJson(json))
          .toList();
    } catch (e) {
      print('❌ Error fetching recent milestones: $e');
      throw Exception('Failed to fetch recent milestones: $e');
    }
  }

  /// Delete a milestone
  Future<void> deleteMilestone(String milestoneId) async {
    try {
      await _client.from('milestones').delete().eq('id', milestoneId);
      print('✅ Milestone deleted successfully');
    } catch (e) {
      print('❌ Error deleting milestone: $e');
      throw Exception('Failed to delete milestone: $e');
    }
  }

  /// Get milestone statistics for a baby
  Future<Map<String, dynamic>> getMilestoneStats(String babyId) async {
    try {
      final milestones = await getMilestones(babyId);
      final categoryStats = <String, int>{};
      
      for (final milestone in milestones) {
        final category = milestone.category.name;
        categoryStats[category] = (categoryStats[category] ?? 0) + 1;
      }

      final recentCount = milestones.where(
        (m) => DateTime.now().difference(m.achievedDate).inDays <= 30
      ).length;

      return {
        'total': milestones.length,
        'recent': recentCount,
        'categories': categoryStats,
        'latest': milestones.isNotEmpty ? milestones.first.toJson() : null,
      };
    } catch (e) {
      print('❌ Error fetching milestone stats: $e');
      throw Exception('Failed to fetch milestone stats: $e');
    }
  }

  Future<void> insertMilestone(Milestone milestone) async {
    try {
      final client = await this.client;
      // Force schema refresh to avoid cache issues
      await client.from('milestones').select().limit(0);
      debugPrint('Schema refreshed for milestones table');
      
      // Use toInsertJson to avoid including auto-generated fields like id
      final insertData = milestone.toInsertJson();
      debugPrint('Inserting milestone data: $insertData');
      
      await client.from('milestones').insert(insertData);
      debugPrint('Milestone inserted successfully: ${milestone.title}');
    } catch (e) {
      debugPrint('Error inserting milestone: $e');
      rethrow;
    }
  }

  // ============================================================================
  // SCHEDULED ACTIVITIES METHODS
  // ============================================================================

  /// Create a new scheduled activity
  Future<ScheduledActivity> createScheduledActivity(ScheduledActivity activity) async {
    try {
      debugPrint('📅 Creating scheduled activity: ${activity.title}');
      
      final client = await this.client;
      final data = activity.toJson();
      data.remove('id'); // Remove ID to let database generate it
      
      final response = await client
          .from('scheduled_activities')
          .insert(data)
          .select()
          .single();
      
      final createdActivity = ScheduledActivity.fromJson(response);
      debugPrint('✅ Scheduled activity created: ${createdActivity.id}');
      return createdActivity;
    } catch (e) {
      debugPrint('❌ Error creating scheduled activity: $e');
      throw Exception('Failed to create scheduled activity: $e');
    }
  }

  /// Get scheduled activities for a specific date range
  Future<List<ScheduledActivity>> getScheduledActivitiesForDateRange(
    String babyId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      debugPrint('📅 Fetching scheduled activities for baby: $babyId from $startDate to $endDate');
      
      final client = await this.client;
      final response = await client
          .rpc('get_scheduled_activities_for_date_range', params: {
            'p_baby_id': babyId,
            'p_start_date': startDate.toIso8601String(),
            'p_end_date': endDate.toIso8601String(),
          });
      
      debugPrint('🔍 Raw response: $response');
      
      if (response == null) {
        debugPrint('⚠️ Received null response from RPC function');
        return [];
      }
      
      final responseList = response as List;
      debugPrint('📊 Response contains ${responseList.length} items');
      
      final activities = <ScheduledActivity>[];
      for (int i = 0; i < responseList.length; i++) {
        try {
          final data = responseList[i];
          debugPrint('🔍 Processing item $i: $data');
          final activity = ScheduledActivity.fromJson(data);
          activities.add(activity);
        } catch (e) {
          debugPrint('❌ Error parsing scheduled activity at index $i: $e');
          debugPrint('🔍 Problematic data: ${responseList[i]}');
          // Continue processing other items instead of failing completely
        }
      }
      
      debugPrint('✅ Retrieved ${activities.length} scheduled activities');
      return activities;
    } catch (e) {
      debugPrint('❌ Error fetching scheduled activities: $e');
      throw Exception('Failed to fetch scheduled activities: $e');
    }
  }

  /// Get scheduled activities for a specific date
  Future<List<ScheduledActivity>> getScheduledActivitiesForDate(
    String babyId,
    DateTime date,
  ) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    return getScheduledActivitiesForDateRange(babyId, startOfDay, endOfDay);
  }

  /// Get upcoming scheduled activities (for notifications)
  Future<List<Map<String, dynamic>>> getUpcomingScheduledActivities(
    String babyId, {
    int minutesAhead = 60,
  }) async {
    try {
      debugPrint('🔔 Fetching upcoming scheduled activities for baby: $babyId');
      
      final client = await this.client;
      final response = await client
          .rpc('get_upcoming_scheduled_activities', params: {
            'p_baby_id': babyId,
            'p_minutes_ahead': minutesAhead,
          });
      
      final activities = List<Map<String, dynamic>>.from(response);
      debugPrint('✅ Retrieved ${activities.length} upcoming scheduled activities');
      return activities;
    } catch (e) {
      debugPrint('❌ Error fetching upcoming scheduled activities: $e');
      throw Exception('Failed to fetch upcoming scheduled activities: $e');
    }
  }

  /// Update a scheduled activity
  Future<ScheduledActivity> updateScheduledActivity(ScheduledActivity activity) async {
    try {
      debugPrint('📅 Updating scheduled activity: ${activity.id}');
      
      final client = await this.client;
      final data = activity.toJson();
      data['updated_at'] = DateTime.now().toIso8601String();
      
      final response = await client
          .from('scheduled_activities')
          .update(data)
          .eq('id', activity.id)
          .select()
          .single();
      
      final updatedActivity = ScheduledActivity.fromJson(response);
      debugPrint('✅ Scheduled activity updated: ${updatedActivity.id}');
      return updatedActivity;
    } catch (e) {
      debugPrint('❌ Error updating scheduled activity: $e');
      throw Exception('Failed to update scheduled activity: $e');
    }
  }

  /// Mark a scheduled activity as completed
  Future<void> markScheduledActivityCompleted(String activityId) async {
    try {
      debugPrint('✅ Marking scheduled activity as completed: $activityId');
      
      final client = await this.client;
      await client.rpc('mark_scheduled_activity_completed', params: {
        'p_activity_id': activityId,
      });
      
      debugPrint('✅ Scheduled activity marked as completed');
    } catch (e) {
      debugPrint('❌ Error marking scheduled activity as completed: $e');
      throw Exception('Failed to mark scheduled activity as completed: $e');
    }
  }

  /// Delete a scheduled activity
  Future<void> deleteScheduledActivity(String activityId) async {
    try {
      debugPrint('🗑️ Deleting scheduled activity: $activityId');
      
      final client = await this.client;
      await client.from('scheduled_activities').delete().eq('id', activityId);
      
      debugPrint('✅ Scheduled activity deleted');
    } catch (e) {
      debugPrint('❌ Error deleting scheduled activity: $e');
      throw Exception('Failed to delete scheduled activity: $e');
    }
  }

  /// Delete records with filters and additional conditions
  Future<void> deleteWithFilters(
    String table,
    Map<String, dynamic> filters, {
    String? additionalFilters,
  }) async {
    final client = await this.client;
    var query = client.from(table).delete();

    // Apply basic filters
    filters.forEach((key, value) {
      query = query.eq(key, value);
    });

    // Apply additional filters if provided
    if (additionalFilters != null) {
      // Parse additional filters (simplified implementation)
      // Format: "column.operator.value"
      final parts = additionalFilters.split('.');
      if (parts.length >= 3) {
        final column = parts[0];
        final operator = parts[1];
        final value = parts.sublist(2).join('.');
        
        switch (operator) {
          case 'lt':
            query = query.lt(column, value);
            break;
          case 'gt':
            query = query.gt(column, value);
            break;
          case 'lte':
            query = query.lte(column, value);
            break;
          case 'gte':
            query = query.gte(column, value);
            break;
          case 'eq':
            query = query.eq(column, value);
            break;
          case 'neq':
            query = query.neq(column, value);
            break;
        }
      }
    }

    await query;
  }



  /// Retrieves all active scheduled activities for a specific baby
  /// 
  /// This method fetches scheduled activities that are currently active
  /// and orders them by scheduled time in ascending order.
  /// 
  /// Parameters:
  /// - [babyId]: The unique identifier of the baby
  /// 
  /// Returns:
  /// A [Future] that resolves to a [List] of [ScheduledActivity] objects
  /// 
  /// Throws:
  /// - [ArgumentError] if the baby ID is null, empty, or invalid format
  /// - [Exception] if the database query fails
  /// 
  /// Example:
  /// ```dart
  /// final activities = await supabaseService.getAllScheduledActivities('baby-123');
  /// print('Found ${activities.length} scheduled activities');
  /// ```
  Future<List<ScheduledActivity>> getAllScheduledActivities(String babyId) async {
    try {
      debugPrint('📅 Fetching all scheduled activities for baby: $babyId');
      
      final client = await this.client;
      final response = await client
          .from('scheduled_activities')
          .select()
          .eq('baby_id', babyId)
          .eq('is_active', true)
          .order('scheduled_time', ascending: true);
      
      final activities = (response as List)
          .map((data) => ScheduledActivity.fromJson(data))
          .toList();
      
      return activities;
    } catch (e) {
      debugPrint('❌ Error fetching all scheduled activities: $e');
      throw Exception('Failed to fetch all scheduled activities: $e');
    }
  }
}

// Extension for easier use
extension SupabaseExtension on SupabaseService {
  // Enhanced Activity Type Validation
  Future<bool> validateActivityType(String activityType) async {
    try {
      final client = await this.client;
      final result = await client.rpc('validate_activity_type',
          params: {'p_activity_type': activityType});
      return result == true;
    } catch (e) {
      return false;
    }
  }

  // Get valid activity types
  Future<List<String>> getValidActivityTypes() async {
    try {
      final client = await this.client;
      final result = await client.rpc('get_valid_activity_types');
      return List<String>.from(result ?? []);
    } catch (e) {
      throw Exception('Failed to get valid activity types: ${e.toString()}');
    }
  }

  // Enhanced Log Activity Method with validation
  Future<Map<String, dynamic>> logActivity({
    required String babyId,
    required String activityType,
    required String title,
    DateTime? startedAt,
    DateTime? endedAt,
    String? notes,
    Map<String, dynamic>? activityData,
  }) async {
    try {
      // Validate activity type before logging
      final isValid = await validateActivityType(activityType);
      if (!isValid) {
        final validTypes = await getValidActivityTypes();
        throw Exception(
            'Invalid activity type: $activityType. Valid types are: ${validTypes.join(', ')}');
      }

      final client = await this.client;

      // Call the log_activity function
      final response = await client.rpc('log_activity', params: {
        'p_baby_id': babyId,
        'p_activity_type': activityType,
        'p_title': title,
        'p_started_at': (startedAt ?? DateTime.now()).toIso8601String(),
        'p_ended_at': endedAt?.toIso8601String(),
        'p_notes': notes,
        'p_activity_data': activityData ?? {},
      });

      return {'success': true, 'activity_id': response};
    } catch (e) {
      // Enhanced error handling for temperature enum issues
      if (e.toString().contains('temperature enum value not found') ||
          e.toString().contains('Failed to verify temperature enum value')) {
        throw Exception(
            'Temperature activity type is not properly configured in the database. '
            'Please run the latest database migration to fix this issue. '
            'Original error: ${e.toString()}');
      }
      throw Exception('Failed to log activity: ${e.toString()}');
    }
  }

  // Enhanced Temperature Activity Logger with validation
  Future<Map<String, dynamic>> logTemperatureActivity({
    required String babyId,
    required double temperatureValue,
    String unit = 'celsius',
    String? measurementLocation,
    String? notes,
    DateTime? timestamp,
  }) async {
    try {
      // Validate temperature value
      if (temperatureValue < 30.0 || temperatureValue > 50.0) {
        throw Exception(
            'Invalid temperature value: $temperatureValue. Must be between 30.0 and 50.0');
      }

      // Validate unit
      if (!['celsius', 'fahrenheit'].contains(unit.toLowerCase())) {
        throw Exception(
            'Invalid temperature unit: $unit. Must be celsius or fahrenheit');
      }

      return await logActivity(
        babyId: babyId,
        activityType: 'temperature',
        title: 'Temperature Reading',
        startedAt: timestamp ?? DateTime.now(),
        notes: notes,
        activityData: {
          'temperature_value': temperatureValue,
          'unit': unit.toLowerCase(),
          'measurement_location': measurementLocation ?? 'forehead',
          'notes': notes,
        },
      );
    } catch (e) {
      throw Exception('Failed to log temperature activity: ${e.toString()}');
    }
  }

  // Enhanced Database Query with error handling
  Future<List<Map<String, dynamic>>> query(String sql,
      {Map<String, dynamic>? params}) async {
    try {
      final client = await this.client;
      final response = await client.rpc('sql_query', params: {
        'query': sql,
        'params': params ?? {},
      });
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw Exception('Failed to execute query: ${e.toString()}');
    }
  }

  // Execute SQL with enhanced error handling
  Future<dynamic> executeSql(String sql, {Map<String, dynamic>? params}) async {
    try {
      final client = await this.client;
      final response = await client.rpc('execute_sql', params: {
        'sql': sql,
        'params': params ?? {},
      });
      return response;
    } catch (e) {
      // Check for enum-related errors
      if (e.toString().contains('enum') &&
          e.toString().contains('temperature')) {
        throw Exception(
            'Database enum configuration error. Please ensure the latest migrations are applied. '
            'Original error: ${e.toString()}');
      }
      throw Exception('Failed to execute SQL: ${e.toString()}');
    }
  }

  // Test database connection and enum validation
  Future<Map<String, dynamic>> testDatabaseConnection() async {
    try {
      final client = await this.client;

      // Test basic connection
      final connectionTest =
          await client.from('user_profiles').select('count').limit(1);

      // Test enum validation
      final validTypes = await getValidActivityTypes();
      final temperatureValid = await validateActivityType('temperature');

      return {
        'connection': 'success',
        'valid_activity_types': validTypes,
        'temperature_enum_available': temperatureValid,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'connection': 'failed',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Get current user's subscription information
  Future<Map<String, dynamic>?> getCurrentUserSubscription() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return null;

      final response = await _client
          .from('user_subscriptions')
          .select('*')
          .eq('user_id', user.id)
          .maybeSingle();

      return response;
    } catch (e) {
      print('Error fetching user subscription: $e');
      return null;
    }
  }

  /// Update user's subscription information
  Future<bool> updateUserSubscription(Map<String, dynamic> subscriptionData) async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return false;

      // Add user_id to the subscription data
      subscriptionData['user_id'] = user.id;
      subscriptionData['updated_at'] = DateTime.now().toIso8601String();

      await _client
          .from('user_subscriptions')
          .upsert(subscriptionData);

      return true;
    } catch (e) {
      print('Error updating user subscription: $e');
      return false;
    }
  }

  /// Delete user's subscription (for cancellations)
  Future<bool> deleteUserSubscription() async {
    try {
      final user = _client.auth.currentUser;
      if (user == null) return false;

      await _client
          .from('user_subscriptions')
          .delete()
          .eq('user_id', user.id);

      return true;
    } catch (e) {
      print('Error deleting user subscription: $e');
      return false;
    }
  }
}
