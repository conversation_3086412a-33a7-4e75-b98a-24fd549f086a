import '../models/activity_log.dart';
import '../models/baby_profile.dart';
import '../models/milestone.dart';
import './openai_client.dart';
import './openai_service.dart';
import './supabase_service.dart';
import 'package:flutter/foundation.dart';
import 'dart:convert';

class AIInsightsService {
  static final AIInsightsService _instance = AIInsightsService._internal();
  OpenAIClient? _openAIClient;
  final SupabaseService _supabaseService = SupabaseService();

  factory AIInsightsService() {
    return _instance;
  }

  AIInsightsService._internal();

  Future<OpenAIClient> get _client async {
    if (_openAIClient == null) {
      final openAIService = OpenAIService();
      await openAIService.initialize();
      final dio = await openAIService.dio;
      _openAIClient = OpenAIClient(dio);
    }
    return _openAIClient!;
  }

  /// Generate comprehensive AI insights for all baby care categories
  Future<Map<String, String>> generateComprehensiveInsights({
    required BabyProfile babyProfile,
    required String babyId,
    List<ActivityLog>? activities,
    List<Milestone>? milestones,
  }) async {
    try {
      debugPrint('🤖 ===== STARTING AI INSIGHTS GENERATION =====');
      debugPrint('👶 Baby Profile: ${babyProfile.name} (ID: $babyId)');
      debugPrint('📅 Baby Age: ${DateTime.now().difference(babyProfile.birthDate).inDays} days');
      
      // Fetch recent activity logs from all categories
      debugPrint('📊 Fetching recent activities from Supabase...');
      final allActivities = activities ?? await _supabaseService.getRecentActivities(babyId, limit: 200);
      debugPrint('📈 Total activities fetched: ${allActivities.length}');
      
      // Fetch milestones if not provided
      debugPrint('🏆 Fetching milestones from Supabase...');
      final babyMilestones = milestones ?? await _supabaseService.getMilestones(babyId);
      debugPrint('🎯 Total milestones fetched: ${babyMilestones.length}');
      
      // Filter activities by type
      final sleepLogs = allActivities.where((log) => log.type == ActivityType.sleep).toList();
      final feedingLogs = allActivities.where((log) => log.type == ActivityType.feeding).toList();
      final diaperLogs = allActivities.where((log) => log.type == ActivityType.diaper).toList();
      final growthLogs = allActivities.where((log) => log.type == ActivityType.growth).toList();
      
      debugPrint('💤 Sleep logs: ${sleepLogs.length}');
      debugPrint('🍼 Feeding logs: ${feedingLogs.length}');
      debugPrint('👶 Diaper logs: ${diaperLogs.length}');
      debugPrint('📏 Growth logs: ${growthLogs.length}');
      
      // Log activity details for debugging
      if (sleepLogs.isNotEmpty) {
        debugPrint('💤 Recent sleep activities:');
        for (var i = 0; i < sleepLogs.take(3).length; i++) {
          final log = sleepLogs[i];
          debugPrint('  - ${log.timestamp}: ${log.duration?.inMinutes ?? 0} minutes, quality: ${log.notes ?? 'N/A'}');
        }
      }
      
      if (feedingLogs.isNotEmpty) {
        debugPrint('🍼 Recent feeding activities:');
        for (var i = 0; i < feedingLogs.take(3).length; i++) {
          final log = feedingLogs[i];
          debugPrint('  - ${log.timestamp}: ${log.data['quantity'] ?? 'Unknown'} ${log.data['unit'] ?? ''}');
        }
      }

      // Build comprehensive prompt
      debugPrint('✍️ Building comprehensive prompt for OpenAI...');
      final prompt = _buildComprehensiveInsightsPrompt(
        babyProfile: babyProfile,
        sleepLogs: sleepLogs,
        feedingLogs: feedingLogs,
        diaperLogs: diaperLogs,
        growthLogs: growthLogs,
      );
      
      debugPrint('📝 Prompt length: ${prompt.length} characters');
      debugPrint('🔤 Prompt preview: ${prompt.substring(0, prompt.length > 200 ? 200 : prompt.length)}...');

      debugPrint('🚀 Sending request to OpenAI API...');
      final stopwatch = Stopwatch()..start();
      
      final completion = await (await _client).createChatCompletion(
        messages: [
          Message(role: 'system', content: _getInsightsSystemPrompt()),
          Message(role: 'user', content: prompt),
        ],
        model: 'gpt-4o-mini',  // Correct model name as per OpenAI docs
        options: {
          'max_completion_tokens': 2000,
        },
      );
      
      stopwatch.stop();
      debugPrint('⏱️ OpenAI API response time: ${stopwatch.elapsedMilliseconds}ms');
      debugPrint('📤 OpenAI response length: ${completion.text.length} characters');
      debugPrint('📜 OpenAI response preview: ${completion.text.substring(0, completion.text.length > 300 ? 300 : completion.text.length)}...');

      // Parse the response into separate insights
      debugPrint('🔍 Parsing OpenAI response...');
      final parsedInsights = _parseInsightsResponse(completion.text);
      debugPrint('✅ Successfully parsed ${parsedInsights.length} insights');
      debugPrint('🔑 Insight keys: ${parsedInsights.keys.join(', ')}');
      
      // Log detailed insight content
      for (final entry in parsedInsights.entries) {
        final key = entry.key;
        final value = entry.value;
        debugPrint('📄 $key: "${value.length > 100 ? value.substring(0, 100) + '...' : value}"');
      }
      
      debugPrint('🤖 ===== AI INSIGHTS GENERATION COMPLETED =====');
      return parsedInsights;
    } catch (e, stackTrace) {
      debugPrint('❌ AI Insights Service Error: $e');
      debugPrint('📚 Stack trace: $stackTrace');
      debugPrint('🔄 Falling back to default insights');
      return _getDefaultInsights();
    }
  }

  String _buildComprehensiveInsightsPrompt({
    required BabyProfile babyProfile,
    required List<ActivityLog> sleepLogs,
    required List<ActivityLog> feedingLogs,
    required List<ActivityLog> diaperLogs,
    required List<ActivityLog> growthLogs,
  }) {
    final now = DateTime.now();
    final babyAge = now.difference(babyProfile.birthDate).inDays;
    final ageMonths = (babyAge / 30.44).round();

    return """
Analyze the comprehensive baby care data for ${babyProfile.name} and provide insights for all categories.

BABY PROFILE:
- Name: ${babyProfile.name}
- Age: $ageMonths months ($babyAge days)
- Birth Weight: ${babyProfile.birthWeight ?? 'Unknown'} kg
- Birth Height: ${babyProfile.birthHeight ?? 'Unknown'} cm
- Special Notes: ${babyProfile.note ?? 'None'}

SLEEP DATA (Last 30 days):
${_formatSleepLogs(sleepLogs)}

FEEDING DATA (Last 30 days):
${_formatFeedingLogs(feedingLogs)}

DIAPER DATA (Last 30 days):
${_formatDiaperLogs(diaperLogs)}

GROWTH DATA (Recent measurements):
${_formatGrowthLogs(growthLogs)}

Please provide insights and recommendations for each category in the following JSON format:
{
  "sleep_insights": "Brief analysis of sleep patterns and quality",
  "sleep_recommendations": "Actionable sleep improvement suggestions",
  "feeding_insights": "Analysis of feeding patterns and frequency",
  "feeding_recommendations": "Feeding schedule and nutrition recommendations",
  "diaper_insights": "Analysis of diaper change patterns and health indicators",
  "diaper_recommendations": "Health and hygiene recommendations",
  "growth_insights": "Analysis of growth trends and development",
  "growth_recommendations": "Growth and development guidance",
  "overall_insights": "Summary of baby's overall health and development",
  "overall_recommendations": "Priority recommendations for parents"
}

Keep each insight concise (2-3 sentences) and recommendations actionable. Focus on positive reinforcement while providing helpful guidance.
""";
  }

  String _formatSleepLogs(List<ActivityLog> logs) {
    if (logs.isEmpty) return "No sleep data available";
    
    final last7Days = logs.take(14).toList();
    final summary = StringBuffer();
    
    for (final log in last7Days) {
      final duration = log.duration?.inMinutes ?? 0;
      final hours = (duration / 60).toStringAsFixed(1);
      final date = log.timestamp.toString().substring(0, 10);
      summary.writeln("$date: ${hours}h sleep, Quality: ${log.notes ?? 'Good'}");
    }
    
    return summary.toString();
  }

  String _formatFeedingLogs(List<ActivityLog> logs) {
    if (logs.isEmpty) return "No feeding data available";
    
    final dailyFeedings = <String, int>{};
    for (final log in logs.take(21)) {
      final date = log.timestamp.toString().substring(0, 10);
      dailyFeedings[date] = (dailyFeedings[date] ?? 0) + 1;
    }
    
    final summary = StringBuffer();
    dailyFeedings.entries.take(7).forEach((entry) {
      summary.writeln("${entry.key}: ${entry.value} feeding sessions");
    });
    
    return summary.toString();
  }

  String _formatDiaperLogs(List<ActivityLog> logs) {
    if (logs.isEmpty) return "No diaper data available";
    
    final dailyChanges = <String, int>{};
    for (final log in logs.take(21)) {
      final date = log.timestamp.toString().substring(0, 10);
      dailyChanges[date] = (dailyChanges[date] ?? 0) + 1;
    }
    
    final summary = StringBuffer();
    dailyChanges.entries.take(7).forEach((entry) {
      summary.writeln("${entry.key}: ${entry.value} diaper changes");
    });
    
    return summary.toString();
  }

  String _formatGrowthLogs(List<ActivityLog> logs) {
    if (logs.isEmpty) return "No growth data available";
    
    final summary = StringBuffer();
    for (final log in logs.take(5)) {
      final date = log.timestamp.toString().substring(0, 10);
      final weight = log.data['quantity'] ?? 0;
      summary.writeln("$date: ${weight.toStringAsFixed(2)}kg");
    }
    
    return summary.toString();
  }

  Map<String, String> _parseInsightsResponse(String response) {
    try {
      debugPrint('📄 Attempting to parse AI response...');
      
      // Try to extract JSON from the response
      final jsonStart = response.indexOf('{');
      final jsonEnd = response.lastIndexOf('}');
      
      if (jsonStart != -1 && jsonEnd != -1) {
        final jsonString = response.substring(jsonStart, jsonEnd + 1);
        debugPrint('📜 Extracted JSON length: ${jsonString.length} characters');
        
        final Map<String, dynamic> parsed = jsonDecode(jsonString);
        debugPrint('✅ Successfully parsed JSON from AI response');
        debugPrint('🔑 AI response contains keys: ${parsed.keys.toList()}');
        
        final result = <String, String>{
          'sleep_insights': (parsed['sleep_insights'] as String?) ?? _getDefaultSleepInsights(),
          'sleep_recommendations': (parsed['sleep_recommendations'] as String?) ?? _getDefaultSleepRecommendations(),
          'feeding_insights': (parsed['feeding_insights'] as String?) ?? _getDefaultFeedingInsights(),
          'feeding_recommendations': (parsed['feeding_recommendations'] as String?) ?? _getDefaultFeedingRecommendations(),
          'diaper_insights': (parsed['diaper_insights'] as String?) ?? _getDefaultDiaperInsights(),
          'diaper_recommendations': (parsed['diaper_recommendations'] as String?) ?? _getDefaultDiaperRecommendations(),
          'growth_insights': (parsed['growth_insights'] as String?) ?? _getDefaultGrowthInsights(),
          'growth_recommendations': (parsed['growth_recommendations'] as String?) ?? _getDefaultGrowthRecommendations(),
          'overall_insights': (parsed['overall_insights'] as String?) ?? _getDefaultOverallInsights(),
          'overall_recommendations': (parsed['overall_recommendations'] as String?) ?? _getDefaultOverallRecommendations(),
        };
        
        // Log which items are from AI vs defaults
        for (final key in result.keys) {
          final isFromAI = parsed.containsKey(key) && parsed[key] != null;
          debugPrint('🤖 $key: ${isFromAI ? 'FROM AI' : 'DEFAULT FALLBACK'}');
        }
        
        return result;
      } else {
        debugPrint('⚠️ No valid JSON found in AI response');
      }
    } catch (e) {
      debugPrint('❌ Failed to parse AI insights response: $e');
      debugPrint('🔄 Response content: ${response.length > 200 ? response.substring(0, 200) + '...' : response}');
    }
    
    debugPrint('🛡️ Falling back to all default insights');
    return _getDefaultInsights();
  }

  Map<String, String> _getDefaultInsights() {
    return {
      'sleep_insights': _getDefaultSleepInsights(),
      'sleep_recommendations': _getDefaultSleepRecommendations(),
      'feeding_insights': _getDefaultFeedingInsights(),
      'feeding_recommendations': _getDefaultFeedingRecommendations(),
      'diaper_insights': _getDefaultDiaperInsights(),
      'diaper_recommendations': _getDefaultDiaperRecommendations(),
      'growth_insights': _getDefaultGrowthInsights(),
      'growth_recommendations': _getDefaultGrowthRecommendations(),
      'overall_insights': _getDefaultOverallInsights(),
      'overall_recommendations': _getDefaultOverallRecommendations(),
    };
  }

  String _getDefaultSleepInsights() => "Continue tracking sleep patterns to get personalized insights about your baby's rest quality and schedule.";
  String _getDefaultSleepRecommendations() => "Maintain consistent bedtime routines and ensure a calm sleep environment for optimal rest.";
  String _getDefaultFeedingInsights() => "Keep logging feeding sessions to understand your baby's nutrition patterns and preferences.";
  String _getDefaultFeedingRecommendations() => "Follow your baby's hunger cues and maintain regular feeding schedules.";
  String _getDefaultDiaperInsights() => "Regular diaper changes indicate healthy digestion and hydration patterns.";
  String _getDefaultDiaperRecommendations() => "Continue monitoring diaper changes as they reflect your baby's health and nutrition.";
  String _getDefaultGrowthInsights() => "Track growth measurements regularly to monitor your baby's healthy development.";
  String _getDefaultGrowthRecommendations() => "Consult your pediatrician for regular checkups and growth assessments.";
  String _getDefaultOverallInsights() => "Your baby's development is progressing well. Continue with consistent care routines.";
  String _getDefaultOverallRecommendations() => "Maintain regular feeding, sleeping, and play schedules for optimal development.";

  String _getInsightsSystemPrompt() {
    return """
You are an expert pediatric consultant and child development specialist with extensive experience in baby care. 
Your role is to analyze baby activity data and provide evidence-based insights and recommendations for parents.

Key principles:
1. Always be positive and encouraging to parents
2. Base recommendations on pediatric best practices
3. Acknowledge when patterns are healthy and normal
4. Provide specific, actionable advice
5. Recommend consulting healthcare providers when appropriate
6. Keep insights concise but informative
7. Focus on developmental milestones appropriate for the baby's age
8. Consider individual variations in baby development

Respond only with the requested JSON format containing insights and recommendations for each category.
""";
  }
  /// Method to generate milestone predictions
  Future<Map<String, dynamic>> generateMilestonePredictions({
    required BabyProfile babyProfile,
    required String babyId,
  }) async {
    // Example stub method - returns static predictions
    return {
      "gross_motor": "Strong gross motor skills development expected",
      "fine_motor": "Improvement in fine motor control forecasted",
      "language": "Language comprehension progressing steadily",
    };
  }
}
