import 'dart:math';

class WHOPercentileService {
  static const Map<String, Map<String, List<Map<String, double>>>> _whoData = {
    'weight': {
      'boys': [
        { 'age': 0,  '3rd': 2.5,  '15th': 3.0,  '50th': 3.3,  '85th': 3.7,  '97th': 4.4 },
        { 'age': 1,  '3rd': 3.4,  '15th': 4.0,  '50th': 4.5,  '85th': 5.1,  '97th': 5.8 },
        { 'age': 2,  '3rd': 4.3,  '15th': 5.1,  '50th': 5.6,  '85th': 6.3,  '97th': 7.1 },
        { 'age': 3,  '3rd': 5.0,  '15th': 5.8,  '50th': 6.4,  '85th': 7.2,  '97th': 8.0 },
        { 'age': 4,  '3rd': 5.6,  '15th': 6.4,  '50th': 7.0,  '85th': 7.8,  '97th': 8.7 },
        { 'age': 5,  '3rd': 6.0,  '15th': 6.9,  '50th': 7.5,  '85th': 8.4,  '97th': 9.3 },
        { 'age': 6,  '3rd': 6.4,  '15th': 7.3,  '50th': 7.9,  '85th': 8.8,  '97th': 9.8 },
        { 'age': 7,  '3rd': 6.7,  '15th': 7.6,  '50th': 8.3,  '85th': 9.2,  '97th': 10.3 },
        { 'age': 8,  '3rd': 7.0,  '15th': 7.9,  '50th': 8.6,  '85th': 9.6,  '97th': 10.7 },
        { 'age': 9,  '3rd': 7.3,  '15th': 8.2,  '50th': 8.9,  '85th': 9.9,  '97th': 11.0 },
        { 'age': 10, '3rd': 7.5,  '15th': 8.4,  '50th': 9.2,  '85th': 10.2, '97th': 11.4 },
        { 'age': 11, '3rd': 7.7,  '15th': 8.6,  '50th': 9.4,  '85th': 10.5, '97th': 11.7 },
        { 'age': 12, '3rd': 7.9,  '15th': 8.8,  '50th': 9.6,  '85th': 10.8, '97th': 12.0 },
        { 'age': 13, '3rd': 8.1,  '15th': 9.0,  '50th': 9.9,  '85th': 11.0,  '97th': 12.3 },
        { 'age': 14, '3rd': 8.3,  '15th': 9.2,  '50th': 10.1, '85th': 11.3,  '97th': 12.6 },
        { 'age': 15, '3rd': 8.5,  '15th': 9.4,  '50th': 10.3, '85th': 11.5,  '97th': 12.9 },
        { 'age': 16, '3rd': 8.7,  '15th': 9.6,  '50th': 10.5, '85th': 11.7,  '97th': 13.2 },
        { 'age': 17, '3rd': 8.9,  '15th': 9.8,  '50th': 10.7, '85th': 12.0,  '97th': 13.5 },
        { 'age': 18, '3rd': 9.1,  '15th': 10.0, '50th': 10.9, '85th': 12.2,  '97th': 13.8 },
        { 'age': 19, '3rd': 9.3,  '15th': 10.2, '50th': 11.1, '85th': 12.5,  '97th': 14.1 },
        { 'age': 20, '3rd': 9.4,  '15th': 10.4, '50th': 11.3, '85th': 12.7,  '97th': 14.4 },
        { 'age': 21, '3rd': 9.6,  '15th': 10.6, '50th': 11.5, '85th': 13.0,  '97th': 14.7 },
        { 'age': 22, '3rd': 9.7,  '15th': 10.8, '50th': 11.7, '85th': 13.2,  '97th': 15.0 },
        { 'age': 23, '3rd': 9.9,  '15th': 11.0, '50th': 11.9, '85th': 13.5,  '97th': 15.3 },
        { 'age': 24, '3rd': 10.0, '15th': 11.2, '50th': 12.2, '85th': 13.8,  '97th': 15.6 },
        { 'age': 25, '3rd': 10.2, '15th': 11.4, '50th': 12.4, '85th': 14.1,  '97th': 15.9 },
        { 'age': 26, '3rd': 10.3, '15th': 11.6, '50th': 12.6, '85th': 14.4,  '97th': 16.2 },
        { 'age': 27, '3rd': 10.5, '15th': 11.8, '50th': 12.8, '85th': 14.6,  '97th': 16.5 },
        { 'age': 28, '3rd': 10.6, '15th': 12.0, '50th': 13.0, '85th': 14.9,  '97th': 16.8 },
        { 'age': 29, '3rd': 10.8, '15th': 12.2, '50th': 13.2, '85th': 15.1,  '97th': 17.1 },
        { 'age': 30, '3rd': 10.9, '15th': 12.4, '50th': 13.4, '85th': 15.4,  '97th': 17.4 },
        { 'age': 31, '3rd': 11.1, '15th': 12.6, '50th': 13.6, '85th': 15.6,  '97th': 17.7 },
        { 'age': 32, '3rd': 11.2, '15th': 12.8, '50th': 13.8, '85th': 15.9,  '97th': 18.0 },
        { 'age': 33, '3rd': 11.4, '15th': 13.0, '50th': 14.0, '85th': 16.1,  '97th': 18.3 },
        { 'age': 34, '3rd': 11.5, '15th': 13.2, '50th': 14.2, '85th': 16.4,  '97th': 18.6 },
        { 'age': 35, '3rd': 11.7, '15th': 13.4, '50th': 14.4, '85th': 16.6,  '97th': 18.9 },
        { 'age': 36, '3rd': 11.8, '15th': 13.6, '50th': 14.7, '85th': 16.9,  '97th': 19.2 },
        { 'age': 42, '3rd': 12.4, '15th': 14.1, '50th': 15.3, '85th': 17.6,  '97th': 20.1 },
        { 'age': 48, '3rd': 12.9, '15th': 14.7, '50th': 15.9, '85th': 18.3,  '97th': 21.0 },
        { 'age': 54, '3rd': 13.4, '15th': 15.2, '50th': 16.5, '85th': 19.0,  '97th': 21.9 },
        { 'age': 60, '3rd': 13.9, '15th': 15.8, '50th': 17.1, '85th': 19.7,  '97th': 22.8 },
      ],
      'girls': [
        { 'age': 0,  '3rd': 2.4,  '15th': 2.9,  '50th': 3.2,  '85th': 3.6,  '97th': 4.2 },
        { 'age': 1,  '3rd': 3.2,  '15th': 3.7,  '50th': 4.2,  '85th': 4.7,  '97th': 5.5 },
        { 'age': 2,  '3rd': 3.9,  '15th': 4.7,  '50th': 5.1,  '85th': 5.7,  '97th': 6.6 },
        { 'age': 3,  '3rd': 4.5,  '15th': 5.3,  '50th': 5.8,  '85th': 6.6,  '97th': 7.5 },
        { 'age': 4,  '3rd': 5.0,  '15th': 5.8,  '50th': 6.4,  '85th': 7.3,  '97th': 8.3 },
        { 'age': 5,  '3rd': 5.4,  '15th': 6.2,  '50th': 6.9,  '85th': 7.8,  '97th': 8.9 },
        { 'age': 6,  '3rd': 5.7,  '15th': 6.6,  '50th': 7.3,  '85th': 8.2,  '97th': 9.3 },
        { 'age': 7,  '3rd': 6.0,  '15th': 6.9,  '50th': 7.6,  '85th': 8.6,  '97th': 9.8 },
        { 'age': 8,  '3rd': 6.3,  '15th': 7.2,  '50th': 7.9,  '85th': 8.9,  '97th': 10.2 },
        { 'age': 9,  '3rd': 6.5,  '15th': 7.4,  '50th': 8.2,  '85th': 9.2,  '97th': 10.5 },
        { 'age': 10, '3rd': 6.8,  '15th': 7.7,  '50th': 8.5,  '85th': 9.5,  '97th': 10.9 },
        { 'age': 11, '3rd': 7.0,  '15th': 7.9,  '50th': 8.7,  '85th': 9.8,  '97th': 11.2 },
        { 'age': 12, '3rd': 7.2,  '15th': 8.1,  '50th': 8.9,  '85th': 10.1, '97th': 11.5 },
        { 'age': 13, '3rd': 7.4,  '15th': 8.3,  '50th': 9.2,  '85th': 10.3, '97th': 11.8 },
        { 'age': 14, '3rd': 7.6,  '15th': 8.5,  '50th': 9.4,  '85th': 10.6, '97th': 12.1 },
        { 'age': 15, '3rd': 7.8,  '15th': 8.7,  '50th': 9.6,  '85th': 10.9, '97th': 12.4 },
        { 'age': 16, '3rd': 7.8,  '15th': 8.6,  '50th': 9.8,  '85th': 11.1,  '97th': 12.8 },
        { 'age': 17, '3rd': 8.0,  '15th': 8.8,  '50th': 10.0, '85th': 11.4,  '97th': 13.1 },
        { 'age': 18, '3rd': 8.2,  '15th': 9.0,  '50th': 10.2, '85th': 11.6,  '97th': 13.4 },
        { 'age': 19, '3rd': 8.3,  '15th': 9.2,  '50th': 10.4, '85th': 11.9,  '97th': 13.7 },
        { 'age': 20, '3rd': 8.5,  '15th': 9.4,  '50th': 10.6, '85th': 12.1,  '97th': 14.0 },
        { 'age': 21, '3rd': 8.7,  '15th': 9.6,  '50th': 10.9, '85th': 12.3,  '97th': 14.1 },
        { 'age': 22, '3rd': 8.8,  '15th': 9.8,  '50th': 11.1, '85th': 12.6,  '97th': 14.4 },
        { 'age': 23, '3rd': 9.0,  '15th': 10.0, '50th': 11.3, '85th': 12.8,  '97th': 14.7 },
        { 'age': 24, '3rd': 9.2,  '15th': 10.2, '50th': 11.5, '85th': 13.1,  '97th': 15.0 },
        { 'age': 25, '3rd': 9.3,  '15th': 10.4, '50th': 11.7, '85th': 13.4,  '97th': 15.3 },
        { 'age': 26, '3rd': 9.5,  '15th': 10.6, '50th': 11.9, '85th': 13.6,  '97th': 15.6 },
        { 'age': 27, '3rd': 9.6,  '15th': 10.8, '50th': 12.1, '85th': 13.9,  '97th': 15.9 },
        { 'age': 28, '3rd': 9.8,  '15th': 11.0, '50th': 12.3, '85th': 14.1,  '97th': 16.2 },
        { 'age': 29, '3rd': 9.9,  '15th': 11.2, '50th': 12.5, '85th': 14.4,  '97th': 16.5 },
        { 'age': 30, '3rd': 10.1, '15th': 11.4, '50th': 12.6, '85th': 14.7,  '97th': 16.8 },
        { 'age': 31, '3rd': 10.2, '15th': 11.6, '50th': 12.8, '85th': 15.0,  '97th': 17.1 },
        { 'age': 32, '3rd': 10.4, '15th': 11.8, '50th': 13.0, '85th': 15.2,  '97th': 17.4 },
        { 'age': 33, '3rd': 10.5, '15th': 12.0, '50th': 13.2, '85th': 15.5,  '97th': 17.7 },
        { 'age': 34, '3rd': 10.7, '15th': 12.2, '50th': 13.4, '85th': 15.8,  '97th': 18.0 },
        { 'age': 35, '3rd': 10.8, '15th': 12.4, '50th': 13.6, '85th': 16.0,  '97th': 18.3 },
        { 'age': 36, '3rd': 11.0, '15th': 12.6, '50th': 13.7, '85th': 16.3,  '97th': 18.6 },
        { 'age': 42, '3rd': 11.3, '15th': 13.1, '50th': 14.2, '85th': 17.0,  '97th': 19.5 },
        { 'age': 48, '3rd': 11.6, '15th': 13.6, '50th': 14.8, '85th': 17.7,  '97th': 20.4 },
        { 'age': 54, '3rd': 11.9, '15th': 14.1, '50th': 15.3, '85th': 18.4,  '97th': 21.3 },
        { 'age': 60, '3rd': 12.2, '15th': 14.6, '50th': 15.9, '85th': 19.1,  '97th': 22.2 },
      ],
    },
    'height': {
      'boys': [
        {
          'age': 0,
          '3rd': 46.1,
          '10th': 47.5,
          '25th': 48.9,
          '50th': 49.9,
          '75th': 51.0,
          '90th': 52.4,
          '97th': 53.7
        },
        {
          'age': 1,
          '3rd': 48.9,
          '10th': 50.8,
          '25th': 52.4,
          '50th': 54.7,
          '75th': 57.0,
          '90th': 59.2,
          '97th': 61.7
        },
        {
          'age': 2,
          '3rd': 52.4,
          '10th': 54.4,
          '25th': 56.2,
          '50th': 58.4,
          '75th': 60.8,
          '90th': 63.1,
          '97th': 65.5
        },
        {
          'age': 3,
          '3rd': 55.3,
          '10th': 57.3,
          '25th': 59.0,
          '50th': 61.4,
          '75th': 63.8,
          '90th': 66.1,
          '97th': 68.6
        },
        {
          'age': 4,
          '3rd': 57.6,
          '10th': 59.7,
          '25th': 61.4,
          '50th': 63.9,
          '75th': 66.3,
          '90th': 68.6,
          '97th': 71.1
        },
        {
          'age': 5,
          '3rd': 59.6,
          '10th': 61.7,
          '25th': 63.5,
          '50th': 65.9,
          '75th': 68.4,
          '90th': 70.7,
          '97th': 73.3
        },
        {
          'age': 6,
          '3rd': 61.2,
          '10th': 63.3,
          '25th': 65.1,
          '50th': 67.6,
          '75th': 70.1,
          '90th': 72.5,
          '97th': 75.0
        },
        {
          'age': 7,
          '3rd': 62.7,
          '10th': 64.8,
          '25th': 66.7,
          '50th': 69.2,
          '75th': 71.7,
          '90th': 74.2,
          '97th': 76.8
        },
        {
          'age': 8,
          '3rd': 64.0,
          '10th': 66.2,
          '25th': 68.1,
          '50th': 70.6,
          '75th': 73.2,
          '90th': 75.7,
          '97th': 78.4
        },
        {
          'age': 9,
          '3rd': 65.2,
          '10th': 67.5,
          '25th': 69.4,
          '50th': 72.0,
          '75th': 74.5,
          '90th': 77.1,
          '97th': 79.8
        },
        {
          'age': 10,
          '3rd': 66.4,
          '10th': 68.7,
          '25th': 70.6,
          '50th': 73.3,
          '75th': 75.9,
          '90th': 78.5,
          '97th': 81.2
        },
        {
          'age': 11,
          '3rd': 67.6,
          '10th': 69.9,
          '25th': 71.9,
          '50th': 74.5,
          '75th': 77.2,
          '90th': 79.8,
          '97th': 82.5
        },
        {
          'age': 12,
          '3rd': 71.0,
          '10th': 72.6,
          '25th': 74.1,
          '50th': 75.7,
          '75th': 77.3,
          '90th': 78.8,
          '97th': 80.5
        },
        {
          'age': 15,
          '3rd': 70.6,
          '10th': 73.1,
          '25th': 75.2,
          '50th': 78.0,
          '75th': 80.7,
          '90th': 83.5,
          '97th': 86.3
        },
        {
          'age': 18,
          '3rd': 74.2,
          '10th': 76.9,
          '25th': 79.1,
          '50th': 82.3,
          '75th': 85.1,
          '90th': 88.0,
          '97th': 91.1
        },
        {
          'age': 21,
          '3rd': 77.2,
          '10th': 80.2,
          '25th': 82.5,
          '50th': 86.0,
          '75th': 89.0,
          '90th': 92.0,
          '97th': 95.3
        },
        {
          'age': 24,
          '3rd': 78.7,
          '10th': 81.7,
          '25th': 84.1,
          '50th': 87.7,
          '75th': 90.8,
          '90th': 93.9,
          '97th': 97.3
        },
        {
          'age': 30,
          '3rd': 82.5,
          '10th': 85.7,
          '25th': 88.3,
          '50th': 92.1,
          '75th': 95.4,
          '90th': 98.7,
          '97th': 102.3
        },
        {
          'age': 36,
          '3rd': 86.0,
          '10th': 89.4,
          '25th': 92.1,
          '50th': 96.1,
          '75th': 99.8,
          '90th': 103.3,
          '97th': 107.2
        },
        {
          'age': 42,
          '3rd': 89.2,
          '10th': 92.8,
          '25th': 95.7,
          '50th': 99.9,
          '75th': 103.8,
          '90th': 107.6,
          '97th': 111.7
        },
        {
          'age': 48,
          '3rd': 92.2,
          '10th': 96.0,
          '25th': 99.1,
          '50th': 103.5,
          '75th': 107.6,
          '90th': 111.7,
          '97th': 116.1
        },
        {
          'age': 54,
          '3rd': 95.0,
          '10th': 99.0,
          '25th': 102.3,
          '50th': 106.9,
          '75th': 111.2,
          '90th': 115.5,
          '97th': 120.2
        },
        {
          'age': 60,
          '3rd': 97.7,
          '10th': 101.9,
          '25th': 105.3,
          '50th': 110.2,
          '75th': 114.7,
          '90th': 119.2,
          '97th': 124.1
        },
      ],
      'girls': [
        {
          'age': 0,
          '3rd': 43.6,
          '10th': 45.4,
          '25th': 47.1,
          '50th': 49.1,
          '75th': 51.1,
          '90th': 53.1,
          '97th': 55.6
        },
        {
          'age': 1,
          '3rd': 48.2,
          '10th': 50.0,
          '25th': 51.7,
          '50th': 53.7,
          '75th': 55.8,
          '90th': 57.8,
          '97th': 60.1
        },
        {
          'age': 2,
          '3rd': 51.7,
          '10th': 53.5,
          '25th': 55.3,
          '50th': 57.4,
          '75th': 59.6,
          '90th': 61.7,
          '97th': 64.0
        },
        {
          'age': 3,
          '3rd': 54.7,
          '10th': 56.6,
          '25th': 58.4,
          '50th': 60.4,
          '75th': 62.7,
          '90th': 64.9,
          '97th': 67.3
        },
        {
          'age': 4,
          '3rd': 57.1,
          '10th': 59.0,
          '25th': 60.9,
          '50th': 62.9,
          '75th': 65.3,
          '90th': 67.6,
          '97th': 70.0
        },
        {
          'age': 5,
          '3rd': 59.1,
          '10th': 61.0,
          '25th': 62.9,
          '50th': 65.0,
          '75th': 67.4,
          '90th': 69.8,
          '97th': 72.3
        },
        {
          'age': 6,
          '3rd': 60.8,
          '10th': 62.7,
          '25th': 64.6,
          '50th': 66.8,
          '75th': 69.2,
          '90th': 71.6,
          '97th': 74.2
        },
        {
          'age': 7,
          '3rd': 62.2,
          '10th': 64.1,
          '25th': 66.1,
          '50th': 68.3,
          '75th': 70.8,
          '90th': 73.2,
          '97th': 75.8
        },
        {
          'age': 8,
          '3rd': 63.4,
          '10th': 65.4,
          '25th': 67.4,
          '50th': 69.7,
          '75th': 72.2,
          '90th': 74.7,
          '97th': 77.3
        },
        {
          'age': 9,
          '3rd': 64.6,
          '10th': 66.6,
          '25th': 68.6,
          '50th': 70.9,
          '75th': 73.5,
          '90th': 76.0,
          '97th': 78.7
        },
        {
          'age': 10,
          '3rd': 65.7,
          '10th': 67.7,
          '25th': 69.7,
          '50th': 72.1,
          '75th': 74.7,
          '90th': 77.3,
          '97th': 80.0
        },
        {
          'age': 11,
          '3rd': 66.7,
          '10th': 68.8,
          '25th': 70.8,
          '50th': 73.2,
          '75th': 75.9,
          '90th': 78.5,
          '97th': 81.2
        },
        {
          'age': 12,
          '3rd': 67.7,
          '10th': 69.8,
          '25th': 71.8,
          '50th': 74.3,
          '75th': 77.0,
          '90th': 79.6,
          '97th': 82.4
        },
        {
          'age': 15,
          '3rd': 70.0,
          '10th': 72.2,
          '25th': 74.2,
          '50th': 76.8,
          '75th': 79.6,
          '90th': 82.3,
          '97th': 85.2
        },
        {
          'age': 18,
          '3rd': 72.8,
          '10th': 75.0,
          '25th': 77.1,
          '50th': 79.8,
          '75th': 82.6,
          '90th': 85.4,
          '97th': 88.4
        },
        {
          'age': 21,
          '3rd': 75.0,
          '10th': 77.3,
          '25th': 79.5,
          '50th': 82.3,
          '75th': 85.2,
          '90th': 88.1,
          '97th': 91.2
        },
        {
          'age': 24,
          '3rd': 76.0,
          '10th': 78.4,
          '25th': 80.7,
          '50th': 83.5,
          '75th': 86.5,
          '90th': 89.6,
          '97th': 92.9
        },
        {
          'age': 30,
          '3rd': 80.1,
          '10th': 82.6,
          '25th': 85.1,
          '50th': 88.0,
          '75th': 91.1,
          '90th': 94.3,
          '97th': 97.7
        },
        {
          'age': 36,
          '3rd': 83.6,
          '10th': 86.2,
          '25th': 88.9,
          '50th': 92.0,
          '75th': 95.2,
          '90th': 98.6,
          '97th': 102.1
        },
        {
          'age': 42,
          '3rd': 86.8,
          '10th': 89.6,
          '25th': 92.4,
          '50th': 95.7,
          '75th': 99.1,
          '90th': 102.7,
          '97th': 106.4
        },
        {
          'age': 48,
          '3rd': 89.8,
          '10th': 92.8,
          '25th': 95.7,
          '50th': 99.2,
          '75th': 102.8,
          '90th': 106.6,
          '97th': 110.5
        },
        {
          'age': 54,
          '3rd': 92.6,
          '10th': 95.8,
          '25th': 98.8,
          '50th': 102.5,
          '75th': 106.3,
          '90th': 110.3,
          '97th': 114.4
        },
        {
          'age': 60,
          '3rd': 95.2,
          '10th': 98.6,
          '25th': 101.7,
          '50th': 105.6,
          '75th': 109.6,
          '90th': 113.8,
          '97th': 118.1
        },
      ],
    },
    'head_circumference': {
      'boys': [
        {
          'age': 0,
          '3rd': 32.1,
          '10th': 33.2,
          '25th': 34.1,
          '50th': 34.5,
          '75th': 35.7,
          '90th': 36.3,
          '97th': 36.9
        },
        {
          'age': 1,
          '3rd': 35.7,
          '10th': 37.0,
          '25th': 37.9,
          '50th': 39.1,
          '75th': 40.2,
          '90th': 41.3,
          '97th': 42.6
        },
        {
          'age': 2,
          '3rd': 37.9,
          '10th': 39.1,
          '25th': 40.0,
          '50th': 41.2,
          '75th': 42.4,
          '90th': 43.5,
          '97th': 44.8
        },
        {
          'age': 3,
          '3rd': 39.5,
          '10th': 40.7,
          '25th': 41.6,
          '50th': 42.8,
          '75th': 44.0,
          '90th': 45.1,
          '97th': 46.4
        },
        {
          'age': 4,
          '3rd': 40.8,
          '10th': 42.0,
          '25th': 42.9,
          '50th': 44.2,
          '75th': 45.4,
          '90th': 46.5,
          '97th': 47.8
        },
        {
          'age': 5,
          '3rd': 41.8,
          '10th': 43.1,
          '25th': 44.0,
          '50th': 45.2,
          '75th': 46.4,
          '90th': 47.6,
          '97th': 48.9
        },
        {
          'age': 6,
          '3rd': 42.7,
          '10th': 44.0,
          '25th': 44.9,
          '50th': 46.1,
          '75th': 47.3,
          '90th': 48.5,
          '97th': 49.8
        },
        {
          'age': 7,
          '3rd': 43.5,
          '10th': 44.8,
          '25th': 45.7,
          '50th': 46.9,
          '75th': 48.1,
          '90th': 49.3,
          '97th': 50.6
        },
        {
          'age': 8,
          '3rd': 44.2,
          '10th': 45.5,
          '25th': 46.4,
          '50th': 47.6,
          '75th': 48.8,
          '90th': 50.0,
          '97th': 51.4
        },
        {
          'age': 9,
          '3rd': 44.8,
          '10th': 46.1,
          '25th': 47.0,
          '50th': 48.2,
          '75th': 49.5,
          '90th': 50.7,
          '97th': 52.1
        },
        {
          'age': 10,
          '3rd': 45.4,
          '10th': 46.7,
          '25th': 47.6,
          '50th': 48.8,
          '75th': 50.1,
          '90th': 51.3,
          '97th': 52.7
        },
        {
          'age': 11,
          '3rd': 45.9,
          '10th': 47.2,
          '25th': 48.1,
          '50th': 49.4,
          '75th': 50.7,
          '90th': 51.9,
          '97th': 53.3
        },
        {
          'age': 12,
          '3rd': 44.2,
          '10th': 45.0,
          '25th': 45.5,
          '50th': 46.1,
          '75th': 46.8,
          '90th': 47.4,
          '97th': 48.0
        },
        {
          'age': 15,
          '3rd': 47.3,
          '10th': 48.6,
          '25th': 49.5,
          '50th': 50.8,
          '75th': 52.2,
          '90th': 53.4,
          '97th': 54.9
        },
        {
          'age': 18,
          '3rd': 48.8,
          '10th': 50.1,
          '25th': 51.0,
          '50th': 52.4,
          '75th': 53.8,
          '90th': 55.1,
          '97th': 56.7
        },
        {
          'age': 21,
          '3rd': 49.4,
          '10th': 50.7,
          '25th': 51.7,
          '50th': 53.1,
          '75th': 54.5,
          '90th': 55.9,
          '97th': 57.5
        },
        {
          'age': 24,
          '3rd': 50.5,
          '10th': 51.8,
          '25th': 52.8,
          '50th': 54.3,
          '75th': 55.8,
          '90th': 57.2,
          '97th': 58.9
        },
        {
          'age': 30,
          '3rd': 51.0,
          '10th': 52.3,
          '25th': 53.3,
          '50th': 54.8,
          '75th': 56.3,
          '90th': 57.8,
          '97th': 59.5
        },
        {
          'age': 36,
          '3rd': 51.4,
          '10th': 52.7,
          '25th': 53.8,
          '50th': 55.3,
          '75th': 56.8,
          '90th': 58.3,
          '97th': 60.0
        },
        {
          'age': 42,
          '3rd': 51.8,
          '10th': 53.1,
          '25th': 54.2,
          '50th': 55.7,
          '75th': 57.3,
          '90th': 58.8,
          '97th': 60.5
        },
        {
          'age': 48,
          '3rd': 52.1,
          '10th': 53.4,
          '25th': 54.5,
          '50th': 56.1,
          '75th': 57.7,
          '90th': 59.2,
          '97th': 61.0
        },
        {
          'age': 54,
          '3rd': 52.4,
          '10th': 53.7,
          '25th': 54.9,
          '50th': 56.4,
          '75th': 58.0,
          '90th': 59.6,
          '97th': 61.4
        },
        {
          'age': 60,
          '3rd': 52.7,
          '10th': 54.0,
          '25th': 55.2,
          '50th': 56.8,
          '75th': 58.4,
          '90th': 60.0,
          '97th': 61.8
        },
      ],
      'girls': [
        {
          'age': 0,
          '3rd': 31.5,
          '10th': 32.7,
          '25th': 33.6,
          '50th': 34.7,
          '75th': 35.8,
          '90th': 36.8,
          '97th': 38.1
        },
        {
          'age': 1,
          '3rd': 35.2,
          '10th': 36.5,
          '25th': 37.4,
          '50th': 38.4,
          '75th': 39.5,
          '90th': 40.6,
          '97th': 42.0
        },
        {
          'age': 2,
          '3rd': 37.1,
          '10th': 38.3,
          '25th': 39.1,
          '50th': 40.1,
          '75th': 41.2,
          '90th': 42.2,
          '97th': 43.6
        },
        {
          'age': 3,
          '3rd': 38.4,
          '10th': 39.6,
          '25th': 40.4,
          '50th': 41.4,
          '75th': 42.4,
          '90th': 43.5,
          '97th': 44.8
        },
        {
          'age': 4,
          '3rd': 39.5,
          '10th': 40.7,
          '25th': 41.5,
          '50th': 42.5,
          '75th': 43.6,
          '90th': 44.6,
          '97th': 45.9
        },
        {
          'age': 5,
          '3rd': 40.4,
          '10th': 41.6,
          '25th': 42.4,
          '50th': 43.4,
          '75th': 44.5,
          '90th': 45.5,
          '97th': 46.8
        },
        {
          'age': 6,
          '3rd': 41.2,
          '10th': 42.4,
          '25th': 43.2,
          '50th': 44.2,
          '75th': 45.3,
          '90th': 46.3,
          '97th': 47.6
        },
        {
          'age': 7,
          '3rd': 41.9,
          '10th': 43.1,
          '25th': 43.9,
          '50th': 44.9,
          '75th': 46.0,
          '90th': 47.0,
          '97th': 48.3
        },
        {
          'age': 8,
          '3rd': 42.6,
          '10th': 43.8,
          '25th': 44.6,
          '50th': 45.6,
          '75th': 46.7,
          '90th': 47.7,
          '97th': 49.0
        },
        {
          'age': 9,
          '3rd': 43.2,
          '10th': 44.4,
          '25th': 45.2,
          '50th': 46.2,
          '75th': 47.3,
          '90th': 48.3,
          '97th': 49.6
        },
        {
          'age': 10,
          '3rd': 43.7,
          '10th': 44.9,
          '25th': 45.7,
          '50th': 46.7,
          '75th': 47.8,
          '90th': 48.8,
          '97th': 50.1
        },
        {
          'age': 11,
          '3rd': 44.2,
          '10th': 45.4,
          '25th': 46.2,
          '50th': 47.2,
          '75th': 48.3,
          '90th': 49.3,
          '97th': 50.6
        },
        {
          'age': 12,
          '3rd': 44.7,
          '10th': 45.9,
          '25th': 46.7,
          '50th': 47.7,
          '75th': 48.8,
          '90th': 49.8,
          '97th': 51.1
        },
        {
          'age': 15,
          '3rd': 45.6,
          '10th': 46.8,
          '25th': 47.6,
          '50th': 48.6,
          '75th': 49.7,
          '90th': 50.7,
          '97th': 52.0
        },
        {
          'age': 18,
          '3rd': 46.8,
          '10th': 48.0,
          '25th': 48.8,
          '50th': 49.8,
          '75th': 50.9,
          '90th': 51.9,
          '97th': 53.2
        },
        {
          'age': 21,
          '3rd': 47.4,
          '10th': 48.6,
          '25th': 49.4,
          '50th': 50.4,
          '75th': 51.5,
          '90th': 52.5,
          '97th': 53.8
        },
        {
          'age': 24,
          '3rd': 48.1,
          '10th': 49.3,
          '25th': 50.2,
          '50th': 51.2,
          '75th': 52.3,
          '90th': 53.3,
          '97th': 54.6
        },
        {
          'age': 30,
          '3rd': 48.6,
          '10th': 49.8,
          '25th': 50.7,
          '50th': 51.7,
          '75th': 52.8,
          '90th': 53.9,
          '97th': 55.2
        },
        {
          'age': 36,
          '3rd': 49.0,
          '10th': 50.2,
          '25th': 51.1,
          '50th': 52.1,
          '75th': 53.2,
          '90th': 54.3,
          '97th': 55.7
        },
        {
          'age': 42,
          '3rd': 49.3,
          '10th': 50.5,
          '25th': 51.5,
          '50th': 52.5,
          '75th': 53.6,
          '90th': 54.7,
          '97th': 56.1
        },
        {
          'age': 48,
          '3rd': 49.6,
          '10th': 50.8,
          '25th': 51.8,
          '50th': 52.8,
          '75th': 54.0,
          '90th': 55.1,
          '97th': 56.5
        },
        {
          'age': 54,
          '3rd': 49.9,
          '10th': 51.1,
          '25th': 52.1,
          '50th': 53.1,
          '75th': 54.3,
          '90th': 55.4,
          '97th': 56.8
        },
        {
          'age': 60,
          '3rd': 50.2,
          '10th': 51.4,
          '25th': 52.4,
          '50th': 53.5,
          '75th': 54.6,
          '90th': 55.8,
          '97th': 57.2
        },
      ],
    },
  };

  /// Calculate percentile for a given measurement
  static double calculatePercentile(
      double value, double ageInMonths, String measurementType, String gender) {
    final data = _whoData[measurementType]?[gender];
    if (data == null) return 50.0;

    // Find the closest age data points
    Map<String, double>? lowerData;
    Map<String, double>? upperData;

    for (int i = 0; i < data.length - 1; i++) {
      final currentAge = (data[i]['age'] as num).toDouble();
      final nextAge = (data[i + 1]['age'] as num).toDouble();
      
      if (ageInMonths >= currentAge && ageInMonths <= nextAge) {
        lowerData = data[i];
        upperData = data[i + 1];
        break;
      }
    }

    // If exact age match, use that data
    if (lowerData == null) {
      final exactMatch = data.firstWhere(
        (d) => (d['age'] as num).toDouble() == ageInMonths,
        orElse: () => data.last,
      );
      lowerData = exactMatch;
      upperData = exactMatch;
    }

    // Get available percentiles for this measurement type
    final percentiles = _getAvailablePercentilesForMeasurement(measurementType);
    final interpolatedValues = <String, double>{};

    for (final percentile in percentiles) {
      // Safely get values with null checks
      final lowerPercentileValue = lowerData[percentile];
      final upperPercentileValue = upperData![percentile];
      
      if (lowerPercentileValue == null || upperPercentileValue == null) {
        continue; // Skip this percentile if data is missing
      }
      
      final lowerAge = (lowerData['age'] as num).toDouble();
      final upperAge = (upperData['age'] as num).toDouble();
      final lowerValue = (lowerPercentileValue as num).toDouble();
      final upperValue = (upperPercentileValue as num).toDouble();
      
      if (lowerAge == upperAge) {
        interpolatedValues[percentile] = lowerValue;
      } else {
        final ratio = (ageInMonths - lowerAge) / (upperAge - lowerAge);
        interpolatedValues[percentile] = lowerValue + (upperValue - lowerValue) * ratio;
      }
    }

    // Find which percentile the value falls into
    final percentileValues = _getPercentileValues(measurementType);
    final sortedPercentiles = percentiles.toList()..sort((a, b) {
      final aValue = percentileValues[a] ?? 0.0;
      final bValue = percentileValues[b] ?? 0.0;
      return aValue.compareTo(bValue);
    });

    for (int i = 0; i < sortedPercentiles.length - 1; i++) {
      final currentPercentile = sortedPercentiles[i];
      final nextPercentile = sortedPercentiles[i + 1];
      
      final current = interpolatedValues[currentPercentile];
      final next = interpolatedValues[nextPercentile];
      
      if (current == null || next == null) continue;

      if (value >= current && value <= next) {
        // Interpolate between percentiles
        final ratio = (value - current) / (next - current);
        final currentValue = percentileValues[currentPercentile] ?? 0.0;
        final nextValue = percentileValues[nextPercentile] ?? 0.0;
        return currentValue + (nextValue - currentValue) * ratio;
      }
    }

    // Handle edge cases
    final lowestPercentile = sortedPercentiles.first;
    final highestPercentile = sortedPercentiles.last;
    final pLowest = interpolatedValues[lowestPercentile];
    final pHighest = interpolatedValues[highestPercentile];
    
    if (pLowest != null && value < pLowest) {
      final lowestValue = percentileValues[lowestPercentile] ?? 3.0;
      return max(0.1, lowestValue * (value / pLowest));
    } else if (pHighest != null && value > pHighest) {
      final highestValue = percentileValues[highestPercentile] ?? 97.0;
      return min(99.9, highestValue + (value - pHighest) / pHighest * 2.0);
    }

    return 50.0; // Fallback
  }

  /// Get available percentiles for a specific measurement type
  static List<String> _getAvailablePercentilesForMeasurement(String measurementType) {
    switch (measurementType) {
      case 'weight':
        return ['3rd', '15th', '50th', '85th', '97th'];
      case 'height':
      case 'head_circumference':
        return ['3rd', '10th', '25th', '50th', '75th', '90th', '97th'];
      default:
        return ['3rd', '50th', '97th']; // Fallback
    }
  }

  /// Get percentile numeric values for interpolation
  static Map<String, double> _getPercentileValues(String measurementType) {
    switch (measurementType) {
      case 'weight':
        return {
          '3rd': 3.0,
          '15th': 15.0,
          '50th': 50.0,
          '85th': 85.0,
          '97th': 97.0,
        };
      case 'height':
      case 'head_circumference':
        return {
          '3rd': 3.0,
          '10th': 10.0,
          '25th': 25.0,
          '50th': 50.0,
          '75th': 75.0,
          '90th': 90.0,
          '97th': 97.0,
        };
      default:
        return {
          '3rd': 3.0,
          '50th': 50.0,
          '97th': 97.0,
        };
    }
  }

  /// Get percentile value for a specific percentile and age
  static double getPercentileValue(String percentile, double ageInMonths,
      String measurementType, String gender) {
    final data = _whoData[measurementType]?[gender];
    if (data == null) return 0.0;

    // Check if this percentile is available for this measurement type
    final availablePercentiles = _getAvailablePercentilesForMeasurement(measurementType);
    if (!availablePercentiles.contains(percentile)) {
      return 0.0; // Percentile not available for this measurement type
    }

    // Find the closest age data points
    Map<String, double>? lowerData;
    Map<String, double>? upperData;

    for (int i = 0; i < data.length - 1; i++) {
      final currentAge = (data[i]['age'] as num).toDouble();
      final nextAge = (data[i + 1]['age'] as num).toDouble();
      
      if (ageInMonths >= currentAge && ageInMonths <= nextAge) {
        lowerData = data[i];
        upperData = data[i + 1];
        break;
      }
    }

    // If exact age match, use that data
    if (lowerData == null) {
      final exactMatch = data.firstWhere(
        (d) => (d['age'] as num).toDouble() == ageInMonths,
        orElse: () => data.last,
      );
      final value = exactMatch[percentile];
      return value != null ? (value as num).toDouble() : 0.0;
    }

    // Safely get percentile values with null checks
    final lowerPercentileValue = lowerData[percentile];
    final upperPercentileValue = upperData![percentile];
    
    if (lowerPercentileValue == null || upperPercentileValue == null) {
      return 0.0; // Data missing for this percentile
    }

    // Interpolate value for exact age
    final lowerAge = (lowerData['age'] as num).toDouble();
    final upperAge = (upperData['age'] as num).toDouble();
    final lowerValue = (lowerPercentileValue as num).toDouble();
    final upperValue = (upperPercentileValue as num).toDouble();
    
    if (lowerAge == upperAge) {
      return lowerValue;
    } else {
      final ratio = (ageInMonths - lowerAge) / (upperAge - lowerAge);
      return lowerValue + (upperValue - lowerValue) * ratio;
    }
  }

  /// Get all available percentiles for chart drawing
  static List<String> getAvailablePercentiles([String? measurementType]) {
    if (measurementType != null) {
      return _getAvailablePercentilesForMeasurement(measurementType);
    }
    // Return all possible percentiles if no measurement type specified
    return ['3rd', '10th', '15th', '25th', '50th', '75th', '85th', '90th', '97th'];
  }

  /// Get age range for specific time period
  static List<double> getAgeRange(String dateRange) {
    switch (dateRange) {
      case '6 months':
        return List.generate(7, (i) => i.toDouble()); // 0-6 months
      case '1 year':
        return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]; // 0-12 months
      case '2 years':
        return [0, 2, 4, 6, 8, 10, 12, 15, 18, 21, 24]; // 0-24 months
      case '3 years':
        return [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 15, 18, 21, 24, 30, 36]; // 0-36 months
      case '4 years':
        return [0, 3, 6, 9, 12, 15, 18, 21, 24, 30, 36, 42, 48]; // 0-48 months
      case '5 years':
        return [0, 3, 6, 9, 12, 15, 18, 21, 24, 30, 36, 42, 48, 54, 60]; // 0-60 months (5 years)
      default:
        return List.generate(7, (i) => i.toDouble());
    }
  }

  /// Convert measurement units
  static double convertUnit(double value, String fromUnit, String toUnit) {
    // Weight conversions
    if (fromUnit == 'kg' && toUnit == 'lbs') return value * 2.20462;
    if (fromUnit == 'lbs' && toUnit == 'kg') return value / 2.20462;
    if (fromUnit == 'g' && toUnit == 'kg') return value / 1000;
    if (fromUnit == 'kg' && toUnit == 'g') return value * 1000;
    if (fromUnit == 'g' && toUnit == 'lbs') return value / 453.592;
    if (fromUnit == 'lbs' && toUnit == 'g') return value * 453.592;

    // Height conversions
    if (fromUnit == 'cm' && toUnit == 'in') return value / 2.54;
    if (fromUnit == 'in' && toUnit == 'cm') return value * 2.54;
    if (fromUnit == 'm' && toUnit == 'cm') return value * 100;
    if (fromUnit == 'cm' && toUnit == 'm') return value / 100;
    if (fromUnit == 'm' && toUnit == 'in') return value * 39.3701;
    if (fromUnit == 'in' && toUnit == 'm') return value / 39.3701;

    // Head circumference conversions
    if (fromUnit == 'mm' && toUnit == 'cm') return value / 10;
    if (fromUnit == 'cm' && toUnit == 'mm') return value * 10;

    return value; // No conversion needed
  }
}
