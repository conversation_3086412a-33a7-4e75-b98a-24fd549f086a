import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import '../core/global_navigator.dart';

/// Automatic email synchronization service that listens to auth state changes
/// and automatically syncs email changes between Supabase Auth and database
class AutomaticEmailSyncService {
  static const String _tag = 'AutomaticEmailSyncService';
  static StreamSubscription<AuthState>? _authSubscription;
  static Timer? _backgroundRefreshTimer;
  static bool _isInitialized = false;
  static String? _lastKnownEmail;
  static bool _hasActivePendingChange = false;
  
  /// Initialize the automatic email sync service
  static Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('$_tag: Already initialized');
      return;
    }
    
    try {
      debugPrint('$_tag: Initializing automatic email sync service...');
      
      // Get current user email
      final currentUser = Supabase.instance.client.auth.currentUser;
      _lastKnownEmail = currentUser?.email;
      
      // Listen to auth state changes
      _authSubscription = Supabase.instance.client.auth.onAuthStateChange.listen(
        (data) => _handleAuthStateChange(data),
        onError: (error) {
          debugPrint('$_tag: Auth state change error: $error');
        },
      );
      
      _isInitialized = true;
      debugPrint('$_tag: ✅ Automatic email sync service initialized');
      
      // Perform initial sync check
      await _performSyncCheck();
      
      // Start background refresh timer for pending email changes
      _startBackgroundRefresh();
      
    } catch (e) {
      debugPrint('$_tag: ❌ Failed to initialize: $e');
    }
  }
  
  /// Handle auth state changes
  static Future<void> _handleAuthStateChange(AuthState authState) async {
    try {
      final user = authState.session?.user;
      if (user == null) {
        debugPrint('$_tag: User signed out, clearing last known email');
        _lastKnownEmail = null;
        return;
      }
      
      final currentEmail = user.email;
      final newEmail = user.newEmail;
      
      debugPrint('$_tag: Auth state changed - Event: ${authState.event}');
      debugPrint('$_tag: Current email: $currentEmail');
      debugPrint('$_tag: New email (pending): $newEmail');
      debugPrint('$_tag: Last known email: $_lastKnownEmail');
      
      // Check if email change completed
      if (newEmail == null && currentEmail != null && currentEmail != _lastKnownEmail) {
        debugPrint('$_tag: 🎉 Email change detected! Old: $_lastKnownEmail → New: $currentEmail');
        await _syncEmailToDatabase(user);
        _lastKnownEmail = currentEmail;
        _hasActivePendingChange = false; // Clear pending flag
      }
      
      // Track if we have a pending email change
      if (newEmail != null) {
        _hasActivePendingChange = true;
        debugPrint('$_tag: 📧 Pending email change detected: $currentEmail → $newEmail');
      }
      
      // Check for initial sync if emails don't match
      if (authState.event == AuthChangeEvent.signedIn || 
          authState.event == AuthChangeEvent.tokenRefreshed) {
        await _performSyncCheck();
      }
      
    } catch (e) {
      debugPrint('$_tag: Error handling auth state change: $e');
    }
  }
  
  /// Perform a sync check between auth and database
  static Future<void> _performSyncCheck() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) return;
      
      final authEmail = user.email;
      if (authEmail == null) return;
      
      // Get database email
      final supabaseService = SupabaseService();
      final userProfiles = await supabaseService.select(
        'user_profiles',
        filters: {'auth_id': user.id},
        limit: 1,
      );
      
      if (userProfiles.isNotEmpty) {
        final dbEmail = userProfiles.first['email'] as String?;
        
        if (dbEmail != authEmail) {
          debugPrint('$_tag: 🔄 Email mismatch detected - Auth: $authEmail, DB: $dbEmail');
          debugPrint('$_tag: Automatically syncing database...');
          await _syncEmailToDatabase(user);
        } else {
          debugPrint('$_tag: ✅ Emails are in sync: $authEmail');
        }
      }
      
    } catch (e) {
      debugPrint('$_tag: Error in sync check: $e');
    }
  }
  
  /// Sync email to database
  static Future<void> _syncEmailToDatabase(User user) async {
    try {
      debugPrint('$_tag: Syncing email to database: ${user.email}');
      
      final supabaseService = SupabaseService();
      await supabaseService.update(
        'user_profiles',
        {
          'email': user.email,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        user.id,
      );
      
      debugPrint('$_tag: ✅ Email synced to database successfully');
      
      // Show success notification
      _showSyncNotification(user.email!);
      
    } catch (e) {
      debugPrint('$_tag: ❌ Failed to sync email to database: $e');
    }
  }
  
  /// Show sync notification to user and refresh UI
  static void _showSyncNotification(String email) {
    final context = navigatorKey.currentContext;
    if (context == null) return;
    
    // Show success notification
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white),
            SizedBox(width: 12),
            Expanded(
              child: Text(
                'Email automatically updated to $email',
                style: TextStyle(color: Colors.white, fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Refresh',
          textColor: Colors.white,
          onPressed: () => _triggerUIRefresh(),
        ),
      ),
    );
    
    // Trigger UI refresh after a short delay
    Future.delayed(Duration(milliseconds: 500), () {
      _triggerUIRefresh();
    });
  }
  
  /// Trigger UI refresh across the app
  static void _triggerUIRefresh() {
    final context = navigatorKey.currentContext;
    if (context == null) return;
    
    // Force a rebuild of the current route
    Navigator.of(context).pushReplacementNamed(
      ModalRoute.of(context)?.settings.name ?? '/settings',
    );
  }
  
  /// Manual sync trigger (for edge cases)
  static Future<void> forceSyncCheck() async {
    debugPrint('$_tag: Manual sync check requested');
    await _performSyncCheck();
  }
  
  /// Start background refresh timer for pending email changes
  static void _startBackgroundRefresh() {
    debugPrint('$_tag: Starting background refresh timer');
    
    _backgroundRefreshTimer = Timer.periodic(Duration(seconds: 30), (timer) async {
      try {
        if (_hasActivePendingChange) {
          debugPrint('$_tag: 🔄 Background refresh - checking for email change completion...');
          
          // Force session refresh to get latest state
          await Supabase.instance.client.auth.refreshSession();
          
          final user = Supabase.instance.client.auth.currentUser;
          if (user != null) {
            debugPrint('$_tag: Background check - Email: ${user.email}, NewEmail: ${user.newEmail}');
            
            if (user.newEmail == null && user.email != _lastKnownEmail) {
              // Email change completed!
              debugPrint('$_tag: 🎉 Background refresh detected completed email change!');
              await _syncEmailToDatabase(user);
              _lastKnownEmail = user.email;
              _hasActivePendingChange = false;
            }
          }
        }
      } catch (e) {
        debugPrint('$_tag: Error in background refresh: $e');
      }
    });
  }
  
  /// Stop background refresh timer
  static void _stopBackgroundRefresh() {
    if (_backgroundRefreshTimer != null) {
      _backgroundRefreshTimer!.cancel();
      _backgroundRefreshTimer = null;
      debugPrint('$_tag: Background refresh timer stopped');
    }
  }
  
  /// Dispose the service
  static void dispose() {
    debugPrint('$_tag: Disposing automatic email sync service');
    _authSubscription?.cancel();
    _authSubscription = null;
    _stopBackgroundRefresh();
    _isInitialized = false;
    _lastKnownEmail = null;
    _hasActivePendingChange = false;
  }
  
  /// Check if service is initialized
  static bool get isInitialized => _isInitialized;
}