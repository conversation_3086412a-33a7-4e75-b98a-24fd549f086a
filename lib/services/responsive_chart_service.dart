import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

/// Responsive chart layout service for different screen sizes
class ResponsiveChartService {
  ResponsiveChartService._();
  
  /// Get chart configuration based on screen size
  static ChartLayoutConfig getChartLayout(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth > 600;
    final isLandscape = screenWidth > screenHeight;
    
    if (isTablet) {
      return _getTabletLayout(screenWidth, screenHeight, isLandscape);
    } else {
      return _getPhoneLayout(screenWidth, screenHeight, isLandscape);
    }
  }
  
  /// Get tablet-specific layout configuration
  static ChartLayoutConfig _getTabletLayout(double width, double height, bool isLandscape) {
    return ChartLayoutConfig(
      chartHeight: isLandscape ? height * 0.6 : height * 0.45,
      chartPadding: EdgeInsets.all(4.w),
      headerHeight: 8.h,
      legendHeight: 6.h,
      toolbarHeight: 12.h,
      showExtendedControls: true,
      showDetailedLegend: true,
      enableZoomControls: true,
      chartAspectRatio: isLandscape ? 2.2 : 1.8,
      fontSize: ChartFontSizes(
        title: 18.sp,
        subtitle: 14.sp,
        axisLabel: 12.sp,
        legendLabel: 11.sp,
        tooltipLabel: 13.sp,
      ),
      spacing: ChartSpacing(
        small: 1.w,
        medium: 2.w,
        large: 4.w,
        extraLarge: 6.w,
      ),
      iconSizes: ChartIconSizes(
        small: 18,
        medium: 22,
        large: 26,
        extraLarge: 30,
      ),
    );
  }
  
  /// Get phone-specific layout configuration
  static ChartLayoutConfig _getPhoneLayout(double width, double height, bool isLandscape) {
    return ChartLayoutConfig(
      chartHeight: isLandscape ? height * 0.7 : height * 0.4,
      chartPadding: EdgeInsets.all(3.w),
      headerHeight: isLandscape ? 6.h : 7.h,
      legendHeight: isLandscape ? 4.h : 5.h,
      toolbarHeight: isLandscape ? 8.h : 10.h,
      showExtendedControls: !isLandscape,
      showDetailedLegend: !isLandscape,
      enableZoomControls: true,
      chartAspectRatio: isLandscape ? 2.5 : 1.5,
      fontSize: ChartFontSizes(
        title: 16.sp,
        subtitle: 12.sp,
        axisLabel: 10.sp,
        legendLabel: 9.sp,
        tooltipLabel: 11.sp,
      ),
      spacing: ChartSpacing(
        small: 1.w,
        medium: 2.w,
        large: 3.w,
        extraLarge: 4.w,
      ),
      iconSizes: ChartIconSizes(
        small: 16,
        medium: 20,
        large: 24,
        extraLarge: 28,
      ),
    );
  }
  
  /// Get responsive grid configuration for chart controls
  static GridConfig getControlsGridConfig(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    if (isTablet) {
      return GridConfig(
        crossAxisCount: 4,
        childAspectRatio: 2.5,
        crossAxisSpacing: 3.w,
        mainAxisSpacing: 2.h,
      );
    } else {
      return GridConfig(
        crossAxisCount: 2,
        childAspectRatio: 2.0,
        crossAxisSpacing: 2.w,
        mainAxisSpacing: 1.5.h,
      );
    }
  }
  
  /// Get responsive legend configuration
  static LegendConfig getLegendConfig(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isTablet = screenWidth > 600;
    
    return LegendConfig(
      itemsPerRow: isTablet ? 4 : 3,
      itemSpacing: isTablet ? 4.w : 2.w,
      rowSpacing: isTablet ? 1.5.h : 1.h,
      showIcons: true,
      showLabels: true,
      compactMode: !isTablet,
    );
  }
  
  /// Calculate optimal chart dimensions based on content
  static ChartDimensions calculateOptimalDimensions({
    required BuildContext context,
    required int dataPointCount,
    required String timeRange,
    required bool hasLegend,
    required bool hasControls,
  }) {
    final layout = getChartLayout(context);
    final screenSize = MediaQuery.of(context).size;
    
    // Base chart height
    double chartHeight = layout.chartHeight;
    
    // Adjust for data density
    if (dataPointCount > 50) {
      chartHeight *= 1.2; // Increase height for dense data
    } else if (dataPointCount < 10) {
      chartHeight *= 0.9; // Decrease height for sparse data
    }
    
    // Adjust for time range
    switch (timeRange) {
      case '3_months':
      case '6_months':
        chartHeight *= 0.9; // Shorter ranges need less height
        break;
      case '4_years':
      case '5_years':
      case 'all':
        chartHeight *= 1.1; // Longer ranges need more height
        break;
    }
    
    // Calculate total height including components
    double totalHeight = chartHeight;
    if (hasControls) totalHeight += layout.toolbarHeight;
    if (hasLegend) totalHeight += layout.legendHeight;
    totalHeight += layout.headerHeight;
    
    // Ensure minimum and maximum bounds
    totalHeight = totalHeight.clamp(
      screenSize.height * 0.3, // Minimum 30% of screen
      screenSize.height * 0.8, // Maximum 80% of screen
    );
    
    return ChartDimensions(
      totalHeight: totalHeight,
      chartHeight: chartHeight,
      width: screenSize.width - (layout.chartPadding.horizontal),
      aspectRatio: layout.chartAspectRatio,
    );
  }
  
  /// Get responsive breakpoints
  static ResponsiveBreakpoints getBreakpoints() {
    return ResponsiveBreakpoints(
      mobile: 480,
      tablet: 768,
      desktop: 1024,
      largeDesktop: 1440,
    );
  }
  
  /// Check if current screen size matches breakpoint
  static bool isScreenSize(BuildContext context, ScreenSize size) {
    final screenWidth = MediaQuery.of(context).size.width;
    final breakpoints = getBreakpoints();
    
    switch (size) {
      case ScreenSize.mobile:
        return screenWidth < breakpoints.tablet;
      case ScreenSize.tablet:
        return screenWidth >= breakpoints.tablet && screenWidth < breakpoints.desktop;
      case ScreenSize.desktop:
        return screenWidth >= breakpoints.desktop && screenWidth < breakpoints.largeDesktop;
      case ScreenSize.largeDesktop:
        return screenWidth >= breakpoints.largeDesktop;
    }
  }
  
  /// Get adaptive padding based on screen size
  static EdgeInsets getAdaptivePadding(BuildContext context) {
    if (isScreenSize(context, ScreenSize.mobile)) {
      return EdgeInsets.all(3.w);
    } else if (isScreenSize(context, ScreenSize.tablet)) {
      return EdgeInsets.all(4.w);
    } else {
      return EdgeInsets.all(5.w);
    }
  }
  
  /// Get adaptive spacing based on screen size
  static double getAdaptiveSpacing(BuildContext context, SpacingSize size) {
    final isLarge = isScreenSize(context, ScreenSize.tablet) || 
                   isScreenSize(context, ScreenSize.desktop);
    
    switch (size) {
      case SpacingSize.small:
        return isLarge ? 2.w : 1.w;
      case SpacingSize.medium:
        return isLarge ? 3.w : 2.w;
      case SpacingSize.large:
        return isLarge ? 5.w : 3.w;
      case SpacingSize.extraLarge:
        return isLarge ? 7.w : 4.w;
    }
  }
  
  /// Get adaptive chart configuration based on data complexity
  static AdaptiveChartConfig getAdaptiveChartConfig({
    required BuildContext context,
    required int dataPointCount,
    required String timeRange,
    required String measurementType,
  }) {
    final baseLayout = getChartLayout(context);
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;
    
    // Adjust configuration based on data complexity
    double complexityFactor = 1.0;
    if (dataPointCount > 100) {
      complexityFactor = 1.3;
    } else if (dataPointCount > 50) {
      complexityFactor = 1.15;
    } else if (dataPointCount < 10) {
      complexityFactor = 0.9;
    }
    
    // Adjust for time range complexity
    double timeRangeFactor = 1.0;
    switch (timeRange) {
      case '3_months':
      case '6_months':
        timeRangeFactor = 0.9;
        break;
      case '4_years':
      case '5_years':
      case 'all':
        timeRangeFactor = 1.1;
        break;
    }
    
    final adjustedHeight = baseLayout.chartHeight * complexityFactor * timeRangeFactor;
    
    return AdaptiveChartConfig(
      chartHeight: adjustedHeight.clamp(200.0, screenSize.height * 0.7),
      showDataLabels: dataPointCount <= 20,
      enableAnimations: dataPointCount <= 100,
      showGridLines: !isLandscape || dataPointCount <= 50,
      labelDensity: _calculateLabelDensity(dataPointCount, screenSize.width),
      interactionSensitivity: _calculateInteractionSensitivity(context),
      renderQuality: _getRenderQuality(dataPointCount, screenSize),
    );
  }
  
  /// Calculate optimal label density based on data and screen size
  static LabelDensity _calculateLabelDensity(int dataPointCount, double screenWidth) {
    final pointsPerPixel = dataPointCount / screenWidth;
    
    if (pointsPerPixel > 0.5) {
      return LabelDensity.sparse;
    } else if (pointsPerPixel > 0.2) {
      return LabelDensity.normal;
    } else {
      return LabelDensity.dense;
    }
  }
  
  /// Calculate interaction sensitivity based on device type
  static InteractionSensitivity _calculateInteractionSensitivity(BuildContext context) {
    if (isScreenSize(context, ScreenSize.mobile)) {
      return InteractionSensitivity.high; // More sensitive for touch
    } else {
      return InteractionSensitivity.normal;
    }
  }
  
  /// Determine render quality based on performance considerations
  static RenderQuality _getRenderQuality(int dataPointCount, Size screenSize) {
    final pixelCount = screenSize.width * screenSize.height;
    final complexity = dataPointCount * pixelCount;
    
    if (complexity > 10000000) { // Very complex
      return RenderQuality.performance;
    } else if (complexity > 5000000) { // Moderately complex
      return RenderQuality.balanced;
    } else {
      return RenderQuality.quality;
    }
  }
  
  /// Get responsive chart margins based on content
  static EdgeInsets getResponsiveChartMargins({
    required BuildContext context,
    required bool hasYAxisLabels,
    required bool hasXAxisLabels,
    required bool hasTitle,
    required bool hasLegend,
  }) {
    final baseSpacing = getAdaptiveSpacing(context, SpacingSize.medium);
    
    double left = baseSpacing;
    double right = baseSpacing;
    double top = baseSpacing;
    double bottom = baseSpacing;
    
    // Adjust for Y-axis labels
    if (hasYAxisLabels) {
      left += isScreenSize(context, ScreenSize.mobile) ? 40 : 60;
    }
    
    // Adjust for X-axis labels
    if (hasXAxisLabels) {
      bottom += isScreenSize(context, ScreenSize.mobile) ? 30 : 40;
    }
    
    // Adjust for title
    if (hasTitle) {
      top += isScreenSize(context, ScreenSize.mobile) ? 25 : 35;
    }
    
    // Adjust for legend
    if (hasLegend) {
      bottom += isScreenSize(context, ScreenSize.mobile) ? 40 : 50;
    }
    
    return EdgeInsets.fromLTRB(left, top, right, bottom);
  }
  
  /// Get optimal chart configuration for unit system and time range
  static OptimalChartConfig getOptimalChartConfig({
    required BuildContext context,
    required String measurementType,
    required bool isMetric,
    required String timeRangeId,
    required int dataPointCount,
    required bool isOfflineMode,
  }) {
    final baseLayout = getChartLayout(context);
    final screenSize = MediaQuery.of(context).size;
    final adaptiveConfig = getAdaptiveChartConfig(
      context: context,
      dataPointCount: dataPointCount,
      timeRange: timeRangeId,
      measurementType: measurementType,
    );
    
    // Adjust for unit system
    final unitSystemFactor = isMetric ? 1.0 : 1.1; // Imperial may need slightly more space
    
    // Adjust for offline mode
    final offlineFactor = isOfflineMode ? 0.9 : 1.0; // Slightly reduced for offline
    
    // Calculate optimal font sizes
    final fontSizes = ChartFontSizes(
      title: (baseLayout.fontSize.title * unitSystemFactor).clamp(12.0, 24.0),
      subtitle: (baseLayout.fontSize.subtitle * unitSystemFactor).clamp(10.0, 18.0),
      axisLabel: (baseLayout.fontSize.axisLabel * unitSystemFactor).clamp(8.0, 14.0),
      legendLabel: (baseLayout.fontSize.legendLabel * unitSystemFactor).clamp(8.0, 12.0),
      tooltipLabel: (baseLayout.fontSize.tooltipLabel * unitSystemFactor).clamp(9.0, 16.0),
    );
    
    return OptimalChartConfig(
      layout: baseLayout.copyWith(
        chartHeight: adaptiveConfig.chartHeight * offlineFactor,
        fontSize: fontSizes,
      ),
      adaptiveConfig: adaptiveConfig,
      unitSystemFactor: unitSystemFactor,
      offlineOptimized: isOfflineMode,
      recommendedInteractionMode: _getRecommendedInteractionMode(context, dataPointCount),
    );
  }
  
  /// Get recommended interaction mode based on device and data
  static ChartInteractionMode _getRecommendedInteractionMode(BuildContext context, int dataPointCount) {
    if (isScreenSize(context, ScreenSize.mobile)) {
      return dataPointCount > 50 ? ChartInteractionMode.simplified : ChartInteractionMode.touch;
    } else if (isScreenSize(context, ScreenSize.tablet)) {
      return ChartInteractionMode.enhanced;
    } else {
      return ChartInteractionMode.full;
    }
  }
  
  /// Calculate responsive grid layout for chart controls
  static ResponsiveGridLayout calculateResponsiveGrid({
    required BuildContext context,
    required int itemCount,
    required double minItemWidth,
    required double maxItemWidth,
  }) {
    final screenWidth = MediaQuery.of(context).size.width;
    final availableWidth = screenWidth - (getAdaptivePadding(context).horizontal);
    
    // Calculate optimal columns
    int columns = (availableWidth / minItemWidth).floor();
    columns = columns.clamp(1, 6); // Reasonable limits
    
    // Adjust item width
    final itemWidth = (availableWidth / columns).clamp(minItemWidth, maxItemWidth);
    
    // Calculate rows needed
    final rows = (itemCount / columns).ceil();
    
    return ResponsiveGridLayout(
      columns: columns,
      rows: rows,
      itemWidth: itemWidth,
      itemHeight: itemWidth * 0.6, // Reasonable aspect ratio
      spacing: getAdaptiveSpacing(context, SpacingSize.medium),
      totalHeight: (rows * itemWidth * 0.6) + ((rows - 1) * getAdaptiveSpacing(context, SpacingSize.medium)),
    );
  }
  
  /// Get device-specific performance settings
  static DevicePerformanceSettings getDevicePerformanceSettings(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final pixelRatio = MediaQuery.of(context).devicePixelRatio;
    final totalPixels = screenSize.width * screenSize.height * pixelRatio;
    
    // Categorize device performance based on screen resolution
    DevicePerformanceLevel performanceLevel;
    if (totalPixels < 2000000) { // ~1080p
      performanceLevel = DevicePerformanceLevel.basic;
    } else if (totalPixels < 8000000) { // ~4K
      performanceLevel = DevicePerformanceLevel.standard;
    } else {
      performanceLevel = DevicePerformanceLevel.high;
    }
    
    return DevicePerformanceSettings(
      performanceLevel: performanceLevel,
      enableAnimations: performanceLevel != DevicePerformanceLevel.basic,
      enableShadows: performanceLevel == DevicePerformanceLevel.high,
      maxDataPoints: performanceLevel == DevicePerformanceLevel.basic ? 100 : 
                    performanceLevel == DevicePerformanceLevel.standard ? 500 : 1000,
      renderQuality: performanceLevel == DevicePerformanceLevel.basic ? RenderQuality.performance :
                    performanceLevel == DevicePerformanceLevel.standard ? RenderQuality.balanced :
                    RenderQuality.quality,
    );
  }
  
  /// Get adaptive chart theme based on screen size and context
  static AdaptiveChartTheme getAdaptiveChartTheme({
    required BuildContext context,
    required bool isDarkMode,
    required bool isOfflineMode,
  }) {
    final screenSize = MediaQuery.of(context).size;
    final isLargeScreen = screenSize.width > 600;
    
    // Base colors
    final primaryColor = Theme.of(context).primaryColor;
    final backgroundColor = Theme.of(context).scaffoldBackgroundColor;
    final textColor = Theme.of(context).textTheme.bodyLarge?.color ?? Colors.black;
    
    // Adjust colors for offline mode
    final adjustedPrimaryColor = isOfflineMode 
        ? Colors.orange.withValues(alpha: 0.8)
        : primaryColor;
    
    return AdaptiveChartTheme(
      primaryColor: adjustedPrimaryColor,
      backgroundColor: backgroundColor,
      textColor: textColor,
      gridColor: textColor.withValues(alpha: isDarkMode ? 0.2 : 0.1),
      axisColor: textColor.withValues(alpha: isDarkMode ? 0.4 : 0.3),
      tooltipBackgroundColor: isDarkMode ? Colors.grey[800]! : Colors.white,
      tooltipTextColor: isDarkMode ? Colors.white : Colors.black,
      lineWidth: isLargeScreen ? 2.0 : 1.5,
      pointSize: isLargeScreen ? 6.0 : 4.0,
      fontSize: isLargeScreen ? 12.0 : 10.0,
      isOfflineMode: isOfflineMode,
    );
  }
}

/// Chart layout configuration
class ChartLayoutConfig {
  final double chartHeight;
  final EdgeInsets chartPadding;
  final double headerHeight;
  final double legendHeight;
  final double toolbarHeight;
  final bool showExtendedControls;
  final bool showDetailedLegend;
  final bool enableZoomControls;
  final double chartAspectRatio;
  final ChartFontSizes fontSize;
  final ChartSpacing spacing;
  final ChartIconSizes iconSizes;
  
  const ChartLayoutConfig({
    required this.chartHeight,
    required this.chartPadding,
    required this.headerHeight,
    required this.legendHeight,
    required this.toolbarHeight,
    required this.showExtendedControls,
    required this.showDetailedLegend,
    required this.enableZoomControls,
    required this.chartAspectRatio,
    required this.fontSize,
    required this.spacing,
    required this.iconSizes,
  });
}

/// Font sizes for different chart elements
class ChartFontSizes {
  final double title;
  final double subtitle;
  final double axisLabel;
  final double legendLabel;
  final double tooltipLabel;
  
  const ChartFontSizes({
    required this.title,
    required this.subtitle,
    required this.axisLabel,
    required this.legendLabel,
    required this.tooltipLabel,
  });
}

/// Spacing configuration for chart elements
class ChartSpacing {
  final double small;
  final double medium;
  final double large;
  final double extraLarge;
  
  const ChartSpacing({
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
  });
}

/// Icon sizes for different chart elements
class ChartIconSizes {
  final double small;
  final double medium;
  final double large;
  final double extraLarge;
  
  const ChartIconSizes({
    required this.small,
    required this.medium,
    required this.large,
    required this.extraLarge,
  });
}

/// Grid configuration for responsive layouts
class GridConfig {
  final int crossAxisCount;
  final double childAspectRatio;
  final double crossAxisSpacing;
  final double mainAxisSpacing;
  
  const GridConfig({
    required this.crossAxisCount,
    required this.childAspectRatio,
    required this.crossAxisSpacing,
    required this.mainAxisSpacing,
  });
}

/// Legend configuration
class LegendConfig {
  final int itemsPerRow;
  final double itemSpacing;
  final double rowSpacing;
  final bool showIcons;
  final bool showLabels;
  final bool compactMode;
  
  const LegendConfig({
    required this.itemsPerRow,
    required this.itemSpacing,
    required this.rowSpacing,
    required this.showIcons,
    required this.showLabels,
    required this.compactMode,
  });
}

/// Chart dimensions
class ChartDimensions {
  final double totalHeight;
  final double chartHeight;
  final double width;
  final double aspectRatio;
  
  const ChartDimensions({
    required this.totalHeight,
    required this.chartHeight,
    required this.width,
    required this.aspectRatio,
  });
}

/// Responsive breakpoints
class ResponsiveBreakpoints {
  final double mobile;
  final double tablet;
  final double desktop;
  final double largeDesktop;
  
  const ResponsiveBreakpoints({
    required this.mobile,
    required this.tablet,
    required this.desktop,
    required this.largeDesktop,
  });
}

/// Screen size categories
enum ScreenSize {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Spacing size categories
enum SpacingSize {
  small,
  medium,
  large,
  extraLarge,
}

/// Adaptive chart configuration based on data complexity
class AdaptiveChartConfig {
  final double chartHeight;
  final bool showDataLabels;
  final bool enableAnimations;
  final bool showGridLines;
  final LabelDensity labelDensity;
  final InteractionSensitivity interactionSensitivity;
  final RenderQuality renderQuality;
  
  const AdaptiveChartConfig({
    required this.chartHeight,
    required this.showDataLabels,
    required this.enableAnimations,
    required this.showGridLines,
    required this.labelDensity,
    required this.interactionSensitivity,
    required this.renderQuality,
  });
}

/// Label density options
enum LabelDensity {
  sparse,   // Few labels for crowded data
  normal,   // Standard label density
  dense,    // More labels for sparse data
}

/// Interaction sensitivity levels
enum InteractionSensitivity {
  low,      // Less sensitive, requires more deliberate gestures
  normal,   // Standard sensitivity
  high,     // More sensitive for touch devices
}

/// Render quality options
enum RenderQuality {
  performance, // Optimized for performance, lower quality
  balanced,    // Balance between quality and performance
  quality,     // High quality rendering
}

/// Optimal chart configuration combining layout and adaptive settings
class OptimalChartConfig {
  final ChartLayoutConfig layout;
  final AdaptiveChartConfig adaptiveConfig;
  final double unitSystemFactor;
  final bool offlineOptimized;
  final ChartInteractionMode recommendedInteractionMode;
  
  const OptimalChartConfig({
    required this.layout,
    required this.adaptiveConfig,
    required this.unitSystemFactor,
    required this.offlineOptimized,
    required this.recommendedInteractionMode,
  });
}

/// Chart interaction modes
enum ChartInteractionMode {
  simplified,  // Basic touch interactions only
  touch,       // Standard touch interactions
  enhanced,    // Enhanced interactions with gestures
  full,        // Full interaction capabilities
}

/// Responsive grid layout configuration
class ResponsiveGridLayout {
  final int columns;
  final int rows;
  final double itemWidth;
  final double itemHeight;
  final double spacing;
  final double totalHeight;
  
  const ResponsiveGridLayout({
    required this.columns,
    required this.rows,
    required this.itemWidth,
    required this.itemHeight,
    required this.spacing,
    required this.totalHeight,
  });
}

/// Device performance settings
class DevicePerformanceSettings {
  final DevicePerformanceLevel performanceLevel;
  final bool enableAnimations;
  final bool enableShadows;
  final int maxDataPoints;
  final RenderQuality renderQuality;
  
  const DevicePerformanceSettings({
    required this.performanceLevel,
    required this.enableAnimations,
    required this.enableShadows,
    required this.maxDataPoints,
    required this.renderQuality,
  });
}

/// Device performance levels
enum DevicePerformanceLevel {
  basic,     // Low-end devices
  standard,  // Mid-range devices
  high,      // High-end devices
}

/// Adaptive chart theme configuration
class AdaptiveChartTheme {
  final Color primaryColor;
  final Color backgroundColor;
  final Color textColor;
  final Color gridColor;
  final Color axisColor;
  final Color tooltipBackgroundColor;
  final Color tooltipTextColor;
  final double lineWidth;
  final double pointSize;
  final double fontSize;
  final bool isOfflineMode;
  
  const AdaptiveChartTheme({
    required this.primaryColor,
    required this.backgroundColor,
    required this.textColor,
    required this.gridColor,
    required this.axisColor,
    required this.tooltipBackgroundColor,
    required this.tooltipTextColor,
    required this.lineWidth,
    required this.pointSize,
    required this.fontSize,
    required this.isOfflineMode,
  });
}

/// Extension for ChartLayoutConfig to support copying with modifications
extension ChartLayoutConfigExtension on ChartLayoutConfig {
  ChartLayoutConfig copyWith({
    double? chartHeight,
    EdgeInsets? chartPadding,
    double? headerHeight,
    double? legendHeight,
    double? toolbarHeight,
    bool? showExtendedControls,
    bool? showDetailedLegend,
    bool? enableZoomControls,
    double? chartAspectRatio,
    ChartFontSizes? fontSize,
    ChartSpacing? spacing,
    ChartIconSizes? iconSizes,
  }) {
    return ChartLayoutConfig(
      chartHeight: chartHeight ?? this.chartHeight,
      chartPadding: chartPadding ?? this.chartPadding,
      headerHeight: headerHeight ?? this.headerHeight,
      legendHeight: legendHeight ?? this.legendHeight,
      toolbarHeight: toolbarHeight ?? this.toolbarHeight,
      showExtendedControls: showExtendedControls ?? this.showExtendedControls,
      showDetailedLegend: showDetailedLegend ?? this.showDetailedLegend,
      enableZoomControls: enableZoomControls ?? this.enableZoomControls,
      chartAspectRatio: chartAspectRatio ?? this.chartAspectRatio,
      fontSize: fontSize ?? this.fontSize,
      spacing: spacing ?? this.spacing,
      iconSizes: iconSizes ?? this.iconSizes,
    );
  }
}