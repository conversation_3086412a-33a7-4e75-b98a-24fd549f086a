import 'dart:async';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import '../core/global_navigator.dart';
import 'supabase_service.dart';
import 'manual_email_verification_service.dart';

/// Professional email change service with security best practices
class EmailChangeService {
  static const String _tag = 'EmailChangeService';
  
  /// Initiate email change process with security validations
  static Future<EmailChangeResult> initiateEmailChange({
    required String currentEmail,
    required String newEmail,
    required String password,
  }) async {
    try {
      debugPrint('$_tag: Initiating email change from $currentEmail to $newEmail');
      
      // 1. Validate new email format
      if (!_isValidEmail(newEmail)) {
        return EmailChangeResult.error('Invalid email format');
      }
      
      // 2. Check if new email is different from current
      if (newEmail.toLowerCase() == currentEmail.toLowerCase()) {
        return EmailChangeResult.error('New email must be different from current email');
      }
      
      // 3. Verify current password for security
      final passwordVerified = await _verifyCurrentPassword(password);
      if (!passwordVerified) {
        return EmailChangeResult.error('Current password is incorrect');
      }
      
      // 4. Check if new email is already in use
      final emailExists = await _checkEmailExists(newEmail);
      if (emailExists) {
        return EmailChangeResult.error('This email address is already registered');
      }
      
      // 5. Send verification email to new address with no redirect URL
      // This prevents the localhost:3000 redirect issue
      await Supabase.instance.client.auth.updateUser(
        UserAttributes(email: newEmail),
        emailRedirectTo: 'https://example.com/auth/v1/verify?type=email_change&redirect_to=babytracker://email-verified',
        );

      _startEmailVerificationPolling(currentEmail, newEmail); // Start polling for email verification
      
      // Start manual verification process with user instructions
      ManualEmailVerificationService.startManualVerification(currentEmail, newEmail);
      
      debugPrint('$_tag: Email change verification sent to $newEmail');
      
      return EmailChangeResult.success(
        'Verification email sent to $newEmail. Please check your inbox and click the verification link to complete the email change.',
      );
      
    } catch (e) {
      debugPrint('$_tag: Error initiating email change: $e');
      
      if (e.toString().contains('Email rate limit exceeded')) {
        return EmailChangeResult.error('Too many email change requests. Please wait before trying again.');
      } else if (e.toString().contains('Invalid email')) {
        return EmailChangeResult.error('Please enter a valid email address');
      } else {
        return EmailChangeResult.error('Failed to initiate email change. Please try again.');
      }
    }
  }
  
  /// Verify current password for security
  static Future<bool> _verifyCurrentPassword(String password) async {
    try {
      final currentUser = Supabase.instance.client.auth.currentUser;
      if (currentUser?.email == null) return false;
      
      // Attempt to sign in with current credentials to verify password
      final response = await Supabase.instance.client.auth.signInWithPassword(
        email: currentUser!.email!,
        password: password,
      );
      
      return response.user != null;
    } catch (e) {
      debugPrint('$_tag: Password verification failed: $e');
      return false;
    }
  }
  
  /// Check if email already exists in the system
  static Future<bool> _checkEmailExists(String email) async {
    try {
      // This would typically be done through a secure server-side function
      // For now, we'll rely on Supabase's built-in email uniqueness validation
      return false;
    } catch (e) {
      debugPrint('$_tag: Error checking email existence: $e');
      return false;
    }
  }
  
  /// Start polling for email verification completion
  static void _startEmailVerificationPolling(String oldEmail, String newEmail) {
    Timer.periodic(Duration(seconds: 10), (timer) async {
      try {
        // Refresh the session to get the latest user data
        final response = await Supabase.instance.client.auth.refreshSession();
        final user = response.user;
        
        if (user != null) {
          debugPrint('$_tag: Checking email verification status...');
          debugPrint('$_tag: Current email: ${user.email}');
          debugPrint('$_tag: New email: ${user.newEmail}');
          debugPrint('$_tag: Email confirmed at: ${user.emailConfirmedAt}');
          
          // Check if email change is complete (newEmail is null and email has changed)
          if (user.newEmail == null && user.email == newEmail) {
            debugPrint('$_tag: Email verification completed! New email: ${user.email}');
            
            // Update local user profile
            await _updateLocalUserProfile(user);
            
            // Show success notification
            _showEmailChangeSuccess(user.email!);
            
            // Stop polling
            timer.cancel();
          } else if (user.newEmail != null) {
            debugPrint('$_tag: Email change still pending. Current: ${user.email}, New: ${user.newEmail}');
          }
        }
      } catch (e) {
        debugPrint('$_tag: Error during email verification polling: $e');
      }
    });
  }
  
  /// Update local user profile after email change
  static Future<void> _updateLocalUserProfile(User user) async {
    try {
      final supabaseService = SupabaseService();
      
      // Update user_profiles table with new email
      await supabaseService.update(
        'user_profiles',
        {
          'email': user.email,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        user.id,
      );
      
      debugPrint('$_tag: Updated user profile with new email: ${user.email}');
    } catch (e) {
      debugPrint('$_tag: Error updating user profile: $e');
    }
  }
  
  /// Show success notification for email change
  static void _showEmailChangeSuccess(String newEmail) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(
                Icons.check_circle,
                color: Colors.white,
                size: 20,
              ),
              SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Email successfully changed to $newEmail',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          margin: EdgeInsets.all(16),
          duration: Duration(seconds: 5),
        ),
      );
    }
  }
  
  /// Validate email format
  static bool _isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }
  
  /// Get pending email change status
  static Future<PendingEmailChange?> getPendingEmailChange() async {
    try {
      final user = Supabase.instance.client.auth.currentUser;
      if (user?.newEmail != null) {
        return PendingEmailChange(
          currentEmail: user!.email!,
          newEmail: user.newEmail!,
          sentAt: DateTime.now(), // This would come from user metadata in real implementation
        );
      }
      return null;
    } catch (e) {
      debugPrint('$_tag: Error getting pending email change: $e');
      return null;
    }
  }
  
  /// Cancel pending email change
  static Future<bool> cancelEmailChange() async {
    try {
      // This would typically require a server-side function to cancel the email change
      // For now, we'll just return true as Supabase handles this automatically on timeout
      return true;
    } catch (e) {
      debugPrint('$_tag: Error cancelling email change: $e');
      return false;
    }
  }
}

/// Result of email change operation
class EmailChangeResult {
  final bool isSuccess;
  final String message;
  
  const EmailChangeResult._(this.isSuccess, this.message);
  
  factory EmailChangeResult.success(String message) => EmailChangeResult._(true, message);
  factory EmailChangeResult.error(String message) => EmailChangeResult._(false, message);
}

/// Pending email change information
class PendingEmailChange {
  final String currentEmail;
  final String newEmail;
  final DateTime sentAt;
  
  const PendingEmailChange({
    required this.currentEmail,
    required this.newEmail,
    required this.sentAt,
  });
  
  bool get isExpired => DateTime.now().difference(sentAt).inHours > 24;
}