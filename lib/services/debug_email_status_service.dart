import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../core/app_export.dart';
import 'supabase_service.dart';

/// Fixed debug service that prevents infinite subscription loops
class DebugEmailStatusService {
  static const String _tag = 'DebugEmailStatusService';
  static bool _isChecking = false;
  
  /// Check current email status across all systems (with loop prevention)
  static Future<void> checkEmailStatus() async {
    if (_isChecking) {
      debugPrint('$_tag: Email status check already in progress, skipping');
      return;
    }
    
    _isChecking = true;
    
    try {
      debugPrint('$_tag: === EMAIL STATUS DEBUG ===');
      
      // 1. Check Supabase Auth user
      final user = Supabase.instance.client.auth.currentUser;
      if (user != null) {
        debugPrint('$_tag: Auth User Email: ${user.email}');
        debugPrint('$_tag: Auth User New Email: ${user.newEmail}');
        debugPrint('$_tag: Auth User ID: ${user.id}');
        debugPrint('$_tag: Email Confirmed At: ${user.emailConfirmedAt}');
        debugPrint('$_tag: Email Change Sent At: ${user.emailChangeSentAt}');
      } else {
        debugPrint('$_tag: No authenticated user found');
        return;
      }
      
      // 2. Check user_profiles table (without creating subscriptions)
      final supabaseService = SupabaseService();
      final userProfiles = await supabaseService.select(
        'user_profiles',
        filters: {'auth_id': user.id},
        limit: 1,
      );
      
      if (userProfiles.isNotEmpty) {
        final profile = userProfiles.first;
        debugPrint('$_tag: Database Email: ${profile['email']}');
        debugPrint('$_tag: Database ID: ${profile['id']}');
        debugPrint('$_tag: Database Auth ID: ${profile['auth_id']}');
        debugPrint('$_tag: Database Updated At: ${profile['updated_at']}');
      } else {
        debugPrint('$_tag: No user profile found in database');
      }
      
      // 3. Check if there's a mismatch
      if (userProfiles.isNotEmpty && user.email != userProfiles.first['email']) {
        debugPrint('$_tag: ⚠️ EMAIL MISMATCH DETECTED!');
        debugPrint('$_tag: Auth system email: ${user.email}');
        debugPrint('$_tag: Database email: ${userProfiles.first['email']}');
        
        if (user.newEmail != null) {
          debugPrint('$_tag: Pending email change to: ${user.newEmail}');
        }
      } else {
        debugPrint('$_tag: ✅ Emails match across systems');
      }
      
      debugPrint('$_tag: === END EMAIL STATUS DEBUG ===');
      
    } catch (e) {
      debugPrint('$_tag: Error checking email status: $e');
    } finally {
      _isChecking = false;
    }
  }
  
  /// Force sync database email with auth email (with loop prevention)
  static Future<bool> syncDatabaseEmail() async {
    if (_isChecking) {
      debugPrint('$_tag: Sync already in progress, skipping');
      return false;
    }
    
    _isChecking = true;
    
    try {
      debugPrint('$_tag: Attempting to sync database email with auth email...');
      
      final user = Supabase.instance.client.auth.currentUser;
      if (user == null) {
        debugPrint('$_tag: No authenticated user');
        return false;
      }
      
      final supabaseService = SupabaseService();
      
      // Update database with current auth email
      await supabaseService.update(
        'user_profiles',
        {
          'email': user.email,
          'updated_at': DateTime.now().toIso8601String(),
        },
        'auth_id',
        user.id,
      );
      
      debugPrint('$_tag: ✅ Database email synced to: ${user.email}');
      return true;
      
    } catch (e) {
      debugPrint('$_tag: ❌ Failed to sync database email: $e');
      return false;
    } finally {
      _isChecking = false;
    }
  }
}