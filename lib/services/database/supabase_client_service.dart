import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

/// Core Supabase client initialization and management
class SupabaseClientService {
  static final SupabaseClientService _instance = SupabaseClientService._internal();
  late final SupabaseClient _client;
  bool _isInitialized = false;
  static Future<void>? _initFuture;

  factory SupabaseClientService() => _instance;
  SupabaseClientService._internal();

  static const String supabaseUrl = String.fromEnvironment('SUPABASE_URL', defaultValue: '');
  static const String supabaseAnonKey = String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: '');

  /// Initialize Supabase client
  static Future<void> initialize() async {
    _initFuture ??= _initializeSupabase();
    return _initFuture!;
  }

  static Future<void> _initializeSupabase() async {
    final credentials = await _loadCredentials();
    final url = credentials['url']!;
    final key = credentials['key']!;
    
    if (url.isEmpty || key.isEmpty) {
      throw Exception('SUPABASE_URL and SUPABASE_ANON_KEY must be defined');
    }

    await Supabase.initialize(url: url, anonKey: key);
    _instance._client = Supabase.instance.client;
    _instance._isInitialized = true;
  }

  static Future<Map<String, String>> _loadCredentials() async {
    String url = supabaseUrl;
    String key = supabaseAnonKey;
    
    if (url.isEmpty || key.isEmpty) {
      try {
        final String envJson = await rootBundle.loadString('env.json');
        final Map<String, dynamic> env = json.decode(envJson);
        url = env['SUPABASE_URL'] ?? '';
        key = env['SUPABASE_ANON_KEY'] ?? '';
      } catch (e) {
        throw Exception('Failed to load credentials: $e');
      }
    }
    
    return {'url': url, 'key': key};
  }

  /// Get initialized client
  SupabaseClient get client {
    if (!_isInitialized) {
      throw Exception('SupabaseClientService not initialized');
    }
    return _client;
  }

  bool get isInitialized => _isInitialized;
}