import 'package:flutter/foundation.dart';
import 'supabase_client_service.dart';
import '../../utils/service_decorators.dart';
import '../logging_service.dart';

/// Base repository class with common database operations
abstract class BaseRepository<T> {
  final SupabaseClientService _clientService = SupabaseClientService();
  
  /// Table name for this repository
  String get tableName;
  
  /// Convert JSON to model
  T fromJson(Map<String, dynamic> json);
  
  /// Convert model to JSON
  Map<String, dynamic> toJson(T model);

  /// Get client instance
  @protected
  get client => _clientService.client;

  /// Find by ID with error handling
  Future<T?> findById(String id) async {
    return ServiceDecorators.withDatabaseErrorHandling(
      () async {
        final response = await client
            .from(tableName)
            .select()
            .eq('id', id)
            .maybeSingle();
            
        return response != null ? fromJson(response) : null;
      },
      operationName: 'findById',
      tag: tableName,
      context: {'id': id},
    );
  }

  /// Find all with optional filters
  Future<List<T>> findAll({
    Map<String, dynamic>? filters,
    String? orderBy,
    bool ascending = true,
    int? limit,
  }) async {
    return ServiceDecorators.withDatabaseErrorHandling(
      () async {
        var query = client.from(tableName).select();

        if (filters != null) {
          filters.forEach((key, value) {
            query = query.eq(key, value);
          });
        }

        if (orderBy != null) {
          query = query.order(orderBy, ascending: ascending);
        }

        if (limit != null) {
          query = query.limit(limit);
        }

        final response = await query;
        return (response as List).map((json) => fromJson(json)).toList();
      },
      operationName: 'findAll',
      tag: tableName,
      context: {'filters': filters, 'orderBy': orderBy, 'limit': limit},
    );
  }

  /// Create new record
  Future<T> create(T model) async {
    return ServiceDecorators.withDatabaseErrorHandling(
      () async {
        final data = toJson(model);
        data.remove('id'); // Let database generate ID
        
        final response = await client
            .from(tableName)
            .insert(data)
            .select()
            .single();
            
        return fromJson(response);
      },
      operationName: 'create',
      tag: tableName,
    );
  }

  /// Update existing record
  Future<T> update(String id, T model) async {
    return ServiceDecorators.withDatabaseErrorHandling(
      () async {
        final data = toJson(model);
        data['updated_at'] = DateTime.now().toIso8601String();
        
        final response = await client
            .from(tableName)
            .update(data)
            .eq('id', id)
            .select()
            .single();
            
        return fromJson(response);
      },
      operationName: 'update',
      tag: tableName,
      context: {'id': id},
    );
  }

  /// Delete record
  Future<void> delete(String id) async {
    return ServiceDecorators.withDatabaseErrorHandling(
      () async {
        await client.from(tableName).delete().eq('id', id);
      },
      operationName: 'delete',
      tag: tableName,
      context: {'id': id},
    );
  }

  /// Execute custom query with error handling
  Future<List<Map<String, dynamic>>> executeQuery(
    String Function() queryBuilder,
    String operationName,
  ) async {
    return ServiceDecorators.withDatabaseErrorHandling(
      () async {
        final query = queryBuilder();
        LoggingService.database(operationName, tableName);
        return await client.rpc(query);
      },
      operationName: operationName,
      tag: tableName,
    );
  }
}