import '../database/base_repository.dart';
import '../../models/milestone.dart';

/// Repository for milestone-specific database operations
class MilestoneRepository extends BaseRepository<Milestone> {
  @override
  String get tableName => 'milestones';

  @override
  Milestone fromJson(Map<String, dynamic> json) => Milestone.fromJson(json);

  @override
  Map<String, dynamic> toJson(Milestone model) => model.toJson();

  /// Get milestones by category with caching
  Future<List<Milestone>> findByCategory(
    String babyId, 
    MilestoneCategory category,
  ) async {
    return findAll(
      filters: {
        'baby_id': babyId,
        'category': category.name,
      },
      orderBy: 'achieved_date',
      ascending: false,
    );
  }

  /// Get recent milestones (last 30 days)
  Future<List<Milestone>> findRecent(String babyId) async {
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
    
    return executeQuery(
      () => 'get_recent_milestones',
      'findRecent',
    ).then((response) => response.map((data) => from<PERSON>son(data)).toList());
  }

  /// Get milestone statistics
  Future<Map<String, dynamic>> getStatistics(String babyId) async {
    return executeQuery(
      () => 'get_milestone_stats',
      'getStatistics',
    ).then((response) => response.first);
  }
}