import 'package:flutter/foundation.dart';
import '../../models/activity_log.dart';
import '../../models/milestone.dart';
import 'supabase_client_service.dart';

/// Repository for activity-related database operations
class ActivityRepository {
  final SupabaseClientService _clientService = SupabaseClientService();

  /// Insert activity log with proper error handling
  Future<Map<String, dynamic>> insertActivityLog(ActivityLog log) async {
    try {
      debugPrint('📝 Saving activity log: ${log.type}');
      
      final response = await _clientService.client
          .from('activity_logs')
          .insert(log.toJson())
          .select()
          .single();

      debugPrint('✅ Activity log saved successfully');
      return response;
    } catch (e) {
      debugPrint('❌ Error saving activity log: $e');
      throw ActivityRepositoryException('Failed to save activity log', e);
    }
  }

  /// Get recent activities with improved error handling
  Future<List<ActivityLog>> getRecentActivities(
    String babyId, {
    int limit = 20,
    bool todayOnly = false,
  }) async {
    try {
      debugPrint('🔄 Fetching recent activities for baby: $babyId');
      
      final now = DateTime.now();
      var query = _clientService.client
          .from('activity_logs')
          .select()
          .eq('baby_id', babyId);
          
      if (todayOnly) {
        final startOfDay = DateTime(now.year, now.month, now.day);
        final endOfDay = startOfDay.add(const Duration(days: 1));
        
        query = query
            .gte('recorded_at', startOfDay.toIso8601String())
            .lt('recorded_at', endOfDay.toIso8601String());
      }
      
      final response = await query
          .order('recorded_at', ascending: false)
          .limit(limit);

      final activities = (response as List)
          .map((data) => ActivityLog.fromJson(data))
          .toList();
          
      debugPrint('✅ Fetched ${activities.length} activities');
      return activities;
    } catch (e) {
      debugPrint('❌ Error fetching activities: $e');
      throw ActivityRepositoryException('Failed to fetch activities', e);
    }
  }

  /// Get activities for specific date
  Future<List<ActivityLog>> getActivitiesForDate(String babyId, DateTime date) async {
    try {
      final startOfDay = DateTime(date.year, date.month, date.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));
      
      final response = await _clientService.client
          .from('activity_logs')
          .select()
          .eq('baby_id', babyId)
          .gte('recorded_at', startOfDay.toIso8601String())
          .lt('recorded_at', endOfDay.toIso8601String())
          .order('recorded_at', ascending: false);

      return (response as List)
          .map((data) => ActivityLog.fromJson(data))
          .toList();
    } catch (e) {
      throw ActivityRepositoryException('Failed to fetch activities for date', e);
    }
  }

  /// Get activity summary using RPC
  Future<List<Map<String, dynamic>>> getActivitySummary(String babyId) async {
    try {
      final response = await _clientService.client
          .rpc('get_todays_activity_summary', params: {'p_baby_id': babyId});
          
      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      throw ActivityRepositoryException('Failed to get activity summary', e);
    }
  }
}

/// Custom exception for activity repository operations
class ActivityRepositoryException implements Exception {
  final String message;
  final dynamic originalError;

  ActivityRepositoryException(this.message, [this.originalError]);

  @override
  String toString() => 'ActivityRepositoryException: $message${originalError != null ? ' ($originalError)' : ''}';
}