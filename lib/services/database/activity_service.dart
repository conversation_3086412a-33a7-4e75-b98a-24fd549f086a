import '../database/base_repository.dart';
import '../../models/activity_log.dart';
import '../../models/milestone.dart';

/// Specialized service for activity-related database operations
class ActivityService extends BaseRepository<ActivityLog> {
  @override
  String get tableName => 'activity_logs';

  @override
  ActivityLog fromJson(Map<String, dynamic> json) => ActivityLog.fromJson(json);

  @override
  Map<String, dynamic> to<PERSON>son(ActivityLog model) => model.toJson();

  /// Get recent activities with milestones combined
  Future<List<ActivityLog>> getRecentActivities(
    String babyId, {
    int limit = 20,
    bool todayOnly = false,
  }) async {
    return executeQuery(
      () => _buildRecentActivitiesQuery(babyId, limit, todayOnly),
      'getRecentActivities',
    ).then((response) => response.map((data) => fromJson(data)).toList());
  }

  /// Get activities for specific date
  Future<List<ActivityLog>> getActivitiesForDate(
    String babyId,
    DateTime date,
  ) async {
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    return findAll(
      filters: {
        'baby_id': babyId,
      },
      // Add date range filtering logic here
    );
  }

  String _buildRecentActivitiesQuery(String babyId, int limit, bool todayOnly) {
    // Build optimized query combining activities and milestones
    return 'get_combined_activities';
  }
}