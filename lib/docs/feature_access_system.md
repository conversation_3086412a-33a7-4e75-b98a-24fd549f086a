# Feature Access System Documentation

## Overview

The Feature Access System provides a centralized mechanism for managing subscription-based feature restrictions in BabyTracker Pro. It ensures consistent enforcement of feature access rules based on the user's subscription status (Free vs Premium) across the entire application.

This document serves as a comprehensive guide for developers to understand and integrate with the Feature Access System.

## Table of Contents

1. [Core Components](#core-components)
2. [Integration Patterns](#integration-patterns)
3. [Best Practices](#best-practices)
4. [Troubleshooting Guide](#troubleshooting-guide)
5. [Migration Guide](#migration-guide)
6. [API Reference](#api-reference)

## Core Components

The Feature Access System consists of several key components:

### 1. FeatureAccessService

The central service that determines feature access based on subscription status. It acts as the single source of truth for all subscription-based restrictions.

```dart
// Access through Provider
final featureAccessService = Provider.of<FeatureAccessService>(context, listen: false);

// Check if a feature is accessible
bool canUseFeature = featureAccessService.hasFeatureAccess(AppFeature.aiInsights);

// Get detailed access result
FeatureAccessResult result = featureAccessService.checkFeatureAccess(AppFeature.dataExport);
```### 2. F
eatureAccessController

A UI-friendly controller that provides reactive updates and widget builders for feature access.

```dart
// Access through Provider
final controller = Provider.of<FeatureAccessController>(context);

// Simple boolean check
bool canUseFeature = controller.canAccessFeature(AppFeature.familySharing);

// Get upgrade prompt configuration
UpgradePromptConfig config = controller.getUpgradePromptConfig(AppFeature.whoGrowthCharts);
```

### 3. Feature Gate Widgets

Reusable widgets for implementing feature restrictions in the UI.

```dart
// Basic feature gate
FeatureGate(
  feature: AppFeature.multipleBabyProfiles,
  child: BabyProfileCreationButton(),
  fallback: UpgradePromptWidget(feature: AppFeature.multipleBabyProfiles),
)

// Upgrade prompt
UpgradePromptWidget(
  feature: AppFeature.aiInsights,
  style: PromptStyle.banner,
)
```

### 4. AppFeature Enum

Centralized enum defining all features that can be restricted.

```dart
enum AppFeature {
  // Activity tracking
  unlimitedActivityTracking,
  scheduledActivities,
  
  // Baby profiles
  multipleBabyProfiles,
  unlimitedBabyProfiles,
  
  // Family features
  familySharing,
  multipleFamilyMembers,
  
  // AI features
  aiInsights,
  aiChat,
  aiAnalysis,
  
  // Charts and analytics
  whoGrowthCharts,
  advancedAnalytics,
  customCharts,
  
  // Data features
  dataExport,
  dataBackup,
  
  // Support features
  premiumSupport,
  prioritySupport,
  
  // Notifications
  customNotifications,
  advancedNotifications,
}
```## Integra
tion Patterns

### 1. Basic Feature Check

The simplest way to check if a feature is accessible:

```dart
// Using FeatureAccessService directly
bool hasAccess = Provider.of<FeatureAccessService>(context, listen: false)
    .hasFeatureAccess(AppFeature.aiInsights);

if (hasAccess) {
  // Show premium feature
} else {
  // Show alternative or upgrade prompt
}
```

### 2. Using FeatureGate Widget

For UI components that should be conditionally rendered:

```dart
FeatureGate(
  feature: AppFeature.whoGrowthCharts,
  child: WhoGrowthChartsWidget(),
  fallback: BasicGrowthChartWidget(), // Optional fallback for free users
  showUpgradePrompt: true, // Show upgrade prompt if feature is restricted
)
```

### 3. Screen-Level Protection

For entire screens that require premium access:

```dart
class AiInsightsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ScreenFeatureGuard(
      feature: AppFeature.aiInsights,
      child: AiInsightsContent(),
      // Will automatically show upgrade prompt and handle navigation
    );
  }
}
```

### 4. Navigation Integration

For route-based feature protection:

```dart
// Define protected routes
final routes = {
  '/ai-insights': FeatureAccessRoute(
    feature: AppFeature.aiInsights,
    builder: (context) => AiInsightsScreen(),
  ),
  '/data-export': FeatureAccessRoute(
    feature: AppFeature.dataExport,
    builder: (context) => DataExportScreen(),
  ),
};

// Use in Navigator
Navigator.of(context).push(
  FeatureAccessRoute(
    feature: AppFeature.aiInsights,
    builder: (context) => AiInsightsScreen(),
  ),
);
```

### 5. Usage Tracking and Limits

For features with usage limits:

```dart
final controller = Provider.of<FeatureAccessController>(context);

// Check current usage vs limit
int currentUsage = controller.getCurrentUsage(AppFeature.babyProfiles);
int? limit = controller.getFeatureLimit(AppFeature.babyProfiles);

// Track usage
controller.incrementFeatureUsage(AppFeature.aiChat);

// Check if near limit (for proactive notifications)
bool isNearLimit = controller.isNearUsageLimit(AppFeature.aiChat);
```

### 6. Context Extension Methods

For convenient access in widgets:

```dart
// Using extension methods
if (context.hasFeatureAccess(AppFeature.familySharing)) {
  // Show family sharing UI
}

// Show upgrade prompt
context.showUpgradePrompt(
  feature: AppFeature.dataExport,
  style: PromptStyle.dialog,
);
```## B
est Practices

### 1. Single Source of Truth

Always use the Feature Access System for all subscription-based restrictions. Avoid implementing custom checks or hardcoding subscription rules elsewhere in the codebase.

```dart
// GOOD: Using the centralized system
if (featureAccessService.hasFeatureAccess(AppFeature.aiInsights)) {
  // Show AI insights
}

// BAD: Custom logic or hardcoded checks
if (subscriptionInfo.isPremium || user.hasLegacyAccess) {
  // Show AI insights
}
```

### 2. Graceful Degradation

Always provide a good experience for free users. Consider what alternative functionality or information you can show instead of simply blocking access.

```dart
// GOOD: Providing alternative functionality
FeatureGate(
  feature: AppFeature.whoGrowthCharts,
  child: DetailedGrowthChartsWidget(),
  fallback: BasicGrowthChartWidget(), // Free alternative
)

// BAD: Just blocking with no alternative
FeatureGate(
  feature: AppFeature.whoGrowthCharts,
  child: DetailedGrowthChartsWidget(),
  // No fallback provided
)
```

### 3. Consistent Upgrade Messaging

Use the provided `UpgradePromptWidget` with standard configurations to ensure consistent messaging and branding across the app.

```dart
// GOOD: Using standard upgrade prompt
UpgradePromptWidget(
  feature: AppFeature.aiInsights,
  style: PromptStyle.banner,
)

// BAD: Custom upgrade messaging
Text("Upgrade to Premium to access AI Insights!")
ElevatedButton(
  onPressed: () => navigateToSubscription(),
  child: Text("Upgrade Now"),
)
```

### 4. Reactive UI Updates

Use the `listen: true` parameter when accessing the FeatureAccessController to ensure your UI updates when subscription status changes.

```dart
// GOOD: Reactive UI that updates when subscription changes
Consumer<FeatureAccessController>(
  builder: (context, controller, child) {
    return controller.canAccessFeature(AppFeature.familySharing)
        ? FamilySharingWidget()
        : UpgradePromptWidget(feature: AppFeature.familySharing);
  },
)

// BAD: Non-reactive UI that won't update
final controller = Provider.of<FeatureAccessController>(context, listen: false);
return controller.canAccessFeature(AppFeature.familySharing)
    ? FamilySharingWidget()
    : UpgradePromptWidget(feature: AppFeature.familySharing);
```

### 5. Analytics Integration

Always track feature access attempts and upgrade prompt interactions to gather valuable conversion data.

```dart
// Track feature access attempt
UsageAnalyticsTracker.instance.trackFeatureAccess(
  feature: AppFeature.aiInsights,
  granted: hasAccess,
);

// Track upgrade prompt interaction
UsageAnalyticsTracker.instance.trackUpgradePromptInteraction(
  feature: AppFeature.aiInsights,
  action: PromptAction.upgrade,
);
```

### 6. Testing Feature Access

Use the admin override capabilities for testing premium features during development.

```dart
// In development/testing code
AdminOverrideService.instance.grantTemporaryAccess(
  feature: AppFeature.aiInsights,
  durationMinutes: 60,
);
```## 
Troubleshooting Guide

### 1. Feature Access Not Updating After Subscription Change

**Symptoms:**
- User purchases premium but still sees upgrade prompts
- Premium features remain locked after upgrade

**Possible Causes:**
- Subscription status not properly propagated to FeatureAccessService
- Cache not invalidated after subscription change
- UI not reactive to subscription changes

**Solutions:**
- Verify that `SubscriptionController` is properly notifying listeners
- Check that `FeatureAccessService` is listening to subscription changes
- Ensure widgets are using `listen: true` when accessing controllers
- Try calling `FeatureAccessService.refreshAccess()` manually

```dart
// Force refresh access permissions
Provider.of<FeatureAccessService>(context, listen: false).refreshAccess();
```

### 2. Inconsistent Feature Access Across Screens

**Symptoms:**
- Feature is accessible in one screen but blocked in another
- Upgrade prompts appear inconsistently

**Possible Causes:**
- Different access check methods being used
- Custom logic overriding the central system
- Stale cached access results

**Solutions:**
- Standardize on using `FeatureAccessController` throughout the app
- Remove any custom subscription checks
- Verify that all screens are using the same `AppFeature` enum values

### 3. Upgrade Prompts Not Showing

**Symptoms:**
- Features are blocked but no upgrade prompt appears
- Blank or empty UI where premium features should be

**Possible Causes:**
- Missing fallback widgets in `FeatureGate`
- `showUpgradePrompt` parameter set to `false`
- Styling issues with upgrade prompts

**Solutions:**
- Always provide a fallback widget in `FeatureGate`
- Set `showUpgradePrompt: true` in `FeatureGate` widgets
- Check that upgrade prompt configurations exist for all features

```dart
// Ensure upgrade prompts are shown
FeatureGate(
  feature: AppFeature.aiInsights,
  child: AiInsightsWidget(),
  fallback: SizedBox.shrink(), // Even an empty widget is better than null
  showUpgradePrompt: true,
)
```

### 4. Usage Limits Not Working Correctly

**Symptoms:**
- Users can exceed usage limits for free tier
- Limit warnings appear incorrectly for premium users

**Possible Causes:**
- Usage tracking not implemented for the feature
- Incorrect limit configuration
- Not checking limits before allowing feature use

**Solutions:**
- Verify feature limits are correctly defined in `FeatureRestrictionRulesEngine`
- Ensure `incrementFeatureUsage()` is called when feature is used
- Check current usage against limits before allowing feature access

```dart
// Proper usage limit checking
final controller = Provider.of<FeatureAccessController>(context);
final currentUsage = controller.getCurrentUsage(AppFeature.babyProfiles);
final limit = controller.getFeatureLimit(AppFeature.babyProfiles);

if (limit != null && currentUsage >= limit) {
  // Show limit reached message
  return LimitReachedWidget(feature: AppFeature.babyProfiles);
}

// Allow creating new profile
return CreateProfileButton();
```

### 5. Analytics Data Missing

**Symptoms:**
- Feature usage data not appearing in analytics dashboard
- Conversion tracking incomplete

**Possible Causes:**
- Analytics tracking not implemented for all features
- Batch uploads failing
- Database permissions issues

**Solutions:**
- Verify `UsageAnalyticsTracker` is tracking all feature access attempts
- Check for errors in analytics batch uploads
- Verify database RLS policies allow writing to analytics tables## 
Migration Guide

### Migrating from Legacy Subscription Checks

If you're migrating from direct subscription checks to the new Feature Access System, follow these steps:

#### 1. Identify Current Subscription Checks

Look for code patterns like:

```dart
// Legacy pattern 1: Direct subscription property check
if (user.isSubscribed || user.isPremium) {
  // Show premium feature
}

// Legacy pattern 2: Helper method
if (hasAccess('ai_insights') || canAccessPremiumFeature()) {
  // Show premium feature
}

// Legacy pattern 3: Custom logic
if (user.subscriptionTier >= SubscriptionTier.premium || 
    user.hasGrandfatheredAccess) {
  // Show premium feature
}
```

#### 2. Map Features to AppFeature Enum

For each feature check, identify the corresponding `AppFeature` enum value:

```dart
// Before
if (user.isPremium && canAccessAiFeatures()) {
  showAiInsights();
}

// After - Map to appropriate AppFeature
if (featureAccessService.hasFeatureAccess(AppFeature.aiInsights)) {
  showAiInsights();
}
```

#### 3. Replace Direct Checks with FeatureAccessService

Replace all direct subscription checks with the Feature Access System:

```dart
// Before
Widget build(BuildContext context) {
  final user = Provider.of<UserModel>(context);
  
  return user.isPremium
      ? PremiumFeatureWidget()
      : BasicFeatureWidget();
}

// After
Widget build(BuildContext context) {
  final featureAccess = Provider.of<FeatureAccessController>(context);
  
  return featureAccess.canAccessFeature(AppFeature.premiumFeature)
      ? PremiumFeatureWidget()
      : BasicFeatureWidget();
}
```

#### 4. Implement FeatureGate for UI Components

Replace conditional rendering with `FeatureGate` widgets:

```dart
// Before
Widget build(BuildContext context) {
  final user = Provider.of<UserModel>(context);
  
  if (user.isPremium) {
    return PremiumFeatureWidget();
  } else {
    return Text("Upgrade to access this feature");
  }
}

// After
Widget build(BuildContext context) {
  return FeatureGate(
    feature: AppFeature.premiumFeature,
    child: PremiumFeatureWidget(),
    fallback: UpgradePromptWidget(feature: AppFeature.premiumFeature),
  );
}
```

#### 5. Update Navigation Logic

Replace navigation guards with `FeatureAccessRoute`:

```dart
// Before
void navigateToPremiumFeature() {
  if (user.isPremium) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (_) => PremiumFeatureScreen()),
    );
  } else {
    showUpgradeDialog();
  }
}

// After
void navigateToPremiumFeature() {
  Navigator.of(context).push(
    FeatureAccessRoute(
      feature: AppFeature.premiumFeature,
      builder: (_) => PremiumFeatureScreen(),
    ),
  );
}
```

#### 6. Add Analytics Tracking

Add analytics tracking for feature access and upgrade prompts:

```dart
// Add to relevant places in your code
UsageAnalyticsTracker.instance.trackFeatureAccess(
  feature: AppFeature.premiumFeature,
  granted: hasAccess,
);

UsageAnalyticsTracker.instance.trackUpgradePromptInteraction(
  feature: AppFeature.premiumFeature,
  action: PromptAction.viewed,
);
```#
# API Reference

### FeatureAccessService

```dart
class FeatureAccessService extends ChangeNotifier {
  // Core feature access methods
  bool hasFeatureAccess(AppFeature feature);
  FeatureAccessResult checkFeatureAccess(AppFeature feature);
  int? getFeatureLimit(AppFeature feature);
  int getCurrentUsage(AppFeature feature);
  
  // Upgrade prompt management
  UpgradePromptConfig getUpgradePrompt(AppFeature feature);
  void trackFeatureAccessAttempt(AppFeature feature);
  
  // Subscription integration
  void onSubscriptionChanged(SubscriptionInfo subscription);
  
  // Refresh and reset
  void refreshAccess();
  void resetUsageCounts();
}
```

### FeatureAccessController

```dart
class FeatureAccessController extends ChangeNotifier {
  // Widget-friendly access methods
  bool canAccessFeature(AppFeature feature);
  Widget buildFeatureGate(AppFeature feature, Widget child, {Widget? fallback});
  Widget buildUpgradePrompt(AppFeature feature, {PromptStyle? style});
  
  // Usage tracking
  void incrementFeatureUsage(AppFeature feature);
  bool isNearUsageLimit(AppFeature feature);
  int getCurrentUsage(AppFeature feature);
  int? getFeatureLimit(AppFeature feature);
  
  // Upgrade prompt configuration
  UpgradePromptConfig getUpgradePromptConfig(AppFeature feature);
}
```

### Feature Gate Widgets

```dart
class FeatureGate extends StatelessWidget {
  final AppFeature feature;
  final Widget child;
  final Widget? fallback;
  final bool showUpgradePrompt;
  
  // Constructor
  FeatureGate({
    required this.feature,
    required this.child,
    this.fallback,
    this.showUpgradePrompt = true,
  });
}

class UpgradePromptWidget extends StatelessWidget {
  final AppFeature feature;
  final PromptStyle style;
  final VoidCallback? onUpgrade;
  final VoidCallback? onDismiss;
  
  // Constructor
  UpgradePromptWidget({
    required this.feature,
    this.style = PromptStyle.inline,
    this.onUpgrade,
    this.onDismiss,
  });
}

class ScreenFeatureGuard extends StatelessWidget {
  final AppFeature feature;
  final Widget child;
  final PromptStyle promptStyle;
  
  // Constructor
  ScreenFeatureGuard({
    required this.feature,
    required this.child,
    this.promptStyle = PromptStyle.fullScreen,
  });
}
```

### UsageAnalyticsTracker

```dart
class UsageAnalyticsTracker {
  // Usage tracking
  void trackFeatureAccess(AppFeature feature, bool granted);
  void trackUpgradePromptShown(AppFeature feature);
  void trackUpgradePromptInteraction(AppFeature feature, PromptAction action);
  
  // Analytics reporting
  Future<UsageReport> generateUsageReport();
  Future<ConversionReport> generateConversionReport();
}
```

### AdminOverrideService

```dart
class AdminOverrideService {
  // Admin overrides
  void grantTemporaryAccess(AppFeature feature, int durationMinutes);
  void revokeTemporaryAccess(AppFeature feature);
  bool hasAdminOverride(AppFeature feature);
  
  // Feature flags
  void setFeatureFlag(String flagName, bool enabled);
  bool isFeatureFlagEnabled(String flagName);
}
```

### BuildContext Extensions

```dart
extension FeatureAccessExtensions on BuildContext {
  // Convenience methods
  bool hasFeatureAccess(AppFeature feature);
  FeatureAccessResult checkFeatureAccess(AppFeature feature);
  void showUpgradePrompt({required AppFeature feature, PromptStyle? style});
  
  // Controller and service access
  FeatureAccessController get featureAccessController;
  FeatureAccessService get featureAccessService;
}
```

---

## Quick Start Checklist

When implementing feature access in a new screen or component:

1. ✅ **Identify the feature** - Map your functionality to an `AppFeature` enum value
2. ✅ **Choose integration pattern** - Use `FeatureGate`, `ScreenFeatureGuard`, or direct service calls
3. ✅ **Provide fallback** - Always offer alternative functionality for free users
4. ✅ **Add analytics** - Track feature access attempts and upgrade prompt interactions
5. ✅ **Test both tiers** - Verify behavior for both free and premium users
6. ✅ **Handle edge cases** - Consider subscription changes, network issues, and error states

## Support

For questions or issues with the Feature Access System:

1. Check this documentation first
2. Review existing examples in `lib/examples/`
3. Run tests to verify expected behavior
4. Check the troubleshooting guide above
5. Contact the development team for complex integration questions

Remember: The Feature Access System is designed to be the single source of truth for all subscription-based restrictions. Always use it instead of implementing custom logic.