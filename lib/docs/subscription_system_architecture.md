# Subscription System Architecture

## Overview

The BabyTracker Pro subscription system provides a comprehensive, scalable solution for managing feature access based on user subscription status. The system is designed with clean architecture principles, separation of concerns, and extensibility in mind.

## Core Components

### 1. Models (`lib/models/`)

#### `SubscriptionInfo`
- Represents complete subscription data including plan details, status, and feature flags
- Includes computed properties for trial status, renewal dates, and formatted pricing
- Provides JSON serialization for database storage

#### `AppFeature` Enum
- Defines all features that can be restricted by subscription
- Includes display names, descriptions, and icons for UI consistency
- Extensible for adding new premium features

#### `FeatureAccessResult`
- Encapsulates the result of feature access checks
- Includes access status, restriction reasons, and upgrade prompts
- Provides usage tracking and limit information

### 2. Services (`lib/services/`)

#### `FeatureAccessService`
- Central service for checking feature access permissions
- Implements caching for performance optimization
- Provides usage tracking and limit enforcement
- Uses Strategy pattern for different access rule types

#### `FeatureMappingService`
- Centralizes mapping between string feature names and enum values
- Eliminates duplicate mapping logic across components
- Provides validation and conversion utilities

#### `FeatureAccessStrategies`
- Implements Strategy pattern for different access rule types:
  - `PremiumOnlyStrategy`: Binary premium/free access
  - `UsageLimitedStrategy`: Features with usage limits
  - `ConditionalPremiumStrategy`: Features requiring specific subscription flags

### 3. Controllers (`lib/presentation/subscription/controllers/`)

#### `SubscriptionController`
- Manages subscription state and purchase flow
- Handles loading, purchasing, and canceling subscriptions
- Provides backward compatibility with string-based feature checking
- Implements proper error handling with specific error types

#### `FeatureAccessController`
- UI-focused controller for feature access management
- Provides reactive UI updates when subscription changes
- Handles upgrade prompts and user interactions

### 4. Utilities (`lib/utils/`)

#### `SubscriptionAccessControl`
- Static utility methods for quick feature access checks
- Provides UI helpers for upgrade prompts and banners
- Includes widget wrappers for conditional content display

#### `PerformanceMonitor`
- Monitors subscription system performance
- Tracks operation durations and identifies bottlenecks
- Provides statistics and debugging information

## Architecture Patterns

### Strategy Pattern
Used for feature access rules to allow different types of restrictions:
- Premium-only features
- Usage-limited features  
- Conditional premium features

### Observer Pattern
Controllers use `ChangeNotifier` to provide reactive UI updates when subscription status changes.

### Factory Pattern
`FeatureAccessStrategyFactory` creates appropriate strategy instances for each feature type.

### Template Method Pattern
Base classes define common behavior while allowing customization in subclasses.

## Data Flow

```
User Action → Controller → Service → Strategy → Database/Cache → UI Update
```

1. User attempts to access a feature
2. Controller checks with FeatureAccessService
3. Service uses appropriate strategy to evaluate access
4. Strategy checks subscription data and usage limits
5. Result is cached and returned to UI
6. UI shows content or upgrade prompt based on result

## Configuration

### `FeatureAccessConfig`
Centralizes all configuration constants:
- Plan limits and pricing
- Feature lists and messages
- Performance settings
- Debug options

### Environment-Specific Settings
- Product IDs for app store integration
- API endpoints for subscription validation
- Feature flags for A/B testing

## Error Handling

### Subscription Errors
- `SubscriptionError` enum defines specific error types
- Controllers handle network, parsing, and business logic errors
- Graceful degradation when subscription service is unavailable

### Fail-Safe Behavior
- System defaults to free plan when subscription data is unavailable
- Feature access checks fail closed (deny access on error)
- UI provides clear error messages and recovery options

## Performance Optimizations

### Caching Strategy
- Feature access results are cached to avoid repeated calculations
- Cache invalidation is selective (only affected features)
- Configurable cache timeout and size limits

### Lazy Loading
- Subscription data is loaded on-demand
- Feature strategies are created only when needed
- UI components use efficient rebuild patterns

### Monitoring
- Performance monitoring tracks slow operations
- Statistics help identify optimization opportunities
- Debug logging provides detailed operation traces

## Testing Strategy

### Unit Tests
- Model serialization/deserialization
- Service logic and edge cases
- Strategy pattern implementations

### Integration Tests
- End-to-end subscription flows
- Feature access scenarios
- Error handling paths

### Widget Tests
- UI component behavior
- Upgrade prompt interactions
- Conditional content display

## Extensibility

### Adding New Features
1. Add feature to `AppFeature` enum
2. Create or reuse appropriate strategy
3. Update configuration constants
4. Add UI integration points

### Supporting New Plans
1. Update `SubscriptionInfo` model
2. Add plan configuration constants
3. Update strategy logic if needed
4. Test all feature combinations

### Platform Integration
- Abstract subscription service interface
- Platform-specific implementations (iOS, Android, Web)
- Unified API for all platforms

## Security Considerations

### Server-Side Validation
- All subscription status must be validated server-side
- Client-side checks are for UX only
- Receipt validation prevents tampering

### Data Protection
- Subscription data is encrypted in transit and at rest
- User privacy is maintained in analytics
- Compliance with app store guidelines

## Deployment

### Feature Flags
- Gradual rollout of new premium features
- A/B testing of upgrade prompts
- Emergency feature disabling

### Monitoring
- Subscription conversion metrics
- Feature usage analytics
- Error rate monitoring

This architecture provides a solid foundation for a scalable, maintainable subscription system that can grow with your business needs.