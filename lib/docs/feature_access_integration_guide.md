# Feature Access System Integration Guide

## Overview

This guide shows how to integrate the unified feature access system with your existing app screens. The system provides subscription-based feature restrictions with professional upgrade prompts.

## Quick Setup

### 1. Add Providers to main.dart

Add these providers to your main.dart file:

```dart
// Add these imports
import 'services/feature_access_service.dart';
import 'presentation/subscription/controllers/feature_access_controller.dart';
import 'presentation/subscription/controllers/subscription_controller.dart';

// Add to your MultiProvider:
ChangeNotifierProvider(create: (_) => SubscriptionController()),
ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
  create: (context) => FeatureAccessService(
    Provider.of<SubscriptionController>(context, listen: false),
  ),
  update: (context, subscription, previous) => 
    previous ?? FeatureAccessService(subscription),
),
ChangeNotifierProxyProvider<FeatureAccessService, FeatureAccessController>(
  create: (context) => FeatureAccessController(
    Provider.of<FeatureAccessService>(context, listen: false),
  ),
  update: (context, service, previous) => 
    previous ?? FeatureAccessController(service),
),
```

### 2. Basic Feature Gate Usage

Wrap any premium content with a FeatureGate:

```dart
import '../presentation/subscription/widgets/feature_gate.dart';
import '../models/feature_access.dart';

// Example: AI Insights Screen
FeatureGate(
  feature: AppFeature.aiInsights,
  child: YourAIInsightsContent(),
  onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
)
```

### 3. Usage Limit Checking

For features with usage limits (like baby profiles):

```dart
Consumer<FeatureAccessController>(
  builder: (context, controller, child) {
    final canCreate = controller.canAccessFeature(AppFeature.multipleBabyProfiles);
    final usage = controller.getCurrentUsage(AppFeature.multipleBabyProfiles);
    final limit = controller.getFeatureLimit(AppFeature.multipleBabyProfiles);
    
    if (!canCreate && limit != null && usage >= limit) {
      return controller.buildUpgradePrompt(AppFeature.multipleBabyProfiles);
    }
    
    return YourCreateProfileForm();
  },
)
```

## Integration Examples

### Baby Profile Creation

```dart
// In your baby profile creation screen
Consumer<FeatureAccessController>(
  builder: (context, featureController, child) {
    final canCreateMultiple = featureController.canAccessFeature(AppFeature.multipleBabyProfiles);
    final currentUsage = featureController.getCurrentUsage(AppFeature.multipleBabyProfiles);
    final limit = featureController.getFeatureLimit(AppFeature.multipleBabyProfiles);
    
    // If user has reached limit, show upgrade prompt
    if (!canCreateMultiple && limit != null && currentUsage >= limit) {
      return Scaffold(
        appBar: AppBar(title: Text('Create Baby Profile')),
        body: Center(
          child: featureController.buildUpgradePrompt(
            AppFeature.multipleBabyProfiles,
            onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
          ),
        ),
      );
    }
    
    // Otherwise show the creation form
    return YourBabyProfileCreationForm();
  },
)

// When saving a new profile, increment usage:
void _saveProfile() async {
  // ... save logic ...
  
  // Increment usage counter
  final featureController = Provider.of<FeatureAccessController>(context, listen: false);
  featureController.incrementFeatureUsage(AppFeature.multipleBabyProfiles);
}
```

### AI Features (Insights, Chat)

```dart
// Wrap entire screen content
Scaffold(
  appBar: AppBar(title: Text('AI Insights')),
  body: FeatureGate(
    feature: AppFeature.aiInsights,
    child: YourAIInsightsContent(),
    onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
  ),
)
```

### WHO Growth Charts

```dart
// In your growth charts screen
Scaffold(
  appBar: AppBar(title: Text('Growth Charts')),
  body: FeatureGate(
    feature: AppFeature.whoGrowthCharts,
    child: YourGrowthChartsContent(),
    onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
  ),
)
```

### Data Export with Usage Limits

```dart
// In your export function
void _handleExport() {
  final controller = Provider.of<FeatureAccessController>(context, listen: false);
  final canExport = controller.canAccessFeature(AppFeature.dataExport);
  
  if (!canExport) {
    // Show upgrade prompt
    showUpgradeDialog(
      context,
      feature: AppFeature.dataExport,
      config: UpgradePromptConfig(
        title: 'Export Limit Reached',
        description: 'Upgrade for unlimited exports.',
        benefits: ['Unlimited exports', 'Multiple formats', 'Priority processing'],
      ),
      onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
    );
    return;
  }
  
  // Perform export and increment usage
  _performExport();
  controller.incrementFeatureUsage(AppFeature.dataExport);
}
```

### Family Sharing

```dart
// In your family/user management screen
Scaffold(
  appBar: AppBar(title: Text('Family Sharing')),
  body: FeatureGate(
    feature: AppFeature.familySharing,
    child: YourFamilySharingContent(),
    onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
  ),
)
```

## Available Features

The system supports these features out of the box:

- `AppFeature.multipleBabyProfiles` - Limited to 1 for free users
- `AppFeature.familySharing` - Premium only
- `AppFeature.whoGrowthCharts` - Premium only  
- `AppFeature.aiInsights` - Premium only
- `AppFeature.aiChat` - Premium only
- `AppFeature.dataExport` - Limited to 1/month for free users
- `AppFeature.advancedAnalytics` - Premium only
- `AppFeature.customReminders` - Premium only
- `AppFeature.prioritySupport` - Premium only

## Customizing Upgrade Prompts

You can customize upgrade prompts by providing your own configuration:

```dart
UpgradePromptWidget(
  config: UpgradePromptConfig(
    title: 'Custom Title',
    description: 'Custom description',
    benefits: ['Benefit 1', 'Benefit 2'],
    ctaText: 'Custom Button Text',
    style: PromptStyle.card,
  ),
  feature: AppFeature.yourFeature,
  onUpgrade: () => Navigator.pushNamed(context, '/subscription'),
)
```

## Testing

The system includes comprehensive tests. Run them with:

```bash
flutter test test/services/feature_access_service_test.dart
flutter test test/presentation/subscription/controllers/feature_access_controller_test.dart
```

## Best Practices

1. **Always check access before showing restricted content**
2. **Increment usage counters when features are used**
3. **Provide clear upgrade paths with specific benefits**
4. **Test both free and premium user flows**
5. **Use consistent upgrade prompt styling**

## Troubleshooting

### Common Issues

1. **Feature always shows as restricted**: Check that SubscriptionController is properly initialized
2. **Usage not incrementing**: Ensure you're calling `incrementFeatureUsage()` after successful operations
3. **Upgrade prompts not showing**: Verify FeatureGate is properly wrapped around content

### Debug Information

You can check current feature access status:

```dart
final controller = Provider.of<FeatureAccessController>(context, listen: false);
print('Can access AI Insights: ${controller.canAccessFeature(AppFeature.aiInsights)}');
print('Baby profile usage: ${controller.getCurrentUsage(AppFeature.multipleBabyProfiles)}');
print('Baby profile limit: ${controller.getFeatureLimit(AppFeature.multipleBabyProfiles)}');
```