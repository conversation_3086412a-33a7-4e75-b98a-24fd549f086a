import 'package:flutter/material.dart';
import '../models/activity_log.dart';
import '../models/baby_profile.dart';

/// Type-safe route builders for common argument patterns
class TypedRouteBuilders {
  /// Route builder that expects a BabyProfile argument
  static WidgetBuilder withBabyProfile(
    Widget Function(BuildContext context, BabyProfile babyProfile) builder, {
    Widget Function(BuildContext context)? fallback,
  }) {
    return (BuildContext context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is BabyProfile) {
        return builder(context, args);
      }
      
      if (fallback != null) {
        return fallback(context);
      }
      
      throw ArgumentError('BabyProfile argument required');
    };
  }

  /// Route builder that expects optional BabyProfile argument
  static WidgetBuilder withOptionalBabyProfile(
    Widget Function(BuildContext context, BabyProfile? babyProfile) builder,
  ) {
    return (BuildContext context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      final babyProfile = args is BabyProfile ? args : null;
      return builder(context, babyProfile);
    };
  }

  /// Route builder that expects BabyProfile and activities
  static WidgetBuilder withBabyProfileAndActivities(
    Widget Function(BuildContext context, BabyProfile babyProfile, List<ActivityLog> activities) builder,
  ) {
    return (BuildContext context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic>) {
        final babyProfile = args['babyProfile'] as BabyProfile?;
        final activities = args['activities'] as List<ActivityLog>?;
        
        if (babyProfile != null && activities != null) {
          return builder(context, babyProfile, activities);
        }
      }
      
      throw ArgumentError('BabyProfile and activities arguments required');
    };
  }

  /// Route builder for QuickLog with flexible arguments
  static WidgetBuilder quickLogBuilder(
    Widget Function(BuildContext context, BabyProfile? babyProfile, String? activityType) builder,
  ) {
    return (BuildContext context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      
      BabyProfile? babyProfile;
      String? activityType;
      
      if (args is Map<String, dynamic>) {
        babyProfile = args['babyProfile'] as BabyProfile?;
        activityType = args['initialActivityType'] as String?;
      } else if (args is BabyProfile) {
        babyProfile = args;
      }
      
      return builder(context, babyProfile, activityType);
    };
  }

  /// Route builder for subscription screen with options
  static WidgetBuilder subscriptionBuilder(
    Widget Function(BuildContext context, {bool showBackButton, String? initialFocus}) builder,
  ) {
    return (BuildContext context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      
      bool showBackButton = true;
      String? initialFocus;
      
      if (args is Map<String, dynamic>) {
        showBackButton = args['showBackButton'] ?? true;
        initialFocus = args['initialFocus'] as String?;
      }
      
      return builder(context, showBackButton: showBackButton, initialFocus: initialFocus);
    };
  }
}