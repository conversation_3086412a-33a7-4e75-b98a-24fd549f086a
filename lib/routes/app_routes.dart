import 'package:flutter/material.dart';

import '../models/activity_log.dart';
import '../models/baby_profile.dart';
import '../presentation/ai_chat/ai_chat_screen.dart';
import '../presentation/ai_chat_assistant/ai_chat_assistant.dart';
import '../presentation/ai_insights/ai_insights_screen.dart';
import '../presentation/ai_insights_dashboard/ai_insights_dashboard.dart';
import '../utils/subscription_access_control.dart';
import '../presentation/subscription/widgets/upgrade_required_screen.dart';
import 'route_guards.dart';
import 'typed_route_builders.dart';
import '../presentation/auth/sign_in_screen.dart';
import '../presentation/auth/sign_up_screen.dart';
import '../presentation/authentication_screen/authentication_screen.dart';
import '../presentation/baby_profile_creation/baby_profile_creation.dart';
import '../presentation/baby_selector_screen/baby_selector_screen.dart';
import '../presentation/growth_charts/growth_charts.dart';
import '../presentation/home/<USER>';
import '../presentation/main_navigation/main_navigation_screen.dart';
import '../presentation/onboarding_flow/onboarding_flow.dart';
import '../presentation/quick_log_bottom_sheet/quick_log_bottom_sheet.dart';
import '../presentation/settings/settings.dart';
import '../presentation/notifications/notifications_screen.dart';
import '../presentation/splash_screen/splash_screen.dart';
import '../presentation/tracker_screen/tracker_screen.dart';
import '../presentation/user_management_screen/user_management_screen.dart';
import '../presentation/debug/database_test_screen.dart';
import '../presentation/activity_timeline/activity_timeline_screen.dart';
import '../presentation/milestones/milestones_screen.dart';
import '../presentation/scheduler/scheduler_screen.dart';
import '../presentation/subscription/subscription_screen.dart';

class AppRoutes {
  // TODO: Add your routes here
  static const String initial = '/';
  static const String splashScreen = '/splash-screen';
  static const String authenticationScreen = '/authentication-screen';
  static const String signIn = '/sign-in';
  static const String signUp = '/sign-up';
  static const String onboardingFlow = '/onboarding-flow';
  static const String babyProfileCreation = '/baby-profile-creation';
  static const String babySelectorScreen = '/baby-selector-screen';
  static const String mainNavigation = '/main-navigation';
  static const String home = '/home';
  static const String sleepTracker = '/sleep-tracker';
  static const String growthCharts = '/growth-charts';
  static const String quickLogBottomSheet = '/quick-log-bottom-sheet';
  static const String feedingTracker = '/feeding-tracker';
  static const String aiChat = '/ai-chat';
  static const String aiChatAssistant = '/ai-chat-assistant';
  static const String aiInsights = '/ai-insights';
  static const String aiInsightsDashboard = '/ai-insights-dashboard';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String userManagementScreen = '/user-management-screen';
  static const String trackerScreen = '/tracker-screen';
  static const String databaseTest = '/database-test';
  static const String babyProfileView = '/baby-profile-view';
  static const String diaperLog = '/diaper-log';
  static const String activityTimeline = '/activity-timeline';
  static const String milestones = '/milestones';
  static const String scheduler = '/scheduler';
  static const String subscription = '/subscription';

  static Map<String, WidgetBuilder> routes = {
    initial: (context) => SplashScreen(),
    splashScreen: (context) => SplashScreen(),
    authenticationScreen: (context) => AuthenticationScreen(),
    signIn: (context) => SignInScreen(),
    signUp: (context) => SignUpScreen(),
    onboardingFlow: (context) => OnboardingFlow(),
    babyProfileCreation: TypedRouteBuilders.withOptionalBabyProfile(
      (context, babyProfile) => BabyProfileCreation(babyProfile: babyProfile),
    ),
    babySelectorScreen: (context) => BabySelectorScreen(),
    mainNavigation: (context) => MainNavigationScreen(),
    home: (context) => Home(),
    sleepTracker: (context) => TrackerScreen(),
    growthCharts: (context) {
      // Check subscription access first
      if (!SubscriptionAccessControl.hasFeatureAccess(context, 'who_growth_charts')) {
        return UpgradeRequiredScreen(
          featureName: 'who_growth_charts',
          title: 'Growth Charts',
          customMessage: 'WHO Growth Charts require Premium plan. Upgrade to track your baby\'s growth with official WHO percentile charts.',
        );
      }
      
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is BabyProfile) {
        return GrowthCharts(babyProfile: args);
      }
      // Handle the case where no baby profile is provided,
      // perhaps by navigating to a baby selection screen or showing an error.
      // For now, we'll throw an error.
      throw ArgumentError('BabyProfile argument required for GrowthCharts');
    },
    quickLogBottomSheet: (context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic>) {
        return QuickLogBottomSheet(
          babyProfile: args['babyProfile'] as BabyProfile?,
          initialActivityType: args['initialActivityType'] as String?,
        );
      } else if (args is BabyProfile) {
        return QuickLogBottomSheet(babyProfile: args);
      }
      return QuickLogBottomSheet();
    },
    feedingTracker: (context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic>) {
        return TrackerScreen();
      } else if (args is BabyProfile) {
        return TrackerScreen();
      }
      return TrackerScreen();
    },
    aiInsights: (context) {
      // Check subscription access first
      if (!SubscriptionAccessControl.hasFeatureAccess(context, 'ai_insights')) {
        return UpgradeRequiredScreen(
          featureName: 'ai_insights',
          title: 'AI Insights',
          customMessage: 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
        );
      }
      
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic>) {
        final babyProfile = args['babyProfile'] as BabyProfile;
        final activities = args['activities'] as List<ActivityLog>;
        return AIInsightsScreen(babyProfile: babyProfile, activities: activities);
      }
      throw ArgumentError('BabyProfile and activities arguments required for AIInsightsScreen');
    },
    aiInsightsDashboard: (context) {
      // Check subscription access first
      if (!SubscriptionAccessControl.hasFeatureAccess(context, 'ai_insights')) {
        return UpgradeRequiredScreen(
          featureName: 'ai_insights',
          title: 'AI Insights',
          customMessage: 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
        );
      }
      
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is BabyProfile) {
        return AIInsightsDashboard(babyProfile: args);
      }
      throw ArgumentError('BabyProfile argument required for AIInsightsDashboard');
    },
    settings: (context) => Settings(),
    notifications: (context) => NotificationsScreen(),
    userManagementScreen: (context) => UserManagementScreen(),
    trackerScreen: (context) => TrackerScreen(),
    databaseTest: (context) => DatabaseTestScreen(),
    babyProfileView: (context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is BabyProfile) {
        return BabyProfileCreation(babyProfile: args);
      }
      return const BabyProfileCreation();
    },
    diaperLog: (context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      return QuickLogBottomSheet(
        babyProfile: args is BabyProfile ? args : null,
        initialActivityType: 'diaper',
      );
    },
    activityTimeline: (context) => ActivityTimelineScreen(),
    milestones: (context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is BabyProfile) {
        return QuickLogBottomSheet(
          babyProfile: args,
          initialActivityType: 'milestones',
        );
      } else if (args is Map<String, dynamic>) {
        return QuickLogBottomSheet(
          babyProfile: args['babyProfile'] as BabyProfile?,
          initialActivityType: 'milestones',
        );
      }
      return QuickLogBottomSheet(initialActivityType: 'milestones');
    },
    scheduler: (context) => SchedulerScreen(),
    subscription: (context) {
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is Map<String, dynamic>) {
        return SubscriptionScreen(
          showBackButton: args['showBackButton'] ?? true,
          initialFocus: args['initialFocus'] as String?,
        );
      }
      return const SubscriptionScreen();
    },
    // AI routes require arguments, handled separately
    // TODO: Add your other routes here
  };

  // Helper method to navigate to AI Chat with arguments
  static void navigateToAIChat(
    BuildContext context, {
    required BabyProfile babyProfile,
    List<ActivityLog>? recentActivities,
  }) {
    // Check subscription access first
    if (!SubscriptionAccessControl.hasFeatureAccess(context, 'ai_chat')) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UpgradeRequiredScreen(
            featureName: 'ai_chat',
            title: 'Ask AI',
            customMessage: 'AI Chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
          ),
        ),
      );
      return;
    }
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIChatScreen(
          babyProfile: babyProfile,
          recentActivities: recentActivities,
        ),
      ),
    );
  }

  // Helper method to navigate to AI Chat Assistant with arguments
  static void navigateToAIChatAssistant(
    BuildContext context, {
    required BabyProfile babyProfile,
    List<ActivityLog>? recentActivities,
  }) {
    // Check subscription access first
    if (!SubscriptionAccessControl.hasFeatureAccess(context, 'ai_chat')) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UpgradeRequiredScreen(
            featureName: 'ai_chat',
            title: 'Ask AI',
            customMessage: 'AI Chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
          ),
        ),
      );
      return;
    }
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIChatAssistant(
          babyProfile: babyProfile,
          recentActivities: recentActivities,
        ),
      ),
    );
  }

  // Helper method to navigate to AI Insights with arguments
  static void navigateToAIInsights(
    BuildContext context, {
    required BabyProfile babyProfile,
    required List<ActivityLog> activities,
  }) {
    // Check subscription access first
    if (!SubscriptionAccessControl.hasFeatureAccess(context, 'ai_insights')) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UpgradeRequiredScreen(
            featureName: 'ai_insights',
            title: 'AI Insights',
            customMessage: 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
          ),
        ),
      );
      return;
    }
    
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AIInsightsScreen(
          babyProfile: babyProfile,
          activities: activities,
        ),
      ),
    );
  }
}
