import 'package:flutter/material.dart';
import '../utils/subscription_access_control.dart';
import '../presentation/subscription/widgets/upgrade_required_screen.dart';

/// Utility class for protecting routes with subscription access control
class RouteGuards {
  /// Wraps a route builder with subscription access control
  /// 
  /// If the user doesn't have access to the feature, shows an upgrade screen instead
  static WidgetBuilder withSubscriptionAccess({
    required String featureName,
    required String title,
    required WidgetBuilder builder,
    String? customMessage,
    List<String>? benefits,
    IconData? icon,
  }) {
    return (BuildContext context) {
      if (!SubscriptionAccessControl.hasFeatureAccess(context, featureName)) {
        return UpgradeRequiredScreen(
          featureName: featureName,
          title: title,
          customMessage: customMessage,
          benefits: benefits,
          icon: icon,
        );
      }
      return builder(context);
    };
  }

  /// Wraps a route builder that requires arguments with subscription access control
  static WidgetBuilder withSubscriptionAccessAndArgs<T>({
    required String featureName,
    required String title,
    required Widget Function(BuildContext context, T args) builder,
    String? customMessage,
    List<String>? benefits,
    IconData? icon,
  }) {
    return (BuildContext context) {
      if (!SubscriptionAccessControl.hasFeatureAccess(context, featureName)) {
        return UpgradeRequiredScreen(
          featureName: featureName,
          title: title,
          customMessage: customMessage,
          benefits: benefits,
          icon: icon,
        );
      }
      
      final args = ModalRoute.of(context)?.settings.arguments;
      if (args is T) {
        return builder(context, args);
      }
      
      throw ArgumentError('${T.toString()} argument required for $title');
    };
  }

  /// Common subscription messages for different features
  static const Map<String, String> _featureMessages = {
    'who_growth_charts': 'WHO Growth Charts require Premium plan. Upgrade to track your baby\'s growth with official WHO percentile charts.',
    'ai_insights': 'AI Insights require Premium plan. Upgrade to get personalized insights about your baby\'s development patterns.',
    'ai_chat': 'AI Chat requires Premium plan. Upgrade to get 24/7 AI assistance for parenting questions.',
    'family_sharing': 'Family Sharing requires Premium plan. Upgrade to invite up to 10 family members.',
    'data_export': 'Data Export requires Premium plan. Upgrade to export your data for healthcare providers.',
  };

  /// Get default message for a feature
  static String getFeatureMessage(String featureName) {
    return _featureMessages[featureName] ?? 
           'This feature requires Premium plan. Upgrade to unlock all features.';
  }
}