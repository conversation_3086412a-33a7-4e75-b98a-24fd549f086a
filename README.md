# 🍼 BabyTracker Pro - AI-Powered Baby Care Assistant

<div align="center">

![BabyTracker Pro](assets/images/img_app_logo.svg)

**A comprehensive, AI-powered baby tracking application built with Flutter**

[![Flutter](https://img.shields.io/badge/Flutter-3.29.2-blue.svg)](https://flutter.dev/)
[![Dart](https://img.shields.io/badge/Dart-3.6.0-blue.svg)](https://dart.dev/)
[![Supabase](https://img.shields.io/badge/Supabase-Backend-green.svg)](https://supabase.com/)
[![OpenAI](https://img.shields.io/badge/OpenAI-GPT--4-orange.svg)](https://openai.com/)

</div>

## 📖 Overview

BabyTracker Pro is a modern, comprehensive baby care tracking application that combines traditional activity logging with AI-powered insights. Built with Flutter for cross-platform compatibility, it offers parents and caregivers an intelligent, intuitive way to monitor their baby's development, health, and daily activities.

### ✨ Key Features

#### 🤖 **AI-Powered Intelligence**
- **Smart Chat Assistant**: Interactive AI chat powered by OpenAI GPT-4o-mini
- **Comprehensive AI Insights**: ML-driven pattern analysis across sleep, feeding, diaper, and growth data
- **Behavioral Analytics**: Advanced sleep pattern recognition, feeding habit analysis, and development trend tracking
- **Personalized Recommendations**: AI-generated actionable suggestions based on your baby's unique data patterns
- **Smart Caching**: Intelligent rate limiting with user activity tracking to optimize AI usage

#### 📊 **Comprehensive Activity Tracking**
- **25+ Activity Types**: Feeding, Sleep, Diaper, Medicine, Vaccination, Temperature, Potty, Bath, Tummy Time, Story Time, Screen Time, Skin-to-Skin, Outdoor Play, Indoor Play, Brush Teeth, Pumping, Growth Measurements, Milestones, and Custom Activities
- **Quick Log Bottom Sheet**: Instant activity recording with contextual forms for each activity type
- **Recent Logs Integration**: Shows relevant historical entries under each activity form for context
- **Smart Timers**: Built-in duration tracking for feeding and sleep sessions
- **Photo Documentation**: Attach photos to activities for visual tracking
- **Consistent Data Display**: Unified presentation across all widgets with proper activity-specific details

#### 📈 **Growth & Development Monitoring**
- **WHO Growth Percentiles**: Integrated World Health Organization growth charts with percentile calculations
- **Weight, Height & Head Circumference**: Comprehensive measurement tracking with trend analysis
- **Milestone Tracking**: Complete milestone logging with categories, descriptions, and age tracking
- **Visual Progress Charts**: Interactive charts with historical data visualization and scaling optimization
- **Growth Insights**: AI-powered analysis of growth patterns and recommendations
- **Professional Measurement Units**: Hybrid metric/imperial system with precision preservation

#### 📷 **Professional Photo Management**
- **Manual Image Editing**: Full-screen crop editor with drag, move, and zoom functionality for perfect baby photos
- **Face Positioning**: Intuitive drag and pinch controls to position baby's face perfectly in the frame
- **Square Cropping**: Automatic square aspect ratio for consistent, professional profile photos
- **Manual Confirmation**: No auto-upload - users have complete control over the photo process with preview and confirmation
- **Real Camera Integration**: Native camera access for iOS and Android devices with immediate editing workflow
- **Gallery Selection**: Browse and select existing photos from device gallery with instant crop editor
- **Image Optimization**: Smart compression (typically 60-80KB, 700x700 or 800x800) while maintaining excellent visual quality
- **Storage Cost Optimization**: Automatic cleanup of old images - only one photo per baby maintained to minimize Supabase storage costs
- **Download Capability**: Save photos to device gallery with professional full-screen viewer
- **Professional UI**: Clean, intuitive black interface with clear instructions ("Drag to move • Pinch to zoom")
- **Cloud Storage**: Automatic upload to Supabase storage with public URL generation and old image cleanup
- **Cross-Platform Permissions**: Seamless permission handling for camera and photo library
- **Consistent Display**: Unified baby photo widgets across all app screens
- **Fallback Avatars**: Gender-based avatar system with initials when photos unavailable

#### �👥 **Multi-Carer Support**
- **Family Sharing**: Multiple caregivers can access and update the same baby profile
- **Role-Based Permissions**: Customizable access levels for different family members
- **Real-time Synchronization**: Instant updates across all connected devices with Supabase realtime
- **Session Management**: Track active sessions and manage family member access
- **User Management**: Comprehensive family member invitation and role assignment system

#### 🎨 **Beautiful User Experience**
- **Material 3 Design**: Modern, intuitive interface following Google's latest design principles
- **"Nurturing Minimalism" Theme**: Custom "Gentle Authority" color palette optimized for parents
- **Dark/Light Themes**: Automatic theme switching with nursery-optimized dark mode
- **Responsive Design**: Optimized for phones, tablets, and different screen sizes using Sizer
- **Accessibility**: Built with accessibility features for all users

## 🏗️ Technical Architecture

### **Frontend**
- **Framework**: Flutter 3.29.2 with Dart 3.6.0
- **State Management**: Provider pattern with reactive UI updates
- **UI Components**: Custom widgets with Material 3 theming
- **Responsive Design**: Sizer package for adaptive layouts
- **Charts**: FL Chart for beautiful data visualizations
- **Measurement System**: Hybrid metric/imperial with precision preservation

### **Backend Services**
- **Database**: Supabase (PostgreSQL) with real-time subscriptions
- **Authentication**: Supabase Auth with row-level security
- **AI Services**: OpenAI GPT-4 for chat and insights
- **File Storage**: Supabase Storage for baby photos and documents
- **Real-time Sync**: Live data synchronization across devices

### **Key Dependencies**
```yaml
dependencies:
  flutter: sdk: flutter
  supabase_flutter: ^2.9.1      # Backend & Auth
  fl_chart: ^0.65.0             # Data visualization
  sizer: ^2.0.15                # Responsive design
  provider: ^6.1.2              # State management
  image_picker: ^1.0.8          # Camera & gallery access
  image: ^4.2.0                 # Image processing & compression
  permission_handler: ^11.3.1   # Runtime permissions
  path_provider: ^2.1.4         # Local file storage
  connectivity_plus: ^5.0.2     # Network status
```

## 🚀 Quick Start

### Prerequisites
- Flutter SDK (≥3.29.2)
- Dart SDK (≥3.6.0)  
- Android Studio / VS Code with Flutter extensions
- iOS development: Xcode (macOS only)
- Android development: Android SDK

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/georgie-liao/Baby-Tracker-AI.git
   cd Baby-Tracker-AI
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Configure environment variables**
   
   Create an `env.json` file in the root directory:
   ```json
   {
     "SUPABASE_URL": "your-supabase-project-url",
     "SUPABASE_ANON_KEY": "your-supabase-anon-key",
     "OPENAI_API_KEY": "your-openai-api-key"
   }
   ```

4. **Run the application**
   ```bash
   flutter run --dart-define-from-file=env.json
   ```

### IDE Configuration

#### **VS Code**
Create `.vscode/launch.json`:
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Launch BabyTracker Pro",
      "request": "launch",
      "type": "dart",
      "program": "lib/main.dart",
      "args": ["--dart-define-from-file", "env.json"]
    }
  ]
}
```

#### **Android Studio / IntelliJ**
- Go to **Run > Edit Configurations**
- Add to **Additional arguments**: `--dart-define-from-file=env.json`

## 📁 Project Structure

```
babytracker_pro/
├── 📱 android/                 # Android-specific configuration
├── 🍎 ios/                     # iOS-specific configuration  
├── 🌐 web/                     # Web-specific configuration
├── 📂 lib/
│   ├── 🎯 core/                # Core utilities and exports
│   ├── 📊 models/              # Data models
│   │   ├── activity_log.dart   # Activity tracking models
│   │   ├── baby_profile.dart   # Baby profile data
│   │   ├── ai_insight.dart     # AI insights models
│   │   └── user_profile.dart   # User management models
│   ├── 🎨 presentation/        # UI screens and widgets
│   │   ├── 🏠 dashboard/       # Main dashboard
│   │   ├── 🤖 ai_chat/         # AI chat assistant
│   │   ├── 📈 ai_insights/     # AI insights & analytics
│   │   ├── 🍼 feeding_tracker/ # Feeding management
│   │   ├── 😴 sleep_tracker/   # Sleep monitoring
│   │   ├── 📏 growth_charts/   # WHO growth percentiles
│   │   ├── 👥 user_management/ # Multi-carer support
│   │   ├── ⚙️ settings/        # App settings
│   │   └── 🔐 auth/           # Authentication
│   ├── 🔧 services/           # Backend services
│   │   ├── supabase_service.dart    # Database operations
│   │   ├── openai_service.dart      # AI chat & insights
│   │   ├── ai_analysis_service.dart # Pattern analysis
│   │   └── who_percentile_service.dart # Growth charts
│   ├── 🎨 theme/              # App theming
│   ├── 🛣️ routes/             # Navigation routing
│   └── 🧩 widgets/            # Reusable components
├── 🗄️ supabase/
│   └── migrations/            # Database schema migrations
├── 📁 assets/                 # Images, icons, and static files
├── 📋 pubspec.yaml           # Dependencies and configuration
└── 📖 README.md              # This file
```

## 🔧 Recent Improvements & Fixes

### 🍼 **Enhanced Feeding Log Management** (Latest Update)
Comprehensive feeding tracking with improved UI and data accuracy:

- **Smart Slider Controls**: Sliders automatically hide when custom amount/duration is entered, eliminating visual confusion
- **Breast Feeding Duration Display**: Fixed missing duration information in Recent Feedings section for breast feeding logs
- **Extended Recent Feeding History**: Increased from 3 to 10 recent feeding logs with "View All" button for full activity timeline
- **Type-Specific Data Filtering**: Proper separation of bottle amounts, breast feeding duration, and solid food details
- **Widget Cleanup**: Removed obsolete feeding widgets (`enhanced_feeding_entry_widget.dart`, `feeding_entry_widget_old.dart`) for cleaner codebase

### 🎯 **Activity Log Consistency Architecture**
Implemented a comprehensive solution for consistent activity display across all widgets:

- **Unified Display Logic**: All activity types now show consistent titles and detailed information
- **Centralized Data Mapping**: The `toRecentActivityMap()` method ensures uniform presentation
- **Activity-Specific Details**: Each activity type displays relevant information (vaccine names, medication dosages, sleep quality, etc.)
- **Smart Data Handling**: Proper extraction and formatting of activity-specific data from the database

### 🤖 **AI Insights Intelligence**
Enhanced AI system with smart caching and user activity tracking:

- **User Activity Tracking**: Only generates fresh insights when users are actively using the app
- **Smart Rate Limiting**: Prevents unnecessary API calls by checking for new data before generating insights
- **Comprehensive Analysis**: AI analyzes sleep patterns, feeding habits, diaper changes, and growth trends
- **Contextual Recommendations**: Provides actionable suggestions based on baby's age and recent activities

### 💉 **Vaccination Tracking System**
Complete vaccination logging with professional-grade features:

- **Comprehensive Vaccine Database**: 13 common vaccines plus custom option
- **Medical Record Integration**: Proper storage and display of vaccination records
- **Recent Vaccination History**: Shows context for upcoming immunizations
- **Pediatric Compliance**: Designed to support standard vaccination schedules

### 📊 **Recent Logs Integration**
Contextual historical data display for better user experience:

- **Activity-Specific History**: Shows relevant recent logs under each activity form
- **Smart Filtering**: Displays only the most recent 3 entries for each activity type
- **Consistent UI Design**: Matches the main Recent Activities widget styling
- **Performance Optimized**: Efficient data loading and client-side filtering

### 🔄 **Data Synchronization Fixes**
Robust data consistency across all screens:

- **Shared Data Loading**: All screens use consistent data loading methods
- **Real-time Updates**: Supabase realtime subscriptions for live data sync
- **Navigation Callbacks**: Proper refresh triggers when navigating between screens
- **Fallback Mechanisms**: Graceful handling of navigation edge cases

### 🏗️ **Centralized Architecture Pattern**
Designed for scalability and maintainability:

```dart
// Template Method Pattern for Activity Logs
class ActivityLog {
  Map<String, dynamic> toRecentActivityMap() {
    // Centralized logic for consistent display
    return {
      'title': _getActivityTitle(),
      'type_detail': _getActivityDetails(),
      'notes': notes,
      'timestamp': timestamp,
    };
  }
}
```

### 🎨 **Professional UI/UX Improvements**
- **Material 3 Integration**: Complete adoption of Material 3 design principles
- **"Nurturing Minimalism"**: Custom design philosophy optimized for parents
- **Responsive Typography**: Google Fonts with readability optimization
- **Accessibility Compliance**: WCAG guidelines implementation
- **Dark Mode Optimization**: Nursery-friendly dark theme for night usage

## 📚 Centralized Architecture Solution

### 🎨 **Design Pattern for Future Activity Widgets**

To prevent inconsistency issues when adding new activity log widgets, we've implemented a centralized architecture using the **Template Method Pattern**:

#### **1. Abstract Base Class Architecture**
```dart
abstract class ActivityLogWidget {
  // Template method that defines the structure
  Widget build(BuildContext context) {
    return Column(
      children: [
        buildHeader(),
        buildForm(),
        buildRecentLogs(),
        buildSaveButton(),
      ],
    );
  }
  
  // Abstract methods to be implemented by subclasses
  String getActivityTitle();
  String getActivityDetails();
  Widget buildForm();
  Map<String, dynamic> buildData();
  
  // Concrete method with consistent logic
  Map<String, dynamic> toRecentActivityMap() {
    return {
      'title': getActivityTitle(),
      'type_detail': getActivityDetails(),
      'notes': notes,
      'timestamp': timestamp,
    };
  }
}
```

#### **2. Concrete Implementation Example**
```dart
class VaccinationWidget extends ActivityLogWidget {
  @override
  String getActivityTitle() => 'Vaccination';
  
  @override
  String getActivityDetails() => selectedVaccine;
  
  @override
  Widget buildForm() {
    return Column(
      children: [
        VaccineDropdown(),
        NotesField(),
        DateTimePicker(),
      ],
    );
  }
  
  @override
  Map<String, dynamic> buildData() {
    return {
      'vaccine': selectedVaccine,
      'notes': notes,
      'startTime': selectedDateTime,
    };
  }
}
```

#### **3. Centralized Registration System**
```dart
class ActivityWidgetRegistry {
  static final Map<String, ActivityLogWidget Function()> _widgets = {
    'feeding': () => FeedingWidget(),
    'sleep': () => SleepWidget(),
    'diaper': () => DiaperWidget(),
    'medicine': () => MedicineWidget(),
    'vaccination': () => VaccinationWidget(),
    // Easy to add new widgets
  };
  
  static ActivityLogWidget createWidget(String type) {
    return _widgets[type]?.call() ?? GenericActivityWidget();
  }
}
```

### 🛠️ **Benefits of This Architecture**

1. **Consistency**: All activity widgets follow the same structure and display logic
2. **Scalability**: Easy to add new activity types without affecting existing code
3. **Maintainability**: Centralized logic makes updates and bug fixes straightforward
4. **Type Safety**: Compile-time checks ensure all required methods are implemented
5. **Code Reuse**: Common functionality is shared across all activity widgets

### 📝 **Implementation Checklist for New Activity Widgets**

When adding a new activity widget:

- [ ] Extend the `ActivityLogWidget` base class
- [ ] Implement required abstract methods
- [ ] Add activity type to `ActivityType` enum
- [ ] Register widget in `ActivityWidgetRegistry`
- [ ] Add to QuickLog bottom sheet activity types
- [ ] Create database migration for new activity type
- [ ] Update Recent Activities display logic
- [ ] Add appropriate icons and colors
- [ ] Test data flow and display consistency

### 🔄 **Future-Proof Data Flow**

```dart
// Consistent data flow for all activity types
User Input → ActivityLogWidget → buildData() → Supabase → toRecentActivityMap() → UI Display
```

This architecture ensures that:
- All activity widgets have consistent UI structure
- Data is handled uniformly across the application
- Display logic is centralized and maintainable
- New activity types can be added with minimal effort
- The codebase remains clean and organized

### 🔮 **Recommended Next Steps**

1. **Refactor Existing Widgets**: Gradually migrate current activity widgets to use the new base class
2. **Create Widget Generator**: Build a code generator for new activity widgets
3. **Add Validation Framework**: Implement centralized validation logic
4. **Enhance Type System**: Add stronger typing for activity-specific data
5. **Documentation**: Create developer guides for adding new activity types

## 🔧 Configuration

### Supabase Setup

1. **Create a Supabase project** at [supabase.com](https://supabase.com)

2. **Run database migrations**
   ```bash
   supabase db push
   ```

3. **Enable Row Level Security (RLS)** for all tables

4. **Configure authentication providers** in Supabase dashboard

5. **Set up Storage Bucket for baby photos**
   - Go to Storage in Supabase dashboard
   - Create new bucket named `baby-photos` (public bucket)
   - Set file size limit to 50MB
   - Allow image types: JPEG, PNG, WebP, GIF
   - See [SUPABASE_STORAGE_SETUP.md](SUPABASE_STORAGE_SETUP.md) for detailed instructions

### OpenAI Setup

1. **Get API key** from [OpenAI Platform](https://platform.openai.com)
2. **Set up billing** and usage limits
3. **Add key to environment variables**

## 📊 Database Schema

### Core Tables
- **`user_profiles`**: User accounts with personal measurements
- **`baby_profiles`**: Baby information with birth measurements
- **`activity_logs`**: All activities with measurement data
- **`growth_measurements`**: WHO-standard growth tracking
- **`milestones`**: Development milestones with measurements
- **`scheduled_activities`**: Planned activities with target measurements
- **`medicine_logs`**: Medicine administration with dosage tracking
- **`vaccination_logs`**: Vaccination records with dosage data
- **`ai_insights`**: AI-generated insights and recommendations
- **`chat_messages`**: AI chat conversation history

### Hybrid Measurement System
Each measurement field includes:
- `*_metric_value` - Value in metric units (kg, cm, °C, ml)
- `*_imperial_value` - Value in imperial units (lbs, in, °F, fl oz)
- `*_original_value` - Exact value as entered by user
- `*_original_unit` - Unit used when originally entered
- `*_entered_as_metric` - Whether user entered in metric system

This ensures perfect precision preservation and accurate conversions for all users.

### Key Features
- **Real-time subscriptions** for live data updates
- **Row Level Security (RLS)** for data privacy
- **Automated timestamps** and audit trails
- **Optimized indexes** for fast queries
- **Dual measurement storage** with automatic conversion triggers

## 🎨 Theming & Design

### Design System
- **Material 3** design language
- **Dynamic color** system with theme adaptations
- **Typography scale** optimized for readability
- **Consistent spacing** using 8px grid system

### Responsive Design
```dart
// Example responsive sizing
Container(
  width: 90.w,    // 90% of screen width
  height: 25.h,   // 25% of screen height
  padding: EdgeInsets.all(4.w), // 4% padding
)
```

### Custom Components
- **Activity cards** with consistent styling
- **Chart widgets** with interactive tooltips
- **AI chat bubbles** with typing indicators
- **Quick action buttons** with haptic feedback

## 🚀 Deployment

### Android (APK)
```bash
flutter build apk --release --dart-define-from-file=env.json
```

### Android (App Bundle)
```bash
flutter build appbundle --release --dart-define-from-file=env.json
```

### iOS
```bash
flutter build ios --release --dart-define-from-file=env.json
```

### Web
```bash
flutter build web --release --dart-define-from-file=env.json
```

## 🧪 Testing

### Run all tests
```bash
flutter test
```

### Widget tests
```bash
flutter test test/widget_test.dart
```

### Integration tests
```bash
flutter drive --target=test_driver/app.dart
```

## 📈 Performance Optimization

### Key Optimizations
- **Lazy loading** for large data sets
- **Image caching** for smooth scrolling  
- **Database indexing** for fast queries
- **Widget rebuilding** optimization with Provider
- **Memory management** for long-running sessions

### Monitoring
- **Crashlytics** integration for error tracking
- **Performance monitoring** with Firebase
- **User analytics** for feature usage insights

## 🔒 Security & Privacy

### Data Protection
- **End-to-end encryption** for sensitive data
- **Row Level Security (RLS)** in database
- **API key protection** with environment variables
- **Secure authentication** with Supabase Auth

### Privacy Features
- **Local data storage** option
- **Data export/import** capabilities
- **Account deletion** with data purging
- **GDPR compliance** features

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Setup
1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Flutter Team** for the amazing framework
- **Supabase** for the powerful backend platform
- **OpenAI** for advanced AI capabilities
- **WHO** for growth percentile data
- **Material Design** for beautiful UI components

## 📞 Support

- **Documentation**: [Wiki](https://github.com/georgie-liao/Baby-Tracker-AI/wiki)
- **Issues**: [GitHub Issues](https://github.com/georgie-liao/Baby-Tracker-AI/issues)
- **Discussions**: [GitHub Discussions](https://github.com/georgie-liao/Baby-Tracker-AI/discussions)

---

<div align="center">

**Made with ❤️ for parents and babies everywhere**

[⭐ Star this repository](https://github.com/georgie-liao/Baby-Tracker-AI) if you find it helpful!

</div>
