#!/bin/bash

# <PERSON><PERSON>t to reset database and run original migrations
# Make sure you have Supabase CLI installed and are logged in

echo "🔄 Starting database reset and migration process..."

# Check if supabase CLI is available
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI not found. Please install it first:"
    echo "npm install -g supabase"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Not in a Supabase project directory. Please run from your project root."
    exit 1
fi

echo "🗑️  Step 1: Resetting database..."
# Reset the database using the reset script
supabase db reset --linked

echo "✅ Database reset completed!"

echo "🚀 Step 2: Running original migrations in order..."

# Run migrations in chronological order
migrations=(
    "20250102170000_create_missing_tables.sql"
    "20250107120000_fix_database_columns.sql"
    "20250107130000_create_activity_logs_table.sql"
    "20250107150000_create_role_management_system.sql"
    "20250107170000_fix_user_profiles_rls.sql"
    "20250108000000_fix_data_sync_issues.sql"
    "20250110000000_create_milestones_table.sql"
    "20250117000000_enhance_growth_measurements_schema.sql"
    "20250717224000_create_baby_profiles_table.sql"
    "20250717000000_create_scheduled_activities_table.sql"
    "20250704120000_add_note_to_baby_profiles.sql"
    "20250709000000_add_percentile_to_growth_measurements.sql"
    "20250125000003_add_vaccination_activity_type.sql"
    "20250717221903_add_missing_scheduled_activity_types.sql"
    "20250717223040_add_enum_values_only.sql"
)

for migration in "${migrations[@]}"; do
    if [ -f "supabase/migrations/$migration" ]; then
        echo "📝 Running: $migration"
        supabase db push --include-all
        if [ $? -eq 0 ]; then
            echo "✅ $migration completed successfully"
        else
            echo "❌ $migration failed"
            exit 1
        fi
    else
        echo "⚠️  Migration file not found: $migration"
    fi
done

echo "🎉 All migrations completed successfully!"
echo "🧪 You can now test baby profile creation in your app."