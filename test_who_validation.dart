import 'lib/services/who_data_service.dart';

void main() {
  print('=== WHO Data Service Validation ===\n');

  // Test Boys Weight at 12 months
  print('Boys Weight at 12 months:');
  final boysWeight12 = WHODataService.calculateExactPercentile(9.6, 12, 'weight', 'male');
  print('9.6 kg -> ${boysWeight12.toStringAsFixed(1)}th percentile');
  
  final boysWeight12Low = WHODataService.calculateExactPercentile(7.9, 12, 'weight', 'male');
  print('7.9 kg -> ${boysWeight12Low.toStringAsFixed(1)}th percentile');
  
  final boysWeight12High = WHODataService.calculateExactPercentile(12.0, 12, 'weight', 'male');
  print('12.0 kg -> ${boysWeight12High.toStringAsFixed(1)}th percentile\n');

  // Test Girls Weight at 12 months
  print('Girls Weight at 12 months:');
  final girlsWeight12 = WHODataService.calculateExactPercentile(8.9, 12, 'weight', 'female');
  print('8.9 kg -> ${girlsWeight12.toStringAsFixed(1)}th percentile');
  
  final girlsWeight12Low = WHODataService.calculateExactPercentile(7.2, 12, 'weight', 'female');
  print('7.2 kg -> ${girlsWeight12Low.toStringAsFixed(1)}th percentile');
  
  final girlsWeight12High = WHODataService.calculateExactPercentile(11.5, 12, 'weight', 'female');
  print('11.5 kg -> ${girlsWeight12High.toStringAsFixed(1)}th percentile\n');

  // Test Boys Height at 24 months
  print('Boys Height at 24 months:');
  final boysHeight24 = WHODataService.calculateExactPercentile(87.7, 24, 'height', 'male');
  print('87.7 cm -> ${boysHeight24.toStringAsFixed(1)}th percentile');
  
  final boysHeight24Low = WHODataService.calculateExactPercentile(82.0, 24, 'height', 'male');
  print('82.0 cm -> ${boysHeight24Low.toStringAsFixed(1)}th percentile');
  
  final boysHeight24High = WHODataService.calculateExactPercentile(93.0, 24, 'height', 'male');
  print('93.0 cm -> ${boysHeight24High.toStringAsFixed(1)}th percentile\n');

  // Test Z-scores
  print('Z-Score Examples:');
  final zScore50 = WHODataService.calculateZScore(9.6, 12, 'weight', 'male');
  print('9.6 kg at 12 months (boys) -> Z-score: ${zScore50.toStringAsFixed(2)}');
  
  final zScoreLow = WHODataService.calculateZScore(7.9, 12, 'weight', 'male');
  print('7.9 kg at 12 months (boys) -> Z-score: ${zScoreLow.toStringAsFixed(2)}');
  
  final zScoreHigh = WHODataService.calculateZScore(12.0, 12, 'weight', 'male');
  print('12.0 kg at 12 months (boys) -> Z-score: ${zScoreHigh.toStringAsFixed(2)}\n');

  // Test Percentile Analysis
  print('Percentile Analysis Examples:');
  final normalAnalysis = WHODataService.analyzePercentile(9.6, 12, 'weight', 'male');
  print('9.6 kg at 12 months (boys):');
  print('  Category: ${normalAnalysis.category}');
  print('  Interpretation: ${normalAnalysis.interpretation}');
  print('  Requires Attention: ${normalAnalysis.requiresAttention}\n');

  final lowAnalysis = WHODataService.analyzePercentile(6.0, 12, 'weight', 'male');
  print('6.0 kg at 12 months (boys):');
  print('  Category: ${lowAnalysis.category}');
  print('  Interpretation: ${lowAnalysis.interpretation}');
  print('  Requires Attention: ${lowAnalysis.requiresAttention}\n');

  // Test Data Validation
  print('Data Validation Examples:');
  print('Valid weight (5.0 kg at 6 months): ${WHODataService.isValidMeasurement(5.0, 6, 'weight', 'male')}');
  print('Invalid weight (100.0 kg at 6 months): ${WHODataService.isValidMeasurement(100.0, 6, 'weight', 'male')}');
  print('Valid height (65.0 cm at 6 months): ${WHODataService.isValidMeasurement(65.0, 6, 'height', 'male')}');
  print('Invalid height (200.0 cm at 6 months): ${WHODataService.isValidMeasurement(200.0, 6, 'height', 'male')}\n');

  // Test Normal Range
  print('Normal Range Examples:');
  print('9.6 kg at 12 months (boys) in normal range: ${WHODataService.isWithinNormalRange(9.6, 12, 'weight', 'male')}');
  print('6.0 kg at 12 months (boys) in normal range: ${WHODataService.isWithinNormalRange(6.0, 12, 'weight', 'male')}');
  print('15.0 kg at 12 months (boys) in normal range: ${WHODataService.isWithinNormalRange(15.0, 12, 'weight', 'male')}\n');

  print('=== Validation Complete ===');
}