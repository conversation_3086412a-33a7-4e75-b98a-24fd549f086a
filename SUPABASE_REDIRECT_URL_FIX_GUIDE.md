# Supabase Redirect URL Fix Guide

## 🎯 **Problem**
Email verification links are redirecting to `localhost:3000` which doesn't exist, causing blank screens and preventing email changes from completing.

## 🔧 **Solution: Fix Supabase Project Settings**

### **Step 1: Access Supabase Dashboard**
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Sign in to your account
3. Select your project: **snqeizaqnswgpxdhnlkr** (from your project URL)

### **Step 2: Navigate to Authentication Settings**
1. In the left sidebar, click **"Authentication"**
2. Click **"Settings"** (under Authentication)
3. Look for **"URL Configuration"** section

### **Step 3: Fix Site URL**
**Current Problem**: Site URL is probably set to `http://localhost:3000`

**Fix Options:**

#### **Option A: Use a Working Web URL (Recommended)**
Set Site URL to: `https://snqeizaqnswgpxdhnlkr.supabase.co`

#### **Option B: Use Deep Link for Mobile**
Set Site URL to: `io.supabase.flutter://signin-callback/`

#### **Option C: Use a Simple Success Page**
Set Site URL to: `https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify`

### **Step 4: Configure Redirect URLs**
In the **"Redirect URLs"** section, add these URLs:

```
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/callback
io.supabase.flutter://signin-callback/
```

### **Step 5: Save Changes**
1. Click **"Save"** at the bottom of the page
2. Wait for the changes to propagate (usually takes 1-2 minutes)

## 📱 **What Each URL Does**

### **Site URL**
- This is where email verification links redirect by default
- Should NOT be `localhost:3000` for production

### **Redirect URLs**
- These are allowed redirect destinations
- Must include your chosen Site URL
- Can include multiple options for flexibility

## 🎯 **Recommended Configuration**

```
Site URL: https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify

Redirect URLs:
- https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify
- https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/callback
- io.supabase.flutter://signin-callback/
```

## 🔍 **How to Find These Settings**

### **Visual Guide:**
1. **Dashboard** → **Authentication** → **Settings**
2. Scroll down to **"URL Configuration"**
3. You'll see:
   - **Site URL** (currently `localhost:3000` - this is the problem!)
   - **Redirect URLs** (list of allowed redirects)

### **What You'll See:**
```
Site URL: http://localhost:3000  ← CHANGE THIS!
```

### **What It Should Be:**
```
Site URL: https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify
```

## ⚠️ **Important Notes**

1. **Don't use localhost in production** - it only works on your development machine
2. **Use your Supabase project URL** - it's always available and works everywhere
3. **Save and wait** - changes take 1-2 minutes to propagate
4. **Test after changes** - try the email verification again

## 🧪 **Testing After Fix**

1. **Trigger email change** in the app again
2. **Check your email** at the new address
3. **Click the verification link** - should now work without blank screen
4. **Return to app** - should detect email change completion automatically

## 🆘 **If You Can't Find These Settings**

Look for these sections in your Supabase dashboard:
- **Authentication** → **Settings** → **URL Configuration**
- **Authentication** → **URL Configuration**
- **Settings** → **Authentication** → **URLs**

The exact location might vary slightly depending on your Supabase dashboard version.

## 🎯 **Expected Result**

After fixing these settings:
- ✅ Email verification links will work from any device
- ✅ No more blank screens
- ✅ Email changes will complete successfully
- ✅ App will automatically detect verification completion