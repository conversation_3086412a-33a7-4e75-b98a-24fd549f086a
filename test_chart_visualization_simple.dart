import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'lib/presentation/growth_charts/widgets/growth_chart_renderer.dart';

void main() {
  // Sample test data
  final testMeasurements = [
    {
      'date': DateTime.now().subtract(const Duration(days: 180)),
      'value': 7.5,
      'unit': 'kg',
      'notes': '6 month checkup',
    },
    {
      'date': DateTime.now().subtract(const Duration(days: 90)),
      'value': 9.2,
      'unit': 'kg',
      'notes': '9 month checkup',
    },
    {
      'date': DateTime.now(),
      'value': 10.5,
      'unit': 'kg',
      'notes': '12 month checkup',
    },
  ];

  testWidgets('GrowthChartRenderer displays correctly with test data', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: ThemeData(
          colorScheme: ColorScheme.light(
            primary: Colors.blue,
            secondary: Colors.green,
          ),
        ),
        home: Scaffold(
          body: Growth<PERSON><PERSON><PERSON>enderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );

    // Verify the chart is rendered
    expect(find.byType(GrowthChartRenderer), findsOneWidget);
    
    // Verify chart header elements
    expect(find.text('Weight Growth Chart'), findsOneWidget);
    expect(find.text('WHO Standards • 1 year • 3 measurements'), findsOneWidget);
    
    // Verify chart controls
    expect(find.text('Curves'), findsOneWidget);
    expect(find.text('Points'), findsOneWidget);
    
    // Verify legend items
    expect(find.text('Legend'), findsOneWidget);
    expect(find.text('Your Baby'), findsOneWidget);
    expect(find.text('97th'), findsOneWidget);
    expect(find.text('50th (Median)'), findsOneWidget);
    expect(find.text('3rd'), findsOneWidget);
  });

  testWidgets('GrowthChartRenderer handles empty measurements', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: ThemeData(
          colorScheme: ColorScheme.light(
            primary: Colors.blue,
            secondary: Colors.green,
          ),
        ),
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: [],
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );

    // Verify empty state is shown
    expect(find.text('No measurements yet'), findsOneWidget);
    expect(find.text('Add your first weight measurement\nto see growth trends and percentiles'), findsOneWidget);
  });

  testWidgets('GrowthChartRenderer toggles percentile curves', (WidgetTester tester) async {
    await tester.pumpWidget(
      MaterialApp(
        theme: ThemeData(
          colorScheme: ColorScheme.light(
            primary: Colors.blue,
            secondary: Colors.green,
          ),
        ),
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );

    // Find and tap the Curves toggle button
    final curvesButton = find.text('Curves');
    expect(curvesButton, findsOneWidget);
    
    await tester.tap(curvesButton);
    await tester.pumpAndSettle();
    
    // Find and tap the Points toggle button
    final pointsButton = find.text('Points');
    expect(pointsButton, findsOneWidget);
    
    await tester.tap(pointsButton);
    await tester.pumpAndSettle();
  });

  testWidgets('GrowthChartRenderer supports different measurement types', (WidgetTester tester) async {
    // Test weight
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );
    expect(find.text('Weight Growth Chart'), findsOneWidget);
    
    // Test height
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'height',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );
    expect(find.text('Height Growth Chart'), findsOneWidget);
    
    // Test head circumference
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'head_circumference',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );
    expect(find.text('Head Circumference Growth Chart'), findsOneWidget);
  });

  testWidgets('GrowthChartRenderer supports metric and imperial units', (WidgetTester tester) async {
    // Test metric units
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: true,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );
    
    // Test imperial units
    await tester.pumpWidget(
      MaterialApp(
        home: Scaffold(
          body: GrowthChartRenderer(
            measurements: testMeasurements,
            measurementType: 'weight',
            gender: 'male',
            isMetric: false,
            dateRange: '1 year',
            birthDate: DateTime.now().subtract(const Duration(days: 365)),
          ),
        ),
      ),
    );
  });

  testWidgets('GrowthChartRenderer supports different date ranges', (WidgetTester tester) async {
    final dateRanges = ['6 months', '1 year', '2 years', '3 years', '4 years', '5 years'];
    
    for (final range in dateRanges) {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: GrowthChartRenderer(
              measurements: testMeasurements,
              measurementType: 'weight',
              gender: 'male',
              isMetric: true,
              dateRange: range,
              birthDate: DateTime.now().subtract(const Duration(days: 365)),
            ),
          ),
        ),
      );
      
      expect(find.textContaining(range), findsOneWidget);
    }
  });
}