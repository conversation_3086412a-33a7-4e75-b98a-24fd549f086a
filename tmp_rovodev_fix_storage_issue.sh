#!/bin/bash

echo "🧹 Cleaning Flutter build cache..."
flutter clean

echo "📦 Getting dependencies..."
flutter pub get

echo "🔧 Cleaning Gradle cache..."
cd android
./gradlew clean
cd ..

echo "📱 Uninstalling old app from emulator..."
adb -s emulator-5554 uninstall com.babytracker_pro.app 2>/dev/null || echo "App not installed or emulator not running"

echo "🚀 Building and installing optimized debug APK..."
flutter run --dart-define-from-file=env.json -d emulator-5554 --debug

echo "✅ Done! If this still fails, try increasing emulator storage in Android Studio."