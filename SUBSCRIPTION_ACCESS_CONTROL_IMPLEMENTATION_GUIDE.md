# Subscription Access Control Implementation Guide

## Overview

This guide shows how to properly implement subscription-based access control throughout your BabyTracker Pro app using the updated subscription controller and access control utilities.

## Features by Plan

### Free Plan
- ✅ Basic activity tracking
- ✅ Basic scheduled activities  
- ✅ Up to 1 baby profile
- ❌ No family sharing
- ❌ No WHO Growth Charts
- ❌ No AI insights
- ❌ No Ask AI chat
- ❌ No data export
- ❌ No priority support
- ❌ No custom notifications

### Premium Plan
- ✅ Unlimited activity tracking
- ✅ Unlimited scheduled activities
- ✅ Unlimited baby profiles
- ✅ Family sharing with up to 10 members
- ✅ WHO Growth Charts
- ✅ AI insights
- ✅ Ask AI chat
- ✅ Data export
- ✅ Priority customer support
- ✅ Custom notifications

## Implementation Examples

### 1. Screen-Level Access Control

```dart
// In your screen widget
class AIInsightsDashboard extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // Check access before building the screen
    if (!SubscriptionAccessControl.canAccessScreen(context, 'ai_insights')) {
      return Scaffold(
        appBar: AppBar(title: Text('AI Insights')),
        body: SubscriptionAccessControl.buildUpgradeBanner(
          context,
          'ai_insights',
          customMessage: 'AI Insights require Premium plan. Get personalized insights about your baby\'s development patterns.',
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(title: Text('AI Insights')),
      body: _buildAIInsightsContent(),
    );
  }
}
```

### 2. Feature Gating with SubscriptionGate Widget

```dart
// Wrap premium features with SubscriptionGate
class HomeScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Always visible content
          BasicActivityTracker(),
          
          // Premium-only AI Insights
          SubscriptionGate(
            featureName: 'ai_insights',
            child: AIInsightsCard(),
            // Shows upgrade banner if no access
          ),
          
          // Premium-only Growth Charts
          SubscriptionGate(
            featureName: 'who_growth_charts',
            child: GrowthChartsCard(),
          ),
          
          // Premium-only Family Sharing
          SubscriptionGate(
            featureName: 'family_sharing',
            child: FamilySharingCard(),
          ),
        ],
      ),
    );
  }
}
```

### 3. Navigation Access Control

```dart
// In your navigation/routing logic
class AppRouter {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case '/ai-insights':
        return MaterialPageRoute(
          builder: (context) {
            if (!SubscriptionAccessControl.canAccessScreen(context, 'ai_insights')) {
              return UpgradeRequiredScreen(
                featureName: 'ai_insights',
                title: 'AI Insights',
              );
            }
            return AIInsightsDashboard();
          },
        );
        
      case '/growth-charts':
        return MaterialPageRoute(
          builder: (context) {
            if (!SubscriptionAccessControl.canAccessScreen(context, 'growth_charts')) {
              return UpgradeRequiredScreen(
                featureName: 'who_growth_charts',
                title: 'Growth Charts',
              );
            }
            return GrowthChartsScreen();
          },
        );
        
      default:
        return MaterialPageRoute(builder: (_) => NotFoundScreen());
    }
  }
}
```

### 4. Button/Action Access Control

```dart
class ActivityLogScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Activity Log'),
        actions: [
          // Data export button - premium only
          if (SubscriptionAccessControl.hasFeatureAccess(context, 'data_export'))
            IconButton(
              icon: Icon(Icons.download),
              onPressed: () => _exportData(context),
              tooltip: 'Export Data',
            )
          else
            IconButton(
              icon: Icon(Icons.download),
              onPressed: () => SubscriptionAccessControl.showUpgradePrompt(
                context,
                'data_export',
                customTitle: 'Export Your Data',
              ),
              tooltip: 'Export Data (Premium)',
            ),
        ],
      ),
      body: _buildActivityList(),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _addActivity(context),
        child: Icon(Icons.add),
      ),
    );
  }
}
```

### 5. Settings Screen Integration

```dart
class SettingsScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Settings')),
      body: ListView(
        children: [
          // Subscription status
          SubscriptionStatusWidget(
            showUpgradeButton: true,
          ),
          
          Divider(),
          
          // Family sharing settings - premium only
          SubscriptionGate(
            featureName: 'family_sharing',
            child: ListTile(
              leading: Icon(Icons.family_restroom),
              title: Text('Family Sharing'),
              subtitle: Text('Manage family members'),
              onTap: () => Navigator.pushNamed(context, '/family-sharing'),
            ),
          ),
          
          // Notification settings - premium only
          SubscriptionGate(
            featureName: 'custom_notifications',
            child: ListTile(
              leading: Icon(Icons.notifications),
              title: Text('Custom Notifications'),
              subtitle: Text('Set personalized reminders'),
              onTap: () => Navigator.pushNamed(context, '/notifications'),
            ),
          ),
          
          // Data export - premium only
          SubscriptionGate(
            featureName: 'data_export',
            child: ListTile(
              leading: Icon(Icons.download),
              title: Text('Export Data'),
              subtitle: Text('Download your data'),
              onTap: () => _showExportOptions(context),
            ),
          ),
        ],
      ),
    );
  }
}
```

### 6. Baby Profile Limit Enforcement

```dart
class AddBabyProfileScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('Add Baby Profile')),
      body: FutureBuilder<int>(
        future: _getCurrentProfileCount(),
        builder: (context, snapshot) {
          if (!snapshot.hasData) return CircularProgressIndicator();
          
          final currentCount = snapshot.data!;
          final limit = SubscriptionAccessControl.getFeatureLimit(context, 'baby_profiles');
          
          // Check if user can add more profiles
          if (limit != null && currentCount >= limit) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.baby_changing_station, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'Profile Limit Reached',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Free plan is limited to $limit baby profile.',
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () => SubscriptionAccessControl.showUpgradePrompt(
                      context,
                      'unlimited_profiles',
                      customTitle: 'Upgrade for Unlimited Profiles',
                    ),
                    child: Text('Upgrade to Premium'),
                  ),
                ],
              ),
            );
          }
          
          return _buildAddProfileForm();
        },
      ),
    );
  }
}
```

### 7. AI Chat Access Control

```dart
class AIChatScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    if (!SubscriptionAccessControl.hasFeatureAccess(context, 'ai_chat')) {
      return Scaffold(
        appBar: AppBar(title: Text('Ask AI')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.chat, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'AI Chat Assistant',
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              SizedBox(height: 8),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 32),
                child: Text(
                  'Get 24/7 AI assistance for parenting questions. Upgrade to Premium to unlock unlimited AI chat.',
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => SubscriptionAccessControl.showUpgradePrompt(
                  context,
                  'ai_chat',
                  customTitle: '24/7 AI Assistant',
                ),
                child: Text('Upgrade to Premium'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(title: Text('Ask AI')),
      body: _buildChatInterface(),
    );
  }
}
```

### 8. Provider Setup in main.dart

```dart
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Subscription Controller
        ChangeNotifierProvider(
          create: (_) {
            final controller = SubscriptionController();
            controller.initialize(); // Load current subscription
            return controller;
          },
        ),
        
        // Feature Access Service
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<SubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
              previous ?? FeatureAccessService(subscription),
        ),
        
        // Other providers...
      ],
      child: MaterialApp(
        title: 'BabyTracker Pro',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: HomeScreen(),
        onGenerateRoute: AppRouter.generateRoute,
      ),
    );
  }
}
```

## Key Methods Reference

### SubscriptionController Methods
- `hasFeature(String featureName)` - Check if user has access to a feature
- `canAccessScreen(String screenName)` - Check if user can access a screen
- `getFeatureLimit(String featureName)` - Get usage limit for a feature
- `getRestrictionMessage(String featureName)` - Get user-friendly restriction message
- `hasPremiumAccess` - Check if user has premium access
- `isOnFreePlan` - Check if user is on free plan

### SubscriptionAccessControl Utility Methods
- `hasFeatureAccess(context, featureName)` - Check feature access
- `canAccessScreen(context, screenName)` - Check screen access
- `showUpgradePrompt(context, featureName)` - Show upgrade dialog
- `buildUpgradeBanner(context, featureName)` - Build upgrade banner widget
- `isPremiumUser(context)` - Check if user is premium
- `getCurrentPlanName(context)` - Get current plan name

### Feature Names Reference
- `'ai_insights'` - AI Insights dashboard
- `'ai_chat'` - Ask AI chat assistant
- `'who_growth_charts'` - WHO Growth Charts
- `'family_sharing'` - Family member management
- `'unlimited_profiles'` - Multiple baby profiles
- `'data_export'` - Data export functionality
- `'custom_notifications'` - Custom notifications
- `'premium_support'` - Priority customer support
- `'basic_activity_tracking'` - Basic activity logging (always available)

## Testing Your Implementation

Use the `test_comprehensive_subscription_integration.dart` file to test your subscription access control implementation. It provides comprehensive testing of all subscription features and access controls.

## Best Practices

1. **Fail Closed**: Always deny access if there's an error checking permissions
2. **Clear Messaging**: Provide clear, helpful messages about why features are restricted
3. **Easy Upgrade Path**: Always provide a clear path to upgrade when showing restrictions
4. **Consistent UX**: Use the same patterns for access control throughout your app
5. **Performance**: Cache subscription status and avoid repeated database calls
6. **Error Handling**: Handle subscription service errors gracefully
7. **Testing**: Test both free and premium user flows thoroughly

## Database Integration

The subscription controller automatically syncs with your Supabase `user_subscriptions` table. Make sure your database schema matches the expected structure:

```sql
-- user_subscriptions table structure
CREATE TABLE user_subscriptions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  plan_id TEXT NOT NULL,
  plan_name TEXT NOT NULL,
  status TEXT NOT NULL,
  monthly_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
  renewal_date TIMESTAMP WITH TIME ZONE,
  is_trial_active BOOLEAN DEFAULT FALSE,
  trial_ends_at TIMESTAMP WITH TIME ZONE,
  payment_method JSONB,
  max_family_members INTEGER DEFAULT 1,
  includes_ai_insights BOOLEAN DEFAULT FALSE,
  includes_data_export BOOLEAN DEFAULT FALSE,
  includes_premium_support BOOLEAN DEFAULT FALSE,
  storage_limit INTEGER,
  features JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

This implementation provides comprehensive subscription access control that matches the features listed in your subscription screen and properly restricts access based on the user's plan.