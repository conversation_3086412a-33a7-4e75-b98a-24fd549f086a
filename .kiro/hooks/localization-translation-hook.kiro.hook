{"enabled": true, "name": "Localization Translation Monitor", "description": "Monitors changes to user-facing text content files (JSON, YAML, and other localization files) and automatically generates translations for all configured target languages while maintaining proper context and locale-specific conventions.", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.json", "**/*.yaml", "**/*.yml", "**/strings.xml", "**/Localizable.strings", "**/localization/**", "**/i18n/**", "**/translations/**", "**/locale/**"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes to identify new or modified text content that requires translation. For each piece of text that needs translation:\n\n1. Extract the new/modified text strings and their context\n2. Identify the source language (likely English based on the project structure)\n3. Generate translations for common target languages (Spanish, French, German, Italian, Portuguese, Japanese, Chinese Simplified, Korean)\n4. Ensure translations maintain proper context and meaning\n5. Apply locale-specific conventions (date formats, number formats, cultural adaptations)\n6. Preserve any formatting, placeholders, or special characters\n7. Generate the translated content in the same file structure/format as the source\n\nPlease provide the translations in a structured format that can be easily integrated into the localization system, maintaining the original keys/identifiers while providing translated values."}}