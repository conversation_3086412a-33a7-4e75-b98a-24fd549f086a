{"enabled": true, "name": "Test Coverage Generator", "description": "Monitors source file changes to identify new/modified functions, check test coverage, generate missing tests, run verification, and update coverage reports", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart"]}, "then": {"type": "askAgent", "prompt": "A source file has been modified. Please:\n\n1. Analyze the changed file to identify new or modified functions and methods\n2. Check if corresponding test files exist in the test/ directory for the modified code\n3. Examine existing tests to determine if they cover the new/modified functionality\n4. If coverage is missing or insufficient:\n   - Generate comprehensive test cases for the new/modified code\n   - Create new test files if they don't exist\n   - Add test cases to existing test files if appropriate\n5. Run the generated tests to verify they pass\n6. Update any coverage reports or documentation as needed\n\nFocus on:\n- Unit tests for individual functions/methods\n- Widget tests for UI components\n- Integration tests where appropriate\n- Edge cases and error conditions\n- Mocking external dependencies appropriately\n\nEnsure tests follow Flutter/Dart testing best practices and maintain consistency with existing test patterns in the codebase."}}