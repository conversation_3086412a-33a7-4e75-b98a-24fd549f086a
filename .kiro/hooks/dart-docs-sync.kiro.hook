{"enabled": true, "name": "Dart Documentation Sync", "description": "Monitors all Dart source files and related configuration files for changes, then triggers documentation updates in README.md and any docs folder", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart", "test/**/*.dart", "pubspec.yaml", "analysis_options.yaml", "android/app/build.gradle", "ios/Runner/Info.plist"]}, "then": {"type": "askAgent", "prompt": "Source files have been modified in this Flutter/Dart project. Please analyze the changes and update the documentation accordingly. Focus on:\n\n1. Update README.md with any new features, API changes, or setup instructions\n2. If there's a /docs folder, update relevant documentation there\n3. Document any new dependencies added to pubspec.yaml\n4. Update any architectural changes reflected in the lib/ structure\n5. Document new test coverage or testing approaches\n6. Update any platform-specific configuration changes (Android/iOS)\n\nPlease ensure the documentation is clear, comprehensive, and reflects the current state of the codebase."}}