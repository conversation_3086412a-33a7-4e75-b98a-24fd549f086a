{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving readability, maintainability, and performance while preserving functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart", "android/**/*.kt", "android/**/*.java", "ios/**/*.swift", "ios/**/*.m", "ios/**/*.h", "test/**/*.dart"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify long methods, large classes, duplicate code, complex conditionals, and other anti-patterns\n2. **Design Patterns**: Suggest appropriate design patterns that could improve code structure\n3. **Best Practices**: Check for proper error handling, null safety, naming conventions, and documentation\n4. **Performance**: Look for potential performance optimizations like unnecessary rebuilds, inefficient algorithms, or memory leaks\n5. **Readability**: Suggest improvements for code clarity, proper commenting, and logical organization\n6. **Maintainability**: Identify areas that could be refactored for easier future maintenance\n\nFor each issue found, provide:\n- Clear description of the problem\n- Specific location (file and line if possible)\n- Concrete suggestion for improvement\n- Brief explanation of why the change would be beneficial\n\nMaintain the existing functionality while suggesting improvements. Prioritize suggestions that have the highest impact on code quality."}}