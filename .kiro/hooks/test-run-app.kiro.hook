{"enabled": true, "name": "Test Run App", "description": "Automatically runs the Flutter app to check for errors whenever code files are modified, ensuring no runtime errors before creating summaries", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart", "pubspec.yaml", "android/**/*.gradle", "android/**/*.kt", "ios/**/*.swift", "ios/**/*.plist"]}, "then": {"type": "askAgent", "prompt": "Code files have been modified. Please run `flutter run --debug` or `flutter run --release` to test the app and verify there are no compilation or runtime errors. Once the app runs successfully without errors, provide a summary of the changes made. If there are any errors, fix them before proceeding with the summary."}}