{"enabled": true, "name": "Security Vulnerability Scanner", "description": "Automatically reviews changed files for potential security issues including API keys, credentials, private keys, authentication tokens, passwords, and other sensitive data. Provides security recommendations and best practices.", "version": "1", "when": {"type": "fileEdited", "patterns": ["lib/**/*.dart", "android/**/*.gradle", "android/**/*.properties", "android/**/*.xml", "android/**/*.kt", "android/**/*.java", "ios/**/*.swift", "ios/**/*.m", "ios/**/*.h", "ios/**/*.plist", "ios/**/*.xcconfig", "*.yaml", "*.yml", "*.json", "*.sql", "*.md", "*.js", "*.py", "*.env", "supabase/**/*.sql"]}, "then": {"type": "askAgent", "prompt": "Review the changed files for potential security issues:\n\n1. **Credential Detection**: Look for API keys, tokens, or credentials in source code\n2. **Private Key Scanning**: Check for private keys or sensitive credentials  \n3. **Encryption Analysis**: Scan for encryption keys or certificates\n4. **Authentication Review**: Identify authentication tokens or session IDs\n5. **Configuration Security**: Flag passwords or secrets in configuration files\n6. **Data Exposure**: Detect IP addresses containing sensitive data\n7. **URL Security**: Find hardcoded internal URLs\n8. **Database Security**: Spot database connection credentials\n\nFor each security issue found:\n- **Highlight the specific security risk** and explain why it's dangerous\n- **Suggest a secure alternative approach** (environment variables, secure storage, etc.)\n- **Recommend security best practices** for the specific type of vulnerability\n- **Provide code examples** of secure implementations when applicable\n\nFocus on practical, actionable security improvements that can be implemented immediately."}}