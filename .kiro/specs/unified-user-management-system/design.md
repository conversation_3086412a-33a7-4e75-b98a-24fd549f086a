Design.md:# Design Document

## Overview

The Account & Profile section redesign focuses on creating a professional, logical, and user-friendly interface that clearly presents user information, family sharing capabilities, and account management features. The design follows the app's "Nurturing Minimalism" philosophy with "Gentle Authority" visual hierarchy, ensuring information is easily scannable and actions are clearly accessible.

## Architecture

### Component Structure
```
AccountProfileSection
├── ProfileHeaderCard
│   ├── UserAvatarWidget
│   ├── UserInfoDisplay
│   └── ProfileCompletionIndicator
├── FamilySharingCard
│   ├── FamilyMembersList
│   ├── FamilyStatsWidget
│   └── InviteMemberButton
├── AccountManagementCard
│   ├── SubscriptionStatusWidget
│   ├── SecuritySettingsWidget
│   └── AccountActionsWidget
└── QuickActionsRow
    ├── EditProfileButton
    ├── ManageUsersButton
    └── SettingsButton
```

### State Management
- Uses Provider pattern for reactive updates
- Centralized state in `AccountProfileController`
- Real-time family member updates via Supabase subscriptions
- Optimistic UI updates for better user experience

## Components and Interfaces

### 1. ProfileHeaderCard
**Purpose**: Display user profile information with professional visual hierarchy

**Key Features**:
- Large, prominent user avatar (photo or initial-based)
- Clear display of full name, email, and role
- Profile completion progress indicator
- Account creation date and last activity
- Role badge with consistent color coding

**Visual Design**:
- Card elevation: 2dp for subtle depth
- Corner radius: 16px for modern feel
- Padding: 20px for comfortable spacing
- Avatar size: 80x80px for prominence
- Role badge: Rounded pill with role-specific colors

**Interface**:
```dart
class ProfileHeaderCard extends StatelessWidget {
  final UserProfile? userProfile;
  final double completionPercentage;
  final VoidCallback onEditProfile;
  final bool isLoading;
}
```

### 2. FamilySharingCard
**Purpose**: Manage family members and sharing settings

**Key Features**:
- Family member count and active status
- List of family members with roles and last activity
- Invite new member functionality
- Family sharing permissions overview
- Pending invitations display

**Visual Design**:
- Horizontal member avatars for quick overview
- Member count badge with green accent
- "Invite Member" button with primary color
- Expandable member list with detailed info
- Status indicators (active, pending, offline)

**Interface**:
```dart
class FamilySharingCard extends StatelessWidget {
  final List<FamilyMember> familyMembers;
  final int pendingInvitations;
  final VoidCallback onInviteMember;
  final Function(FamilyMember) onMemberTap;
  final bool canManageFamily;
}
```

### 3. AccountManagementCard
**Purpose**: Display subscription status and account settings

**Key Features**:
- Subscription plan display with features
- Renewal date and billing information
- Security settings overview
- Account preferences
- Upgrade/downgrade options

**Visual Design**:
- Subscription badge with premium styling
- Feature list with checkmarks
- Security status indicators
- Clear upgrade call-to-action
- Organized settings groups

**Interface**:
```dart
class AccountManagementCard extends StatelessWidget {
  final SubscriptionInfo? subscription;
  final SecuritySettings securitySettings;
  final VoidCallback onManageSubscription;
  final VoidCallback onSecuritySettings;
}
```

### 4. UserAvatarWidget
**Purpose**: Professional avatar display with fallback options

**Key Features**:
- Photo upload and management
- Initial-based fallback avatar
- Role-based border colors
- Loading states and error handling
- Accessibility support

**Visual Design**:
- Circular avatar with subtle border
- Role-specific accent colors
- Upload overlay on hover/tap
- Smooth transitions between states
- Proper contrast ratios

**Interface**:
```dart
class UserAvatarWidget extends StatelessWidget {
  final String? imageUrl;
  final String initials;
  final String role;
  final double size;
  final VoidCallback? onTap;
  final bool isEditable;
}
```

## Data Models

### Enhanced UserProfile Model
```dart
class UserProfile {
  final String id;
  final String email;
  final String fullName;
  final String? avatarUrl;
  final String role;
  final DateTime createdAt;
  final DateTime? lastSignInAt;
  final int signInCount;
  final ProfileCompletionStatus completionStatus;
  final Map<String, bool> permissions;
  
  // New fields for enhanced profile
  final String? phoneNumber;
  final String? timezone;
  final Map<String, dynamic> preferences;
  final bool isEmailVerified;
  final bool isTwoFactorEnabled;
}
```

### FamilyMember Model
```dart
class FamilyMember {
  final String id;
  final String fullName;
  final String email;
  final String role;
  final String? avatarUrl;
  final DateTime joinedAt;
  final DateTime? lastActiveAt;
  final FamilyMemberStatus status;
  final Map<String, bool> permissions;
  final String? invitedBy;
}
```

### SubscriptionInfo Model
```dart
class SubscriptionInfo {
  final String planId;
  final String planName;
  final SubscriptionStatus status;
  final DateTime? renewalDate;
  final double monthlyPrice;
  final List<String> features;
  final bool isTrialActive;
  final DateTime? trialEndsAt;
  final PaymentMethod? paymentMethod;
}
```

### ProfileCompletionStatus Model
```dart
class ProfileCompletionStatus {
  final double percentage;
  final List<String> completedSteps;
  final List<String> remainingSteps;
  final String nextRecommendedAction;
}
```

## Error Handling

### Network Error Handling
- Graceful degradation when offline
- Retry mechanisms for failed requests
- Clear error messages with recovery options
- Cached data display during network issues

### Validation Error Handling
- Real-time form validation
- Clear field-level error messages
- Prevention of invalid state submissions
- User-friendly error explanations

### Permission Error Handling
- Role-based feature access control
- Clear messaging for insufficient permissions
- Graceful hiding of unauthorized features
- Upgrade prompts for premium features

### Data Loading Error Handling
- Skeleton screens during loading
- Error states with retry options
- Fallback data when available
- Progressive loading for large datasets

## Testing Strategy

### Unit Tests
- UserProfile model serialization/deserialization
- Permission calculation logic
- Profile completion percentage calculation
- Role-based access control functions

### Widget Tests
- ProfileHeaderCard rendering with different user states
- FamilySharingCard member list display
- UserAvatarWidget fallback behavior
- Form validation and error display

### Integration Tests
- Complete profile editing flow
- Family member invitation process
- Subscription management workflow
- Theme switching and accessibility

### Accessibility Tests
- Screen reader compatibility
- Keyboard navigation support
- Color contrast validation
- Touch target size verification

## Performance Considerations

### Optimization Strategies
- Image caching for user avatars
- Lazy loading of family member details
- Debounced search and filtering
- Efficient state management with Provider

### Memory Management
- Proper disposal of controllers and streams
- Image memory optimization
- Subscription cleanup on widget disposal
- Efficient list rendering for large families

### Network Optimization
- Request batching for related data
- Optimistic UI updates
- Background sync for family updates
- Compressed image uploads

## Accessibility Features

### Screen Reader Support
- Semantic labels for all interactive elements
- Proper heading hierarchy
- Descriptive button labels
- Status announcements for state changes

### Visual Accessibility
- High contrast mode support
- Scalable text and UI elements
- Color-blind friendly design
- Focus indicators for keyboard navigation

### Motor Accessibility
- Minimum 44px touch targets
- Gesture alternatives for complex interactions
- Voice control compatibility
- Reduced motion options

## Security Considerations

### Data Protection
- Encrypted storage of sensitive information
- Secure transmission of profile data
- Role-based access control enforcement
- Audit logging for sensitive operations

### Privacy Features
- Granular privacy settings
- Family member permission management
- Data export and deletion options
- Clear privacy policy integration

### Authentication Security
- Two-factor authentication support
- Session management and timeout
- Secure password requirements
- Account lockout protection
