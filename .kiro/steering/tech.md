# Technology Stack

## Framework & Language
- **Flutter**: 3.29.2+ (cross-platform mobile development)
- **Dart**: 3.6.0+ (programming language)
- **Material 3**: Design system with custom "Gentle Authority" theme

## Backend & Database
- **Supabase**: PostgreSQL database with real-time subscriptions
- **Row Level Security (RLS)**: Data privacy and multi-tenant support
- **Realtime**: Live data synchronization across devices
- **Supabase Auth**: User authentication and session management

## AI & Analytics
- **OpenAI GPT-4**: Chat assistant and insights generation
- **Custom AI Analysis Service**: Pattern recognition and recommendations
- **Smart Caching**: Rate limiting with user activity tracking

## Key Dependencies
```yaml
# Core Flutter packages
flutter: sdk: flutter
sizer: ^2.0.15              # Responsive design
provider: ^6.1.1            # State management

# Backend & Data
supabase_flutter: ^2.3.3    # Database & auth
realtime_client: ^2.0.1     # Live updates

# UI & Visualization
google_fonts: ^6.1.0        # Typography (Inter + JetBrains Mono)
fl_chart: ^1.0.0            # Data visualization
flutter_svg: ^2.0.9         # Vector graphics
cached_network_image: ^3.3.1 # Image caching

# Networking & Storage
dio: ^5.7.0                 # HTTP client
shared_preferences: ^2.2.2   # Local storage
connectivity_plus: ^5.0.2    # Network status
```

## Architecture Patterns
- **Provider Pattern**: State management with reactive UI updates
- **Service Layer**: Centralized business logic (SupabaseService, OpenAIService, etc.)
- **Template Method Pattern**: Consistent activity widget architecture
- **Repository Pattern**: Data access abstraction

## Environment Configuration
Create `env.json` in project root:
```json
{
  "SUPABASE_URL": "your-supabase-project-url",
  "SUPABASE_ANON_KEY": "your-supabase-anon-key", 
  "OPENAI_API_KEY": "your-openai-api-key"
}
```

## Common Commands

### Development
```bash
# Install dependencies
flutter pub get

# Run with environment variables
flutter run --dart-define-from-file=env.json

# Hot reload (automatic during development)
# Hot restart: Cmd+Shift+\ (VS Code) or r in terminal
```

### Database
```bash
# Apply migrations
supabase db push

# Reset database (development only)
supabase db reset

# Generate types
supabase gen types dart --project-id YOUR_PROJECT_ID > lib/types/supabase.dart
```

### Building
```bash
# Android APK
flutter build apk --release --dart-define-from-file=env.json

# Android App Bundle (for Play Store)
flutter build appbundle --release --dart-define-from-file=env.json

# iOS (requires macOS and Xcode)
flutter build ios --release --dart-define-from-file=env.json

# Web
flutter build web --release --dart-define-from-file=env.json
```

### Testing
```bash
# Run all tests
flutter test

# Run specific test file
flutter test test/widget_test.dart

# Run with coverage
flutter test --coverage
```

### Code Quality
```bash
# Analyze code
flutter analyze

# Format code
dart format .

# Fix common issues
dart fix --apply
```

## Performance Considerations
- Use `Sizer` package for responsive layouts (avoid hardcoded dimensions)
- Implement lazy loading for large data sets
- Cache images and API responses appropriately
- Optimize widget rebuilds with `Provider.of<T>(context, listen: false)`
- Use `const` constructors where possible