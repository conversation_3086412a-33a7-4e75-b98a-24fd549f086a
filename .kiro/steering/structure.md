# Project Structure

## Root Directory Layout
```
babytracker_pro/
├── lib/                    # Main application code
├── assets/                 # Static resources (images, icons)
├── supabase/              # Database migrations and config
├── android/               # Android-specific configuration
├── ios/                   # iOS-specific configuration
├── web/                   # Web-specific configuration
├── test/                  # Unit and widget tests
├── pubspec.yaml           # Dependencies and project config
├── env.json              # Environment variables (not in git)
└── analysis_options.yaml # Dart analyzer configuration
```

## Core Application Structure (`lib/`)

### Entry Point
- `main.dart` - Application entry point with theme and service initialization

### Core Architecture
```
lib/
├── core/
│   └── app_export.dart    # Central exports for commonly used classes
├── models/                # Data models and entities
├── services/              # Business logic and external integrations
├── presentation/          # UI screens and widgets
├── theme/                 # App theming and styling
├── routes/                # Navigation configuration
├── utils/                 # Helper utilities
└── widgets/               # Reusable UI components
```

## Models (`lib/models/`)
Data classes representing core entities:
- `activity_log.dart` - Activity tracking data model
- `baby_profile.dart` - Baby profile information
- `ai_insight.dart` - AI-generated insights
- `user_profile.dart` - User account data
- `milestone.dart` - Developmental milestones

## Services (`lib/services/`)
Business logic and external service integrations:
- `supabase_service.dart` - Database operations and real-time subscriptions
- `auth_service.dart` - User authentication
- `openai_service.dart` - AI chat and insights
- `ai_analysis_service.dart` - Pattern analysis and recommendations
- `theme_service.dart` - Theme management
- `settings_service.dart` - App settings persistence

## Presentation Layer (`lib/presentation/`)
Feature-based screen organization:
```
presentation/
├── auth/                  # Authentication screens
├── home/                  # Main dashboard
├── activity_timeline/     # Activity history view
├── ai_chat/              # AI chat assistant
├── ai_insights/          # AI insights dashboard
├── feeding_tracker/      # Feeding management
├── sleep_tracker/        # Sleep monitoring
├── growth_charts/        # WHO growth percentiles
├── milestones/           # Milestone tracking
├── settings/             # App configuration
└── [feature]/widgets/    # Feature-specific widgets
```

## Shared Widgets (`lib/widgets/`)
Reusable UI components:
- `activity_log_item.dart` - Activity display component
- `custom_error_widget.dart` - Error handling UI
- `modern_date_time_picker.dart` - Date/time selection
- `shared/` - Cross-feature shared widgets

## Theme System (`lib/theme/`)
- `app_theme.dart` - Complete Material 3 theme with "Gentle Authority" colors
- `theme_aware_colors.dart` - Dynamic color utilities
- `ui_improvements.dart` - UI enhancement utilities

## Database Structure (`supabase/`)
```
supabase/
├── migrations/           # Database schema changes (chronological)
└── .temp/               # Supabase CLI temporary files
```

## Naming Conventions

### Files and Directories
- Use `snake_case` for file and directory names
- Feature directories should be descriptive (`feeding_tracker` not `feeding`)
- Widget files should end with `_widget.dart` if they're reusable components
- Screen files should end with `_screen.dart`

### Classes and Variables
- Use `PascalCase` for class names
- Use `camelCase` for variables and methods
- Use `SCREAMING_SNAKE_CASE` for constants
- Prefix private members with underscore `_`

### Database Tables
- Use `snake_case` for table and column names
- Use plural names for tables (`activity_logs`, `baby_profiles`)
- Include `created_at` and `updated_at` timestamps
- Use UUIDs for primary keys

## Code Organization Principles

### Feature-First Structure
- Group related functionality together
- Each feature should have its own directory under `presentation/`
- Feature-specific widgets go in `[feature]/widgets/`

### Separation of Concerns
- Models contain only data and serialization logic
- Services handle business logic and external APIs
- Presentation layer focuses on UI and user interaction
- Widgets are pure UI components

### Dependency Flow
```
Presentation Layer (UI)
       ↓
Services Layer (Business Logic)
       ↓
Models Layer (Data)
```

### Import Organization
1. Dart/Flutter imports first
2. Third-party package imports
3. Local imports (relative paths)
4. Separate groups with blank lines

Example:
```dart
import 'package:flutter/material.dart';

import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import '../core/app_export.dart';
import '../services/supabase_service.dart';
```

## Testing Structure (`test/`)
Mirror the `lib/` structure:
```
test/
├── models/               # Model unit tests
├── services/             # Service unit tests
├── widgets/              # Widget tests
├── utils/                # Utility tests
└── widget_test.dart      # Main widget test
```

## Asset Organization (`assets/`)
```
assets/
├── images/               # App images and illustrations
│   ├── img_app_logo.svg # App logo
│   └── no-image.jpg     # Placeholder images
└── [future folders]     # Icons, fonts, etc.
```