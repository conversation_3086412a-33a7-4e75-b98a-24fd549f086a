import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:sizer/sizer.dart';

import 'lib/core/app_export.dart';
import 'lib/presentation/settings/settings.dart';
import 'lib/presentation/user_profile_edit/user_profile_edit_screen.dart';
import 'lib/presentation/babies_management/babies_management_screen.dart';
import 'lib/services/theme_service.dart';
import 'lib/theme/app_theme.dart';

void main() {
  group('Settings and Profile Management Theme Tests', () {
    late ThemeService themeService;

    setUp(() {
      themeService = ThemeService();
    });

    testWidgets('Settings screen uses theme-aware colors in light mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return ChangeNotifierProvider<ThemeService>.value(
              value: themeService,
              child: MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: ThemeMode.light,
                home: Settings(),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Verify app bar uses theme colors
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.backgroundColor, equals(AppTheme.lightTheme.scaffoldBackgroundColor));

      // Verify scaffold background uses theme color
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(AppTheme.lightTheme.scaffoldBackgroundColor));

      // Verify cards use theme colors
      final cards = tester.widgetList<Card>(find.byType(Card));
      for (final card in cards) {
        expect(card.color, isNull); // Should use theme default
      }

      print('✅ Settings screen light theme test passed');
    });

    testWidgets('Settings screen uses theme-aware colors in dark mode', (WidgetTester tester) async {
      await themeService.setThemeMode(ThemeMode.dark);
      
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return ChangeNotifierProvider<ThemeService>.value(
              value: themeService,
              child: MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: ThemeMode.dark,
                home: Settings(),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Verify app bar uses dark theme colors
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.backgroundColor, equals(AppTheme.darkTheme.scaffoldBackgroundColor));

      // Verify scaffold background uses dark theme color
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(AppTheme.darkTheme.scaffoldBackgroundColor));

      print('✅ Settings screen dark theme test passed');
    });

    testWidgets('Theme dialog shows proper theme-aware styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return ChangeNotifierProvider<ThemeService>.value(
              value: themeService,
              child: MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: ThemeMode.light,
                home: Settings(),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Find and tap the theme tile to open dialog
      final themeTile = find.text('Theme');
      expect(themeTile, findsOneWidget);
      
      await tester.tap(themeTile);
      await tester.pumpAndSettle();

      // Verify dialog appears with theme-aware styling
      expect(find.byType(AlertDialog), findsOneWidget);
      expect(find.text('Select Theme'), findsOneWidget);
      expect(find.text('Light'), findsOneWidget);
      expect(find.text('Dark'), findsOneWidget);
      expect(find.text('System'), findsOneWidget);

      // Verify radio buttons are present
      expect(find.byType(RadioListTile<String>), findsNWidgets(3));

      print('✅ Theme dialog styling test passed');
    });

    testWidgets('Switch components use theme-aware colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return ChangeNotifierProvider<ThemeService>.value(
              value: themeService,
              child: MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: ThemeMode.light,
                home: Settings(),
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Find switch components
      final switches = tester.widgetList<Switch>(find.byType(Switch));
      
      // Verify switches have theme-aware colors
      for (final switchWidget in switches) {
        expect(switchWidget.activeColor, equals(AppTheme.lightTheme.colorScheme.primary));
        expect(switchWidget.activeTrackColor, isNotNull);
        expect(switchWidget.inactiveThumbColor, isNotNull);
        expect(switchWidget.inactiveTrackColor, isNotNull);
      }

      print('✅ Switch theme colors test passed');
    });

    testWidgets('User Profile Edit screen uses theme-aware colors', (WidgetTester tester) async {
      // Create a mock user profile
      final mockUserProfile = UserProfile(
        id: 'test-id',
        fullName: 'Test User',
        email: '<EMAIL>',
        role: 'parent',
        signInCount: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: ThemeMode.light,
              home: UserProfileEditScreen(userProfile: mockUserProfile),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Verify app bar uses theme colors
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.backgroundColor, equals(AppTheme.lightTheme.scaffoldBackgroundColor));

      // Verify text fields use theme-aware styling
      final textFields = tester.widgetList<TextFormField>(find.byType(TextFormField));
      for (final textField in textFields) {
        final decoration = textField.decoration as InputDecoration;
        expect(decoration.filled, isTrue);
        expect(decoration.fillColor, equals(AppTheme.lightTheme.colorScheme.surface));
      }

      print('✅ User Profile Edit screen theme test passed');
    });

    testWidgets('Babies Management screen uses theme-aware colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return MaterialApp(
              theme: AppTheme.lightTheme,
              darkTheme: AppTheme.darkTheme,
              themeMode: ThemeMode.light,
              home: BabiesManagementScreen(),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Verify app bar uses theme colors
      final appBar = tester.widget<AppBar>(find.byType(AppBar));
      expect(appBar.backgroundColor, equals(AppTheme.lightTheme.scaffoldBackgroundColor));

      // Verify scaffold background uses theme color
      final scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(AppTheme.lightTheme.scaffoldBackgroundColor));

      // Verify floating action button uses theme colors
      final fab = tester.widget<FloatingActionButton>(find.byType(FloatingActionButton));
      expect(fab.backgroundColor, equals(AppTheme.lightTheme.colorScheme.primary));

      print('✅ Babies Management screen theme test passed');
    });

    testWidgets('Theme switching provides immediate visual feedback', (WidgetTester tester) async {
      await tester.pumpWidget(
        Sizer(
          builder: (context, orientation, deviceType) {
            return ChangeNotifierProvider<ThemeService>.value(
              value: themeService,
              child: Consumer<ThemeService>(
                builder: (context, themeService, child) {
                  return MaterialApp(
                    theme: AppTheme.lightTheme,
                    darkTheme: AppTheme.darkTheme,
                    themeMode: themeService.themeMode,
                    home: Settings(),
                  );
                },
              ),
            );
          },
        ),
      );

      await tester.pumpAndSettle();

      // Verify initial light theme
      var scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(AppTheme.lightTheme.scaffoldBackgroundColor));

      // Switch to dark theme
      await themeService.setThemeMode(ThemeMode.dark);
      await tester.pumpAndSettle();

      // Verify dark theme is applied
      scaffold = tester.widget<Scaffold>(find.byType(Scaffold));
      expect(scaffold.backgroundColor, equals(AppTheme.darkTheme.scaffoldBackgroundColor));

      print('✅ Theme switching immediate feedback test passed');
    });
  });

  group('Dialog Theme Tests', () {
    testWidgets('Alert dialogs use proper theme-aware styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.dark,
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      backgroundColor: Theme.of(context).dialogBackgroundColor,
                      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(16),
                      ),
                      title: Text(
                        'Test Dialog',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      content: Text(
                        'This is a test dialog',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          style: TextButton.styleFrom(
                            foregroundColor: Theme.of(context).colorScheme.primary,
                          ),
                          child: Text('OK'),
                        ),
                      ],
                    ),
                  );
                },
                child: Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Tap button to show dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify dialog appears with proper styling
      expect(find.byType(AlertDialog), findsOneWidget);
      
      final dialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
      expect(dialog.backgroundColor, equals(AppTheme.darkTheme.dialogBackgroundColor));
      expect(dialog.shape, isA<RoundedRectangleBorder>());

      print('✅ Alert dialog theme styling test passed');
    });
  });

  print('\n🎉 All Settings and Profile Management theme tests completed successfully!');
  print('✅ Settings screen theme-aware colors');
  print('✅ User Profile Edit screen theme-aware colors');
  print('✅ Babies Management screen theme-aware colors');
  print('✅ Theme toggle functionality with immediate feedback');
  print('✅ Switch components theme-aware styling');
  print('✅ Dialog components theme-aware styling');
  print('✅ Form components theme-aware styling');
}