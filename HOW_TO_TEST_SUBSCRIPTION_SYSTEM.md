# How to Test the Subscription System

## Overview

This guide explains how to test the subscription system to ensure it's working correctly. The subscription system should restrict free users from accessing premium features and show appropriate upgrade prompts.

## Test 1: Run the App with a Free User

1. **Ensure you have a free user in your database**:
   ```sql
   INSERT INTO "public"."user_subscriptions" (
     "user_id", "plan_id", "plan_name", "status", "monthly_price", 
     "max_family_members", "includes_ai_insights", "includes_data_export", 
     "includes_premium_support", "features"
   ) VALUES (
     '[YOUR-USER-ID]', 'free', 'Free', 'free', '0.00', 
     1, 'false', 'false', 'false', 
     '["Basic activity tracking","Basic scheduled activities","Up to 1 baby profile"]'
   );
   ```

2. **Run the app and log in as this free user**:
   ```bash
   flutter run --dart-define-from-file=env.json
   ```

3. **Try to access premium features**:
   - Tap on the "Ask AI" tab
   - Tap on the "AI Insights" tab
   - Tap on the "Growth Charts" tab

4. **Expected behavior**:
   - You should see upgrade screens instead of the actual features
   - Each upgrade screen should explain the benefits of the premium feature
   - There should be an "Upgrade" button that takes you to the subscription screen

## Test 2: Run the App with a Premium User

1. **Create a premium user in your database**:
   ```sql
   INSERT INTO "public"."user_subscriptions" (
     "user_id", "plan_id", "plan_name", "status", "monthly_price", 
     "max_family_members", "includes_ai_insights", "includes_data_export", 
     "includes_premium_support", "features"
   ) VALUES (
     '[YOUR-USER-ID]', 'premium', 'Premium', 'active', '9.99', 
     10, 'true', 'true', 'true', 
     '["Unlimited activity tracking","Unlimited scheduled activities","Unlimited baby profiles","Family sharing with up to 10 members","WHO Growth Charts","AI insights","Ask AI chat","Data export","Priority customer support","Custom notifications"]'
   );
   ```

2. **Run the app and log in as this premium user**:
   ```bash
   flutter run --dart-define-from-file=env.json
   ```

3. **Try to access premium features**:
   - Tap on the "Ask AI" tab
   - Tap on the "AI Insights" tab
   - Tap on the "Growth Charts" tab

4. **Expected behavior**:
   - You should be able to access all premium features
   - No upgrade screens should appear

## Test 3: Run the Subscription Access Test

For a quick test of the subscription system, you can run the included test file:

```bash
flutter run -t test_subscription_access.dart --dart-define-from-file=env.json
```

This will:
1. Load your current subscription from the database
2. Display your subscription details
3. Test access to all premium features
4. Show which features are allowed or blocked

## Troubleshooting

If the subscription system isn't working correctly:

1. **Check the database**:
   - Verify that the user has the correct subscription record in the database
   - Check that the `status` field is set correctly ('free' or 'active')
   - Ensure the `includes_ai_insights`, `includes_data_export`, etc. fields are set correctly

2. **Check the logs**:
   - Look for any errors related to loading the subscription
   - Check if the subscription controller is initializing properly

3. **Verify the providers**:
   - Make sure the `SubscriptionController` and `FeatureAccessService` are properly provided in `main.dart`
   - Check that they're being initialized correctly

4. **Test individual components**:
   - Try accessing premium features directly through routes
   - Check if the subscription controller is correctly identifying the user's plan

## Next Steps

Once you've verified that the subscription system is working correctly, you can:

1. **Implement payment processing** - Connect to a real payment provider
2. **Add subscription management UI** - Allow users to manage their subscriptions
3. **Add analytics** - Track subscription conversions and feature usage
4. **Refine upgrade prompts** - Optimize messaging for better conversion