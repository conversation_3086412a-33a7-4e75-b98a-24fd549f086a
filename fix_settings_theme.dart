// Script to fix remaining hardcoded theme references in settings
import 'dart:io';

void main() async {
  final file = File('lib/presentation/settings/settings.dart');
  String content = await file.readAsString();
  
  // Replace all remaining AppTheme.lightTheme.colorScheme.primary references
  content = content.replaceAll(
    'AppTheme.lightTheme.colorScheme.primary',
    'Theme.of(context).colorScheme.primary'
  );
  
  // Replace AppTheme.lightTheme.colorScheme.error
  content = content.replaceAll(
    'AppTheme.lightTheme.colorScheme.error',
    'Theme.of(context).colorScheme.error'
  );
  
  // Replace AppTheme.lightTheme.textTheme references
  content = content.replaceAll(
    'AppTheme.lightTheme.textTheme',
    'Theme.of(context).textTheme'
  );
  
  // Replace divider color
  content = content.replaceAll(
    'color: Colors.grey[300]',
    'color: Theme.of(context).dividerColor'
  );
  
  await file.writeAsString(content);
  print('Settings theme fixes applied successfully!');
}