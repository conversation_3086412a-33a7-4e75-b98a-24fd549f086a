-- Fix AI Insights ON CONFLICT constraint issue
-- This adds the unique constraint that the code expects for upsert operations

BEGIN;

-- Add the unique constraint that the code expects for ON CONFLICT
-- This allows the upsert operation with onConflict: 'baby_id,insight_type'
ALTER TABLE public.ai_insights 
DROP CONSTRAINT IF EXISTS ai_insights_baby_insight_unique;

ALTER TABLE public.ai_insights 
ADD CONSTRAINT ai_insights_baby_insight_unique 
UNIQUE (baby_id, insight_type);

-- Create index for better performance on the constraint columns
CREATE INDEX IF NOT EXISTS idx_ai_insights_baby_type ON public.ai_insights(baby_id, insight_type);

COMMIT;

-- Verify the constraint was added
SELECT 
    conname as constraint_name,
    contype as constraint_type,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid = 'public.ai_insights'::regclass 
AND conname = 'ai_insights_baby_insight_unique';