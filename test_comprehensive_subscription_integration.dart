import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/presentation/subscription/controllers/subscription_controller.dart';
import 'lib/services/feature_access_service.dart';
import 'lib/utils/subscription_access_control.dart';
import 'lib/models/enums.dart';
import 'lib/models/subscription_info.dart';

/// Comprehensive test demonstrating proper subscription integration
class ComprehensiveSubscriptionIntegrationTest extends StatefulWidget {
  const ComprehensiveSubscriptionIntegrationTest({super.key});

  @override
  State<ComprehensiveSubscriptionIntegrationTest> createState() => _ComprehensiveSubscriptionIntegrationTestState();
}

class _ComprehensiveSubscriptionIntegrationTestState extends State<ComprehensiveSubscriptionIntegrationTest> {
  final StringBuffer _testResults = StringBuffer();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _runComprehensiveTests();
    });
  }

  Future<void> _runComprehensiveTests() async {
    _testResults.clear();
    _testResults.writeln('🧪 COMPREHENSIVE SUBSCRIPTION INTEGRATION TEST');
    _testResults.writeln('=' * 50);
    _testResults.writeln();

    await _testSubscriptionController();
    await _testFeatureAccessService();
    await _testAccessControlUtility();
    await _testFeatureGating();
    await _testSubscriptionPlans();

    setState(() {});
  }

  Future<void> _testSubscriptionController() async {
    _testResults.writeln('📋 Testing SubscriptionController');
    _testResults.writeln('-' * 30);

    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      
      // Test current subscription
      final subscription = subscriptionController.currentSubscription;
      _testResults.writeln('✓ Current Plan: ${subscription.planName}');
      _testResults.writeln('✓ Status: ${subscription.status.displayName}');
      _testResults.writeln('✓ Is Premium: ${subscription.isPremium}');
      _testResults.writeln('✓ Max Family Members: ${subscription.maxFamilyMembers}');
      _testResults.writeln('✓ Includes AI Insights: ${subscription.includesAiInsights}');
      _testResults.writeln('✓ Includes Data Export: ${subscription.includesDataExport}');
      _testResults.writeln('✓ Includes Premium Support: ${subscription.includesPremiumSupport}');
      _testResults.writeln();

      // Test feature access methods
      _testResults.writeln('🔍 Feature Access Tests:');
      final features = [
        'ai_insights',
        'ai_chat',
        'who_growth_charts',
        'family_sharing',
        'unlimited_profiles',
        'data_export',
        'custom_notifications',
        'premium_support',
        'basic_activity_tracking',
      ];

      for (final feature in features) {
        final hasAccess = subscriptionController.hasFeature(feature);
        final limit = subscriptionController.getFeatureLimit(feature);
        final limitText = limit == null ? 'unlimited' : limit.toString();
        _testResults.writeln('  • $feature: ${hasAccess ? '✅' : '❌'} (limit: $limitText)');
      }
      _testResults.writeln();

      // Test screen access
      _testResults.writeln('🖥️ Screen Access Tests:');
      final screens = [
        'ai_insights',
        'ai_chat',
        'growth_charts',
        'family_sharing',
        'data_export',
      ];

      for (final screen in screens) {
        final canAccess = subscriptionController.canAccessScreen(screen);
        _testResults.writeln('  • $screen: ${canAccess ? '✅' : '❌'}');
      }
      _testResults.writeln();

    } catch (e) {
      _testResults.writeln('❌ SubscriptionController test failed: $e');
    }
  }

  Future<void> _testFeatureAccessService() async {
    _testResults.writeln('🔧 Testing FeatureAccessService');
    _testResults.writeln('-' * 30);

    try {
      final featureAccessService = Provider.of<FeatureAccessService>(context, listen: false);
      
      // Test each app feature
      for (final feature in AppFeature.values) {
        final hasAccess = featureAccessService.hasFeatureAccess(feature);
        final result = featureAccessService.checkFeatureAccess(feature);
        final limit = featureAccessService.getFeatureLimit(feature);
        
        _testResults.writeln('  • ${feature.displayName}:');
        _testResults.writeln('    - Access: ${hasAccess ? '✅' : '❌'}');
        _testResults.writeln('    - Limit: ${limit ?? 'unlimited'}');
        if (result.restrictionReason != null) {
          _testResults.writeln('    - Restriction: ${result.restrictionReason}');
        }
      }
      _testResults.writeln();

      // Test restrictions and benefits
      final restrictions = featureAccessService.getCurrentRestrictions();
      _testResults.writeln('🚫 Current Restrictions (${restrictions.length}):');
      for (final restriction in restrictions) {
        _testResults.writeln('  • ${restriction.feature.displayName}: ${restriction.reason}');
      }
      _testResults.writeln();

      final benefits = featureAccessService.getCurrentBenefits();
      _testResults.writeln('✨ Current Benefits (${benefits.length}):');
      for (final benefit in benefits) {
        _testResults.writeln('  • ${benefit.feature.displayName}: ${benefit.description}');
      }
      _testResults.writeln();

    } catch (e) {
      _testResults.writeln('❌ FeatureAccessService test failed: $e');
    }
  }

  Future<void> _testAccessControlUtility() async {
    _testResults.writeln('🛡️ Testing SubscriptionAccessControl Utility');
    _testResults.writeln('-' * 30);

    try {
      // Test utility methods
      final isPremium = SubscriptionAccessControl.isPremiumUser(context);
      final isFree = SubscriptionAccessControl.isFreeUser(context);
      final planName = SubscriptionAccessControl.getCurrentPlanName(context);
      final statusMessage = SubscriptionAccessControl.getSubscriptionStatusMessage(context);
      final needsAttention = SubscriptionAccessControl.needsAttention(context);
      final upgradeMessage = SubscriptionAccessControl.getUpgradeMessage(context);

      _testResults.writeln('✓ Is Premium: $isPremium');
      _testResults.writeln('✓ Is Free: $isFree');
      _testResults.writeln('✓ Plan Name: $planName');
      _testResults.writeln('✓ Status Message: $statusMessage');
      _testResults.writeln('✓ Needs Attention: $needsAttention');
      _testResults.writeln('✓ Upgrade Message: ${upgradeMessage ?? 'None'}');
      _testResults.writeln();

      // Test feature access checks
      final testFeatures = [
        'ai_insights',
        'family_sharing',
        'data_export',
        'basic_activity_tracking',
      ];

      for (final feature in testFeatures) {
        final hasAccess = SubscriptionAccessControl.hasFeatureAccess(context, feature);
        final limit = SubscriptionAccessControl.getFeatureLimit(context, feature);
        final message = SubscriptionAccessControl.getRestrictionMessage(context, feature);
        
        _testResults.writeln('  • $feature:');
        _testResults.writeln('    - Access: ${hasAccess ? '✅' : '❌'}');
        _testResults.writeln('    - Limit: ${limit ?? 'unlimited'}');
        if (!hasAccess && message.isNotEmpty) {
          _testResults.writeln('    - Message: $message');
        }
      }
      _testResults.writeln();

    } catch (e) {
      _testResults.writeln('❌ SubscriptionAccessControl test failed: $e');
    }
  }

  Future<void> _testFeatureGating() async {
    _testResults.writeln('🚪 Testing Feature Gating');
    _testResults.writeln('-' * 30);

    try {
      // Test different gating scenarios
      final scenarios = [
        {'feature': 'ai_insights', 'description': 'AI Insights Dashboard'},
        {'feature': 'family_sharing', 'description': 'Family Member Management'},
        {'feature': 'data_export', 'description': 'Data Export Functionality'},
        {'feature': 'basic_activity_tracking', 'description': 'Basic Activity Logging'},
      ];

      for (final scenario in scenarios) {
        final feature = scenario['feature']!;
        final description = scenario['description']!;
        final hasAccess = SubscriptionAccessControl.hasFeatureAccess(context, feature);
        
        _testResults.writeln('  • $description:');
        _testResults.writeln('    - Feature: $feature');
        _testResults.writeln('    - Access: ${hasAccess ? '✅ Allowed' : '❌ Blocked'}');
        
        if (!hasAccess) {
          final message = SubscriptionAccessControl.getRestrictionMessage(context, feature);
          _testResults.writeln('    - Reason: $message');
        }
      }
      _testResults.writeln();

    } catch (e) {
      _testResults.writeln('❌ Feature gating test failed: $e');
    }
  }

  Future<void> _testSubscriptionPlans() async {
    _testResults.writeln('📋 Testing Subscription Plans');
    _testResults.writeln('-' * 30);

    try {
      // Test Free Plan
      final freePlan = SubscriptionPlans.free;
      _testResults.writeln('🆓 Free Plan:');
      _testResults.writeln('  • Plan ID: ${freePlan.planId}');
      _testResults.writeln('  • Plan Name: ${freePlan.planName}');
      _testResults.writeln('  • Price: \$${freePlan.monthlyPrice}');
      _testResults.writeln('  • Status: ${freePlan.status.displayName}');
      _testResults.writeln('  • Max Family Members: ${freePlan.maxFamilyMembers}');
      _testResults.writeln('  • AI Insights: ${freePlan.includesAiInsights}');
      _testResults.writeln('  • Data Export: ${freePlan.includesDataExport}');
      _testResults.writeln('  • Premium Support: ${freePlan.includesPremiumSupport}');
      _testResults.writeln('  • Features:');
      for (final feature in freePlan.features) {
        _testResults.writeln('    - $feature');
      }
      _testResults.writeln();

      // Test Premium Plan
      final premiumPlan = SubscriptionPlans.premium;
      _testResults.writeln('⭐ Premium Plan:');
      _testResults.writeln('  • Plan ID: ${premiumPlan.planId}');
      _testResults.writeln('  • Plan Name: ${premiumPlan.planName}');
      _testResults.writeln('  • Price: \$${premiumPlan.monthlyPrice}');
      _testResults.writeln('  • Status: ${premiumPlan.status.displayName}');
      _testResults.writeln('  • Max Family Members: ${premiumPlan.maxFamilyMembers}');
      _testResults.writeln('  • AI Insights: ${premiumPlan.includesAiInsights}');
      _testResults.writeln('  • Data Export: ${premiumPlan.includesDataExport}');
      _testResults.writeln('  • Premium Support: ${premiumPlan.includesPremiumSupport}');
      _testResults.writeln('  • Features:');
      for (final feature in premiumPlan.features) {
        _testResults.writeln('    - $feature');
      }
      _testResults.writeln();

    } catch (e) {
      _testResults.writeln('❌ Subscription plans test failed: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Subscription Integration Test'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: Column(
        children: [
          // Subscription Status Widget
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SubscriptionStatusWidget(),
          ),
          
          // Test Results
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Feature Gating Examples
                  Text(
                    'Feature Gating Examples:',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  
                  // AI Insights Gate
                  SubscriptionGate(
                    featureName: 'ai_insights',
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green),
                      ),
                      child: Text('✅ AI Insights Dashboard - You have access!'),
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // Family Sharing Gate
                  SubscriptionGate(
                    featureName: 'family_sharing',
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green),
                      ),
                      child: Text('✅ Family Sharing - You have access!'),
                    ),
                  ),
                  const SizedBox(height: 8),
                  
                  // Data Export Gate
                  SubscriptionGate(
                    featureName: 'data_export',
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.green),
                      ),
                      child: Text('✅ Data Export - You have access!'),
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Test Results
                  Text(
                    'Test Results:',
                    style: Theme.of(context).textTheme.headlineSmall,
                  ),
                  const SizedBox(height: 16),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _testResults.toString(),
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _runComprehensiveTests,
        child: Icon(Icons.refresh),
        tooltip: 'Run Tests Again',
      ),
    );
  }
}

/// Example of how to integrate subscription access control in your app
class ExampleAppWithSubscriptionControl extends StatelessWidget {
  const ExampleAppWithSubscriptionControl({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SubscriptionController()),
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<SubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
              previous ?? FeatureAccessService(subscription),
        ),
      ],
      child: MaterialApp(
        title: 'BabyTracker Pro',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: ComprehensiveSubscriptionIntegrationTest(),
      ),
    );
  }
}

void main() {
  runApp(ExampleAppWithSubscriptionControl());
}