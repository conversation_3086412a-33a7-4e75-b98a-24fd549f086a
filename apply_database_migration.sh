#!/bin/bash

# <PERSON><PERSON>t to apply the database migration to Supabase
# Make sure you have the Supabase CLI installed and are logged in

echo "Applying database migration to recreate all tables..."

# Navigate to the project root directory
cd "$(dirname "$0")"

# Apply the migration using Supabase CLI
supabase db push

echo "Migration applied successfully!"
echo "Your database tables have been recreated."
echo ""
echo "Next steps:"
echo "1. Verify that all tables were created correctly"
echo "2. Import your data if you have backups"
echo "3. Test your application to ensure everything works"

# Optional: Run a verification query to check if tables were created
echo ""
echo "Verifying tables..."
supabase db query "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;"