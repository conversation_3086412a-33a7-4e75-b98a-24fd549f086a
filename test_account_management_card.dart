import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import 'lib/widgets/account_management_card.dart';
import 'lib/models/subscription_info.dart';
import 'lib/models/enums.dart';
import 'lib/theme/app_theme.dart';

void main() {
  runApp(const AccountManagementCardTestApp());
}

class AccountManagementCardTestApp extends StatelessWidget {
  const AccountManagementCardTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Sizer(
      builder: (context, orientation, deviceType) {
        return MaterialApp(
          title: 'Account Management Card Test',
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          home: const AccountManagementCardTestScreen(),
        );
      },
    );
  }
}

class AccountManagementCardTestScreen extends StatefulWidget {
  const AccountManagementCardTestScreen({super.key});

  @override
  State<AccountManagementCardTestScreen> createState() => _AccountManagementCardTestScreenState();
}

class _AccountManagementCardTestScreenState extends State<AccountManagementCardTestScreen> {
  int _selectedExample = 0;
  
  final List<String> _examples = [
    'Free Plan',
    'Premium Active',
    'Trial Ending',
    'Expired',
    'Loading',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Account Management Card Test'),
        actions: [
          PopupMenuButton<int>(
            onSelected: (value) => setState(() => _selectedExample = value),
            itemBuilder: (context) => _examples.asMap().entries.map((entry) {
              return PopupMenuItem<int>(
                value: entry.key,
                child: Text(entry.value),
              );
            }).toList(),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(4.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Current: ${_examples[_selectedExample]}',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 2.h),
            _buildExample(),
          ],
        ),
      ),
    );
  }

  Widget _buildExample() {
    switch (_selectedExample) {
      case 0:
        return _buildFreeExample();
      case 1:
        return _buildPremiumExample();
      case 2:
        return _buildTrialExample();
      case 3:
        return _buildExpiredExample();
      case 4:
        return _buildLoadingExample();
      default:
        return _buildFreeExample();
    }
  }

  Widget _buildFreeExample() {
    return AccountManagementCard(
      subscription: SubscriptionPlans.free,
      hasAdminPrivileges: false,
      isEmailVerified: true,
      isTwoFactorEnabled: false,
      activeSessions: 1,
      onManageSubscription: () => _showSnackBar('Manage Subscription'),
      onSecuritySettings: () => _showSnackBar('Security Settings'),
      onAccountPreferences: () => _showSnackBar('Account Preferences'),
      onUpgrade: () => _showSnackBar('Upgrade'),
    );
  }

  Widget _buildPremiumExample() {
    final premiumSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.active,
      renewalDate: DateTime.now().add(const Duration(days: 15)),
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
        'Advanced charts and analytics',
        'Data export',
        'Premium support',
        'Unlimited storage',
      ],
      isTrialActive: false,
      paymentMethod: const PaymentMethod(
        type: 'card',
        last4: '4242',
        brand: 'visa',
        expiryMonth: 12,
        expiryYear: 2025,
        isDefault: true,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now(),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: premiumSubscription,
      hasAdminPrivileges: true,
      isEmailVerified: true,
      isTwoFactorEnabled: true,
      activeSessions: 1,
      onManageSubscription: () => _showSnackBar('Manage Subscription'),
      onSecuritySettings: () => _showSnackBar('Security Settings'),
      onAccountPreferences: () => _showSnackBar('Account Preferences'),
    );
  }

  Widget _buildTrialExample() {
    final trialSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium Trial',
      status: SubscriptionStatus.trial,
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
        'Advanced charts and analytics',
      ],
      isTrialActive: true,
      trialEndsAt: DateTime.now().add(const Duration(days: 2)),
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      updatedAt: DateTime.now(),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: trialSubscription,
      hasAdminPrivileges: false,
      isEmailVerified: true,
      isTwoFactorEnabled: false,
      activeSessions: 1,
      onManageSubscription: () => _showSnackBar('Manage Subscription'),
      onSecuritySettings: () => _showSnackBar('Security Settings'),
      onAccountPreferences: () => _showSnackBar('Account Preferences'),
      onUpgrade: () => _showSnackBar('Upgrade'),
      onDowngrade: () => _showSnackBar('Continue Free'),
    );
  }

  Widget _buildExpiredExample() {
    final expiredSubscription = SubscriptionInfo(
      planId: 'premium',
      planName: 'Premium',
      status: SubscriptionStatus.expired,
      monthlyPrice: 9.99,
      features: [
        'Unlimited activity tracking',
        'Unlimited baby profiles',
        'Up to 6 family members',
        'AI-powered insights',
      ],
      isTrialActive: false,
      paymentMethod: const PaymentMethod(
        type: 'card',
        last4: '1234',
        brand: 'mastercard',
        expiryMonth: 1,
        expiryYear: 2020,
        isDefault: true,
      ),
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      maxFamilyMembers: 6,
      includesAiInsights: true,
      includesDataExport: true,
      includesPremiumSupport: true,
      storageLimit: null,
    );

    return AccountManagementCard(
      subscription: expiredSubscription,
      hasAdminPrivileges: false,
      isEmailVerified: false,
      isTwoFactorEnabled: false,
      activeSessions: 3,
      onManageSubscription: () => _showSnackBar('Manage Subscription'),
      onSecuritySettings: () => _showSnackBar('Security Settings'),
      onAccountPreferences: () => _showSnackBar('Account Preferences'),
      onUpgrade: () => _showSnackBar('Reactivate'),
    );
  }

  Widget _buildLoadingExample() {
    return const AccountManagementCard(
      isLoading: true,
    );
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$message tapped'),
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
