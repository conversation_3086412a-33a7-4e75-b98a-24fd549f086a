import 'package:flutter/material.dart';

void main() {
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Growth Chart Test',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
        useMaterial3: true,
      ),
      home: const GrowthChartTestScreen(),
    );
  }
}

class GrowthChartTestScreen extends StatefulWidget {
  const GrowthChartTestScreen({super.key});

  @override
  State<GrowthChartTestScreen> createState() => _GrowthChartTestScreenState();
}

class _GrowthChartTestScreenState extends State<GrowthChartTestScreen> {
  bool _isMetric = true;
  String _dateRange = '1 year';
  
  // Sample test data
  final testMeasurements = [
    {
      'date': DateTime.now().subtract(const Duration(days: 180)),
      'value': 7.5,
      'unit': 'kg',
      'notes': '6 month checkup',
    },
    {
      'date': DateTime.now().subtract(const Duration(days: 90)),
      'value': 9.2,
      'unit': 'kg',
      'notes': '9 month checkup',
    },
    {
      'date': DateTime.now(),
      'value': 10.5,
      'unit': 'kg',
      'notes': '12 month checkup',
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Growth Chart Test'),
        actions: [
          // Toggle metric/imperial
          IconButton(
            onPressed: () {
              setState(() {
                _isMetric = !_isMetric;
              });
            },
            icon: Text(_isMetric ? 'Metric' : 'Imperial'),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Date range selector
            DropdownButton<String>(
              value: _dateRange,
              items: ['6 months', '1 year', '2 years', '3 years', '4 years', '5 years']
                  .map((range) => DropdownMenuItem(
                        value: range,
                        child: Text(range),
                      ))
                  .toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _dateRange = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),
            
            // Growth chart renderer
            Container(
              height: 400,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  'Growth Chart Renderer would be displayed here\nwith ${testMeasurements.length} measurements\n${_isMetric ? 'Metric' : 'Imperial'} units\n$_dateRange',
                  textAlign: TextAlign.center,
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Measurements list
            const Text(
              'Measurements:',
              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18),
            ),
            const SizedBox(height: 8),
            ...testMeasurements.map((measurement) {
              final date = measurement['date'] as DateTime;
              final value = measurement['value'] as double;
              final unit = measurement['unit'] as String;
              final notes = measurement['notes'] as String;
              
              return ListTile(
                title: Text('$value $unit'),
                subtitle: Text('${date.month}/${date.day}/${date.year} - $notes'),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }
}