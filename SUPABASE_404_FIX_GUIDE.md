# Supabase 404 Error Fix Guide

## 🎯 **Current Issue**
The verification link now opens but shows "404 page not found" because `/auth/v1/verify` doesn't exist as a webpage.

## 🔧 **Solution: Use Correct Supabase Endpoint**

### **Step 1: Go Back to Supabase Dashboard**
1. Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
2. Select your project: **snqeizaqnswgpxdhnlkr**
3. Navigate to: **Authentication** → **Settings** → **URL Configuration**

### **Step 2: Change Site URL to Working Endpoint**

**Current (causing 404):**
```
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify
```

**Change to (this will work):**
```
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/callback
```

### **Step 3: Update Redirect URLs**
Make sure your Redirect URLs include:
```
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/callback
https://snqeizaqnswgpxdhnlkr.supabase.co/rest/v1/
io.supabase.flutter://signin-callback/
```

### **Step 4: Save and Test**
1. **Save** the changes
2. **Wait 1-2 minutes** for propagation
3. **Try email change again** in the app
4. **Click the new verification link**

## 🎯 **Alternative Solution: Use Simple Success Page**

If the callback endpoint still doesn't work, try this:

**Site URL:**
```
https://snqeizaqnswgpxdhnlkr.supabase.co
```

This will redirect to your Supabase project's main page, which always exists.

## 🔍 **What the URL Should Look Like**

After the fix, the verification link should look like:
```
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/callback#message=Confirmation+link+accepted...
```

Instead of the current:
```
https://snqeizaqnswgpxdhnlkr.supabase.co/auth/v1/verify%20#message=...
```

## ⚠️ **Note About the Message**
The message "Please proceed to confirm link sent to the other email" is normal - it means Supabase is processing the email change. The app should automatically detect the completion.

## 🎯 **Expected Flow After Fix**
1. **Click verification link** → Opens Supabase page (no 404)
2. **See confirmation message** → Email change is being processed
3. **Return to app** → App detects email change completion automatically
4. **Success!** → Email changed from old to new address