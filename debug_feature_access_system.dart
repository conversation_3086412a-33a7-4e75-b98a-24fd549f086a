/// Debug script to test and fix the feature access system
/// Run with: flutter run debug_feature_access_system.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/models/feature_access.dart';
import 'lib/models/subscription_info.dart';
import 'lib/models/enums.dart';
import 'lib/services/feature_access_service.dart';
import 'lib/presentation/subscription/controllers/feature_access_controller.dart';
import 'lib/presentation/subscription/controllers/subscription_controller.dart';
import 'lib/presentation/subscription/widgets/feature_gate.dart';

void main() {
  runApp(DebugFeatureAccessApp());
}

class DebugFeatureAccessApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => DebugSubscriptionController()),
        ChangeNotifierProxyProvider<DebugSubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<DebugSubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
            previous ?? FeatureAccessService(subscription),
        ),
        ChangeNotifierProxyProvider<FeatureAccessService, FeatureAccessController>(
          create: (context) => FeatureAccessController(
            Provider.of<FeatureAccessService>(context, listen: false),
          ),
          update: (context, service, previous) => 
            previous ?? FeatureAccessController(service),
        ),
      ],
      child: MaterialApp(
        title: 'Feature Access Debug',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: DebugScreen(),
      ),
    );
  }
}

/// Debug version of SubscriptionController that we can control
class DebugSubscriptionController extends ChangeNotifier {
  SubscriptionInfo _currentSubscription = SubscriptionPlans.free;

  SubscriptionInfo get currentSubscription => _currentSubscription;

  void setSubscriptionStatus(SubscriptionStatus status) {
    debugPrint('DEBUG: Setting subscription status to: ${status.toString()}');
    
    try {
      debugPrint('DEBUG: Status name property: ${status.toString().split('.').last}');
      debugPrint('DEBUG: Status isPremium: ${status.isPremium}');
      debugPrint('DEBUG: Status displayName: ${status.displayName}');
    } catch (e) {
      debugPrint('ERROR: Problem with status properties: $e');
    }
    
    // Create subscription based on status
    if (status == SubscriptionStatus.active || status == SubscriptionStatus.trial) {
      _currentSubscription = SubscriptionPlans.premium;
    } else {
      _currentSubscription = SubscriptionPlans.free;
    }
    
    // Manually set the status
    _currentSubscription = _currentSubscription.copyWith(status: status);
    
    debugPrint('DEBUG: Updated subscription - Plan: ${_currentSubscription.planName}, Status: ${_currentSubscription.status}, isPremium: ${_currentSubscription.status.isPremium}');
    
    notifyListeners();
  }

  void addListener(Function() listener) {
    super.addListener(listener);
  }

  void removeListener(Function() listener) {
    super.removeListener(listener);
  }
}

class DebugScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Feature Access System Debug'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<FeatureAccessController>(
        builder: (context, controller, child) {
          return Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Debug Info
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Debug Information',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 8),
                        Consumer<DebugSubscriptionController>(
                          builder: (context, subscriptionController, _) {
                            final sub = subscriptionController.currentSubscription;
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text('Plan: ${sub.planName}'),
                                Text('Status: ${sub.status}'),
                                Text('Status toString: ${sub.status.toString()}'),
                                Text('isPremium: ${sub.status.isPremium}'),
                                Text('displayName: ${sub.status.displayName}'),
                                Builder(
                                  builder: (context) {
                                    try {
                                      return Text('status.name: ${sub.status.toString().split('.').last}');
                                    } catch (e) {
                                      return Text('ERROR with status.name: $e');
                                    }
                                  },
                                ),
                              ],
                            );
                          },
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: 16),
                
                // Subscription Controls
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Subscription Controls',
                          style: Theme.of(context).textTheme.titleLarge,
                        ),
                        SizedBox(height: 8),
                        Row(
                          children: [
                            ElevatedButton(
                              onPressed: () => _setSubscription(context, SubscriptionStatus.free),
                              child: Text('Free'),
                            ),
                            SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: () => _setSubscription(context, SubscriptionStatus.active),
                              child: Text('Premium'),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                
                SizedBox(height: 16),
                
                // Feature Tests
                Expanded(
                  child: ListView(
                    children: AppFeature.values.map((feature) => Card(
                      child: Padding(
                        padding: EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(feature.icon),
                                SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    feature.displayName,
                                    style: Theme.of(context).textTheme.titleMedium,
                                  ),
                                ),
                                _buildAccessChip(
                                  context, 
                                  controller.canAccessFeature(feature)
                                ),
                              ],
                            ),
                            SizedBox(height: 8),
                            Text(
                              feature.description,
                              style: Theme.of(context).textTheme.bodySmall,
                            ),
                            SizedBox(height: 12),
                            
                            // Test FeatureGate
                            FeatureGate(
                              feature: feature,
                              child: Container(
                                padding: EdgeInsets.all(12),
                                decoration: BoxDecoration(
                                  color: Colors.green.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Row(
                                  children: [
                                    Icon(Icons.check_circle, color: Colors.green),
                                    SizedBox(width: 8),
                                    Text('✅ Feature Available'),
                                  ],
                                ),
                              ),
                              onUpgrade: () => _showUpgradeDialog(context, feature),
                            ),
                          ],
                        ),
                      ),
                    )).toList(),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildAccessChip(BuildContext context, bool hasAccess) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: hasAccess ? Colors.green : Colors.red,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        hasAccess ? 'Available' : 'Restricted',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _setSubscription(BuildContext context, SubscriptionStatus status) {
    final controller = Provider.of<DebugSubscriptionController>(context, listen: false);
    controller.setSubscriptionStatus(status);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Subscription set to: ${status.displayName}'),
        backgroundColor: status.isPremium ? Colors.green : Colors.orange,
      ),
    );
  }

  void _showUpgradeDialog(BuildContext context, AppFeature feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upgrade Required'),
        content: Text('${feature.displayName} requires a Premium subscription.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Would redirect to subscription screen...')),
              );
            },
            child: Text('Upgrade'),
          ),
        ],
      ),
    );
  }
}

/// Predefined subscription plans for easy access
class SubscriptionPlans {
  static final free = SubscriptionInfo(
    planId: 'free',
    planName: 'Free',
    status: SubscriptionStatus.free,
    monthlyPrice: 0.0,
    features: [
      '1 baby profile',
      'Basic activity tracking',
      'Basic growth tracking',
    ],
    isTrialActive: false,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    maxFamilyMembers: 1,
    includesAiInsights: false,
    includesDataExport: false,
    includesPremiumSupport: false,
  );

  static final premium = SubscriptionInfo(
    planId: 'premium',
    planName: 'Premium',
    status: SubscriptionStatus.active,
    renewalDate: DateTime.now().add(Duration(days: 30)),
    monthlyPrice: 9.99,
    features: [
      'Unlimited baby profiles',
      'Full family sharing (up to 10 members)', 
      'WHO Growth Charts',
      'AI Insights & Analysis',
      'AI Chat Assistant',
      'Data Export & Backup',
      'Priority Support',
      'Advanced Analytics',
    ],
    isTrialActive: false,
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    maxFamilyMembers: 10,
    includesAiInsights: true,
    includesDataExport: true,
    includesPremiumSupport: true,
  );
}
