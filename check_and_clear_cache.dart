import 'dart:io';
import 'dart:convert';

Future<void> main() async {
  print('🔍 Checking and Clearing ALL AI Insights Cache...\n');
  
  // Read environment configuration
  final envFile = File('env.json');
  if (!envFile.existsSync()) {
    print('❌ Error: env.json file not found');
    exit(1);
  }
  
  final envContent = await envFile.readAsString();
  final envConfig = jsonDecode(envContent);
  
  final supabaseUrl = envConfig['SUPABASE_URL'];
  final supabaseKey = envConfig['SUPABASE_ANON_KEY'];
  
  if (supabaseUrl == null || supabaseKey == null) {
    print('❌ Error: Supabase credentials not found in env.json');
    exit(1);
  }
  
  // The baby ID from the logs
  const babyId = 'c5959165-09bb-4aa5-8149-42c12b17f3c3';
  
  try {
    // First, check what insights exist
    print('📊 Checking existing AI insights for baby: $babyId');
    final checkResult = await Process.run('curl', [
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (checkResult.exitCode == 0) {
      final response = checkResult.stdout.toString();
      if (response.trim() != '[]') {
        print('🔍 Found cached insights:');
        
        // Parse and print key details
        try {
          final insights = jsonDecode(response) as List;
          for (var insight in insights) {
            print('   - ID: ${insight['id']}');
            print('   - Type: ${insight['insight_type']}');
            print('   - Created: ${insight['created_at']}');
            print('   - Updated: ${insight['updated_at']}');
            print('');
          }
          print('Total insights found: ${insights.length}');
        } catch (e) {
          print('Raw response: $response');
        }
      } else {
        print('✅ No cached insights found');
        return;
      }
    }
    
    print('\n🗑️ Deleting ALL cached insights for baby: $babyId');
    
    // Delete cached insights for this baby
    final deleteResult = await Process.run('curl', [
      '-X', 'DELETE',
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '-H', 'Content-Type: application/json',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (deleteResult.exitCode == 0) {
      print('✅ Successfully cleared cached AI insights');
      print('📊 Delete response: ${deleteResult.stdout}');
    } else {
      print('❌ Error clearing cached insights: ${deleteResult.stderr}');
    }
    
    // Verify deletion
    print('\n🔍 Verifying cache is cleared...');
    final verifyResult = await Process.run('curl', [
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (verifyResult.exitCode == 0) {
      final response = verifyResult.stdout.toString();
      if (response.trim() == '[]') {
        print('✅ Cache successfully cleared - no insights found');
        print('\n🔄 The next time the app loads AI insights, it will generate completely fresh ones with correct timestamps.');
        print('\n📱 You can now:');
        print('   1. Pull to refresh the home screen, or');
        print('   2. Tap the refresh button on the AI insights card, or');
        print('   3. Restart the app');
      } else {
        print('⚠️ Some insights still exist:');
        print(response);
      }
    }
    
  } catch (e) {
    print('❌ Error: $e');
    exit(1);
  }
}
