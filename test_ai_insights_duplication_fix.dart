import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'lib/services/ai_insights_state_manager.dart';
import 'lib/core/app_export.dart';

/// Test to verify that AI insights are only loaded once and shared between screens
void main() {
  group('AI Insights Duplication Fix Tests', () {
    late AIInsightsStateManager stateManager;
    late BabyProfile testBaby;

    setUp(() {
      stateManager = AIInsightsStateManager();
      testBaby = BabyProfile(
        id: 'test-baby-123',
        name: 'Test Baby',
        birthDate: DateTime.now().subtract(Duration(days: 30)),
        userId: 'test-user-123',
      );
    });

    test('should not reload insights if already loaded for same baby', () async {
      // Simulate that insights are already loaded
      stateManager.setLastUpdateTimeForTesting(DateTime.now());
      
      // Track the number of API calls (this would need to be implemented in the actual service)
      int apiCallCount = 0;
      
      // First call - should load insights
      await stateManager.loadInsights(testBaby);
      
      // Second call - should skip loading since insights already exist
      await stateManager.loadInsights(testBaby);
      
      // Verify that hasInsightsForBaby returns true after first load
      expect(stateManager.hasInsightsForBaby(testBaby.id), isTrue);
    });

    test('should prevent duplicate loading when isLoading is true', () async {
      // This test would verify that concurrent calls are prevented
      // In a real implementation, you'd mock the AI service to track calls
      
      // Simulate concurrent calls
      final future1 = stateManager.loadInsights(testBaby);
      final future2 = stateManager.loadInsights(testBaby);
      
      await Future.wait([future1, future2]);
      
      // Verify that only one actual API call was made
      // This would require mocking the AI service to count calls
    });

    test('should share state between Home and Dashboard screens', () {
      // Test that both screens use the same state manager instance
      final homeStateManager = AIInsightsStateManager();
      final dashboardStateManager = AIInsightsStateManager();
      
      // Should be the same instance (singleton)
      expect(identical(homeStateManager, dashboardStateManager), isTrue);
    });
  });
}