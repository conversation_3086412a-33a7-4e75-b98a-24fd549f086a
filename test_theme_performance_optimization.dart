import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'lib/theme/app_theme.dart';
import 'lib/theme/theme_aware_colors.dart';
import 'lib/theme/milestone_theme_colors.dart';
import 'lib/theme/ui_improvements.dart';
import 'lib/services/theme_service.dart';
import 'lib/utils/form_theme_helper.dart';
import 'lib/utils/dialog_theme_helper.dart';
import 'lib/utils/activity_configs.dart';

/// Comprehensive test suite for theme performance optimizations and cleanup.
/// 
/// This test validates:
/// 1. Theme switching performance without app restart
/// 2. Proper cleanup of unused theme code
/// 3. Theme-aware color consistency
/// 4. Performance optimizations in theme helpers
/// 5. Accessibility compliance in both themes
void main() {
  group('Theme Performance Optimization Tests', () {
    late ThemeService themeService;

    setUp(() async {
      themeService = ThemeService();
      await themeService.init();
    });

    testWidgets('Theme switching works without app restart', (WidgetTester tester) async {
      // Create test app with theme service
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeService,
          child: Consumer<ThemeService>(
            builder: (context, themeService, child) {
              return MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeService.themeMode,
                home: Scaffold(
                  body: Column(
                    children: [
                      // Test theme-aware colors
                      Container(
                        color: ThemeAwareColors.getPrimaryColor(context),
                        child: Text(
                          'Primary Color Test',
                          style: TextStyle(
                            color: ThemeAwareColors.getPrimaryTextColor(context),
                          ),
                        ),
                      ),
                      // Test milestone colors
                      Container(
                        color: MilestoneThemeColors.getTotalMilestonesColor(context),
                        child: Text('Milestone Color Test'),
                      ),
                      // Theme toggle button
                      ElevatedButton(
                        onPressed: () => themeService.toggleTheme(),
                        child: Text('Toggle Theme'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Verify initial light theme
      expect(themeService.themeMode, ThemeMode.system);
      
      // Toggle to dark theme
      await tester.tap(find.text('Toggle Theme'));
      await tester.pumpAndSettle();
      
      // Verify theme changed without restart
      expect(themeService.isDarkMode, isTrue);
      
      // Toggle back to light theme
      await tester.tap(find.text('Toggle Theme'));
      await tester.pumpAndSettle();
      
      // Verify theme changed back
      expect(themeService.isDarkMode, isFalse);
    });

    testWidgets('ThemeAwareColors performance optimization', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: Builder(
            builder: (context) {
              // Test that colors are retrieved efficiently
              final stopwatch = Stopwatch()..start();
              
              // Test multiple color retrievals
              for (int i = 0; i < 100; i++) {
                ThemeAwareColors.getPrimaryColor(context);
                ThemeAwareColors.getSecondaryColor(context);
                ThemeAwareColors.getSurfaceColor(context);
                ThemeAwareColors.getPrimaryTextColor(context);
              }
              
              stopwatch.stop();
              
              // Performance should be under 10ms for 400 color retrievals
              expect(stopwatch.elapsedMilliseconds, lessThan(10));
              
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('MilestoneThemeColors optimization', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: Builder(
            builder: (context) {
              // Test optimized milestone colors
              final totalColor = MilestoneThemeColors.getTotalMilestonesColor(context);
              final monthColor = MilestoneThemeColors.getThisMonthColor(context);
              final ageColor = MilestoneThemeColors.getBabyAgeColor(context);
              
              // Verify colors are using AppTheme constants
              expect(totalColor, equals(AppTheme.primaryLight));
              expect(monthColor, equals(AppTheme.secondaryLight));
              expect(ageColor, equals(AppTheme.accentLight));
              
              return Container();
            },
          ),
        ),
      );
    });

    testWidgets('Form theme helpers work with optimized colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return Column(
                  children: [
                    // Test optimized form components
                    TextFormField(
                      decoration: FormThemeHelper.getInputDecoration(
                        context,
                        labelText: 'Test Field',
                      ),
                    ),
                    FormThemeHelper.getThemedElevatedButton(
                      context,
                      onPressed: () {},
                      child: Text('Test Button'),
                    ),
                    FormThemeHelper.getThemedSwitch(
                      context,
                      value: true,
                      onChanged: (value) {},
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      );

      // Verify form components render without errors
      expect(find.byType(TextFormField), findsOneWidget);
      expect(find.byType(ElevatedButton), findsOneWidget);
      expect(find.byType(Switch), findsOneWidget);
    });

    testWidgets('Dialog theme helpers work with optimized colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: Scaffold(
            body: Builder(
              builder: (context) {
                return ElevatedButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (context) => DialogThemeHelper.createThemedAlertDialog(
                        context: context,
                        title: Text('Test Dialog'),
                        content: Text('Test content'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.pop(context),
                            child: Text('OK'),
                          ),
                        ],
                      ),
                    );
                  },
                  child: Text('Show Dialog'),
                );
              },
            ),
          ),
        ),
      );

      // Test dialog creation
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();
      
      expect(find.text('Test Dialog'), findsOneWidget);
      expect(find.text('Test content'), findsOneWidget);
      
      // Close dialog
      await tester.tap(find.text('OK'));
      await tester.pumpAndSettle();
    });

    testWidgets('Theme service caching works correctly', (WidgetTester tester) async {
      // Test that system brightness caching improves performance
      final service = ThemeService();
      await service.init();
      
      // Set to system mode
      await service.setThemeMode(ThemeMode.system);
      
      // Test multiple isDarkMode calls (should use cached brightness)
      final stopwatch = Stopwatch()..start();
      for (int i = 0; i < 100; i++) {
        service.isDarkMode;
      }
      stopwatch.stop();
      
      // Should be very fast due to caching
      expect(stopwatch.elapsedMicroseconds, lessThan(1000));
    });

    test('Theme service initialization handles errors gracefully', () async {
      // Test error handling in theme service
      final service = ThemeService();
      
      // Should not throw even if SharedPreferences fails
      await service.init();
      expect(service.isInitialized, isTrue);
      
      // Should have fallback theme mode
      expect(service.themeMode, isNotNull);
    });

    testWidgets('Container decorations use optimized colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: Builder(
            builder: (context) {
              final decoration = ThemeAwareColors.getContainerDecoration(context);
              final selectedDecoration = ThemeAwareColors.getSelectedContainerDecoration(
                context,
                isSelected: true,
              );
              
              // Verify decorations use theme colors
              expect(decoration.color, equals(Theme.of(context).colorScheme.surface));
              expect(selectedDecoration.color, isNotNull);
              
              return Container(decoration: decoration);
            },
          ),
        ),
      );
    });

    testWidgets('Theme switching preserves widget state', (WidgetTester tester) async {
      String textFieldValue = '';
      
      await tester.pumpWidget(
        ChangeNotifierProvider.value(
          value: themeService,
          child: Consumer<ThemeService>(
            builder: (context, themeService, child) {
              return MaterialApp(
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeService.themeMode,
                home: Scaffold(
                  body: Column(
                    children: [
                      TextFormField(
                        key: Key('test_field'),
                        onChanged: (value) => textFieldValue = value,
                        decoration: FormThemeHelper.getInputDecoration(
                          context,
                          labelText: 'Test Field',
                        ),
                      ),
                      ElevatedButton(
                        onPressed: () => themeService.toggleTheme(),
                        child: Text('Toggle Theme'),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      );

      // Enter text
      await tester.enterText(find.byKey(Key('test_field')), 'Test Value');
      expect(textFieldValue, equals('Test Value'));
      
      // Toggle theme
      await tester.tap(find.text('Toggle Theme'));
      await tester.pumpAndSettle();
      
      // Verify text is preserved after theme change
      expect(find.text('Test Value'), findsOneWidget);
    });

    testWidgets('All theme colors maintain accessibility contrast', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: Builder(
            builder: (context) {
              // Test contrast ratios for key color combinations
              final backgroundColor = ThemeAwareColors.getBackgroundColor(context);
              final textColor = ThemeAwareColors.getPrimaryTextColor(context);
              final primaryColor = ThemeAwareColors.getPrimaryColor(context);
              
              // Calculate contrast ratios (simplified check)
              final backgroundLuminance = backgroundColor.computeLuminance();
              final textLuminance = textColor.computeLuminance();
              final primaryLuminance = primaryColor.computeLuminance();
              
              // Verify minimum contrast ratios
              final textContrast = (backgroundLuminance + 0.05) / (textLuminance + 0.05);
              expect(textContrast, greaterThan(4.5)); // WCAG AA standard
              
              return Container();
            },
          ),
        ),
      );
    });
  });

  group('Theme Cleanup Validation', () {
    test('Deprecated UIImprovements class is properly marked', () {
      // Verify that UIImprovements is deprecated and throws for removed methods
      expect(() => UIImprovements.getTextFieldDecoration(), throwsUnimplementedError);
      expect(() => UIImprovements.getSnackBar(message: 'test'), throwsUnimplementedError);
      expect(() => UIImprovements.getAppBar(title: 'test'), throwsUnimplementedError);
    });

    testWidgets('Activity configs use optimized theme colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          home: Builder(
            builder: (context) {
              // Verify activity configs use AppTheme colors instead of hardcoded values
              final feedingConfig = ActivityConfigs.configs['feeding']!;
              expect(feedingConfig.lightColor, equals(AppTheme.primaryLight));
              expect(feedingConfig.darkColor, equals(AppTheme.primaryDark));
              
              final sleepConfig = ActivityConfigs.configs['sleep']!;
              expect(sleepConfig.lightColor, equals(AppTheme.accentLight));
              expect(sleepConfig.darkColor, equals(AppTheme.accentDark));
              
              return Container();
            },
          ),
        ),
      );
    });
  });
}