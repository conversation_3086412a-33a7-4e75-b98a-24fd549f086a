// Example refactored structure
class SleepEntryWidget extends BaseActivityWidget {
  // Main widget - only UI coordination
}

class SleepTimerManager {
  // Handles live sleep session timing
  Timer? _liveSessionTimer;
  Duration? _currentSessionDuration;
  
  void startTimer(DateTime startTime, VoidCallback onUpdate) { }
  void stopTimer() { }
}

class SleepAlarmManager {
  // Handles wake-up alarm functionality
  Timer? _alarmTimer;
  DateTime? _alarmTime;
  
  Future<void> scheduleAlarm(DateTime alarmTime) { }
  void cancelAlarm() { }
}

class SleepEnvironmentWidget extends StatelessWidget {
  // Separate widget for sleep environment controls
  final String sleepQuality;
  final String sleepLocation;
  final double roomTemperature;
  final Function(Map<String, dynamic>) onChanged;
}

class SleepModeSelector extends StatelessWidget {
  // Separate widget for live/past mode selection
  final String sleepMode;
  final bool isLiveSleepMode;
  final VoidCallback onStartLive;
  final VoidCallback onStopLive;
  final VoidCallback onSetPast;
}