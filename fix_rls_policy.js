const { createClient } = require('@supabase/supabase-js');

// Load Supabase credentials from environment variables
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   - SUPABASE_URL');
  console.error('   - SUPABASE_ANON_KEY');
  console.error('Please set these in your .env file');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

// SQL to fix the RLS policy
const fixRLSPolicySQL = `
-- Fix baby_profiles RLS policy to allow users to create their own baby profiles
-- This migration fixes the issue where users cannot create baby profiles due to overly restrictive RLS policies

-- Drop the problematic enhanced policy
DROP POLICY IF EXISTS "enhanced_baby_access" ON public.baby_profiles;

-- Create a new policy that allows users to manage their own baby profiles
-- This policy handles both cases: with and without family_id
CREATE POLICY "users_can_manage_own_baby_profiles" ON public.baby_profiles
FOR ALL TO authenticated
USING (
    user_id = auth.uid() OR 
    public.can_access_baby_profile(id)
)
WITH CHECK (
    user_id = auth.uid() OR 
    (family_id IS NOT NULL AND public.is_family_admin(family_id))
);

-- Also create a simpler policy specifically for INSERT operations
-- This ensures users can always create baby profiles for themselves
CREATE POLICY "users_can_create_baby_profiles" ON public.baby_profiles
FOR INSERT TO authenticated
WITH CHECK (user_id = auth.uid());

-- Update the can_access_baby_profile function to handle NULL family_id gracefully
CREATE OR REPLACE FUNCTION public.can_access_baby_profile(baby_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.baby_profiles bp
    LEFT JOIN public.families f ON f.id = bp.family_id
    LEFT JOIN public.family_members fm ON fm.family_id = f.id
    LEFT JOIN public.baby_carer_assignments bca ON bca.baby_id = bp.id AND bca.carer_id = fm.id
    WHERE bp.id = baby_uuid 
    AND (
        bp.user_id = auth.uid() OR
        (bp.family_id IS NOT NULL AND f.admin_user_id = auth.uid()) OR
        (bp.family_id IS NOT NULL AND fm.user_id = auth.uid() AND fm.is_active = true AND (bca.can_view = true OR bca.can_view IS NULL))
    )
)
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.baby_profiles TO authenticated;
`;

async function fixRLSPolicy() {
    try {
        console.log('🔧 Connecting to Supabase and fixing RLS policy...');
        
        // Execute the SQL fix
        const { data, error } = await supabase.rpc('exec_sql', {
            sql: fixRLSPolicySQL
        });
        
        if (error) {
            // If the RPC doesn't exist, try running individual queries
            console.log('RPC method not available, trying individual queries...');
            
            // Split the SQL into individual statements and execute them
            const statements = fixRLSPolicySQL
                .split(';')
                .map(stmt => stmt.trim())
                .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
            
            for (const statement of statements) {
                console.log(`Executing: ${statement.substring(0, 50)}...`);
                const { error: stmtError } = await supabase.rpc('exec_sql', {
                    sql: statement
                });
                
                if (stmtError) {
                    console.error(`❌ Error executing statement: ${stmtError.message}`);
                    console.error(`Statement: ${statement}`);
                    return;
                }
            }
        }
        
        console.log('✅ RLS policy fix completed successfully!');
        console.log('🎉 You can now create baby profiles in your app!');
        
    } catch (error) {
        console.error('❌ Error fixing RLS policy:', error);
    }
}

// Run the fix
fixRLSPolicy(); 