import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'lib/core/app_export.dart';
import 'lib/presentation/settings/settings.dart';
import 'lib/presentation/user_profile_edit/user_profile_edit_screen.dart';
import 'lib/presentation/babies_management/babies_management_screen.dart';

void main() {
  group('Settings Theme Implementation Tests', () {
    testWidgets('Settings screen uses theme-aware colors', (WidgetTester tester) async {
      // Create a test app with theme service
      final themeService = ThemeService();
      await themeService.init();

      await tester.pumpWidget(
        ChangeNotifierProvider<ThemeService>.value(
          value: themeService,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.light,
            home: const Settings(),
          ),
        ),
      );

      // Wait for the widget to build
      await tester.pumpAndSettle();

      // Verify that the settings screen builds without errors
      expect(find.byType(Settings), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);

      print('✅ Settings screen builds successfully with light theme');

      // Switch to dark theme
      await themeService.setThemeMode(ThemeMode.dark);
      await tester.pumpWidget(
        ChangeNotifierProvider<ThemeService>.value(
          value: themeService,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: ThemeMode.dark,
            home: const Settings(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the settings screen still builds without errors in dark theme
      expect(find.byType(Settings), findsOneWidget);
      expect(find.text('Settings'), findsOneWidget);

      print('✅ Settings screen builds successfully with dark theme');
    });

    testWidgets('User Profile Edit screen uses theme-aware colors', (WidgetTester tester) async {
      // Create a mock user profile
      final userProfile = UserProfile(
        id: 'test-id',
        fullName: 'Test User',
        email: '<EMAIL>',
        role: 'parent',
        signInCount: 1,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: UserProfileEditScreen(userProfile: userProfile),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the user profile edit screen builds without errors
      expect(find.byType(UserProfileEditScreen), findsOneWidget);
      expect(find.text('Edit Profile'), findsOneWidget);

      print('✅ User Profile Edit screen builds successfully with light theme');

      // Test with dark theme
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.dark,
          home: UserProfileEditScreen(userProfile: userProfile),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the user profile edit screen still builds without errors in dark theme
      expect(find.byType(UserProfileEditScreen), findsOneWidget);
      expect(find.text('Edit Profile'), findsOneWidget);

      print('✅ User Profile Edit screen builds successfully with dark theme');
    });

    testWidgets('Babies Management screen uses theme-aware colors', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.light,
          home: const BabiesManagementScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the babies management screen builds without errors
      expect(find.byType(BabiesManagementScreen), findsOneWidget);
      expect(find.text('Manage Babies'), findsOneWidget);

      print('✅ Babies Management screen builds successfully with light theme');

      // Test with dark theme
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.lightTheme,
          darkTheme: AppTheme.darkTheme,
          themeMode: ThemeMode.dark,
          home: const BabiesManagementScreen(),
        ),
      );

      await tester.pumpAndSettle();

      // Verify that the babies management screen still builds without errors in dark theme
      expect(find.byType(BabiesManagementScreen), findsOneWidget);
      expect(find.text('Manage Babies'), findsOneWidget);

      print('✅ Babies Management screen builds successfully with dark theme');
    });

    test('Theme switching provides immediate visual feedback', () async {
      final themeService = ThemeService();
      await themeService.init();

      // Test initial state
      expect(themeService.themeMode, ThemeMode.system);

      // Test switching to light theme
      await themeService.setThemeMode(ThemeMode.light);
      expect(themeService.themeMode, ThemeMode.light);
      expect(themeService.themeModeString, 'Light');

      // Test switching to dark theme
      await themeService.setThemeMode(ThemeMode.dark);
      expect(themeService.themeMode, ThemeMode.dark);
      expect(themeService.themeModeString, 'Dark');

      // Test switching back to system theme
      await themeService.setThemeMode(ThemeMode.system);
      expect(themeService.themeMode, ThemeMode.system);
      expect(themeService.themeModeString, 'System');

      print('✅ Theme switching works correctly and provides immediate feedback');
    });
  });
}