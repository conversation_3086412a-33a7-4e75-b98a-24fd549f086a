#!/usr/bin/env python3

import psycopg2
import sys

# Supabase connection details from environment variables
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

DB_HOST = os.getenv("SUPABASE_DB_HOST")
DB_PORT = os.getenv("SUPABASE_DB_PORT", "5432")
DB_NAME = os.getenv("SUPABASE_DB_NAME", "postgres")
DB_USER = os.getenv("SUPABASE_DB_USER", "postgres")
DB_PASSWORD = os.getenv("SUPABASE_DB_PASSWORD")

if not all([DB_HOST, DB_PASSWORD]):
    print("❌ Missing required environment variables:")
    print("   - SUPABASE_DB_HOST")
    print("   - SUPABASE_DB_PASSWORD")
    print("Please set these in your .env file")
    exit(1)

# SQL to fix the RLS policy
FIX_RLS_SQL = """
-- Fix baby_profiles RLS policy to allow users to create their own baby profiles
-- Drop the problematic enhanced policy
DROP POLICY IF EXISTS "enhanced_baby_access" ON public.baby_profiles;

-- Create a new policy that allows users to manage their own baby profiles
CREATE POLICY "users_can_manage_own_baby_profiles" ON public.baby_profiles
FOR ALL TO authenticated
USING (
    user_id = auth.uid() OR 
    public.can_access_baby_profile(id)
)
WITH CHECK (
    user_id = auth.uid() OR 
    (family_id IS NOT NULL AND public.is_family_admin(family_id))
);

-- Also create a simpler policy specifically for INSERT operations
CREATE POLICY "users_can_create_baby_profiles" ON public.baby_profiles
FOR INSERT TO authenticated
WITH CHECK (user_id = auth.uid());

-- Update the can_access_baby_profile function to handle NULL family_id gracefully
CREATE OR REPLACE FUNCTION public.can_access_baby_profile(baby_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.baby_profiles bp
    LEFT JOIN public.families f ON f.id = bp.family_id
    LEFT JOIN public.family_members fm ON fm.family_id = f.id
    LEFT JOIN public.baby_carer_assignments bca ON bca.baby_id = bp.id AND bca.carer_id = fm.id
    WHERE bp.id = baby_uuid 
    AND (
        bp.user_id = auth.uid() OR
        (bp.family_id IS NOT NULL AND f.admin_user_id = auth.uid()) OR
        (bp.family_id IS NOT NULL AND fm.user_id = auth.uid() AND fm.is_active = true AND (bca.can_view = true OR bca.can_view IS NULL))
    )
)
$$;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.baby_profiles TO authenticated;
"""

def fix_rls_policy():
    try:
        print("🔧 Connecting to Supabase PostgreSQL database...")
        
        # Create connection string
        connection_string = f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode=require"
        
        # Connect to database
        conn = psycopg2.connect(connection_string)
        cursor = conn.cursor()
        
        print("✅ Connected successfully!")
        print("🛠️  Executing RLS policy fix...")
        
        # Execute the SQL fix
        cursor.execute(FIX_RLS_SQL)
        conn.commit()
        
        print("✅ RLS policy fix completed successfully!")
        print("🎉 You can now create baby profiles in your app!")
        
        cursor.close()
        conn.close()
        
    except psycopg2.Error as e:
        print(f"❌ Database error: {e}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_rls_policy() 