import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'lib/presentation/ai_insights/widgets/insight_card_widget.dart';
import 'lib/presentation/ai_insights/widgets/insights_filter_widget.dart';
import 'lib/presentation/ai_insights/widgets/milestone_predictions_widget.dart';
import 'lib/theme/app_theme.dart';
import 'lib/core/app_export.dart';

void main() {
  group('Dark Theme Fixes Tests', () {
    testWidgets('InsightCardWidget uses theme-aware colors', (WidgetTester tester) async {
      // Test with dark theme
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.darkTheme,
          home: Scaffold(
            body: InsightCardWidget(
              insight: AIInsight(
                id: 'test',
                babyId: 'test-baby',
                type: InsightType.sleep,
                title: 'Test Sleep Analysis',
                description: 'Test description',
                priority: InsightPriority.high,
                confidence: 0.85,
                generatedAt: DateTime.now(),
                isRead: false,
                isArchived: false,
                data: {},
                recommendations: ['Test recommendation'],
              ),
              onRead: () {},
              onArchive: () {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.text('Test Sleep Analysis'), findsOneWidget);
      expect(find.text('Test description'), findsOneWidget);
    });

    testWidgets('InsightsFilterWidget adapts to dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.darkTheme,
          home: Scaffold(
            body: InsightsFilterWidget(
              selectedFilter: null,
              showArchived: false,
              onFilterChanged: (filter) {},
              onArchivedToggled: (show) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.text('Filter Insights'), findsOneWidget);
      expect(find.text('All'), findsOneWidget);
    });

    test('Theme colors are properly defined', () {
      // Test light theme colors
      expect(AppTheme.cardLight, equals(AppTheme.surfaceLight));
      expect(AppTheme.cardDark, equals(AppTheme.surfaceDark));
      
      // Test dark theme colors are different from light theme
      expect(AppTheme.backgroundDark, isNot(equals(AppTheme.backgroundLight)));
      expect(AppTheme.surfaceDark, isNot(equals(AppTheme.surfaceLight)));
      expect(AppTheme.textPrimaryDark, isNot(equals(AppTheme.textPrimaryLight)));
    });
  });
}