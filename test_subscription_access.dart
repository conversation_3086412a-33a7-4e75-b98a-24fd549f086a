import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import 'lib/presentation/subscription/controllers/subscription_controller.dart';
import 'lib/services/feature_access_service.dart';
import 'lib/utils/subscription_access_control.dart';
import 'lib/models/enums.dart';
import 'lib/models/subscription_info.dart';

/// Test widget to verify subscription access control
class SubscriptionAccessTest extends StatefulWidget {
  const SubscriptionAccessTest({super.key});

  @override
  State<SubscriptionAccessTest> createState() => _SubscriptionAccessTestState();
}

class _SubscriptionAccessTestState extends State<SubscriptionAccessTest> {
  final StringBuffer _testResults = StringBuffer();
  bool _isLoading = true;
  bool _isPremium = false;
  String _planName = 'Unknown';
  String _statusMessage = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _runSubscriptionTest();
    });
  }

  Future<void> _runSubscriptionTest() async {
    _testResults.clear();
    _testResults.writeln('🧪 SUBSCRIPTION ACCESS TEST');
    _testResults.writeln('=' * 50);
    _testResults.writeln();

    try {
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      
      // Load subscription data
      await subscriptionController.initialize();
      
      // Get subscription info
      final subscription = subscriptionController.currentSubscription;
      setState(() {
        _isPremium = subscription.isPremium;
        _planName = subscription.planName;
        _statusMessage = subscription.statusMessage;
        _isLoading = false;
      });
      
      _testResults.writeln('✅ Subscription loaded successfully');
      _testResults.writeln('📋 Subscription details:');
      _testResults.writeln('  • Plan: ${subscription.planName}');
      _testResults.writeln('  • Status: ${subscription.status.displayName}');
      _testResults.writeln('  • Is Premium: ${subscription.isPremium}');
      _testResults.writeln('  • Max Family Members: ${subscription.maxFamilyMembers}');
      _testResults.writeln('  • Includes AI Insights: ${subscription.includesAiInsights}');
      _testResults.writeln('  • Includes Data Export: ${subscription.includesDataExport}');
      _testResults.writeln('  • Includes Premium Support: ${subscription.includesPremiumSupport}');
      _testResults.writeln();

      // Test feature access
      _testResults.writeln('🔍 Feature Access Tests:');
      final features = [
        'ai_insights',
        'ai_chat',
        'who_growth_charts',
        'family_sharing',
        'unlimited_profiles',
        'data_export',
        'custom_notifications',
        'premium_support',
        'basic_activity_tracking',
      ];

      for (final feature in features) {
        final hasAccess = SubscriptionAccessControl.hasFeatureAccess(context, feature);
        final message = hasAccess ? '✅ Allowed' : '❌ Blocked';
        _testResults.writeln('  • $feature: $message');
      }
      _testResults.writeln();

      // Test screen access
      _testResults.writeln('🖥️ Screen Access Tests:');
      final screens = [
        'ai_insights',
        'ai_chat',
        'growth_charts',
        'family_sharing',
        'data_export',
      ];

      for (final screen in screens) {
        final canAccess = SubscriptionAccessControl.canAccessScreen(context, screen);
        final message = canAccess ? '✅ Allowed' : '❌ Blocked';
        _testResults.writeln('  • $screen: $message');
      }
      _testResults.writeln();

    } catch (e) {
      _testResults.writeln('❌ Subscription test failed: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Subscription Access Test'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Theme.of(context).colorScheme.onPrimary,
      ),
      body: _isLoading
          ? Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Subscription Status Card
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: _isPremium
                          ? Theme.of(context).colorScheme.primaryContainer
                          : Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          _isPremium ? Icons.star : Icons.star_border,
                          color: _isPremium
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.onSurfaceVariant,
                          size: 32,
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _planName,
                                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                _statusMessage,
                                style: Theme.of(context).textTheme.bodyMedium,
                              ),
                            ],
                          ),
                        ),
                        if (!_isPremium)
                          ElevatedButton(
                            onPressed: () {
                              // Navigate to subscription screen
                            },
                            child: Text('Upgrade'),
                          ),
                      ],
                    ),
                  ),
                  
                  const SizedBox(height: 24),
                  
                  // Test Results
                  Text(
                    'Test Results:',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surfaceVariant,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _testResults.toString(),
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _runSubscriptionTest,
        child: Icon(Icons.refresh),
        tooltip: 'Run Test Again',
      ),
    );
  }
}

/// Example of how to run the test
void main() {
  runApp(
    MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SubscriptionController()),
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<SubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
              previous ?? FeatureAccessService(subscription),
        ),
      ],
      child: MaterialApp(
        title: 'Subscription Test',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: SubscriptionAccessTest(),
      ),
    ),
  );
}