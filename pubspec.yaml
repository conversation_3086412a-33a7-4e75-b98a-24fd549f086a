name: babytracker_pro
description: "A comprehensive baby tracking application with AI insights"
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.2.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # Core packages
  cupertino_icons: ^1.0.2
  sizer: ^2.0.15

  # UI Enhancement
  google_fonts: ^6.1.0
  cached_network_image: ^3.3.1
  flutter_svg: ^2.0.9

  # Data Visualization
  fl_chart: ^1.0.0
  pdf: ^3.11.1
  printing: ^5.12.0

  # Networking
  dio: ^5.7.0
  http: ^1.1.0
  connectivity_plus: ^5.0.2

  # Storage
  shared_preferences: ^2.2.2
  intl: ^0.18.1
  
  # State Management
  provider: ^6.1.1
  
  # Supabase Integration
  supabase_flutter: ^2.3.3
  realtime_client: ^2.0.1

  # Image & Media Handling
  image_picker: ^1.0.8
  image: ^4.2.0
  crop_your_image: ^2.0.0
  gal: ^2.3.0
  path_provider: ^2.1.4
  permission_handler: ^11.3.1

  web: any
  uuid: ^4.5.1
  table_calendar: ^3.0.9
  
  # Device and app info
  device_info_plus: ^10.1.0
  package_info_plus: ^8.0.0
  url_launcher: ^6.2.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - env.json
