/// Simple test to verify the feature access system works
/// Run this with: flutter run test_feature_access_system.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/models/feature_access.dart';
import 'lib/models/subscription_info.dart';
import 'lib/models/enums.dart';
import 'lib/services/feature_access_service.dart';
import 'lib/presentation/subscription/controllers/feature_access_controller.dart';
import 'lib/presentation/subscription/controllers/subscription_controller.dart';
import 'lib/presentation/subscription/widgets/feature_gate.dart';

void main() {
  runApp(FeatureAccessTestApp());
}

class FeatureAccessTestApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SubscriptionController()),
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<SubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
            previous ?? FeatureAccessService(subscription),
        ),
        ChangeNotifierProxyProvider<FeatureAccessService, FeatureAccessController>(
          create: (context) => FeatureAccessController(
            Provider.of<FeatureAccessService>(context, listen: false),
          ),
          update: (context, service, previous) => 
            previous ?? FeatureAccessController(service),
        ),
      ],
      child: MaterialApp(
        title: 'Feature Access Test',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: FeatureAccessTestScreen(),
      ),
    );
  }
}

class FeatureAccessTestScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Feature Access System Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<FeatureAccessController>(
        builder: (context, controller, child) {
          return ListView(
            padding: EdgeInsets.all(16),
            children: [
              // Test subscription status toggle
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Subscription Status',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      SizedBox(height: 8),
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: () => _setSubscriptionStatus(context, SubscriptionStatus.free),
                            child: Text('Free'),
                          ),
                          SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: () => _setSubscriptionStatus(context, SubscriptionStatus.active),
                            child: Text('Premium'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // Test each feature
              ...AppFeature.values.map((feature) => Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(feature.icon),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              feature.displayName,
                              style: Theme.of(context).textTheme.titleMedium,
                            ),
                          ),
                          _buildAccessChip(context, controller.canAccessFeature(feature)),
                        ],
                      ),
                      SizedBox(height: 8),
                      Text(
                        feature.description,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                      SizedBox(height: 12),
                      
                      // Test feature gate
                      FeatureGate(
                        feature: feature,
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.green),
                              SizedBox(width: 8),
                              Text('✅ Feature Available'),
                            ],
                          ),
                        ),
                        onUpgrade: () => _showUpgradeDialog(context, feature),
                      ),
                    ],
                  ),
                ),
              )),
            ],
          );
        },
      ),
    );
  }
  
  Widget _buildAccessChip(BuildContext context, bool hasAccess) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: hasAccess ? Colors.green : Colors.red,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        hasAccess ? 'Available' : 'Restricted',
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
  
  void _setSubscriptionStatus(BuildContext context, SubscriptionStatus status) {
    final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
    
    // Create a mock subscription with the new status
    final mockSubscription = SubscriptionInfo(
      planId: status == SubscriptionStatus.active ? 'premium' : 'free',
      planName: status == SubscriptionStatus.active ? 'Premium' : 'Free',
      status: status,
      renewalDate: status == SubscriptionStatus.active ? DateTime.now().add(Duration(days: 30)) : null,
      monthlyPrice: status == SubscriptionStatus.active ? 9.99 : 0.0,
      features: status == SubscriptionStatus.active 
        ? ['Unlimited everything', 'Premium support']
        : ['Basic tracking'],
      isTrialActive: false,
      trialEndsAt: null,
      paymentMethod: null,
      maxFamilyMembers: status == SubscriptionStatus.active ? 10 : 1,
      includesAiInsights: status == SubscriptionStatus.active,
      includesDataExport: status == SubscriptionStatus.active,
      includesPremiumSupport: status == SubscriptionStatus.active,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    // Update the subscription (you'll need to add this method to SubscriptionController)
    // subscriptionController.updateSubscription(mockSubscription);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Subscription set to: ${status.displayName}'),
        backgroundColor: status.isPremium ? Colors.green : Colors.orange,
      ),
    );
  }
  
  void _showUpgradeDialog(BuildContext context, AppFeature feature) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Upgrade Required'),
        content: Text('${feature.displayName} requires a Premium subscription.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text('Redirecting to subscription screen...')),
              );
            },
            child: Text('Upgrade'),
          ),
        ],
      ),
    );
  }
}