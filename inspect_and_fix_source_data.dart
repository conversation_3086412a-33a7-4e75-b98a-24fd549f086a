import 'dart:io';
import 'dart:convert';

Future<void> main() async {
  print('🔍 Inspecting Source AI Insights Data...\n');
  
  // Read environment configuration
  final envFile = File('env.json');
  if (!envFile.existsSync()) {
    print('❌ Error: env.json file not found');
    exit(1);
  }
  
  final envContent = await envFile.readAsString();
  final envConfig = jsonDecode(envContent);
  
  final supabaseUrl = envConfig['SUPABASE_URL'];
  final supabaseKey = envConfig['SUPABASE_ANON_KEY'];
  
  if (supabaseUrl == null || supabaseKey == null) {
    print('❌ Error: Supabase credentials not found in env.json');
    exit(1);
  }
  
  const babyId = 'c5959165-09bb-4aa5-8149-42c12b17f3c3';
  
  try {
    print('🔍 Step 1: Check ai_insights table for problematic records...');
    
    final checkAiInsights = await Process.run('curl', [
      '-H', 'apikey: $supabaseKey',
      '-H', 'Authorization: Bearer $supabaseKey',
      '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
    ]);
    
    if (checkAiInsights.exitCode == 0) {
      final response = checkAiInsights.stdout.toString();
      if (response.trim() != '[]') {
        print('🔍 Found AI insights records:');
        print(response);
        print('\n🗑️ Deleting these records...');
        
        final deleteResult = await Process.run('curl', [
          '-X', 'DELETE',
          '-H', 'apikey: $supabaseKey',
          '-H', 'Authorization: Bearer $supabaseKey',
          '$supabaseUrl/rest/v1/ai_insights?baby_id=eq.$babyId'
        ]);
        
        if (deleteResult.exitCode == 0) {
          print('✅ Deleted ai_insights records');
        }
      } else {
        print('✅ No records in ai_insights table');
      }
    }
    
    print('\n🔍 Step 2: Check for any other tables with timestamp issues...');
    
    // Check if there are any cached insight records in other tables
    final possibleTables = [
      'insights_cache',
      'baby_insights',
      'cached_ai_insights',
      'ai_analysis_cache',
      'analysis_results'
    ];
    
    for (final table in possibleTables) {
      print('   Checking $table...');
      final checkTable = await Process.run('curl', [
        '-H', 'apikey: $supabaseKey',
        '-H', 'Authorization: Bearer $supabaseKey',
        '$supabaseUrl/rest/v1/$table?baby_id=eq.$babyId'
      ]);
      
      if (checkTable.exitCode == 0) {
        final response = checkTable.stdout.toString();
        if (!response.contains('does not exist') && 
            !response.contains('relation') && 
            !response.contains('404') && 
            response.trim() != '[]') {
          print('   🔍 Found data in $table:');
          print('   $response');
          
          // Delete the problematic records
          final deleteTable = await Process.run('curl', [
            '-X', 'DELETE',
            '-H', 'apikey: $supabaseKey',
            '-H', 'Authorization: Bearer $supabaseKey',
            '$supabaseUrl/rest/v1/$table?baby_id=eq.$babyId'
          ]);
          
          if (deleteTable.exitCode == 0) {
            print('   ✅ Deleted records from $table');
          }
        } else {
          print('   ✅ No problematic data in $table');
        }
      }
    }
    
    print('\n🔍 Step 3: Look for ANY records with the problematic timestamp...');
    
    // Search for the specific problematic timestamp across tables
    const problematicTimestamp = '2025-07-07 19:18:25.160978Z';
    const problemTimestampVariants = [
      '2025-07-07T19:18:25.160978Z',
      '2025-07-07 19:18:25.160978',
      '2025-07-07T19:18:25',
    ];
    
    for (final variant in problemTimestampVariants) {
      print('   Searching for timestamp: $variant');
      
      // Try to find this timestamp in ai_insights
      final searchTimestamp = await Process.run('curl', [
        '-H', 'apikey: $supabaseKey',
        '-H', 'Authorization: Bearer $supabaseKey',
        '$supabaseUrl/rest/v1/ai_insights?select=*&or=(created_at.eq.$variant,updated_at.eq.$variant)'
      ]);
      
      if (searchTimestamp.exitCode == 0) {
        final response = searchTimestamp.stdout.toString();
        if (response.trim() != '[]') {
          print('   🔍 Found records with problematic timestamp:');
          print('   $response');
          
          // Delete these specific records
          final deleteSpecific = await Process.run('curl', [
            '-X', 'DELETE',
            '-H', 'apikey: $supabaseKey',
            '-H', 'Authorization: Bearer $supabaseKey',
            '$supabaseUrl/rest/v1/ai_insights?or=(created_at.eq.$variant,updated_at.eq.$variant)'
          ]);
          
          if (deleteSpecific.exitCode == 0) {
            print('   ✅ Deleted records with problematic timestamp');
          }
        }
      }
    }
    
    print('\n🔄 Step 4: Force clear app data again to ensure clean state...');
    
    final clearAppData = await Process.run('adb', [
      'shell',
      'pm',
      'clear',
      'com.babytracker_pro.app'
    ]);
    
    if (clearAppData.exitCode == 0) {
      print('✅ Cleared app data again');
    } else {
      print('⚠️ Could not clear app data: ${clearAppData.stderr}');
    }
    
    print('\n🎉 COMPREHENSIVE CLEANUP COMPLETE');
    print('   ✅ All database records with problematic timestamps deleted');
    print('   ✅ App data cleared to remove any local cache');
    print('   ✅ Next app start will generate completely fresh insights');
    print('\n📱 Next steps:');
    print('   1. Restart the Flutter app');
    print('   2. Sign in to your account');
    print('   3. Fresh AI insights will be generated with current timestamp');
    print('   4. The "Last updated: just now" issue should be permanently resolved');
    
  } catch (e) {
    print('❌ Error: $e');
    exit(1);
  }
}
