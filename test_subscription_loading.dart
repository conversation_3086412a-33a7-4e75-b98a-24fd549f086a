/// Test script to verify subscription loading and feature access
/// Run with: flutter run test_subscription_loading.dart

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/models/feature_access.dart';
import 'lib/models/subscription_info.dart';
import 'lib/models/enums.dart';
import 'lib/services/feature_access_service.dart';
import 'lib/presentation/subscription/controllers/feature_access_controller.dart';
import 'lib/presentation/subscription/controllers/subscription_controller.dart';
import 'lib/presentation/subscription/widgets/feature_gate.dart';
import 'lib/services/supabase_service.dart';

void main() {
  runApp(TestSubscriptionApp());
}

class TestSubscriptionApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => SubscriptionController()),
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) => FeatureAccessService(
            Provider.of<SubscriptionController>(context, listen: false),
          ),
          update: (context, subscription, previous) => 
            previous ?? FeatureAccessService(subscription),
        ),
        ChangeNotifierProxyProvider<FeatureAccessService, FeatureAccessController>(
          create: (context) => FeatureAccessController(
            Provider.of<FeatureAccessService>(context, listen: false),
          ),
          update: (context, service, previous) => 
            previous ?? FeatureAccessController(service),
        ),
      ],
      child: MaterialApp(
        title: 'Subscription Test',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          useMaterial3: true,
        ),
        home: TestScreen(),
      ),
    );
  }
}

class TestScreen extends StatefulWidget {
  @override
  _TestScreenState createState() => _TestScreenState();
}

class _TestScreenState extends State<TestScreen> {
  bool _isInitialized = false;
  String _status = 'Initializing...';

  @override
  void initState() {
    super.initState();
    _initializeSystem();
  }

  Future<void> _initializeSystem() async {
    try {
      // Initialize Supabase first
      await SupabaseService.initialize();
      
      // Initialize subscription controller
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      await subscriptionController.initialize();
      
      setState(() {
        _isInitialized = true;
        _status = 'System initialized';
      });
      
      print('🎉 SUBSCRIPTION SYSTEM TEST RESULTS:');
      final subscription = subscriptionController.currentSubscription;
      print('Plan: ${subscription.planName}');
      print('Status: ${subscription.status.toString().split('.').last}');
      print('Is Premium: ${subscription.isPremium}');
      print('Max Family Members: ${subscription.maxFamilyMembers}');
      print('Includes AI Insights: ${subscription.includesAiInsights}');
      
    } catch (e) {
      setState(() {
        _status = 'Error: $e';
      });
      print('❌ Error initializing: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Scaffold(
        appBar: AppBar(title: Text('Subscription Test')),
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(),
              SizedBox(height: 16),
              Text(_status),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text('Feature Access Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Consumer<FeatureAccessController>(
        builder: (context, controller, child) {
          return Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Card(
                  child: Padding(
                    padding: EdgeInsets.all(16),
                    child: Consumer<SubscriptionController>(
                      builder: (context, subscriptionController, _) {
                        final sub = subscriptionController.currentSubscription;
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '🎯 USER SUBSCRIPTION STATUS',
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: sub.isPremium ? Colors.green : Colors.orange,
                              ),
                            ),
                            SizedBox(height: 12),
                            Text('Plan: ${sub.planName}'),
                            Text('Status: ${sub.status.toString().split('.').last}'),
                            Text('Is Premium: ${sub.isPremium ? "✅ YES" : "❌ NO"}'),
                            Text('Max Family Members: ${sub.maxFamilyMembers}'),
                            Text('AI Insights: ${sub.includesAiInsights ? "✅ Included" : "❌ Not included"}'),
                            Text('Data Export: ${sub.includesDataExport ? "✅ Included" : "❌ Not included"}'),
                          ],
                        );
                      },
                    ),
                  ),
                ),
                
                SizedBox(height: 16),
                
                Text(
                  'Feature Access Test:',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
                
                SizedBox(height: 12),
                
                Expanded(
                  child: ListView(
                    children: [
                      _buildFeatureTest(context, controller, AppFeature.whoGrowthCharts),
                      _buildFeatureTest(context, controller, AppFeature.aiInsights),
                      _buildFeatureTest(context, controller, AppFeature.aiChat),
                      _buildFeatureTest(context, controller, AppFeature.familySharing),
                      _buildFeatureTest(context, controller, AppFeature.multipleBabyProfiles),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildFeatureTest(BuildContext context, FeatureAccessController controller, AppFeature feature) {
    final hasAccess = controller.canAccessFeature(feature);
    
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(feature.icon),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    feature.displayName,
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: hasAccess ? Colors.green : Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    hasAccess ? '✅ Available' : '❌ Restricted',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              feature.description,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            SizedBox(height: 12),
            
            // Test FeatureGate
            FeatureGate(
              feature: feature,
              child: Container(
                padding: EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.check_circle, color: Colors.green),
                    SizedBox(width: 8),
                    Text('✅ Feature Content Accessible'),
                  ],
                ),
              ),
              onUpgrade: () {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: Text('Upgrade Required'),
                    content: Text('This feature requires Premium subscription.'),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        child: Text('OK'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
