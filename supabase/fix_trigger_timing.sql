-- Fix trigger timing issue - change from <PERSON><PERSON><PERSON><PERSON> to AFTER trigger
-- This ensures the baby_profiles record exists before creating baby_user_access

BEGIN;

-- Drop the existing BEFORE trigger
DROP TRIGGER IF EXISTS sync_baby_user_access_trigger ON public.baby_profiles;

-- Create the sync function as <PERSON><PERSON><PERSON> trigger
CREATE OR REPLACE FUNCTION sync_baby_user_access()
RETURNS TRIGGER AS $$
BEGIN
    -- Only handle INSERT operations in AFTER trigger
    IF TG_OP = 'INSERT' THEN
        -- Create baby_user_access record for the creator
        IF NEW.user_id IS NOT NULL THEN
            INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
            VALUES (NEW.id, NEW.user_id, 'owner')
            ON CONFLICT (baby_id, user_id) DO NOTHING;
        -- If no user_id but created_by exists, use that
        ELSIF NEW.created_by IS NOT NULL THEN
            INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
            VALUES (NEW.id, NEW.created_by, 'owner')
            ON CONFLICT (baby_id, user_id) DO NOTHING;
        END IF;
        
        RETURN NEW;
    END IF;
    
    -- Handle UPDATE operations
    IF TG_OP = 'UPDATE' THEN
        -- If user_id changed, update baby_user_access
        IF OLD.user_id IS DISTINCT FROM NEW.user_id THEN
            -- Remove old access if exists
            IF OLD.user_id IS NOT NULL THEN
                DELETE FROM public.baby_user_access 
                WHERE baby_id = NEW.id AND user_id = OLD.user_id;
            END IF;
            
            -- Add new access if new user_id exists
            IF NEW.user_id IS NOT NULL THEN
                INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
                VALUES (NEW.id, NEW.user_id, 'owner')
                ON CONFLICT (baby_id, user_id) DO UPDATE SET access_level = 'owner';
            END IF;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create AFTER trigger instead of BEFORE trigger
CREATE TRIGGER sync_baby_user_access_trigger
    AFTER INSERT OR UPDATE ON public.baby_profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_baby_user_access();

-- Also create a separate BEFORE trigger just for setting user_id from created_by
CREATE OR REPLACE FUNCTION set_user_id_from_created_by()
RETURNS TRIGGER AS $$
BEGIN
    -- Set user_id from created_by if not already set
    IF NEW.user_id IS NULL AND NEW.created_by IS NOT NULL THEN
        NEW.user_id = NEW.created_by;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create BEFORE trigger for setting user_id
DROP TRIGGER IF EXISTS set_user_id_trigger ON public.baby_profiles;
CREATE TRIGGER set_user_id_trigger
    BEFORE INSERT ON public.baby_profiles
    FOR EACH ROW
    EXECUTE FUNCTION set_user_id_from_created_by();

COMMIT;
