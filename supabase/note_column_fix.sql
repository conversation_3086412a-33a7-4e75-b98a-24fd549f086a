-- Quick fix to add missing 'note' column to baby_profiles
-- The app expects 'note' but our schema has 'notes'

BEGIN;

-- Add the missing 'note' column
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS note TEXT;

-- Create function to sync 'note' and 'notes' columns
CREATE OR REPLACE FUNCTION sync_note_columns()
R<PERSON><PERSON>NS TRIGGER AS $$
BEGIN
    -- If note is provided but notes is not, copy it
    IF NEW.note IS NOT NULL AND NEW.notes IS NULL THEN
        NEW.notes = NEW.note;
    END IF;
    
    -- If notes is provided but note is not, copy it
    IF NEW.notes IS NOT NULL AND NEW.note IS NULL THEN
        NEW.note = NEW.notes;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to sync note columns
DROP TRIGGER IF EXISTS sync_note_columns_trigger ON public.baby_profiles;
CREATE TRIGGER sync_note_columns_trigger
    BEFORE INSERT OR UPDATE ON public.baby_profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_note_columns();

COMMIT;
