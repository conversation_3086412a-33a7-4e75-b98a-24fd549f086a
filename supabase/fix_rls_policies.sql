-- Fix RLS policies to allow baby profile creation
-- The current RLS policies are too restrictive for baby creation

BEGIN;

-- Drop existing restrictive policies
DROP POLICY IF EXISTS baby_user_access_policy ON public.baby_user_access;
DROP POLICY IF EXISTS baby_profiles_policy ON public.baby_profiles;

-- Create more permissive policies for baby_user_access
-- Allow users to insert records for themselves
CREATE POLICY baby_user_access_insert_policy ON public.baby_user_access
    FOR INSERT
    WITH CHECK (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = baby_user_access.user_id
        AND up.auth_id = auth.uid()
    ));

-- Allow users to view records where they have access
CREATE POLICY baby_user_access_select_policy ON public.baby_user_access
    FOR SELECT
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = baby_user_access.user_id
        AND up.auth_id = auth.uid()
    ));

-- Allow users to update records where they have access
CREATE POLICY baby_user_access_update_policy ON public.baby_user_access
    FOR UPDATE
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = baby_user_access.user_id
        AND up.auth_id = auth.uid()
    ));

-- Allow users to delete records where they have access
CREATE POLICY baby_user_access_delete_policy ON public.baby_user_access
    FOR DELETE
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = baby_user_access.user_id
        AND up.auth_id = auth.uid()
    ));

-- Create more permissive policies for baby_profiles
-- Allow users to insert baby profiles
CREATE POLICY baby_profiles_insert_policy ON public.baby_profiles
    FOR INSERT
    WITH CHECK (
        -- User can insert if they are the creator or user_id matches their profile
        (created_by IS NULL OR EXISTS (
            SELECT 1 FROM public.user_profiles up
            WHERE up.id = baby_profiles.created_by
            AND up.auth_id = auth.uid()
        )) AND
        (user_id IS NULL OR EXISTS (
            SELECT 1 FROM public.user_profiles up
            WHERE up.id = baby_profiles.user_id
            AND up.auth_id = auth.uid()
        ))
    );

-- Allow users to view baby profiles they have access to
CREATE POLICY baby_profiles_select_policy ON public.baby_profiles
    FOR SELECT
    USING (
        -- User can view if they have access through baby_user_access table
        EXISTS (
            SELECT 1 FROM public.baby_user_access bua
            JOIN public.user_profiles up ON bua.user_id = up.id
            WHERE bua.baby_id = baby_profiles.id
            AND up.auth_id = auth.uid()
        )
        OR
        -- Or if they are the direct user_id (backwards compatibility)
        EXISTS (
            SELECT 1 FROM public.user_profiles up
            WHERE up.id = baby_profiles.user_id
            AND up.auth_id = auth.uid()
        )
    );

-- Allow users to update baby profiles they have access to
CREATE POLICY baby_profiles_update_policy ON public.baby_profiles
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.baby_user_access bua
            JOIN public.user_profiles up ON bua.user_id = up.id
            WHERE bua.baby_id = baby_profiles.id
            AND up.auth_id = auth.uid()
            AND bua.access_level IN ('owner', 'editor')
        )
        OR
        EXISTS (
            SELECT 1 FROM public.user_profiles up
            WHERE up.id = baby_profiles.user_id
            AND up.auth_id = auth.uid()
        )
    );

-- Allow users to delete baby profiles they own
CREATE POLICY baby_profiles_delete_policy ON public.baby_profiles
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.baby_user_access bua
            JOIN public.user_profiles up ON bua.user_id = up.id
            WHERE bua.baby_id = baby_profiles.id
            AND up.auth_id = auth.uid()
            AND bua.access_level = 'owner'
        )
        OR
        EXISTS (
            SELECT 1 FROM public.user_profiles up
            WHERE up.id = baby_profiles.user_id
            AND up.auth_id = auth.uid()
        )
    );

-- Update the sync function to use SECURITY DEFINER to bypass RLS during triggers
CREATE OR REPLACE FUNCTION sync_baby_user_access()
RETURNS TRIGGER AS $$
BEGIN
    -- When a baby is created, automatically create baby_user_access record
    IF TG_OP = 'INSERT' THEN
        -- Set user_id from created_by if not already set
        IF NEW.user_id IS NULL AND NEW.created_by IS NOT NULL THEN
            NEW.user_id = NEW.created_by;
        END IF;
        
        -- Create baby_user_access record for the creator
        IF NEW.user_id IS NOT NULL THEN
            INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
            VALUES (NEW.id, NEW.user_id, 'owner')
            ON CONFLICT (baby_id, user_id) DO NOTHING;
        END IF;
        
        RETURN NEW;
    END IF;
    
    -- When a baby is updated, sync the user_id
    IF TG_OP = 'UPDATE' THEN
        -- If user_id changed, update baby_user_access
        IF OLD.user_id IS DISTINCT FROM NEW.user_id THEN
            -- Remove old access if exists
            IF OLD.user_id IS NOT NULL THEN
                DELETE FROM public.baby_user_access 
                WHERE baby_id = NEW.id AND user_id = OLD.user_id;
            END IF;
            
            -- Add new access if new user_id exists
            IF NEW.user_id IS NOT NULL THEN
                INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
                VALUES (NEW.id, NEW.user_id, 'owner')
                ON CONFLICT (baby_id, user_id) DO UPDATE SET access_level = 'owner';
            END IF;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

COMMIT;
