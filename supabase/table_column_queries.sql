-- SQL queries to check table columns and structure

-- ========================================
-- QUERY ALL COLUMNS FOR BABY_PROFILES TABLE
-- ========================================

-- Get all columns with their data types for baby_profiles
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'baby_profiles'
ORDER BY ordinal_position;

-- ========================================
-- QUERY ALL COLUMNS FOR BABY_USER_ACCESS TABLE
-- ========================================

-- Get all columns with their data types for baby_user_access
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'baby_user_access'
ORDER BY ordinal_position;

-- ========================================
-- QUERY ALL COLUMNS FOR USER_PROFILES TABLE
-- ========================================

-- Get all columns with their data types for user_profiles
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'user_profiles'
ORDER BY ordinal_position;

-- ========================================
-- QUERY ALL COLUMNS FOR ANY TABLE (REPLACE 'table_name')
-- ========================================

-- Template query - replace 'your_table_name' with actual table name
-- SELECT 
--     column_name,
--     data_type,
--     is_nullable,
--     column_default,
--     character_maximum_length,
--     numeric_precision,
--     numeric_scale
-- FROM information_schema.columns 
-- WHERE table_schema = 'public' 
-- AND table_name = 'your_table_name'
-- ORDER BY ordinal_position;

-- ========================================
-- QUERY ALL TABLES IN PUBLIC SCHEMA
-- ========================================

-- Get all tables in public schema
SELECT 
    table_name,
    table_type,
    is_insertable_into
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- ========================================
-- QUERY FOREIGN KEY CONSTRAINTS
-- ========================================

-- Check foreign key constraints for baby_user_access table
SELECT 
    tc.constraint_name,
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc 
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name = 'baby_user_access';

-- ========================================
-- QUERY TRIGGERS ON BABY_PROFILES TABLE
-- ========================================

-- Check all triggers on baby_profiles table
SELECT 
    trigger_name,
    event_object_table as table_name,
    action_timing,
    event_manipulation,
    action_statement
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND event_object_table = 'baby_profiles'
ORDER BY trigger_name;

-- ========================================
-- SAMPLE DATA QUERIES (OPTIONAL)
-- ========================================

-- Check if there are any baby_profiles records
SELECT COUNT(*) as baby_profiles_count FROM public.baby_profiles;

-- Check if there are any baby_user_access records
SELECT COUNT(*) as baby_user_access_count FROM public.baby_user_access;

-- Check if there are any user_profiles records
SELECT COUNT(*) as user_profiles_count FROM public.user_profiles;

-- Show first few records from baby_profiles (if any)
SELECT * FROM public.baby_profiles LIMIT 5;

-- Show first few records from baby_user_access (if any)
SELECT * FROM public.baby_user_access LIMIT 5;

-- Show first few records from user_profiles (if any)
SELECT * FROM public.user_profiles LIMIT 5;
