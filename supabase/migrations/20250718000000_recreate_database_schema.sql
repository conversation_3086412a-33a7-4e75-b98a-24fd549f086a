-- Complete database schema recreation
-- This migration recreates all tables that were lost

BEGIN;

-- Create user_profiles table
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    display_name TEXT,
    avatar_url TEXT,
    preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create baby_profiles table
CREATE TABLE IF NOT EXISTS public.baby_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    birth_date DATE,
    gender TEXT CHECK (gender IN ('male', 'female', 'other')),
    birth_weight DECIMAL(6,3),
    birth_length DECIMAL(6,2),
    birth_head_circumference DECIMAL(6,2),
    profile_image_url TEXT,
    notes TEXT,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create baby_user_access table for multi-carer support
CREATE TABLE IF NOT EXISTS public.baby_user_access (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    access_level TEXT CHECK (access_level IN ('owner', 'editor', 'viewer')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (baby_id, user_id)
);

-- Create activity_types enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'activity_type') THEN
        CREATE TYPE activity_type AS ENUM (
            'feeding', 'breast_feeding', 'bottle_feeding', 'formula_feeding', 'solid_feeding',
            'sleep', 'diaper', 'bath', 'medicine', 'temperature', 'growth', 'milestone',
            'note', 'photo', 'vaccination', 'doctor_visit', 'play', 'tummy_time',
            'pumping', 'walk', 'other'
        );
    END IF;
END $$;

-- Create scheduled_activity_type enum if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'scheduled_activity_type') THEN
        CREATE TYPE scheduled_activity_type AS ENUM (
            'feeding', 'breast_feeding', 'bottle_feeding', 'formula_feeding', 'solid_feeding',
            'sleep', 'diaper', 'bath', 'medicine', 'temperature', 'doctor_visit', 'play',
            'tummy_time', 'pumping', 'walk', 'other', 'shopping_trip', 'nap_time', 
            'meal_time', 'walk_time'
        );
    END IF;
END $$;

-- Create activity_logs table
CREATE TABLE IF NOT EXISTS public.activity_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    activity_type activity_type NOT NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER,
    details JSONB DEFAULT '{}',
    notes TEXT,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create scheduled_activities table
CREATE TABLE IF NOT EXISTS public.scheduled_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    activity_type scheduled_activity_type NOT NULL,
    scheduled_time TIMESTAMP WITH TIME ZONE NOT NULL,
    duration_minutes INTEGER,
    details JSONB DEFAULT '{}',
    notes TEXT,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern JSONB,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create growth_measurements table
CREATE TABLE IF NOT EXISTS public.growth_measurements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    measurement_type TEXT NOT NULL CHECK (measurement_type IN ('weight', 'length', 'height', 'head_circumference')),
    value DECIMAL(6,3) NOT NULL,
    unit TEXT NOT NULL,
    percentile DECIMAL(5,2),
    measured_at TIMESTAMP WITH TIME ZONE NOT NULL,
    notes TEXT,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    z_score DECIMAL(6,3),
    age_in_months DECIMAL(6,2),
    velocity_data JSONB,
    percentile_analysis JSONB,
    flagged_for_review BOOLEAN DEFAULT FALSE,
    validation_results JSONB,
    metadata JSONB DEFAULT '{}'
);

-- Create milestones table
CREATE TABLE IF NOT EXISTS public.milestones (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    milestone_category TEXT NOT NULL,
    milestone_name TEXT NOT NULL,
    achieved_at TIMESTAMP WITH TIME ZONE,
    expected_age_range JSONB,
    notes TEXT,
    photo_url TEXT,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create medicine_logs table
CREATE TABLE IF NOT EXISTS public.medicine_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    medicine_name TEXT NOT NULL,
    dosage DECIMAL(6,2),
    dosage_unit TEXT,
    administered_at TIMESTAMP WITH TIME ZONE NOT NULL,
    reason TEXT,
    notes TEXT,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create vaccination_logs table
CREATE TABLE IF NOT EXISTS public.vaccination_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    vaccine_name TEXT NOT NULL,
    dose_number INTEGER,
    administered_at TIMESTAMP WITH TIME ZONE NOT NULL,
    administered_by TEXT,
    location TEXT,
    notes TEXT,
    next_dose_due DATE,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create ai_insights table
CREATE TABLE IF NOT EXISTS public.ai_insights (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    insight_type TEXT NOT NULL,
    insight_text TEXT NOT NULL,
    generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    data_range_start TIMESTAMP WITH TIME ZONE,
    data_range_end TIMESTAMP WITH TIME ZONE,
    source_data JSONB,
    is_read BOOLEAN DEFAULT FALSE,
    is_saved BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create chat_messages table
CREATE TABLE IF NOT EXISTS public.chat_messages (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    baby_id UUID REFERENCES public.baby_profiles(id) ON DELETE CASCADE,
    message_text TEXT NOT NULL,
    is_from_user BOOLEAN NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    context_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic updated_at updates
DO $$ 
DECLARE
    t text;
BEGIN
    FOR t IN 
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN (
            'user_profiles', 'baby_profiles', 'baby_user_access', 
            'activity_logs', 'scheduled_activities', 'growth_measurements',
            'milestones', 'medicine_logs', 'vaccination_logs',
            'ai_insights', 'chat_messages'
        )
    LOOP
        EXECUTE format('DROP TRIGGER IF EXISTS update_%s_updated_at ON public.%s', t, t);
        EXECUTE format('CREATE TRIGGER update_%s_updated_at
                        BEFORE UPDATE ON public.%s
                        FOR EACH ROW
                        EXECUTE FUNCTION update_updated_at_column()', t, t);
    END LOOP;
END $$;

-- Create function to calculate age in months from birth date and measurement date
CREATE OR REPLACE FUNCTION calculate_age_in_months(birth_date DATE, measurement_date TIMESTAMP WITH TIME ZONE)
RETURNS DECIMAL(6,2) AS $$
BEGIN
    RETURN EXTRACT(EPOCH FROM (measurement_date::DATE - birth_date)) / (30.44 * 24 * 3600);
END;
$$ LANGUAGE plpgsql;

-- Create function to automatically calculate and update age_in_months
CREATE OR REPLACE FUNCTION auto_calculate_age_in_months()
RETURNS TRIGGER AS $$
DECLARE
    baby_birth_date DATE;
BEGIN
    -- Get baby's birth date
    SELECT birth_date INTO baby_birth_date
    FROM public.baby_profiles
    WHERE id = NEW.baby_id;
    
    -- Calculate age in months if not provided
    IF NEW.age_in_months IS NULL AND baby_birth_date IS NOT NULL THEN
        NEW.age_in_months = calculate_age_in_months(baby_birth_date, NEW.measured_at);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically calculate age_in_months
DROP TRIGGER IF EXISTS auto_calculate_age_trigger ON public.growth_measurements;
CREATE TRIGGER auto_calculate_age_trigger
    BEFORE INSERT OR UPDATE ON public.growth_measurements
    FOR EACH ROW
    EXECUTE FUNCTION auto_calculate_age_in_months();

-- Create view for enhanced measurement analysis
CREATE OR REPLACE VIEW public.growth_measurements_enhanced AS
SELECT 
    gm.*,
    bp.name as baby_name,
    bp.gender as baby_gender,
    bp.birth_date,
    CASE 
        WHEN gm.percentile IS NOT NULL THEN
            CASE 
                WHEN gm.percentile < 3 THEN 'Below Normal'
                WHEN gm.percentile < 10 THEN 'Low Normal'
                WHEN gm.percentile < 25 THEN 'Lower Average'
                WHEN gm.percentile <= 75 THEN 'Average'
                WHEN gm.percentile <= 90 THEN 'Higher Average'
                WHEN gm.percentile <= 97 THEN 'High Normal'
                ELSE 'Above Normal'
            END
        ELSE 'Not Calculated'
    END as percentile_category,
    CASE 
        WHEN gm.percentile IS NOT NULL AND (gm.percentile < 3 OR gm.percentile > 97) THEN TRUE
        WHEN gm.z_score IS NOT NULL AND ABS(gm.z_score) > 2 THEN TRUE
        WHEN gm.flagged_for_review = TRUE THEN TRUE
        ELSE FALSE
    END as requires_attention
FROM public.growth_measurements gm
JOIN public.baby_profiles bp ON gm.baby_id = bp.id;

-- Create function to get measurements requiring attention
CREATE OR REPLACE FUNCTION get_measurements_requiring_attention(p_baby_id UUID DEFAULT NULL)
RETURNS TABLE (
    id UUID,
    baby_id UUID,
    baby_name TEXT,
    measurement_type TEXT,
    value DECIMAL,
    unit TEXT,
    percentile DECIMAL,
    z_score DECIMAL,
    measured_at TIMESTAMP WITH TIME ZONE,
    reason TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        gme.id,
        gme.baby_id,
        gme.baby_name,
        gme.measurement_type,
        gme.value,
        gme.unit,
        gme.percentile,
        gme.z_score,
        gme.measured_at,
        CASE 
            WHEN gme.flagged_for_review THEN 'Flagged for review'
            WHEN gme.percentile < 3 THEN 'Below 3rd percentile'
            WHEN gme.percentile > 97 THEN 'Above 97th percentile'
            WHEN ABS(gme.z_score) > 2 THEN 'Extreme Z-score'
            ELSE 'Requires attention'
        END as reason
    FROM public.growth_measurements_enhanced gme
    WHERE gme.requires_attention = TRUE
      AND (p_baby_id IS NULL OR gme.baby_id = p_baby_id)
    ORDER BY gme.measured_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activity_logs_baby_id ON public.activity_logs(baby_id);
CREATE INDEX IF NOT EXISTS idx_activity_logs_activity_type ON public.activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_activity_logs_start_time ON public.activity_logs(start_time);

CREATE INDEX IF NOT EXISTS idx_growth_measurements_baby_id ON public.growth_measurements(baby_id);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_measurement_type ON public.growth_measurements(measurement_type);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_measured_at ON public.growth_measurements(measured_at);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_z_score ON public.growth_measurements(z_score);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_age_in_months ON public.growth_measurements(age_in_months);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_flagged_for_review ON public.growth_measurements(flagged_for_review);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_updated_at ON public.growth_measurements(updated_at);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_baby_type_age ON public.growth_measurements(baby_id, measurement_type, age_in_months);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_baby_flagged ON public.growth_measurements(baby_id, flagged_for_review);

CREATE INDEX IF NOT EXISTS idx_milestones_baby_id ON public.milestones(baby_id);
CREATE INDEX IF NOT EXISTS idx_milestones_achieved_at ON public.milestones(achieved_at);
CREATE INDEX IF NOT EXISTS idx_milestones_category ON public.milestones(milestone_category);

CREATE INDEX IF NOT EXISTS idx_medicine_logs_baby_id ON public.medicine_logs(baby_id);
CREATE INDEX IF NOT EXISTS idx_medicine_logs_administered_at ON public.medicine_logs(administered_at);

CREATE INDEX IF NOT EXISTS idx_vaccination_logs_baby_id ON public.vaccination_logs(baby_id);
CREATE INDEX IF NOT EXISTS idx_vaccination_logs_administered_at ON public.vaccination_logs(administered_at);

CREATE INDEX IF NOT EXISTS idx_ai_insights_baby_id ON public.ai_insights(baby_id);
CREATE INDEX IF NOT EXISTS idx_ai_insights_generated_at ON public.ai_insights(generated_at);
CREATE INDEX IF NOT EXISTS idx_ai_insights_is_read ON public.ai_insights(is_read);

-- Set up Row Level Security (RLS)
-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.baby_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.baby_user_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.scheduled_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.growth_measurements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.milestones ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.medicine_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.vaccination_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.ai_insights ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.chat_messages ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
-- User profiles policy
CREATE POLICY user_profiles_policy ON public.user_profiles
    USING (auth.uid() = auth_id);

-- Baby profiles policy based on baby_user_access
CREATE POLICY baby_profiles_policy ON public.baby_profiles
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = baby_profiles.id
        AND up.auth_id = auth.uid()
    ));

-- Baby user access policy
CREATE POLICY baby_user_access_policy ON public.baby_user_access
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = baby_user_access.user_id
        AND up.auth_id = auth.uid()
    ));

-- Activity logs policy
CREATE POLICY activity_logs_policy ON public.activity_logs
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = activity_logs.baby_id
        AND up.auth_id = auth.uid()
    ));

-- Scheduled activities policy
CREATE POLICY scheduled_activities_policy ON public.scheduled_activities
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = scheduled_activities.baby_id
        AND up.auth_id = auth.uid()
    ));

-- Growth measurements policy
CREATE POLICY growth_measurements_policy ON public.growth_measurements
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = growth_measurements.baby_id
        AND up.auth_id = auth.uid()
    ));

-- Milestones policy
CREATE POLICY milestones_policy ON public.milestones
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = milestones.baby_id
        AND up.auth_id = auth.uid()
    ));

-- Medicine logs policy
CREATE POLICY medicine_logs_policy ON public.medicine_logs
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = medicine_logs.baby_id
        AND up.auth_id = auth.uid()
    ));

-- Vaccination logs policy
CREATE POLICY vaccination_logs_policy ON public.vaccination_logs
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = vaccination_logs.baby_id
        AND up.auth_id = auth.uid()
    ));

-- AI insights policy
CREATE POLICY ai_insights_policy ON public.ai_insights
    USING (EXISTS (
        SELECT 1 FROM public.baby_user_access bua
        JOIN public.user_profiles up ON bua.user_id = up.id
        WHERE bua.baby_id = ai_insights.baby_id
        AND up.auth_id = auth.uid()
    ));

-- Chat messages policy
CREATE POLICY chat_messages_policy ON public.chat_messages
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = chat_messages.user_id
        AND up.auth_id = auth.uid()
    ));

-- Grant permissions to authenticated users
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO authenticated;

-- Grant permissions on the views
GRANT SELECT ON public.growth_measurements_enhanced TO authenticated;

-- Grant execute permission on the functions
GRANT EXECUTE ON FUNCTION get_measurements_requiring_attention(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_age_in_months(DATE, TIMESTAMP WITH TIME ZONE) TO authenticated;

COMMIT;