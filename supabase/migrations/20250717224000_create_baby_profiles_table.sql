-- Create baby_profiles table if it doesn't exist
-- This is a core table that other tables depend on

CREATE TABLE IF NOT EXISTS public.baby_profiles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    birth_date TIMESTAMP WITH TIME ZONE NOT NULL,
    photo_url TEXT,
    gender TEXT NOT NULL CHECK (gender IN ('boy', 'girl', 'other')),
    birth_weight DECIMAL(6,2),
    birth_height DECIMAL(6,2),
    allergies TEXT[] DEFAULT '{}',
    medications TEXT[] DEFAULT '{}',
    note TEXT,
    health_notes TEXT,
    allergy_notes TEXT,
    is_active BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE public.baby_profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage their own baby profiles" ON public.baby_profiles
FOR ALL TO authenticated
USING (user_id = auth.uid())
WITH CHECK (user_id = auth.uid());

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_baby_profiles_user_id ON public.baby_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_baby_profiles_is_active ON public.baby_profiles(is_active);
CREATE INDEX IF NOT EXISTS idx_baby_profiles_created_at ON public.baby_profiles(created_at);

-- Grant permissions
GRANT ALL ON public.baby_profiles TO authenticated;

-- Show table structure
SELECT 'BABY_PROFILES_COLUMNS' as info, column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'baby_profiles'
ORDER BY ordinal_position;
