-- Migration to create user_subscriptions table for handling subscription information
-- This migration creates the necessary table and RLS policies for subscription management

-- Create subscription status enum
CREATE TYPE subscription_status AS ENUM (
    'free',
    'trial', 
    'active',
    'expired',
    'cancelled',
    'past_due'
);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    
    -- Plan information
    plan_id TEXT NOT NULL,
    plan_name TEXT NOT NULL,
    status subscription_status NOT NULL DEFAULT 'free',
    
    -- Pricing and billing
    monthly_price DECIMAL(10,2) NOT NULL DEFAULT 0.00,
    renewal_date TIMESTAMPTZ,
    
    -- Trial information
    is_trial_active BOOLEAN NOT NULL DEFAULT false,
    trial_ends_at TIMESTAMPTZ,
    
    -- Payment information
    payment_method JSONB,
    
    -- Feature flags
    max_family_members INTEGER NOT NULL DEFAULT 1,
    includes_ai_insights BOOLEAN NOT NULL DEFAULT false,
    includes_data_export BOOLEAN NOT NULL DEFAULT false,
    includes_premium_support BOOLEAN NOT NULL DEFAULT false,
    storage_limit INTEGER, -- GB limit, NULL for unlimited
    
    -- Features list (JSON array)
    features JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Metadata
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    UNIQUE(user_id),
    CHECK (monthly_price >= 0),
    CHECK (max_family_members >= 1),
    CHECK (storage_limit IS NULL OR storage_limit > 0)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_renewal_date ON user_subscriptions(renewal_date);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_trial_ends_at ON user_subscriptions(trial_ends_at);

-- Enable RLS
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Users can only see and modify their own subscription
CREATE POLICY "Users can view own subscription" ON user_subscriptions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own subscription" ON user_subscriptions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own subscription" ON user_subscriptions
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own subscription" ON user_subscriptions
    FOR DELETE USING (auth.uid() = user_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_subscription_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_user_subscriptions_updated_at
    BEFORE UPDATE ON user_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_user_subscription_updated_at();

-- Function to get subscription feature access
CREATE OR REPLACE FUNCTION get_user_subscription_features(p_user_id UUID)
RETURNS JSONB AS $$
DECLARE
    subscription_record RECORD;
    features_result JSONB;
BEGIN
    -- Get user's subscription
    SELECT * INTO subscription_record
    FROM user_subscriptions 
    WHERE user_id = p_user_id;
    
    -- If no subscription found, return free plan features
    IF NOT FOUND THEN
        RETURN jsonb_build_object(
            'has_premium_access', false,
            'max_family_members', 1,
            'includes_ai_insights', false,
            'includes_data_export', false,
            'includes_premium_support', false,
            'storage_limit', 1,
            'plan_name', 'Free',
            'status', 'free'
        );
    END IF;
    
    -- Determine if user has premium access
    -- Premium access is granted for 'active' or 'trial' status
    features_result := jsonb_build_object(
        'has_premium_access', subscription_record.status IN ('active', 'trial'),
        'max_family_members', subscription_record.max_family_members,
        'includes_ai_insights', subscription_record.includes_ai_insights,
        'includes_data_export', subscription_record.includes_data_export,
        'includes_premium_support', subscription_record.includes_premium_support,
        'storage_limit', subscription_record.storage_limit,
        'plan_name', subscription_record.plan_name,
        'status', subscription_record.status,
        'monthly_price', subscription_record.monthly_price,
        'renewal_date', subscription_record.renewal_date,
        'is_trial_active', subscription_record.is_trial_active,
        'trial_ends_at', subscription_record.trial_ends_at
    );
    
    RETURN features_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user has specific feature access
CREATE OR REPLACE FUNCTION user_has_feature_access(
    p_user_id UUID,
    p_feature_name TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    subscription_record RECORD;
    has_access BOOLEAN := false;
BEGIN
    -- Get user's subscription
    SELECT * INTO subscription_record
    FROM user_subscriptions 
    WHERE user_id = p_user_id;
    
    -- If no subscription found, user has free plan
    IF NOT FOUND THEN
        -- Free plan feature access
        CASE p_feature_name
            WHEN 'ai_insights' THEN has_access := false;
            WHEN 'family_sharing' THEN has_access := false;
            WHEN 'who_charts' THEN has_access := false;
            WHEN 'unlimited_profiles' THEN has_access := false;
            WHEN 'data_export' THEN has_access := false;
            WHEN 'premium_support' THEN has_access := false;
            ELSE has_access := false;
        END CASE;
        
        RETURN has_access;
    END IF;
    
    -- Check premium access (active or trial)
    IF subscription_record.status NOT IN ('active', 'trial') THEN
        -- No premium access, same as free plan
        CASE p_feature_name
            WHEN 'ai_insights' THEN has_access := false;
            WHEN 'family_sharing' THEN has_access := false;
            WHEN 'who_charts' THEN has_access := false;
            WHEN 'unlimited_profiles' THEN has_access := false;
            WHEN 'data_export' THEN has_access := false;
            WHEN 'premium_support' THEN has_access := false;
            ELSE has_access := false;
        END CASE;
        
        RETURN has_access;
    END IF;
    
    -- Check specific feature access for premium users
    CASE p_feature_name
        WHEN 'ai_insights' THEN 
            has_access := subscription_record.includes_ai_insights;
        WHEN 'family_sharing' THEN 
            has_access := subscription_record.max_family_members > 1;
        WHEN 'who_charts' THEN 
            has_access := true; -- All premium plans include WHO charts
        WHEN 'unlimited_profiles' THEN 
            has_access := true; -- All premium plans include unlimited profiles
        WHEN 'data_export' THEN 
            has_access := subscription_record.includes_data_export;
        WHEN 'premium_support' THEN 
            has_access := subscription_record.includes_premium_support;
        ELSE 
            has_access := false;
    END CASE;
    
    RETURN has_access;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Insert default free subscriptions for existing users
INSERT INTO user_subscriptions (
    user_id,
    plan_id,
    plan_name,
    status,
    monthly_price,
    max_family_members,
    includes_ai_insights,
    includes_data_export,
    includes_premium_support,
    features
)
SELECT 
    id as user_id,
    'free' as plan_id,
    'Free' as plan_name,
    'free'::subscription_status as status,
    0.00 as monthly_price,
    1 as max_family_members,
    false as includes_ai_insights,
    false as includes_data_export,
    false as includes_premium_support,
    '["Basic activity tracking", "Up to 1 baby profile", "No family sharing", "No WHO Growth Charts", "No AI insights", "No Ask AI chat"]'::jsonb as features
FROM auth.users 
WHERE id NOT IN (SELECT user_id FROM user_subscriptions)
ON CONFLICT (user_id) DO NOTHING;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON user_subscriptions TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_subscription_features(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION user_has_feature_access(UUID, TEXT) TO authenticated;
