-- ============================================================================
-- ADD MISSING SCHEDULED ACTIVITY TYPES
-- This migration adds the missing scheduled activity types to the existing enum
-- ============================================================================

-- Add missing values to the scheduled_activity_type enum
ALTER TYPE scheduled_activity_type ADD VALUE 'shopping_trip';
ALTER TYPE scheduled_activity_type ADD VALUE 'nap_time';
ALTER TYPE scheduled_activity_type ADD VALUE 'meal_time';
ALTER TYPE scheduled_activity_type ADD VALUE 'walk_time';
ALTER TYPE scheduled_activity_type ADD VALUE 'bottle_feeding';

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Added missing scheduled activity types:';
    RAISE NOTICE '- shopping_trip';
    RAISE NOTICE '- nap_time';
    RAISE NOTICE '- meal_time';
    RAISE NOTICE '- walk_time';
    RAISE NOTICE '- bottle_feeding';
    RAISE NOTICE 'Migration completed successfully!';
END $$;
