-- Fix user profile schema issues
-- Add missing columns and tables required by the application

BEGIN;

-- Add missing columns to user_profiles table
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS is_email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS family_id UUID,
ADD COLUMN IF NOT EXISTS phone_number TEXT,
ADD COLUMN IF NOT EXISTS timezone TEXT,
ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"email": true, "push": true}',
ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'free' CHECK (subscription_status IN ('free', 'premium', 'family')),
ADD COLUMN IF NOT EXISTS last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS full_name TEXT,
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'parent',
ADD COLUMN IF NOT EXISTS provider TEXT,
ADD COLUMN IF NOT EXISTS provider_id TEXT,
ADD COLUMN IF NOT EXISTS last_sign_in_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS sign_in_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_two_factor_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}';

-- Create families table for family sharing
CREATE TABLE IF NOT EXISTS public.families (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    created_by UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create subscriptions table
CREATE TABLE IF NOT EXISTS public.subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    family_id UUID REFERENCES public.families(id) ON DELETE CASCADE,
    subscription_type TEXT NOT NULL CHECK (subscription_type IN ('free', 'premium', 'family')),
    status TEXT NOT NULL CHECK (status IN ('active', 'inactive', 'cancelled', 'expired')),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    stripe_subscription_id TEXT,
    stripe_customer_id TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_sessions table for tracking user activity
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    device_info JSONB,
    ip_address INET,
    session_start TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    session_end TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add foreign key constraint for family_id
ALTER TABLE public.user_profiles 
ADD CONSTRAINT fk_user_profiles_family_id 
FOREIGN KEY (family_id) REFERENCES public.families(id) ON DELETE SET NULL;

-- Create function to track user sessions
CREATE OR REPLACE FUNCTION public.track_user_session(
    p_device_info JSONB DEFAULT NULL,
    p_ip_address TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
    v_user_id UUID;
    v_session_id UUID;
BEGIN
    -- Get user_id from auth.uid()
    SELECT id INTO v_user_id 
    FROM public.user_profiles 
    WHERE auth_id = auth.uid();
    
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User profile not found for authenticated user';
    END IF;
    
    -- End any existing active sessions for this user
    UPDATE public.user_sessions 
    SET is_active = FALSE, 
        session_end = NOW(),
        updated_at = NOW()
    WHERE user_id = v_user_id 
    AND is_active = TRUE;
    
    -- Create new session
    INSERT INTO public.user_sessions (
        user_id, 
        device_info, 
        ip_address
    ) VALUES (
        v_user_id,
        p_device_info,
        p_ip_address::INET
    ) RETURNING id INTO v_session_id;
    
    -- Update user's last_active_at
    UPDATE public.user_profiles 
    SET last_active_at = NOW(),
        updated_at = NOW()
    WHERE id = v_user_id;
    
    RETURN v_session_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get chat sessions (referenced in error)
CREATE OR REPLACE FUNCTION public.get_chat_sessions(
    p_user_id UUID DEFAULT NULL,
    p_limit INTEGER DEFAULT 50
)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    baby_id UUID,
    message_text TEXT,
    is_from_user BOOLEAN,
    sent_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        cm.id,
        cm.user_id,
        cm.baby_id,
        cm.message_text,
        cm.is_from_user,
        cm.sent_at
    FROM public.chat_messages cm
    WHERE (p_user_id IS NULL OR cm.user_id = p_user_id)
    ORDER BY cm.sent_at DESC
    LIMIT p_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to update user profile safely
CREATE OR REPLACE FUNCTION public.update_user_profile(
    p_full_name TEXT DEFAULT NULL,
    p_avatar_url TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_timezone TEXT DEFAULT NULL,
    p_preferences JSONB DEFAULT NULL,
    p_is_email_verified BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    auth_id UUID,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    role TEXT,
    provider TEXT,
    provider_id TEXT,
    last_sign_in_at TIMESTAMP WITH TIME ZONE,
    sign_in_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    phone_number TEXT,
    timezone TEXT,
    preferences JSONB,
    is_email_verified BOOLEAN,
    is_two_factor_enabled BOOLEAN,
    permissions JSONB,
    family_id UUID,
    notification_preferences JSONB,
    subscription_status TEXT,
    last_active_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Get user_id from auth.uid()
    SELECT up.id INTO v_user_id 
    FROM public.user_profiles up
    WHERE up.auth_id = auth.uid();
    
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User profile not found for authenticated user';
    END IF;
    
    -- Update the profile
    UPDATE public.user_profiles 
    SET 
        full_name = COALESCE(p_full_name, full_name),
        avatar_url = COALESCE(p_avatar_url, avatar_url),
        phone_number = COALESCE(p_phone_number, phone_number),
        timezone = COALESCE(p_timezone, timezone),
        preferences = COALESCE(p_preferences, preferences),
        is_email_verified = COALESCE(p_is_email_verified, is_email_verified),
        updated_at = NOW(),
        last_active_at = NOW()
    WHERE id = v_user_id;
    
    -- Return the updated profile
    RETURN QUERY
    SELECT 
        up.id,
        up.auth_id,
        up.email,
        up.full_name,
        up.avatar_url,
        up.role,
        up.provider,
        up.provider_id,
        up.last_sign_in_at,
        up.sign_in_count,
        up.created_at,
        up.updated_at,
        up.phone_number,
        up.timezone,
        up.preferences,
        up.is_email_verified,
        up.is_two_factor_enabled,
        up.permissions,
        up.family_id,
        up.notification_preferences,
        up.subscription_status,
        up.last_active_at
    FROM public.user_profiles up
    WHERE up.id = v_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create user profile if it doesn't exist
CREATE OR REPLACE FUNCTION public.create_user_profile_if_not_exists()
RETURNS UUID AS $$
DECLARE
    v_user_id UUID;
    v_auth_user auth.users%ROWTYPE;
BEGIN
    -- Get the authenticated user
    SELECT * INTO v_auth_user FROM auth.users WHERE id = auth.uid();
    
    IF v_auth_user.id IS NULL THEN
        RAISE EXCEPTION 'No authenticated user found';
    END IF;
    
    -- Check if profile already exists
    SELECT id INTO v_user_id 
    FROM public.user_profiles 
    WHERE auth_id = v_auth_user.id;
    
    -- If profile doesn't exist, create it
    IF v_user_id IS NULL THEN
        INSERT INTO public.user_profiles (
            auth_id,
            email,
            full_name,
            is_email_verified,
            created_at,
            updated_at
        ) VALUES (
            v_auth_user.id,
            v_auth_user.email,
            COALESCE(v_auth_user.raw_user_meta_data->>'full_name', v_auth_user.email),
            COALESCE((v_auth_user.email_confirmed_at IS NOT NULL), false),
            NOW(),
            NOW()
        ) RETURNING id INTO v_user_id;
    END IF;
    
    RETURN v_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add triggers for updated_at on new tables
CREATE TRIGGER update_families_updated_at
    BEFORE UPDATE ON public.families
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_subscriptions_updated_at
    BEFORE UPDATE ON public.subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_user_sessions_updated_at
    BEFORE UPDATE ON public.user_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS on new tables
ALTER TABLE public.families ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for new tables
-- Families policy - users can see families they belong to
CREATE POLICY families_policy ON public.families
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.family_id = families.id
        AND up.auth_id = auth.uid()
    ));

-- Subscriptions policy - users can see their own subscriptions
CREATE POLICY subscriptions_policy ON public.subscriptions
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE (up.id = subscriptions.user_id OR up.family_id = subscriptions.family_id)
        AND up.auth_id = auth.uid()
    ));

-- User sessions policy - users can see their own sessions
CREATE POLICY user_sessions_policy ON public.user_sessions
    USING (EXISTS (
        SELECT 1 FROM public.user_profiles up
        WHERE up.id = user_sessions.user_id
        AND up.auth_id = auth.uid()
    ));

-- Grant permissions on new tables
GRANT SELECT, INSERT, UPDATE, DELETE ON public.families TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.subscriptions TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_sessions TO authenticated;

-- Grant execute permissions on new functions
GRANT EXECUTE ON FUNCTION public.track_user_session(JSONB, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_chat_sessions(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_profile(TEXT, TEXT, TEXT, TEXT, JSONB, BOOLEAN) TO authenticated;
GRANT EXECUTE ON FUNCTION public.create_user_profile_if_not_exists() TO authenticated;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_family_id ON public.user_profiles(family_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_auth_id ON public.user_profiles(auth_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_last_active_at ON public.user_profiles(last_active_at);

CREATE INDEX IF NOT EXISTS idx_families_created_by ON public.families(created_by);

CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_family_id ON public.subscriptions(family_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_status ON public.subscriptions(status);

CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_is_active ON public.user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_session_start ON public.user_sessions(session_start);

-- Create trigger function to automatically create user profile on auth user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.user_profiles (
        auth_id,
        email,
        full_name,
        is_email_verified,
        created_at,
        updated_at
    ) VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email),
        COALESCE((NEW.email_confirmed_at IS NOT NULL), false),
        NOW(),
        NOW()
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user profile
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION public.handle_new_user();

COMMIT;