-- Fix user profile RLS policies and creation issues
-- This addresses the "Failed to update profile" error and RLS policy violations

BEGIN;

-- Drop existing restrictive policies that might be blocking user profile creation
DROP POLICY IF EXISTS user_profiles_policy ON public.user_profiles;
DROP POLICY IF EXISTS user_profiles_select_policy ON public.user_profiles;
DROP POLICY IF EXISTS user_profiles_insert_policy ON public.user_profiles;
DROP POLICY IF EXISTS user_profiles_update_policy ON public.user_profiles;
DROP POLICY IF EXISTS user_profiles_delete_policy ON public.user_profiles;

-- Create comprehensive RLS policies for user_profiles
-- Allow users to view their own profile
CREATE POLICY user_profiles_select_policy ON public.user_profiles
    FOR SELECT
    USING (auth_id = auth.uid());

-- Allow users to insert their own profile
CREATE POLICY user_profiles_insert_policy ON public.user_profiles
    FOR INSERT
    WITH CHECK (auth_id = auth.uid());

-- Allow users to update their own profile
CREATE POLICY user_profiles_update_policy ON public.user_profiles
    FOR UPDATE
    USING (auth_id = auth.uid())
    WITH CHECK (auth_id = auth.uid());

-- Allow users to delete their own profile (optional, usually not needed)
CREATE POLICY user_profiles_delete_policy ON public.user_profiles
    FOR DELETE
    USING (auth_id = auth.uid());

-- Ensure the user_profiles table has RLS enabled
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- Create or replace the function to create user profile safely
CREATE OR REPLACE FUNCTION public.create_or_update_user_profile(
    p_full_name TEXT DEFAULT NULL,
    p_avatar_url TEXT DEFAULT NULL,
    p_phone_number TEXT DEFAULT NULL,
    p_timezone TEXT DEFAULT NULL,
    p_notification_preferences JSONB DEFAULT NULL
)
RETURNS TABLE(
    id UUID,
    auth_id UUID,
    email TEXT,
    full_name TEXT,
    avatar_url TEXT,
    phone_number TEXT,
    timezone TEXT,
    notification_preferences JSONB,
    role TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
DECLARE
    v_user_id UUID;
    v_auth_user auth.users%ROWTYPE;
    v_existing_profile public.user_profiles%ROWTYPE;
BEGIN
    -- Get the authenticated user
    SELECT * INTO v_auth_user FROM auth.users WHERE id = auth.uid();
    
    IF v_auth_user.id IS NULL THEN
        RAISE EXCEPTION 'User profile not found for authenticated user' USING ERRCODE = 'P0001';
    END IF;
    
    -- Check if profile already exists
    SELECT * INTO v_existing_profile 
    FROM public.user_profiles 
    WHERE auth_id = v_auth_user.id;
    
    -- If profile exists, update it
    IF v_existing_profile.id IS NOT NULL THEN
        UPDATE public.user_profiles SET
            full_name = COALESCE(p_full_name, v_existing_profile.full_name),
            avatar_url = COALESCE(p_avatar_url, v_existing_profile.avatar_url),
            phone_number = COALESCE(p_phone_number, v_existing_profile.phone_number),
            timezone = COALESCE(p_timezone, v_existing_profile.timezone),
            notification_preferences = COALESCE(p_notification_preferences, v_existing_profile.notification_preferences),
            updated_at = NOW()
        WHERE auth_id = v_auth_user.id
        RETURNING * INTO v_existing_profile;
        
        RETURN QUERY SELECT 
            v_existing_profile.id,
            v_existing_profile.auth_id,
            v_existing_profile.email,
            v_existing_profile.full_name,
            v_existing_profile.avatar_url,
            v_existing_profile.phone_number,
            v_existing_profile.timezone,
            v_existing_profile.notification_preferences,
            v_existing_profile.role,
            v_existing_profile.created_at,
            v_existing_profile.updated_at;
    ELSE
        -- Profile doesn't exist, create it
        INSERT INTO public.user_profiles (
            auth_id,
            email,
            full_name,
            avatar_url,
            phone_number,
            timezone,
            notification_preferences,
            role,
            is_email_verified,
            created_at,
            updated_at
        ) VALUES (
            v_auth_user.id,
            v_auth_user.email,
            COALESCE(p_full_name, v_auth_user.raw_user_meta_data->>'full_name', v_auth_user.email),
            p_avatar_url,
            p_phone_number,
            p_timezone,
            COALESCE(p_notification_preferences, '{"email": true, "push": true}'::jsonb),
            'parent',
            COALESCE((v_auth_user.email_confirmed_at IS NOT NULL), false),
            NOW(),
            NOW()
        ) RETURNING * INTO v_existing_profile;
        
        RETURN QUERY SELECT 
            v_existing_profile.id,
            v_existing_profile.auth_id,
            v_existing_profile.email,
            v_existing_profile.full_name,
            v_existing_profile.avatar_url,
            v_existing_profile.phone_number,
            v_existing_profile.timezone,
            v_existing_profile.notification_preferences,
            v_existing_profile.role,
            v_existing_profile.created_at,
            v_existing_profile.updated_at;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.create_or_update_user_profile(TEXT, TEXT, TEXT, TEXT, JSONB) TO authenticated;

COMMIT;