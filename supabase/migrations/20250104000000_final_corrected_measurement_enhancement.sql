-- Final Corrected Measurement Enhancement Migration
-- Based on actual table structure from Supabase

-- =====================================================
-- 1. BABY_PROFILES TABLE ENHANCEMENTS
-- =====================================================
-- Actual columns: birth_weight (numeric), birth_height (numeric)
ALTER TABLE baby_profiles 
ADD COLUMN IF NOT EXISTS birth_weight_metric_value DECIMAL(6,3),
ADD COLUMN IF NOT EXISTS birth_weight_metric_unit VARCHAR(10) DEFAULT 'kg',
ADD COLUMN IF NOT EXISTS birth_weight_imperial_value DECIMAL(6,3),
ADD COLUMN IF NOT EXISTS birth_weight_imperial_unit VARCHAR(10) DEFAULT 'lbs',
ADD COLUMN IF NOT EXISTS birth_weight_original_value DECIMAL(6,3),
ADD COLUMN IF NOT EXISTS birth_weight_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS birth_weight_entered_as_metric BOOLEAN DEFAULT true,

ADD COLUMN IF NOT EXISTS birth_height_metric_value DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS birth_height_metric_unit VARCHAR(10) DEFAULT 'cm',
ADD COLUMN IF NOT EXISTS birth_height_imperial_value DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS birth_height_imperial_unit VARCHAR(10) DEFAULT 'in',
ADD COLUMN IF NOT EXISTS birth_height_original_value DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS birth_height_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS birth_height_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 2. GROWTH_MEASUREMENTS TABLE ENHANCEMENTS
-- =====================================================
-- Actual columns: measurement_type (text), value (numeric), unit (text)
ALTER TABLE growth_measurements
ADD COLUMN IF NOT EXISTS value_metric_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS value_metric_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS value_imperial_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS value_imperial_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS value_original_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS value_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS value_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 3. ACTIVITY_LOGS TABLE ENHANCEMENTS
-- =====================================================
-- Actual columns: quantity (numeric), unit (text), details (jsonb)
ALTER TABLE activity_logs
-- Temperature measurements
ADD COLUMN IF NOT EXISTS temperature_metric_value DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS temperature_metric_unit VARCHAR(10) DEFAULT '°C',
ADD COLUMN IF NOT EXISTS temperature_imperial_value DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS temperature_imperial_unit VARCHAR(10) DEFAULT '°F',
ADD COLUMN IF NOT EXISTS temperature_original_value DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS temperature_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS temperature_entered_as_metric BOOLEAN DEFAULT true,

-- Volume measurements (feeding, pumping)
ADD COLUMN IF NOT EXISTS volume_metric_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS volume_metric_unit VARCHAR(10) DEFAULT 'ml',
ADD COLUMN IF NOT EXISTS volume_imperial_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS volume_imperial_unit VARCHAR(10) DEFAULT 'fl oz',
ADD COLUMN IF NOT EXISTS volume_original_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS volume_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS volume_entered_as_metric BOOLEAN DEFAULT true,

-- General quantity measurements
ADD COLUMN IF NOT EXISTS quantity_metric_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS quantity_metric_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS quantity_imperial_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS quantity_imperial_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS quantity_original_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS quantity_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS quantity_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 4. MEDICINE_LOGS TABLE ENHANCEMENTS
-- =====================================================
-- Actual columns: dosage (text), unit (text)
ALTER TABLE medicine_logs
ADD COLUMN IF NOT EXISTS dosage_numeric_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS dosage_metric_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS dosage_metric_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS dosage_imperial_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS dosage_imperial_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS dosage_original_value DECIMAL(8,3),
ADD COLUMN IF NOT EXISTS dosage_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS dosage_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 5. VACCINATION_LOGS TABLE ENHANCEMENTS
-- =====================================================
-- Add dosage fields (they don't exist yet)
ALTER TABLE vaccination_logs
ADD COLUMN IF NOT EXISTS dosage_amount DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS dosage_unit TEXT,
ADD COLUMN IF NOT EXISTS dosage_metric_value DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS dosage_metric_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS dosage_imperial_value DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS dosage_imperial_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS dosage_original_value DECIMAL(6,2),
ADD COLUMN IF NOT EXISTS dosage_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS dosage_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 6. SCHEDULED_ACTIVITIES TABLE ENHANCEMENTS
-- =====================================================
-- Add target measurement fields
ALTER TABLE scheduled_activities
ADD COLUMN IF NOT EXISTS target_quantity DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS target_unit TEXT,
ADD COLUMN IF NOT EXISTS target_metric_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS target_metric_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS target_imperial_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS target_imperial_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS target_original_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS target_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS target_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 7. MILESTONES TABLE ENHANCEMENTS
-- =====================================================
-- Add measurement fields for physical milestones
ALTER TABLE milestones
ADD COLUMN IF NOT EXISTS measurement_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS measurement_unit TEXT,
ADD COLUMN IF NOT EXISTS measurement_metric_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS measurement_metric_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS measurement_imperial_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS measurement_imperial_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS measurement_original_value DECIMAL(8,2),
ADD COLUMN IF NOT EXISTS measurement_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS measurement_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- 8. USER_PROFILES TABLE ENHANCEMENTS
-- =====================================================
-- Add personal measurement fields
ALTER TABLE user_profiles
ADD COLUMN IF NOT EXISTS height DECIMAL(5,1),
ADD COLUMN IF NOT EXISTS height_unit TEXT DEFAULT 'cm',
ADD COLUMN IF NOT EXISTS weight DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS weight_unit TEXT DEFAULT 'kg',
ADD COLUMN IF NOT EXISTS height_metric_value DECIMAL(5,1),
ADD COLUMN IF NOT EXISTS height_metric_unit VARCHAR(10) DEFAULT 'cm',
ADD COLUMN IF NOT EXISTS height_imperial_value DECIMAL(5,1),
ADD COLUMN IF NOT EXISTS height_imperial_unit VARCHAR(10) DEFAULT 'in',
ADD COLUMN IF NOT EXISTS height_original_value DECIMAL(5,1),
ADD COLUMN IF NOT EXISTS height_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS height_entered_as_metric BOOLEAN DEFAULT true,
ADD COLUMN IF NOT EXISTS weight_metric_value DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS weight_metric_unit VARCHAR(10) DEFAULT 'kg',
ADD COLUMN IF NOT EXISTS weight_imperial_value DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS weight_imperial_unit VARCHAR(10) DEFAULT 'lbs',
ADD COLUMN IF NOT EXISTS weight_original_value DECIMAL(5,2),
ADD COLUMN IF NOT EXISTS weight_original_unit VARCHAR(10),
ADD COLUMN IF NOT EXISTS weight_entered_as_metric BOOLEAN DEFAULT true;

-- =====================================================
-- CORRECTED MIGRATION FUNCTION
-- =====================================================
CREATE OR REPLACE FUNCTION migrate_existing_measurements_final()
RETURNS void AS $$
BEGIN
  -- Migrate baby_profiles birth_weight (column exists as numeric)
  UPDATE baby_profiles 
  SET 
    birth_weight_metric_value = birth_weight,
    birth_weight_metric_unit = 'kg',
    birth_weight_imperial_value = ROUND((birth_weight * 2.20462)::numeric, 3),
    birth_weight_imperial_unit = 'lbs',
    birth_weight_original_value = birth_weight,
    birth_weight_original_unit = 'kg',
    birth_weight_entered_as_metric = true
  WHERE birth_weight IS NOT NULL 
    AND birth_weight_metric_value IS NULL;

  -- Migrate baby_profiles birth_height (column exists as numeric)
  UPDATE baby_profiles 
  SET 
    birth_height_metric_value = birth_height,
    birth_height_metric_unit = 'cm',
    birth_height_imperial_value = ROUND((birth_height * 0.393701)::numeric, 2),
    birth_height_imperial_unit = 'in',
    birth_height_original_value = birth_height,
    birth_height_original_unit = 'cm',
    birth_height_entered_as_metric = true
  WHERE birth_height IS NOT NULL 
    AND birth_height_metric_value IS NULL;

  -- Migrate growth_measurements value (columns exist: value, unit, measurement_type)
  UPDATE growth_measurements 
  SET 
    value_metric_value = CASE 
      WHEN unit IN ('kg', 'g', 'cm', 'm') THEN value
      WHEN unit = 'lbs' THEN ROUND((value / 2.20462)::numeric, 3)
      WHEN unit = 'in' THEN ROUND((value / 0.393701)::numeric, 2)
      ELSE value
    END,
    value_metric_unit = CASE 
      WHEN measurement_type = 'weight' THEN 'kg'
      WHEN measurement_type IN ('length', 'height', 'head_circumference') THEN 'cm'
      ELSE unit
    END,
    value_imperial_value = CASE 
      WHEN unit IN ('lbs', 'in') THEN value
      WHEN unit = 'kg' THEN ROUND((value * 2.20462)::numeric, 3)
      WHEN unit = 'cm' THEN ROUND((value * 0.393701)::numeric, 2)
      ELSE value
    END,
    value_imperial_unit = CASE 
      WHEN measurement_type = 'weight' THEN 'lbs'
      WHEN measurement_type IN ('length', 'height', 'head_circumference') THEN 'in'
      ELSE unit
    END,
    value_original_value = value,
    value_original_unit = unit,
    value_entered_as_metric = CASE 
      WHEN unit IN ('kg', 'g', 'cm', 'm') THEN true
      ELSE false
    END
  WHERE value IS NOT NULL 
    AND value_metric_value IS NULL;

  -- Migrate medicine_logs dosage (column exists as text)
  -- Try to extract numeric value from text dosage
  UPDATE medicine_logs 
  SET 
    dosage_numeric_value = CASE 
      WHEN dosage ~ '^[0-9]+\.?[0-9]*$' THEN dosage::numeric
      ELSE NULL
    END,
    dosage_metric_value = CASE 
      WHEN dosage ~ '^[0-9]+\.?[0-9]*$' THEN dosage::numeric
      ELSE NULL
    END,
    dosage_metric_unit = unit,
    dosage_imperial_value = CASE 
      WHEN dosage ~ '^[0-9]+\.?[0-9]*$' THEN dosage::numeric
      ELSE NULL
    END,
    dosage_imperial_unit = unit,
    dosage_original_value = CASE 
      WHEN dosage ~ '^[0-9]+\.?[0-9]*$' THEN dosage::numeric
      ELSE NULL
    END,
    dosage_original_unit = unit,
    dosage_entered_as_metric = true
  WHERE dosage IS NOT NULL 
    AND dosage_metric_value IS NULL;

  -- Migrate activity_logs quantity (column exists as numeric)
  UPDATE activity_logs 
  SET 
    quantity_metric_value = quantity,
    quantity_metric_unit = unit,
    quantity_imperial_value = CASE 
      WHEN unit = 'ml' THEN ROUND((quantity * 0.033814)::numeric, 2)
      WHEN unit = 'kg' THEN ROUND((quantity * 2.20462)::numeric, 3)
      WHEN unit = 'cm' THEN ROUND((quantity * 0.393701)::numeric, 2)
      ELSE quantity
    END,
    quantity_imperial_unit = CASE 
      WHEN unit = 'ml' THEN 'fl oz'
      WHEN unit = 'kg' THEN 'lbs'
      WHEN unit = 'cm' THEN 'in'
      ELSE unit
    END,
    quantity_original_value = quantity,
    quantity_original_unit = unit,
    quantity_entered_as_metric = CASE 
      WHEN unit IN ('ml', 'kg', 'cm', 'g', 'm') THEN true
      ELSE false
    END
  WHERE quantity IS NOT NULL 
    AND quantity_metric_value IS NULL;

  -- Migrate activity_logs temperature from details JSON
  UPDATE activity_logs 
  SET 
    temperature_metric_value = CASE 
      WHEN activity_type::text = 'temperature' AND details->>'temperature' IS NOT NULL 
      THEN (details->>'temperature')::numeric 
      ELSE NULL 
    END,
    temperature_metric_unit = '°C',
    temperature_imperial_value = CASE 
      WHEN activity_type::text = 'temperature' AND details->>'temperature' IS NOT NULL 
      THEN ROUND((((details->>'temperature')::numeric * 9/5) + 32)::numeric, 2)
      ELSE NULL 
    END,
    temperature_imperial_unit = '°F',
    temperature_original_value = CASE 
      WHEN activity_type::text = 'temperature' AND details->>'temperature' IS NOT NULL 
      THEN (details->>'temperature')::numeric 
      ELSE NULL 
    END,
    temperature_original_unit = '°C',
    temperature_entered_as_metric = true
  WHERE activity_type::text = 'temperature' 
    AND details->>'temperature' IS NOT NULL 
    AND temperature_metric_value IS NULL;

  -- Migrate activity_logs volume from details JSON (feeding, pumping)
  UPDATE activity_logs 
  SET 
    volume_metric_value = CASE 
      WHEN activity_type::text IN ('feeding', 'pumping') AND details->>'amount' IS NOT NULL 
      THEN (details->>'amount')::numeric 
      ELSE NULL 
    END,
    volume_metric_unit = 'ml',
    volume_imperial_value = CASE 
      WHEN activity_type::text IN ('feeding', 'pumping') AND details->>'amount' IS NOT NULL 
      THEN ROUND(((details->>'amount')::numeric * 0.033814)::numeric, 2)
      ELSE NULL 
    END,
    volume_imperial_unit = 'fl oz',
    volume_original_value = CASE 
      WHEN activity_type::text IN ('feeding', 'pumping') AND details->>'amount' IS NOT NULL 
      THEN (details->>'amount')::numeric 
      ELSE NULL 
    END,
    volume_original_unit = 'ml',
    volume_entered_as_metric = true
  WHERE activity_type::text IN ('feeding', 'pumping') 
    AND details->>'amount' IS NOT NULL 
    AND volume_metric_value IS NULL;

  RAISE NOTICE 'Final measurement data migration completed successfully';
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- ENHANCED TRIGGER FUNCTION
-- =====================================================
CREATE OR REPLACE FUNCTION sync_measurement_values_final()
RETURNS TRIGGER AS $$
BEGIN
  -- Handle baby_profiles
  IF TG_TABLE_NAME = 'baby_profiles' THEN
    -- Birth weight sync
    IF NEW.birth_weight_original_value IS NOT NULL AND NEW.birth_weight_original_unit IS NOT NULL THEN
      IF NEW.birth_weight_entered_as_metric THEN
        NEW.birth_weight_metric_value := NEW.birth_weight_original_value;
        NEW.birth_weight_metric_unit := 'kg';
        NEW.birth_weight_imperial_value := ROUND((NEW.birth_weight_original_value * 2.20462)::numeric, 3);
        NEW.birth_weight_imperial_unit := 'lbs';
      ELSE
        NEW.birth_weight_imperial_value := NEW.birth_weight_original_value;
        NEW.birth_weight_imperial_unit := 'lbs';
        NEW.birth_weight_metric_value := ROUND((NEW.birth_weight_original_value / 2.20462)::numeric, 3);
        NEW.birth_weight_metric_unit := 'kg';
      END IF;
      -- Update legacy column for backward compatibility
      NEW.birth_weight := NEW.birth_weight_metric_value;
    END IF;

    -- Birth height sync
    IF NEW.birth_height_original_value IS NOT NULL AND NEW.birth_height_original_unit IS NOT NULL THEN
      IF NEW.birth_height_entered_as_metric THEN
        NEW.birth_height_metric_value := NEW.birth_height_original_value;
        NEW.birth_height_metric_unit := 'cm';
        NEW.birth_height_imperial_value := ROUND((NEW.birth_height_original_value * 0.393701)::numeric, 2);
        NEW.birth_height_imperial_unit := 'in';
      ELSE
        NEW.birth_height_imperial_value := NEW.birth_height_original_value;
        NEW.birth_height_imperial_unit := 'in';
        NEW.birth_height_metric_value := ROUND((NEW.birth_height_original_value / 0.393701)::numeric, 2);
        NEW.birth_height_metric_unit := 'cm';
      END IF;
      -- Update legacy column for backward compatibility
      NEW.birth_height := NEW.birth_height_metric_value;
    END IF;
  END IF;

  -- Handle growth_measurements
  IF TG_TABLE_NAME = 'growth_measurements' THEN
    IF NEW.value_original_value IS NOT NULL AND NEW.value_original_unit IS NOT NULL THEN
      IF NEW.value_entered_as_metric THEN
        NEW.value_metric_value := NEW.value_original_value;
        NEW.value_metric_unit := CASE 
          WHEN NEW.measurement_type = 'weight' THEN 'kg'
          WHEN NEW.measurement_type IN ('length', 'height', 'head_circumference') THEN 'cm'
          ELSE NEW.value_original_unit
        END;
        
        -- Calculate imperial value
        IF NEW.measurement_type = 'weight' THEN
          NEW.value_imperial_value := ROUND((NEW.value_original_value * 2.20462)::numeric, 3);
          NEW.value_imperial_unit := 'lbs';
        ELSIF NEW.measurement_type IN ('length', 'height', 'head_circumference') THEN
          NEW.value_imperial_value := ROUND((NEW.value_original_value * 0.393701)::numeric, 2);
          NEW.value_imperial_unit := 'in';
        END IF;
      ELSE
        NEW.value_imperial_value := NEW.value_original_value;
        NEW.value_imperial_unit := CASE 
          WHEN NEW.measurement_type = 'weight' THEN 'lbs'
          WHEN NEW.measurement_type IN ('length', 'height', 'head_circumference') THEN 'in'
          ELSE NEW.value_original_unit
        END;
        
        -- Calculate metric value
        IF NEW.measurement_type = 'weight' THEN
          NEW.value_metric_value := ROUND((NEW.value_original_value / 2.20462)::numeric, 3);
          NEW.value_metric_unit := 'kg';
        ELSIF NEW.measurement_type IN ('length', 'height', 'head_circumference') THEN
          NEW.value_metric_value := ROUND((NEW.value_original_value / 0.393701)::numeric, 2);
          NEW.value_metric_unit := 'cm';
        END IF;
      END IF;
      
      -- Update legacy columns for backward compatibility
      NEW.value := NEW.value_metric_value;
      NEW.unit := NEW.value_metric_unit;
    END IF;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- CREATE TRIGGERS
-- =====================================================
DROP TRIGGER IF EXISTS sync_baby_profile_measurements_final ON baby_profiles;
CREATE TRIGGER sync_baby_profile_measurements_final
  BEFORE INSERT OR UPDATE ON baby_profiles
  FOR EACH ROW
  EXECUTE FUNCTION sync_measurement_values_final();

DROP TRIGGER IF EXISTS sync_growth_measurements_final ON growth_measurements;
CREATE TRIGGER sync_growth_measurements_final
  BEFORE INSERT OR UPDATE ON growth_measurements
  FOR EACH ROW
  EXECUTE FUNCTION sync_measurement_values_final();

-- =====================================================
-- CREATE INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_baby_profiles_birth_weight_metric_final ON baby_profiles(birth_weight_metric_value);
CREATE INDEX IF NOT EXISTS idx_baby_profiles_birth_height_metric_final ON baby_profiles(birth_height_metric_value);
CREATE INDEX IF NOT EXISTS idx_growth_measurements_value_metric_final ON growth_measurements(value_metric_value);
CREATE INDEX IF NOT EXISTS idx_medicine_logs_dosage_metric_final ON medicine_logs(dosage_metric_value);
CREATE INDEX IF NOT EXISTS idx_activity_logs_quantity_metric_final ON activity_logs(quantity_metric_value);
CREATE INDEX IF NOT EXISTS idx_activity_logs_temperature_metric_final ON activity_logs(temperature_metric_value);
CREATE INDEX IF NOT EXISTS idx_activity_logs_volume_metric_final ON activity_logs(volume_metric_value);

-- =====================================================
-- RUN THE MIGRATION
-- =====================================================
SELECT migrate_existing_measurements_final();