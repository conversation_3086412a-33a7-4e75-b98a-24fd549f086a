-- Create a view that combines activities and milestones for efficient querying
CREATE OR REPLACE VIEW combined_activities AS
SELECT 
  'activity'::text as source_type,
  id,
  baby_id,
  activity_type as type,
  title,
  recorded_at as timestamp,
  notes,
  activity_data as data,
  created_at,
  updated_at
FROM activity_logs
UNION ALL
SELECT 
  'milestone'::text as source_type,
  id,
  baby_id,
  'milestone'::text as type,
  title,
  achieved_date as timestamp,
  notes,
  jsonb_build_object(
    'category', category,
    'milestone_type', type,
    'age_in_months', age_in_months,
    'age_in_days', age_in_days,
    'is_custom', is_custom
  ) as data,
  created_at,
  updated_at
FROM milestones;

-- Create function for efficient recent activities retrieval
CREATE OR REPLACE FUNCTION get_combined_recent_activities(
  p_baby_id UUID,
  p_limit INTEGER DEFAULT 20,
  p_today_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
  source_type TEXT,
  id UUID,
  baby_id UUID,
  type TEXT,
  title TEXT,
  timestamp TIMESTAMPTZ,
  notes TEXT,
  data JSONB,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
AS $$
BEGIN
  IF p_today_only THEN
    RETURN QUERY
    SELECT *
    FROM combined_activities ca
    WHERE ca.baby_id = p_baby_id
      AND ca.timestamp >= CURRENT_DATE
      AND ca.timestamp < CURRENT_DATE + INTERVAL '1 day'
    ORDER BY ca.timestamp DESC
    LIMIT p_limit;
  ELSE
    RETURN QUERY
    SELECT *
    FROM combined_activities ca
    WHERE ca.baby_id = p_baby_id
    ORDER BY ca.timestamp DESC
    LIMIT p_limit;
  END IF;
END;
$$;