-- ============================================================================
-- CREATE SCHEDULED ACTIVITIES TABLE
-- This migration creates the scheduled_activities table for the Scheduler feature
-- ============================================================================

-- Create enum for scheduled activity types
CREATE TYPE scheduled_activity_type AS ENUM (
    'sleep_reminder',
    'feeding_reminder',
    'medication_reminder',
    'doctor_appointment',
    'vaccination_appointment',
    'diaper_change_reminder',
    'bath_time',
    'tummy_time',
    'play_time',
    'shopping_trip',
    'nap_time',
    'meal_time',
    'walk_time',
    'nursing_session',
    'bottle_feeding',
    'custom_reminder'
);

-- Create enum for recurrence patterns
CREATE TYPE recurrence_pattern AS ENUM (
    'none',
    'daily',
    'weekly',
    'monthly',
    'custom'
);

-- Create scheduled_activities table
CREATE TABLE scheduled_activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    baby_id UUID REFERENCES baby_profiles(id) ON DELETE CASCADE NOT NULL,
    type scheduled_activity_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    scheduled_time TIMESTAMPTZ NOT NULL,
    notify_before_minutes INTEGER DEFAULT 10,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_pattern recurrence_pattern DEFAULT 'none',
    recurrence_interval INTEGER DEFAULT 1, -- e.g., every 2 days, every 3 weeks
    recurrence_end_date TIMESTAMPTZ,
    is_active BOOLEAN DEFAULT TRUE,
    is_completed BOOLEAN DEFAULT FALSE,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_recurrence CHECK (
        (is_recurring = FALSE AND recurrence_pattern = 'none') OR
        (is_recurring = TRUE AND recurrence_pattern != 'none')
    ),
    CONSTRAINT valid_interval CHECK (recurrence_interval > 0),
    CONSTRAINT valid_end_date CHECK (
        recurrence_end_date IS NULL OR recurrence_end_date > scheduled_time
    )
);

-- Enable RLS on scheduled_activities
ALTER TABLE scheduled_activities ENABLE ROW LEVEL SECURITY;

-- Create RLS policy for scheduled_activities
CREATE POLICY "Users can access their baby's scheduled activities" ON scheduled_activities
    FOR ALL USING (
        baby_id IN (
            SELECT id FROM baby_profiles
            WHERE user_id = auth.uid()
        )
    );

-- Create indexes for better performance
CREATE INDEX idx_scheduled_activities_baby_id ON scheduled_activities(baby_id);
CREATE INDEX idx_scheduled_activities_scheduled_time ON scheduled_activities(scheduled_time);
CREATE INDEX idx_scheduled_activities_type ON scheduled_activities(type);
CREATE INDEX idx_scheduled_activities_active ON scheduled_activities(is_active);
CREATE INDEX idx_scheduled_activities_completed ON scheduled_activities(is_completed);
CREATE INDEX idx_scheduled_activities_baby_time ON scheduled_activities(baby_id, scheduled_time);

-- Add updated_at trigger to scheduled_activities
CREATE TRIGGER update_scheduled_activities_updated_at
    BEFORE UPDATE ON scheduled_activities
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to get scheduled activities for a date range
CREATE OR REPLACE FUNCTION get_scheduled_activities_for_date_range(
    p_baby_id UUID,
    p_start_date TIMESTAMPTZ,
    p_end_date TIMESTAMPTZ
)
RETURNS TABLE (
    id UUID,
    baby_id UUID,
    type scheduled_activity_type,
    title VARCHAR(255),
    description TEXT,
    scheduled_time TIMESTAMPTZ,
    notify_before_minutes INTEGER,
    is_recurring BOOLEAN,
    recurrence_pattern recurrence_pattern,
    recurrence_interval INTEGER,
    recurrence_end_date TIMESTAMPTZ,
    is_active BOOLEAN,
    is_completed BOOLEAN,
    completed_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sa.id,
        sa.baby_id,
        sa.type,
        sa.title,
        sa.description,
        sa.scheduled_time,
        sa.notify_before_minutes,
        sa.is_recurring,
        sa.recurrence_pattern,
        sa.recurrence_interval,
        sa.recurrence_end_date,
        sa.is_active,
        sa.is_completed,
        sa.completed_at,
        sa.created_at,
        sa.updated_at
    FROM scheduled_activities sa
    WHERE sa.baby_id = p_baby_id
        AND sa.is_active = TRUE
        AND sa.scheduled_time >= p_start_date
        AND sa.scheduled_time <= p_end_date
    ORDER BY sa.scheduled_time ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get upcoming scheduled activities (for notifications)
CREATE OR REPLACE FUNCTION get_upcoming_scheduled_activities(
    p_baby_id UUID,
    p_minutes_ahead INTEGER DEFAULT 60
)
RETURNS TABLE (
    id UUID,
    baby_id UUID,
    type scheduled_activity_type,
    title VARCHAR(255),
    description TEXT,
    scheduled_time TIMESTAMPTZ,
    notify_before_minutes INTEGER,
    minutes_until_scheduled INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sa.id,
        sa.baby_id,
        sa.type,
        sa.title,
        sa.description,
        sa.scheduled_time,
        sa.notify_before_minutes,
        EXTRACT(EPOCH FROM (sa.scheduled_time - NOW()))::INTEGER / 60 as minutes_until_scheduled
    FROM scheduled_activities sa
    WHERE sa.baby_id = p_baby_id
        AND sa.is_active = TRUE
        AND sa.is_completed = FALSE
        AND sa.scheduled_time >= NOW()
        AND sa.scheduled_time <= NOW() + INTERVAL '1 hour' * p_minutes_ahead
    ORDER BY sa.scheduled_time ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to mark scheduled activity as completed
CREATE OR REPLACE FUNCTION mark_scheduled_activity_completed(
    p_activity_id UUID
)
RETURNS VOID AS $$
BEGIN
    UPDATE scheduled_activities
    SET is_completed = TRUE,
        completed_at = NOW(),
        updated_at = NOW()
    WHERE id = p_activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_scheduled_activities_for_date_range(UUID, TIMESTAMPTZ, TIMESTAMPTZ) TO authenticated;
GRANT EXECUTE ON FUNCTION get_upcoming_scheduled_activities(UUID, INTEGER) TO authenticated;
GRANT EXECUTE ON FUNCTION mark_scheduled_activity_completed(UUID) TO authenticated;

-- Show completion message
DO $$
BEGIN
    RAISE NOTICE 'Scheduled activities table created successfully!';
    RAISE NOTICE 'Added RLS policies, indexes, and utility functions';
    RAISE NOTICE 'Scheduler feature database setup complete';
END $$;
