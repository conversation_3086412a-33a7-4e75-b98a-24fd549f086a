-- Create a unified view for activities and milestones
CREATE OR REPLACE VIEW recent_activities_view AS
SELECT 
    'activity' as source_type,
    id,
    baby_id,
    activity_type as type,
    title,
    recorded_at as timestamp,
    notes,
    activity_data as data,
    created_at,
    updated_at
FROM activity_logs
UNION ALL
SELECT 
    'milestone' as source_type,
    id,
    baby_id,
    'milestone' as type,
    title,
    achieved_date as timestamp,
    notes,
    jsonb_build_object(
        'category', category,
        'milestone_type', type,
        'age_in_months', age_in_months,
        'age_in_days', age_in_days,
        'is_custom', is_custom,
        'description', description
    ) as data,
    created_at,
    updated_at
FROM milestones;

-- Create function to get recent activities efficiently
CREATE OR REPLACE FUNCTION get_recent_activities(
    p_baby_id UUID,
    p_limit INTEGER DEFAULT 20,
    p_today_only BOOLEAN DEFAULT FALSE
)
RETURNS TABLE (
    source_type TEXT,
    id UUID,
    baby_id UUID,
    type TEXT,
    title TEXT,
    timestamp TIMESTAMPTZ,
    notes TEXT,
    data JSONB,
    created_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
AS $$
BEGIN
    IF p_today_only THEN
        RETURN QUERY
        SELECT *
        FROM recent_activities_view
        WHERE recent_activities_view.baby_id = p_baby_id
        AND DATE(recent_activities_view.timestamp) = CURRENT_DATE
        ORDER BY recent_activities_view.timestamp DESC
        LIMIT p_limit;
    ELSE
        RETURN QUERY
        SELECT *
        FROM recent_activities_view
        WHERE recent_activities_view.baby_id = p_baby_id
        ORDER BY recent_activities_view.timestamp DESC
        LIMIT p_limit;
    END IF;
END;
$$;