-- Fix column name mismatches in user_profiles table
-- The app expects 'preferences' but we might have 'notification_preferences'

BEGIN;

-- Check what columns actually exist
DO $$
DECLARE
    col_exists BOOLEAN;
BEGIN
    -- Check if 'preferences' column exists, if not rename or add it
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'user_profiles' 
        AND column_name = 'preferences'
        AND table_schema = 'public'
    ) INTO col_exists;
    
    IF NOT col_exists THEN
        -- Check if we have notification_preferences instead
        SELECT EXISTS (
            SELECT 1 FROM information_schema.columns 
            WHERE table_name = 'user_profiles' 
            AND column_name = 'notification_preferences'
            AND table_schema = 'public'
        ) INTO col_exists;
        
        IF col_exists THEN
            -- Rename notification_preferences to preferences
            ALTER TABLE public.user_profiles 
            RENAME COLUMN notification_preferences TO preferences;
            RAISE NOTICE 'Renamed notification_preferences to preferences';
        ELSE
            -- Add preferences column
            ALTER TABLE public.user_profiles 
            ADD COLUMN preferences JSONB DEFAULT '{}';
            RAISE NOTICE 'Added preferences column';
        END IF;
    END IF;
END $$;

-- Ensure all required columns exist with correct names
ALTER TABLE public.user_profiles 
ADD COLUMN IF NOT EXISTS full_name TEXT,
ADD COLUMN IF NOT EXISTS role TEXT DEFAULT 'parent',
ADD COLUMN IF NOT EXISTS sign_in_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS is_email_verified BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS permissions JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS phone_number TEXT,
ADD COLUMN IF NOT EXISTS timezone TEXT,
ADD COLUMN IF NOT EXISTS provider TEXT,
ADD COLUMN IF NOT EXISTS provider_id TEXT,
ADD COLUMN IF NOT EXISTS last_sign_in_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS is_two_factor_enabled BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS family_id UUID,
ADD COLUMN IF NOT EXISTS subscription_status TEXT DEFAULT 'free',
ADD COLUMN IF NOT EXISTS last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();

-- Update existing profiles to have proper default values
UPDATE public.user_profiles 
SET 
    full_name = COALESCE(full_name, display_name, email),
    role = COALESCE(role, 'parent'),
    sign_in_count = COALESCE(sign_in_count, 0),
    is_email_verified = COALESCE(is_email_verified, false),
    preferences = COALESCE(preferences, '{}'),
    permissions = COALESCE(permissions, '{}'),
    subscription_status = COALESCE(subscription_status, 'free'),
    last_active_at = COALESCE(last_active_at, updated_at, created_at)
WHERE full_name IS NULL 
   OR role IS NULL 
   OR sign_in_count IS NULL 
   OR preferences IS NULL 
   OR permissions IS NULL;

-- Create a function to ensure user profile exists for current user
CREATE OR REPLACE FUNCTION public.ensure_user_profile_exists()
RETURNS UUID AS $$
DECLARE
    v_user_id UUID;
    v_auth_user auth.users%ROWTYPE;
BEGIN
    -- Get the authenticated user
    SELECT * INTO v_auth_user FROM auth.users WHERE id = auth.uid();
    
    IF v_auth_user.id IS NULL THEN
        RAISE EXCEPTION 'No authenticated user found';
    END IF;
    
    -- Check if profile already exists
    SELECT id INTO v_user_id 
    FROM public.user_profiles 
    WHERE auth_id = v_auth_user.id;
    
    -- If profile doesn't exist, create it
    IF v_user_id IS NULL THEN
        INSERT INTO public.user_profiles (
            auth_id,
            email,
            display_name,
            full_name,
            role,
            sign_in_count,
            is_email_verified,
            preferences,
            permissions,
            created_at,
            updated_at,
            last_active_at
        ) VALUES (
            v_auth_user.id,
            v_auth_user.email,
            COALESCE(v_auth_user.raw_user_meta_data->>'full_name', v_auth_user.email),
            COALESCE(v_auth_user.raw_user_meta_data->>'full_name', v_auth_user.email),
            'parent',
            0,
            COALESCE((v_auth_user.email_confirmed_at IS NOT NULL), false),
            '{}',
            '{}',
            NOW(),
            NOW(),
            NOW()
        ) RETURNING id INTO v_user_id;
        
        RAISE NOTICE 'Created user profile for user: %', v_auth_user.email;
    ELSE
        -- Update last_active_at for existing profile
        UPDATE public.user_profiles 
        SET last_active_at = NOW(), updated_at = NOW()
        WHERE id = v_user_id;
        
        RAISE NOTICE 'Updated existing user profile: %', v_auth_user.email;
    END IF;
    
    RETURN v_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION public.ensure_user_profile_exists() TO authenticated;

-- Update the user profile policies to be more permissive for profile creation
DROP POLICY IF EXISTS user_profiles_policy ON public.user_profiles;
CREATE POLICY user_profiles_policy ON public.user_profiles
    USING (auth.uid() = auth_id)
    WITH CHECK (auth.uid() = auth_id);

COMMIT;