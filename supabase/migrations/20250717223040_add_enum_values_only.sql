-- Safe migration to add enum values to scheduled_activity_type
-- This checks if the enum exists and adds values only if they don't already exist

DO $$
BEGIN
    -- Check if scheduled_activity_type enum exists
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'scheduled_activity_type') THEN
        
        -- Add enum values if they don't exist
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'shopping_trip' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'scheduled_activity_type')) THEN
            ALTER TYPE scheduled_activity_type ADD VALUE 'shopping_trip';
            RAISE NOTICE 'Added shopping_trip to scheduled_activity_type enum';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'nap_time' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'scheduled_activity_type')) THEN
            ALTER TYPE scheduled_activity_type ADD VALUE 'nap_time';
            RAISE NOTICE 'Added nap_time to scheduled_activity_type enum';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'meal_time' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'scheduled_activity_type')) THEN
            ALTER TYPE scheduled_activity_type ADD VALUE 'meal_time';
            RAISE NOTICE 'Added meal_time to scheduled_activity_type enum';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'walk_time' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'scheduled_activity_type')) THEN
            ALTER TYPE scheduled_activity_type ADD VALUE 'walk_time';
            RAISE NOTICE 'Added walk_time to scheduled_activity_type enum';
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM pg_enum WHERE enumlabel = 'bottle_feeding' AND enumtypid = (SELECT oid FROM pg_type WHERE typname = 'scheduled_activity_type')) THEN
            ALTER TYPE scheduled_activity_type ADD VALUE 'bottle_feeding';
            RAISE NOTICE 'Added bottle_feeding to scheduled_activity_type enum';
        END IF;
        
        RAISE NOTICE 'Successfully added missing enum values to scheduled_activity_type';
        
    ELSE
        RAISE NOTICE 'scheduled_activity_type enum does not exist yet. The scheduled activities table needs to be created first.';
    END IF;
END
$$;

-- Show current enum values
SELECT enumlabel as enum_value 
FROM pg_enum 
WHERE enumtypid = (SELECT oid FROM pg_type WHERE typname = 'scheduled_activity_type')
ORDER BY enumlabel;
