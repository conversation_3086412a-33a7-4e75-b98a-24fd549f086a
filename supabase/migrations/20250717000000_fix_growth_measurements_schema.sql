-- Fix growth_measurements schema issues
-- This migration ensures the age_in_months column exists and updates existing records

BEGIN;

-- Ensure age_in_months column exists
DO $
BEGIN
    -- Add age_in_months column if it doesn't exist
    IF NOT EXISTS (SELECT FROM information_schema.columns WHERE table_schema = 'public' AND table_name = 'growth_measurements' AND column_name = 'age_in_months') THEN
        ALTER TABLE public.growth_measurements ADD COLUMN age_in_months DECIMAL(6,2);
        RAISE NOTICE 'Added age_in_months column to growth_measurements table';
    END IF;
END $;

-- Create function to calculate age in months from birth date and measurement date if not already exists
CREATE OR REPLACE FUNCTION calculate_age_in_months(birth_date DATE, measurement_date TIMESTAMP WITH TIME ZONE)
RETURNS DECIMAL(6,2) AS $
BEGIN
    RETURN EXTRACT(EPOCH FROM (measurement_date::DATE - birth_date)) / (30.44 * 24 * 3600);
END;
$ LANGUAGE plpgsql;

-- Update existing records to populate age_in_months where missing
UPDATE public.growth_measurements gm
SET age_in_months = calculate_age_in_months(bp.birth_date, gm.measured_at)
FROM public.baby_profiles bp
WHERE gm.baby_id = bp.id 
  AND (gm.age_in_months IS NULL OR gm.age_in_months = 0)
  AND bp.birth_date IS NOT NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_growth_measurements_age_in_months ON public.growth_measurements(age_in_months);

-- Show the updated table structure
SELECT 'FIXED_GROWTH_MEASUREMENTS_COLUMNS' as info, column_name, data_type, is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'growth_measurements'
ORDER BY ordinal_position;

COMMIT;