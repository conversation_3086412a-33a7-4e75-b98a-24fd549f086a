-- Supplementary SQL to add missing functions and fix activity summary function

BEGIN;

-- <PERSON>reate function to get today's activity summary with potty support
CREATE OR REPLACE FUNCTION public.get_todays_activity_summary(p_baby_id UUID)
RETURNS TABLE (
    activity_type TEXT,
    count INTEGER,
    total_duration INTEGER,
    details JSONB
) AS $$
BEGIN
    RETURN QUERY
    WITH today_activities AS (
        SELECT 
            al.activity_type,
            COUNT(*) as activity_count,
            COALESCE(SUM(al.duration_minutes)::INTEGER, 0) as duration_minutes,
            JSONB_AGG(
                JSONB_BUILD_OBJECT(
                    'id', al.id,
                    'start_time', al.start_time,
                    'end_time', al.end_time,
                    'details', al.details,
                    'notes', al.notes,
                    'vaccine', al.details->>'vaccine',  -- Extract vaccine name
                    'batch_number', al.details->>'batch_number',  -- Extract batch number
                    'potty_type', al.details->>'potty_type',  -- Extract potty type
                    'success_level', al.details->>'success_level',  -- Extract success level
                    'location', al.details->>'location'  -- Extract location
                )
            ) as activity_details
        FROM public.activity_logs al
        WHERE al.baby_id = p_baby_id
        AND al.start_time >= DATE_TRUNC('day', NOW())
        AND al.start_time < DATE_TRUNC('day', NOW() + INTERVAL '1 day')
        GROUP BY al.activity_type
    )
    SELECT 
        a.activity_type::TEXT,
        a.activity_count::INTEGER,
        a.duration_minutes::INTEGER,
        a.activity_details::JSONB
    FROM today_activities a
    ORDER BY 
        -- Prioritize essential activities
        CASE a.activity_type 
            WHEN 'feeding' THEN 1
            WHEN 'sleep' THEN 2
            WHEN 'diaper' THEN 3
            WHEN 'medicine' THEN 4
            WHEN 'vaccination' THEN 5
            WHEN 'temperature' THEN 6
            WHEN 'potty' THEN 7  -- Add potty to priority order
            ELSE 10
        END,
        a.activity_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get milestone today summary
CREATE OR REPLACE FUNCTION public.get_milestone_today_summary(p_baby_id UUID)
RETURNS TABLE (
    category TEXT,
    count INTEGER,
    milestones JSONB
) AS $$
BEGIN
    RETURN QUERY
    WITH today_milestones AS (
        SELECT 
            m.milestone_category,
            COUNT(*) as milestone_count,
            JSONB_AGG(
                JSONB_BUILD_OBJECT(
                    'id', m.id,
                    'milestone_name', m.milestone_name,
                    'achieved_at', m.achieved_at,
                    'notes', m.notes,
                    'photo_url', m.photo_url
                )
            ) as milestone_details
        FROM public.milestones m
        WHERE m.baby_id = p_baby_id
        AND m.achieved_at >= DATE_TRUNC('day', NOW())
        AND m.achieved_at < DATE_TRUNC('day', NOW() + INTERVAL '1 day')
        GROUP BY m.milestone_category
    )
    SELECT 
        tm.milestone_category::TEXT,
        tm.milestone_count::INTEGER,
        tm.milestone_details::JSONB
    FROM today_milestones tm
    ORDER BY tm.milestone_count DESC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on the new functions
GRANT EXECUTE ON FUNCTION public.get_todays_activity_summary(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_milestone_today_summary(UUID) TO authenticated;

COMMIT;
