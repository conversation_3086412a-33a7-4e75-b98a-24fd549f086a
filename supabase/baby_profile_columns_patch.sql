-- Schema patch to add missing columns for baby profile creation
-- This adds columns that the app expects but are missing from the schema

BEGIN;

-- Add missing columns to baby_profiles table
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS birth_height DECIMAL(6,2);
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS allergies JSONB DEFAULT '[]';
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS medications JSONB DEFAULT '[]';
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS health_notes TEXT;
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS allergy_notes TEXT;
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS photo_url TEXT;

-- Add indexes for commonly queried columns
CREATE INDEX IF NOT EXISTS idx_baby_profiles_birth_height ON public.baby_profiles(birth_height);
CREATE INDEX IF NOT EXISTS idx_baby_profiles_photo_url ON public.baby_profiles(photo_url);

-- <PERSON><PERSON> function to sync birth_height with birth_length (if both are used)
CREATE OR REPLACE FUNCTION sync_birth_measurements()
RETURNS TRIGGER AS $$
BEGIN
    -- If birth_height is provided but birth_length is not, copy it
    IF NEW.birth_height IS NOT NULL AND NEW.birth_length IS NULL THEN
        NEW.birth_length = NEW.birth_height;
    END IF;
    
    -- If birth_length is provided but birth_height is not, copy it
    IF NEW.birth_length IS NOT NULL AND NEW.birth_height IS NULL THEN
        NEW.birth_height = NEW.birth_length;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to sync birth measurements
DROP TRIGGER IF EXISTS sync_birth_measurements_trigger ON public.baby_profiles;
CREATE TRIGGER sync_birth_measurements_trigger
    BEFORE INSERT OR UPDATE ON public.baby_profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_birth_measurements();

-- Create view for baby profiles with all expected columns
CREATE OR REPLACE VIEW public.baby_profiles_complete AS
SELECT 
    bp.*,
    COALESCE(bp.birth_height, bp.birth_length) as birth_height_complete,
    COALESCE(bp.birth_length, bp.birth_height) as birth_length_complete,
    COALESCE(bp.profile_image_url, bp.photo_url) as photo_complete
FROM public.baby_profiles bp;

-- Grant permissions on the new view
GRANT SELECT ON public.baby_profiles_complete TO authenticated;

COMMIT;
