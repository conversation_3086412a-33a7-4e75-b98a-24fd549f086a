-- Verification Queries for Database Schema Setup
-- Run these queries in Supabase SQL Editor to verify the setup

-- ========================================
-- 1. VERIFY ALL TABLES ARE CREATED
-- ========================================

-- Check if all required tables exist
SELECT 
    table_name,
    table_type,
    is_insertable_into
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'user_profiles', 
    'baby_profiles', 
    'baby_user_access',
    'activity_logs', 
    'scheduled_activities', 
    'growth_measurements',
    'milestones', 
    'medicine_logs', 
    'vaccination_logs',
    'ai_insights', 
    'chat_messages'
)
ORDER BY table_name;

-- Check table columns for key tables
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('baby_profiles', 'activity_logs', 'scheduled_activities')
ORDER BY table_name, ordinal_position;

-- ========================================
-- 2. VERIFY ENUMS ARE CREATED
-- ========================================

-- Check if custom enums exist
SELECT 
    typname as enum_name,
    enumlabel as enum_values
FROM pg_type t
JOIN pg_enum e ON t.oid = e.enumtypid
WHERE typname IN ('activity_type', 'scheduled_activity_type')
ORDER BY typname, enumsortorder;

-- ========================================
-- 3. VERIFY FUNCTIONS ARE CREATED
-- ========================================

-- Check if all required functions exist
SELECT 
    routine_name as function_name,
    routine_type,
    data_type as return_type,
    routine_definition IS NOT NULL as has_definition
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'update_updated_at_column',
    'calculate_age_in_months',
    'auto_calculate_age_in_months',
    'get_measurements_requiring_attention',
    'get_todays_activity_summary',
    'get_milestone_today_summary'
)
ORDER BY routine_name;

-- ========================================
-- 4. VERIFY TRIGGERS ARE CREATED
-- ========================================

-- Check if triggers exist on tables
SELECT 
    trigger_name,
    event_object_table as table_name,
    action_timing,
    event_manipulation
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
AND (trigger_name LIKE '%updated_at%' OR trigger_name LIKE '%age%')
ORDER BY event_object_table, trigger_name;

-- ========================================
-- 5. VERIFY INDEXES ARE CREATED
-- ========================================

-- Check if key indexes exist
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND tablename IN (
    'activity_logs', 
    'growth_measurements', 
    'milestones'
)
ORDER BY tablename, indexname;

-- ========================================
-- 6. VERIFY RLS POLICIES ARE ENABLED
-- ========================================

-- Check if RLS is enabled on tables
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_enabled
FROM pg_tables 
WHERE schemaname = 'public'
AND tablename IN (
    'user_profiles', 
    'baby_profiles', 
    'baby_user_access',
    'activity_logs', 
    'scheduled_activities', 
    'growth_measurements',
    'milestones', 
    'medicine_logs', 
    'vaccination_logs',
    'ai_insights', 
    'chat_messages'
)
ORDER BY tablename;

-- Check RLS policies
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    roles,
    cmd,
    qual,
    with_check
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- ========================================
-- 7. VERIFY VIEWS ARE CREATED
-- ========================================

-- Check if views exist
SELECT 
    table_name as view_name,
    view_definition
FROM information_schema.views 
WHERE table_schema = 'public'
AND table_name = 'growth_measurements_enhanced';

-- ========================================
-- 8. VERIFY PERMISSIONS ARE GRANTED
-- ========================================

-- Check table permissions for authenticated role
SELECT 
    table_schema,
    table_name,
    privilege_type,
    grantee
FROM information_schema.table_privileges 
WHERE table_schema = 'public' 
AND grantee = 'authenticated'
AND table_name IN (
    'user_profiles', 
    'baby_profiles', 
    'activity_logs'
)
ORDER BY table_name, privilege_type;

-- ========================================
-- 9. TEST BASIC FUNCTIONALITY
-- ========================================

-- Test enum values (should return without error)
SELECT 
    unnest(enum_range(NULL::activity_type)) as activity_types
ORDER BY activity_types;

SELECT 
    unnest(enum_range(NULL::scheduled_activity_type)) as scheduled_activity_types
ORDER BY scheduled_activity_types;

-- Test function execution (should return without error)
SELECT 
    calculate_age_in_months('2024-01-01'::DATE, NOW()) as age_in_months;

-- ========================================
-- 10. SUMMARY CHECK
-- ========================================

-- Get a summary of all created objects
SELECT 
    'Tables' as object_type,
    COUNT(*) as count
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'user_profiles', 'baby_profiles', 'baby_user_access',
    'activity_logs', 'scheduled_activities', 'growth_measurements',
    'milestones', 'medicine_logs', 'vaccination_logs',
    'ai_insights', 'chat_messages'
)

UNION ALL

SELECT 
    'Functions' as object_type,
    COUNT(*) as count
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'update_updated_at_column',
    'calculate_age_in_months',
    'auto_calculate_age_in_months',
    'get_measurements_requiring_attention',
    'get_todays_activity_summary',
    'get_milestone_today_summary'
)

UNION ALL

SELECT 
    'RLS Policies' as object_type,
    COUNT(*) as count
FROM pg_policies 
WHERE schemaname = 'public'

UNION ALL

SELECT 
    'Views' as object_type,
    COUNT(*) as count
FROM information_schema.views 
WHERE table_schema = 'public'
AND table_name = 'growth_measurements_enhanced'

ORDER BY object_type;
