-- Compatibility patch for app requirements
-- This adds missing columns and functions that the app expects

BEGIN;

-- Add missing columns to baby_profiles table
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS user_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL;
ALTER TABLE public.baby_profiles ADD COLUMN IF NOT EXISTS is_active BOOLEAN DEFAULT FALSE;

-- Create indexes for new columns
CREATE INDEX IF NOT EXISTS idx_baby_profiles_user_id ON public.baby_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_baby_profiles_is_active ON public.baby_profiles(is_active);

-- Create function to track user sessions (referenced in error logs)
CREATE OR REPLACE FUNCTION public.track_user_session(p_device_info TEXT, p_ip_address TEXT)
RETURNS VOID AS $$
BEGIN
    -- Simple placeholder function to prevent errors
    -- You can implement actual session tracking logic here if needed
    RETURN;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate function to sync baby_profiles with baby_user_access
CREATE OR REPLACE FUNCTION sync_baby_user_access()
<PERSON><PERSON>URNS TRIGGER AS $$
BEGIN
    -- When a baby is created, automatically create baby_user_access record
    IF TG_OP = 'INSERT' THEN
        -- Set user_id from created_by if not already set
        IF NEW.user_id IS NULL AND NEW.created_by IS NOT NULL THEN
            NEW.user_id = NEW.created_by;
        END IF;
        
        -- Create baby_user_access record for the creator
        IF NEW.user_id IS NOT NULL THEN
            INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
            VALUES (NEW.id, NEW.user_id, 'owner')
            ON CONFLICT (baby_id, user_id) DO NOTHING;
        END IF;
        
        RETURN NEW;
    END IF;
    
    -- When a baby is updated, sync the user_id
    IF TG_OP = 'UPDATE' THEN
        -- If user_id changed, update baby_user_access
        IF OLD.user_id IS DISTINCT FROM NEW.user_id THEN
            -- Remove old access if exists
            IF OLD.user_id IS NOT NULL THEN
                DELETE FROM public.baby_user_access 
                WHERE baby_id = NEW.id AND user_id = OLD.user_id;
            END IF;
            
            -- Add new access if new user_id exists
            IF NEW.user_id IS NOT NULL THEN
                INSERT INTO public.baby_user_access (baby_id, user_id, access_level)
                VALUES (NEW.id, NEW.user_id, 'owner')
                ON CONFLICT (baby_id, user_id) DO UPDATE SET access_level = 'owner';
            END IF;
        END IF;
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to sync baby_profiles with baby_user_access
DROP TRIGGER IF EXISTS sync_baby_user_access_trigger ON public.baby_profiles;
CREATE TRIGGER sync_baby_user_access_trigger
    BEFORE INSERT OR UPDATE ON public.baby_profiles
    FOR EACH ROW
    EXECUTE FUNCTION sync_baby_user_access();

-- Create function to ensure only one active baby per user
CREATE OR REPLACE FUNCTION ensure_single_active_baby()
RETURNS TRIGGER AS $$
BEGIN
    -- If setting a baby as active, make sure all other babies for this user are inactive
    IF NEW.is_active = TRUE AND (OLD.is_active IS NULL OR OLD.is_active = FALSE) THEN
        -- Deactivate all other babies for this user
        UPDATE public.baby_profiles 
        SET is_active = FALSE 
        WHERE user_id = NEW.user_id 
        AND id != NEW.id 
        AND is_active = TRUE;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to ensure only one active baby per user
DROP TRIGGER IF EXISTS ensure_single_active_baby_trigger ON public.baby_profiles;
CREATE TRIGGER ensure_single_active_baby_trigger
    AFTER UPDATE ON public.baby_profiles
    FOR EACH ROW
    EXECUTE FUNCTION ensure_single_active_baby();

-- Update existing baby_profiles to populate user_id from baby_user_access
UPDATE public.baby_profiles 
SET user_id = bua.user_id
FROM public.baby_user_access bua
WHERE baby_profiles.id = bua.baby_id 
AND baby_profiles.user_id IS NULL
AND bua.access_level = 'owner';

-- If no owner exists, use the first user with access
UPDATE public.baby_profiles 
SET user_id = bua.user_id
FROM public.baby_user_access bua
WHERE baby_profiles.id = bua.baby_id 
AND baby_profiles.user_id IS NULL
AND bua.user_id = (
    SELECT user_id 
    FROM public.baby_user_access 
    WHERE baby_id = baby_profiles.id 
    ORDER BY created_at ASC 
    LIMIT 1
);

-- Grant execute permission on the new function
GRANT EXECUTE ON FUNCTION public.track_user_session(TEXT, TEXT) TO authenticated;

COMMIT;
