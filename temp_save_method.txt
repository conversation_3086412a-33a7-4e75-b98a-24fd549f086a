  void _saveEntry() async {
    if (!isValidEntry) return;

    HapticFeedback.mediumImpact();

    try {
      // Get current baby profile ID from BabyProfileStateManager
      final babyId = _babyProfileManager.getActiveBabyId();
      if (babyId == null) {
        throw Exception('No baby profile selected');
      }

      // Prepare activity log data
      final activityLogData = {
        'baby_id': babyId,
        'activity_type': selectedActivityType,
        'activity_data': entryData,
        'logged_at': entryData['time']?.toIso8601String() ?? DateTime.now().toIso8601String(),
        'notes': entryData['notes'],
        'created_at': DateTime.now().toIso8601String(),
      };

      debugPrint('🔄 Saving activity log: \$activityLogData');

      // Save to database using SupabaseService
      await _supabaseService.insert('activity_logs', activityLogData);
      
      debugPrint('✅ Activity log saved successfully');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                '\${selectedActivityType?.toUpperCase()} logged successfully!'),
            duration: const Duration(seconds: 2),
            backgroundColor: Colors.green,
          ),
        );

        _dismissSheet();
      }
    } catch (e) {
      debugPrint('❌ Error saving activity log: \$e');
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to save log: \${e.toString()}'),
            duration: const Duration(seconds: 3),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
