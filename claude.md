# BabyTracker Pro - Project Overview for Claude AI

## Project Description
BabyTracker Pro is a comprehensive, AI-powered baby care tracking application built with Flutter. It combines traditional activity logging with AI-powered insights to help parents and caregivers monitor their baby's development, health, and daily activities.

## Technology Stack
- **Frontend**: Flutter 3.29.2 / Dart 3.6.0
- **Backend**: Supabase (PostgreSQL database, real-time subscriptions, authentication)
- **AI Integration**: OpenAI GPT-4 for chat assistant and insights
- **Platform**: Cross-platform (iOS, Android)

## Key Features

### AI-Powered Intelligence
- Smart Chat Assistant with OpenAI GPT-4 integration
- Predictive insights using ML-driven pattern recognition
- Behavioral analysis for sleep patterns, feeding habits, and development trends
- Personalized recommendations based on baby's data

### Activity Tracking
- 17+ activity types including feeding, sleep, diaper changes, medicine, temperature, etc.
- Real-time logging with quick-log bottom sheet
- Smart duration tracking with automatic timers
- Photo attachments and detailed notes

### Growth & Development
- WHO percentile charts for weight, height, and head circumference
- Milestone tracking and predictions
- Growth trend analysis
- Development pattern recognition

### Data Management
- Multi-baby support for families
- Family member collaboration with role-based permissions
- Data export capabilities
- Offline-first architecture with cloud sync

## Project Structure

### Core Directories
- `lib/` - Main Flutter application code
  - `models/` - Data models (activity_log, ai_insight, baby_profile, user_profile)
  - `services/` - Business logic and API services
  - `presentation/` - UI screens and widgets
  - `routes/` - Navigation routing
  - `theme/` - App theming and styling
  - `utils/` - Utility functions

### Key Services
- `supabase_service.dart` - Database and authentication
- `ai_chat_service.dart` - OpenAI integration for chat
- `ai_insights_service.dart` - AI-powered analytics and insights
- `auth_service.dart` - User authentication management
- `baby_profile_state_manager.dart` - Baby profile state management

### Database Schema (Supabase)
- `user_profiles` - User account information
- `baby_profiles` - Baby information and settings
- `activity_logs` - All tracked activities and events
- `ai_insights` - Generated AI insights and analytics
- `chat_messages` - AI chat conversation history
- `growth_measurements` - Growth tracking data

## Key Screens
- **Dashboard**: Main overview with recent activities and AI insights
- **Activity Timeline**: Chronological view of all logged activities
- **Quick Log**: Bottom sheet for rapid activity entry
- **AI Chat**: Interactive chat with AI assistant
- **AI Insights**: Analytics dashboard with charts and predictions
- **Growth Charts**: WHO percentile charts and growth tracking
- **Settings**: User preferences and baby management

## Development Notes

### State Management
- Uses Flutter's built-in state management with StatefulWidget
- Custom state managers for complex features (AI insights, baby profiles)
- Real-time updates via Supabase subscriptions

### Database Migrations
- Located in `supabase/migrations/`
- Handles schema evolution and data integrity
- Recent focus on fixing RLS (Row Level Security) policies

### AI Integration
- OpenAI GPT-4 for conversational AI
- Custom prompts for baby care context
- Caching mechanisms for insights to optimize API usage

### Testing
- Widget tests in `test/` directory
- Database diagnostic tools and test services
- AI insights timestamp testing

## Recent Development Focus
Based on the workspace files, recent development has focused on:
- Database schema fixes and RLS policy improvements
- AI insights caching and performance optimization
- Activity synchronization and navigation fixes
- Chart scaling and data visualization improvements
- Timestamp handling and local time fixes

## Configuration Files
- `pubspec.yaml` - Flutter dependencies and project configuration
- `analysis_options.yaml` - Dart/Flutter linting rules
- Platform-specific configs in `android/` and `ios/` directories

## Development Environment
- Requires Flutter SDK 3.29.2+
- Supabase project setup for backend
- OpenAI API key for AI features
- Platform-specific setup for iOS/Android development

This project represents a modern, feature-rich mobile application that leverages AI to provide intelligent baby care assistance while maintaining a clean, user-friendly interface built with Flutter best practices.