# Supabase Branching Guide

## What are Supabase Branches?

Think of branches like separate copies of your database:
- **Main branch** = Your live/production database (real users, real data)
- **Development branch** = Your testing database (safe to break, experiment)

## Why Use a Development Branch?

### 🚨 **Risks of working on Main:**
- Could break your live app
- Could lose real user data
- Users might see errors while you're testing
- Hard to undo changes

### ✅ **Benefits of Development branch:**
- Safe to experiment and make mistakes
- Can test schema changes without affecting users
- Easy to reset if something goes wrong
- Can preview changes before applying to production

## How to Create and Use Development Branch

### Step 1: Create Development Branch
```bash
# Create a new branch called "development"
supabase branches create development

# Switch to the development branch
supabase branches switch development
```

### Step 2: Work on Development
```bash
# Now all your commands work on the development branch
supabase db reset --linked  # Only affects development
supabase db push            # Only affects development

# Run your schema in development SQL editor
# Test baby profile creation
```

### Step 3: When Everything Works
```bash
# Switch back to main
supabase branches switch main

# Apply the working changes to production
supabase db push
```

## Current Situation Recommendation

Since you're on Main right now:

1. **Create development branch first**
2. **Test our schema changes there**
3. **Only apply to Main when working**

This way your production app stays stable while we fix the issues.