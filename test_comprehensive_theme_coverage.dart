import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

import 'lib/core/app_export.dart';
import 'lib/services/supabase_service.dart';
import 'lib/presentation/main_navigation/main_navigation_screen.dart';
import 'lib/presentation/settings/settings.dart';
import 'lib/presentation/auth/sign_in_screen.dart';
import 'lib/presentation/auth/sign_up_screen.dart';
import 'lib/presentation/tracker_screen/tracker_screen.dart';
import 'lib/presentation/milestones/milestones_screen.dart';
import 'lib/presentation/ai_insights_dashboard/ai_insights_dashboard.dart';
import 'lib/presentation/growth_charts/growth_charts.dart';
import 'lib/presentation/ai_chat_assistant/ai_chat_assistant.dart';
import 'lib/presentation/baby_profile_creation/baby_profile_creation.dart';
import 'lib/presentation/user_profile_edit/user_profile_edit_screen.dart';
import 'lib/models/baby_profile.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Services
  try {
    await SettingsService.instance.init();
    final themeService = ThemeService();
    await themeService.init();
    await SupabaseService.initialize();
    
    runApp(ThemeTestApp(themeService: themeService));
  } catch (e) {
    debugPrint('❌ Initialization error: $e');
    runApp(ThemeTestApp());
  }
}

class ThemeTestApp extends StatelessWidget {
  final ThemeService? themeService;
  
  const ThemeTestApp({super.key, this.themeService});

  @override
  Widget build(BuildContext context) {
    if (themeService != null) {
      return ChangeNotifierProvider.value(
        value: themeService!,
        child: Consumer<ThemeService>(
          builder: (context, themeService, child) {
            return Sizer(builder: (context, orientation, screenType) {
              return MaterialApp(
                navigatorKey: navigatorKey,
                title: 'Theme Coverage Test',
                theme: AppTheme.lightTheme,
                darkTheme: AppTheme.darkTheme,
                themeMode: themeService.themeMode,
                home: ThemeTestDashboard(themeService: themeService),
                debugShowCheckedModeBanner: false,
              );
            });
          },
        ),
      );
    }
    
    return Sizer(builder: (context, orientation, screenType) {
      return MaterialApp(
        navigatorKey: navigatorKey,
        title: 'Theme Coverage Test',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: ThemeTestDashboard(),
        debugShowCheckedModeBanner: false,
      );
    });
  }
}

class ThemeTestDashboard extends StatefulWidget {
  final ThemeService? themeService;
  
  const ThemeTestDashboard({super.key, this.themeService});

  @override
  State<ThemeTestDashboard> createState() => _ThemeTestDashboardState();
}

class _ThemeTestDashboardState extends State<ThemeTestDashboard> {
  final List<ThemeTestScenario> _testScenarios = [
    ThemeTestScenario(
      name: 'Main Navigation',
      description: 'Test bottom navigation, app bars, and main layout',
      screen: const MainNavigationScreen(),
      testPoints: [
        'Bottom navigation colors',
        'App bar theme consistency',
        'Background colors',
        'Icon visibility',
      ],
    ),
    ThemeTestScenario(
      name: 'Authentication Screens',
      description: 'Test sign in and sign up forms',
      screen: const SignInScreen(),
      testPoints: [
        'Form field backgrounds',
        'Button colors and contrast',
        'Text field borders',
        'Error message visibility',
      ],
    ),
    ThemeTestScenario(
      name: 'Settings Screen',
      description: 'Test settings UI and theme toggle',
      screen: const SettingsScreen(),
      testPoints: [
        'List tile backgrounds',
        'Switch and toggle colors',
        'Divider visibility',
        'Theme toggle feedback',
      ],
    ),
    ThemeTestScenario(
      name: 'Tracker Screen',
      description: 'Test activity tracking interface',
      screen: const TrackerScreen(),
      testPoints: [
        'Activity card backgrounds',
        'Quick action button colors',
        'Expansion tile themes',
        'FAB visibility',
      ],
    ),
    ThemeTestScenario(
      name: 'Milestones Screen',
      description: 'Test milestone tracking and statistics',
      screen: const MilestonesScreen(),
      testPoints: [
        'Milestone card themes',
        'Tab bar colors',
        'Statistics widget backgrounds',
        'Progress indicator colors',
      ],
    ),
    ThemeTestScenario(
      name: 'AI Insights Dashboard',
      description: 'Test AI insights and charts',
      screen: AIInsightsDashboard(babyProfile: _createMockBabyProfile()),
      testPoints: [
        'Chart background colors',
        'Insight card themes',
        'Data visualization colors',
        'Loading state themes',
      ],
    ),
    ThemeTestScenario(
      name: 'Growth Charts',
      description: 'Test growth chart visualization',
      screen: GrowthCharts(babyProfile: _createMockBabyProfile()),
      testPoints: [
        'Chart line colors',
        'Background themes',
        'Legend visibility',
        'Data point colors',
      ],
    ),
    ThemeTestScenario(
      name: 'AI Chat Assistant',
      description: 'Test chat interface and messages',
      screen: AIChatAssistant(
        babyProfile: _createMockBabyProfile(),
        recentActivities: const [],
      ),
      testPoints: [
        'Message bubble colors',
        'Input field themes',
        'Send button visibility',
        'Chat background',
      ],
    ),
    ThemeTestScenario(
      name: 'Baby Profile Creation',
      description: 'Test profile creation forms',
      screen: const BabyProfileCreation(),
      testPoints: [
        'Form field themes',
        'Date picker colors',
        'Image picker backgrounds',
        'Save button visibility',
      ],
    ),
    ThemeTestScenario(
      name: 'User Profile Edit',
      description: 'Test user profile editing',
      screen: const UserProfileEditScreen(),
      testPoints: [
        'Profile form themes',
        'Avatar picker colors',
        'Input validation colors',
        'Action button themes',
      ],
    ),
  ];

  static BabyProfile _createMockBabyProfile() {
    return BabyProfile(
      id: 'mock-id',
      name: 'Test Baby',
      birthDate: DateTime.now().subtract(const Duration(days: 90)),
      gender: 'Other',
      userId: 'mock-user-id',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Theme Coverage Test Dashboard'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          if (widget.themeService != null)
            IconButton(
              icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
              onPressed: () => widget.themeService!.toggleTheme(),
              tooltip: 'Toggle Theme',
            ),
          IconButton(
            icon: const Icon(Icons.info_outline),
            onPressed: () => _showTestInstructions(context),
            tooltip: 'Test Instructions',
          ),
        ],
      ),
      body: Column(
        children: [
          // Theme Status Card
          Container(
            margin: EdgeInsets.all(4.w),
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  isDark ? Icons.dark_mode : Icons.light_mode,
                  color: Theme.of(context).colorScheme.primary,
                  size: 8.w,
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Current Theme: ${isDark ? 'Dark' : 'Light'}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                      Text(
                        'Test all screens in both themes',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onPrimaryContainer.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.themeService != null)
                  ElevatedButton.icon(
                    onPressed: () => widget.themeService!.toggleTheme(),
                    icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
                    label: Text(isDark ? 'Light' : 'Dark'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                    ),
                  ),
              ],
            ),
          ),
          
          // Test Scenarios List
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              itemCount: _testScenarios.length,
              itemBuilder: (context, index) {
                final scenario = _testScenarios[index];
                return _buildTestScenarioCard(context, scenario);
              },
            ),
          ),
          
          // Test Summary
          Container(
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: Theme.of(context).colorScheme.outline.withOpacity(0.3),
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    '${_testScenarios.length} screens to test',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => _runAutomatedTests(context),
                  icon: const Icon(Icons.play_arrow),
                  label: const Text('Run Tests'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).colorScheme.secondary,
                    foregroundColor: Theme.of(context).colorScheme.onSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTestScenarioCard(BuildContext context, ThemeTestScenario scenario) {
    return Card(
      margin: EdgeInsets.only(bottom: 3.w),
      child: InkWell(
        onTap: () => _navigateToTestScreen(context, scenario),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(4.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(2.w),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.phone_android,
                      color: Theme.of(context).colorScheme.primary,
                      size: 6.w,
                    ),
                  ),
                  SizedBox(width: 3.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          scenario.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          scenario.description,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface.withOpacity(0.7),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
                    size: 4.w,
                  ),
                ],
              ),
              SizedBox(height: 2.h),
              Text(
                'Test Points:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 1.h),
              ...scenario.testPoints.map((point) => Padding(
                padding: EdgeInsets.only(bottom: 0.5.h),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: Theme.of(context).colorScheme.primary,
                      size: 4.w,
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        point,
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }

  void _navigateToTestScreen(BuildContext context, ThemeTestScenario scenario) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ThemeTestWrapper(
          scenario: scenario,
          themeService: widget.themeService,
        ),
      ),
    );
  }

  void _showTestInstructions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Theme Testing Instructions'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Manual Testing Steps:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 2.h),
              _buildInstructionStep('1', 'Test each screen in light theme'),
              _buildInstructionStep('2', 'Switch to dark theme using toggle'),
              _buildInstructionStep('3', 'Verify all elements are visible'),
              _buildInstructionStep('4', 'Check contrast and readability'),
              _buildInstructionStep('5', 'Test interactive elements'),
              _buildInstructionStep('6', 'Verify navigation consistency'),
              SizedBox(height: 2.h),
              Text(
                'Look for:',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 1.h),
              _buildCheckPoint('✓ Proper color contrast'),
              _buildCheckPoint('✓ Readable text in both themes'),
              _buildCheckPoint('✓ Visible interactive elements'),
              _buildCheckPoint('✓ Consistent navigation colors'),
              _buildCheckPoint('✓ Appropriate card/container backgrounds'),
              _buildCheckPoint('✓ Working form elements'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  Widget _buildInstructionStep(String number, String instruction) {
    return Padding(
      padding: EdgeInsets.only(bottom: 1.h),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6.w,
            height: 6.w,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10.sp,
                ),
              ),
            ),
          ),
          SizedBox(width: 3.w),
          Expanded(
            child: Text(
              instruction,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCheckPoint(String point) {
    return Padding(
      padding: EdgeInsets.only(bottom: 0.5.h),
      child: Text(
        point,
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  void _runAutomatedTests(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Automated Tests'),
        content: const Text(
          'Automated theme tests can be run using:\n\n'
          'flutter test test/theme_validation_test.dart\n\n'
          'This will validate theme consistency programmatically.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}

class ThemeTestWrapper extends StatelessWidget {
  final ThemeTestScenario scenario;
  final ThemeService? themeService;
  
  const ThemeTestWrapper({
    super.key,
    required this.scenario,
    this.themeService,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    
    return Scaffold(
      appBar: AppBar(
        title: Text('Testing: ${scenario.name}'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        foregroundColor: Theme.of(context).colorScheme.onSurface,
        actions: [
          if (themeService != null)
            IconButton(
              icon: Icon(isDark ? Icons.light_mode : Icons.dark_mode),
              onPressed: () => themeService!.toggleTheme(),
              tooltip: 'Toggle Theme',
            ),
          IconButton(
            icon: const Icon(Icons.checklist),
            onPressed: () => _showTestChecklist(context),
            tooltip: 'Test Checklist',
          ),
        ],
      ),
      body: scenario.screen,
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showTestChecklist(context),
        icon: const Icon(Icons.checklist),
        label: const Text('Checklist'),
        backgroundColor: Theme.of(context).colorScheme.secondary,
        foregroundColor: Theme.of(context).colorScheme.onSecondary,
      ),
    );
  }

  void _showTestChecklist(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('${scenario.name} Checklist'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'Verify these points in both light and dark themes:',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 2.h),
              ...scenario.testPoints.map((point) => CheckboxListTile(
                title: Text(
                  point,
                  style: Theme.of(context).textTheme.bodySmall,
                ),
                value: false,
                onChanged: (value) {},
                dense: true,
                contentPadding: EdgeInsets.zero,
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class ThemeTestScenario {
  final String name;
  final String description;
  final Widget screen;
  final List<String> testPoints;

  ThemeTestScenario({
    required this.name,
    required this.description,
    required this.screen,
    required this.testPoints,
  });
}