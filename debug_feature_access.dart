// Debug script to test feature access system
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'lib/models/feature_access.dart';
import 'lib/presentation/subscription/controllers/feature_access_controller.dart';
import 'lib/presentation/subscription/controllers/subscription_controller.dart';

void debugFeatureAccess(BuildContext context) {
  final featureController = Provider.of<FeatureAccessController>(context, listen: false);
  final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
  
  print('=== FEATURE ACCESS DEBUG ===');
  print('Current subscription: ${subscriptionController.currentSubscription}');
  print('Subscription status: ${subscriptionController.currentSubscription.status}');
  
  for (final feature in AppFeature.values) {
    final hasAccess = featureController.canAccessFeature(feature);
    print('${feature.displayName}: ${hasAccess ? "✅ ALLOWED" : "❌ BLOCKED"}');
  }
  print('=== END DEBUG ===');
}