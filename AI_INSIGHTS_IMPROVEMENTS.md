# AI Insights Generation Improvements

## Changes Made

### 1. User Activity Tracking
- **Issue**: The logic "After 24 hours: Automatically generates fresh insights" was running even when users weren't using the app
- **Solution**: Added user activity tracking to only generate fresh insights when:
  - User is actively using the app (within last 30 minutes)
  - Cache is 24+ hours old 
  - There are new activity logs since last update

**Implementation**:
- Added `_lastUserActivity` and `_lastAppOpen` tracking in `AIInsightsStateManager`
- Added `trackUserActivity()` method called when:
  - User opens the app
  - User adds new activity logs
  - User interacts with the app
- Added `isUserActive` getter to check if user is active within 30-minute threshold
- Added `clearUserActivity()` for when app goes to background

### 2. New Logs Validation
- **Issue**: Fresh insights were generated even without new data
- **Solution**: Added validation to check for new activity logs before generating insights

**Implementation**:
- Enhanced `manualRefresh()` to check `hasNewActivitiesSince()` before proceeding
- Added automatic cache validation with new data checks
- Only auto-generate fresh insights if all conditions met:
  1. <PERSON><PERSON> is 24+ hours old
  2. User is currently active
  3. New activities exist since last cache

### 3. Better User Messaging
- **Issue**: Generic error messages when no new insights available
- **Solution**: Improved messaging to distinguish between different scenarios

**Implementation**:
- Updated manual refresh flows in:
  - `home.dart`
  - `ai_insights_dashboard.dart` 
  - `dashboard.dart`
- Added specific message: "No new insights available. Continue logging activities to receive updated insights and recommendations."
- Distinguished between rate limiting vs no new data scenarios

### 4. Error Handling Improvements
- **Issue**: Error in logs: "type '_Map<dynamic, dynamic>' is not a subtype of type 'Map<String, dynamic>?'"
- **Solution**: Added proper error handling and type safety

**Implementation**:
- Wrapped `_generateOverallSummary()` calls in try-catch blocks
- Added fallback summary objects when errors occur
- Improved type casting and null safety throughout

## User Experience Flow

### Before Changes:
1. User opens app → Always generates fresh insights if 24h+ old
2. User clicks refresh → Always attempts to generate, shows generic error if fails
3. Backend continuously generates insights even when user not active

### After Changes:
1. User opens app → Only generates fresh insights if:
   - User is active AND
   - Cache is 24h+ old AND  
   - New activities exist
2. User clicks refresh → 
   - Checks for new data first
   - Shows "No new insights available..." if no new data
   - Shows "Manual refresh failed or rate limited" if rate limited
3. Background operations reduced - only when user is actively using app

## Technical Details

### User Activity Conditions:
```dart
// Only auto-generate if ALL conditions met:
if (cacheAge.inHours >= 24) {
  if (isUserActive) {
    final hasNewData = await _aiAnalysisService.hasNewActivitiesSince(babyProfile.id, parsedTime);
    if (hasNewData) {
      // Generate fresh insights
    }
  }
}
```

### Manual Refresh Logic:
```dart
// Check for new data before generating
if (!forceRefresh && _lastUpdateTime != null) {
  final hasNewData = await _aiAnalysisService.hasNewActivitiesSince(babyProfile.id, _lastUpdateTime!);
  if (!hasNewData) {
    return false; // No new data available
  }
}
```

## Benefits

1. **Reduced Backend Load**: No unnecessary AI calls when user isn't active
2. **Better UX**: Clear messaging about why refresh isn't available
3. **Smart Caching**: Only refreshes when there's actually new data to analyze
4. **User-Centric**: Respects user's app usage patterns
5. **Error Resilience**: Proper fallbacks when AI generation fails

## Message Examples

- **No New Data**: "No new insights available. Continue logging activities to receive updated insights and recommendations."
- **Rate Limited**: "Manual refresh failed or rate limited" 
- **Success**: "AI insights refreshed successfully"
- **User Not Active**: Logs show "Auto-refresh skipped: User not active (background mode)"
- **No New Activities**: Logs show "Auto-refresh skipped: No new activities since last cache"
