import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'lib/presentation/growth_charts/widgets/chart_toolbar_widget.dart';
import 'lib/presentation/growth_charts/widgets/recent_measurements_widget.dart';
import 'lib/presentation/growth_charts/widgets/measurement_selector_widget.dart';
import 'lib/theme/app_theme.dart';

void main() {
  group('Growth Charts Dark Theme Tests', () {
    testWidgets('ChartToolbarWidget renders without errors in dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.darkTheme,
          home: Scaffold(
            body: ChartToolbarWidget(
              selectedDateRange: '1 year',
              isMetric: true,
              onDateRangeChanged: (range) {},
              onUnitToggle: (metric) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.text('Time Period'), findsOneWidget);
      expect(find.text('Units'), findsOneWidget);
    });

    testWidgets('RecentMeasurementsWidget renders without errors in dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.darkTheme,
          home: Scaffold(
            body: RecentMeasurementsWidget(
              measurements: [],
              measurementType: 0,
              isMetric: true,
              onEdit: (index) {},
              onDelete: (index) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.text('Recent Measurements'), findsOneWidget);
    });

    testWidgets('MeasurementSelectorWidget renders without errors in dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.darkTheme,
          home: Scaffold(
            body: MeasurementSelectorWidget(
              selectedIndex: 0,
              onChanged: (index) {},
            ),
          ),
        ),
      );

      // Verify the widget renders without errors
      expect(find.text('Weight'), findsOneWidget);
      expect(find.text('Height'), findsOneWidget);
      expect(find.text('Head'), findsOneWidget);
    });
  });
}