#!/usr/bin/env dart

/// Comprehensive script to fix all remaining theme and code issues
/// This addresses all the critical problems found in the analysis

import 'dart:io';

void main() async {
  print('🔧 Applying comprehensive theme and code fixes...\n');
  
  await fixThemeConsistencyIssues();
  await createThemeHelperUtility();
  
  print('\n✅ All fixes applied successfully!');
  print('📋 Summary of changes:');
  print('   • Fixed hardcoded color references');
  print('   • Created ThemeHelper utility');
  print('   • Improved theme consistency');
  print('   • Enhanced code maintainability');
  print('\n🧪 Next steps:');
  print('   1. Test the app in both light and dark modes');
  print('   2. Verify all components render correctly');
  print('   3. Run flutter analyze to check for any remaining issues');
}

Future<void> fixThemeConsistencyIssues() async {
  print('🎨 Fixing theme consistency issues...');
  
  // Fix remaining hardcoded colors in various files
  final colorFixes = <String, Map<String, String>>{
    'lib/widgets/custom_image_widget.dart': {
      'Colors.grey[200]': 'Theme.of(context).colorScheme.surfaceVariant',
    },
    'lib/widgets/shared/today_summary_card_widget.dart': {
      'Colors.grey[600]': 'Theme.of(context).colorScheme.onSurfaceVariant',
    },
    'lib/widgets/shared/growth_chart_preview_widget.dart': {
      'Colors.grey[800]': 'Theme.of(context).colorScheme.onSurface',
    },
    'lib/presentation/babies_management_screen.dart': {
      'Colors.grey[400]': 'Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.6)',
      'Colors.grey[600]': 'Theme.of(context).colorScheme.onSurfaceVariant',
      'Colors.grey[500]': 'Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8)',
      'AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(': 'Theme.of(context).textTheme.headlineSmall?.copyWith(',
      'AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(': 'Theme.of(context).textTheme.bodyMedium?.copyWith(',
    },
    'lib/presentation/user_profile_edit/user_profile_edit_screen.dart': {
      'Colors.grey[600]': 'Theme.of(context).colorScheme.onSurfaceVariant',
      'Colors.grey[100]': 'Theme.of(context).colorScheme.surfaceVariant',
      'Colors.grey.shade300': 'Theme.of(context).colorScheme.outline.withValues(alpha: 0.5)',
      'Colors.grey[700]': 'Theme.of(context).colorScheme.onSurface',
      'AppTheme.lightTheme.textTheme.bodySmall?.copyWith(': 'Theme.of(context).textTheme.bodySmall?.copyWith(',
      'AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(': 'Theme.of(context).textTheme.bodyMedium?.copyWith(',
    },
    'lib/presentation/ai_chat/widgets/chat_message_widget.dart': {
      'Colors.grey[600]': 'Theme.of(context).colorScheme.onSurfaceVariant',
      'Colors.grey[500]': 'Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8)',
      'Colors.grey[800]!': 'Theme.of(context).colorScheme.surfaceVariant',
      'Colors.grey[100]!': 'Theme.of(context).colorScheme.surface',
      'Colors.grey[200]!': 'Theme.of(context).colorScheme.onSurface',
    },
    'lib/presentation/ai_chat/widgets/quick_topics_widget.dart': {
      'Colors.grey[800]': 'Theme.of(context).colorScheme.onSurface',
      'Colors.grey[600]': 'Theme.of(context).colorScheme.onSurfaceVariant',
    },
    'lib/presentation/ai_chat/widgets/chat_input_widget.dart': {
      'Colors.grey[800]': 'Theme.of(context).colorScheme.surfaceVariant',
      'Colors.grey[50]': 'Theme.of(context).colorScheme.surface',
      'Colors.grey[600]!': 'Theme.of(context).colorScheme.outline',
    },
  };
  
  for (final entry in colorFixes.entries) {
    final filePath = entry.key;
    final replacements = entry.value;
    
    final file = File(filePath);
    if (!file.existsSync()) {
      print('⚠️  File not found: $filePath');
      continue;
    }
    
    print('🔧 Processing: $filePath');
    String content = await file.readAsString();
    bool hasChanges = false;
    
    for (final replacement in replacements.entries) {
      if (content.contains(replacement.key)) {
        content = content.replaceAll(replacement.key, replacement.value);
        hasChanges = true;
      }
    }
    
    if (hasChanges) {
      await file.writeAsString(content);
      print('   ✅ Updated');
    } else {
      print('   ℹ️  No changes needed');
    }
  }
}

Future<void> createThemeHelperUtility() async {
  print('🛠️  Creating ThemeHelper utility...');
  
  final themeHelperContent = '''
import 'package:flutter/material.dart';
import '../theme/app_theme.dart';

/// Utility class for theme-aware color and style helpers
/// Provides consistent access to theme colors across the app
class ThemeHelper {
  ThemeHelper._();

  /// Get warning color based on current theme
  static Color getWarningColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.warningDark
        : AppTheme.warningLight;
  }

  /// Get success color based on current theme
  static Color getSuccessColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.successDark
        : AppTheme.successLight;
  }

  /// Get accent color based on current theme
  static Color getAccentColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.accentDark
        : AppTheme.accentLight;
  }

  /// Get primary text color based on current theme
  static Color getPrimaryTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.textPrimaryDark
        : AppTheme.textPrimaryLight;
  }

  /// Get secondary text color based on current theme
  static Color getSecondaryTextColor(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? AppTheme.textSecondaryDark
        : AppTheme.textSecondaryLight;
  }

  /// Get surface color with proper contrast
  static Color getSurfaceColor(BuildContext context) {
    return Theme.of(context).colorScheme.surface;
  }

  /// Get outline color with proper opacity
  static Color getOutlineColor(BuildContext context, {double alpha = 0.3}) {
    return Theme.of(context).colorScheme.outline.withValues(alpha: alpha);
  }

  /// Get data text style using JetBrains Mono for numerical values
  static TextStyle getDataTextStyle(BuildContext context, {
    double fontSize = 16,
    FontWeight fontWeight = FontWeight.w400,
  }) {
    return AppTheme.getDataTextStyle(
      isLight: Theme.of(context).brightness == Brightness.light,
      fontSize: fontSize,
      fontWeight: fontWeight,
    );
  }

  /// Check if current theme is dark mode
  static bool isDarkMode(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark;
  }

  /// Get appropriate color for status indicators
  static Color getStatusColor(BuildContext context, String status) {
    switch (status.toLowerCase()) {
      case 'success':
      case 'completed':
      case 'good':
        return getSuccessColor(context);
      case 'warning':
      case 'attention':
      case 'medium':
        return getWarningColor(context);
      case 'error':
      case 'failed':
      case 'high':
        return Theme.of(context).colorScheme.error;
      default:
        return Theme.of(context).colorScheme.primary;
    }
  }

  /// Get card decoration with proper theme colors
  static BoxDecoration getCardDecoration(BuildContext context, {
    double borderRadius = 12.0,
    bool hasBorder = true,
    Color? borderColor,
  }) {
    return BoxDecoration(
      color: Theme.of(context).colorScheme.surface,
      borderRadius: BorderRadius.circular(borderRadius),
      border: hasBorder ? Border.all(
        color: borderColor ?? getOutlineColor(context),
      ) : null,
    );
  }

  /// Get input decoration with theme colors
  static InputDecoration getInputDecoration(BuildContext context, {
    String? labelText,
    String? hintText,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return InputDecoration(
      labelText: labelText,
      hintText: hintText,
      prefixIcon: prefixIcon,
      suffixIcon: suffixIcon,
      filled: true,
      fillColor: Theme.of(context).colorScheme.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: getOutlineColor(context)),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: getOutlineColor(context)),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12),
        borderSide: BorderSide(color: Theme.of(context).colorScheme.primary),
      ),
    );
  }
}
''';

  final themeHelperFile = File('lib/utils/theme_helper.dart');
  await themeHelperFile.writeAsString(themeHelperContent);
  print('   ✅ Created lib/utils/theme_helper.dart');
}