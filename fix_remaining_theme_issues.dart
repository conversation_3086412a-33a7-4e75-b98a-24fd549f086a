#!/usr/bin/env dart

/// Comprehensive script to fix all remaining hardcoded color references
/// This addresses the critical theme consistency issues found in the analysis

import 'dart:io';

void main() async {
  print('🎨 Fixing remaining critical theme consistency issues...\n');
  
  // Define comprehensive replacement patterns for hardcoded colors
  final replacements = <String, String>{
    // Grey color replacements
    'Colors.grey[200]': 'Theme.of(context).colorScheme.surfaceVariant',
    'Colors.grey[300]': 'Theme.of(context).colorScheme.outline.withValues(alpha: 0.5)',
    'Colors.grey[400]': 'Theme.of(context).colorScheme.onSurfaceVariant',
    'Colors.grey[500]': 'Theme.of(context).colorScheme.onSurfaceVariant.withValues(alpha: 0.8)',
    'Colors.grey[600]': 'Theme.of(context).colorScheme.onSurfaceVariant',
    'Colors.grey[700]': 'Theme.of(context).colorScheme.onSurface',
    'Colors.grey[800]': 'Theme.of(context).colorScheme.onSurface',
    'Colors.grey.shade300': 'Theme.of(context).colorScheme.outline.withValues(alpha: 0.5)',
    
    // Blue color replacements
    'Colors.blue[600]': 'Theme.of(context).colorScheme.primary',
    'Colors.blue[700]': 'Theme.of(context).colorScheme.primary',
    
    // Orange/Warning color replacements
    'Colors.orange[600]': 'ThemeHelper.getWarningColor(context)',
    'Colors.orange[700]': 'ThemeHelper.getWarningColor(context)',
    'Colors.orange[800]': 'ThemeHelper.getWarningColor(context)',
    
    // Green/Success color replacements
    'Colors.green': 'ThemeHelper.getSuccessColor(context)',
    'Colors.lightGreen': 'ThemeHelper.getSuccessColor(context).withValues(alpha: 0.8)',
    
    // Hardcoded background colors
    'Colors.grey[50]': 'Theme.of(context).colorScheme.surface',
    'Colors.grey[100]': 'Theme.of(context).colorScheme.surfaceVariant',
    
    // AppTheme.lightTheme references that should be theme-aware
    'AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(': 'Theme.of(context).textTheme.headlineSmall?.copyWith(',
    'AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(': 'Theme.of(context).textTheme.bodyMedium?.copyWith(',
    'AppTheme.lightTheme.textTheme.bodySmall?.copyWith(': 'Theme.of(context).textTheme.bodySmall?.copyWith(',
  };
  
  // Files that need theme helper import
  final filesNeedingThemeHelper = <String>{
    'lib/presentation/ai_insights_dashboard/ai_insights_dashboard.dart',
    'lib/widgets/shared/today_summary_card_widget.dart',
    'lib/presentation/ai_insights/widgets/insight_card_widget.dart',
  };
  
  // Files to process
  final filesToProcess = [
    'lib/widgets/custom_image_widget.dart',
    'lib/widgets/shared/today_summary_card_widget.dart',
    'lib/widgets/shared/growth_chart_preview_widget.dart',
    'lib/presentation/ai_insights_dashboard/ai_insights_dashboard.dart',
    'lib/presentation/babies_management_screen.dart',
    'lib/presentation/user_profile_edit/user_profile_edit_screen.dart',
    'lib/presentation/ai_chat/widgets/chat_message_widget.dart',
    'lib/presentation/ai_chat/widgets/quick_topics_widget.dart',
    'lib/presentation/ai_insights/widgets/insight_card_widget.dart',
    'lib/presentation/ai_chat/widgets/chat_input_widget.dart',
  ];
  
  int totalReplacements = 0;
  
  for (final filePath in filesToProcess) {
    final file = File(filePath);
    if (!file.existsSync()) {
      print('⚠️  File not found: $filePath');
      continue;
    }
    
    print('🔧 Processing: $filePath');
    String content = await file.readAsString();
    int fileReplacements = 0;
    
    // Add ThemeHelper import if needed
    if (filesNeedingThemeHelper.contains(filePath) && 
        (content.contains('Colors.orange') || content.contains('Colors.green')) &&
        !content.contains("import '../../../utils/theme_helper.dart';") &&
        !content.contains("import '../../utils/theme_helper.dart';")) {
      
      // Determine correct import path based on file location
      String importPath;
      if (filePath.contains('presentation/')) {
        importPath = "import '../../../utils/theme_helper.dart';";
      } else {
        importPath = "import '../../utils/theme_helper.dart';";
      }
      
      // Add import after existing imports
      final importIndex = content.lastIndexOf("import ");
      if (importIndex != -1) {
        final endOfImport = content.indexOf('\n', importIndex);
        content = content.substring(0, endOfImport + 1) + 
                 "$importPath\n" + 
                 content.substring(endOfImport + 1);
        fileReplacements++;
      }
    }
    
    // Apply replacements
    for (final entry in replacements.entries) {
      final oldPattern = entry.key;
      final newPattern = entry.value;
      
      if (content.contains(oldPattern)) {
        content = content.replaceAll(oldPattern, newPattern);
        fileReplacements++;
      }
    }
    
    if (fileReplacements > 0) {
      await file.writeAsString(content);
      print('   ✅ Made $fileReplacements replacements');
      totalReplacements += fileReplacements;
    } else {
      print('   ℹ️  No changes needed');
    }
  }
  
  print('\n🎉 Critical theme consistency fixes completed!');
  print('📊 Total replacements made: $totalReplacements');
  print('\n📝 Next steps:');
  print('   1. Test the app in both light and dark modes');
  print('   2. Verify all components render correctly');
  print('   3. Check navigation and interactive elements');
  print('   4. Validate accessibility compliance');
  print('\n⚠️  Important: Some files may need manual review for complex color logic');
}