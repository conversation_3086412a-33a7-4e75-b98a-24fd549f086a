/// Direct test of subscription system
/// Run with: flutter run test_direct_subscription.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:provider/provider.dart';

// Core imports
import 'lib/services/supabase_service.dart';
import 'lib/services/feature_access_service.dart';
import 'lib/presentation/subscription/controllers/subscription_controller.dart';
import 'lib/presentation/subscription/controllers/feature_access_controller.dart';
import 'lib/models/feature_access.dart';
import 'lib/presentation/subscription/widgets/feature_gate.dart';
import 'lib/services/theme_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🚀 Starting direct subscription test...');
  
  // Initialize Supabase first
  try {
    await SupabaseService.initialize();
    print('✅ Supabase initialized');
  } catch (e) {
    print('❌ Supabase initialization failed: $e');
    return;
  }
  
  // Initialize theme service
  final themeService = ThemeService();
  await themeService.init();
  
  runApp(DirectSubscriptionTestApp(themeService: themeService));
}

class DirectSubscriptionTestApp extends StatelessWidget {
  final ThemeService themeService;
  
  const DirectSubscriptionTestApp({super.key, required this.themeService});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider.value(value: themeService),
        ChangeNotifierProvider(
          create: (_) {
            print('🔧 Creating SubscriptionController...');
            final controller = SubscriptionController();
            // Initialize immediately
            controller.initialize();
            return controller;
          },
        ),
        ChangeNotifierProxyProvider<SubscriptionController, FeatureAccessService>(
          create: (context) {
            print('🔧 Creating FeatureAccessService...');
            return FeatureAccessService(
              Provider.of<SubscriptionController>(context, listen: false),
            );
          },
          update: (context, subscription, previous) =>
            previous ?? FeatureAccessService(subscription),
        ),
        ChangeNotifierProxyProvider<FeatureAccessService, FeatureAccessController>(
          create: (context) {
            print('🔧 Creating FeatureAccessController...');
            return FeatureAccessController(
              Provider.of<FeatureAccessService>(context, listen: false),
            );
          },
          update: (context, featureAccess, previous) =>
            previous ?? FeatureAccessController(featureAccess),
        ),
      ],
      child: Sizer(
        builder: (context, orientation, screenType) {
          return MaterialApp(
            title: 'Direct Subscription Test',
            theme: ThemeData(
              primarySwatch: Colors.blue,
              useMaterial3: true,
            ),
            home: DirectTestScreen(),
          );
        },
      ),
    );
  }
}

class DirectTestScreen extends StatefulWidget {
  @override
  _DirectTestScreenState createState() => _DirectTestScreenState();
}

class _DirectTestScreenState extends State<DirectTestScreen> {
  bool _testCompleted = false;
  String _testResults = 'Running tests...';

  @override
  void initState() {
    super.initState();
    // Run tests after a short delay to ensure providers are ready
    Future.delayed(Duration(seconds: 1), _runDirectTests);
  }

  Future<void> _runDirectTests() async {
    final buffer = StringBuffer();
    buffer.writeln('📊 DIRECT SUBSCRIPTION SYSTEM TEST RESULTS\n');

    try {
      // Test 1: Direct Supabase subscription lookup
      buffer.writeln('🔍 Test 1: Direct Supabase Subscription Lookup');
      final supabaseService = SupabaseService();
      final directResult = await supabaseService.getCurrentUserSubscription();
      buffer.writeln('Result: $directResult\n');

      // Test 2: SubscriptionController test
      buffer.writeln('🔍 Test 2: SubscriptionController');
      final subscriptionController = Provider.of<SubscriptionController>(context, listen: false);
      buffer.writeln('Controller created: ${subscriptionController != null}');
      buffer.writeln('Current subscription plan: ${subscriptionController.currentSubscription.planName}');
      buffer.writeln('Is premium: ${subscriptionController.currentSubscription.isPremium}');
      buffer.writeln('Status: ${subscriptionController.currentSubscription.status}\n');

      // Test 3: FeatureAccessService test
      buffer.writeln('🔍 Test 3: FeatureAccessService');
      final featureAccessService = Provider.of<FeatureAccessService>(context, listen: false);
      buffer.writeln('Service created: ${featureAccessService != null}');
      
      final aiChatAccess = featureAccessService.hasFeatureAccess(AppFeature.aiChat);
      buffer.writeln('AI Chat access: $aiChatAccess');
      
      final aiInsightsAccess = featureAccessService.hasFeatureAccess(AppFeature.aiInsights);
      buffer.writeln('AI Insights access: $aiInsightsAccess');
      
      final growthChartsAccess = featureAccessService.hasFeatureAccess(AppFeature.whoGrowthCharts);
      buffer.writeln('Growth Charts access: $growthChartsAccess\n');

      // Test 4: FeatureAccessController test
      buffer.writeln('🔍 Test 4: FeatureAccessController');
      final featureAccessController = Provider.of<FeatureAccessController>(context, listen: false);
      buffer.writeln('Controller created: ${featureAccessController != null}');
      
      final canAccessAiChat = featureAccessController.canAccessFeature(AppFeature.aiChat);
      buffer.writeln('Can access AI Chat: $canAccessAiChat');
      
      buffer.writeln('\n✅ All tests completed!');
      
    } catch (e, stackTrace) {
      buffer.writeln('❌ Test error: $e');
      buffer.writeln('Stack trace: $stackTrace');
    }

    setState(() {
      _testResults = buffer.toString();
      _testCompleted = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Direct Subscription Test'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!_testCompleted) ...[
              Center(child: CircularProgressIndicator()),
              SizedBox(height: 16),
              Text('Running subscription system tests...'),
            ] else ...[
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                    width: double.infinity,
                    padding: EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.grey[100],
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey[300]!),
                    ),
                    child: Text(
                      _testResults,
                      style: TextStyle(
                        fontFamily: 'monospace',
                        fontSize: 12,
                      ),
                    ),
                  ),
                ),
              ),
              
              SizedBox(height: 16),
              
              // Test FeatureGate directly
              Card(
                child: Padding(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Live FeatureGate Test - AI Chat',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      SizedBox(height: 8),
                      FeatureGate(
                        feature: AppFeature.aiChat,
                        child: Container(
                          padding: EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.green.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.check_circle, color: Colors.green),
                              SizedBox(width: 8),
                              Text('✅ AI Chat is accessible!'),
                            ],
                          ),
                        ),
                        onUpgrade: () {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('Upgrade prompt triggered!')),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
