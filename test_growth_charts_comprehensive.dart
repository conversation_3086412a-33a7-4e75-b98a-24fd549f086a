import 'package:flutter_test/flutter_test.dart';
import 'package:babytracker_pro/services/who_data_service.dart';
import 'package:babytracker_pro/services/growth_analyzer.dart';
import 'package:babytracker_pro/services/enhanced_percentile_calculator.dart';
import 'package:babytracker_pro/models/baby_profile.dart';

void main() {
  group('Growth Charts Comprehensive Tests', () {
    late BabyProfile testBaby;
    
    setUp(() {
      testBaby = BabyProfile(
        id: 'test-baby-123',
        name: 'Test Baby',
        gender: 'male',
        birthDate: DateTime(2024, 1, 1),
        birthWeight: 3.5,
        birthHeight: 50.0,
        headCircumference: 35.0,
        userId: 'test-user-123',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    group('WHO Data Service Tests', () {
      test('should calculate accurate percentiles for boys weight', () {
        // Test cases from WHO standards
        final testCases = [
          {'age': 0.0, 'weight': 3.5, 'expectedPercentile': 50.0}, // Birth weight median
          {'age': 6.0, 'weight': 7.9, 'expectedPercentile': 50.0}, // 6 months median
          {'age': 12.0, 'weight': 9.6, 'expectedPercentile': 50.0}, // 12 months median
          {'age': 24.0, 'weight': 12.2, 'expectedPercentile': 50.0}, // 24 months median
        ];

        for (final testCase in testCases) {
          final percentile = WHODataService.calculateExactPercentile(
            testCase['weight'] as double,
            testCase['age'] as double,
            'weight',
            'male',
          );
          
          expect(percentile, closeTo(testCase['expectedPercentile'] as double, 10.0),
              reason: 'Failed for age ${testCase['age']}, weight ${testCase['weight']}');
        }
      });

      test('should calculate accurate percentiles for girls weight', () {
        final testCases = [
          {'age': 0.0, 'weight': 3.2, 'expectedPercentile': 50.0}, // Birth weight median
          {'age': 6.0, 'weight': 7.3, 'expectedPercentile': 50.0}, // 6 months median
          {'age': 12.0, 'weight': 8.9, 'expectedPercentile': 50.0}, // 12 months median
          {'age': 24.0, 'weight': 11.5, 'expectedPercentile': 50.0}, // 24 months median
        ];

        for (final testCase in testCases) {
          final percentile = WHODataService.calculateExactPercentile(
            testCase['weight'] as double,
            testCase['age'] as double,
            'weight',
            'female',
          );
          
          expect(percentile, closeTo(testCase['expectedPercentile'] as double, 10.0),
              reason: 'Failed for age ${testCase['age']}, weight ${testCase['weight']}');
        }
      });

      test('should calculate accurate percentiles for boys height', () {
        final testCases = [
          {'age': 0.0, 'height': 49.9, 'expectedPercentile': 50.0}, // Birth height median
          {'age': 6.0, 'height': 67.6, 'expectedPercentile': 50.0}, // 6 months median
          {'age': 12.0, 'height': 75.7, 'expectedPercentile': 50.0}, // 12 months median
          {'age': 24.0, 'height': 87.1, 'expectedPercentile': 50.0}, // 24 months median
        ];

        for (final testCase in testCases) {
          final percentile = WHODataService.calculateExactPercentile(
            testCase['height'] as double,
            testCase['age'] as double,
            'height',
            'male',
          );
          
          expect(percentile, closeTo(testCase['expectedPercentile'] as double, 10.0),
              reason: 'Failed for age ${testCase['age']}, height ${testCase['height']}');
        }
      });

      test('should calculate accurate percentiles for head circumference', () {
        final testCases = [
          {'age': 0.0, 'head': 34.5, 'expectedPercentile': 50.0}, // Birth head median
          {'age': 6.0, 'head': 43.3, 'expectedPercentile': 50.0}, // 6 months median
          {'age': 12.0, 'head': 46.5, 'expectedPercentile': 50.0}, // 12 months median
          {'age': 24.0, 'head': 49.2, 'expectedPercentile': 50.0}, // 24 months median
        ];

        for (final testCase in testCases) {
          final percentile = WHODataService.calculateExactPercentile(
            testCase['head'] as double,
            testCase['age'] as double,
            'head_circumference',
            'male',
          );
          
          expect(percentile, closeTo(testCase['expectedPercentile'] as double, 10.0),
              reason: 'Failed for age ${testCase['age']}, head circumference ${testCase['head']}');
        }
      });

      test('should handle extreme percentiles correctly', () {
        // Test extreme values
        final extremeTests = [
          {'age': 6.0, 'weight': 5.0, 'gender': 'male', 'expected': 3.0}, // Very low
          {'age': 6.0, 'weight': 11.0, 'gender': 'male', 'expected': 97.0}, // Very high
        ];

        for (final test in extremeTests) {
          final percentile = WHODataService.calculateExactPercentile(
            test['weight'] as double,
            test['age'] as double,
            'weight',
            test['gender'] as String,
          );
          
          expect(percentile, greaterThan(0.0));
          expect(percentile, lessThan(100.0));
        }
      });

      test('should calculate Z-scores correctly', () {
        final zScore = WHODataService.calculateZScore(
          7.9, // Weight
          6.0, // Age in months
          'weight',
          'male',
        );
        
        expect(zScore, closeTo(0.0, 0.5), reason: 'Z-score should be close to 0 for median values');
      });

      test('should get percentile values correctly', () {
        final medianWeight = WHODataService.getPercentileValue(
          50.0, // 50th percentile
          6.0, // Age in months
          'weight',
          'male',
        );
        
        expect(medianWeight, closeTo(7.9, 0.5), reason: 'Median weight at 6 months should be around 7.9kg');
      });

      test('should handle invalid inputs gracefully', () {
        expect(() => WHODataService.calculateExactPercentile(-1.0, 6.0, 'weight', 'male'), 
               throwsArgumentError);
        expect(() => WHODataService.calculateExactPercentile(7.9, -1.0, 'weight', 'male'), 
               throwsArgumentError);
        expect(() => WHODataService.calculateExactPercentile(7.9, 6.0, 'invalid', 'male'), 
               throwsArgumentError);
      });
    });

    group('Growth Analysis Tests', () {
      test('should analyze normal growth pattern', () {
        final measurements = [
          MeasurementData(
            measurementType: 'weight',
            value: 3.5,
            unit: 'kg',
            measuredAt: DateTime(2024, 1, 1),
            ageInMonths: 0.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: 'Birth weight',
          ),
          MeasurementData(
            measurementType: 'weight',
            value: 7.9,
            unit: 'kg',
            measuredAt: DateTime(2024, 7, 1),
            ageInMonths: 6.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: '6 month checkup',
          ),
        ];

        final analysis = GrowthAnalyzer.analyzeGrowthPattern(measurements, testBaby);
        
        expect(analysis.alerts.length, equals(0), reason: 'Normal growth should not trigger alerts');
        expect(analysis.overallAssessment, contains('normal'));
      });

      test('should detect concerning growth patterns', () {
        final measurements = [
          MeasurementData(
            measurementType: 'weight',
            value: 3.5,
            unit: 'kg',
            measuredAt: DateTime(2024, 1, 1),
            ageInMonths: 0.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: 'Birth weight',
          ),
          MeasurementData(
            measurementType: 'weight',
            value: 5.0, // Very low for 6 months
            unit: 'kg',
            measuredAt: DateTime(2024, 7, 1),
            ageInMonths: 6.0,
            percentile: 3.0,
            zScore: -2.0,
            notes: '6 month checkup',
          ),
        ];

        final analysis = GrowthAnalyzer.analyzeGrowthPattern(measurements, testBaby);
        
        expect(analysis.alerts.length, greaterThan(0), reason: 'Concerning growth should trigger alerts');
        expect(analysis.hasUrgentAlerts, isTrue, reason: 'Below 3rd percentile should be urgent');
      });

      test('should provide appropriate recommendations', () {
        final measurements = [
          MeasurementData(
            measurementType: 'weight',
            value: 3.5,
            unit: 'kg',
            measuredAt: DateTime(2024, 1, 1),
            ageInMonths: 0.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: 'Birth weight',
          ),
        ];

        final analysis = GrowthAnalyzer.analyzeGrowthPattern(measurements, testBaby);
        
        expect(analysis.recommendations.length, greaterThan(0), reason: 'Should provide recommendations');
        expect(analysis.recommendations.any((r) => r.contains('measurements')), isTrue, 
               reason: 'Should recommend regular measurements');
      });
    });

    group('Enhanced Percentile Calculator Tests', () {
      test('should calculate growth velocity correctly', () {
        final measurements = [
          MeasurementData(
            measurementType: 'weight',
            value: 3.5,
            unit: 'kg',
            measuredAt: DateTime(2024, 1, 1),
            ageInMonths: 0.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: 'Birth weight',
          ),
          MeasurementData(
            measurementType: 'weight',
            value: 7.9,
            unit: 'kg',
            measuredAt: DateTime(2024, 7, 1),
            ageInMonths: 6.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: '6 month checkup',
          ),
        ];

        final velocity = EnhancedPercentileCalculator.calculateGrowthVelocity(measurements);
        
        expect(velocity, isNotNull, reason: 'Should calculate velocity for two measurements');
        expect(velocity!.velocityPerMonth, greaterThan(0), reason: 'Should have positive velocity');
        expect(velocity.isNormal, isTrue, reason: 'Normal growth velocity should be flagged as normal');
      });

      test('should identify percentile crossing', () {
        final measurements = [
          MeasurementData(
            measurementType: 'weight',
            value: 7.9,
            unit: 'kg',
            measuredAt: DateTime(2024, 1, 1),
            ageInMonths: 6.0,
            percentile: 50.0,
            zScore: 0.0,
            notes: '6 month checkup',
          ),
          MeasurementData(
            measurementType: 'weight',
            value: 5.0,
            unit: 'kg',
            measuredAt: DateTime(2024, 4, 1),
            ageInMonths: 9.0,
            percentile: 3.0,
            zScore: -2.0,
            notes: '9 month checkup',
          ),
        ];

        final hasCrossing = EnhancedPercentileCalculator.hasSignificantPercentileCrossing(measurements);
        
        expect(hasCrossing, isTrue, reason: 'Should detect significant percentile drop');
      });

      test('should validate measurement data', () {
        final validMeasurement = MeasurementData(
          measurementType: 'weight',
          value: 7.9,
          unit: 'kg',
          measuredAt: DateTime(2024, 7, 1),
          ageInMonths: 6.0,
          percentile: 50.0,
          zScore: 0.0,
          notes: '6 month checkup',
        );

        expect(EnhancedPercentileCalculator.isValidMeasurementData(validMeasurement), isTrue);

        final invalidMeasurement = MeasurementData(
          measurementType: 'weight',
          value: -1.0, // Invalid negative weight
          unit: 'kg',
          measuredAt: DateTime(2024, 7, 1),
          ageInMonths: 6.0,
          percentile: 50.0,
          zScore: 0.0,
          notes: 'Invalid weight',
        );

        expect(EnhancedPercentileCalculator.isValidMeasurementData(invalidMeasurement), isFalse);
      });
    });

    group('Unit Conversion Tests', () {
      test('should convert between metric and imperial units correctly', () {
        // Weight conversions
        expect(3.5 * 2.20462, closeTo(7.7, 0.1)); // kg to lbs
        expect(7.7 / 2.20462, closeTo(3.5, 0.1)); // lbs to kg
        
        // Height conversions
        expect(50.0 / 2.54, closeTo(19.7, 0.1)); // cm to inches
        expect(19.7 * 2.54, closeTo(50.0, 0.1)); // inches to cm
      });
    });

    group('Data Validation Tests', () {
      test('should validate age ranges', () {
        expect(0.0 >= 0.0 && 0.0 <= 60.0, isTrue, reason: 'Birth age should be valid');
        expect(6.0 >= 0.0 && 6.0 <= 60.0, isTrue, reason: '6 months should be valid');
        expect(24.0 >= 0.0 && 24.0 <= 60.0, isTrue, reason: '24 months should be valid');
        expect(60.0 >= 0.0 && 60.0 <= 60.0, isTrue, reason: '60 months should be valid');
        expect(-1.0 >= 0.0 && -1.0 <= 60.0, isFalse, reason: 'Negative age should be invalid');
        expect(70.0 >= 0.0 && 70.0 <= 60.0, isFalse, reason: 'Age over 60 months should be invalid');
      });

      test('should validate weight ranges', () {
        expect(0.5 >= 0.5 && 0.5 <= 30.0, isTrue, reason: 'Minimum weight should be valid');
        expect(3.5 >= 0.5 && 3.5 <= 30.0, isTrue, reason: 'Normal birth weight should be valid');
        expect(15.0 >= 0.5 && 15.0 <= 30.0, isTrue, reason: 'Toddler weight should be valid');
        expect(0.0 >= 0.5 && 0.0 <= 30.0, isFalse, reason: 'Zero weight should be invalid');
        expect(35.0 >= 0.5 && 35.0 <= 30.0, isFalse, reason: 'Excessive weight should be invalid');
      });

      test('should validate height ranges', () {
        expect(30.0 >= 30.0 && 30.0 <= 120.0, isTrue, reason: 'Minimum height should be valid');
        expect(50.0 >= 30.0 && 50.0 <= 120.0, isTrue, reason: 'Normal birth height should be valid');
        expect(90.0 >= 30.0 && 90.0 <= 120.0, isTrue, reason: 'Toddler height should be valid');
        expect(25.0 >= 30.0 && 25.0 <= 120.0, isFalse, reason: 'Too short height should be invalid');
        expect(130.0 >= 30.0 && 130.0 <= 120.0, isFalse, reason: 'Too tall height should be invalid');
      });

      test('should validate head circumference ranges', () {
        expect(30.0 >= 30.0 && 30.0 <= 60.0, isTrue, reason: 'Minimum head circumference should be valid');
        expect(35.0 >= 30.0 && 35.0 <= 60.0, isTrue, reason: 'Normal birth head circumference should be valid');
        expect(50.0 >= 30.0 && 50.0 <= 60.0, isTrue, reason: 'Toddler head circumference should be valid');
        expect(25.0 >= 30.0 && 25.0 <= 60.0, isFalse, reason: 'Too small head circumference should be invalid');
        expect(65.0 >= 30.0 && 65.0 <= 60.0, isFalse, reason: 'Too large head circumference should be invalid');
      });
    });

    group('Error Handling Tests', () {
      test('should handle missing data gracefully', () {
        final emptyMeasurements = <MeasurementData>[];
        final analysis = GrowthAnalyzer.analyzeGrowthPattern(emptyMeasurements, testBaby);
        
        expect(analysis.alerts.length, greaterThan(0), reason: 'Should alert for missing measurements');
        expect(analysis.alerts.any((a) => a.type == 'missing_measurements'), isTrue);
      });

      test('should handle invalid measurement types', () {
        expect(() => WHODataService.calculateExactPercentile(7.9, 6.0, 'invalid_type', 'male'), 
               throwsArgumentError);
      });

      test('should handle invalid gender values', () {
        // The service should handle case insensitivity and variations
        expect(() => WHODataService.calculateExactPercentile(7.9, 6.0, 'weight', 'MALE'), 
               returnsNormally);
        expect(() => WHODataService.calculateExactPercentile(7.9, 6.0, 'weight', 'boy'), 
               returnsNormally);
      });
    });
  });
}
