-- Alternative method to get table structure
-- If the previous query doesn't work, try these individual queries:

-- 1. Baby Profiles Structure
\d baby_profiles;

-- 2. Activity Logs Structure  
\d activity_logs;

-- 3. Growth Measurements Structure
\d growth_measurements;

-- 4. Medicine Logs Structure
\d medicine_logs;

-- 5. Vaccination Logs Structure
\d vaccination_logs;

-- 6. User Profiles Structure
\d user_profiles;

-- 7. Scheduled Activities Structure
\d scheduled_activities;

-- 8. Milestones Structure
\d milestones;

-- OR try this simpler approach:
SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'baby_profiles' AND table_schema = 'public';

SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'growth_measurements' AND table_schema = 'public';

SELECT table_name, column_name, data_type 
FROM information_schema.columns 
WHERE table_name = 'medicine_logs' AND table_schema = 'public';