# Database Restoration Instructions

This guide will help you restore your Supabase database tables that were lost.

## Option 1: Using Supabase CLI (Recommended)

If you have the Supabase CLI installed and configured, this is the easiest method:

1. Make sure you have the latest Supabase CLI installed:
   ```bash
   npm install -g supabase
   ```

2. Login to your Supabase account:
   ```bash
   supabase login
   ```

3. Link your project (if not already linked):
   ```bash
   supabase link --project-ref <your-project-ref>
   ```

4. Run the migration script:
   ```bash
   chmod +x apply_database_migration.sh
   ./apply_database_migration.sh
   ```

## Option 2: Using Supabase SQL Editor

If you don't have the CLI set up or prefer using the Supabase dashboard:

1. Log in to your Supabase dashboard at https://app.supabase.com
2. Select your project
3. Go to the SQL Editor
4. Open the `manual_database_update.sql` file from this repository
5. Copy the entire contents and paste it into the SQL Editor
6. Run the SQL script

## Option 3: Using Supabase API

If you prefer using the API:

1. Install the Supabase JS client:
   ```bash
   npm install @supabase/supabase-js
   ```

2. Create a script to run the SQL:
   ```javascript
   import { createClient } from '@supabase/supabase-js'
   import fs from 'fs'

   const supabaseUrl = 'YOUR_SUPABASE_URL'
   const supabaseKey = 'YOUR_SUPABASE_SERVICE_ROLE_KEY'
   const supabase = createClient(supabaseUrl, supabaseKey)

   const sql = fs.readFileSync('manual_database_update.sql', 'utf8')
   
   async function restoreDatabase() {
     const { data, error } = await supabase.rpc('pgbouncer_exec', { query: sql })
     
     if (error) {
       console.error('Error restoring database:', error)
     } else {
       console.log('Database restored successfully!')
     }
   }

   restoreDatabase()
   ```

## Verification

After restoring the database, run the verification script to ensure all tables were created correctly:

1. Using Supabase CLI:
   ```bash
   supabase db query -f database_schema_diagnostic.sql
   ```

2. Or using the SQL Editor:
   - Open the `database_schema_diagnostic.sql` file
   - Copy the contents and run it in the SQL Editor

## Data Recovery

If you had a backup of your data:

1. Import your data using CSV files through the Supabase dashboard
2. Or use the Supabase API to insert your data programmatically

## Troubleshooting

If you encounter any issues:

1. Check the Supabase logs for error messages
2. Ensure you have the necessary permissions
3. Try running the SQL commands one by one to identify any specific issues
4. If you get foreign key constraint errors, make sure to create tables in the correct order

## Next Steps

After restoring your database:

1. Update your application's environment variables if needed
2. Test your application to ensure it connects properly
3. Consider setting up regular database backups to prevent future data loss