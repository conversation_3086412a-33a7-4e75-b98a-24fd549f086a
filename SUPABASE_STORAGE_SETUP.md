# 🗄️ Supabase Storage Setup Instructions

## Issue Description
The app is getting a "Bucket not found" error when trying to upload baby photos to Supabase Storage.

**Error Log:**
```
I/flutter (25875): ❌ Error uploading to Supabase: StorageException(message: Bucket not found, statusCode: 404, error: Bucket not found).
```

## 🛠️ Solution: Create Storage Bucket

### Option 1: Using Supabase Dashboard (Recommended)

1. **Open Supabase Dashboard**
   - Go to [supabase.com](https://supabase.com)
   - Sign in to your account
   - Select your Baby Tracker project

2. **Navigate to Storage**
   - Click on "Storage" in the left sidebar
   - Click on "Buckets" tab

3. **Create New Bucket**
   - Click "New bucket" button
   - Enter bucket name: `baby-photos`
   - Set as **Public** bucket (enable "Public bucket" toggle)
   - Click "Create bucket"

4. **Configure Bucket Policies**
   - Go to "Policies" tab in Storage
   - Click "New policy" 
   - Select template: "Enable read access for all users"
   - Apply to bucket: `baby-photos`
   - Save policy

### Option 2: Using SQL Commands

Execute these SQL commands in the Supabase SQL Editor:

```sql
-- Create the baby-photos storage bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'baby-photos',
  'baby-photos',
  true,
  ********, -- 50MB limit
  ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
);

-- Create policy for authenticated users to upload
CREATE POLICY "Users can upload baby photos" ON storage.objects
FOR INSERT 
TO authenticated
WITH CHECK (bucket_id = 'baby-photos');

-- Create policy for public read access
CREATE POLICY "Public can view baby photos" ON storage.objects
FOR SELECT 
TO public
USING (bucket_id = 'baby-photos');

-- Create policy for users to update their own photos
CREATE POLICY "Users can update baby photos" ON storage.objects
FOR UPDATE 
TO authenticated
USING (bucket_id = 'baby-photos');

-- Create policy for users to delete their own photos
CREATE POLICY "Users can delete baby photos" ON storage.objects
FOR DELETE 
TO authenticated
USING (bucket_id = 'baby-photos');
```

### Option 3: Quick Setup SQL (Copy-Paste Ready)

```sql
-- Quick setup for baby-photos bucket with all necessary policies
DO $$
BEGIN
  -- Create bucket if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM storage.buckets WHERE id = 'baby-photos') THEN
    INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
    VALUES (
      'baby-photos',
      'baby-photos', 
      true,
      ********,
      ARRAY['image/jpeg', 'image/png', 'image/webp', 'image/gif']
    );
  END IF;
END $$;

-- Create comprehensive policies
CREATE POLICY IF NOT EXISTS "baby_photos_upload" ON storage.objects
FOR INSERT TO authenticated
WITH CHECK (bucket_id = 'baby-photos');

CREATE POLICY IF NOT EXISTS "baby_photos_view" ON storage.objects  
FOR SELECT TO public
USING (bucket_id = 'baby-photos');

CREATE POLICY IF NOT EXISTS "baby_photos_update" ON storage.objects
FOR UPDATE TO authenticated  
USING (bucket_id = 'baby-photos');

CREATE POLICY IF NOT EXISTS "baby_photos_delete" ON storage.objects
FOR DELETE TO authenticated
USING (bucket_id = 'baby-photos');
```

## 🔧 Verification Steps

After creating the bucket, verify it works:

1. **Test Upload via Dashboard**
   - Go to Storage > baby-photos bucket
   - Try uploading a test image
   - Confirm it appears in the bucket

2. **Test in App**
   - Open the Baby Tracker app
   - Go to baby profile creation/editing
   - Try taking a photo or selecting from gallery
   - Check if upload succeeds

3. **Check Bucket URL**
   The bucket should be accessible at:
   ```
   https://[your-project-id].supabase.co/storage/v1/object/public/baby-photos/
   ```

## 📋 Bucket Configuration Details

- **Bucket Name:** `baby-photos`
- **Public Access:** Yes (for image display)
- **File Size Limit:** 50MB per file
- **Allowed Types:** JPEG, PNG, WebP, GIF
- **Folder Structure:** `[user-id]/[baby-id]/[filename]`

## 🔒 Security Notes

- The bucket is public for read access (required for displaying images in app)
- Upload/update/delete operations require authentication
- File paths include user IDs for access control
- Row Level Security (RLS) policies ensure users can only manage their own photos

## 🐛 Troubleshooting

### If you still get "Bucket not found" error:

1. **Check bucket name spelling** - must be exactly `baby-photos`
2. **Verify project connection** - ensure app is connected to correct Supabase project
3. **Check environment variables** - verify `SUPABASE_URL` and `SUPABASE_ANON_KEY` in `env.json`
4. **Test authentication** - ensure user is properly authenticated before upload

### If upload fails with permissions error:

1. **Check user authentication** - user must be logged in
2. **Verify policies** - ensure upload policy exists and is enabled
3. **Check file type** - ensure it's an allowed image format
4. **Verify file size** - ensure image is under 50MB limit

## ✅ Success Confirmation

Once properly set up, you should see:
- ✅ Bucket appears in Supabase Storage dashboard
- ✅ Upload policy shows as "Enabled" 
- ✅ Test upload works in dashboard
- ✅ App photo upload succeeds without errors
- ✅ Uploaded photos display correctly in app

The storage setup is complete when baby photos can be uploaded from the app and display correctly across all screens.
