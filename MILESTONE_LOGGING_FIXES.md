# Milestone Logging Fixes

## Problem
Milestones are not appearing in "Today's Summary" or "Recent Activities" sections of the dashboard, even though they are correctly saved and displayed in the milestones screen.

## Root Cause
The issue is in two places:

1. **Database Level**: The `get_todays_activity_summary` RPC function only queries the `activity_logs` table and doesn't include milestones from the `milestones` table.

2. **Application Level**: The `getRecentActivities` method in `SupabaseService` only queries the `activity_logs` table and doesn't include milestones.

## Fixes Applied

### 1. Database Fix
Created migration `20250125000002_minimal_activity_summary_update.sql` to update the `get_todays_activity_summary` function to include milestones:

```sql
-- Updated function combines results from both activity_logs and milestones tables
CREATE OR REPLACE FUNCTION get_todays_activity_summary(p_baby_id UUID)
RETURNS TABLE (
    activity_type TEXT,
    count BIGINT,
    total_duration INTEGER,
    last_activity_time TIMESTAMPTZ
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        combined.activity_type,
        SUM(combined.count)::BIGINT as count,
        SUM(combined.total_duration)::INTEGER as total_duration,
        MAX(combined.last_activity_time) as last_activity_time
    FROM (
        -- Get activities from activity_logs table
        SELECT 
            al.activity_type::TEXT,
            COUNT(*)::BIGINT as count,
            COALESCE(SUM(al.duration_minutes), 0)::INTEGER as total_duration,
            MAX(al.recorded_at) as last_activity_time
        FROM activity_logs al
        WHERE al.baby_id = p_baby_id
            AND al.recorded_at >= CURRENT_DATE::TIMESTAMPTZ
            AND al.recorded_at < (CURRENT_DATE + INTERVAL '1 day')::TIMESTAMPTZ
        GROUP BY al.activity_type
        
        UNION ALL
        
        -- Get milestones from milestones table
        SELECT 
            'milestone'::TEXT as activity_type,
            COUNT(*)::BIGINT as count,
            0::INTEGER as total_duration, -- Milestones don't have duration
            MAX(m.achieved_date) as last_activity_time
        FROM milestones m
        WHERE m.baby_id = p_baby_id
            AND m.achieved_date >= CURRENT_DATE::TIMESTAMPTZ
            AND m.achieved_date < (CURRENT_DATE + INTERVAL '1 day')::TIMESTAMPTZ
    ) combined
    GROUP BY combined.activity_type
    ORDER BY combined.activity_type;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 2. Application Fix
Updated `getRecentActivities` method in `lib/services/supabase_service.dart` to:
- Query both `activity_logs` and `milestones` tables
- Convert milestones to `ActivityLog` format using the existing `ActivityLog.fromRawData` method
- Combine and sort results by timestamp
- Apply the limit to the combined results

### 3. Model Fix
Added `toRecentActivityMap()` method to the `Milestone` class in `lib/models/milestone.dart` to convert milestones to the format expected by the Recent Activities widget.

## Manual Steps Required

Since the automated migration failed due to existing database policies, you need to manually apply the function update:

1. Open Supabase web interface
2. Go to SQL Editor
3. Run the SQL from `20250125000002_minimal_activity_summary_update.sql`

## Expected Results

After applying these fixes:
- Milestones will appear in "Today's Summary" with the correct count
- Milestones will appear in "Recent Activities" mixed with other activities
- The milestone display will include proper icons, colors, and metadata

## Files Changed

1. `supabase/migrations/20250125000002_minimal_activity_summary_update.sql` - Database function update
2. `lib/services/supabase_service.dart` - Updated getRecentActivities method
3. `lib/models/milestone.dart` - Added toRecentActivityMap method

## Testing

After applying the fixes, test by:
1. Adding a milestone for today
2. Checking that it appears in "Today's Summary" 
3. Checking that it appears in "Recent Activities"
4. Verifying the milestone shows with proper formatting and icons
