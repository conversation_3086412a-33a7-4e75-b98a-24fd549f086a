#!/bin/bash

# Fix migration order by temporarily renaming problematic migrations
# This ensures baby_profiles is created before other tables reference it

echo "🔧 Fixing migration order..."

# Create a backup directory
mkdir -p supabase/migrations_backup

# Move problematic migrations that reference baby_profiles to run later
problematic_migrations=(
    "20250102170000_create_missing_tables.sql"
    "20250102180000_create_chat_messages_table.sql" 
    "20250102190000_create_chat_messages_table.sql"
    "20250107130000_create_activity_logs_table.sql"
    "20250110000000_create_milestones_table.sql"
    "20250117000000_enhance_growth_measurements_schema.sql"
    "20250717000000_create_scheduled_activities_table.sql"
)

# Backup and rename these migrations to run after baby_profiles is created
for migration in "${problematic_migrations[@]}"; do
    if [ -f "supabase/migrations/$migration" ]; then
        echo "📦 Backing up: $migration"
        cp "supabase/migrations/$migration" "supabase/migrations_backup/"
        
        # Rename to run after baby_profiles (use 20250720000000+ timestamps)
        new_name=$(echo "$migration" | sed 's/^[0-9]*/20250720000000/')
        mv "supabase/migrations/$migration" "supabase/migrations/$new_name"
        echo "📝 Renamed to: $new_name"
    fi
done

echo "✅ Migration order fixed!"
echo "🚀 Now run: supabase db reset --linked && supabase db push"