-- Query to get all columns for relevant tables
-- Run this in your Supabase SQL editor and share the results

SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default,
    character_maximum_length,
    numeric_precision,
    numeric_scale
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name IN (
        'user_profiles',
        'baby_profiles', 
        'activity_logs',
        'growth_measurements',
        'milestones',
        'scheduled_activities',
        'medicine_logs',
        'vaccination_logs',
        'ai_insights',
        'chat_messages'
    )
ORDER BY 
    table_name,
    ordinal_position;

-- Also get table names to see what tables actually exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
    AND table_type = 'BASE TABLE'
ORDER BY table_name;